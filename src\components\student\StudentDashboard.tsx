import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { StudentAuthData } from '@/services/StudentAuthService';
import { LogoutButton } from '@/components/ui/logout-button';
import {
  GraduationCap,
  User,
  Calendar,
  BookOpen,
  ClipboardCheck,
  Phone,
  Mail,
  Lock,
  AlertCircle
} from 'lucide-react';

interface StudentDashboardProps {
  studentData: StudentAuthData;
  onLogout?: () => void; // Made optional since we'll use standardized logout
  onChangePassword: () => void;
}

const StudentDashboard: React.FC<StudentDashboardProps> = ({
  studentData,
  onLogout,
  onChangePassword
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getDepartmentName = (dept: string) => {
    const deptMap: { [key: string]: string } = {
      'cse': 'Computer Science and Engineering',
      'ece': 'Electronics and Communication Engineering',
      'mech': 'Mechanical Engineering',
      'eee': 'Electrical and Electronics Engineering',
      'civil': 'Civil Engineering',
      'it': 'Information Technology'
    };
    return deptMap[dept] || dept.toUpperCase();
  };

  const quickActions = [
    {
      title: 'View Timetable',
      description: 'Check your class schedule',
      icon: Calendar,
      action: () => console.log('View timetable'),
      color: 'text-blue-600 bg-blue-50'
    },
    {
      title: 'Attendance',
      description: 'View attendance records',
      icon: ClipboardCheck,
      action: () => console.log('View attendance'),
      color: 'text-green-600 bg-green-50'
    },
    {
      title: 'Internal Assessment',
      description: 'Check IA marks',
      icon: BookOpen,
      action: () => console.log('View IA'),
      color: 'text-purple-600 bg-purple-50'
    },
    {
      title: 'Change Password',
      description: 'Update your password',
      icon: Lock,
      action: onChangePassword,
      color: 'text-orange-600 bg-orange-50'
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-white">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <GraduationCap className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold">Student Portal</h1>
                <p className="text-sm text-muted-foreground">
                  {currentTime.toLocaleDateString()} - {currentTime.toLocaleTimeString()}
                </p>
              </div>
            </div>
            <LogoutButton
              variant="outline"
              onClick={onLogout}
            />
          </div>
        </div>
      </div>

      <div className="container mx-auto p-6 space-y-6">
        {/* Welcome Section */}
        <div className="space-y-4">
          <div>
            <h2 className="text-3xl font-bold">Welcome, {studentData.student_name}!</h2>
            <p className="text-muted-foreground">
              Here's your academic dashboard
            </p>
          </div>

          {/* Student Info Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Student Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Roll Number</p>
                  <p className="font-semibold">{studentData.student_id}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Department</p>
                  <p className="font-semibold">{getDepartmentName(studentData.department)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Semester</p>
                  <p className="font-semibold">Semester {studentData.semester}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Section</p>
                  <p className="font-semibold">Section {studentData.section}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* First Login Alert */}
          {studentData.first_login && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <strong>Security Notice:</strong> This is your first login. We recommend changing your password for better security.
                <Button 
                  variant="link" 
                  className="p-0 h-auto text-orange-800 underline ml-2"
                  onClick={onChangePassword}
                >
                  Change Password Now
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Quick Actions */}
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1 space-y-1">
                      <h4 className="font-semibold">{action.title}</h4>
                      <p className="text-sm text-muted-foreground">{action.description}</p>
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full mt-4"
                    onClick={action.action}
                  >
                    Access
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Academic Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Today's Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Today's Schedule
              </CardTitle>
              <CardDescription>
                {currentTime.toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Timetable integration coming soon</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ClipboardCheck className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Your latest academic updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-center py-8 text-muted-foreground">
                  <ClipboardCheck className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Activity tracking coming soon</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Need Help?
            </CardTitle>
            <CardDescription>
              Contact information for academic support
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <Phone className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">Academic Office</p>
                  <p className="text-sm text-muted-foreground">+91 80 1234 5678</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <Mail className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">Student Support</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 border rounded-lg">
                <GraduationCap className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium">Class Teacher</p>
                  <p className="text-sm text-muted-foreground">Contact via office</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground py-4">
          <p>© 2024 TimeTable Manager - Student Portal</p>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
