// Add or update a mapping with lab slots
async saveMapping(mapping: MappingType): Promise<MappingType> {
  try {
    // First, save the basic mapping
    const payload = {
      academic_year: mapping.academicYear,
      department: mapping.department,
      semester: mapping.semester,
      section: mapping.section,
      subject_id: mapping.subject.id,
      subject_code: mapping.subject.code,
      subject_name: mapping.subject.name,
      subject_type: mapping.subject.type,
      faculty_1_id: mapping.faculty.id,
      faculty_1_name: mapping.faculty.name,
      faculty_2_id: mapping.faculty2Id || null,
      faculty_2_name: mapping.faculty2Name || null,
      hours_per_week: mapping.hoursPerWeek,
      classroom: mapping.classroom,
      slots_per_week: mapping.slotsPerWeek
    };

    let result;
    if (mapping.id) {
      // Update existing mapping
      const { data, error } = await supabase
        .from('subject_faculty_mappings')
        .update(payload)
        .eq('id', mapping.id)
        .select()
        .single();
      
      if (error) throw error;
      result = data;
    } else {
      // Create new mapping
      const { data, error } = await supabase
        .from('subject_faculty_mappings')
        .insert(payload)
        .select()
        .single();
      
      if (error) throw error;
      result = data;
    }

    // Now handle lab slots if this is a lab subject
    if (mapping.subject.type === 'laboratory' || mapping.subject.type === 'lab') {
      // First, delete any existing lab slots for this mapping
      if (mapping.id) {
        await supabase
          .from('lab_time_slots')
          .delete()
          .eq('mapping_id', mapping.id);
      }
      
      // Then insert new lab slots if provided
      if (mapping.labSlots && mapping.labSlots.length > 0) {
        const labSlotPayload = mapping.labSlots.map((slot, index) => ({
          mapping_id: result.id,
          day: slot.day,
          time_of_day: slot.timeOfDay,
          batch_name: slot.batch || `${mapping.section}${index + 1}`,
          slot_order: index + 1 // Add slot_order field
        }));
        
        const { error: labSlotError } = await supabase
          .from('lab_time_slots')
          .insert(labSlotPayload);
          
        if (labSlotError) {
          console.error("Error saving lab slots:", labSlotError);
          throw labSlotError;
        }
      }
    }

    return {
      ...mapping,
      id: result.id
    };
  } catch (error) {
    console.error("Error in saveMapping:", error);
    throw error;
  }
}