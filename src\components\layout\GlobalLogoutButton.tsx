import React from 'react';
import { LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLogout } from '@/hooks/useLogout';
import { cn } from '@/lib/utils';

interface GlobalLogoutButtonProps {
  className?: string;
  showText?: boolean;
  position?: 'fixed' | 'static';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}

/**
 * Global logout button that can be positioned anywhere on the page
 * Useful for emergency logout or as a fallback logout option
 * OPTIMIZED: Uses fast logout for immediate response
 */
export const GlobalLogoutButton: React.FC<GlobalLogoutButtonProps> = ({
  className,
  showText = true,
  position = 'static',
  variant = 'outline'
}) => {
  const { fastLogout, isLoggedIn } = useLogout();

  // Don't render if no user is logged in
  if (!isLoggedIn) {
    return null;
  }

  const baseClasses = position === 'fixed' 
    ? 'fixed top-4 right-4 z-50' 
    : '';

  return (
    <Button
      variant={variant}
      onClick={fastLogout}
      className={cn(
        baseClasses,
        "text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20",
        className
      )}
    >
      <LogOut className="h-4 w-4" />
      {showText && <span className="ml-2">Logout</span>}
    </Button>
  );
};
