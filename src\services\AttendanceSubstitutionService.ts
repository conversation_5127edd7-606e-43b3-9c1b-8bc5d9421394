import { supabase } from "@/integrations/supabase/client";

export interface SubstitutionRecord {
  id: string;
  leave_request_id: string;
  original_faculty_id: string;
  substitute_faculty_id: string;
  date: string;
  time_slot: string;
  subject_code: string;
  semester: string;
  section: string;
  department: string;
  status: 'active' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface AttendanceSubstitutionInfo {
  is_substitute: boolean;
  original_faculty_id?: string;
  original_faculty_name?: string;
  substitute_faculty_id?: string;
  substitute_faculty_name?: string;
  leave_request_id?: string;
  substitution_notes?: string;
}

export class AttendanceSubstitutionService {
  /**
   * Check if a faculty is a substitute for a specific class session
   */
  static async checkSubstitutionStatus(
    facultyId: string,
    date: string,
    timeSlot: string,
    subjectCode: string,
    semester: string,
    section: string,
    department: string
  ): Promise<AttendanceSubstitutionInfo> {
    try {
      console.log('🔍 Checking substitution status for:', {
        facultyId, date, timeSlot, subjectCode, semester, section
      });

      // Check if this faculty is acting as a substitute
      const { data: substitutionData, error: substitutionError } = await supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          affected_classes,
          status,
          employee_details!leave_requests_faculty_id_fkey(full_name)
        `)
        .eq('status', 'approved')
        .lte('start_date', date)
        .gte('end_date', date);

      if (substitutionError) {
        console.error('Error checking substitution status:', substitutionError);
        return { is_substitute: false };
      }

      if (!substitutionData || substitutionData.length === 0) {
        return { is_substitute: false };
      }

      // Check if any approved leave request has this faculty as a substitute
      for (const leaveRequest of substitutionData) {
        if (leaveRequest.affected_classes) {
          for (const affectedClass of leaveRequest.affected_classes) {
            // Check if this matches the current class and faculty is the substitute
            if (
              affectedClass.substitute_faculty_id === facultyId &&
              affectedClass.subject_code === subjectCode &&
              affectedClass.semester === semester &&
              affectedClass.section === section &&
              affectedClass.time_slot === timeSlot
            ) {
              return {
                is_substitute: true,
                original_faculty_id: leaveRequest.faculty_id,
                original_faculty_name: leaveRequest.employee_details?.full_name,
                substitute_faculty_id: facultyId,
                leave_request_id: leaveRequest.id,
                substitution_notes: affectedClass.substitution_notes
              };
            }
          }
        }
      }

      // Check if this faculty is on leave and someone else should be substituting
      const isOnLeave = substitutionData.some(leave => leave.faculty_id === facultyId);
      if (isOnLeave) {
        // Find who is substituting for this faculty
        const leaveRequest = substitutionData.find(leave => leave.faculty_id === facultyId);
        if (leaveRequest?.affected_classes) {
          const matchingClass = leaveRequest.affected_classes.find(
            (cls: any) => 
              cls.subject_code === subjectCode &&
              cls.semester === semester &&
              cls.section === section &&
              cls.time_slot === timeSlot
          );

          if (matchingClass?.substitute_faculty_id) {
            // Get substitute faculty name
            const { data: substituteFaculty } = await supabase
              .from('employee_details')
              .select('full_name')
              .eq('id', matchingClass.substitute_faculty_id)
              .single();

            return {
              is_substitute: false, // Current faculty is on leave, not a substitute
              original_faculty_id: facultyId,
              substitute_faculty_id: matchingClass.substitute_faculty_id,
              substitute_faculty_name: substituteFaculty?.full_name,
              leave_request_id: leaveRequest.id,
              substitution_notes: matchingClass.substitution_notes
            };
          }
        }
      }

      return { is_substitute: false };

    } catch (error) {
      console.error('Error checking substitution status:', error);
      return { is_substitute: false };
    }
  }

  /**
   * Get substitution information for attendance marking
   */
  static async getAttendanceSubstitutionInfo(
    date: string,
    timeSlot: string,
    subjectCode: string,
    semester: string,
    section: string,
    department: string
  ): Promise<AttendanceSubstitutionInfo | null> {
    try {
      console.log('📋 Getting attendance substitution info for:', {
        date, timeSlot, subjectCode, semester, section
      });

      // Find approved leave requests that affect this class on this date
      const { data: leaveRequests, error } = await supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          affected_classes,
          status,
          employee_details!leave_requests_faculty_id_fkey(full_name)
        `)
        .eq('status', 'approved')
        .lte('start_date', date)
        .gte('end_date', date);

      if (error) {
        console.error('Error fetching leave requests:', error);
        return null;
      }

      if (!leaveRequests || leaveRequests.length === 0) {
        return null;
      }

      // Find the specific class that matches our criteria
      for (const leaveRequest of leaveRequests) {
        if (leaveRequest.affected_classes) {
          const matchingClass = leaveRequest.affected_classes.find(
            (cls: any) => 
              cls.subject_code === subjectCode &&
              cls.semester === semester &&
              cls.section === section &&
              cls.time_slot === timeSlot
          );

          if (matchingClass && matchingClass.substitute_faculty_id) {
            // Get substitute faculty details
            const { data: substituteFaculty } = await supabase
              .from('employee_details')
              .select('full_name')
              .eq('id', matchingClass.substitute_faculty_id)
              .single();

            return {
              is_substitute: true,
              original_faculty_id: leaveRequest.faculty_id,
              original_faculty_name: leaveRequest.employee_details?.full_name,
              substitute_faculty_id: matchingClass.substitute_faculty_id,
              substitute_faculty_name: substituteFaculty?.full_name,
              leave_request_id: leaveRequest.id,
              substitution_notes: matchingClass.substitution_notes
            };
          }
        }
      }

      return null;

    } catch (error) {
      console.error('Error getting attendance substitution info:', error);
      return null;
    }
  }

  /**
   * Update attendance records to reflect substitution
   */
  static async updateAttendanceWithSubstitution(
    attendanceRecords: any[],
    substitutionInfo: AttendanceSubstitutionInfo
  ): Promise<any[]> {
    try {
      if (!substitutionInfo.is_substitute) {
        return attendanceRecords;
      }

      // Update attendance records to show substitution information
      const updatedRecords = attendanceRecords.map(record => ({
        ...record,
        // Add substitution metadata
        substitution_info: {
          is_substitute_session: true,
          original_faculty_id: substitutionInfo.original_faculty_id,
          original_faculty_name: substitutionInfo.original_faculty_name,
          substitute_faculty_id: substitutionInfo.substitute_faculty_id,
          substitute_faculty_name: substitutionInfo.substitute_faculty_name,
          leave_request_id: substitutionInfo.leave_request_id,
          substitution_notes: substitutionInfo.substitution_notes
        },
        // Update faculty_id to reflect the substitute
        faculty_id: substitutionInfo.substitute_faculty_id,
        // Add a note about substitution
        notes: record.notes ? 
          `${record.notes} [Substitute: ${substitutionInfo.substitute_faculty_name} for ${substitutionInfo.original_faculty_name}]` :
          `Substitute: ${substitutionInfo.substitute_faculty_name} for ${substitutionInfo.original_faculty_name}`
      }));

      return updatedRecords;

    } catch (error) {
      console.error('Error updating attendance with substitution:', error);
      return attendanceRecords;
    }
  }

  /**
   * Get active substitutions for a faculty member
   */
  static async getActiveSubstitutions(
    facultyId: string,
    date?: string
  ): Promise<SubstitutionRecord[]> {
    try {
      const targetDate = date || new Date().toISOString().split('T')[0];

      // Find leave requests where this faculty is a substitute
      const { data: leaveRequests, error } = await supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          start_date,
          end_date,
          affected_classes,
          status,
          employee_details!leave_requests_faculty_id_fkey(full_name)
        `)
        .eq('status', 'approved')
        .lte('start_date', targetDate)
        .gte('end_date', targetDate);

      if (error) throw error;

      const substitutions: SubstitutionRecord[] = [];

      for (const leaveRequest of leaveRequests || []) {
        if (leaveRequest.affected_classes) {
          for (const affectedClass of leaveRequest.affected_classes) {
            if (affectedClass.substitute_faculty_id === facultyId) {
              substitutions.push({
                id: `${leaveRequest.id}-${affectedClass.id}`,
                leave_request_id: leaveRequest.id,
                original_faculty_id: leaveRequest.faculty_id,
                substitute_faculty_id: facultyId,
                date: targetDate,
                time_slot: affectedClass.time_slot,
                subject_code: affectedClass.subject_code,
                semester: affectedClass.semester,
                section: affectedClass.section,
                department: affectedClass.department || '',
                status: 'active',
                created_at: leaveRequest.created_at || '',
                updated_at: leaveRequest.updated_at || ''
              });
            }
          }
        }
      }

      return substitutions;

    } catch (error) {
      console.error('Error getting active substitutions:', error);
      return [];
    }
  }

  /**
   * Get substitution history for reporting
   */
  static async getSubstitutionHistory(
    facultyId?: string,
    department?: string,
    startDate?: string,
    endDate?: string
  ): Promise<SubstitutionRecord[]> {
    try {
      let query = supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          start_date,
          end_date,
          affected_classes,
          status,
          department,
          employee_details!leave_requests_faculty_id_fkey(full_name)
        `)
        .eq('status', 'approved');

      if (startDate) {
        query = query.gte('start_date', startDate);
      }
      if (endDate) {
        query = query.lte('end_date', endDate);
      }
      if (department) {
        query = query.eq('department', department);
      }

      const { data: leaveRequests, error } = await query;

      if (error) throw error;

      const substitutions: SubstitutionRecord[] = [];

      for (const leaveRequest of leaveRequests || []) {
        if (leaveRequest.affected_classes) {
          for (const affectedClass of leaveRequest.affected_classes) {
            // Filter by faculty if specified
            if (facultyId && 
                affectedClass.substitute_faculty_id !== facultyId && 
                leaveRequest.faculty_id !== facultyId) {
              continue;
            }

            substitutions.push({
              id: `${leaveRequest.id}-${affectedClass.id}`,
              leave_request_id: leaveRequest.id,
              original_faculty_id: leaveRequest.faculty_id,
              substitute_faculty_id: affectedClass.substitute_faculty_id,
              date: leaveRequest.start_date,
              time_slot: affectedClass.time_slot,
              subject_code: affectedClass.subject_code,
              semester: affectedClass.semester,
              section: affectedClass.section,
              department: leaveRequest.department,
              status: 'completed',
              created_at: leaveRequest.created_at || '',
              updated_at: leaveRequest.updated_at || ''
            });
          }
        }
      }

      return substitutions;

    } catch (error) {
      console.error('Error getting substitution history:', error);
      return [];
    }
  }
}
