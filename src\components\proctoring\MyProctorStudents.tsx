import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { 
  Users, 
  Search, 
  Eye, 
  UserMinus,
  AlertCircle,
  Loader2,
  GraduationCap,
  Building2,
  Calendar,
  Mail,
  Phone
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { StudentProctoringService, ProctorStudent } from '@/services/StudentProctoringService';
import StudentProgressCard from './StudentProgressCard';

const MyProctorStudents: React.FC = () => {
  const [students, setStudents] = useState<ProctorStudent[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<ProctorStudent[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedStudentUsn, setSelectedStudentUsn] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalStudents: 0,
    departmentBreakdown: {} as { [key: string]: number },
    semesterBreakdown: {} as { [key: string]: number }
  });

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (user?.id) {
      loadProctorStudents();
    }
  }, [user?.id]);

  useEffect(() => {
    filterStudents();
  }, [students, searchQuery]);

  const loadProctorStudents = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const [studentsData, statsData] = await Promise.all([
        StudentProctoringService.getFacultyProctorStudents(user.id),
        StudentProctoringService.getFacultyProctoringStats(user.id)
      ]);
      
      setStudents(studentsData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading proctor students:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your proctor students',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const filterStudents = () => {
    let filtered = [...students];

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(student => 
        student.student_name.toLowerCase().includes(query) ||
        student.student_usn.toLowerCase().includes(query)
      );
    }

    setFilteredStudents(filtered);
  };

  const handleViewProgress = (studentUsn: string) => {
    setSelectedStudentUsn(studentUsn);
  };

  const handleRemoveStudent = async (proctoringId: string, studentName: string) => {
    if (!confirm(`Are you sure you want to remove ${studentName} from your proctor list?`)) {
      return;
    }

    try {
      await StudentProctoringService.removeStudentFromProctoring(proctoringId);
      toast({
        title: 'Student Removed',
        description: `${studentName} has been removed from your proctor list`,
      });
      await loadProctorStudents();
    } catch (error) {
      console.error('Error removing student:', error);
      toast({
        title: 'Removal Failed',
        description: 'Failed to remove student. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading your proctor students...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold">{stats.totalStudents}</div>
                <div className="text-sm text-gray-600">Total Students</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-50 rounded-lg">
                <Building2 className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {Object.keys(stats.departmentBreakdown).length}
                </div>
                <div className="text-sm text-gray-600">Departments</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-50 rounded-lg">
                <GraduationCap className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {Object.keys(stats.semesterBreakdown).length}
                </div>
                <div className="text-sm text-gray-600">Semesters</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Students List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            My Proctor Students
          </CardTitle>
          <CardDescription>
            Students assigned to you as their proctor. Click on any student to view their detailed progress card.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search by student name or USN..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Students List */}
          {filteredStudents.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {students.length === 0 
                  ? "No students assigned to you as proctor yet. Use the 'Assign Students' tab to add students."
                  : "No students match your search criteria."
                }
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-3">
              {filteredStudents.map((student) => (
                <div
                  key={student.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-lg">{student.student_name}</span>
                            <Badge variant="outline">{student.student_usn}</Badge>
                          </div>
                          <div className="text-sm text-gray-600 flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <Building2 className="w-3 h-3" />
                              {student.department}
                            </span>
                            <span>Semester {student.semester}</span>
                            <span>Section {student.section}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          Assigned: {new Date(student.assigned_date).toLocaleDateString()}
                        </div>
                        {student.student_email && (
                          <div className="flex items-center gap-1">
                            <Mail className="w-3 h-3" />
                            {student.student_email}
                          </div>
                        )}
                        {student.student_mobile && (
                          <div className="flex items-center gap-1">
                            <Phone className="w-3 h-3" />
                            {student.student_mobile}
                          </div>
                        )}
                      </div>

                      {student.notes && (
                        <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                          <strong>Notes:</strong> {student.notes}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewProgress(student.student_usn)}
                        className="flex items-center gap-2"
                      >
                        <Eye className="w-4 h-4" />
                        View Progress
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemoveStudent(student.id, student.student_name)}
                        className="flex items-center gap-2 text-red-600 hover:text-red-700"
                      >
                        <UserMinus className="w-4 h-4" />
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Department and Semester Breakdown */}
      {stats.totalStudents > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Department Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(stats.departmentBreakdown).map(([dept, count]) => (
                  <div key={dept} className="flex items-center justify-between">
                    <span className="text-sm">{dept}</span>
                    <Badge variant="secondary">{count} students</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Semester Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(stats.semesterBreakdown).map(([sem, count]) => (
                  <div key={sem} className="flex items-center justify-between">
                    <span className="text-sm">Semester {sem}</span>
                    <Badge variant="secondary">{count} students</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Progress Card Modal */}
      {selectedStudentUsn && (
        <StudentProgressCard
          studentUsn={selectedStudentUsn}
          onClose={() => setSelectedStudentUsn(null)}
        />
      )}
    </div>
  );
};

export default MyProctorStudents;
