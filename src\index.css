
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced Professional Color Palette */
    --background: 210 40% 98%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    /* Premium Primary Colors - Deep Professional Blue */
    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;
    --primary-hover: 217 91% 55%;
    --primary-active: 217 91% 50%;

    /* Elegant Secondary Colors */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 9% 46%;
    --secondary-hover: 220 14% 92%;

    /* Sophisticated Muted Colors */
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    /* Professional Accent Colors */
    --accent: 142 71% 45%;
    --accent-foreground: 210 40% 98%;
    --accent-hover: 142 71% 40%;

    /* Enhanced Status Colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 71% 45%;
    --success-foreground: 210 40% 98%;
    --warning: 43 96% 56%;
    --warning-foreground: 215 25% 27%;
    --info: 217 91% 60%;
    --info-foreground: 210 40% 98%;

    /* Enhanced Border and Input Colors */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 60%;

    /* Modern Radius System */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 215 25% 27%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 250 89% 65%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 217 91% 60%;

    /* Timetable Theme Variables - Light */
    --timetable-header: 217 91% 60%;
    --timetable-header-foreground: 210 40% 98%;
    --timetable-border: 214 32% 91%;
    --timetable-cell: 0 0% 100%;
    --timetable-cell-foreground: 215 25% 27%;
    --timetable-subject: 217 91% 95%;
    --timetable-subject-foreground: 217 91% 20%;
    --timetable-lab: 142 71% 95%;
    --timetable-lab-foreground: 142 71% 20%;
    --timetable-skill-lab: 250 89% 95%;
    --timetable-skill-lab-foreground: 250 89% 20%;
    --timetable-break: 43 96% 95%;
    --timetable-break-foreground: 43 96% 20%;
    --timetable-tea-break: 199 89% 95%;
    --timetable-tea-break-foreground: 199 89% 20%;
  }

  .dark {
    /* Enhanced Dark Mode Professional Colors */
    --background: 215 28% 17%;
    --foreground: 210 40% 98%;

    --card: 215 28% 19%;
    --card-foreground: 210 40% 98%;

    --popover: 215 28% 19%;
    --popover-foreground: 210 40% 98%;

    /* Premium Dark Primary Colors */
    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;
    --primary-hover: 217 91% 65%;
    --primary-active: 217 91% 70%;

    /* Elegant Dark Secondary Colors */
    --secondary: 215 25% 27%;
    --secondary-foreground: 210 40% 98%;
    --secondary-hover: 215 25% 32%;

    /* Sophisticated Dark Muted Colors */
    --muted: 215 25% 27%;
    --muted-foreground: 217 10% 64%;

    /* Professional Dark Accent Colors */
    --accent: 142 71% 45%;
    --accent-foreground: 210 40% 98%;
    --accent-hover: 142 71% 50%;

    /* Enhanced Dark Status Colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 71% 45%;
    --success-foreground: 210 40% 98%;
    --warning: 43 96% 56%;
    --warning-foreground: 215 28% 17%;
    --info: 217 91% 60%;
    --info-foreground: 210 40% 98%;

    /* Enhanced Dark Border and Input Colors */
    --border: 215 25% 27%;
    --input: 215 25% 27%;
    --ring: 217 91% 60%;

    --sidebar-background: 215 28% 17%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 250 89% 65%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 25% 27%;
    --sidebar-ring: 217 91% 60%;

    /* Timetable Theme Variables - Dark */
    --timetable-header: 217 91% 60%;
    --timetable-header-foreground: 210 40% 98%;
    --timetable-border: 215 25% 27%;
    --timetable-cell: 215 28% 17%;
    --timetable-cell-foreground: 210 40% 98%;
    --timetable-subject: 217 91% 15%;
    --timetable-subject-foreground: 217 91% 80%;
    --timetable-lab: 142 71% 15%;
    --timetable-lab-foreground: 142 71% 80%;
    --timetable-skill-lab: 250 89% 15%;
    --timetable-skill-lab-foreground: 250 89% 80%;
    --timetable-break: 43 96% 15%;
    --timetable-break-foreground: 43 96% 80%;
    --timetable-tea-break: 199 89% 15%;
    --timetable-tea-break-foreground: 199 89% 80%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Enhanced scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Enhanced focus styles */
  :focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
}

/* Layout and Container Styles */
.timetable-page-container {
  @apply w-full max-w-none mx-auto;
  padding-left: 0;
  padding-right: 0;
}

/* Ensure proper spacing from sidebar */
.timetable-main-content {
  @apply w-full;
  margin-left: 0;
  padding: 1.5rem;
}

/* Enhanced Timetable Styles */
.timetable-container {
  @apply rounded-xl border shadow-lg overflow-hidden w-full;
  background-color: hsl(var(--timetable-cell));
  border-color: hsl(var(--timetable-border));
}

.timetable-grid {
  display: grid;
  grid-template-columns: 100px repeat(5, 1fr);
  grid-auto-rows: minmax(50px, auto);
  gap: 0;
  border: 2px solid hsl(var(--timetable-border));
}

.timetable-cell {
  @apply p-1.5 flex items-center justify-center text-center transition-all duration-200;
  min-height: 50px;
  border: 2px solid hsl(var(--timetable-border));
  border-top: 1px solid hsl(var(--timetable-border));
  border-left: 1px solid hsl(var(--timetable-border));
  background-color: hsl(var(--timetable-cell));
  color: hsl(var(--timetable-cell-foreground));
}

/* Enhanced border styling for grid structure */
.timetable-cell:first-child {
  border-left: 2px solid hsl(var(--timetable-border));
}

.timetable-cell:last-child {
  border-right: 2px solid hsl(var(--timetable-border));
}

/* Table-specific border enhancements */
.modern-timetable-container table {
  border-collapse: separate;
  border-spacing: 0;
}

.modern-timetable-container th,
.modern-timetable-container td {
  border: 2px solid hsl(var(--timetable-border));
  border-top: 1px solid hsl(var(--timetable-border));
  border-left: 1px solid hsl(var(--timetable-border));
}

.modern-timetable-container th:first-child,
.modern-timetable-container td:first-child {
  border-left: 2px solid hsl(var(--timetable-border));
}

.modern-timetable-container th:last-child,
.modern-timetable-container td:last-child {
  border-right: 2px solid hsl(var(--timetable-border));
}

.modern-timetable-container tr:first-child th,
.modern-timetable-container tr:first-child td {
  border-top: 2px solid hsl(var(--timetable-border));
}

.modern-timetable-container tr:last-child th,
.modern-timetable-container tr:last-child td {
  border-bottom: 2px solid hsl(var(--timetable-border));
}

.timetable-header {
  @apply font-semibold shadow-sm;
  background: linear-gradient(to right, hsl(var(--timetable-header)), hsl(var(--timetable-header)));
  color: hsl(var(--timetable-header-foreground));
}

.timetable-time {
  @apply font-medium border-r-2 transition-all duration-200;
  background: linear-gradient(to right, hsl(var(--muted)), hsl(var(--muted)));
  color: hsl(var(--muted-foreground));
  border-right-color: hsl(var(--border));
}

.timetable-subject {
  @apply transition-all duration-200;
  background: linear-gradient(to bottom right, hsl(var(--timetable-subject)), hsl(var(--timetable-subject)));
  color: hsl(var(--timetable-subject-foreground));
}

.timetable-lab {
  @apply border-l-4 transition-all duration-200;
  background: linear-gradient(to bottom right, hsl(var(--timetable-lab)), hsl(var(--timetable-lab)));
  color: hsl(var(--timetable-lab-foreground));
  border-left-color: hsl(var(--accent));
}

.timetable-skill-lab {
  @apply border-l-4 transition-all duration-200;
  background: linear-gradient(to bottom right, hsl(var(--timetable-skill-lab)), hsl(var(--timetable-skill-lab)));
  color: hsl(var(--timetable-skill-lab-foreground));
  border-left-color: hsl(var(--secondary));
}

/* Break Column Styles */
.break-column {
  @apply border-x-2 transition-all duration-200;
  background: linear-gradient(180deg, hsl(var(--timetable-break)), hsl(var(--timetable-break) / 0.95));
  color: hsl(var(--timetable-break-foreground));
  border-color: hsl(var(--timetable-border));
  border-top: 2px solid hsl(var(--timetable-border));
  border-bottom: 2px solid hsl(var(--timetable-border));
}

.tea-break-column {
  @apply border-x-2 transition-all duration-200;
  background: linear-gradient(180deg, hsl(var(--timetable-tea-break)), hsl(var(--timetable-tea-break) / 0.95));
  color: hsl(var(--timetable-tea-break-foreground));
  border-color: hsl(var(--timetable-border));
  border-top: 2px solid hsl(var(--timetable-border));
  border-bottom: 2px solid hsl(var(--timetable-border));
}

/* Enhanced Card Styles */
.timetable-card {
  @apply rounded-md shadow-sm border-2 transition-all duration-200 hover:shadow-md hover:scale-[1.01] cursor-move;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0.375rem 0.5rem;
  background-color: transparent;
  border-color: hsl(var(--timetable-border) / 0.3);
  color: inherit;
}

/* Ensure action buttons stay in corner */
.timetable-card .absolute.top-0.right-0 {
  z-index: 30;
}

/* Action button styling */
.timetable-card button {
  border: none;
  outline: none;
}

.timetable-card button:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

.timetable-card-theory {
  background: linear-gradient(135deg, hsl(var(--timetable-subject) / 0.8), hsl(var(--timetable-subject) / 0.6));
  border-color: hsl(var(--timetable-border) / 0.4);
  color: hsl(var(--timetable-subject-foreground));
}

.timetable-card-theory:hover {
  background: linear-gradient(135deg, hsl(var(--timetable-subject) / 0.9), hsl(var(--timetable-subject) / 0.7));
  border-color: hsl(var(--timetable-border) / 0.6);
}

.timetable-card-lab {
  background: linear-gradient(135deg, hsl(var(--timetable-lab) / 0.8), hsl(var(--timetable-lab) / 0.6));
  border-color: hsl(var(--timetable-border) / 0.4);
  color: hsl(var(--timetable-lab-foreground));
  border-left: 3px solid hsl(var(--timetable-lab-foreground) / 0.7);
}

.timetable-card-lab:hover {
  background: linear-gradient(135deg, hsl(var(--timetable-lab) / 0.9), hsl(var(--timetable-lab) / 0.7));
  border-color: hsl(var(--timetable-border) / 0.6);
}

.timetable-card-skill-lab {
  background: linear-gradient(135deg, hsl(var(--timetable-skill-lab) / 0.8), hsl(var(--timetable-skill-lab) / 0.6));
  border-color: hsl(var(--timetable-border) / 0.4);
  color: hsl(var(--timetable-skill-lab-foreground));
  border-left: 3px solid hsl(var(--timetable-skill-lab-foreground) / 0.7);
}

.timetable-card-skill-lab:hover {
  background: linear-gradient(135deg, hsl(var(--timetable-skill-lab) / 0.9), hsl(var(--timetable-skill-lab) / 0.7));
  border-color: hsl(var(--timetable-border) / 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .timetable-container {
    @apply rounded-lg shadow-md;
  }

  .timetable-grid {
    grid-template-columns: 80px repeat(5, minmax(120px, 1fr));
    grid-auto-rows: minmax(45px, auto);
  }

  .timetable-cell {
    min-height: 45px;
    @apply p-1 text-xs;
    border: 2px solid hsl(var(--timetable-border));
    border-top: 1px solid hsl(var(--timetable-border));
    border-left: 1px solid hsl(var(--timetable-border));
  }

  .timetable-card {
    @apply text-xs;
    min-height: 35px;
    padding: 0.25rem 0.375rem;
    border: 2px solid hsl(var(--timetable-border) / 0.3);
  }

  .timetable-subject-title {
    @apply text-xs leading-tight;
  }

  /* Make action buttons slightly larger on mobile for better touch targets */
  .timetable-card .absolute.top-1.right-1 button {
    @apply p-1;
  }
}

@media (max-width: 640px) {
  .timetable-grid {
    grid-template-columns: 70px repeat(5, minmax(100px, 1fr));
    grid-auto-rows: minmax(40px, auto);
  }

  .timetable-cell {
    min-height: 40px;
    @apply p-0.5;
    border: 2px solid hsl(var(--timetable-border));
    border-top: 1px solid hsl(var(--timetable-border));
    border-left: 1px solid hsl(var(--timetable-border));
  }

  .timetable-card {
    min-height: 32px;
    padding: 0.25rem;
    border: 2px solid hsl(var(--timetable-border) / 0.3);
  }

  .timetable-subject-title {
    @apply text-xs font-medium leading-tight;
  }
}

/* Enhanced Typography */
.timetable-subject-title {
  @apply font-semibold text-xs leading-tight;
  color: inherit;
}

.timetable-faculty-name {
  @apply text-xs font-medium opacity-90;
  color: inherit;
}

.timetable-room-number {
  @apply text-xs font-normal opacity-80;
  color: inherit;
}

.timetable-batch-name {
  @apply text-xs font-semibold;
  color: hsl(var(--primary-foreground));
  background: hsl(var(--primary) / 0.8);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Animation Classes */
.fade-in {
  animation: fade-in 0.3s ease-out;
}

.slide-in {
  animation: slide-in 0.3s ease-out;
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Focus and Accessibility */
.timetable-card:focus {
  @apply outline-none ring-2 ring-offset-2;
  ring-color: hsl(var(--ring));
}

.timetable-cell:focus-within {
  @apply ring-2 ring-offset-1;
  ring-color: hsl(var(--ring));
}

/* Additional Responsive Utilities */
@media (max-width: 475px) {
  .timetable-container {
    @apply rounded-md shadow;
  }

  .timetable-grid {
    grid-template-columns: 60px repeat(5, minmax(90px, 1fr));
    grid-auto-rows: minmax(35px, auto);
  }

  .timetable-cell {
    min-height: 35px;
    @apply p-0.5 text-xs;
    border: 2px solid hsl(var(--timetable-border));
    border-top: 1px solid hsl(var(--timetable-border));
    border-left: 1px solid hsl(var(--timetable-border));
  }

  .timetable-card {
    @apply text-xs;
    min-height: 28px;
    padding: 0.125rem 0.25rem;
    border: 2px solid hsl(var(--timetable-border) / 0.3);
  }

  .timetable-subject-title {
    @apply text-xs font-medium leading-tight;
  }

  /* Simplify batch display on very small screens */
  .timetable-card .bg-blue-100 {
    @apply px-1 py-0.5 text-xs;
  }
}

/* Print Styles */
@media print {
  .timetable-container {
    @apply shadow-none border;
    border-color: hsl(var(--border));
  }

  .timetable-card {
    @apply shadow-none;
  }

  .timetable-card:hover {
    @apply transform-none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .timetable-card {
    @apply border-2;
    border-color: hsl(var(--foreground));
  }

  .timetable-subject-title {
    @apply font-bold;
    color: hsl(var(--foreground));
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .timetable-card {
    @apply transition-none;
  }

  .timetable-cell {
    @apply transition-none;
  }

  .fade-in,
  .slide-in {
    animation: none;
  }
}

/* Utility Classes */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Landing Page Specific Styles */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(var(--foreground) / 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(var(--foreground) / 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced animations for landing page */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(var(--primary) / 0.3); }
  50% { box-shadow: 0 0 30px rgba(var(--primary) / 0.5); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Modern Component Styles */
@layer components {
  /* Enhanced Button Styles */
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all duration-200 font-medium;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-2 focus:ring-secondary focus:ring-offset-2 transition-all duration-200;
  }

  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-200;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-200;
  }

  /* Enhanced Card Styles */
  .card-modern {
    @apply bg-card text-card-foreground rounded-xl border shadow-sm hover:shadow-md transition-all duration-300;
  }

  .card-elevated {
    @apply bg-card text-card-foreground rounded-xl border shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .card-interactive {
    @apply bg-card text-card-foreground rounded-xl border shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-300 cursor-pointer;
  }

  /* Enhanced Input Styles */
  .input-modern {
    @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  /* Enhanced Navigation Styles */
  .nav-item {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200 hover:bg-accent hover:text-accent-foreground;
  }

  .nav-item-active {
    @apply bg-accent text-accent-foreground;
  }

  /* Enhanced Layout Containers */
  .container-modern {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .page-header {
    @apply space-y-0.5 pb-6;
  }

  .page-title {
    @apply text-2xl font-bold tracking-tight sm:text-3xl;
  }

  .page-description {
    @apply text-muted-foreground text-sm sm:text-base;
  }

  /* Enhanced Grid Layouts */
  .grid-responsive {
    @apply grid gap-4 sm:gap-6 lg:gap-8;
  }

  .grid-cards {
    @apply grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-dashboard {
    @apply grid gap-4 md:gap-6 lg:gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  /* Enhanced Form Layouts */
  .form-group {
    @apply space-y-2;
  }

  .form-row {
    @apply grid gap-4 sm:grid-cols-2;
  }

  .form-actions {
    @apply flex flex-col sm:flex-row gap-3 pt-6;
  }

  /* Enhanced Status Indicators */
  .status-success {
    @apply bg-success/10 text-success border border-success/20 rounded-lg px-3 py-2 text-sm font-medium;
  }

  .status-warning {
    @apply bg-warning/10 text-warning-foreground border border-warning/20 rounded-lg px-3 py-2 text-sm font-medium;
  }

  .status-error {
    @apply bg-destructive/10 text-destructive border border-destructive/20 rounded-lg px-3 py-2 text-sm font-medium;
  }

  .status-info {
    @apply bg-info/10 text-info-foreground border border-info/20 rounded-lg px-3 py-2 text-sm font-medium;
  }

  /* Enhanced Loading States */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }

  .loading-skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Enhanced Typography */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Enhanced Shadows */
  .shadow-soft {
    box-shadow: 0 2px 8px -2px hsl(var(--foreground) / 0.1);
  }

  .shadow-medium {
    box-shadow: 0 4px 16px -4px hsl(var(--foreground) / 0.15);
  }

  .shadow-strong {
    box-shadow: 0 8px 32px -8px hsl(var(--foreground) / 0.2);
  }

  /* Enhanced Borders */
  .border-gradient {
    border: 1px solid transparent;
    background: linear-gradient(hsl(var(--background)), hsl(var(--background))) padding-box,
                linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent))) border-box;
  }
}

/* Responsive Design Utilities */
@layer utilities {
  /* Extra Small Breakpoint Utilities */
  .xs\:inline {
    @media (min-width: 475px) {
      display: inline;
    }
  }

  .xs\:hidden {
    @media (min-width: 475px) {
      display: none;
    }
  }

  /* Responsive Text Sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  /* Responsive Spacing */
  .space-responsive {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }

  .gap-responsive {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }

  .p-responsive {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .px-responsive {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .py-responsive {
    @apply py-4 sm:py-6 lg:py-8;
  }

  /* Responsive Widths */
  .w-responsive-full {
    @apply w-full max-w-none sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl;
  }

  .w-responsive-container {
    @apply w-full max-w-7xl mx-auto;
  }

  /* Modern Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }
}

/* Modern Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Animation utility classes */
.animate-shimmer {
  animation: shimmer 2s infinite;
}
