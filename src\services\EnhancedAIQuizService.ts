/**
 * Enhanced AI Quiz Generation Service
 * 
 * This service handles multi-material analysis and module-wise quiz generation.
 * It can process syllabus + multiple textbooks + module content together.
 */

import { 
  AIQuestionGenerationRequest, 
  AIQuestionGenerationResponse, 
  AIGeneratedQuestion,
  QuestionType,
  DifficultyLevel,
  MaterialContent,
  ModuleMapping,
  CrossReferenceAnalysis
} from '@/types/quiz-system';

// OpenAI API configuration
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

export class EnhancedAIQuizService {

  /**
   * Generate module-wise quiz questions from multiple materials
   */
  static async generateModuleWiseQuestions(request: AIQuestionGenerationRequest): Promise<AIQuestionGenerationResponse> {
    const startTime = Date.now();
    
    try {
      if (!OPENAI_API_KEY) {
        throw new Error('OpenAI API key not configured');
      }

      // First, perform cross-reference analysis if not provided
      let crossRefAnalysis = request.cross_reference_analysis;
      if (!crossRefAnalysis) {
        crossRefAnalysis = await this.performCrossReferenceAnalysis(request.materials);
      }

      const prompt = this.buildEnhancedPrompt(request, crossRefAnalysis);
      
      const response = await fetch(OPENAI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: `You are an expert educational content creator specializing in module-wise quiz generation from multiple academic materials. You excel at:
              1. Cross-referencing syllabus modules with textbook chapters
              2. Creating questions that test both theoretical understanding and practical application
              3. Ensuring questions align with learning objectives and allocated hours
              4. Maintaining consistency across different source materials`
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 6000, // Increased for multi-material analysis
          response_format: { type: 'json_object' }
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const aiResponse = JSON.parse(data.choices[0].message.content);
      
      const processingTime = Date.now() - startTime;
      
      return {
        questions: this.validateAndFormatQuestions(aiResponse.questions),
        processing_time_ms: processingTime,
        confidence_score: aiResponse.confidence_score || 0.85,
        source_analysis: {
          key_topics: aiResponse.key_topics || [],
          content_complexity: aiResponse.content_complexity || request.difficulty_level,
          suggested_question_distribution: aiResponse.question_distribution || {},
          module_coverage: aiResponse.module_coverage || {},
          cross_reference_quality: aiResponse.cross_reference_quality || 0.8
        }
      };

    } catch (error) {
      console.error('Enhanced AI question generation failed:', error);
      throw new Error(`Failed to generate questions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Perform cross-reference analysis between materials
   */
  static async performCrossReferenceAnalysis(materials: MaterialContent[]): Promise<CrossReferenceAnalysis> {
    try {
      const syllabusContent = materials.find(m => m.material_type === 'syllabus');
      const textbooks = materials.filter(m => m.material_type === 'textbook');
      const modules = materials.filter(m => m.material_type === 'module');

      if (!syllabusContent) {
        throw new Error('Syllabus material is required for cross-reference analysis');
      }

      const analysisPrompt = `
Perform cross-reference analysis between the following academic materials:

SYLLABUS CONTENT:
${syllabusContent.extracted_text}

TEXTBOOK MATERIALS:
${textbooks.map(tb => `
${tb.material_identifier || 'Textbook'}:
${tb.extracted_text.substring(0, 2000)}...
`).join('\n')}

MODULE MATERIALS:
${modules.map(mod => `
${mod.material_identifier || 'Module'}:
${mod.extracted_text.substring(0, 1000)}...
`).join('\n')}

Analyze and return a JSON object with:
{
  "syllabus_textbook_mapping": {
    "MODULE-1": {
      "textbook_sources": ["Textbook 1", "Textbook 2"],
      "chapter_coverage": ["Chapter 1.1-1.4", "Chapter 2.1-2.5"],
      "content_alignment_score": 0.85
    }
  },
  "content_gaps": ["Topics mentioned in syllabus but not covered in textbooks"],
  "redundant_content": ["Content covered multiple times across materials"],
  "recommended_weightage": {
    "MODULE-1": 30,
    "MODULE-2": 40,
    "MODULE-3": 30
  }
}

Focus on:
1. Mapping syllabus modules to specific textbook chapters/sections
2. Identifying content alignment and gaps
3. Recommending question distribution based on content depth
`;

      const response = await fetch(OPENAI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are an expert at analyzing academic content relationships and creating comprehensive cross-reference mappings.'
            },
            {
              role: 'user',
              content: analysisPrompt
            }
          ],
          temperature: 0.3,
          max_tokens: 2000,
          response_format: { type: 'json_object' }
        })
      });

      if (!response.ok) {
        throw new Error(`Cross-reference analysis failed: ${response.status}`);
      }

      const data = await response.json();
      return JSON.parse(data.choices[0].message.content);

    } catch (error) {
      console.error('Cross-reference analysis failed:', error);
      // Return default analysis if fails
      return {
        syllabus_textbook_mapping: {},
        content_gaps: [],
        redundant_content: [],
        recommended_weightage: {}
      };
    }
  }

  /**
   * Build enhanced prompt for multi-material quiz generation
   */
  private static buildEnhancedPrompt(request: AIQuestionGenerationRequest, crossRefAnalysis: CrossReferenceAnalysis): string {
    const questionTypeInstructions = this.getQuestionTypeInstructions(request.question_types);
    const difficultyInstructions = this.getDifficultyInstructions(request.difficulty_level);
    
    const materialsSection = request.materials.map(material => `
${material.material_type.toUpperCase()} - ${material.material_identifier || 'Unnamed'}:
${material.extracted_text}
`).join('\n---\n');

    const moduleWeightage = request.module_weightage || crossRefAnalysis.recommended_weightage;
    const targetModules = request.target_modules.length > 0 ? request.target_modules : Object.keys(moduleWeightage);

    return `
Generate ${request.question_count} high-quality quiz questions for the subject "${request.subject_name}" focusing on the specified modules.

MATERIALS TO ANALYZE:
${materialsSection}

TARGET MODULES: ${targetModules.join(', ')}

MODULE WEIGHTAGE:
${Object.entries(moduleWeightage).map(([module, weight]) => `${module}: ${weight}%`).join('\n')}

CROSS-REFERENCE MAPPING:
${JSON.stringify(crossRefAnalysis.syllabus_textbook_mapping, null, 2)}

REQUIREMENTS:
- Difficulty Level: ${request.difficulty_level} ${difficultyInstructions}
- Question Types: ${request.question_types.join(', ')} ${questionTypeInstructions}
- Subject: ${request.subject_name}
- Focus Modules: ${targetModules.join(', ')}

RESPONSE FORMAT:
Return a JSON object with:
{
  "questions": [
    {
      "question_text": "Clear, well-formed question text",
      "question_type": "multiple_choice|true_false|short_answer|essay",
      "options": {"A": "Option 1", "B": "Option 2", "C": "Option 3", "D": "Option 4"},
      "correct_answer": "A|B|C|D|true|false|expected answer text",
      "difficulty_level": "easy|medium|hard",
      "topic_tags": ["topic1", "topic2"],
      "module_reference": "MODULE-1",
      "source_materials": ["Syllabus", "Textbook 1"],
      "ai_confidence_score": 0.95,
      "source_material_reference": "Brief reference to source content"
    }
  ],
  "confidence_score": 0.90,
  "key_topics": ["topic1", "topic2", "topic3"],
  "content_complexity": "medium",
  "question_distribution": {
    "multiple_choice": 15,
    "true_false": 3,
    "short_answer": 2
  },
  "module_coverage": {
    "MODULE-1": 8,
    "MODULE-2": 12
  },
  "cross_reference_quality": 0.85
}

ENHANCED QUALITY GUIDELINES:
1. Questions should integrate content from both syllabus objectives and textbook details
2. Ensure questions test understanding at the appropriate depth for allocated module hours
3. Cross-reference syllabus learning objectives with textbook explanations
4. Include questions that test both theoretical knowledge and practical application
5. Maintain consistency with module weightage distribution
6. Tag questions with specific module references
7. Ensure questions align with the cross-reference mapping provided

Generate exactly ${request.question_count} questions distributed according to module weightage.
`;
  }

  /**
   * Get instructions for specific question types
   */
  private static getQuestionTypeInstructions(questionTypes: QuestionType[]): string {
    const instructions: Record<QuestionType, string> = {
      multiple_choice: 'Create 4 options (A, B, C, D) with one clearly correct answer and plausible distractors.',
      true_false: 'Create clear statements that are definitively true or false based on the content.',
      short_answer: 'Create questions requiring 1-3 sentence answers demonstrating understanding.',
      essay: 'Create questions requiring detailed explanations or analysis (100-200 words).'
    };

    return questionTypes.map(type => `${type}: ${instructions[type]}`).join(' ');
  }

  /**
   * Get instructions for difficulty levels
   */
  private static getDifficultyInstructions(difficulty: DifficultyLevel): string {
    const instructions: Record<DifficultyLevel, string> = {
      easy: 'Focus on basic recall and simple understanding of key concepts.',
      medium: 'Include application of concepts and moderate analysis.',
      hard: 'Require synthesis, evaluation, and complex problem-solving.',
      mixed: 'Include a balanced mix of easy (30%), medium (50%), and hard (20%) questions.'
    };

    return instructions[difficulty];
  }

  /**
   * Validate and format AI-generated questions
   */
  private static validateAndFormatQuestions(questions: any[]): AIGeneratedQuestion[] {
    return questions.map((q, index) => {
      // Validate required fields
      if (!q.question_text || !q.question_type || !q.correct_answer) {
        throw new Error(`Invalid question at index ${index}: missing required fields`);
      }

      // Validate question type
      if (!['multiple_choice', 'true_false', 'short_answer', 'essay'].includes(q.question_type)) {
        throw new Error(`Invalid question type at index ${index}: ${q.question_type}`);
      }

      // Validate multiple choice questions have options
      if (q.question_type === 'multiple_choice' && (!q.options || Object.keys(q.options).length < 2)) {
        throw new Error(`Multiple choice question at index ${index} missing valid options`);
      }

      // Validate true/false questions
      if (q.question_type === 'true_false' && !['true', 'false'].includes(q.correct_answer.toLowerCase())) {
        throw new Error(`True/false question at index ${index} has invalid correct answer`);
      }

      return {
        question_text: q.question_text.trim(),
        question_type: q.question_type,
        options: q.question_type === 'multiple_choice' ? q.options : undefined,
        correct_answer: q.correct_answer,
        difficulty_level: q.difficulty_level || 'medium',
        topic_tags: Array.isArray(q.topic_tags) ? q.topic_tags : [],
        ai_confidence_score: Math.min(Math.max(q.ai_confidence_score || 0.8, 0), 1),
        source_material_reference: q.source_material_reference || '',
        module_reference: q.module_reference || '',
        source_materials: Array.isArray(q.source_materials) ? q.source_materials : []
      };
    });
  }
}
