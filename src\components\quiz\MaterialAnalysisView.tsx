import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BookOpen, 
  FileText, 
  Target, 
  CheckCircle, 
  AlertTriangle,
  Brain,
  Layers,
  Link
} from 'lucide-react';
import { CourseMaterial, CrossReferenceAnalysis } from '@/types/quiz-system';

interface MaterialAnalysisViewProps {
  materials: CourseMaterial[];
  crossReferenceAnalysis?: CrossReferenceAnalysis;
}

const MaterialAnalysisView: React.FC<MaterialAnalysisViewProps> = ({
  materials,
  crossReferenceAnalysis
}) => {
  // Organize materials by type
  const materialsByType = materials.reduce((acc, material) => {
    const type = material.material_type;
    if (!acc[type]) acc[type] = [];
    acc[type].push(material);
    return acc;
  }, {} as {[key: string]: CourseMaterial[]});

  // Extract detected modules
  const detectedModules = materials
    .filter(m => m.material_type === 'syllabus' && m.module_mappings)
    .flatMap(m => Object.keys(m.module_mappings || {}));

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'syllabus': return '📋';
      case 'textbook': return '📚';
      case 'module': return '📖';
      case 'reference': return '📑';
      case 'notes': return '📝';
      default: return '📄';
    }
  };

  const getProcessingStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'processing': return 'text-yellow-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Material Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            Material Analysis Overview
          </CardTitle>
          <CardDescription>
            Comprehensive analysis of uploaded course materials and their relationships
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Material Types Summary */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Object.entries(materialsByType).map(([type, typeMaterials]) => (
              <div key={type} className="p-3 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">{getTypeIcon(type)}</span>
                  <span className="font-medium capitalize">{type}</span>
                </div>
                <div className="text-2xl font-bold">{typeMaterials.length}</div>
                <div className="text-xs text-muted-foreground">
                  {typeMaterials.filter(m => m.processing_status === 'completed').length} processed
                </div>
              </div>
            ))}
          </div>

          {/* Processing Status */}
          <div className="space-y-2">
            <h4 className="font-medium">Processing Status</h4>
            {materials.map((material) => (
              <div key={material.id} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center gap-2">
                  <span>{getTypeIcon(material.material_type)}</span>
                  <div>
                    <p className="text-sm font-medium">{material.file_name}</p>
                    <p className="text-xs text-muted-foreground">
                      {material.material_identifier && `${material.material_identifier} • `}
                      {material.material_type}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={material.processing_status === 'completed' ? 'default' : 'secondary'}
                    className={getProcessingStatusColor(material.processing_status)}
                  >
                    {material.processing_status}
                  </Badge>
                  {material.processing_status === 'completed' && (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  )}
                  {material.processing_status === 'failed' && (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Module Detection */}
      {detectedModules.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Detected Modules
            </CardTitle>
            <CardDescription>
              Modules identified from syllabus materials for targeted quiz generation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {detectedModules.map((module) => (
                <Badge key={module} variant="outline" className="justify-center p-2">
                  {module}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cross-Reference Analysis */}
      {crossReferenceAnalysis && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Link className="h-5 w-5" />
              Cross-Reference Analysis
            </CardTitle>
            <CardDescription>
              AI analysis of relationships between syllabus modules and textbook content
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Module-Textbook Mapping */}
            {Object.keys(crossReferenceAnalysis.syllabus_textbook_mapping).length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium">Module-Textbook Mapping</h4>
                {Object.entries(crossReferenceAnalysis.syllabus_textbook_mapping).map(([module, mapping]) => (
                  <div key={module} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium">{module}</h5>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">Alignment:</span>
                        <Progress 
                          value={mapping.content_alignment_score * 100} 
                          className="w-20 h-2"
                        />
                        <span className="text-sm font-medium">
                          {(mapping.content_alignment_score * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex flex-wrap gap-1">
                        <span className="text-sm text-muted-foreground">Sources:</span>
                        {mapping.textbook_sources.map((source) => (
                          <Badge key={source} variant="secondary" className="text-xs">
                            {source}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex flex-wrap gap-1">
                        <span className="text-sm text-muted-foreground">Chapters:</span>
                        {mapping.chapter_coverage.map((chapter) => (
                          <Badge key={chapter} variant="outline" className="text-xs">
                            {chapter}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Content Gaps and Redundancies */}
            <div className="grid md:grid-cols-2 gap-4">
              {crossReferenceAnalysis.content_gaps.length > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Content Gaps Detected:</strong>
                    <ul className="mt-1 text-sm">
                      {crossReferenceAnalysis.content_gaps.map((gap, index) => (
                        <li key={index} className="text-muted-foreground">• {gap}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {crossReferenceAnalysis.redundant_content.length > 0 && (
                <Alert>
                  <Brain className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Redundant Content:</strong>
                    <ul className="mt-1 text-sm">
                      {crossReferenceAnalysis.redundant_content.map((redundant, index) => (
                        <li key={index} className="text-muted-foreground">• {redundant}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Recommended Weightage */}
            {Object.keys(crossReferenceAnalysis.recommended_weightage).length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Recommended Question Distribution</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {Object.entries(crossReferenceAnalysis.recommended_weightage).map(([module, weight]) => (
                    <div key={module} className="p-2 border rounded text-center">
                      <div className="font-medium text-sm">{module}</div>
                      <div className="text-lg font-bold text-primary">{weight}%</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* AI Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Material Completeness:</strong> {materials.length} materials uploaded across {Object.keys(materialsByType).length} categories.
                {materialsByType.syllabus ? ' Syllabus detected for module mapping.' : ' Consider uploading a syllabus for better module organization.'}
              </AlertDescription>
            </Alert>

            {materialsByType.textbook && materialsByType.textbook.length > 1 && (
              <Alert>
                <BookOpen className="h-4 w-4" />
                <AlertDescription>
                  <strong>Multi-Textbook Support:</strong> {materialsByType.textbook.length} textbooks detected. 
                  AI will cross-reference content across all textbooks for comprehensive question generation.
                </AlertDescription>
              </Alert>
            )}

            {detectedModules.length > 0 && (
              <Alert>
                <Target className="h-4 w-4" />
                <AlertDescription>
                  <strong>Module-Wise Generation Ready:</strong> {detectedModules.length} modules detected. 
                  You can now generate targeted quizzes for specific modules with cross-referenced content.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MaterialAnalysisView;
