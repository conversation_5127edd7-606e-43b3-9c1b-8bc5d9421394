import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import {
  Star,
  User,
  BookOpen,
  Clock,
  CheckCircle,
  AlertCircle,
  Send
} from 'lucide-react';

import { FeedbackService } from '@/services/FeedbackService';
import {
  FeedbackQuestion,
  FeedbackQuestionResponse,
  SubmitFeedbackRequest,
  PendingFeedback
} from '@/types/feedback-system';

interface StudentFeedbackFormProps {
  studentUsn: string;
  studentName: string;
  department: string;
  semester: string;
  section: string;
  pendingFeedback: PendingFeedback;
  onSubmissionComplete: () => void;
}

const StudentFeedbackForm: React.FC<StudentFeedbackFormProps> = ({
  studentUsn,
  studentName,
  department,
  semester,
  section,
  pendingFeedback,
  onSubmissionComplete
}) => {
  const [questions, setQuestions] = useState<FeedbackQuestion[]>([]);
  const [responses, setResponses] = useState<FeedbackQuestionResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const { toast } = useToast();

  useEffect(() => {
    loadQuestions();
  }, []);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      const questionsData = await FeedbackService.getFeedbackQuestions();
      setQuestions(questionsData);
      
      // Initialize responses array
      const initialResponses = questionsData.map(q => ({
        question_id: q.id,
        question_number: q.question_number,
        rating: 0
      }));
      setResponses(initialResponses);
    } catch (error) {
      console.error('Error loading questions:', error);
      toast({
        title: "Error",
        description: "Failed to load feedback questions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRatingChange = (questionId: string, rating: number) => {
    setResponses(prev => 
      prev.map(response => 
        response.question_id === questionId 
          ? { ...response, rating }
          : response
      )
    );
  };

  const handleSubmit = async () => {
    // Validate all questions are answered
    const unansweredQuestions = responses.filter(r => r.rating === 0);
    if (unansweredQuestions.length > 0) {
      toast({
        title: "Incomplete Feedback",
        description: `Please rate all questions. ${unansweredQuestions.length} questions remaining.`,
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);

      const submitRequest: SubmitFeedbackRequest = {
        session_id: pendingFeedback.session_id,
        faculty_id: pendingFeedback.faculty_id,
        subject_code: pendingFeedback.subject_code,
        responses: responses
      };

      await FeedbackService.submitStudentFeedback(
        submitRequest,
        studentUsn,
        studentName,
        department,
        semester,
        section
      );

      toast({
        title: "Feedback Submitted",
        description: `Your feedback for ${pendingFeedback.faculty_name} has been submitted successfully.`,
      });

      onSubmissionComplete();
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast({
        title: "Submission Failed",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const getCompletionPercentage = () => {
    const answeredQuestions = responses.filter(r => r.rating > 0).length;
    return (answeredQuestions / questions.length) * 100;
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 8) return 'text-green-600';
    if (rating >= 6) return 'text-yellow-600';
    if (rating >= 4) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="loading-spinner h-8 w-8 mx-auto"></div>
          <p className="text-muted-foreground">Loading feedback form...</p>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const currentResponse = responses.find(r => r.question_id === currentQuestion?.id);

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Faculty Feedback Form
          </CardTitle>
          <CardDescription>
            Please rate {pendingFeedback.faculty_name} for {pendingFeedback.subject_name} ({pendingFeedback.subject_code})
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <span>Progress: {Math.round(getCompletionPercentage())}% Complete</span>
              <span>{responses.filter(r => r.rating > 0).length} of {questions.length} questions answered</span>
            </div>
            <Progress value={getCompletionPercentage()} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Faculty Info */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">{pendingFeedback.faculty_name}</span>
            </div>
            <div className="flex items-center gap-2">
              <BookOpen className="h-4 w-4 text-muted-foreground" />
              <span>{pendingFeedback.subject_name}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>Due: {new Date(pendingFeedback.deadline).toLocaleDateString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Questions */}
      <div className="space-y-6">
        {questions.map((question, index) => {
          const response = responses.find(r => r.question_id === question.id);
          const isAnswered = response && response.rating > 0;

          return (
            <Card key={question.id} className={isAnswered ? 'border-green-200 bg-green-50/50' : ''}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-bold">
                        {question.question_number}
                      </span>
                      {question.question_text}
                    </CardTitle>
                    <CardDescription>
                      Rate from 1 (Poor) to 10 (Excellent)
                    </CardDescription>
                  </div>
                  {isAnswered && (
                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0 mt-1" />
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Rating Scale */}
                  <div className="flex flex-wrap gap-2">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
                      <Button
                        key={rating}
                        variant={response?.rating === rating ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleRatingChange(question.id, rating)}
                        className={`w-12 h-12 ${
                          response?.rating === rating 
                            ? getRatingColor(rating) + ' bg-primary text-primary-foreground' 
                            : 'hover:bg-muted'
                        }`}
                      >
                        {rating}
                      </Button>
                    ))}
                  </div>

                  {/* Rating Labels */}
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>1 - Poor</span>
                    <span>5 - Average</span>
                    <span>10 - Excellent</span>
                  </div>

                  {/* Current Rating Display */}
                  {response && response.rating > 0 && (
                    <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                      <Star className={`h-4 w-4 ${getRatingColor(response.rating)}`} />
                      <span className="font-medium">
                        Your Rating: {response.rating}/10
                      </span>
                      <Badge variant="secondary" className={getRatingColor(response.rating)}>
                        {response.rating >= 8 ? 'Excellent' : 
                         response.rating >= 6 ? 'Good' : 
                         response.rating >= 4 ? 'Average' : 'Needs Improvement'}
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Submit Section */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {getCompletionPercentage() < 100 && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Please answer all questions before submitting your feedback.
                </AlertDescription>
              </Alert>
            )}

            <div className="flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                {responses.filter(r => r.rating > 0).length} of {questions.length} questions completed
              </div>
              <Button
                onClick={handleSubmit}
                disabled={getCompletionPercentage() < 100 || submitting}
                className="gap-2"
                size="lg"
              >
                <Send className="h-4 w-4" />
                {submitting ? 'Submitting...' : 'Submit Feedback'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentFeedbackForm;
