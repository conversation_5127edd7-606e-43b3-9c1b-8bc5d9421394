import { supabase } from "@/integrations/supabase/client";

export interface StudentAuthData {
  id: string;
  usn: string;
  email: string;
  is_verified: boolean;
  created_at: string;
}

export class SimpleStudentAuthService {
  // USN format validation regex: 1KS23CS001
  private static readonly USN_REGEX = /^1KS\d{2}[A-Z]{2,4}\d{3}$/;
  
  // Email format validation regex
  private static readonly EMAIL_REGEX = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;

  /**
   * Validate USN format
   */
  static validateUSN(usn: string): boolean {
    return this.USN_REGEX.test(usn);
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    return this.EMAIL_REGEX.test(email);
  }

  /**
   * Simple hash function for browser compatibility
   */
  private static async hashPassword(password: string): Promise<string> {
    // Use Web Crypto API for browser compatibility
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'student_salt_2024');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Ensure student_auth table exists
   */
  static async ensureTableExists(): Promise<void> {
    try {
      // Try different RPC function names
      let error = null;

      // Try execute_sql with sql_query parameter
      try {
        const result = await supabase.rpc('execute_sql', {
          sql_query: `
            CREATE TABLE IF NOT EXISTS student_auth (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                usn TEXT NOT NULL UNIQUE,
                email TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                is_verified BOOLEAN DEFAULT false,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            );

            CREATE INDEX IF NOT EXISTS idx_student_auth_usn ON student_auth(usn);
            CREATE INDEX IF NOT EXISTS idx_student_auth_email ON student_auth(email);
          `
        });
        console.log('✅ Table creation successful');
        return;
      } catch (e) {
        error = e;
      }

      // If that fails, try with query parameter
      try {
        const result = await supabase.rpc('execute_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS student_auth (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                usn TEXT NOT NULL UNIQUE,
                email TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                is_verified BOOLEAN DEFAULT false,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            );

            CREATE INDEX IF NOT EXISTS idx_student_auth_usn ON student_auth(usn);
            CREATE INDEX IF NOT EXISTS idx_student_auth_email ON student_auth(email);
          `
        });
        console.log('✅ Table creation successful');
        return;
      } catch (e) {
        error = e;
      }

      console.warn('Could not create student_auth table via RPC, table might already exist:', error);
    } catch (error) {
      console.warn('Table creation failed, continuing anyway:', error);
    }
  }

  /**
   * Bulk register students with default password (with upsert for fresh semester uploads)
   */
  static async bulkRegisterStudents(
    studentsData: Array<{
      usn: string;
      email?: string;
      student_name?: string;
    }>,
    createdBy: string
  ): Promise<{ success: StudentAuthData[], errors: { row: number, error: string }[] }> {
    const success: StudentAuthData[] = [];
    const errors: { row: number, error: string }[] = [];
    const defaultPassword = 'Password@123';

    // Ensure table exists
    await this.ensureTableExists();

    console.log(`📋 Processing ${studentsData.length} students for auth account creation...`);

    for (let i = 0; i < studentsData.length; i++) {
      try {
        const studentData = studentsData[i];

        // Validate USN format
        if (!this.validateUSN(studentData.usn)) {
          throw new Error('Invalid USN format. Expected format: 1KS23CS001');
        }

        // Generate email if not provided
        const email = studentData.email || `${studentData.usn.toLowerCase()}@student.college.edu`;

        // Hash default password
        const passwordHash = await this.hashPassword(defaultPassword);

        // Check if student already exists
        const { data: existingStudent, error: checkError } = await supabase
          .from('student_auth')
          .select('id')
          .eq('usn', studentData.usn.toUpperCase())
          .maybeSingle();

        let result;
        if (existingStudent) {
          // Update existing student (reset password for fresh semester)
          const { data: updatedData, error: updateError } = await supabase
            .from('student_auth')
            .update({
              email: email.toLowerCase(),
              password_hash: passwordHash,
              is_verified: false,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingStudent.id)
            .select('id, usn, email, is_verified, created_at')
            .single();

          if (updateError) {
            throw new Error(`Failed to update auth account: ${updateError.message}`);
          }
          result = updatedData;
        } else {
          // Create new student
          const { data: newData, error: insertError } = await supabase
            .from('student_auth')
            .insert({
              usn: studentData.usn.toUpperCase(),
              email: email.toLowerCase(),
              password_hash: passwordHash,
              is_verified: false
            })
            .select('id, usn, email, is_verified, created_at')
            .single();

          if (insertError) {
            throw new Error(`Failed to create auth account: ${insertError.message}`);
          }
          result = newData;
        }

        success.push(result);

      } catch (error) {
        errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`✅ Auth account processing completed: ${success.length} success, ${errors.length} errors`);
    return { success, errors };
  }

  /**
   * Clear all existing auth accounts (for fresh semester start)
   */
  static async clearAllAuthAccounts(): Promise<void> {
    try {
      const { error } = await supabase
        .from('student_auth')
        .delete()
        .neq('id', '********-0000-0000-0000-************'); // Delete all records

      if (error) {
        throw new Error(`Failed to clear auth accounts: ${error.message}`);
      }

      console.log('✅ All auth accounts cleared successfully');
    } catch (error) {
      console.error('Error clearing auth accounts:', error);
      throw error;
    }
  }

  /**
   * Authenticate student login
   */
  static async authenticateStudent(usn: string, password: string): Promise<StudentAuthData | null> {
    try {
      // Validate USN format
      if (!this.validateUSN(usn)) {
        throw new Error('Invalid USN format');
      }

      // Get student record
      const { data: student, error } = await supabase
        .from('student_auth')
        .select('id, usn, email, password_hash, is_verified, created_at')
        .eq('usn', usn.toUpperCase())
        .single();

      if (error || !student) {
        return null; // Invalid USN
      }

      // Compare password
      const hashedInput = await this.hashPassword(password);
      
      if (hashedInput !== student.password_hash) {
        return null; // Invalid password
      }

      // Return student data (without password hash)
      return {
        id: student.id,
        usn: student.usn,
        email: student.email,
        is_verified: student.is_verified,
        created_at: student.created_at
      };
    } catch (error) {
      console.error('Authentication error:', error);
      return null;
    }
  }

  /**
   * Get student details from class_students table
   */
  static async getStudentDetails(usn: string): Promise<{
    student_name: string;
    semester: string;
    section: string;
    department: string;
    email?: string;
  } | null> {
    try {
      const { data, error } = await supabase
        .from('class_students')
        .select('student_name, semester, section, department, email')
        .eq('usn', usn.toUpperCase())
        .single();

      if (error || !data) {
        console.log('Student details not found in class_students table for USN:', usn);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching student details:', error);
      return null;
    }
  }
}
