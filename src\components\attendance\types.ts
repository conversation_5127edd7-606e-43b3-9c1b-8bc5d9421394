
// Ensure the attendance status type is properly constrained
export type AttendanceStatus = "present" | "absent";

export interface Student {
  id: string;
  name: string;
  department: string;
  section: string;
  semester: string;
  enrollment_year: string;
  created_at: string;
  updated_at: string;
  attendance: {
    status: AttendanceStatus | null;
  };
}

export interface AttendanceRecord {
  student_id: string;
  faculty_id: string;
  date: string;
  subject_id: string;
  status: AttendanceStatus;
}

export interface SubjectOption {
  id: string;
  name: string;
  code: string;
  department?: string;
  semester?: string;
  section?: string;
}

export interface StudentAttendanceSummary {
  name: string;
  present: number;
  absent: number;
  percentage: number;
}

export interface DailyAttendanceEntry {
  date: string;
  present: number;
  absent: number;
}

export interface OverallAttendanceEntry {
  name: string;
  value: number;
}

export interface ChartData {
  dailyData: DailyAttendanceEntry[];
  overallData: OverallAttendanceEntry[];
}
