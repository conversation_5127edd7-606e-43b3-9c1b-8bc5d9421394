import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  BookOpen, 
  Users, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Eye,
  Download,
  Brain
} from 'lucide-react';
import { useFacultyMaterialsBySubject } from '@/hooks/useFacultyContext';
import { CourseMaterial } from '@/types/quiz-system';

const ContextAwareMaterialsView: React.FC = () => {
  const { materialsBySubject, loading, error } = useFacultyMaterialsBySubject();

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'syllabus': return '📋';
      case 'textbook': return '📚';
      case 'module': return '📖';
      case 'reference': return '📑';
      case 'notes': return '📝';
      default: return '📄';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'processing': return 'secondary';
      case 'failed': return 'destructive';
      default: return 'outline';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load materials: {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (materialsBySubject.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <FileText className="h-16 w-16 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-semibold mb-2">No Course Materials Yet</h3>
          <p className="text-muted-foreground mb-4">
            Upload course materials for your assigned subjects to get started with AI quiz generation.
          </p>
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            Upload First Material
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Materials Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Total Subjects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{materialsBySubject.length}</div>
            <p className="text-xs text-muted-foreground">With uploaded materials</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Total Materials</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {materialsBySubject.reduce((sum, subject) => sum + subject.materials.length, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Across all subjects</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Processing Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(
                (materialsBySubject.reduce((sum, subject) => 
                  sum + subject.materials.filter((m: CourseMaterial) => m.processing_status === 'completed').length, 0
                ) / materialsBySubject.reduce((sum, subject) => sum + subject.materials.length, 0)) * 100
              )}%
            </div>
            <p className="text-xs text-muted-foreground">Materials processed</p>
          </CardContent>
        </Card>
      </div>

      {/* Materials by Subject */}
      <div className="space-y-6">
        {materialsBySubject.map((subjectGroup: any) => (
          <Card key={`${subjectGroup.subject_code}-${subjectGroup.department}-${subjectGroup.semester}-${subjectGroup.section}`}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    {subjectGroup.subject_code} - {subjectGroup.subject_name}
                  </CardTitle>
                  <CardDescription>
                    {subjectGroup.department} | Semester {subjectGroup.semester} | Section {subjectGroup.section} | {subjectGroup.academic_year}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    {subjectGroup.materials.length} {subjectGroup.materials.length === 1 ? 'Material' : 'Materials'}
                  </Badge>
                  <Badge variant="secondary">
                    <Users className="h-3 w-3 mr-1" />
                    Class
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Material Type Summary */}
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">Material Types</h4>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(
                    subjectGroup.materials.reduce((acc: any, material: CourseMaterial) => {
                      acc[material.material_type] = (acc[material.material_type] || 0) + 1;
                      return acc;
                    }, {})
                  ).map(([type, count]) => (
                    <Badge key={type} variant="outline" className="text-xs">
                      {getTypeIcon(type)} {type} ({count})
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Individual Materials */}
              <div className="space-y-3">
                {subjectGroup.materials.map((material: CourseMaterial) => (
                  <div key={material.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="text-xl">{getTypeIcon(material.material_type)}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium">{material.file_name}</p>
                          <Badge variant="outline" className="text-xs">
                            {material.material_type}
                          </Badge>
                          {material.material_identifier && (
                            <Badge variant="secondary" className="text-xs">
                              {material.material_identifier}
                            </Badge>
                          )}
                        </div>
                        {material.material_description && (
                          <p className="text-sm text-muted-foreground italic">
                            {material.material_description}
                          </p>
                        )}
                        <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                          <span>{(material.file_size_bytes / 1024 / 1024).toFixed(2)} MB</span>
                          <span>Uploaded: {new Date(material.upload_date).toLocaleDateString()}</span>
                          {material.processed_date && (
                            <span>Processed: {new Date(material.processed_date).toLocaleDateString()}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        {getStatusIcon(material.processing_status)}
                        <Badge variant={getStatusColor(material.processing_status) as any}>
                          {material.processing_status}
                        </Badge>
                      </div>
                      
                      {material.processing_status === 'completed' && (
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View Analysis
                        </Button>
                      )}
                      
                      {material.processing_status === 'failed' && material.processing_error && (
                        <div className="text-xs text-red-600 max-w-xs">
                          Error: {material.processing_error}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* AI Readiness Status */}
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Brain className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">AI Quiz Generation Readiness</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {subjectGroup.materials.some((m: CourseMaterial) => m.material_type === 'syllabus' && m.processing_status === 'completed') && (
                      <Badge variant="default" className="text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Syllabus Ready
                      </Badge>
                    )}
                    {subjectGroup.materials.some((m: CourseMaterial) => m.material_type === 'textbook' && m.processing_status === 'completed') && (
                      <Badge variant="default" className="text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Textbook Ready
                      </Badge>
                    )}
                    {subjectGroup.materials.filter((m: CourseMaterial) => m.processing_status === 'completed').length >= 2 && (
                      <Badge variant="default" className="text-xs bg-green-600">
                        <Brain className="h-3 w-3 mr-1" />
                        Ready for AI Quiz
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="mt-2 text-xs text-muted-foreground">
                  {subjectGroup.materials.filter((m: CourseMaterial) => m.processing_status === 'completed').length} of {subjectGroup.materials.length} materials processed
                  {subjectGroup.materials.filter((m: CourseMaterial) => m.processing_status === 'completed').length >= 2 
                    ? ' • Ready for multi-material AI quiz generation'
                    : ' • Upload more materials for better AI analysis'
                  }
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ContextAwareMaterialsView;
