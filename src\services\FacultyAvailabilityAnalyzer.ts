// FacultyAvailabilityAnalyzer.ts - Analyzes faculty availability patterns for intelligent conflict resolution

import { supabase } from "@/integrations/supabase/client";
import { FacultyAvailabilityProfile, ConflictDetectionParams } from "@/types/ConflictResolution";

export class FacultyAvailabilityAnalyzer {
  /**
   * Analyze faculty availability and create detailed profiles for conflict resolution
   */
  static async analyzeFacultyAvailability(
    facultyIds: string[],
    params: ConflictDetectionParams
  ): Promise<FacultyAvailabilityProfile[]> {
    const profiles: FacultyAvailabilityProfile[] = [];

    for (const facultyId of facultyIds) {
      try {
        const profile = await this.createFacultyProfile(facultyId, params);
        profiles.push(profile);
      } catch (error) {
        console.error(`Error analyzing availability for faculty ${facultyId}:`, error);
      }
    }

    return profiles;
  }

  /**
   * Create a detailed availability profile for a single faculty member
   */
  private static async createFacultyProfile(
    facultyId: string,
    params: ConflictDetectionParams
  ): Promise<FacultyAvailabilityProfile> {
    // Get faculty basic info
    const { data: faculty, error: facultyError } = await supabase
      .from("employee_details")
      .select("id, full_name, vacant_by_day, vacant_count_by_day")
      .eq("id", facultyId)
      .single();

    if (facultyError || !faculty) {
      throw new Error(`Faculty ${facultyId} not found`);
    }

    // Get all current timetable slots for this faculty
    const { data: currentSlots, error: slotsError } = await supabase
      .from("timetable_slots")
      .select("*")
      .eq("academic_year", params.academicYear)
      .eq("department", params.department)
      .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`);

    if (slotsError) {
      throw new Error(`Error fetching slots for faculty ${facultyId}: ${slotsError.message}`);
    }

    const vacant_by_day = faculty.vacant_by_day || {};
    const vacant_count_by_day = faculty.vacant_count_by_day || {};

    // Calculate availability metrics
    const availableSlotsByDay: Record<string, string[]> = {};
    const occupiedSlotsByDay: Record<string, string[]> = {};
    const workloadDistribution: Record<string, number> = {};

    // Standard working days
    const workingDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

    for (const day of workingDays) {
      availableSlotsByDay[day] = vacant_count_by_day[day] || [];
      
      // Get occupied slots for this day
      const daySlots = currentSlots?.filter(slot => slot.day === day) || [];
      occupiedSlotsByDay[day] = daySlots.map(slot => slot.time_slot);
      
      // Calculate workload for this day
      workloadDistribution[day] = daySlots.length;
    }

    // Calculate total available slots
    const totalAvailableSlots = Object.values(vacant_by_day).reduce((sum, count) => sum + count, 0);

    // Calculate flexibility score (higher = more flexible)
    const flexibilityScore = this.calculateFlexibilityScore(
      availableSlotsByDay,
      occupiedSlotsByDay,
      workloadDistribution
    );

    // Identify preferred and constrained days
    const { preferredDays, constrainedDays } = this.identifyDayPreferences(
      availableSlotsByDay,
      workloadDistribution
    );

    return {
      facultyId,
      facultyName: faculty.full_name,
      totalAvailableSlots,
      availableSlotsByDay,
      occupiedSlotsByDay,
      flexibilityScore,
      workloadDistribution,
      preferredDays,
      constrainedDays
    };
  }

  /**
   * Calculate flexibility score based on availability patterns
   */
  private static calculateFlexibilityScore(
    availableSlotsByDay: Record<string, string[]>,
    occupiedSlotsByDay: Record<string, string[]>,
    workloadDistribution: Record<string, number>
  ): number {
    let score = 0;

    // Base score from total available slots
    const totalAvailable = Object.values(availableSlotsByDay)
      .reduce((sum, slots) => sum + slots.length, 0);
    score += totalAvailable * 10;

    // Bonus for even distribution across days
    const workloadValues = Object.values(workloadDistribution);
    const maxWorkload = Math.max(...workloadValues);
    const minWorkload = Math.min(...workloadValues);
    const distributionBonus = Math.max(0, 50 - (maxWorkload - minWorkload) * 10);
    score += distributionBonus;

    // Bonus for having free days
    const freeDays = Object.entries(workloadDistribution)
      .filter(([_, workload]) => workload === 0).length;
    score += freeDays * 20;

    // Penalty for overloaded days (more than 4 slots per day)
    const overloadedDays = Object.values(workloadDistribution)
      .filter(workload => workload > 4).length;
    score -= overloadedDays * 15;

    return Math.max(0, score);
  }

  /**
   * Identify preferred days (high availability) and constrained days (low availability)
   */
  private static identifyDayPreferences(
    availableSlotsByDay: Record<string, string[]>,
    workloadDistribution: Record<string, number>
  ): { preferredDays: string[]; constrainedDays: string[] } {
    const dayScores = Object.entries(availableSlotsByDay).map(([day, slots]) => ({
      day,
      availableSlots: slots.length,
      currentWorkload: workloadDistribution[day] || 0,
      score: slots.length - (workloadDistribution[day] || 0) * 0.5
    }));

    // Sort by score (higher is better)
    dayScores.sort((a, b) => b.score - a.score);

    const preferredDays = dayScores
      .filter(d => d.score > 2 && d.availableSlots > 2)
      .map(d => d.day);

    const constrainedDays = dayScores
      .filter(d => d.score < 1 || d.availableSlots < 2)
      .map(d => d.day);

    return { preferredDays, constrainedDays };
  }

  /**
   * Find faculty members with high availability for potential swapping
   */
  static async findHighAvailabilityFaculty(
    excludeFacultyIds: string[],
    params: ConflictDetectionParams,
    minFlexibilityScore: number = 100
  ): Promise<FacultyAvailabilityProfile[]> {
    // Get all faculty members in the department
    const { data: allFaculty, error: facultyError } = await supabase
      .from("employee_details")
      .select("id, full_name")
      .eq("department", params.department)
      .contains("roles", ["faculty"])
      .not("id", "in", `(${excludeFacultyIds.join(",")})`);

    if (facultyError || !allFaculty) {
      console.error("Error fetching faculty members:", facultyError);
      return [];
    }

    const facultyIds = allFaculty.map(f => f.id);
    const profiles = await this.analyzeFacultyAvailability(facultyIds, params);

    // Filter by flexibility score and sort by availability
    return profiles
      .filter(profile => profile.flexibilityScore >= minFlexibilityScore)
      .sort((a, b) => b.flexibilityScore - a.flexibilityScore);
  }

  /**
   * Check if a faculty member is available for a specific time slot
   */
  static async isFacultyAvailable(
    facultyId: string,
    day: string,
    timeSlot: string,
    params: ConflictDetectionParams
  ): Promise<boolean> {
    const profile = await this.createFacultyProfile(facultyId, params);
    return profile.availableSlotsByDay[day]?.includes(timeSlot) || false;
  }

  /**
   * Get the best available time slots for a faculty member on a specific day
   */
  static async getBestAvailableSlots(
    facultyId: string,
    day: string,
    params: ConflictDetectionParams,
    excludeSlots: string[] = []
  ): Promise<string[]> {
    const profile = await this.createFacultyProfile(facultyId, params);
    const availableSlots = profile.availableSlotsByDay[day] || [];
    
    return availableSlots
      .filter(slot => !excludeSlots.includes(slot))
      .sort(); // Sort by time for consistent ordering
  }
}
