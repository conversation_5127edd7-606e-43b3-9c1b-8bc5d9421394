import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import {
  Upload,
  FileText,
  BookOpen,
  GraduationCap,
  FileCheck,
  AlertCircle,
  Clock,
  CheckCircle,
  XCircle,
  Brain
} from 'lucide-react';

import { ComprehensiveQuizService, CourseMaterial, CourseUploadRequest } from '@/services/ComprehensiveQuizService';
import { supabase } from '@/integrations/supabase/client';

interface CourseMaterialUploadProps {
  onMaterialUploaded: (material: CourseMaterial) => void;
  materials: CourseMaterial[];
}

const CourseMaterialUpload: React.FC<CourseMaterialUploadProps> = ({
  onMaterialUploaded,
  materials
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [facultyAssignments, setFacultyAssignments] = useState<any[]>([]);
  const [loadingAssignments, setLoadingAssignments] = useState(true);
  const [formData, setFormData] = useState({
    selected_assignment: '', // Faculty's subject-class assignment
    content_type: 'syllabus' as 'syllabus' | 'textbook' | 'reference' | 'notes',
    material_identifier: '',
    file: null as File | null,
    // Manual entry fallback
    manual_subject_code: '',
    manual_subject_name: '',
    manual_semester: '',
    manual_section: '',
    use_manual_entry: false
  });

  const { user } = useAuth();
  const { toast } = useToast();
  const { department, departmentName, loading: departmentLoading } = useUserDepartment();

  // Fetch faculty's teaching assignments
  useEffect(() => {
    if (departmentLoading || !user?.id || !department) {
      return;
    }

    let isCancelled = false;

    const fetchAssignments = async () => {
      try {
        setLoadingAssignments(true);
        console.log('🔍 Fetching faculty assignments for:', {
          userId: user.id,
          department,
          userEmail: user.email
        });

        // Primary source: simplified_subject_faculty_mappings
        const { data: mappings, error: mappingError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            department,
            academic_year
          `)
          .or(`faculty_1_id.eq.${user.id},faculty_2_id.eq.${user.id}`);

        if (isCancelled) return;

        console.log('📊 Subject mappings result:', {
          count: mappings?.length || 0,
          mappings: mappings?.slice(0, 3)
        });

        let allAssignments = [...(mappings || [])];

        // Secondary source: Get unique subjects from uploaded course materials
        const { data: materials, error: materialsError } = await supabase
          .from('course_materials')
          .select(`
            subject_code,
            subject_name,
            semester,
            section,
            academic_year
          `)
          .eq('faculty_id', user.id)
          .eq('processing_status', 'completed');

        if (!materialsError && materials && materials.length > 0) {
          console.log('📚 Course materials result:', {
            count: materials.length,
            materials: materials.slice(0, 3)
          });

          // Add unique assignments from course materials
          materials.forEach(material => {
            const key = `${material.subject_code}-${material.semester}-${material.section}`;
            const exists = allAssignments.find(a =>
              `${a.subject_code}-${a.semester}-${a.section}` === key
            );

            if (!exists) {
              allAssignments.push({
                subject_code: material.subject_code,
                subject_name: material.subject_name,
                subject_type: 'theory', // Default type
                semester: material.semester,
                section: material.section,
                department: department,
                academic_year: material.academic_year
              });
            }
          });
        }

        // Remove duplicates and sort
        const uniqueAssignments = allAssignments.reduce((acc, assignment) => {
          const key = `${assignment.subject_code}-${assignment.semester}-${assignment.section}`;
          if (!acc.find(a => `${a.subject_code}-${a.semester}-${a.section}` === key)) {
            acc.push(assignment);
          }
          return acc;
        }, [] as any[]);

        // Sort by subject code
        uniqueAssignments.sort((a, b) => a.subject_code.localeCompare(b.subject_code));

        console.log('✅ Final faculty assignments:', uniqueAssignments);
        setFacultyAssignments(uniqueAssignments);

      } catch (error) {
        if (!isCancelled) {
          console.error('💥 Failed to fetch faculty assignments:', error);
          setFacultyAssignments([]);
        }
      } finally {
        if (!isCancelled) {
          setLoadingAssignments(false);
        }
      }
    };

    fetchAssignments();

    return () => {
      isCancelled = true;
    };
  }, [user?.id, department, departmentLoading]);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: 'Invalid File Type',
          description: 'Please upload PDF, DOC, DOCX, or TXT files only.',
          variant: 'destructive',
        });
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: 'File Too Large',
          description: 'Please upload files smaller than 10MB.',
          variant: 'destructive',
        });
        return;
      }

      setFormData(prev => ({ ...prev, file }));
    }
  }, [toast]);

  const handleUpload = async () => {
    if (!user?.id || !department) return;

    let subjectInfo;

    if (formData.use_manual_entry) {
      // Validate manual entry fields
      const missingFields = [];
      if (!formData.manual_subject_code) missingFields.push('Subject Code');
      if (!formData.manual_subject_name) missingFields.push('Subject Name');
      if (!formData.manual_semester) missingFields.push('Semester');
      if (!formData.manual_section) missingFields.push('Section');
      if (!formData.file) missingFields.push('File');

      if (missingFields.length > 0) {
        toast({
          title: 'Missing Information',
          description: `Please provide: ${missingFields.join(', ')}`,
          variant: 'destructive',
        });
        return;
      }

      subjectInfo = {
        subject_code: formData.manual_subject_code,
        subject_name: formData.manual_subject_name,
        semester: formData.manual_semester,
        section: formData.manual_section,
        academic_year: `${new Date().getFullYear()}-${(new Date().getFullYear() + 1).toString().slice(-2)}`
      };
    } else {
      // Validate assignment selection
      const missingFields = [];
      if (!formData.selected_assignment) missingFields.push('Subject & Class');
      if (!formData.file) missingFields.push('File');

      if (missingFields.length > 0) {
        toast({
          title: 'Missing Information',
          description: `Please select: ${missingFields.join(', ')}`,
          variant: 'destructive',
        });
        return;
      }

      // Get selected assignment details
      const selectedAssignment = facultyAssignments.find(a =>
        `${a.subject_code}-${a.semester}-${a.section}` === formData.selected_assignment
      );

      if (!selectedAssignment) {
        toast({
          title: 'Invalid Selection',
          description: 'Selected assignment not found. Please try again.',
          variant: 'destructive',
        });
        return;
      }

      subjectInfo = selectedAssignment;
    }

    try {
      setUploading(true);
      setUploadProgress(0);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const uploadRequest: CourseUploadRequest = {
        faculty_id: user.id,
        subject_code: subjectInfo.subject_code,
        subject_name: subjectInfo.subject_name,
        department: department,
        semester: subjectInfo.semester,
        section: subjectInfo.section,
        academic_year: subjectInfo.academic_year,
        content_type: formData.content_type,
        file: formData.file!,
        material_identifier: formData.material_identifier || undefined
      };

      const material = await ComprehensiveQuizService.uploadCourseMaterial(uploadRequest);

      clearInterval(progressInterval);
      setUploadProgress(100);

      onMaterialUploaded(material);

      toast({
        title: 'Upload Successful',
        description: `${formData.content_type} material uploaded for ${subjectInfo.subject_name} and AI processing started.`,
      });

      // Reset form
      setFormData({
        selected_assignment: '',
        content_type: 'syllabus',
        material_identifier: '',
        file: null,
        manual_subject_code: '',
        manual_subject_name: '',
        manual_semester: '',
        manual_section: '',
        use_manual_entry: false
      });

      // Reset file input
      const fileInput = document.getElementById('file-upload') as HTMLInputElement;
      if (fileInput) fileInput.value = '';

    } catch (error) {
      console.error('Upload failed:', error);
      toast({
        title: 'Upload Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred.',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  if (departmentLoading || loadingAssignments) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        <span className="ml-2">Loading your teaching assignments...</span>
      </div>
    );
  }

  if (!department) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-muted-foreground mb-2">Department information required</p>
          <p className="text-sm text-muted-foreground">
            Please contact your administrator to set up your department information.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Upload Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Course Materials
          </CardTitle>
          <CardDescription>
            Upload syllabus, textbooks, and reference materials for AI-powered quiz generation
            {departmentName && ` • ${departmentName}`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Subject & Class Selection */}
          <div className="space-y-2">
            <Label>Subject & Class *</Label>
            <p className="text-xs text-muted-foreground">
              Select which subject and class you want to upload materials for
            </p>
            {facultyAssignments.length > 0 ? (
              <div className="space-y-2">
                <Select
                  value={formData.selected_assignment}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, selected_assignment: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a subject and class from your assignments" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {facultyAssignments.map((assignment) => {
                      const key = `${assignment.subject_code}-${assignment.semester}-${assignment.section}`;
                      return (
                        <SelectItem key={key} value={key} className="py-2">
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {assignment.subject_code} - {assignment.subject_name}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Semester {assignment.semester} • Section {assignment.section}
                              {assignment.subject_type && ` • ${assignment.subject_type}`}
                            </span>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded border">
                  💡 <strong>Found {facultyAssignments.length} teaching assignments</strong> from your timetable
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-yellow-600">⚠️</span>
                    <span className="font-medium text-yellow-800">No Teaching Assignments Found</span>
                  </div>
                  <p className="text-sm text-yellow-700 mb-2">
                    No teaching assignments found in the timetable system.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setFormData(prev => ({ ...prev, use_manual_entry: true }))}
                    className="text-yellow-700 border-yellow-300 hover:bg-yellow-100"
                  >
                    Enter Subject Details Manually
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Manual Entry Form */}
          {formData.use_manual_entry && (
            <div className="space-y-4 p-4 border rounded-lg bg-blue-50 border-blue-200">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-blue-800">Manual Subject Entry</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setFormData(prev => ({
                    ...prev,
                    use_manual_entry: false,
                    manual_subject_code: '',
                    manual_subject_name: '',
                    manual_semester: '',
                    manual_section: ''
                  }))}
                  className="text-blue-600 hover:text-blue-800"
                >
                  Cancel
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manual-subject-code">Subject Code *</Label>
                  <Input
                    id="manual-subject-code"
                    placeholder="e.g., BCS401"
                    value={formData.manual_subject_code}
                    onChange={(e) => setFormData(prev => ({ ...prev, manual_subject_code: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manual-subject-name">Subject Name *</Label>
                  <Input
                    id="manual-subject-name"
                    placeholder="e.g., Data Structures and Algorithms"
                    value={formData.manual_subject_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, manual_subject_name: e.target.value }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manual-semester">Semester *</Label>
                  <Input
                    id="manual-semester"
                    placeholder="e.g., 4"
                    value={formData.manual_semester}
                    onChange={(e) => setFormData(prev => ({ ...prev, manual_semester: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manual-section">Section *</Label>
                  <Input
                    id="manual-section"
                    placeholder="e.g., A"
                    value={formData.manual_section}
                    onChange={(e) => setFormData(prev => ({ ...prev, manual_section: e.target.value }))}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Content Type and Identifier */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Content Type *</Label>
              <Select 
                value={formData.content_type} 
                onValueChange={(value: any) => setFormData(prev => ({ ...prev, content_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="syllabus">📋 Syllabus</SelectItem>
                  <SelectItem value="textbook">📚 Textbook</SelectItem>
                  <SelectItem value="reference">📑 Reference Material</SelectItem>
                  <SelectItem value="notes">📝 Lecture Notes</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="material-identifier">Material Identifier (Optional)</Label>
              <Input
                id="material-identifier"
                placeholder="e.g., Textbook 1, Reference A"
                value={formData.material_identifier}
                onChange={(e) => setFormData(prev => ({ ...prev, material_identifier: e.target.value }))}
              />
            </div>
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="file-upload">Upload File *</Label>
            <Input
              id="file-upload"
              type="file"
              accept=".pdf,.txt,.doc,.docx"
              onChange={handleFileSelect}
              disabled={uploading}
            />
            <p className="text-xs text-muted-foreground">
              Supported formats: PDF, DOC, DOCX, TXT (Max 10MB)
            </p>
            <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded border">
              💡 <strong>PDF Processing:</strong> For demonstration, PDF files will use sample content for AI module detection.
              In production, this would integrate with a proper PDF parsing service.
            </div>
          </div>

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Uploading...</span>
                <span className="text-sm text-muted-foreground">{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {/* Upload Button */}
          <Button
            onClick={handleUpload}
            disabled={
              uploading ||
              !formData.file ||
              (!formData.use_manual_entry && !formData.selected_assignment) ||
              (formData.use_manual_entry && (!formData.manual_subject_code || !formData.manual_subject_name || !formData.manual_semester || !formData.manual_section))
            }
            className="w-full"
          >
            {uploading ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Uploading & Processing...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload Material
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default CourseMaterialUpload;
