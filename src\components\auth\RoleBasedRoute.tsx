import { ReactNode, useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface RoleBasedRouteProps {
  children: ReactNode;
  allowedRoles?: string[]; // Roles from employee_details.roles (college_admin, timetable_coordinator, faculty, class_teacher)
  requiresEmployeeRole?: string[]; // Deprecated: use allowedRoles instead
  redirectTo?: string;
}

const RoleBasedRoute = ({
  children,
  allowedRoles,
  requiresEmployeeRole,
  redirectTo = "/dashboard"
}: RoleBasedRouteProps) => {
  const { user, loading } = useAuth();
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [roleLoading, setRoleLoading] = useState(true);
  const location = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    async function fetchUserRoles() {
      if (user?.id) {
        try {
          // Fetch roles from employee_details table only
          const { data: employeeData, error: employeeError } = await supabase
            .from('employee_details')
            .select('roles')
            .eq('id', user.id)
            .single();

          if (employeeData && !employeeError && employeeData.roles) {
            setUserRoles(employeeData.roles);
          } else {
            console.error('Error fetching user roles:', employeeError);
            setUserRoles([]);
          }
        } catch (error) {
          console.error('Error fetching user roles:', error);
          setUserRoles([]);
        }
      }
      setRoleLoading(false);
    }

    if (!loading) {
      fetchUserRoles();
    }
  }, [user, loading]);

  // Show loading spinner while checking authentication and role
  if (loading || roleLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If user is not authenticated, redirect to auth page
  if (!user) {
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Check access permissions using employee_details.roles
  const hasAccess = !allowedRoles || allowedRoles.some(role => userRoles.includes(role));

  // Support for legacy requiresEmployeeRole parameter (for backward compatibility)
  const hasLegacyAccess = !requiresEmployeeRole || requiresEmployeeRole.some(role => userRoles.includes(role));

  // If user doesn't have required permissions, show access denied
  if (!hasAccess || !hasLegacyAccess) {
    // Show toast notification for access denied
    toast({
      title: "Access Denied",
      description: "You don't have permission to access this page.",
      variant: "destructive",
    });

    return <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};

export default RoleBasedRoute;
