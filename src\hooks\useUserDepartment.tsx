import { useState, useEffect, useCallback, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";

export interface UserDepartmentInfo {
  department: string | null;
  departmentName: string | null;
  fullName: string | null;
  loading: boolean;
  error: string | null;
}

export const useUserDepartment = (): UserDepartmentInfo => {
  const { user } = useAuth();
  const [department, setDepartment] = useState<string | null>(null);
  const [departmentName, setDepartmentName] = useState<string | null>(null);
  const [fullName, setFullName] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // CRITICAL FIX: Use ref to track if data has been fetched to prevent unnecessary re-fetches
  const hasFetchedRef = useRef(false);
  const currentUserIdRef = useRef<string | null>(null);

  // Department mapping for display names
  const getDepartmentName = (deptCode: string | null): string | null => {
    if (!deptCode) return null;

    const departmentMap: Record<string, string> = {
      'cse': 'Computer Science & Engineering',
      'ece': 'Electronics & Communication Engineering',
      'mech': 'Mechanical Engineering',
      'eee': 'Electrical & Electronics Engineering',
      'civil': 'Civil Engineering',
      'it': 'Information Technology',
      'System': 'System Administration' // Handle existing system entries
    };

    return departmentMap[deptCode] || deptCode;
  };

  // CRITICAL FIX: Memoize the fetch function to prevent unnecessary re-renders
  const fetchUserDepartment = useCallback(async () => {
    if (!user?.id) {
      setLoading(false);
      setError("User not authenticated");
      return;
    }

    // CRITICAL FIX: Skip fetch if already fetched for the same user
    if (hasFetchedRef.current && currentUserIdRef.current === user.id) {
      setLoading(false);
      return;
    }

      try {
        setLoading(true);
        setError(null);

        const { data, error: fetchError } = await supabase
          .from('employee_details')
          .select('department, full_name')
          .eq('id', user.id)
          .single();

        if (fetchError) {
          console.error('Error fetching user department:', fetchError);
          setError('Failed to fetch user department information');
          setDepartment(null);
          setDepartmentName(null);
          setFullName(null);
          return;
        }

        if (!data?.department) {
          setError('User department not found. Please contact administrator to set your department.');
          setDepartment(null);
          setDepartmentName(null);
          setFullName(data?.full_name || null);
          return;
        }

        console.log('🔍 User department data from employee_details table:', {
          rawDepartment: data.department,
          mappedDepartmentName: getDepartmentName(data.department),
          fullName: data.full_name
        });

        setDepartment(data.department);
        setDepartmentName(getDepartmentName(data.department));
        setFullName(data.full_name || null);
        setError(null);

        // CRITICAL FIX: Mark as fetched for this user
        hasFetchedRef.current = true;
        currentUserIdRef.current = user.id;

      } catch (err) {
        console.error('Error in fetchUserDepartment:', err);
        setError('An unexpected error occurred while fetching department information');
        setDepartment(null);
        setDepartmentName(null);
        setFullName(null);
      } finally {
        setLoading(false);
      }
  }, [user?.id]);

  // CRITICAL FIX: Use useEffect with optimized dependency
  useEffect(() => {
    // CRITICAL FIX: Reset fetch status if user changes
    if (currentUserIdRef.current !== user?.id) {
      hasFetchedRef.current = false;
      currentUserIdRef.current = null;
    }

    fetchUserDepartment();
  }, [fetchUserDepartment]);

  return {
    department,
    departmentName,
    fullName,
    loading,
    error
  };
};
