import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  BookOpen,
  Users,
  Clock,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  XCircle,
  FlaskConical,
  GraduationCap,
  Lightbulb,
  BarChart3
} from 'lucide-react';
import { StudentSubject, StudentDashboardService } from '@/services/StudentDashboardService';
import { cn } from '@/lib/utils';

interface StudentSubjectCardProps {
  subject: StudentSubject;
  className?: string;
}

// Mini attendance chart component for compact visualization
const MiniAttendanceChart: React.FC<{
  percentage: number;
  totalClasses: number;
  attendedClasses: number;
  status: 'good' | 'warning' | 'critical' | 'no-classes';
}> = ({ percentage, totalClasses, attendedClasses, status }) => {
  // Generate mock recent attendance data for visualization
  const generateMockData = () => {
    if (totalClasses === 0) return [];

    const data = [];
    const attendanceRate = percentage / 100;

    // Generate up to 12 recent classes for visualization
    const recentClasses = Math.min(totalClasses, 12);

    // Create a more realistic pattern based on attendance percentage
    let consecutiveAbsences = 0;
    for (let i = 0; i < recentClasses; i++) {
      // Add some pattern to make it more realistic
      let baseRate = attendanceRate;

      // If student has been absent for 2+ consecutive classes, increase chance of attendance
      if (consecutiveAbsences >= 2) {
        baseRate = Math.min(0.9, baseRate + 0.3);
      }

      // Add some randomness but keep it realistic
      const randomFactor = (Math.random() - 0.5) * 0.2;
      const adjustedRate = Math.max(0, Math.min(1, baseRate + randomFactor));

      const isPresent = Math.random() < adjustedRate;

      if (isPresent) {
        consecutiveAbsences = 0;
      } else {
        consecutiveAbsences++;
      }

      data.push({
        day: i + 1,
        present: isPresent,
        status: isPresent ? 'present' : 'absent',
        height: isPresent ? Math.max(30, 20 + (i * 5)) : 15
      });
    }
    return data;
  };

  const mockData = generateMockData();

  const getStatusColor = () => {
    switch (status) {
      case 'good': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      case 'no-classes': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  if (totalClasses === 0) {
    return (
      <div className="flex items-center justify-center h-6 w-16 bg-gray-100 rounded text-xs text-gray-500">
        <Clock className="h-3 w-3" />
      </div>
    );
  }

  return (
    <div className="flex items-end gap-0.5 h-6 w-20">
      {mockData.map((day, index) => (
        <div
          key={index}
          className={cn(
            "flex-1 rounded-sm transition-all duration-300 hover:opacity-80 hover:scale-110",
            day.present ? getStatusColor() : "bg-gray-300",
            "min-h-[3px] max-w-[2px] animate-in slide-in-from-bottom-2",
            "cursor-pointer"
          )}
          style={{
            height: `${day.height}%`,
            animationDelay: `${index * 50}ms`
          }}
          title={`Class ${day.day}: ${day.status.charAt(0).toUpperCase() + day.status.slice(1)}`}
        />
      ))}
    </div>
  );
};

const StudentSubjectCard: React.FC<StudentSubjectCardProps> = ({ subject, className }) => {
  const statusDisplay = StudentDashboardService.getAttendanceStatusDisplay(subject.attendance_status);

  // Map icon names to actual icon components
  const getStatusIcon = (iconName: string) => {
    switch (iconName) {
      case 'CheckCircle':
        return CheckCircle;
      case 'AlertTriangle':
        return AlertTriangle;
      case 'XCircle':
        return XCircle;
      case 'Clock':
        return Clock;
      default:
        return CheckCircle;
    }
  };

  const StatusIcon = getStatusIcon(statusDisplay.iconName);

  const getSubjectTypeConfig = (type: string) => {
    switch (type.toLowerCase()) {
      case 'theory':
        return {
          color: 'bg-blue-100 text-blue-700 border-blue-300',
          icon: BookOpen,
          label: 'Theory'
        };
      case 'laboratory':
      case 'lab':
        return {
          color: 'bg-purple-100 text-purple-700 border-purple-300',
          icon: FlaskConical,
          label: 'Lab'
        };
      case 'elective':
        return {
          color: 'bg-green-100 text-green-700 border-green-300',
          icon: Lightbulb,
          label: 'Elective'
        };
      case 'tutorial':
        return {
          color: 'bg-orange-100 text-orange-700 border-orange-300',
          icon: GraduationCap,
          label: 'Tutorial'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-700 border-gray-300',
          icon: BookOpen,
          label: type.charAt(0).toUpperCase() + type.slice(1)
        };
    }
  };

  const subjectTypeConfig = getSubjectTypeConfig(subject.subject_type);
  const SubjectTypeIcon = subjectTypeConfig.icon;

  return (
    <Card className={cn(
      "hover:shadow-lg hover:-translate-y-1 transition-all duration-300 border-l-4 h-fit",
      "border border-gray-200 hover:border-gray-300",
      statusDisplay.borderColor,
      className
    )}>
      <CardContent className="p-5">
        {/* Header Row */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-gray-900 text-sm truncate">
                {subject.subject_code}
              </h3>
              <Badge
                variant="outline"
                className={cn("text-xs px-1.5 py-0.5 flex items-center gap-1", subjectTypeConfig.color)}
              >
                <SubjectTypeIcon className="h-3 w-3" />
                <span className="hidden sm:inline">{subjectTypeConfig.label}</span>
              </Badge>
            </div>
            <p className="text-xs text-gray-600 font-medium truncate mb-1">
              {subject.subject_name}
            </p>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>Sem {subject.semester}</span>
              <span>•</span>
              <span>Sec {subject.section}</span>
              <span>•</span>
              <span>{subject.hours_per_week}h/wk</span>
            </div>
          </div>

          <div className={cn(
            "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ml-2 shrink-0",
            statusDisplay.bgColor,
            statusDisplay.color
          )}>
            <StatusIcon className="h-3 w-3" />
            <span className="hidden sm:inline">{statusDisplay.label}</span>
          </div>
        </div>

        {/* Compact Attendance Section */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-3.5 w-3.5 text-gray-500" />
              <span className="text-xs font-medium text-gray-700">Attendance</span>
            </div>
            <div className="flex items-center gap-2">
              <MiniAttendanceChart
                percentage={subject.attendance_percentage}
                totalClasses={subject.total_classes}
                attendedClasses={subject.attended_classes}
                status={subject.attendance_status}
              />
              <span className={cn(
                "text-xs font-bold min-w-[2.5rem] text-right",
                subject.total_classes === 0 ? "text-gray-600" :
                subject.attendance_percentage >= 75 ? "text-green-600" :
                subject.attendance_percentage >= 65 ? "text-yellow-600" : "text-red-600"
              )}>
                {subject.total_classes === 0 ? "N/A" : `${subject.attendance_percentage}%`}
              </span>
            </div>
          </div>

          <Progress
            value={subject.total_classes === 0 ? 0 : subject.attendance_percentage}
            className="h-1.5"
            indicatorClassName={cn(
              subject.total_classes === 0 ? "bg-gray-400" :
              subject.attendance_percentage >= 75 ? "bg-green-500" :
              subject.attendance_percentage >= 65 ? "bg-yellow-500" : "bg-red-500"
            )}
          />

          <div className="flex items-center justify-between text-xs text-gray-500">
            {subject.total_classes === 0 ? (
              <span className="text-center w-full">Classes not started yet</span>
            ) : (
              <>
                <span>{subject.attended_classes}/{subject.total_classes} classes</span>
                <span className="text-xs">Recent trend</span>
              </>
            )}
          </div>
        </div>

        {/* Compact Faculty Section */}
        <div className="pt-3 border-t border-gray-100">
          <div className="flex items-center gap-2">
            <Users className="h-3.5 w-3.5 text-gray-500" />
            <div className="text-xs text-gray-600 truncate">
              <span className="font-medium">{subject.faculty_1_name}</span>
              {subject.faculty_2_name && (
                <>
                  <span className="text-gray-400 mx-1">•</span>
                  <span className="font-medium">{subject.faculty_2_name}</span>
                </>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StudentSubjectCard;
