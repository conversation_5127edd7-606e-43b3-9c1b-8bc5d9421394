
import { toast as sonnerToast, type ToastT } from "sonner";
import { ToastActionElement, ToastProps } from "@/components/ui/toast";

// Re-export ToastProps for compatibility
export { type ToastProps } from "@/components/ui/toast";

// Provider component doesn't change in this context
export { Toaster } from "@/components/ui/sonner";

// Custom hook for toast
export function useToast() {
  return {
    toast,
    dismiss: sonnerToast.dismiss,
    error: (message: string) => toast({ title: message, variant: "destructive" }),
  };
}

// Create a wrapped version of toast that handles our custom props
const toast = (props: ToastProps) => {
  const { title, ...restProps } = props;
  
  // Call sonner toast with the correct parameters
  // sonner expects (message, options)
  return sonnerToast(title as string, restProps);
};

export { toast };
