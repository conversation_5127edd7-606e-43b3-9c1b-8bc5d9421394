import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Bell, 
  BellRing, 
  Check, 
  CheckCheck, 
  X, 
  Clock, 
  AlertTriangle,
  Calendar,
  User
} from 'lucide-react';
import { useLeaveNotifications } from '@/hooks/useLeaveNotifications';
import { LeaveNotification } from '@/services/LeaveNotificationService';
import { cn } from '@/lib/utils';

interface LeaveNotificationBadgeProps {
  className?: string;
}

export default function LeaveNotificationBadge({ className }: LeaveNotificationBadgeProps) {
  const {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    dismissNotification,
    getNotificationMessage,
    isUrgent
  } = useLeaveNotifications();

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const getLeaveTypeColor = (leaveType: string): string => {
    switch (leaveType) {
      case 'casual_leave': return 'text-blue-600';
      case 'sick_leave': return 'text-red-600';
      case 'earned_leave': return 'text-green-600';
      case 'emergency_leave': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const handleNotificationClick = async (notification: LeaveNotification) => {
    if (!notification.is_read) {
      await markAsRead(notification.id);
    }
  };

  const handleDismiss = async (e: React.MouseEvent, notificationId: string) => {
    e.stopPropagation();
    await dismissNotification(notificationId);
  };

  if (loading) {
    return (
      <Button variant="ghost" size="icon" className={cn("relative", className)}>
        <Bell className="h-5 w-5 animate-pulse" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className={cn("relative", className)}>
          {unreadCount > 0 ? (
            <BellRing className="h-5 w-5 text-orange-600" />
          ) : (
            <Bell className="h-5 w-5" />
          )}
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
          <span className="sr-only">Leave notifications</span>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-96 max-h-[500px]">
        <div className="flex items-center justify-between p-4 border-b">
          <DropdownMenuLabel className="text-base font-semibold">
            Leave Notifications
          </DropdownMenuLabel>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="text-xs"
            >
              <CheckCheck className="h-3 w-3 mr-1" />
              Mark all read
            </Button>
          )}
        </div>

        {notifications.length === 0 ? (
          <div className="p-6 text-center text-muted-foreground">
            <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No notifications</p>
            <p className="text-xs">You're all caught up!</p>
          </div>
        ) : (
          <div className="max-h-[400px] overflow-y-auto">
            <div className="p-2">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    "group relative p-3 rounded-lg border mb-2 cursor-pointer transition-colors",
                    notification.is_read 
                      ? "bg-background hover:bg-muted/50" 
                      : "bg-blue-50 hover:bg-blue-100 border-blue-200",
                    isUrgent(notification) && "border-orange-300 bg-orange-50"
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  {/* Dismiss button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => handleDismiss(e, notification.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>

                  {/* Notification content */}
                  <div className="space-y-2">
                    {/* Header with faculty name and urgency indicator */}
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium text-sm">
                        {notification.faculty_name}
                      </span>
                      {isUrgent(notification) && (
                        <Badge variant="destructive" className="text-xs">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Urgent
                        </Badge>
                      )}
                      {!notification.is_read && (
                        <div className="w-2 h-2 bg-blue-600 rounded-full ml-auto" />
                      )}
                    </div>

                    {/* Leave details */}
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className={getLeaveTypeColor(notification.leave_type)}>
                          {notification.leave_type.replace('_', ' ').toUpperCase()}
                        </span>
                        <span className="text-muted-foreground">•</span>
                        <span className="text-muted-foreground">
                          {notification.total_days} day{notification.total_days !== 1 ? 's' : ''}
                        </span>
                      </div>
                      
                      <div className="text-xs text-muted-foreground">
                        {new Date(notification.start_date).toLocaleDateString()} - {' '}
                        {new Date(notification.end_date).toLocaleDateString()}
                      </div>
                    </div>

                    {/* Timestamp */}
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {formatTimeAgo(notification.created_at)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {notifications.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <div className="p-2">
              <Button 
                variant="ghost" 
                className="w-full text-sm"
                onClick={() => window.location.href = '/leave-approvals'}
              >
                View All Leave Requests
              </Button>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
