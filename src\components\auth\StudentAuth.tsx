import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { StudentAuthService, StudentAuthData } from '@/services/StudentAuthService';
import { GraduationCap, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useNavigate, useSearchParams } from 'react-router-dom';

// Validation schemas
const loginSchema = z.object({
  roll_number: z.string().min(1, 'Roll number is required'),
  password: z.string().min(1, 'Password is required'),
});

const changePasswordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z.string().min(6, 'Password must be at least 6 characters'),
  confirm_password: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
});

const forgotPasswordSchema = z.object({
  roll_number: z.string().min(1, 'Roll number is required'),
  student_email: z.string().email('Valid email is required'),
});

const resetPasswordSchema = z.object({
  new_password: z.string().min(6, 'Password must be at least 6 characters'),
  confirm_password: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;
type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;
type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

interface StudentAuthProps {
  onLogin?: (studentData: StudentAuthData) => void;
}

const StudentAuth: React.FC<StudentAuthProps> = ({ onLogin }) => {
  const [activeTab, setActiveTab] = useState('login');
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [studentData, setStudentData] = useState<StudentAuthData | null>(null);
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Check for reset token in URL
  useEffect(() => {
    const token = searchParams.get('token');
    const tab = searchParams.get('tab');
    
    if (tab === 'reset-password' && token) {
      setActiveTab('reset-password');
    }
  }, [searchParams]);

  // Form hooks
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: { roll_number: '', password: '' },
  });

  const changePasswordForm = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: { current_password: '', new_password: '', confirm_password: '' },
  });

  const forgotPasswordForm = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: { roll_number: '', student_email: '' },
  });

  const resetPasswordForm = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: { new_password: '', confirm_password: '' },
  });

  // Handle login
  const onLoginSubmit = async (values: LoginFormValues) => {
    setIsSubmitting(true);
    try {
      const authData = await StudentAuthService.authenticateStudent(values);
      
      if (!authData) {
        toast({
          title: 'Login failed',
          description: 'Invalid roll number or password',
          variant: 'destructive',
        });
        return;
      }

      setStudentData(authData);
      
      if (authData.first_login) {
        setActiveTab('change-password');
        toast({
          title: 'First login detected',
          description: 'Please change your password for security',
        });
      } else {
        toast({
          title: 'Login successful',
          description: `Welcome back, ${authData.student_name}!`,
        });
        onLogin?.(authData);
      }
    } catch (error) {
      toast({
        title: 'Login failed',
        description: error instanceof Error ? error.message : 'Authentication failed',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle password change
  const onChangePasswordSubmit = async (values: ChangePasswordFormValues) => {
    if (!studentData) return;

    setIsSubmitting(true);
    try {
      await StudentAuthService.changePassword({
        student_id: studentData.student_id,
        current_password: values.current_password,
        new_password: values.new_password,
      });

      toast({
        title: 'Password changed',
        description: 'Your password has been updated successfully',
      });

      // Update student data to reflect password change
      setStudentData(prev => prev ? { ...prev, first_login: false } : null);
      onLogin?.(studentData);
    } catch (error) {
      toast({
        title: 'Password change failed',
        description: error instanceof Error ? error.message : 'Failed to change password',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle forgot password
  const onForgotPasswordSubmit = async (values: ForgotPasswordFormValues) => {
    setIsSubmitting(true);
    try {
      await StudentAuthService.requestPasswordReset(values);
      
      toast({
        title: 'Reset email sent',
        description: 'Check your email for password reset instructions',
      });
    } catch (error) {
      toast({
        title: 'Reset failed',
        description: error instanceof Error ? error.message : 'Failed to send reset email',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle password reset
  const onResetPasswordSubmit = async (values: ResetPasswordFormValues) => {
    const token = searchParams.get('token');
    if (!token) {
      toast({
        title: 'Invalid reset link',
        description: 'Reset token is missing',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const success = await StudentAuthService.resetPassword(token, values.new_password);
      
      if (success) {
        toast({
          title: 'Password reset successful',
          description: 'You can now login with your new password',
        });
        setActiveTab('login');
        navigate('/student-auth', { replace: true });
      } else {
        toast({
          title: 'Reset failed',
          description: 'Invalid or expired reset token',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Reset failed',
        description: error instanceof Error ? error.message : 'Failed to reset password',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center flex items-center justify-center gap-2">
            <GraduationCap className="h-6 w-6 text-primary" />
            Student Portal
          </CardTitle>
          <CardDescription className="text-center">
            Access your academic information and resources
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {/* Student Info Display */}
          {studentData && activeTab === 'change-password' && (
            <Alert className="mb-4 border-blue-200 bg-blue-50">
              <CheckCircle className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <strong>Welcome, {studentData.student_name}!</strong><br />
                {studentData.department.toUpperCase()} - Semester {studentData.semester} Section {studentData.section}
              </AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="forgot-password">Forgot Password</TabsTrigger>
            </TabsList>

            {/* Login Tab */}
            <TabsContent value="login">
              <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="roll_number">Roll Number</Label>
                  <Input
                    id="roll_number"
                    placeholder="e.g., 1KS25CS001"
                    {...loginForm.register('roll_number')}
                  />
                  {loginForm.formState.errors.roll_number && (
                    <p className="text-sm text-red-600">
                      {loginForm.formState.errors.roll_number.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Enter your password"
                      {...loginForm.register('password')}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {loginForm.formState.errors.password && (
                    <p className="text-sm text-red-600">
                      {loginForm.formState.errors.password.message}
                    </p>
                  )}
                  <Badge variant="outline" className="text-xs">
                    Default password is your roll number
                  </Badge>
                </div>

                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? 'Signing in...' : 'Sign In'}
                </Button>
              </form>
            </TabsContent>

            {/* Change Password Tab */}
            <TabsContent value="change-password">
              <form onSubmit={changePasswordForm.handleSubmit(onChangePasswordSubmit)} className="space-y-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    For security, please change your password from the default.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <Label htmlFor="current_password">Current Password</Label>
                  <Input
                    id="current_password"
                    type="password"
                    placeholder="Enter current password"
                    {...changePasswordForm.register('current_password')}
                  />
                  {changePasswordForm.formState.errors.current_password && (
                    <p className="text-sm text-red-600">
                      {changePasswordForm.formState.errors.current_password.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new_password">New Password</Label>
                  <Input
                    id="new_password"
                    type="password"
                    placeholder="Enter new password"
                    {...changePasswordForm.register('new_password')}
                  />
                  {changePasswordForm.formState.errors.new_password && (
                    <p className="text-sm text-red-600">
                      {changePasswordForm.formState.errors.new_password.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirm_password">Confirm Password</Label>
                  <Input
                    id="confirm_password"
                    type="password"
                    placeholder="Confirm new password"
                    {...changePasswordForm.register('confirm_password')}
                  />
                  {changePasswordForm.formState.errors.confirm_password && (
                    <p className="text-sm text-red-600">
                      {changePasswordForm.formState.errors.confirm_password.message}
                    </p>
                  )}
                </div>

                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? 'Changing Password...' : 'Change Password'}
                </Button>
              </form>
            </TabsContent>

            {/* Forgot Password Tab */}
            <TabsContent value="forgot-password">
              <form onSubmit={forgotPasswordForm.handleSubmit(onForgotPasswordSubmit)} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="forgot_roll_number">Roll Number</Label>
                  <Input
                    id="forgot_roll_number"
                    placeholder="e.g., 1KS25CS001"
                    {...forgotPasswordForm.register('roll_number')}
                  />
                  {forgotPasswordForm.formState.errors.roll_number && (
                    <p className="text-sm text-red-600">
                      {forgotPasswordForm.formState.errors.roll_number.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="student_email">Student Email</Label>
                  <Input
                    id="student_email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...forgotPasswordForm.register('student_email')}
                  />
                  {forgotPasswordForm.formState.errors.student_email && (
                    <p className="text-sm text-red-600">
                      {forgotPasswordForm.formState.errors.student_email.message}
                    </p>
                  )}
                </div>

                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? 'Sending Reset Email...' : 'Send Reset Email'}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => setActiveTab('login')}
                >
                  Back to Login
                </Button>
              </form>
            </TabsContent>

            {/* Reset Password Tab */}
            <TabsContent value="reset-password">
              <form onSubmit={resetPasswordForm.handleSubmit(onResetPasswordSubmit)} className="space-y-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Enter your new password below.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <Label htmlFor="reset_new_password">New Password</Label>
                  <Input
                    id="reset_new_password"
                    type="password"
                    placeholder="Enter new password"
                    {...resetPasswordForm.register('new_password')}
                  />
                  {resetPasswordForm.formState.errors.new_password && (
                    <p className="text-sm text-red-600">
                      {resetPasswordForm.formState.errors.new_password.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reset_confirm_password">Confirm Password</Label>
                  <Input
                    id="reset_confirm_password"
                    type="password"
                    placeholder="Confirm new password"
                    {...resetPasswordForm.register('confirm_password')}
                  />
                  {resetPasswordForm.formState.errors.confirm_password && (
                    <p className="text-sm text-red-600">
                      {resetPasswordForm.formState.errors.confirm_password.message}
                    </p>
                  )}
                </div>

                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? 'Resetting Password...' : 'Reset Password'}
                </Button>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentAuth;
