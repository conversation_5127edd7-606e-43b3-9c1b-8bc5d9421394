<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EduFlow - Comprehensive College ERP System</title>
    <meta name="description" content="Transform your educational institution with EduFlow - a comprehensive ERP solution for modern colleges. Manage timetables, attendance, assessments, faculty, students, and administrative processes seamlessly." />
    <meta name="author" content="EduFlow" />

    <meta property="og:title" content="EduFlow - Comprehensive College ERP System" />
    <meta property="og:description" content="Transform your educational institution with EduFlow - a comprehensive ERP solution for modern colleges. Manage timetables, attendance, assessments, faculty, students, and administrative processes seamlessly." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- Performance and Resource Hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="https://cdn.gpteng.co" />

    <!-- Preload critical resources -->
    <link rel="modulepreload" href="/src/main.tsx" />
    <link rel="modulepreload" href="/src/App.tsx" />

    <!-- Critical CSS for faster rendering -->
    <style>
      /* Critical loading styles */
      #root {
        min-height: 100vh;
        background: hsl(var(--background, 0 0% 100%));
      }

      /* Loading animation for better UX */
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
