import React from 'react';
import { Navigate } from 'react-router-dom';
import StudentLogin from '@/components/student-auth/StudentLogin';
import { useStudentAuth } from '@/contexts/StudentAuthContext';

const StudentLoginPage: React.FC = () => {
  const { isAuthenticated, loading } = useStudentAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If already authenticated, redirect to dashboard
  if (isAuthenticated) {
    return <Navigate to="/student-dashboard" replace />;
  }

  return <StudentLogin />;
};

export default StudentLoginPage;
