
import { Check, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ProcessedData {
  mappings: number;
  labSlots: number;
  labSubjects: number;
  batchesPerLab: number;
}

interface UploadResultsProps {
  uploadComplete: boolean;
  processedData: ProcessedData;
  skippedEntries: string[];
  onUploadAgain: () => void;
}

export const UploadResults = ({
  uploadComplete,
  processedData,
  skippedEntries,
  onUploadAgain
}: UploadResultsProps) => {
  if (!uploadComplete) return null;
  
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 p-3 bg-accent/10 rounded text-accent">
        <Check size={18} />
        <span className="text-sm">File uploaded successfully</span>
      </div>
      
      {processedData.labSlots > 0 && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded text-blue-600 dark:text-blue-400">
          <p className="text-sm font-medium mb-1">Processed Data Summary:</p>
          <ul className="text-xs space-y-1 ml-4">
            <li>Subject Mappings: {processedData.mappings}</li>
            <li>Laboratory Subjects: {processedData.labSubjects}</li>
            <li>Lab Batch Slots: {processedData.labSlots}</li>
            <li>Average Batches per Lab: {processedData.batchesPerLab}</li>
          </ul>
        </div>
      )}
      
      {skippedEntries.length > 0 && (
        <div className="p-3 bg-amber-50 dark:bg-amber-900/20 rounded text-amber-600 dark:text-amber-400">
          <div className="flex items-center space-x-2 mb-2">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm font-medium">Some entries were skipped</span>
          </div>
          <ul className="text-xs space-y-1 ml-6 list-disc">
            {skippedEntries.slice(0, 5).map((entry, index) => (
              <li key={index}>{entry}</li>
            ))}
            {skippedEntries.length > 5 && (
              <li>...and {skippedEntries.length - 5} more</li>
            )}
          </ul>
        </div>
      )}
      
      <div className="flex space-x-2">
        <Button 
          variant="outline"
          className="w-full" 
          onClick={onUploadAgain}
        >
          Upload Another File
        </Button>
        <Button className="w-full">
          Generate Timetable
        </Button>
      </div>
    </div>
  );
};
