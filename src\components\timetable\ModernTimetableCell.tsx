import React from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Clock, Users, MapPin, BookOpen, Beaker } from 'lucide-react';
import { getDisplaySubjectCode } from '@/utils/subjectUtils';

interface TimetableSlot {
  id?: string;
  subject_code?: string;
  subject_short_id?: string;
  subject_name?: string;
  faculty_name?: string;
  faculty2_name?: string;
  room_number?: string;
  subject_type?: 'theory' | 'lab' | 'skill_lab';
  semester?: string;
  section?: string;
  duration?: number;
  isLabStart?: boolean;
  isLabContinuation?: boolean;
}

interface ModernTimetableCellProps {
  slot?: TimetableSlot;
  isEmpty?: boolean;
  isBreak?: boolean;
  breakType?: 'tea' | 'lunch';
  className?: string;
  onClick?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  isEditable?: boolean;
  colSpan?: number;
  rowSpan?: number;
}

/**
 * Modern Timetable Cell Component
 * Enhanced cell styling for professional timetable display
 */
export const ModernTimetableCell: React.FC<ModernTimetableCellProps> = ({
  slot,
  isEmpty = false,
  isBreak = false,
  breakType,
  className,
  onClick,
  onEdit,
  onDelete,
  isEditable = false,
  colSpan = 1,
  rowSpan = 1
}) => {
  if (isBreak) {
    return (
      <td
        colSpan={colSpan}
        rowSpan={rowSpan}
        className={cn(
          'relative p-0 text-center border-2 overflow-hidden',
          breakType === 'tea'
            ? 'border-info/30 bg-gradient-to-b from-info/10 to-info/5'
            : 'border-warning/30 bg-gradient-to-b from-warning/10 to-warning/5',
          className
        )}
      >
        <div className="flex justify-center items-center h-full w-full relative min-h-[120px]">
          {/* Background pattern */}
          <div className={cn(
            'absolute inset-0 opacity-5',
            breakType === 'tea' ? 'bg-info' : 'bg-warning'
          )}>
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-current to-transparent"></div>
          </div>

          <div className="writing-vertical flex justify-center items-center relative z-10">
            <span className={cn(
              'rotate-270 text-sm font-bold tracking-wider',
              breakType === 'tea' ? 'text-info' : 'text-warning'
            )}>
              {breakType === 'tea' ? 'TEA BREAK' : 'LUNCH BREAK'}
            </span>
          </div>

          {/* Modern decorative elements */}
          <div className={cn(
            'absolute top-3 left-1/2 transform -translate-x-1/2 w-2 h-2 rounded-full animate-pulse',
            breakType === 'tea' ? 'bg-info/60' : 'bg-warning/60'
          )} />
          <div
            className={cn(
              'absolute bottom-3 left-1/2 transform -translate-x-1/2 w-2 h-2 rounded-full animate-pulse',
              breakType === 'tea' ? 'bg-info/60' : 'bg-warning/60'
            )}
            style={{ animationDelay: '0.5s' }}
          />
        </div>
      </td>
    );
  }

  if (isEmpty || !slot) {
    return (
      <td
        colSpan={colSpan}
        rowSpan={rowSpan}
        className={cn(
          'p-3 text-center border border-border/30 bg-muted/20 hover:bg-muted/40 transition-all duration-200 cursor-pointer group min-h-[80px]',
          isEditable && 'hover:border-primary/50',
          className
        )}
        onClick={onClick}
      >
        <div className="flex items-center justify-center h-full min-h-[60px]">
          <div className="text-muted-foreground/50 group-hover:text-muted-foreground transition-colors duration-200">
            {isEditable && (
              <div className="flex flex-col items-center gap-1">
                <div className="w-6 h-6 rounded-full border-2 border-dashed border-current flex items-center justify-center">
                  <span className="text-xs">+</span>
                </div>
                <span className="text-xs">Add</span>
              </div>
            )}
          </div>
        </div>
      </td>
    );
  }

  const getSubjectTypeColor = (type?: string) => {
    switch (type) {
      case 'lab':
        return 'bg-gradient-to-br from-success/20 to-success/10 border-success/30 text-success-foreground';
      case 'skill_lab':
        return 'bg-gradient-to-br from-info/20 to-info/10 border-info/30 text-info-foreground';
      default:
        return 'bg-gradient-to-br from-primary/20 to-primary/10 border-primary/30 text-primary-foreground';
    }
  };

  const getSubjectTypeIcon = (type?: string) => {
    switch (type) {
      case 'lab':
      case 'skill_lab':
        return <Beaker className="w-3 h-3" />;
      default:
        return <BookOpen className="w-3 h-3" />;
    }
  };

  return (
    <td
      colSpan={colSpan}
      rowSpan={rowSpan}
      className={cn(
        'p-2 border border-border/30 transition-all duration-200 cursor-pointer group hover:shadow-md relative overflow-hidden',
        getSubjectTypeColor(slot.subject_type),
        isEditable && 'hover:border-primary/50',
        className
      )}
      onClick={onClick}
    >
      <div className="relative z-10 space-y-2 min-h-[60px] flex flex-col justify-center">
        {/* Subject Header */}
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <Badge
              variant="secondary"
              className="text-xs font-bold bg-background/80 text-foreground/80"
            >
              {getDisplaySubjectCode(slot.subject_code || '', slot.subject_short_id)}
            </Badge>
            <div className="flex items-center gap-1">
              {getSubjectTypeIcon(slot.subject_type)}
              {slot.duration && slot.duration > 1 && (
                <Badge variant="outline" className="text-xs">
                  {slot.duration}h
                </Badge>
              )}
            </div>
          </div>

          {slot.subject_name && (
            <div className="text-xs font-medium text-foreground/90 line-clamp-2">
              {slot.subject_name}
            </div>
          )}
        </div>

        {/* Faculty Information */}
        {(slot.faculty_name || slot.faculty2_name) && (
          <div className="space-y-1">
            {slot.faculty_name && (
              <div className="flex items-center gap-1 text-xs text-foreground/80">
                <Users className="w-3 h-3" />
                <span className="truncate">{slot.faculty_name}</span>
              </div>
            )}
            {slot.faculty2_name && (
              <div className="flex items-center gap-1 text-xs text-foreground/70">
                <Users className="w-3 h-3" />
                <span className="truncate">{slot.faculty2_name}</span>
              </div>
            )}
          </div>
        )}

        {/* Room Information */}
        {slot.room_number && (
          <div className="flex items-center gap-1 text-xs text-foreground/70">
            <MapPin className="w-3 h-3" />
            <span>{slot.room_number}</span>
          </div>
        )}

        {/* Class Information */}
        {(slot.semester || slot.section) && (
          <div className="flex items-center gap-1 text-xs text-foreground/60">
            <Clock className="w-3 h-3" />
            <span>
              {slot.semester && `Sem ${slot.semester}`}
              {slot.semester && slot.section && ' - '}
              {slot.section && `Sec ${slot.section}`}
            </span>
          </div>
        )}
      </div>

      {/* Hover overlay for edit actions */}
      {isEditable && (
        <div className="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
          <div className="flex gap-1">
            {onEdit && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit();
                }}
                className="p-1 bg-primary text-primary-foreground rounded text-xs hover:bg-primary/90"
              >
                Edit
              </button>
            )}
            {onDelete && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                className="p-1 bg-destructive text-destructive-foreground rounded text-xs hover:bg-destructive/90"
              >
                Del
              </button>
            )}
          </div>
        </div>
      )}

      {/* Background decoration */}
      <div className="absolute inset-0 opacity-5 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-current to-transparent"></div>
      </div>
    </td>
  );
};

export default ModernTimetableCell;
