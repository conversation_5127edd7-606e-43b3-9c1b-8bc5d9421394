import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { 
  Search, 
  Users, 
  UserPlus, 
  Filter,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { StudentProctoringService, UnassignedStudent } from '@/services/StudentProctoringService';

const ProctorStudentAssignment: React.FC = () => {
  const [students, setStudents] = useState<UnassignedStudent[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<UnassignedStudent[]>([]);
  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [assigning, setAssigning] = useState(false);
  const [filters, setFilters] = useState({
    department: '',
    semester: '',
    section: ''
  });

  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    loadUnassignedStudents();
  }, []);

  useEffect(() => {
    filterStudents();
  }, [students, searchQuery, filters]);

  const loadUnassignedStudents = async () => {
    try {
      setLoading(true);
      const data = await StudentProctoringService.getUnassignedStudents();
      setStudents(data);
    } catch (error) {
      console.error('Error loading unassigned students:', error);
      toast({
        title: 'Error',
        description: 'Failed to load unassigned students',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const filterStudents = () => {
    let filtered = [...students];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(student => 
        student.student_name.toLowerCase().includes(query) ||
        student.usn.toLowerCase().includes(query)
      );
    }

    // Apply department filter
    if (filters.department) {
      filtered = filtered.filter(student => student.department === filters.department);
    }

    // Apply semester filter
    if (filters.semester) {
      filtered = filtered.filter(student => student.semester === filters.semester);
    }

    // Apply section filter
    if (filters.section) {
      filtered = filtered.filter(student => student.section === filters.section);
    }

    setFilteredStudents(filtered);
  };

  const handleStudentSelection = (studentUsn: string, checked: boolean) => {
    const newSelection = new Set(selectedStudents);
    if (checked) {
      newSelection.add(studentUsn);
    } else {
      newSelection.delete(studentUsn);
    }
    setSelectedStudents(newSelection);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allUsns = new Set(filteredStudents.map(student => student.usn));
      setSelectedStudents(allUsns);
    } else {
      setSelectedStudents(new Set());
    }
  };

  const handleAssignStudents = async () => {
    if (selectedStudents.size === 0) {
      toast({
        title: 'No Students Selected',
        description: 'Please select at least one student to assign',
        variant: 'destructive',
      });
      return;
    }

    if (!user?.id) {
      toast({
        title: 'Authentication Error',
        description: 'User not authenticated',
        variant: 'destructive',
      });
      return;
    }

    try {
      setAssigning(true);

      const assignments = Array.from(selectedStudents).map(studentUsn => {
        const student = students.find(s => s.usn === studentUsn)!;
        return {
          faculty_id: user.id,
          student_usn: student.usn,
          student_name: student.student_name,
          department: student.department,
          semester: student.semester,
          section: student.section,
          academic_year: student.academic_year
        };
      });

      await StudentProctoringService.assignStudentsToFaculty(assignments, user.id);

      toast({
        title: 'Students Assigned Successfully',
        description: `${selectedStudents.size} students have been assigned to you as proctor`,
      });

      // Reset selection and reload data
      setSelectedStudents(new Set());
      await loadUnassignedStudents();

    } catch (error) {
      console.error('Error assigning students:', error);
      toast({
        title: 'Assignment Failed',
        description: 'Failed to assign students. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setAssigning(false);
    }
  };

  // Get unique values for filters
  const uniqueDepartments = [...new Set(students.map(s => s.department))];
  const uniqueSemesters = [...new Set(students.map(s => s.semester))];
  const uniqueSections = [...new Set(students.map(s => s.section))];

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading unassigned students...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="w-5 h-5" />
            Assign Students as Proctor
          </CardTitle>
          <CardDescription>
            Select students to assign to yourself as their proctor. Only unassigned students are shown.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by student name or USN..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={filters.department}
                onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="">All Departments</option>
                {uniqueDepartments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
              <select
                value={filters.semester}
                onChange={(e) => setFilters(prev => ({ ...prev, semester: e.target.value }))}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="">All Semesters</option>
                {uniqueSemesters.map(sem => (
                  <option key={sem} value={sem}>{sem}</option>
                ))}
              </select>
              <select
                value={filters.section}
                onChange={(e) => setFilters(prev => ({ ...prev, section: e.target.value }))}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="">All Sections</option>
                {uniqueSections.map(sec => (
                  <option key={sec} value={sec}>{sec}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Selection Summary */}
          {filteredStudents.length > 0 && (
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-4">
                <Checkbox
                  checked={selectedStudents.size === filteredStudents.length && filteredStudents.length > 0}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm font-medium">
                  Select All ({filteredStudents.length} students)
                </span>
                {selectedStudents.size > 0 && (
                  <Badge variant="secondary">
                    {selectedStudents.size} selected
                  </Badge>
                )}
              </div>
              <Button
                onClick={handleAssignStudents}
                disabled={selectedStudents.size === 0 || assigning}
                className="flex items-center gap-2"
              >
                {assigning ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <UserPlus className="w-4 h-4" />
                )}
                Assign Selected Students
              </Button>
            </div>
          )}

          {/* Students List */}
          {filteredStudents.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {students.length === 0 
                  ? "No unassigned students found. All students may already be assigned to proctors."
                  : "No students match your search criteria."
                }
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredStudents.map((student) => (
                <div
                  key={student.usn}
                  className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50"
                >
                  <Checkbox
                    checked={selectedStudents.has(student.usn)}
                    onCheckedChange={(checked) => handleStudentSelection(student.usn, checked as boolean)}
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{student.student_name}</span>
                      <Badge variant="outline">{student.usn}</Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      {student.department} • Semester {student.semester} • Section {student.section}
                    </div>
                    {student.email && (
                      <div className="text-xs text-gray-500">{student.email}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ProctorStudentAssignment;
