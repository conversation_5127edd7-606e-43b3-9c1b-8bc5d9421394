import { supabase } from '@/integrations/supabase/client';

export interface ElectiveSubject {
  subject_id: string;
  subject_code: string;
  subject_name: string;
  subject_short_id?: string;
  faculty_id: string;
  faculty_name: string;
  hours_per_week: number;
}

export interface ElectivePlacementParams {
  academicYear: string;
  department: string;
  semester: string;
  section: string;
  groupingType: 'same_group' | 'different_groups';
  synchronizationMode: 'synchronized' | 'independent';
}

export interface TimeSlot {
  day: string;
  time_slot: string;
  period_index: number;
}

export interface CrossSectionConflict {
  section: string;
  conflictType: 'lab' | 'skill_lab' | 'tutorial' | 'theory';
  subject_code: string;
}

export class ElectivePlacementService {
  /**
   * Get all elective subjects for a specific semester-section
   */
  static async getElectiveSubjects(params: ElectivePlacementParams): Promise<ElectiveSubject[]> {
    console.log('🔍 ElectivePlacementService: Getting elective subjects for:', params);

    // Use separate queries to avoid relationship ambiguity
    const { data, error } = await supabase
      .from('simplified_subject_faculty_mappings')
      .select(`
        id,
        subject_code,
        subject_name,
        hours_per_week,
        faculty_1_id,
        subjects(subject_short_id)
      `)
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .eq('semester', params.semester)
      .eq('section', params.section)
      .eq('subject_type', 'elective');

    console.log('📊 ElectivePlacementService query result:', { data, error });

    if (error) {
      console.error('❌ ElectivePlacementService database error:', error);
      throw error;
    }

    // Process electives and fetch faculty names separately
    const electives = [];
    if (data && data.length > 0) {
      for (const item of data) {
        let facultyName = 'Unknown Faculty';

        if (item.faculty_1_id) {
          const { data: facultyData, error: facultyError } = await supabase
            .from('employee_details')
            .select('full_name')
            .eq('id', item.faculty_1_id)
            .single();

          if (!facultyError && facultyData) {
            facultyName = facultyData.full_name;
          }
        }

        electives.push({
          subject_id: item.id,
          subject_code: item.subject_code,
          subject_name: item.subject_name,
          subject_short_id: item.subjects?.subject_short_id,
          faculty_id: item.faculty_1_id,
          faculty_name: facultyName,
          hours_per_week: item.hours_per_week
        });
      }
    }

    console.log('✅ ElectivePlacementService processed electives:', electives);
    return electives;
  }

  /**
   * Get all sections for the same semester (for synchronization)
   */
  static async getAllSectionsForSemester(
    academicYear: string,
    department: string,
    semester: string
  ): Promise<string[]> {
    const { data, error } = await supabase
      .from('simplified_subject_faculty_mappings')
      .select('section')
      .eq('academic_year', academicYear)
      .eq('department', department)
      .eq('semester', semester)
      .eq('subject_type', 'elective');

    if (error) throw error;

    const sections = [...new Set(data?.map(item => item.section) || [])];
    return sections.sort();
  }

  /**
   * Check faculty availability for elective placement across all sections
   * This ensures electives don't conflict with labs, skill labs, and tutorials
   */
  static async checkFacultyAvailabilityForElectives(
    facultyId: string,
    academicYear: string,
    department: string,
    semester: string
  ): Promise<{ [day: string]: string[] }> {
    // Get faculty availability
    console.log('🔍 Getting faculty availability for:', facultyId);

    const { data: facultyData, error: facultyError } = await supabase
      .from('employee_details')
      .select('vacant_by_day, vacant_count_by_day, full_name')
      .eq('id', facultyId)
      .single();

    if (facultyError) {
      console.error('❌ Error fetching faculty availability:', facultyError);
      throw facultyError;
    }

    console.log('📊 Raw faculty data for', facultyData?.full_name, ':', {
      vacant_by_day: facultyData?.vacant_by_day,
      type: typeof facultyData?.vacant_by_day
    });

    // Parse faculty availability properly
    let facultyAvailability: { [day: string]: string[] } = {};

    if (facultyData?.vacant_by_day) {
      const vacantByDay = facultyData.vacant_by_day;

      // Check if vacant_by_day contains counts (numbers) instead of time slot arrays
      if (typeof vacantByDay === 'object' && !Array.isArray(vacantByDay)) {
        const firstValue = Object.values(vacantByDay)[0];

        if (typeof firstValue === 'number') {
          // vacant_by_day contains counts, need to calculate actual available slots
          console.log('📊 vacant_by_day contains counts, calculating actual availability...');

          const allTimeSlots = [
            '08:30-09:25',
            '09:25-10:20',
            '10:35-11:30',
            '11:30-12:25',
            '13:15-14:10',
            '14:10-15:05',
            '15:05-16:00'
          ];

          // Get faculty's existing commitments to calculate actual availability
          const { data: existingSlots, error: slotsError } = await supabase
            .from('timetable_slots')
            .select('day, time_slot')
            .eq('faculty_id', facultyId)
            .eq('academic_year', academicYear)
            .eq('department', department)
            .eq('semester', semester);

          if (slotsError) {
            console.error('❌ Error fetching existing slots:', slotsError);
          }

          console.log('📊 Faculty existing commitments:', existingSlots);

          // Calculate available slots by removing existing commitments
          Object.keys(vacantByDay).forEach(day => {
            const dayCommitments = existingSlots?.filter(slot => slot.day === day).map(slot => slot.time_slot) || [];
            const availableSlots = allTimeSlots.filter(slot => !dayCommitments.includes(slot));
            facultyAvailability[day] = availableSlots;

            console.log(`📅 ${day}: ${dayCommitments.length} commitments, ${availableSlots.length} available slots`);
          });
        } else {
          // vacant_by_day contains actual time slot arrays
          facultyAvailability = vacantByDay as { [day: string]: string[] };
        }
      } else if (typeof vacantByDay === 'string') {
        try {
          const parsed = JSON.parse(vacantByDay);
          facultyAvailability = parsed;
        } catch (e) {
          console.error('❌ Failed to parse vacant_by_day:', e);
          facultyAvailability = {};
        }
      }
    }

    console.log('✅ Processed faculty availability:', facultyAvailability);

    // Get ALL sections for this semester to check conflicts across all sections
    const allSections = await this.getAllSectionsForSemester(academicYear, department, semester);
    console.log(`🔍 Checking conflicts across all sections: ${allSections.join(', ')}`);

    // Get occupied slots across ALL sections (labs, skill labs, tutorials)
    const occupiedSlots: { [day: string]: string[] } = {};

    for (const section of allSections) {
      const { data: sectionSlots, error: sectionError } = await supabase
        .from('timetable_slots')
        .select('day, time_slot, subject_type, subject_code')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['lab', 'skill_lab', 'tutorial']); // Only check conflicting types

      if (sectionError) throw sectionError;

      sectionSlots?.forEach(slot => {
        if (!occupiedSlots[slot.day]) {
          occupiedSlots[slot.day] = [];
        }
        // Add to global occupied slots if not already present
        if (!occupiedSlots[slot.day].includes(slot.time_slot)) {
          occupiedSlots[slot.day].push(slot.time_slot);
        }
      });

      console.log(`📊 Section ${section} has ${sectionSlots?.length || 0} conflicting slots`);
    }

    console.log(`📊 Global occupied slots (all sections):`, occupiedSlots);

    // Find available slots by removing globally occupied slots
    const availableSlots: { [day: string]: string[] } = {};

    Object.keys(facultyAvailability).forEach(day => {
      const facultyDaySlots = facultyAvailability[day];

      // Ensure facultyDaySlots is an array
      let daySlots: string[] = [];
      if (Array.isArray(facultyDaySlots)) {
        daySlots = facultyDaySlots;
      } else if (typeof facultyDaySlots === 'string') {
        // If it's a string, try to parse it as JSON
        try {
          const parsed = JSON.parse(facultyDaySlots);
          daySlots = Array.isArray(parsed) ? parsed : [];
        } catch {
          daySlots = [];
        }
      }

      console.log(`📅 Faculty availability for ${day}:`, daySlots);

      // Remove globally occupied slots (labs, skill labs, tutorials from ALL sections)
      const globallyOccupiedSlots = occupiedSlots[day] || [];
      const freeSlots = daySlots.filter(timeSlot => {
        return !globallyOccupiedSlots.includes(timeSlot);
      });

      if (freeSlots.length > 0) {
        availableSlots[day] = freeSlots;
      }

      console.log(`📅 ${day}: ${daySlots.length} faculty slots, ${globallyOccupiedSlots.length} occupied, ${freeSlots.length} available`);
    });

    return availableSlots;
  }

  /**
   * Find optimal time slots for electives with proper conflict detection
   */
  static async findSynchronizedElectiveSlots(
    electives: ElectiveSubject[],
    params: ElectivePlacementParams,
    groupingType: 'same_group' | 'different_groups'
  ): Promise<{ [subjectCode: string]: TimeSlot[] }> {
    console.log(`🔄 Finding slots for ${electives.length} electives`);
    console.log(`📋 Grouping Type: "${groupingType}"`);
    console.log(`📋 Params:`, params);
    console.log(`📋 Electives:`, electives.map(e => ({ code: e.subject_code, faculty: e.faculty_name })));

    // DIAGNOSTIC: First, let's see what slots are completely free across all sections
    const diagnosticFreeSlots = await this.getDiagnosticFreeTimeSlots(
      params.academicYear,
      params.department,
      params.semester
    );

    // Step 1: Get globally occupied periods across ALL sections
    const globallyOccupiedPeriods = await this.getGloballyOccupiedPeriods(
      params.academicYear,
      params.department,
      params.semester
    );

    // Step 2: Find truly free periods for elective placement
    const trulyFreePeriods = this.findTrulyFreePeriods(globallyOccupiedPeriods);
    console.log(`🎯 Truly free periods for electives:`, trulyFreePeriods);

    const electiveSlots: { [subjectCode: string]: TimeSlot[] } = {};
    const usedSlots: TimeSlot[] = []; // Track slots used by previous electives

    console.log(`🔀 Checking grouping type: "${groupingType}"`);

    if (groupingType === 'same_group') {
      console.log(`🔗 Using SAME GROUP logic - all electives share same time slots`);

      // Use truly free periods for same group electives
      const selectedSlots = this.selectOptimalTimeSlots(trulyFreePeriods, electives[0].hours_per_week);

      // Assign same slots to all electives (students choose one)
      electives.forEach(elective => {
        electiveSlots[elective.subject_code] = selectedSlots;
      });

    } else {
      console.log(`🔀 Using DIFFERENT GROUPS logic - each elective gets separate time slots`);

      // Create a copy of truly free periods to track usage
      let availableFreePeriods = JSON.parse(JSON.stringify(trulyFreePeriods));

      // Each elective gets separate, non-conflicting time slots
      for (let i = 0; i < electives.length; i++) {
        const elective = electives[i];
        console.log(`\n📚 Processing elective ${i + 1}/${electives.length}: ${elective.subject_code} (${elective.faculty_name})`);

        console.log(`📅 Available free periods for ${elective.subject_code}:`, availableFreePeriods);

        // Check faculty availability within the free periods
        const facultyAvailableSlots = await this.getFacultyAvailableSlots(
          elective.faculty_id,
          {}, // Empty since we're using truly free periods
          params.academicYear,
          params.department,
          params.semester
        );

        // Intersect faculty availability with truly free periods
        const facultyFreePeriods = this.intersectAvailability(facultyAvailableSlots, availableFreePeriods);
        console.log(`📅 Faculty available within free periods:`, facultyFreePeriods);

        const selectedSlots = this.selectOptimalTimeSlots(facultyFreePeriods, elective.hours_per_week);
        electiveSlots[elective.subject_code] = selectedSlots;

        // Remove selected slots from available free periods for next elective
        selectedSlots.forEach(slot => {
          if (availableFreePeriods[slot.day]) {
            availableFreePeriods[slot.day] = availableFreePeriods[slot.day].filter(
              period => period !== slot.time_slot
            );
            if (availableFreePeriods[slot.day].length === 0) {
              delete availableFreePeriods[slot.day];
            }
          }
        });

        console.log(`✅ Assigned ${selectedSlots.length} slots to ${elective.subject_code}:`, selectedSlots);
        console.log(`📝 Remaining free periods:`, availableFreePeriods);
      }
    }

    return electiveSlots;
  }

  /**
   * DIAGNOSTIC: Get completely free time slots across all sections
   */
  static async getDiagnosticFreeTimeSlots(
    academicYear: string,
    department: string,
    semester: string
  ): Promise<{ [day: string]: string[] }> {
    console.log('\n🔍 DIAGNOSTIC: Analyzing free time slots across all sections...');

    // Get all sections for this semester
    const allSections = await this.getAllSectionsForSemester(academicYear, department, semester);
    console.log(`📊 Analyzing sections: ${allSections.join(', ')}`);

    // All possible time slots in a day
    const allTimeSlots = [
      '08:30-09:25',  // Period 1
      '09:25-10:20',  // Period 2
      '10:35-11:30',  // Period 3
      '11:30-12:25',  // Period 4
      '13:15-14:10',  // Period 5
      '14:10-15:05',  // Period 6
      '15:05-16:00'   // Period 7
    ];

    const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

    // Get occupied slots for each section
    const sectionOccupiedSlots: { [section: string]: { [day: string]: string[] } } = {};

    for (const section of allSections) {
      console.log(`\n📋 Analyzing Section ${section}:`);

      const { data: sectionSlots, error } = await supabase
        .from('timetable_slots')
        .select('day, time_slot, subject_type, subject_code, subject_name')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['lab', 'skill_lab', 'tutorial']);

      if (error) {
        console.error(`❌ Error fetching slots for section ${section}:`, error);
        continue;
      }

      sectionOccupiedSlots[section] = {};

      // Group by day
      allDays.forEach(day => {
        const daySlots = sectionSlots?.filter(slot => slot.day === day) || [];
        sectionOccupiedSlots[section][day] = daySlots.map(slot => slot.time_slot);

        if (daySlots.length > 0) {
          console.log(`  📅 ${day}: ${daySlots.length} occupied slots`);
          daySlots.forEach(slot => {
            console.log(`    🔒 ${slot.time_slot} - ${slot.subject_type.toUpperCase()} (${slot.subject_code})`);
          });
        } else {
          console.log(`  📅 ${day}: No occupied slots`);
        }
      });
    }

    // Find globally free slots (free across ALL sections)
    const globallyFreeSlots: { [day: string]: string[] } = {};

    console.log('\n🌍 GLOBAL ANALYSIS - Finding slots free across ALL sections:');

    allDays.forEach(day => {
      const freeSlots: string[] = [];

      allTimeSlots.forEach(timeSlot => {
        let isOccupiedInAnySection = false;

        for (const section of allSections) {
          const sectionDaySlots = sectionOccupiedSlots[section][day] || [];
          if (sectionDaySlots.includes(timeSlot)) {
            isOccupiedInAnySection = true;
            break;
          }
        }

        if (!isOccupiedInAnySection) {
          freeSlots.push(timeSlot);
        }
      });

      globallyFreeSlots[day] = freeSlots;

      console.log(`📅 ${day}: ${freeSlots.length}/${allTimeSlots.length} slots free globally`);
      if (freeSlots.length > 0) {
        console.log(`  ✅ Free slots: ${freeSlots.join(', ')}`);
      } else {
        console.log(`  ❌ No free slots available`);
      }
    });

    // Summary
    const totalFreeSlots = Object.values(globallyFreeSlots).reduce((sum, daySlots) => sum + daySlots.length, 0);
    const totalPossibleSlots = allDays.length * allTimeSlots.length;

    console.log(`\n📊 SUMMARY:`);
    console.log(`  Total possible slots: ${totalPossibleSlots}`);
    console.log(`  Total free slots: ${totalFreeSlots}`);
    console.log(`  Utilization: ${((totalPossibleSlots - totalFreeSlots) / totalPossibleSlots * 100).toFixed(1)}%`);

    return globallyFreeSlots;
  }

  /**
   * Convert time slot to individual periods
   */
  static timeSlotToPeriods(timeSlot: string): string[] {
    const periodMap: { [key: string]: string[] } = {
      '08:30-09:25': ['08:30-09:25'],
      '09:25-10:20': ['09:25-10:20'],
      '08:30-10:20': ['08:30-09:25', '09:25-10:20'], // 2-hour lab
      '10:35-11:30': ['10:35-11:30'],
      '11:30-12:25': ['11:30-12:25'],
      '10:35-12:25': ['10:35-11:30', '11:30-12:25'], // 2-hour lab
      '13:15-14:10': ['13:15-14:10'],
      '14:10-15:05': ['14:10-15:05'],
      '15:05-16:00': ['15:05-16:00'],
      '13:15-15:05': ['13:15-14:10', '14:10-15:05'], // 2-hour lab
      '14:10-16:00': ['14:10-15:05', '15:05-16:00'], // 2-hour lab
      '13:15-16:00': ['13:15-14:10', '14:10-15:05', '15:05-16:00'] // 3-hour skill lab
    };

    return periodMap[timeSlot] || [timeSlot];
  }

  /**
   * Get globally occupied individual periods across ALL sections
   */
  static async getGloballyOccupiedPeriods(
    academicYear: string,
    department: string,
    semester: string
  ): Promise<{ [day: string]: string[] }> {
    const allSections = await this.getAllSectionsForSemester(academicYear, department, semester);
    console.log(`🔍 Checking period conflicts across sections: ${allSections.join(', ')}`);

    const globallyOccupiedPeriods: { [day: string]: string[] } = {};

    for (const section of allSections) {
      const { data: sectionSlots, error } = await supabase
        .from('timetable_slots')
        .select('day, time_slot, subject_type, subject_code')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['lab', 'skill_lab', 'tutorial']);

      if (error) throw error;

      sectionSlots?.forEach(slot => {
        if (!globallyOccupiedPeriods[slot.day]) {
          globallyOccupiedPeriods[slot.day] = [];
        }

        // Convert multi-period slots to individual periods
        const periods = this.timeSlotToPeriods(slot.time_slot);
        periods.forEach(period => {
          if (!globallyOccupiedPeriods[slot.day].includes(period)) {
            globallyOccupiedPeriods[slot.day].push(period);
          }
        });
      });

      console.log(`📊 Section ${section}: ${sectionSlots?.length || 0} conflicting slots`);
    }

    // Log the occupied periods for each day
    Object.keys(globallyOccupiedPeriods).forEach(day => {
      console.log(`🔒 ${day} occupied periods: ${globallyOccupiedPeriods[day].join(', ')}`);
    });

    return globallyOccupiedPeriods;
  }

  /**
   * Get globally occupied time slots across ALL sections (legacy method)
   */
  static async getGloballyOccupiedSlots(
    academicYear: string,
    department: string,
    semester: string
  ): Promise<{ [day: string]: string[] }> {
    const allSections = await this.getAllSectionsForSemester(academicYear, department, semester);
    console.log(`🔍 Checking conflicts across sections: ${allSections.join(', ')}`);

    const globallyOccupied: { [day: string]: string[] } = {};

    for (const section of allSections) {
      const { data: sectionSlots, error } = await supabase
        .from('timetable_slots')
        .select('day, time_slot, subject_type, subject_code')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['lab', 'skill_lab', 'tutorial']);

      if (error) throw error;

      sectionSlots?.forEach(slot => {
        if (!globallyOccupied[slot.day]) {
          globallyOccupied[slot.day] = [];
        }
        if (!globallyOccupied[slot.day].includes(slot.time_slot)) {
          globallyOccupied[slot.day].push(slot.time_slot);
        }
      });

      console.log(`📊 Section ${section}: ${sectionSlots?.length || 0} conflicting slots`);
    }

    return globallyOccupied;
  }

  /**
   * Find truly free periods that don't conflict with any labs/skill labs/tutorials
   */
  static findTrulyFreePeriods(globallyOccupiedPeriods: { [day: string]: string[] }): { [day: string]: string[] } {
    const allPossiblePeriods = [
      '08:30-09:25',  // Period 1
      '09:25-10:20',  // Period 2
      '10:35-11:30',  // Period 3
      '11:30-12:25',  // Period 4
      '13:15-14:10',  // Period 5
      '14:10-15:05',  // Period 6
      '15:05-16:00'   // Period 7
    ];

    const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const trulyFreePeriods: { [day: string]: string[] } = {};

    allDays.forEach(day => {
      const occupiedPeriods = globallyOccupiedPeriods[day] || [];
      const freePeriods = allPossiblePeriods.filter(period =>
        !occupiedPeriods.includes(period)
      );

      if (freePeriods.length > 0) {
        trulyFreePeriods[day] = freePeriods;
      }

      console.log(`📅 ${day}: ${freePeriods.length}/${allPossiblePeriods.length} periods free`);
      if (freePeriods.length > 0) {
        console.log(`  ✅ Free periods: ${freePeriods.join(', ')}`);
      } else {
        console.log(`  ❌ No free periods`);
      }
    });

    return trulyFreePeriods;
  }

  /**
   * Intersect faculty availability with truly free periods
   */
  static intersectAvailability(
    facultyAvailability: { [day: string]: string[] },
    trulyFreePeriods: { [day: string]: string[] }
  ): { [day: string]: string[] } {
    const intersection: { [day: string]: string[] } = {};

    Object.keys(trulyFreePeriods).forEach(day => {
      const freePeriods = trulyFreePeriods[day] || [];
      const facultyPeriods = facultyAvailability[day] || [];

      // Find periods that are both free globally and available for faculty
      const commonPeriods = freePeriods.filter(period =>
        facultyPeriods.includes(period)
      );

      if (commonPeriods.length > 0) {
        intersection[day] = commonPeriods;
      }
    });

    return intersection;
  }

  /**
   * Get faculty available slots after removing global conflicts
   */
  static async getFacultyAvailableSlots(
    facultyId: string,
    globallyOccupiedSlots: { [day: string]: string[] },
    academicYear: string,
    department: string,
    semester: string
  ): Promise<{ [day: string]: string[] }> {
    // Get faculty availability
    const { data: facultyData, error } = await supabase
      .from('employee_details')
      .select('vacant_by_day, full_name')
      .eq('id', facultyId)
      .single();

    if (error) throw error;

    console.log(`🔍 Processing availability for: ${facultyData?.full_name}`);

    // Parse faculty availability
    let facultyAvailability: { [day: string]: string[] } = {};
    if (facultyData?.vacant_by_day) {
      const vacantByDay = facultyData.vacant_by_day;

      if (typeof vacantByDay === 'object' && !Array.isArray(vacantByDay)) {
        const firstValue = Object.values(vacantByDay)[0];

        if (typeof firstValue === 'number') {
          // Convert counts to time slot arrays
          const allTimeSlots = [
            '08:30-09:25', '09:25-10:20', '10:35-11:30', '11:30-12:25',
            '13:15-14:10', '14:10-15:05', '15:05-16:00'
          ];

          Object.keys(vacantByDay).forEach(day => {
            const count = vacantByDay[day] as number;
            facultyAvailability[day] = allTimeSlots.slice(0, count);
          });
        } else {
          facultyAvailability = vacantByDay as { [day: string]: string[] };
        }
      }
    }

    // Remove faculty's existing commitments
    const { data: existingSlots } = await supabase
      .from('timetable_slots')
      .select('day, time_slot')
      .eq('faculty_id', facultyId)
      .eq('academic_year', academicYear)
      .eq('department', department)
      .eq('semester', semester);

    existingSlots?.forEach(slot => {
      if (facultyAvailability[slot.day]) {
        facultyAvailability[slot.day] = facultyAvailability[slot.day].filter(
          timeSlot => timeSlot !== slot.time_slot
        );
      }
    });

    // Remove globally occupied slots
    const availableSlots: { [day: string]: string[] } = {};
    Object.keys(facultyAvailability).forEach(day => {
      const facultyDaySlots = facultyAvailability[day] || [];
      const globallyOccupiedDaySlots = globallyOccupiedSlots[day] || [];

      const freeSlots = facultyDaySlots.filter(timeSlot =>
        !globallyOccupiedDaySlots.includes(timeSlot)
      );

      if (freeSlots.length > 0) {
        availableSlots[day] = freeSlots;
      }

      console.log(`📅 ${day}: ${facultyDaySlots.length} faculty slots, ${globallyOccupiedDaySlots.length} occupied, ${freeSlots.length} available`);
    });

    return availableSlots;
  }

  /**
   * Remove already used slots from available slots
   */
  static removeUsedSlots(
    availableSlots: { [day: string]: string[] },
    usedSlots: TimeSlot[]
  ): { [day: string]: string[] } {
    console.log(`🔍 removeUsedSlots called with:`, { availableSlots, usedSlots });

    const filteredSlots: { [day: string]: string[] } = {};

    Object.keys(availableSlots).forEach(day => {
      const daySlots = availableSlots[day] || [];
      const usedDaySlots = usedSlots
        .filter(slot => slot.day === day)
        .map(slot => slot.time_slot);

      console.log(`📅 Day ${day}: available=${daySlots.length}, used=${usedDaySlots.length}`);
      console.log(`📅 Day ${day} available slots:`, daySlots);
      console.log(`📅 Day ${day} used slots:`, usedDaySlots);

      const remainingSlots = daySlots.filter(timeSlot =>
        !usedDaySlots.includes(timeSlot)
      );

      console.log(`📅 Day ${day} remaining slots:`, remainingSlots);

      if (remainingSlots.length > 0) {
        filteredSlots[day] = remainingSlots;
      }
    });

    console.log(`✅ removeUsedSlots result:`, filteredSlots);
    return filteredSlots;
  }

  /**
   * Select optimal time slots from available slots
   */
  static selectOptimalTimeSlots(
    availableSlots: { [day: string]: string[] },
    hoursPerWeek: number
  ): TimeSlot[] {
    const selectedSlots: TimeSlot[] = [];
    const days = Object.keys(availableSlots).sort();

    console.log(`🎯 Selecting ${hoursPerWeek} slots from available:`, availableSlots);

    if (days.length === 0) {
      console.warn('⚠️ No available days for slot selection');
      return selectedSlots;
    }

    let slotsNeeded = hoursPerWeek;

    // Strategy 1: Try to distribute across different days first
    for (let dayIndex = 0; dayIndex < days.length && slotsNeeded > 0; dayIndex++) {
      const day = days[dayIndex];
      const daySlots = availableSlots[day];

      if (daySlots && daySlots.length > 0) {
        // Take the first available slot from this day
        const timeSlot = daySlots[0];
        selectedSlots.push({
          day,
          time_slot: timeSlot,
          period_index: this.getPeriodIndex(timeSlot)
        });
        slotsNeeded--;

        // Remove this slot from available slots to avoid reuse
        availableSlots[day] = daySlots.slice(1);

        console.log(`✅ Selected slot: ${day} at ${timeSlot} (${slotsNeeded} remaining)`);
      }
    }

    // Strategy 2: If we still need more slots, take additional slots from days with availability
    let roundCount = 0;
    while (slotsNeeded > 0 && roundCount < 3) { // Max 3 rounds to prevent infinite loop
      let slotsAddedThisRound = 0;

      for (let dayIndex = 0; dayIndex < days.length && slotsNeeded > 0; dayIndex++) {
        const day = days[dayIndex];
        const daySlots = availableSlots[day];

        if (daySlots && daySlots.length > 0) {
          const timeSlot = daySlots[0];
          selectedSlots.push({
            day,
            time_slot: timeSlot,
            period_index: this.getPeriodIndex(timeSlot)
          });
          slotsNeeded--;
          slotsAddedThisRound++;

          // Remove this slot from available slots
          availableSlots[day] = daySlots.slice(1);

          console.log(`✅ Selected additional slot: ${day} at ${timeSlot} (${slotsNeeded} remaining)`);
        }
      }

      if (slotsAddedThisRound === 0) {
        console.warn(`⚠️ Could not find enough slots. Still need ${slotsNeeded} more slots.`);
        break;
      }

      roundCount++;
    }

    console.log(`🎯 Final selected slots (${selectedSlots.length}/${hoursPerWeek}):`, selectedSlots);
    return selectedSlots;
  }

  /**
   * Get period index from time slot
   */
  static getPeriodIndex(timeSlot: string): number {
    // Map common time slots to period indices
    const timeSlotMap: { [key: string]: number } = {
      '08:30-09:25': 1,
      '09:25-10:20': 2,
      '10:35-11:30': 3,
      '11:30-12:25': 4,
      '13:15-14:10': 5,
      '14:10-15:05': 6,
      '15:05-16:00': 7
    };

    return timeSlotMap[timeSlot] || 1;
  }

  /**
   * Create elective timetable slots
   */
  static async createElectiveSlots(
    electives: ElectiveSubject[],
    electiveSlots: { [subjectCode: string]: TimeSlot[] },
    params: ElectivePlacementParams,
    groupingType: 'same_group' | 'different_groups'
  ): Promise<void> {
    console.log(`🎯 createElectiveSlots called with:`);
    console.log(`  📋 Electives:`, electives);
    console.log(`  📋 ElectiveSlots:`, electiveSlots);
    console.log(`  📋 Params:`, params);
    console.log(`  📋 GroupingType:`, groupingType);

    // Only place electives for the specific section being processed
    const sections = [params.section];

    console.log(`🎯 Creating elective slots for section: ${params.section}`);

    const slotsToInsert: any[] = [];

    for (const section of sections) {
      console.log(`📋 Processing section: ${section}`);

      for (const elective of electives) {
        const slots = electiveSlots[elective.subject_code] || [];
        console.log(`📚 Processing elective ${elective.subject_code}: ${slots.length} slots`);

        for (const slot of slots) {
          const slotToInsert = {
            academic_year: params.academicYear,
            department: params.department,
            semester: params.semester,
            section: section,
            day: slot.day,
            time_slot: slot.time_slot,
            subject_id: elective.subject_id,
            subject_code: elective.subject_code,
            subject_name: elective.subject_name,
            subject_type: 'elective',
            faculty_id: elective.faculty_id,
            faculty_name: elective.faculty_name,
            room_number: 'Classroom',
            subject_short_id: elective.subject_short_id || elective.subject_code,
            batch_name: null,
            is_lab_start: false,
            is_processed: false,
            col_span: 1,
            faculty2_id: null,
            faculty2_name: null,
            is_hidden: false,
            is_full_session_lab: false,
            period_index: slot.period_index
          };

          console.log(`  ➕ Adding slot:`, slotToInsert);
          slotsToInsert.push(slotToInsert);
        }
      }
    }

    console.log(`📊 Total slots to insert: ${slotsToInsert.length}`);
    console.log(`📊 Slots to insert:`, slotsToInsert);

    if (slotsToInsert.length > 0) {
      console.log(`🔄 Inserting ${slotsToInsert.length} elective slots...`);

      // Validate no conflicts with existing theory/elective slots
      for (const slot of slotsToInsert) {
        const { data: existingSlots, error: checkError } = await supabase
          .from('timetable_slots')
          .select('subject_code, subject_type')
          .eq('academic_year', slot.academic_year)
          .eq('department', slot.department)
          .eq('semester', slot.semester)
          .eq('section', slot.section)
          .eq('day', slot.day)
          .eq('time_slot', slot.time_slot)
          .in('subject_type', ['theory', 'elective']);

        if (checkError) {
          console.error('❌ Error checking for conflicts:', checkError);
        } else if (existingSlots && existingSlots.length > 0) {
          console.warn(`⚠️ Conflict detected for ${slot.subject_code} at ${slot.day} ${slot.time_slot}:`, existingSlots);
        }
      }

      const { data, error } = await supabase
        .from('timetable_slots')
        .insert(slotsToInsert)
        .select();

      if (error) {
        console.error('❌ Error inserting elective slots:', error);
        console.error('❌ Error details:', JSON.stringify(error, null, 2));
        console.error('❌ Failed slots:', slotsToInsert);
        throw error;
      }

      console.log(`✅ Created ${slotsToInsert.length} elective slots successfully`);
      console.log(`✅ Inserted data:`, data);
    } else {
      console.warn('⚠️ No elective slots to insert!');
    }
  }

  /**
   * Update faculty availability after elective placement
   */
  static async updateFacultyAvailability(
    electives: ElectiveSubject[],
    electiveSlots: { [subjectCode: string]: TimeSlot[] }
  ): Promise<void> {
    for (const elective of electives) {
      const slots = electiveSlots[elective.subject_code] || [];

      // Get current faculty availability
      const { data: facultyData, error: fetchError } = await supabase
        .from('employee_details')
        .select('vacant_by_day, vacant_count_by_day')
        .eq('id', elective.faculty_id)
        .single();

      if (fetchError) throw fetchError;

      const vacantByDay = { ...facultyData.vacant_by_day };
      const vacantCountByDay = { ...facultyData.vacant_count_by_day };

      // Remove assigned slots from availability
      slots.forEach(slot => {
        if (vacantByDay[slot.day]) {
          // Ensure vacantByDay[slot.day] is an array
          let daySlots = vacantByDay[slot.day];
          if (!Array.isArray(daySlots)) {
            // If it's a number, convert to array of time slots
            if (typeof daySlots === 'number') {
              const allTimeSlots = [
                '08:30-09:25', '09:25-10:20', '10:35-11:30', '11:30-12:25',
                '13:15-14:10', '14:10-15:05', '15:05-16:00'
              ];
              daySlots = allTimeSlots.slice(0, daySlots);
            } else {
              daySlots = [];
            }
          }

          // Remove the assigned slot
          vacantByDay[slot.day] = daySlots.filter(
            (timeSlot: string) => timeSlot !== slot.time_slot
          );
          vacantCountByDay[slot.day] = vacantByDay[slot.day].length;
        }
      });

      // Update faculty availability
      const { error: updateError } = await supabase
        .from('employee_details')
        .update({
          vacant_by_day: vacantByDay,
          vacant_count_by_day: vacantCountByDay
        })
        .eq('id', elective.faculty_id);

      if (updateError) throw updateError;

      console.log(`✅ Updated availability for faculty: ${elective.faculty_name}`);
    }
  }
}
