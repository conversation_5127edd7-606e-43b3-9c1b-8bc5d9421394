// TimeSlotValidationService.ts
import { TimeStructure } from "@/services/TimetableService";

export interface TimeSlot {
  start: string;
  end: string;
  period: number;
}

export interface TimeValidationResult {
  isValid: boolean;
  message: string;
  periods: string;
  type: 'success' | 'warning' | 'error';
  startPeriod?: number;
  endPeriod?: number;
  duration?: number;
}

export interface AvailableTimeOption {
  value: string;
  label: string;
  period: number;
  disabled?: boolean;
  reason?: string;
}

export class TimeSlotValidationService {
  // Standard time structure - can be overridden with actual TimeStructure data
  private static getStandardTimeSlots(): TimeSlot[] {
    return [
      { start: "08:30", end: "09:25", period: 1 },
      { start: "09:25", end: "10:20", period: 2 },
      { start: "10:35", end: "11:30", period: 3 }, // After tea break
      { start: "11:30", end: "12:25", period: 4 },
      { start: "13:15", end: "14:10", period: 5 }, // After lunch break
      { start: "14:10", end: "15:05", period: 6 },
      { start: "15:05", end: "16:00", period: 7 },
    ];
  }

  /**
   * Get time slots from TimeStructure if available, otherwise use standard
   */
  static getTimeSlots(timeStructure?: TimeStructure): TimeSlot[] {
    if (timeStructure) {
      // Generate time slots from TimeStructure
      const slots: TimeSlot[] = [];
      const duration = timeStructure.theory_class_duration;

      // First half periods
      let currentTime = timeStructure.first_half_start_time;
      for (let i = 1; i <= timeStructure.periods_in_first_half; i++) {
        const endTime = this.addMinutesToTime(currentTime, duration);
        slots.push({ start: currentTime, end: endTime, period: i });

        // Add break time after period 2
        if (i === 2) {
          currentTime = timeStructure.tea_break_end_time;
        } else {
          currentTime = endTime;
        }
      }

      // Second half periods
      currentTime = timeStructure.second_half_start_time;
      for (let i = 1; i <= timeStructure.periods_in_second_half; i++) {
        const periodNumber = timeStructure.periods_in_first_half + i;
        const endTime = this.addMinutesToTime(currentTime, duration);
        slots.push({ start: currentTime, end: endTime, period: periodNumber });
        currentTime = endTime;
      }

      return slots;
    }

    return this.getStandardTimeSlots();
  }

  /**
   * Get available start times for a given duration
   */
  static getAvailableStartTimes(
    duration: number,
    timeStructure?: TimeStructure,
    existingBookings?: { day: string; startTime: string; endTime: string }[]
  ): AvailableTimeOption[] {
    const timeSlots = this.getTimeSlots(timeStructure);
    const options: AvailableTimeOption[] = [];

    for (const slot of timeSlots) {
      const validation = this.validateTimeSelection(slot.start, "", duration, timeStructure);
      const calculatedEndTime = this.calculateEndTime(slot.start, duration, timeStructure);

      if (calculatedEndTime) {
        const finalValidation = this.validateTimeSelection(slot.start, calculatedEndTime, duration, timeStructure);

        options.push({
          value: slot.start,
          label: `${slot.start} (Period ${slot.period})`,
          period: slot.period,
          disabled: !finalValidation.isValid,
          reason: finalValidation.isValid ? undefined : finalValidation.message
        });
      } else {
        options.push({
          value: slot.start,
          label: `${slot.start} (Period ${slot.period})`,
          period: slot.period,
          disabled: true,
          reason: `Cannot fit ${duration}-hour lab starting at this time`
        });
      }
    }

    return options;
  }

  /**
   * Calculate end time based on start time and duration
   */
  static calculateEndTime(
    startTime: string,
    duration: number,
    timeStructure?: TimeStructure
  ): string {
    const timeSlots = this.getTimeSlots(timeStructure);
    const startSlot = timeSlots.find(slot => slot.start === startTime);

    if (!startSlot) return "";

    const startPeriod = startSlot.period;
    let endPeriod = startPeriod + duration - 1;

    // Handle break periods
    const periodsInFirstHalf = timeStructure?.periods_in_first_half || 4;

    // If lab spans across tea break (between periods 2 and 3)
    if (startPeriod <= 2 && endPeriod >= 3) {
      // For 3-hour labs starting at period 1, this is acceptable
      // For 2-hour labs, this should be avoided but we'll allow it with warning
    }

    // If lab would span across lunch break
    if (startPeriod <= periodsInFirstHalf && endPeriod > periodsInFirstHalf) {
      return ""; // Not allowed
    }

    const endSlot = timeSlots.find(slot => slot.period === endPeriod);
    return endSlot ? endSlot.end : "";
  }

  /**
   * Validate time selection
   */
  static validateTimeSelection(
    startTime: string,
    endTime: string,
    expectedDuration: number,
    timeStructure?: TimeStructure
  ): TimeValidationResult {
    if (!startTime) {
      return {
        isValid: false,
        message: "Please select a start time",
        periods: "",
        type: 'error'
      };
    }

    const timeSlots = this.getTimeSlots(timeStructure);
    const startSlot = timeSlots.find(slot => slot.start === startTime);

    if (!startSlot) {
      return {
        isValid: false,
        message: "Invalid start time",
        periods: "",
        type: 'error'
      };
    }

    // If no end time provided, calculate it
    if (!endTime) {
      endTime = this.calculateEndTime(startTime, expectedDuration, timeStructure);
      if (!endTime) {
        return {
          isValid: false,
          message: `Cannot fit ${expectedDuration}-hour lab starting at ${startTime}`,
          periods: "",
          type: 'error'
        };
      }
    }

    const endSlot = timeSlots.find(slot => slot.end === endTime);

    if (!endSlot) {
      return {
        isValid: false,
        message: "Invalid end time",
        periods: "",
        type: 'error'
      };
    }

    const startPeriod = startSlot.period;
    const endPeriod = endSlot.period;
    const actualDuration = endPeriod - startPeriod + 1;

    // Check if duration matches expected
    if (actualDuration !== expectedDuration) {
      return {
        isValid: false,
        message: `Selected time span is ${actualDuration} periods, but ${expectedDuration} periods required`,
        periods: `Periods ${startPeriod}-${endPeriod}`,
        type: 'error',
        startPeriod,
        endPeriod,
        duration: actualDuration
      };
    }

    // Check for break conflicts
    const periodsInFirstHalf = timeStructure?.periods_in_first_half || 4;

    // Check if spans across lunch break
    if (startPeriod <= periodsInFirstHalf && endPeriod > periodsInFirstHalf) {
      return {
        isValid: false,
        message: "Lab sessions cannot span across lunch break",
        periods: `Periods ${startPeriod}-${endPeriod}`,
        type: 'error',
        startPeriod,
        endPeriod,
        duration: actualDuration
      };
    }

    // Check if spans across tea break for 2-hour labs
    if (startPeriod === 2 && endPeriod === 3 && actualDuration === 2) {
      return {
        isValid: true,
        message: "Lab spans across tea break - ensure proper coordination",
        periods: `Periods ${startPeriod}-${endPeriod}`,
        type: 'warning',
        startPeriod,
        endPeriod,
        duration: actualDuration
      };
    }

    return {
      isValid: true,
      message: "Valid time selection",
      periods: `Periods ${startPeriod}-${endPeriod}`,
      type: 'success',
      startPeriod,
      endPeriod,
      duration: actualDuration
    };
  }

  /**
   * Convert time selection to time range string for storage
   * Ensures format matches database constraint: HH:MM-HH:MM
   */
  static formatTimeRange(startTime: string, endTime: string): string {
    // Ensure times are in HH:MM format (pad with zeros if needed)
    const formatTime = (time: string): string => {
      const parts = time.split(':');
      if (parts.length === 2) {
        const hours = parts[0].padStart(2, '0');
        const minutes = parts[1].padStart(2, '0');
        return `${hours}:${minutes}`;
      }
      return time;
    };

    const formattedStart = formatTime(startTime);
    const formattedEnd = formatTime(endTime);

    return `${formattedStart}-${formattedEnd}`;
  }

  /**
   * Parse time range string back to start and end times
   */
  static parseTimeRange(timeRange: string): { startTime: string; endTime: string } | null {
    const parts = timeRange.split('-');
    if (parts.length === 2) {
      return {
        startTime: parts[0],
        endTime: parts[1]
      };
    }
    return null;
  }

  /**
   * Helper method to add minutes to a time string
   */
  private static addMinutesToTime(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, mins, 0, 0);
    date.setMinutes(date.getMinutes() + minutes);
    return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  }

  /**
   * Get period numbers for a time range
   */
  static getPeriodsForTimeRange(
    startTime: string,
    endTime: string,
    timeStructure?: TimeStructure
  ): number[] {
    const timeSlots = this.getTimeSlots(timeStructure);
    const startSlot = timeSlots.find(slot => slot.start === startTime);
    const endSlot = timeSlots.find(slot => slot.end === endTime);

    if (!startSlot || !endSlot) return [];

    const periods: number[] = [];
    for (let p = startSlot.period; p <= endSlot.period; p++) {
      periods.push(p);
    }

    return periods;
  }

  /**
   * Parse flexible time range format and extract duration and periods
   * Handles both old format ("Morning", "Afternoon") and new format ("08:30-10:20")
   */
  static parseTimeSlotInfo(
    timeOfDay: string,
    timeStructure?: TimeStructure
  ): {
    startTime: string;
    endTime: string;
    duration: number;
    periods: number[];
    timeSlots: string[];
    isFlexibleFormat: boolean;
  } | null {
    // Handle null/undefined timeOfDay
    if (!timeOfDay || typeof timeOfDay !== 'string') {
      console.warn('parseTimeSlotInfo: timeOfDay is null, undefined, or not a string:', timeOfDay);
      return null;
    }

    // Check if it's the new flexible format (HH:MM-HH:MM)
    const flexibleFormatMatch = timeOfDay.match(/^([01][0-9]|2[0-3]):([0-5][0-9])-([01][0-9]|2[0-3]):([0-5][0-9])$/);

    if (flexibleFormatMatch) {
      // Parse flexible format
      const [, startHour, startMin, endHour, endMin] = flexibleFormatMatch;
      const startTime = `${startHour}:${startMin}`;
      const endTime = `${endHour}:${endMin}`;

      // Calculate duration in hours
      const startMinutes = parseInt(startHour) * 60 + parseInt(startMin);
      const endMinutes = parseInt(endHour) * 60 + parseInt(endMin);
      const durationMinutes = endMinutes - startMinutes;
      const duration = Math.ceil(durationMinutes / 55); // Assuming 55-minute periods

      // Get periods and time slots
      const periods = this.getPeriodsForTimeRange(startTime, endTime, timeStructure);
      const timeSlots = this.getTimeSlotStringsForRange(startTime, endTime, timeStructure);

      return {
        startTime,
        endTime,
        duration,
        periods,
        timeSlots,
        isFlexibleFormat: true
      };
    }

    // Handle old predefined formats
    const predefinedMappings = this.getPredefinedTimeSlotMappings(timeStructure);
    const mapping = predefinedMappings[timeOfDay];

    if (mapping) {
      return {
        ...mapping,
        isFlexibleFormat: false
      };
    }

    return null;
  }

  /**
   * Get time slot strings for a time range
   */
  static getTimeSlotStringsForRange(
    startTime: string,
    endTime: string,
    timeStructure?: TimeStructure
  ): string[] {
    const timeSlots = this.getTimeSlots(timeStructure);
    const startSlot = timeSlots.find(slot => slot.start === startTime);
    const endSlot = timeSlots.find(slot => slot.end === endTime);

    if (!startSlot || !endSlot) return [];

    const slots: string[] = [];
    for (let p = startSlot.period; p <= endSlot.period; p++) {
      const slot = timeSlots.find(s => s.period === p);
      if (slot) {
        slots.push(`${slot.start}-${slot.end}`);
      }
    }

    return slots;
  }

  /**
   * Get predefined time slot mappings for backward compatibility
   */
  static getPredefinedTimeSlotMappings(timeStructure?: TimeStructure): Record<string, {
    startTime: string;
    endTime: string;
    duration: number;
    periods: number[];
    timeSlots: string[];
  }> {
    const timeSlots = this.getTimeSlots(timeStructure);

    return {
      'Morning': {
        startTime: '08:30',
        endTime: '11:30',
        duration: 3,
        periods: [1, 2, 3],
        timeSlots: timeSlots.slice(0, 3).map(slot => `${slot.start}-${slot.end}`)
      },
      'Afternoon': {
        startTime: '13:15',
        endTime: '16:00',
        duration: 3,
        periods: [5, 6, 7],
        timeSlots: timeSlots.slice(4, 7).map(slot => `${slot.start}-${slot.end}`)
      },
      'MidSession': {
        startTime: '10:35',
        endTime: '12:25',
        duration: 2,
        periods: [3, 4],
        timeSlots: timeSlots.slice(2, 4).map(slot => `${slot.start}-${slot.end}`)
      },
      'EarlyAfternoon2Period': {
        startTime: '13:15',
        endTime: '15:05',
        duration: 2,
        periods: [5, 6],
        timeSlots: timeSlots.slice(4, 6).map(slot => `${slot.start}-${slot.end}`)
      },
      'Morning2Period': {
        startTime: '08:30',
        endTime: '10:20',
        duration: 2,
        periods: [1, 2],
        timeSlots: timeSlots.slice(0, 2).map(slot => `${slot.start}-${slot.end}`)
      },
      'Morning3Period': {
        startTime: '08:30',
        endTime: '11:30',
        duration: 3,
        periods: [1, 2, 3],
        timeSlots: timeSlots.slice(0, 3).map(slot => `${slot.start}-${slot.end}`)
      },
      'Afternoon2Period': {
        startTime: '13:15',
        endTime: '15:05',
        duration: 2,
        periods: [5, 6],
        timeSlots: timeSlots.slice(4, 6).map(slot => `${slot.start}-${slot.end}`)
      },
      'Afternoon3Period': {
        startTime: '13:15',
        endTime: '16:00',
        duration: 3,
        periods: [5, 6, 7],
        timeSlots: timeSlots.slice(4, 7).map(slot => `${slot.start}-${slot.end}`)
      }
    };
  }

  /**
   * Calculate colspan for timetable display based on time slot info
   */
  static calculateColspan(timeOfDay: string, timeStructure?: TimeStructure): number {
    if (!timeOfDay || typeof timeOfDay !== 'string') {
      console.warn('calculateColspan: timeOfDay is null, undefined, or not a string:', timeOfDay);
      return 3; // Default to 3 for backward compatibility
    }

    const info = this.parseTimeSlotInfo(timeOfDay, timeStructure);
    return info ? info.duration : 3; // Default to 3 for backward compatibility
  }

  /**
   * Get start period for timetable positioning
   */
  static getStartPeriod(timeOfDay: string, timeStructure?: TimeStructure): number {
    const info = this.parseTimeSlotInfo(timeOfDay, timeStructure);
    return info && info.periods.length > 0 ? info.periods[0] : 1; // Default to period 1
  }
}
