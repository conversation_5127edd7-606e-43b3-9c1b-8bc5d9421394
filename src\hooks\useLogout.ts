import { useAuth } from "@/contexts/AuthContext";
import { useStudentAuth } from "@/contexts/StudentAuthContext";
import { useToast } from "@/hooks/use-toast";
import { performLogout, quickLogout, logoutWithConfirmation, emergencyLogout, instantLogout } from "@/utils/logout";

/**
 * Standardized logout hook that handles logout for both faculty/admin and student users
 * Always redirects to the landing page (/) after successful logout
 * OPTIMIZED: Uses fast logout methods for better user experience
 */
export const useLogout = () => {
  const { signOut, user: facultyUser } = useAuth();
  const { logout: studentLogout, student } = useStudentAuth();
  const { toast } = useToast();

  // PERFORMANCE OPTIMIZED: Default logout now uses optimized performLogout
  const logout = async () => {
    try {
      // Use the optimized logout utility (non-blocking Supabase signOut)
      await performLogout();
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: "Logout failed",
        description: "There was an error logging out. Please try again.",
        variant: "destructive",
      });

      // Even if logout fails, redirect to landing page for security
      window.location.href = '/';
    }
  };

  // PERFORMANCE OPTIMIZED: Fast logout for immediate response
  const fastLogout = () => {
    try {
      instantLogout(); // Immediate logout without any async waits
    } catch (error) {
      console.error('Fast logout error:', error);
      // Fallback to emergency logout
      emergencyLogout();
    }
  };

  return {
    logout,
    fastLogout, // NEW: Instant logout option
    quickLogout,
    logoutWithConfirmation,
    emergencyLogout,
    instantLogout, // NEW: Direct access to instant logout
    isLoggedIn: !!(facultyUser || student),
    userType: facultyUser ? 'faculty' : student ? 'student' : null
  };
};
