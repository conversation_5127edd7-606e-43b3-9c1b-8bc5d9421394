import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Activity, Clock, Zap, RefreshCw } from 'lucide-react';

interface PerformanceMetric {
  name: string;
  loadTime: number;
  timestamp: number;
  type: 'page' | 'component' | 'chunk';
}

/**
 * Performance monitoring component for development
 * Shows lazy loading performance metrics
 */
export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;

    // Listen for performance entries
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.entryType === 'navigation' || entry.entryType === 'resource') {
          const metric: PerformanceMetric = {
            name: entry.name.split('/').pop() || entry.name,
            loadTime: entry.duration || 0,
            timestamp: Date.now(),
            type: entry.name.includes('.js') ? 'chunk' : 'page'
          };
          
          setMetrics(prev => [...prev.slice(-9), metric]);
        }
      });
    });

    observer.observe({ entryTypes: ['navigation', 'resource'] });

    return () => observer.disconnect();
  }, []);

  // Don't render in production
  if (process.env.NODE_ENV !== 'development') return null;

  const averageLoadTime = metrics.length > 0 
    ? metrics.reduce((sum, m) => sum + m.loadTime, 0) / metrics.length 
    : 0;

  const getPerformanceBadge = (loadTime: number) => {
    if (loadTime < 100) return <Badge variant="default" className="bg-green-500">Fast</Badge>;
    if (loadTime < 500) return <Badge variant="secondary">Good</Badge>;
    if (loadTime < 1000) return <Badge variant="outline">Slow</Badge>;
    return <Badge variant="destructive">Very Slow</Badge>;
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          variant="outline"
          className="bg-background/80 backdrop-blur-sm"
        >
          <Activity className="h-4 w-4 mr-2" />
          Performance
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96">
      <Card className="bg-background/95 backdrop-blur-sm border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              <CardTitle className="text-lg">Performance Monitor</CardTitle>
            </div>
            <Button
              onClick={() => setIsVisible(false)}
              size="sm"
              variant="ghost"
            >
              ×
            </Button>
          </div>
          <CardDescription>
            Lazy loading performance metrics
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Avg Load Time</p>
                <p className="text-xs text-muted-foreground">
                  {averageLoadTime.toFixed(0)}ms
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Total Chunks</p>
                <p className="text-xs text-muted-foreground">
                  {metrics.filter(m => m.type === 'chunk').length}
                </p>
              </div>
            </div>
          </div>

          {/* Recent Metrics */}
          <div className="space-y-2 max-h-48 overflow-y-auto">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Recent Loads</h4>
              <Button
                onClick={() => setMetrics([])}
                size="sm"
                variant="ghost"
                className="h-6 px-2"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
            </div>
            
            {metrics.slice(-5).reverse().map((metric, index) => (
              <div key={index} className="flex items-center justify-between p-2 rounded-md bg-muted/50">
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium truncate">
                    {metric.name}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {metric.loadTime.toFixed(0)}ms
                  </p>
                </div>
                {getPerformanceBadge(metric.loadTime)}
              </div>
            ))}
            
            {metrics.length === 0 && (
              <p className="text-xs text-muted-foreground text-center py-4">
                No metrics yet. Navigate to see lazy loading performance.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceMonitor;
