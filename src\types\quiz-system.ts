/**
 * TypeScript types for AI-Powered Quiz Management System
 * 
 * These types correspond to the new database tables and provide
 * type safety for the quiz management functionality.
 */

// Enums for better type safety
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type QuizStatus = 'draft' | 'ai_generated' | 'reviewed' | 'published' | 'archived';
export type ReviewStatus = 'pending' | 'approved' | 'rejected' | 'modified';
export type QuestionType = 'multiple_choice' | 'true_false' | 'short_answer' | 'essay';
export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'mixed';
export type ScheduleStatus = 'scheduled' | 'active' | 'completed' | 'cancelled';
export type AttemptStatus = 'in_progress' | 'submitted' | 'auto_submitted' | 'abandoned';
export type MaterialType = 'syllabus' | 'textbook' | 'module' | 'reference' | 'notes' | 'other';

// Course Materials (Enhanced for Multi-Material Support)
export interface CourseMaterial {
  id: string;
  faculty_id: string;
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section?: string;
  academic_year: string;

  // Material categorization
  material_type: MaterialType;
  material_identifier?: string; // e.g., 'Textbook 1', 'Textbook 2', 'MODULE-1'
  material_description?: string;

  // File information
  file_name: string;
  file_path: string;
  file_type: 'pdf' | 'doc' | 'docx' | 'txt';
  file_size_bytes: number;

  // AI processing
  processing_status: ProcessingStatus;
  extracted_text?: string;
  ai_analysis?: any; // Enhanced AI analysis with module mapping
  processing_error?: string;

  // Module and chapter mapping
  module_mappings?: ModuleMapping; // Extracted from syllabus
  chapter_content?: ChapterContent; // For textbooks
  related_materials?: string[]; // IDs of related materials

  // Timestamps
  upload_date: string;
  processed_date?: string;
  created_at: string;
  updated_at: string;
}

// Module mapping structure
export interface ModuleMapping {
  [moduleId: string]: {
    title: string;
    hours: number;
    chapters: string[];
    textbook_references: TextbookReference[];
    learning_objectives: string[];
  };
}

// Chapter content structure for textbooks
export interface ChapterContent {
  [chapterId: string]: {
    title: string;
    content: string;
    page_range?: string;
    sections: string[];
  };
}

// Textbook reference structure
export interface TextbookReference {
  textbook_identifier: string; // e.g., 'Textbook 1'
  chapters: string[];
  sections: string[];
}

// Module weightage for quiz generation
export interface ModuleWeightage {
  [moduleId: string]: number; // Percentage (e.g., {"MODULE-1": 40, "MODULE-2": 60})
}

// Cross-reference analysis between materials
export interface CrossReferenceAnalysis {
  syllabus_textbook_mapping: {
    [moduleId: string]: {
      textbook_sources: string[];
      chapter_coverage: string[];
      content_alignment_score: number;
    };
  };
  content_gaps: string[];
  redundant_content: string[];
  recommended_weightage: ModuleWeightage;
}

export interface CreateCourseMaterialRequest {
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section?: string;
  academic_year: string;
  material_type: MaterialType;
  material_identifier?: string; // e.g., 'Textbook 1', 'MODULE-1'
  material_description?: string;
  file: File;
}

// Quiz Templates
export interface QuizTemplate {
  id: string;
  faculty_id: string;
  
  // Subject information
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section?: string;
  academic_year: string;
  
  // Quiz details
  title: string;
  description?: string;
  instructions?: string;
  duration_minutes: number;
  total_marks: number;
  passing_marks: number;
  
  // AI generation settings
  source_material_ids: string[];
  target_modules: string[]; // e.g., ['MODULE-1', 'MODULE-2']
  module_weightage?: ModuleWeightage; // Percentage distribution across modules
  ai_generation_prompt?: string;
  difficulty_level: DifficultyLevel;
  question_types: QuestionType[];
  cross_reference_analysis?: CrossReferenceAnalysis;
  
  // Status
  status: QuizStatus;
  is_ai_generated: boolean;
  review_notes?: string;
  
  // Timestamps
  created_at: string;
  updated_at: string;
  published_at?: string;
}

export interface CreateQuizTemplateRequest {
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section?: string;
  academic_year: string;
  title: string;
  description?: string;
  instructions?: string;
  duration_minutes: number;
  total_marks: number;
  passing_marks: number;
  source_material_ids: string[];
  difficulty_level: DifficultyLevel;
  question_types: QuestionType[];
}

// Quiz Questions
export interface QuizQuestion {
  id: string;
  quiz_template_id: string;
  
  // Question content
  question_text: string;
  question_type: QuestionType;
  options?: Record<string, string>; // {"A": "Option 1", "B": "Option 2", ...}
  correct_answer: string;
  
  // Metadata
  marks: number;
  difficulty_level: DifficultyLevel;
  topic_tags: string[];
  
  // AI generation info
  is_ai_generated: boolean;
  ai_confidence_score?: number;
  source_material_reference?: string;
  
  // Faculty review
  is_reviewed: boolean;
  review_status: ReviewStatus;
  faculty_modifications?: string;
  
  // Display
  question_order: number;
  
  created_at: string;
  updated_at: string;
}

export interface AIGeneratedQuestion {
  question_text: string;
  question_type: QuestionType;
  options?: Record<string, string>;
  correct_answer: string;
  difficulty_level: DifficultyLevel;
  topic_tags: string[];
  ai_confidence_score: number;
  source_material_reference?: string;
}

// Quiz Schedules
export interface QuizSchedule {
  id: string;
  quiz_template_id: string;
  faculty_id: string;
  
  // Scheduling
  title: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  
  // Target audience
  target_department: string;
  target_semester: string;
  target_section?: string;
  academic_year: string;
  
  // Settings
  max_attempts: number;
  show_results_immediately: boolean;
  randomize_questions: boolean;
  randomize_options: boolean;
  
  // Status
  status: ScheduleStatus;
  notification_sent: boolean;
  reminder_sent: boolean;
  
  created_at: string;
  updated_at: string;
}

export interface CreateQuizScheduleRequest {
  quiz_template_id: string;
  title: string;
  start_time: string;
  end_time: string;
  target_department: string;
  target_semester: string;
  target_section?: string;
  academic_year: string;
  max_attempts?: number;
  show_results_immediately?: boolean;
  randomize_questions?: boolean;
  randomize_options?: boolean;
}

// Student Quiz Attempts
export interface StudentQuizAttempt {
  id: string;
  quiz_schedule_id: string;
  
  // Student info
  student_usn: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  
  // Attempt details
  attempt_number: number;
  start_time: string;
  end_time?: string;
  submission_time?: string;
  
  // Scoring
  total_questions: number;
  attempted_questions: number;
  correct_answers: number;
  score: number;
  percentage: number;
  
  // Status
  status: AttemptStatus;
  time_spent_minutes: number;
  
  created_at: string;
  updated_at: string;
}

// Student Quiz Answers
export interface StudentQuizAnswer {
  id: string;
  attempt_id: string;
  question_id: string;
  
  // Answer details
  student_answer?: string;
  is_correct: boolean;
  marks_awarded: number;
  
  // Timing
  time_spent_seconds: number;
  answered_at: string;
  
  created_at: string;
}

// Analytics and Reporting
export interface QuizAnalytics {
  quiz_title: string;
  total_students: number;
  attempted_students: number;
  average_score: number;
  highest_score: number;
  lowest_score: number;
  completion_rate: number;
}

export interface StudentPerformance {
  student_usn: string;
  student_name: string;
  attempt_count: number;
  best_score: number;
  latest_score: number;
  time_spent_minutes: number;
  submission_status: AttemptStatus;
}

export interface QuestionAnalytics {
  question_id: string;
  question_text: string;
  total_attempts: number;
  correct_attempts: number;
  success_rate: number;
  average_time_spent: number;
  difficulty_perception: DifficultyLevel;
}

// Enhanced AI Service Interfaces for Multi-Material Support
export interface AIQuestionGenerationRequest {
  materials: MaterialContent[]; // Multiple materials instead of single text
  subject_name: string;
  target_modules: string[]; // Specific modules to focus on
  module_weightage?: { [moduleId: string]: number };
  difficulty_level: DifficultyLevel;
  question_types: QuestionType[];
  question_count: number;
  cross_reference_analysis?: CrossReferenceAnalysis;
}

export interface MaterialContent {
  material_id: string;
  material_type: MaterialType;
  material_identifier?: string; // e.g., 'Textbook 1', 'MODULE-1'
  extracted_text: string;
  module_mappings?: ModuleMapping; // Module structure from syllabus
  chapter_content?: ChapterContent; // Chapter breakdown from textbooks
}

export interface AIQuestionGenerationResponse {
  questions: AIGeneratedQuestion[];
  processing_time_ms: number;
  confidence_score: number;
  source_analysis: {
    key_topics: string[];
    content_complexity: DifficultyLevel;
    suggested_question_distribution: Record<QuestionType, number>;
  };
}

// Faculty Dashboard Integration
export interface FacultyQuizDashboard {
  total_quizzes: number;
  active_quizzes: number;
  total_attempts: number;
  average_completion_rate: number;
  recent_quizzes: QuizSchedule[];
  pending_reviews: QuizTemplate[];
  course_materials: CourseMaterial[];
}

// Student Dashboard Integration
export interface StudentQuizDashboard {
  upcoming_quizzes: QuizSchedule[];
  completed_quizzes: StudentQuizAttempt[];
  pending_quizzes: QuizSchedule[];
  overall_performance: {
    total_quizzes_taken: number;
    average_score: number;
    best_score: number;
    total_time_spent: number;
  };
}
