import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { StudentService, StudentFormData } from '@/services/StudentService';
import { Upload, Download, FileSpreadsheet, AlertCircle, CheckCircle, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import * as XLSX from 'xlsx';

interface StudentBulkUploadProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  facultyId?: string;
  isClassTeacher?: boolean;
  assignedClass?: {
    department: string;
    semester: string;
    section: string;
  };
}

const StudentBulkUpload: React.FC<StudentBulkUploadProps> = ({
  isOpen,
  onClose,
  onSuccess,
  facultyId,
  isClassTeacher = false,
  assignedClass
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResults, setUploadResults] = useState<{
    success: any[];
    errors: { row: number; error: string }[];
  } | null>(null);
  const [previewData, setPreviewData] = useState<StudentFormData[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;

    if (!selectedFile.name.match(/\.(xlsx|xls)$/)) {
      toast({
        title: 'Invalid file type',
        description: 'Please select an Excel file (.xlsx or .xls)',
        variant: 'destructive',
      });
      return;
    }

    setFile(selectedFile);

    try {
      const parsedData = await StudentService.parseExcelFile(selectedFile);
      setPreviewData(parsedData.slice(0, 5)); // Show first 5 rows for preview
    } catch (error) {
      toast({
        title: 'Error parsing file',
        description: 'Failed to parse the Excel file. Please check the format.',
        variant: 'destructive',
      });
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      const studentsData = await StudentService.parseExcelFile(file);

      // Validate data for class teacher
      if (isClassTeacher && assignedClass) {
        const invalidRows = studentsData.filter(
          (student, index) =>
            student.department !== assignedClass.department ||
            student.semester !== assignedClass.semester ||
            student.section !== assignedClass.section
        );

        if (invalidRows.length > 0) {
          toast({
            title: 'Invalid data',
            description: `Some students don't match your assigned class (${assignedClass.semester}${assignedClass.section})`,
            variant: 'destructive',
          });
          setUploading(false);
          return;
        }
      }

      setUploadProgress(25);

      let results;
      if (isClassTeacher && facultyId) {
        results = await StudentService.bulkUploadStudentsForClassTeacher(
          studentsData,
          facultyId,
          facultyId // Using facultyId as createdBy
        );
      } else {
        results = await StudentService.bulkUploadStudents(
          studentsData,
          facultyId || 'system' // Fallback to system if no facultyId
        );
      }

      setUploadProgress(100);
      setUploadResults(results);

      if (results.success.length > 0) {
        toast({
          title: 'Upload completed',
          description: `Successfully uploaded ${results.success.length} students${
            results.errors.length > 0 ? ` with ${results.errors.length} errors` : ''
          }`,
        });
        onSuccess();
      }
    } catch (error) {
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload students',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
    }
  };

  const downloadTemplate = () => {
    const templateData = [
      {
        'Roll Number': '1KS25CS001',
        'Student Name': 'John Doe',
        'Department': assignedClass?.department || 'cse',
        'Semester': assignedClass?.semester || '4',
        'Section': assignedClass?.section || 'A',
        'Parent Name': 'Jane Doe',
        'Father Phone': '9876543210',
        'Mother Phone': '9876543211',
        'Student Phone': '9876543212',
        'Parent Email': '<EMAIL>',
        'Student Email': '<EMAIL>'
      }
    ];

    const worksheet = XLSX.utils.json_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Students');
    XLSX.writeFile(workbook, 'student_upload_template.xlsx');
  };

  const resetUpload = () => {
    setFile(null);
    setPreviewData([]);
    setUploadResults(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Bulk Upload Students
          </DialogTitle>
          <DialogDescription>
            Upload multiple students using an Excel file
            {isClassTeacher && assignedClass && (
              <span className="block mt-1 text-sm font-medium text-blue-600">
                Restricted to: {assignedClass.semester}{assignedClass.section} - {assignedClass.department.toUpperCase()}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Download */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <FileSpreadsheet className="h-8 w-8 text-green-600" />
              <div>
                <h3 className="font-medium">Download Template</h3>
                <p className="text-sm text-muted-foreground">
                  Get the Excel template with required columns
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={downloadTemplate}>
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="file-upload">Select Excel File</Label>
            <Input
              id="file-upload"
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileSelect}
              ref={fileInputRef}
              disabled={uploading}
            />
          </div>

          {/* Preview Data */}
          {previewData.length > 0 && (
            <div className="space-y-2">
              <Label>Preview (First 5 rows)</Label>
              <div className="border rounded-lg overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-muted">
                    <tr>
                      <th className="p-2 text-left">Roll Number</th>
                      <th className="p-2 text-left">Student Name</th>
                      <th className="p-2 text-left">Department</th>
                      <th className="p-2 text-left">Semester</th>
                      <th className="p-2 text-left">Section</th>
                      <th className="p-2 text-left">Parent Email</th>
                    </tr>
                  </thead>
                  <tbody>
                    {previewData.map((student, index) => (
                      <tr key={index} className="border-t">
                        <td className="p-2">{student.roll_number}</td>
                        <td className="p-2">{student.student_name}</td>
                        <td className="p-2">{student.department}</td>
                        <td className="p-2">{student.semester}</td>
                        <td className="p-2">{student.section}</td>
                        <td className="p-2">{student.parent_email}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-2">
              <Label>Upload Progress</Label>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {/* Upload Results */}
          {uploadResults && (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Badge variant="default" className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  {uploadResults.success.length} Successful
                </Badge>
                {uploadResults.errors.length > 0 && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <X className="h-3 w-3" />
                    {uploadResults.errors.length} Errors
                  </Badge>
                )}
              </div>

              {uploadResults.errors.length > 0 && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">Upload Errors:</p>
                      {uploadResults.errors.slice(0, 5).map((error, index) => (
                        <p key={index} className="text-sm">
                          Row {error.row}: {error.error}
                        </p>
                      ))}
                      {uploadResults.errors.length > 5 && (
                        <p className="text-sm text-muted-foreground">
                          ... and {uploadResults.errors.length - 5} more errors
                        </p>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={resetUpload} disabled={uploading}>
            Reset
          </Button>
          <Button variant="outline" onClick={onClose} disabled={uploading}>
            Close
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!file || uploading}
            className="min-w-[100px]"
          >
            {uploading ? 'Uploading...' : 'Upload Students'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StudentBulkUpload;
