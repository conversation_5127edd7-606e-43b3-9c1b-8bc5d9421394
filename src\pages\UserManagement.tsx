import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Employee, UserService } from "@/services/UserService";
import ExcelUpload from "@/components/users/ExcelUpload";
import UserForm from "@/components/users/UserForm";
import InlineEditableCell from "@/components/users/InlineEditableCell";
import RoleEditCell from "@/components/users/RoleEditCell";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Edit,
  MoreHorizontal,
  Plus,
  Search,
  Trash2,
  Filter,
  X,
} from "lucide-react";

const UserManagement: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [employeeToDelete, setEmployeeToDelete] = useState<Employee | null>(null);
  const [filters, setFilters] = useState<{
    department: string;
    designation: string;
    role: string;
  }>({ department: "", designation: "", role: "" });
  const [showFilters, setShowFilters] = useState(false);

  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    async function checkUserRole() {
      if (!user?.id) return;

      const { data, error } = await supabase
        .from("employee_details")
        .select("roles")
        .eq("id", user.id)
        .single();

      if (error || !data || !data.roles ||
          (!data.roles.includes("college_admin") && !data.roles.includes("timetable_coordinator"))) {
        toast({
          description: "You don't have permission to view this page.",
          variant: "destructive",
        });
        navigate("/dashboard");
      }
    }

    checkUserRole();
  }, [user, navigate]);

  useEffect(() => {
    fetchEmployees();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [employees, searchQuery, filters]);

  const fetchEmployees = async () => {
    setIsLoading(true);
    try {
      const data = await UserService.fetchEmployees();
      setEmployees(data);
    } catch (error) {
      toast({
        description: "Could not fetch employee data from the server.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let list = [...employees];

    if (searchQuery) {
      const q = searchQuery.toLowerCase();
      list = list.filter(
        (emp) =>
          emp.full_name?.toLowerCase().includes(q) ||
          emp.email?.toLowerCase().includes(q) ||
          emp.phone?.toLowerCase().includes(q) ||
          emp.department?.toLowerCase().includes(q) ||
          emp.designation?.toLowerCase().includes(q)
      );
    }

    if (filters.department) {
      list = list.filter((emp) => emp.department === filters.department);
    }
    if (filters.designation) {
      list = list.filter((emp) => emp.designation === filters.designation);
    }
    if (filters.role) {
      list = list.filter((emp) => emp.roles?.includes(filters.role));
    }

    setFilteredEmployees(list);
  };

  const handleUserFormSubmit = async (data: Employee) => {
    try {
      if (data.id) {
        await UserService.updateEmployee(data);
        setEmployees((prev) =>
          prev.map((emp) => (emp.id === data.id ? data : emp))
        );
        toast({ description: "Employee updated successfully." });
      } else {
        const newEmp = await UserService.addEmployee(data);
        setEmployees((prev) => [...prev, newEmp]);
        toast({ description: "New employee added successfully." });
      }
      setUserDialogOpen(false);
      setEditingEmployee(null);
    } catch {
      toast({
        description: "Failed to save employee data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const openEditDialog = (emp: Employee) => {
    setEditingEmployee(emp);
    setUserDialogOpen(true);
  };

  const openDeleteConfirm = (emp: Employee) => {
    setEmployeeToDelete(emp);
    setDeleteConfirmOpen(true);
  };

  const handleDelete = async () => {
    if (!employeeToDelete) return;
    try {
      await UserService.deleteEmployee(employeeToDelete.id);
      setEmployees((prev) =>
        prev.filter((e) => e.id !== employeeToDelete.id)
      );
      toast({ description: "Employee removed successfully." });
    } catch {
      toast({
        description: "Failed to delete employee. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeleteConfirmOpen(false);
      setEmployeeToDelete(null);
    }
  };

  const handleCellEdit = async (
    id: string,
    field: keyof Employee,
    value: string
  ) => {
    const emp = employees.find((e) => e.id === id);
    if (!emp) return;
    const updated: Employee = { ...emp, [field]: value };
    try {
      await UserService.updateEmployee(updated);
      setEmployees((prev) =>
        prev.map((e) => (e.id === id ? updated : e))
      );
      toast({ description: `${field} updated successfully.` });
    } catch {
      toast({
        description: `Could not update ${field}.`,
        variant: "destructive",
      });
    }
  };

  const handleRoleUpdate = (updated: Employee) => {
    setEmployees((prev) =>
      prev.map((e) => (e.id === updated.id ? updated : e))
    );
    toast({ description: "Roles updated successfully." });
  };

  const getDepartmentName = (code?: string) => {
    switch (code) {
      case "cse":
        return "Computer Science";
      case "it":
        return "Information Technology";
      case "ece":
        return "Electronics & Communication";
      case "eee":
        return "Electrical & Electronics";
      case "mech":
        return "Mechanical Engineering";
      case "civil":
        return "Civil Engineering";
      default:
        return code || "Not assigned";
    }
  };

  const getDesignationName = (code?: string) => {
    switch (code) {
      case "professor":
        return "Professor";
      case "associate_professor":
        return "Associate Professor";
      case "assistant_professor":
        return "Assistant Professor";
      case "lab_assistant":
        return "Lab Assistant";
      case "staff":
        return "Staff";
      default:
        return code || "Not specified";
    }
  };

  const resetFilters = () =>
    setFilters({ department: "", designation: "", role: "" });

  const uniqueDepartments = Array.from(
    new Set(employees.map((e) => e.department).filter(Boolean))
  );
  const uniqueDesignations = Array.from(
    new Set(employees.map((e) => e.designation).filter(Boolean))
  );
  const uniqueRoles = Array.from(
    new Set(employees.flatMap((e) => e.roles || []))
  );

  return (
    <div className="container py-10 max-w-7xl mx-auto">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-muted-foreground mt-1">
            Manage faculty, staff and their details
          </p>
        </div>

        <div className="flex flex-wrap gap-3">
          <ExcelUpload onUploadComplete={fetchEmployees} />
          <Button
            onClick={() => {
              setEditingEmployee(null);
              setUserDialogOpen(true);
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Employee
          </Button>
        </div>
      </div>

      <div className="mb-6 flex flex-col md:flex-row gap-3 items-start md:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name, email, department..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Button
          variant="outline"
          size="icon"
          onClick={() => setShowFilters(!showFilters)}
          className={showFilters ? "bg-secondary" : ""}
        >
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {showFilters && (
        <div className="mb-6 p-4 border rounded-md bg-muted/30 space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-sm font-medium">Filters</h2>
            <Button variant="ghost" size="sm" onClick={resetFilters}>
              <X className="h-3 w-3 mr-1" /> Clear filters
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-1 block">
                Department
              </label>
              <select
                className="w-full h-9 rounded-md border border-input bg-transparent px-3 text-sm"
                value={filters.department}
                onChange={(e) =>
                  setFilters((f) => ({ ...f, department: e.target.value }))
                }
              >
                <option value="">All Departments</option>
                {uniqueDepartments.map((dept) => (
                  <option key={dept} value={dept}>
                    {getDepartmentName(dept)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">
                Designation
              </label>
              <select
                className="w-full h-9 rounded-md border border-input bg-transparent px-3 text-sm"
                value={filters.designation}
                onChange={(e) =>
                  setFilters((f) => ({ ...f, designation: e.target.value }))
                }
              >
                <option value="">All Designations</option>
                {uniqueDesignations.map((desig) => (
                  <option key={desig} value={desig}>
                    {getDesignationName(desig)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Role</label>
              <select
                className="w-full h-9 rounded-md border border-input bg-transparent px-3 text-sm"
                value={filters.role}
                onChange={(e) =>
                  setFilters((f) => ({ ...f, role: e.target.value }))
                }
              >
                <option value="">All Roles</option>
                {uniqueRoles.map((role) => (
                  <option key={role} value={role}>
                    {role.charAt(0).toUpperCase() + role.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">Full Name</TableHead>
              <TableHead className="min-w-[180px]">Email</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Designation</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Roles</TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  Loading employees...
                </TableCell>
              </TableRow>
            ) : filteredEmployees.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No employees found.
                  {searchQuery && " Try changing your search query."}
                  {(filters.department ||
                    filters.designation ||
                    filters.role) &&
                    " Try clearing your filters."}
                </TableCell>
              </TableRow>
            ) : (
              filteredEmployees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell>
                    <InlineEditableCell
                      value={employee.full_name}
                      onEdit={(v) =>
                        handleCellEdit(employee.id, "full_name", v)
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <InlineEditableCell
                      value={employee.email}
                      onEdit={(v) => handleCellEdit(employee.id, "email", v)}
                    />
                  </TableCell>
                  <TableCell>
                    <InlineEditableCell
                      value={employee.department || ""}
                      onEdit={(v) =>
                        handleCellEdit(employee.id, "department", v)
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <InlineEditableCell
                      value={employee.designation || ""}
                      onEdit={(v) =>
                        handleCellEdit(employee.id, "designation", v)
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <InlineEditableCell
                      value={employee.phone || ""}
                      onEdit={(v) => handleCellEdit(employee.id, "phone", v)}
                    />
                  </TableCell>
                  <TableCell>
                    <RoleEditCell
                      employeeId={employee.id}
                      roles={employee.roles || []}
                      onUpdate={handleRoleUpdate}
                    />
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(employee)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => openDeleteConfirm(employee)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Add/Edit Employee Dialog */}
      <Dialog open={userDialogOpen} onOpenChange={setUserDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingEmployee ? "Edit Employee" : "Add New Employee"}
            </DialogTitle>
          </DialogHeader>
          <UserForm
            initialData={editingEmployee || undefined}
            onSubmit={handleUserFormSubmit}
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle>Delete Employee</DialogTitle>
          </DialogHeader>
          <p className="text-muted-foreground">
            Are you sure you want to delete {employeeToDelete?.full_name}? This
            action cannot be undone.
          </p>
          <div className="flex justify-end gap-3 mt-4">
            <Button variant="outline" onClick={() => setDeleteConfirmOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserManagement;
