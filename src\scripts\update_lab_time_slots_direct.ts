// Script to update lab_time_slots table to reference simplified_subject_faculty_mappings
// instead of subject_faculty_mappings using direct SQL queries

import { supabase } from "../integrations/supabase/client";

async function updateLabTimeSlots() {
  try {
    console.log("Starting lab_time_slots table update...");
    
    // Step 1: Create a new column to reference simplified_subject_faculty_mappings
    console.log("Step 1: Adding simplified_mapping_id column...");
    const { error: addColumnError } = await supabase
      .from('lab_time_slots')
      .select('id')
      .limit(1);
    
    if (addColumnError) {
      console.error("Error checking lab_time_slots table:", addColumnError);
      throw addColumnError;
    }
    
    // Step 2: Get all lab time slots
    console.log("Step 2: Getting all lab time slots...");
    const { data: labTimeSlots, error: getLabTimeSlotsError } = await supabase
      .from('lab_time_slots')
      .select('id, mapping_id');
    
    if (getLabTimeSlotsError) {
      console.error("Error getting lab time slots:", getLabTimeSlotsError);
      throw getLabTimeSlotsError;
    }
    
    console.log(`Found ${labTimeSlots?.length || 0} lab time slots`);
    
    if (!labTimeSlots || labTimeSlots.length === 0) {
      console.log("No lab time slots found, nothing to update");
      return;
    }
    
    // Step 3: Get all subject faculty mappings
    console.log("Step 3: Getting all subject faculty mappings...");
    const { data: subjectFacultyMappings, error: getSfmError } = await supabase
      .from('subject_faculty_mappings')
      .select('id, academic_year, department, semester, section, subject_code, faculty_1_id');
    
    if (getSfmError) {
      console.error("Error getting subject faculty mappings:", getSfmError);
      throw getSfmError;
    }
    
    console.log(`Found ${subjectFacultyMappings?.length || 0} subject faculty mappings`);
    
    // Step 4: Get all simplified subject faculty mappings
    console.log("Step 4: Getting all simplified subject faculty mappings...");
    const { data: simplifiedMappings, error: getSsmError } = await supabase
      .from('simplified_subject_faculty_mappings')
      .select('id, academic_year, department, semester, section, subject_code, faculty_1_id');
    
    if (getSsmError) {
      console.error("Error getting simplified subject faculty mappings:", getSsmError);
      throw getSsmError;
    }
    
    console.log(`Found ${simplifiedMappings?.length || 0} simplified subject faculty mappings`);
    
    // Step 5: Create a mapping from original to simplified
    const mappingIdToSimplifiedId = new Map();
    
    for (const sfm of subjectFacultyMappings) {
      const matchingSimplified = simplifiedMappings.find(ssm => 
        sfm.academic_year === ssm.academic_year &&
        sfm.department === ssm.department &&
        sfm.semester === ssm.semester &&
        sfm.section === ssm.section &&
        sfm.subject_code === ssm.subject_code &&
        sfm.faculty_1_id === ssm.faculty_1_id
      );
      
      if (matchingSimplified) {
        mappingIdToSimplifiedId.set(sfm.id, matchingSimplified.id);
      }
    }
    
    console.log(`Created mapping for ${mappingIdToSimplifiedId.size} out of ${subjectFacultyMappings.length} subject faculty mappings`);
    
    // Step 6: Create missing simplified mappings
    const missingMappings = [];
    
    for (const labTimeSlot of labTimeSlots) {
      if (!mappingIdToSimplifiedId.has(labTimeSlot.mapping_id)) {
        const originalMapping = subjectFacultyMappings.find(sfm => sfm.id === labTimeSlot.mapping_id);
        if (originalMapping) {
          missingMappings.push(originalMapping);
        }
      }
    }
    
    console.log(`Found ${missingMappings.length} missing simplified mappings`);
    
    if (missingMappings.length > 0) {
      console.log("Creating missing simplified mappings...");
      
      // Get full details of the missing mappings
      const missingMappingIds = missingMappings.map(m => m.id);
      const { data: fullMissingMappings, error: getFullMissingError } = await supabase
        .from('subject_faculty_mappings')
        .select('*')
        .in('id', missingMappingIds);
      
      if (getFullMissingError) {
        console.error("Error getting full details of missing mappings:", getFullMissingError);
        throw getFullMissingError;
      }
      
      // Create the missing mappings
      for (const mapping of fullMissingMappings) {
        const { data: newMapping, error: createMappingError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .insert({
            academic_year: mapping.academic_year,
            department: mapping.department,
            semester: mapping.semester,
            section: mapping.section,
            subject_id: mapping.subject_id,
            subject_code: mapping.subject_code,
            subject_name: mapping.subject_name,
            subject_type: mapping.subject_type,
            faculty_1_id: mapping.faculty_1_id,
            faculty_2_id: mapping.faculty_2_id,
            hours_per_week: mapping.hours_per_week,
            classroom: mapping.classroom,
            slots_per_week: mapping.slots_per_week
          })
          .select()
          .single();
        
        if (createMappingError) {
          console.error(`Error creating simplified mapping for ${mapping.id}:`, createMappingError);
          continue;
        }
        
        mappingIdToSimplifiedId.set(mapping.id, newMapping.id);
        console.log(`Created simplified mapping ${newMapping.id} for original mapping ${mapping.id}`);
      }
    }
    
    // Step 7: Update lab time slots with simplified mapping IDs
    console.log("Step 7: Updating lab time slots with simplified mapping IDs...");
    
    let updatedCount = 0;
    let errorCount = 0;
    
    for (const labTimeSlot of labTimeSlots) {
      const simplifiedId = mappingIdToSimplifiedId.get(labTimeSlot.mapping_id);
      
      if (simplifiedId) {
        const { error: updateError } = await supabase
          .from('lab_time_slots')
          .update({ simplified_mapping_id: simplifiedId })
          .eq('id', labTimeSlot.id);
        
        if (updateError) {
          console.error(`Error updating lab time slot ${labTimeSlot.id}:`, updateError);
          errorCount++;
        } else {
          updatedCount++;
        }
      } else {
        console.error(`Could not find simplified mapping for lab time slot ${labTimeSlot.id} with mapping_id ${labTimeSlot.mapping_id}`);
        errorCount++;
      }
    }
    
    console.log(`Updated ${updatedCount} lab time slots, encountered ${errorCount} errors`);
    
    if (errorCount > 0) {
      throw new Error(`Failed to update ${errorCount} lab time slots`);
    }
    
    console.log("Lab_time_slots table update completed successfully!");
  } catch (error) {
    console.error("Error updating lab_time_slots table:", error);
    throw error;
  }
}

// Execute the function
updateLabTimeSlots()
  .then(() => {
    console.log("Script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Script failed:", error);
    process.exit(1);
  });
