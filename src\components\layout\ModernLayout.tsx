import React from 'react';
import { cn } from '@/lib/utils';

interface ModernLayoutProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  fullWidth?: boolean;
  noPadding?: boolean;
}

/**
 * Modern Layout Component
 * Provides consistent, responsive layout structure with professional styling
 */
export const ModernLayout: React.FC<ModernLayoutProps> = ({
  children,
  className,
  title,
  description,
  actions,
  fullWidth = false,
  noPadding = false
}) => {
  return (
    <div className={cn(
      "min-h-screen bg-background animate-fade-in",
      !fullWidth && "container-modern",
      !noPadding && "py-responsive",
      className
    )}>
      {(title || description || actions) && (
        <div className="page-header flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div className="space-y-1">
            {title && (
              <h1 className="page-title animate-slide-down">
                {title}
              </h1>
            )}
            {description && (
              <p className="page-description animate-slide-down" style={{ animationDelay: '0.1s' }}>
                {description}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex-shrink-0 animate-slide-down" style={{ animationDelay: '0.2s' }}>
              {actions}
            </div>
          )}
        </div>
      )}
      <div className="animate-slide-up" style={{ animationDelay: '0.3s' }}>
        {children}
      </div>
    </div>
  );
};

interface ModernCardProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  interactive?: boolean;
  elevated?: boolean;
}

/**
 * Modern Card Component
 * Enhanced card with consistent styling and optional interactivity
 */
export const ModernCard: React.FC<ModernCardProps> = ({
  children,
  className,
  title,
  description,
  actions,
  interactive = false,
  elevated = false
}) => {
  return (
    <div className={cn(
      "bg-card text-card-foreground rounded-xl border transition-all duration-300",
      elevated ? "shadow-medium hover:shadow-strong" : "shadow-soft hover:shadow-medium",
      interactive && "hover:scale-[1.02] cursor-pointer",
      className
    )}>
      {(title || description || actions) && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-6 pb-4">
          <div className="space-y-1">
            {title && (
              <h3 className="text-lg font-semibold leading-none tracking-tight text-balance">
                {title}
              </h3>
            )}
            {description && (
              <p className="text-sm text-muted-foreground">
                {description}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex-shrink-0">
              {actions}
            </div>
          )}
        </div>
      )}
      <div className={cn(
        "p-6",
        (title || description || actions) && "pt-0"
      )}>
        {children}
      </div>
    </div>
  );
};

interface ModernGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
  responsive?: boolean;
}

/**
 * Modern Grid Component
 * Responsive grid layout with consistent spacing
 */
export const ModernGrid: React.FC<ModernGridProps> = ({
  children,
  className,
  cols = 1,
  gap = 'md',
  responsive = true
}) => {
  const getGridCols = () => {
    if (!responsive) {
      return `grid-cols-${cols}`;
    }
    
    // Responsive grid classes
    switch (cols) {
      case 1:
        return 'grid-cols-1';
      case 2:
        return 'grid-cols-1 sm:grid-cols-2';
      case 3:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
      case 4:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';
      case 5:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5';
      case 6:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6';
      default:
        return 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3';
    }
  };

  const getGapClass = () => {
    switch (gap) {
      case 'sm':
        return 'gap-4';
      case 'md':
        return 'gap-4 sm:gap-6';
      case 'lg':
        return 'gap-4 sm:gap-6 lg:gap-8';
      default:
        return 'gap-4 sm:gap-6';
    }
  };

  return (
    <div className={cn(
      'grid',
      getGridCols(),
      getGapClass(),
      className
    )}>
      {children}
    </div>
  );
};

interface ModernButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Modern Button Group Component
 * Consistent button grouping with proper spacing
 */
export const ModernButtonGroup: React.FC<ModernButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  size = 'md'
}) => {
  const getSpacing = () => {
    switch (size) {
      case 'sm':
        return orientation === 'horizontal' ? 'space-x-2' : 'space-y-2';
      case 'md':
        return orientation === 'horizontal' ? 'space-x-3' : 'space-y-3';
      case 'lg':
        return orientation === 'horizontal' ? 'space-x-4' : 'space-y-4';
      default:
        return orientation === 'horizontal' ? 'space-x-3' : 'space-y-3';
    }
  };

  return (
    <div className={cn(
      'flex',
      orientation === 'horizontal' ? 'flex-row items-center' : 'flex-col',
      getSpacing(),
      className
    )}>
      {children}
    </div>
  );
};

interface ModernSectionProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  spacing?: 'sm' | 'md' | 'lg';
}

/**
 * Modern Section Component
 * Consistent section spacing and typography
 */
export const ModernSection: React.FC<ModernSectionProps> = ({
  children,
  className,
  title,
  description,
  spacing = 'md'
}) => {
  const getSpacing = () => {
    switch (spacing) {
      case 'sm':
        return 'space-y-4';
      case 'md':
        return 'space-y-6';
      case 'lg':
        return 'space-y-8';
      default:
        return 'space-y-6';
    }
  };

  return (
    <section className={cn(getSpacing(), className)}>
      {(title || description) && (
        <div className="space-y-2">
          {title && (
            <h2 className="text-xl font-semibold tracking-tight">
              {title}
            </h2>
          )}
          {description && (
            <p className="text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </section>
  );
};

export default ModernLayout;
