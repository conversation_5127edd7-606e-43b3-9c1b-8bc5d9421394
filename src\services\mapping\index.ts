
// src/services/mapping/index.ts
export * from "./types";
export * from "./utils";
export * from "./ManualMappingService";
export * from "./ImportMappingService";
export * from "./SimplifiedMappingService";

import { supabase } from "@/integrations/supabase/client";
import { ManualMappingService } from "./ManualMappingService";
import { ImportMappingService } from "./ImportMappingService";
import { SimplifiedMappingService } from "./SimplifiedMappingService";
import { FacultyAvailabilityService } from "@/services/FacultyAvailabilityService";

// Flag to determine which implementation to use
// Set to true to use the simplified table approach
const USE_SIMPLIFIED_TABLE = true;

// MappingService class that combines all functionality for backward compatibility
export class MappingService {
  static async createMapping(mapping: any, slots?: any[]): Promise<{ mappingId: string }> {
    return ManualMappingService.createMapping(mapping, slots);
  }

  static async importExcel(file: File, options: any): Promise<any> {
    return ImportMappingService.importExcel(file, options);
  }

  static async createSubjectMapping(mapping: any): Promise<{ mappingId: string }> {
    if (USE_SIMPLIFIED_TABLE) {
      return SimplifiedMappingService.createMapping(mapping);
    } else {
      return ManualMappingService.createSubjectMapping(mapping);
    }
  }

  static async updateSubjectMapping(id: string, mapping: any): Promise<void> {
    if (USE_SIMPLIFIED_TABLE) {
      return SimplifiedMappingService.updateMapping(id, mapping);
    } else {
      return ManualMappingService.updateSubjectMapping(id, mapping);
    }
  }

  static async deleteSubjectMapping(id: string): Promise<void> {
    if (USE_SIMPLIFIED_TABLE) {
      return SimplifiedMappingService.deleteMapping(id);
    } else {
      return ManualMappingService.deleteMapping(id);
    }
  }

  // Add or update a mapping with lab slots
  static async saveMapping(mapping: any): Promise<any> {
    if (USE_SIMPLIFIED_TABLE) {
      console.log("Using SimplifiedMappingService.saveMapping");
      return SimplifiedMappingService.saveMapping(mapping);
    } else {
      console.error("Using legacy saveMapping method - this should be updated");
      // Legacy implementation - this should be updated
      const payload = {
        academic_year: mapping.academicYear,
        department: mapping.department,
        semester: mapping.semester,
        section: mapping.section,
        subject_id: mapping.subject.id,
        subject_code: mapping.subject.code,
        subject_name: mapping.subject.name,
        subject_type: mapping.subject.type,
        faculty_1_id: mapping.faculty.id,
        faculty_1_name: mapping.faculty.name,
        faculty_2_id: mapping.faculty2Id || null,
        faculty_2_name: mapping.faculty2Name || null,
        hours_per_week: mapping.hoursPerWeek,
        classroom: mapping.classroom,
        slots_per_week: mapping.slotsPerWeek
      };

      let result;
      if (mapping.id) {
        // Update existing mapping
        const { data, error } = await supabase
          .from('subject_faculty_mappings')
          .update(payload)
          .eq('id', mapping.id)
          .select()
          .single();

        if (error) throw error;
        result = data;
      } else {
        // Create new mapping
        const { data, error } = await supabase
          .from('subject_faculty_mappings')
          .insert(payload)
          .select()
          .single();

        if (error) throw error;
        result = data;
      }

      // Now handle lab slots if this is a lab subject
      if (mapping.subject.type === 'laboratory' || mapping.subject.type === 'lab') {
        // First, delete any existing lab slots for this mapping
        if (mapping.id) {
          await supabase
            .from('lab_time_slots')
            .delete()
            .eq('mapping_id', mapping.id);
        }

        // Then insert new lab slots if provided
        if (mapping.labSlots && mapping.labSlots.length > 0) {
          const labSlotPayload = mapping.labSlots.map((slot, index) => ({
            mapping_id: result.id,
            day: slot.day,
            time_of_day: slot.timeOfDay,
            batch_name: slot.batch || `${mapping.section}${index + 1}`,
            slot_order: index + 1 // Add slot_order field
          }));

          const { error: labSlotError } = await supabase
            .from('lab_time_slots')
            .insert(labSlotPayload);

          if (labSlotError) {
            console.error("Error saving lab slots:", labSlotError);
            throw labSlotError;
          }

          // Update faculty availability for each lab slot
          console.log("Updating faculty availability for lab slots");
          for (const slot of mapping.labSlots) {
            try {
              // Call the new function to update faculty availability
              await FacultyAvailabilityService.updateFacultyAvailabilityFromLabSlots(
                result.faculty_1_id,
                result.faculty_2_id,
                slot.day,
                slot.timeOfDay
              );
              console.log(`Updated faculty availability for lab slot on ${slot.day} at ${slot.timeOfDay}`);
            } catch (availabilityError) {
              console.error("Error updating faculty availability:", availabilityError);
              // Continue with the next slot even if this one fails
            }
          }
        }
      }

      return {
        ...mapping,
        id: result.id
      };
    }
  }
}
