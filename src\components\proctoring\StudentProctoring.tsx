import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  UserPlus, 
  GraduationCap,
  Info
} from 'lucide-react';
import ProctorStudentAssignment from './ProctorStudentAssignment';
import MyProctorStudents from './MyProctorStudents';

const StudentProctoring: React.FC = () => {
  const [activeTab, setActiveTab] = useState("my-students");

  return (
    <div className="space-y-6 p-6 pb-16">
      <div className="space-y-0.5">
        <h2 className="text-2xl font-bold tracking-tight">Student Proctoring</h2>
        <p className="text-muted-foreground">
          Manage your proctor student assignments and monitor their academic progress
        </p>
      </div>

      {/* Information Card */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="space-y-2">
              <h3 className="font-medium text-blue-900">About Student Proctoring</h3>
              <p className="text-sm text-blue-800">
                As a faculty proctor, you can assign students to monitor their academic progress, 
                including attendance tracking and internal assessment performance. Use the tabs below 
                to assign new students or view detailed progress cards for your existing proctor students.
              </p>
              <div className="flex flex-wrap gap-2 mt-3">
                <Badge variant="outline" className="text-blue-700 border-blue-300">
                  <GraduationCap className="w-3 h-3 mr-1" />
                  Academic Monitoring
                </Badge>
                <Badge variant="outline" className="text-blue-700 border-blue-300">
                  <Users className="w-3 h-3 mr-1" />
                  Student Assignment
                </Badge>
                <Badge variant="outline" className="text-blue-700 border-blue-300">
                  Progress Tracking
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="my-students" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            My Proctor Students
          </TabsTrigger>
          <TabsTrigger value="assign-students" className="flex items-center gap-2">
            <UserPlus className="w-4 h-4" />
            Assign Students
          </TabsTrigger>
        </TabsList>

        <TabsContent value="my-students" className="space-y-4">
          <MyProctorStudents />
        </TabsContent>

        <TabsContent value="assign-students" className="space-y-4">
          <ProctorStudentAssignment />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StudentProctoring;
