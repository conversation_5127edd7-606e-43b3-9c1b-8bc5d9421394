import { supabase } from "@/integrations/supabase/client";
import { ClassSubstitutionService } from './ClassSubstitutionService';

// Types for Leave Management System
export interface LeaveRequest {
  id: string;
  faculty_id: string;
  leave_type: string;
  start_date: string;
  end_date: string;
  total_days: number;
  reason: string;
  supporting_documents: string[];
  status: 'pending' | 'approved' | 'rejected';
  applied_date: string;
  approved_by?: string;
  approved_date?: string;
  rejection_reason?: string;
  academic_year: string;
  department: string;
  approval_level?: 'pending' | 'hod_approved' | 'principal_approved' | 'auto_approved';
  approver_role?: string;
  approval_hierarchy?: ApprovalStep[];
  created_at: string;
  updated_at: string;
  // Enhanced fields for substitution functionality
  affected_classes?: AffectedClass[];
  substitution_status?: 'pending' | 'assigned' | 'approved' | 'rejected';
  total_classes_affected?: number;
  employee_details?: {
    id: string;
    full_name: string;
    email: string;
    department: string;
    designation: string;
  } | null;
}

export interface AffectedClass {
  id: string;
  day: string;
  time_slot: string;
  period_number: number;
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: string;
  section: string;
  room_number?: string;
  batch_name?: string;
  substitute_faculty_id?: string;
  substitute_faculty_name?: string;
  substitution_notes?: string;
  substitution_status: 'pending' | 'assigned' | 'confirmed';
}

export interface SubstituteFaculty {
  id: string;
  full_name: string;
  department: string;
  designation: string;
  availability_status: 'available' | 'occupied' | 'on_leave';
  conflict_details?: string;
  subject_expertise?: string[];
  // Enhanced fields for lab class prioritization
  is_secondary_faculty?: boolean;
  lab_role?: 'primary' | 'secondary' | 'none';
  priority_level?: 'high' | 'medium' | 'low';
  role_description?: string;
}

export interface ClassImpactAnalysis {
  leave_dates: string[];
  total_classes_affected: number;
  affected_classes: AffectedClass[];
  impact_summary: {
    theory_classes: number;
    lab_classes: number;
    tutorial_classes: number;
    departments_affected: string[];
    semesters_affected: string[];
  };
}

export interface ApprovalStep {
  approver_id: string;
  approver_name: string;
  approver_role: string;
  action: 'approved' | 'rejected';
  timestamp: string;
  rejection_reason?: string;
}

export interface LeaveBalance {
  id: string;
  faculty_id: string;
  leave_type: string;
  total_allocated: number;
  used_days: number;
  remaining_days: number;
  academic_year: string;
  department: string;
}

export interface LeavePolicy {
  id: string;
  leave_type: string;
  leave_name: string;
  default_allocation: number;
  max_consecutive_days?: number;
  requires_approval: boolean;
  requires_documents: boolean;
  description?: string;
  is_active: boolean;
}

export interface LeaveFormData {
  leave_type: string;
  start_date: string;
  end_date: string;
  reason: string;
  supporting_documents?: string[];
  // Enhanced fields for substitution
  affected_classes?: AffectedClass[];
  substitution_assignments?: {
    class_id: string;
    substitute_faculty_id: string;
    notes?: string;
  }[];
}

export interface LeaveStats {
  total_requests: number;
  pending_requests: number;
  approved_requests: number;
  rejected_requests: number;
  total_days_taken: number;
}

export class LeaveManagementService {
  /**
   * Map department names for database queries
   */
  private static mapDepartmentName(userDepartment: string): string {
    const departmentMap: Record<string, string> = {
      'Computer Science and Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Electrical and Electronics Engineering': 'eee'
    };
    
    return departmentMap[userDepartment] || userDepartment.toLowerCase();
  }

  /**
   * Get current academic year
   */
  private static getCurrentAcademicYear(): string {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

    // Academic year typically starts in June/July
    if (currentMonth >= 6) {
      return `${currentYear}-${currentYear + 1}`;
    } else {
      return `${currentYear - 1}-${currentYear}`;
    }
  }

  /**
   * Enrich affected classes data with comprehensive information for HOD decision making
   */
  public static async enrichAffectedClassesData(affectedClasses: AffectedClass[], facultyId?: string): Promise<AffectedClass[]> {
    try {
      console.log('🔍 Enriching affected classes data for HOD review:', affectedClasses.length);
      console.log('🔍 Faculty ID for enrichment:', facultyId);

      // Step 1: Get all unique class IDs to fetch complete timetable data
      const classIds = affectedClasses
        .map(cls => cls.id?.split('-')[0]) // Extract timetable slot ID from composite ID
        .filter(id => id && id !== 'undefined');

      console.log('📋 Extracted timetable slot IDs:', classIds);

      let timetableMap = new Map();
      let subjectMap = new Map();
      let facultyScheduleMap = new Map();

      // Step 1.5: If we have faculty ID, query their actual schedule from timetable_slots
      if (facultyId) {
        console.log('🔍 Querying faculty schedule from timetable_slots for faculty:', facultyId);

        const { data: facultySchedule, error: scheduleError } = await supabase
          .from('timetable_slots')
          .select(`
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            day,
            time_slot,
            room_number,
            batch_name,
            department,
            academic_year
          `)
          .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`);

        if (!scheduleError && facultySchedule) {
          facultySchedule.forEach(schedule => {
            // Create a key based on day and time slot for matching
            const scheduleKey = `${schedule.day}-${schedule.time_slot}`;
            facultyScheduleMap.set(scheduleKey, schedule);
          });
          console.log('✅ Fetched faculty schedule from timetable_slots:', facultySchedule.length, 'entries');
          console.log('📊 Faculty schedule details:', facultySchedule.map(s => ({
            subject_code: s.subject_code,
            subject_name: s.subject_name,
            day: s.day,
            time_slot: s.time_slot,
            semester: s.semester,
            section: s.section
          })));
        } else {
          console.error('❌ Error fetching faculty schedule:', scheduleError);
        }

        // Step 1.6: Also get faculty's subject assignments from simplified_subject_faculty_mappings for additional context
        console.log('🔍 Querying faculty subject assignments from simplified_subject_faculty_mappings');

        const { data: facultySubjects, error: subjectsError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            department,
            hours_per_week
          `)
          .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`);

        if (!subjectsError && facultySubjects) {
          facultySubjects.forEach(subject => {
            // Store by subject code for later lookup
            subjectMap.set(subject.subject_code, {
              ...subject,
              source: 'faculty_assignments'
            });
          });
          console.log('✅ Fetched faculty subject assignments:', facultySubjects.length, 'subjects');
          console.log('📊 Faculty subjects:', facultySubjects.map(s => ({
            subject_code: s.subject_code,
            subject_name: s.subject_name,
            semester: s.semester,
            section: s.section
          })));
        }
      }

      // Step 2: Fetch complete timetable data with subject information
      if (classIds.length > 0) {
        const { data: timetableData, error: timetableError } = await supabase
          .from('timetable_slots')
          .select(`
            id,
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            day,
            time_slot,
            room_number,
            batch_name,
            department,
            academic_year
          `)
          .in('id', classIds);

        if (!timetableError && timetableData) {
          timetableData.forEach(slot => {
            timetableMap.set(slot.id.toString(), slot);
          });
          console.log('✅ Fetched timetable data for', timetableData.length, 'slots');
          console.log('📊 Timetable data details:', timetableData.map(slot => ({
            id: slot.id,
            subject_code: slot.subject_code,
            subject_name: slot.subject_name,
            subject_type: slot.subject_type
          })));
        } else {
          console.error('❌ Error fetching timetable data:', timetableError);
        }

        // Step 3: Fetch subject details from multiple sources for comprehensive coverage
        const subjectCodes = [...new Set([
          ...(timetableData?.map(slot => slot.subject_code).filter(Boolean) || []),
          ...(affectedClasses.map(cls => cls.subject_code).filter(code =>
            code &&
            code !== 'UNKNOWN' &&
            code !== 'N/A' &&
            code !== 'COURSE-TBD' &&
            code.trim() !== ''
          ) || [])
        ])];

        console.log('🔍 Searching for subject information for codes:', subjectCodes);
        console.log('🔍 Original affected classes data:', affectedClasses.map(cls => ({
          id: cls.id,
          subject_code: cls.subject_code,
          subject_name: cls.subject_name,
          subject_type: cls.subject_type
        })));

        if (subjectCodes.length > 0) {
          // Source 1: simplified_subject_faculty_mappings (PRIMARY - most current and comprehensive)
          const { data: simplifiedMappingData, error: simplifiedMappingError } = await supabase
            .from('simplified_subject_faculty_mappings')
            .select('subject_code, subject_name, subject_type, hours_per_week, semester, section, department')
            .in('subject_code', subjectCodes);

          if (!simplifiedMappingError && simplifiedMappingData) {
            simplifiedMappingData.forEach(subject => {
              subjectMap.set(subject.subject_code, {
                ...subject,
                source: 'simplified_subject_faculty_mappings'
              });
            });
            console.log('✅ Fetched subject details from simplified mappings for', simplifiedMappingData.length, 'subjects');
          }

          // Source 2: subject_faculty_mappings (fallback for older data)
          const unmappedAfterSimplified = subjectCodes.filter(code => !subjectMap.has(code));
          if (unmappedAfterSimplified.length > 0) {
            const { data: mappingData, error: mappingError } = await supabase
              .from('subject_faculty_mappings')
              .select('subject_code, subject_name, subject_type, hours_per_week')
              .in('subject_code', unmappedAfterSimplified);

            if (!mappingError && mappingData) {
              mappingData.forEach(subject => {
                subjectMap.set(subject.subject_code, {
                  ...subject,
                  source: 'subject_faculty_mappings'
                });
              });
              console.log('✅ Fetched additional subject details from legacy mappings for', mappingData.length, 'subjects');
            }
          }

          // Source 3: subjects table (fallback for basic subject info)
          const unmappedAfterMappings = subjectCodes.filter(code => !subjectMap.has(code));
          if (unmappedAfterMappings.length > 0) {
            const { data: subjectsData, error: subjectsError } = await supabase
              .from('subjects')
              .select('subject_code, subject_name, subject_type, credits')
              .in('subject_code', unmappedAfterMappings);

            if (!subjectsError && subjectsData) {
              subjectsData.forEach(subject => {
                subjectMap.set(subject.subject_code, {
                  ...subject,
                  source: 'subjects'
                });
              });
              console.log('✅ Fetched additional subject details from subjects table for', subjectsData.length, 'subjects');
            }
          }

          // Source 4: Pattern matching for partial codes (last resort)
          const finalUnmappedCodes = subjectCodes.filter(code => !subjectMap.has(code));
          if (finalUnmappedCodes.length > 0) {
            console.log('🔍 Attempting pattern matching for remaining unmapped codes:', finalUnmappedCodes);

            for (const code of finalUnmappedCodes) {
              // Try pattern matching in simplified mappings first
              const { data: patternData, error: patternError } = await supabase
                .from('simplified_subject_faculty_mappings')
                .select('subject_code, subject_name, subject_type')
                .ilike('subject_code', `%${code.substring(0, 3)}%`)
                .limit(1);

              if (!patternError && patternData && patternData.length > 0) {
                subjectMap.set(code, {
                  subject_code: code,
                  subject_name: `${patternData[0].subject_name} (Similar to ${patternData[0].subject_code})`,
                  subject_type: patternData[0].subject_type,
                  source: 'pattern_match_simplified'
                });
                console.log(`✅ Found pattern match for ${code}: ${patternData[0].subject_name}`);
              } else {
                // Fallback to legacy mappings for pattern matching
                const { data: legacyPatternData, error: legacyPatternError } = await supabase
                  .from('subject_faculty_mappings')
                  .select('subject_code, subject_name, subject_type')
                  .ilike('subject_code', `%${code.substring(0, 3)}%`)
                  .limit(1);

                if (!legacyPatternError && legacyPatternData && legacyPatternData.length > 0) {
                  subjectMap.set(code, {
                    subject_code: code,
                    subject_name: `${legacyPatternData[0].subject_name} (Similar to ${legacyPatternData[0].subject_code})`,
                    subject_type: legacyPatternData[0].subject_type,
                    source: 'pattern_match_legacy'
                  });
                  console.log(`✅ Found legacy pattern match for ${code}: ${legacyPatternData[0].subject_name}`);
                }
              }
            }
          }
        }
      }

      // Step 4: Get substitute faculty details with expertise
      const substituteIds = affectedClasses
        .filter(cls => cls.substitute_faculty_id)
        .map(cls => cls.substitute_faculty_id!)
        .filter((id, index, arr) => arr.indexOf(id) === index);

      let facultyMap = new Map();
      let facultyExpertiseMap = new Map();

      if (substituteIds.length > 0) {
        // Get faculty basic details
        const { data: facultyData, error: facultyError } = await supabase
          .from('employee_details')
          .select('id, full_name, department, designation, email')
          .in('id', substituteIds);

        if (!facultyError && facultyData) {
          facultyData.forEach(faculty => {
            facultyMap.set(faculty.id, faculty);
          });
        }

        // Get faculty subject expertise from simplified_subject_faculty_mappings (PRIMARY)
        const { data: simplifiedExpertiseData, error: simplifiedExpertiseError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select('faculty_1_id, faculty_2_id, subject_code, subject_name, subject_type, semester, section')
          .or(substituteIds.map(id => `faculty_1_id.eq.${id},faculty_2_id.eq.${id}`).join(','));

        if (!simplifiedExpertiseError && simplifiedExpertiseData) {
          simplifiedExpertiseData.forEach(mapping => {
            // Handle both faculty_1_id and faculty_2_id
            const facultyId = substituteIds.includes(mapping.faculty_1_id) ? mapping.faculty_1_id : mapping.faculty_2_id;
            if (facultyId && substituteIds.includes(facultyId)) {
              if (!facultyExpertiseMap.has(facultyId)) {
                facultyExpertiseMap.set(facultyId, []);
              }
              facultyExpertiseMap.get(facultyId).push({
                subject_code: mapping.subject_code,
                subject_name: mapping.subject_name,
                subject_type: mapping.subject_type,
                semester: mapping.semester,
                section: mapping.section,
                source: 'simplified_mappings'
              });
            }
          });
          console.log('✅ Fetched substitute faculty expertise from simplified mappings');
        }

        // Fallback: Get faculty subject expertise from legacy subject_faculty_mappings
        const { data: expertiseData, error: expertiseError } = await supabase
          .from('subject_faculty_mappings')
          .select('faculty_1_id, subject_code, subject_name, subject_type')
          .in('faculty_1_id', substituteIds);

        if (!expertiseError && expertiseData) {
          expertiseData.forEach(mapping => {
            if (!facultyExpertiseMap.has(mapping.faculty_1_id)) {
              facultyExpertiseMap.set(mapping.faculty_1_id, []);
            }
            // Only add if not already present from simplified mappings
            const existing = facultyExpertiseMap.get(mapping.faculty_1_id);
            const alreadyExists = existing.some(exp => exp.subject_code === mapping.subject_code);
            if (!alreadyExists) {
              facultyExpertiseMap.get(mapping.faculty_1_id).push({
                subject_code: mapping.subject_code,
                subject_name: mapping.subject_name,
                subject_type: mapping.subject_type,
                source: 'legacy_mappings'
              });
            }
          });
          console.log('✅ Fetched additional substitute faculty expertise from legacy mappings');
        }
      }

      // Step 5: Enrich each affected class with comprehensive data and professional fallbacks
      const enrichedClasses = await Promise.all(affectedClasses.map(async cls => {
        const slotId = cls.id?.split('-')[0];
        const timetableData = slotId ? timetableMap.get(slotId) : null;
        const subjectData = cls.subject_code ? subjectMap.get(cls.subject_code) : null;
        const substituteFaculty = cls.substitute_faculty_id ? facultyMap.get(cls.substitute_faculty_id) : null;
        const substituteExpertise = cls.substitute_faculty_id ? facultyExpertiseMap.get(cls.substitute_faculty_id) || [] : [];

        // NEW: Try to find the actual scheduled subject using faculty schedule and class timing
        let facultyScheduleData = null;
        if (cls.day && cls.time_slot && cls.day !== 'Schedule TBD' && cls.time_slot !== 'Time TBD') {
          const scheduleKey = `${cls.day}-${cls.time_slot}`;
          facultyScheduleData = facultyScheduleMap.get(scheduleKey);
          console.log(`🔍 Looking for faculty schedule with key: ${scheduleKey}`, facultyScheduleData);
        } else {
          console.log(`🔍 Class has placeholder schedule data: ${cls.day}-${cls.time_slot}, trying alternative approach`);

          // If we don't have real schedule data, try to use any subject from faculty's assignments
          // This is better than showing COURSE-TBD
          const facultySubjectCodes = Array.from(subjectMap.keys()).filter(code => {
            const subject = subjectMap.get(code);
            return subject?.source === 'faculty_assignments';
          });

          if (facultySubjectCodes.length > 0) {
            // Use the first subject assignment as a reasonable fallback
            const fallbackSubjectCode = facultySubjectCodes[0];
            const fallbackSubject = subjectMap.get(fallbackSubjectCode);
            facultyScheduleData = {
              subject_code: fallbackSubject.subject_code,
              subject_name: fallbackSubject.subject_name,
              subject_type: fallbackSubject.subject_type,
              semester: fallbackSubject.semester,
              section: fallbackSubject.section,
              day: cls.day,
              time_slot: cls.time_slot
            };
            console.log(`✅ Using faculty subject assignment as fallback:`, fallbackSubject);
          }
        }

        // Comprehensive subject information resolution with NEW faculty schedule priority
        let enrichedSubjectCode = facultyScheduleData?.subject_code || cls.subject_code || timetableData?.subject_code || subjectData?.subject_code;
        let enrichedSubjectName = facultyScheduleData?.subject_name || cls.subject_name || timetableData?.subject_name || subjectData?.subject_name;
        let enrichedSubjectType = facultyScheduleData?.subject_type || cls.subject_type || timetableData?.subject_type || subjectData?.subject_type || 'theory';

        // Log the data sources for debugging
        console.log(`📊 Subject data for class ${cls.id} (${cls.day} ${cls.time_slot}):`, {
          fromClass: { code: cls.subject_code, name: cls.subject_name, type: cls.subject_type },
          fromTimetable: { code: timetableData?.subject_code, name: timetableData?.subject_name, type: timetableData?.subject_type },
          fromSubjectMap: { code: subjectData?.subject_code, name: subjectData?.subject_name, type: subjectData?.subject_type, source: subjectData?.source },
          fromFacultySchedule: { code: facultyScheduleData?.subject_code, name: facultyScheduleData?.subject_name, type: facultyScheduleData?.subject_type, day: facultyScheduleData?.day, time: facultyScheduleData?.time_slot },
          final: { code: enrichedSubjectCode, name: enrichedSubjectName, type: enrichedSubjectType }
        });

        // If we still don't have subject information, try to get it from the timetable slot directly
        if ((!enrichedSubjectCode || !enrichedSubjectName) && slotId) {
          console.log(`🔍 Attempting direct timetable lookup for slot ${slotId}`);
          try {
            const { data: directSlotData, error: directSlotError } = await supabase
              .from('timetable_slots')
              .select('subject_code, subject_name, subject_type')
              .eq('id', slotId)
              .single();

            if (!directSlotError && directSlotData) {
              enrichedSubjectCode = enrichedSubjectCode || directSlotData.subject_code;
              enrichedSubjectName = enrichedSubjectName || directSlotData.subject_name;
              enrichedSubjectType = enrichedSubjectType || directSlotData.subject_type;
              console.log(`✅ Direct slot lookup successful:`, directSlotData);
            }
          } catch (error) {
            console.log(`❌ Direct slot lookup failed for ${slotId}:`, error);
          }
        }

        // Professional fallbacks for missing subject information
        if (!enrichedSubjectCode || enrichedSubjectCode === 'UNKNOWN' || enrichedSubjectCode === 'N/A') {
          enrichedSubjectCode = 'COURSE-TBD';
        }

        if (!enrichedSubjectName || enrichedSubjectName === 'Unknown Subject' || enrichedSubjectName === 'N/A') {
          if (enrichedSubjectCode && enrichedSubjectCode !== 'COURSE-TBD') {
            enrichedSubjectName = `Course ${enrichedSubjectCode}`;
          } else {
            // Try to infer from context
            const semester = cls.semester || timetableData?.semester;
            const section = cls.section || timetableData?.section;
            if (semester && section) {
              enrichedSubjectName = `${enrichedSubjectType.charAt(0).toUpperCase() + enrichedSubjectType.slice(1)} Course - Sem ${semester}${section}`;
            } else {
              enrichedSubjectName = `${enrichedSubjectType.charAt(0).toUpperCase() + enrichedSubjectType.slice(1)} Course`;
            }
          }
        }

        // Comprehensive class information resolution with faculty schedule priority
        let enrichedSemester = facultyScheduleData?.semester || cls.semester || timetableData?.semester;
        let enrichedSection = facultyScheduleData?.section || cls.section || timetableData?.section;
        let enrichedDay = facultyScheduleData?.day || cls.day || timetableData?.day;
        let enrichedTimeSlot = facultyScheduleData?.time_slot || cls.time_slot || timetableData?.time_slot;

        // Professional fallbacks for missing class information
        if (!enrichedSemester || enrichedSemester === 'Unknown' || enrichedSemester === 'N/A') {
          enrichedSemester = 'TBD';
        }
        if (!enrichedSection || enrichedSection === 'Unknown' || enrichedSection === 'N/A') {
          enrichedSection = 'TBD';
        }
        if (!enrichedDay || enrichedDay === 'Unknown' || enrichedDay === 'N/A') {
          enrichedDay = 'Schedule TBD';
        }
        if (!enrichedTimeSlot || enrichedTimeSlot === 'Unknown' || enrichedTimeSlot === 'N/A') {
          enrichedTimeSlot = 'Time TBD';
        }

        // Room information with faculty schedule priority
        const enrichedRoom = facultyScheduleData?.room_number || cls.room_number || timetableData?.room_number || 'Room TBD';
        const enrichedBatchName = facultyScheduleData?.batch_name || cls.batch_name || timetableData?.batch_name;

        // Data quality assessment
        const subjectVerified = !!(
          (timetableData?.subject_name && timetableData.subject_name !== 'Unknown Subject') ||
          (subjectData?.subject_name && subjectData.subject_name !== 'Unknown Subject')
        );
        const scheduleVerified = !!(
          timetableData?.day && timetableData.day !== 'Unknown' &&
          timetableData?.time_slot && timetableData.time_slot !== 'Unknown'
        );

        return {
          ...cls,
          // Enhanced subject information with professional fallbacks
          subject_code: enrichedSubjectCode,
          subject_name: enrichedSubjectName,
          subject_type: enrichedSubjectType,

          // Enhanced class information with professional fallbacks
          semester: enrichedSemester,
          section: enrichedSection,
          day: enrichedDay,
          time_slot: enrichedTimeSlot,
          room_number: enrichedRoom,
          batch_name: enrichedBatchName,

          // Enhanced substitute information with actual subject details
          substitute_faculty_name: cls.substitute_faculty_name || substituteFaculty?.full_name,
          substitute_faculty_designation: substituteFaculty?.designation,
          substitute_faculty_department: substituteFaculty?.department,
          substitute_faculty_expertise: substituteExpertise,

          // Enhanced substitute subject handling information
          substitute_subject_info: substituteExpertise.length > 0 ? {
            primary_subject: substituteExpertise[0], // Use first subject as primary
            all_subjects: substituteExpertise,
            can_handle_subject: substituteExpertise.some(exp =>
              exp.subject_code === enrichedSubjectCode ||
              exp.subject_type === enrichedSubjectType
            ),
            subject_match_details: substituteExpertise.find(exp =>
              exp.subject_code === enrichedSubjectCode
            ) || substituteExpertise.find(exp =>
              exp.subject_type === enrichedSubjectType
            ) || substituteExpertise[0]
          } : null,

          // Enhanced status
          substitution_status: cls.substitute_faculty_id ? 'assigned' : 'pending',

          // Enhanced metadata for HOD decision making
          data_quality: {
            subject_verified: subjectVerified,
            schedule_verified: scheduleVerified,
            substitute_qualified: substituteExpertise.some(exp =>
              exp.subject_code === enrichedSubjectCode ||
              exp.subject_type === enrichedSubjectType
            ),
            completeness_score: [
              subjectVerified ? 25 : 0,
              scheduleVerified ? 25 : 0,
              enrichedSubjectCode !== 'COURSE-TBD' ? 25 : 0,
              enrichedSemester !== 'TBD' && enrichedSection !== 'TBD' ? 25 : 0
            ].reduce((a, b) => a + b, 0)
          },

          // Source information for debugging
          data_sources: {
            subject_source: subjectData?.source || (timetableData?.subject_name ? 'timetable' : 'fallback'),
            timetable_available: !!timetableData,
            subject_mapping_available: !!subjectData
          }
        };
      }));

      console.log('✅ Enrichment completed for', enrichedClasses.length, 'classes');
      return enrichedClasses;

    } catch (error) {
      console.error('❌ Error enriching affected classes data:', error);

      // Fallback: Return classes with basic enrichment
      return affectedClasses.map(cls => ({
        ...cls,
        subject_code: cls.subject_code || 'UNKNOWN',
        subject_name: cls.subject_name || 'Subject information unavailable',
        subject_type: cls.subject_type || 'theory',
        semester: cls.semester || 'Unknown',
        section: cls.section || 'Unknown',
        day: cls.day || 'Unknown',
        time_slot: cls.time_slot || 'Unknown',
        substitution_status: cls.substitute_faculty_id ? 'assigned' : 'pending',
        data_quality: {
          subject_verified: false,
          schedule_verified: false,
          substitute_qualified: false
        }
      }));
    }
  }

  /**
   * Calculate number of days between two dates (excluding weekends)
   */
  private static calculateLeaveDays(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let totalDays = 0;
    
    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      // Count only weekdays (Monday = 1, Friday = 5)
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        totalDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return totalDays;
  }

  /**
   * Get all leave policies
   */
  static async getLeavePolicies(): Promise<LeavePolicy[]> {
    try {
      const { data, error } = await supabase
        .from('leave_policies')
        .select('*')
        .eq('is_active', true)
        .order('leave_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching leave policies:', error);
      throw error;
    }
  }

  /**
   * Get leave balances for a faculty member
   */
  static async getLeaveBalances(
    facultyId: string,
    academicYear?: string
  ): Promise<LeaveBalance[]> {
    try {
      const year = academicYear || this.getCurrentAcademicYear();
      
      const { data, error } = await supabase
        .from('leave_balances')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('academic_year', year)
        .order('leave_type');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching leave balances:', error);
      throw error;
    }
  }

  /**
   * Initialize leave balances for a faculty member (called when first accessing the system)
   */
  static async initializeLeaveBalances(
    facultyId: string,
    department: string,
    academicYear?: string
  ): Promise<void> {
    try {
      const year = academicYear || this.getCurrentAcademicYear();
      // Use full department name for consistency
      const departmentToStore = department;

      console.log('🔍 Leave Balance Initialization:', {
        facultyId,
        originalDept: department,
        departmentToStore,
        year
      });

      // Get all leave policies
      const policies = await this.getLeavePolicies();

      // Check existing balances
      const existingBalances = await this.getLeaveBalances(facultyId, year);
      const existingTypes = existingBalances.map(b => b.leave_type);

      // Create balances for missing leave types
      const balancesToCreate = policies
        .filter(policy => !existingTypes.includes(policy.leave_type))
        .map(policy => ({
          faculty_id: facultyId,
          leave_type: policy.leave_type,
          total_allocated: policy.default_allocation,
          used_days: 0,
          remaining_days: policy.default_allocation,
          academic_year: year,
          department: departmentToStore
        }));

      if (balancesToCreate.length > 0) {
        const { error } = await supabase
          .from('leave_balances')
          .insert(balancesToCreate);

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error initializing leave balances:', error);
      throw error;
    }
  }

  /**
   * Submit a leave request
   */
  static async submitLeaveRequest(
    facultyId: string,
    department: string,
    formData: LeaveFormData
  ): Promise<LeaveRequest> {
    try {
      const academicYear = this.getCurrentAcademicYear();
      // Store the full department name instead of mapped version for consistency
      const departmentToStore = department; // Use full name like 'Computer Science and Engineering'

      console.log('🔍 Leave Request Submission:', {
        originalDept: department,
        departmentToStore,
        academicYear
      });

      // Calculate total days
      const totalDays = this.calculateLeaveDays(formData.start_date, formData.end_date);
      
      // Validate leave balance
      const balances = await this.getLeaveBalances(facultyId, academicYear);
      const leaveBalance = balances.find(b => b.leave_type === formData.leave_type);
      
      if (!leaveBalance) {
        throw new Error('Leave balance not found. Please contact administrator.');
      }
      
      if (leaveBalance.remaining_days < totalDays) {
        throw new Error(`Insufficient leave balance. Available: ${leaveBalance.remaining_days} days, Requested: ${totalDays} days`);
      }

      // Create leave request
      const leaveRequest = {
        faculty_id: facultyId,
        leave_type: formData.leave_type,
        start_date: formData.start_date,
        end_date: formData.end_date,
        total_days: totalDays,
        reason: formData.reason,
        supporting_documents: formData.supporting_documents || [],
        academic_year: academicYear,
        department: departmentToStore, // Use full department name
        status: 'pending' as const
      };

      const { data, error } = await supabase
        .from('leave_requests')
        .insert(leaveRequest)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error submitting leave request:', error);
      throw error;
    }
  }

  /**
   * Submit enhanced leave request with class impact analysis
   */
  static async submitEnhancedLeaveRequest(
    facultyId: string,
    department: string,
    formData: LeaveFormData
  ): Promise<{ leaveRequest: LeaveRequest; classImpact: ClassImpactAnalysis }> {
    try {
      console.log('🚀 Submitting enhanced leave request with class impact analysis');

      // Generate leave dates array
      const leaveDates = this.generateDateRange(formData.start_date, formData.end_date);

      // Analyze class impact
      const classImpact = await ClassSubstitutionService.analyzeClassImpact(
        facultyId,
        leaveDates
      );

      // Update form data with class impact
      const enhancedFormData: LeaveFormData = {
        ...formData,
        affected_classes: classImpact.affected_classes
      };

      // Submit the leave request with enhanced data
      const academicYear = this.getCurrentAcademicYear();
      const departmentToStore = department;

      // Calculate total days
      const totalDays = this.calculateLeaveDays(formData.start_date, formData.end_date);

      // Validate leave balance
      const balances = await this.getLeaveBalances(facultyId, academicYear);
      const leaveBalance = balances.find(b => b.leave_type === formData.leave_type);

      if (!leaveBalance) {
        throw new Error('Leave balance not found. Please contact administrator.');
      }

      if (leaveBalance.remaining_days < totalDays) {
        throw new Error(`Insufficient leave balance. Available: ${leaveBalance.remaining_days} days, Requested: ${totalDays} days`);
      }

      // Create enhanced leave request
      const leaveRequest = {
        faculty_id: facultyId,
        leave_type: formData.leave_type,
        start_date: formData.start_date,
        end_date: formData.end_date,
        total_days: totalDays,
        reason: formData.reason,
        supporting_documents: formData.supporting_documents || [],
        academic_year: academicYear,
        department: departmentToStore,
        status: 'pending' as const,
        // Enhanced fields for substitution
        affected_classes: classImpact.affected_classes,
        substitution_status: formData.substitution_assignments ? 'assigned' : 'pending',
        total_classes_affected: classImpact.total_classes_affected
      };

      const { data, error } = await supabase
        .from('leave_requests')
        .insert(leaveRequest)
        .select()
        .single();

      if (error) throw error;

      // Save substitution assignments if provided
      if (data && formData.substitution_assignments && formData.substitution_assignments.length > 0) {
        await ClassSubstitutionService.saveSubstitutionAssignments(
          data.id,
          formData.substitution_assignments
        );
      }

      console.log('✅ Enhanced leave request submitted successfully');
      return { leaveRequest: data, classImpact };

    } catch (error) {
      console.error('Error submitting enhanced leave request:', error);
      throw error;
    }
  }

  /**
   * Generate array of dates between start and end date
   */
  private static generateDateRange(startDate: string, endDate: string): string[] {
    const dates: string[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      dates.push(date.toISOString().split('T')[0]);
    }

    return dates;
  }

  /**
   * Get leave requests for a faculty member
   */
  static async getLeaveRequests(
    facultyId: string,
    filters?: {
      status?: string;
      leave_type?: string;
      start_date?: string;
      end_date?: string;
      academic_year?: string;
    }
  ): Promise<LeaveRequest[]> {
    try {
      let query = supabase
        .from('leave_requests')
        .select('*')
        .eq('faculty_id', facultyId)
        .order('applied_date', { ascending: false });

      // Apply filters
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      if (filters?.leave_type) {
        query = query.eq('leave_type', filters.leave_type);
      }
      if (filters?.academic_year) {
        query = query.eq('academic_year', filters.academic_year);
      }
      if (filters?.start_date) {
        query = query.gte('start_date', filters.start_date);
      }
      if (filters?.end_date) {
        query = query.lte('end_date', filters.end_date);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching leave requests:', error);
      throw error;
    }
  }

  /**
   * Get leave statistics for a faculty member
   */
  static async getLeaveStats(
    facultyId: string,
    academicYear?: string
  ): Promise<LeaveStats> {
    try {
      const year = academicYear || this.getCurrentAcademicYear();
      
      const requests = await this.getLeaveRequests(facultyId, { academic_year: year });
      
      const stats: LeaveStats = {
        total_requests: requests.length,
        pending_requests: requests.filter(r => r.status === 'pending').length,
        approved_requests: requests.filter(r => r.status === 'approved').length,
        rejected_requests: requests.filter(r => r.status === 'rejected').length,
        total_days_taken: requests
          .filter(r => r.status === 'approved')
          .reduce((sum, r) => sum + r.total_days, 0)
      };

      return stats;
    } catch (error) {
      console.error('Error fetching leave statistics:', error);
      throw error;
    }
  }

  /**
   * Cancel a pending leave request
   */
  static async cancelLeaveRequest(requestId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('leave_requests')
        .delete()
        .eq('id', requestId)
        .eq('status', 'pending'); // Only allow canceling pending requests

      if (error) throw error;
    } catch (error) {
      console.error('Error canceling leave request:', error);
      throw error;
    }
  }

  /**
   * Get upcoming approved leaves for a faculty member
   */
  static async getUpcomingLeaves(facultyId: string): Promise<LeaveRequest[]> {
    try {
      const today = new Date().toISOString().split('T')[0];

      const { data, error } = await supabase
        .from('leave_requests')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('status', 'approved')
        .gte('start_date', today)
        .order('start_date')
        .limit(5);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching upcoming leaves:', error);
      throw error;
    }
  }

  /**
   * Get pending leave requests for HOD approval (department-specific)
   * SIMPLE APPROACH: Just get requests from same department faculty
   */
  static async getPendingRequestsForHOD(
    hodId: string,
    department: string,
    filters?: {
      leave_type?: string;
      start_date?: string;
      end_date?: string;
      faculty_name?: string;
    }
  ): Promise<LeaveRequest[]> {
    try {
      console.log('🔍 SIMPLE HOD Query:', { hodId, department });

      // Step 1: First, let's see ALL users in the database
      const { data: allUsers, error: allUsersError } = await supabase
        .from('employee_details')
        .select('id, full_name, email, department, designation, roles');

      console.log('🔍 ALL USERS in database:', {
        error: allUsersError,
        totalUsers: allUsers?.length || 0,
        users: allUsers?.map(u => ({
          id: u.id,
          name: u.full_name,
          department: u.department,
          roles: u.roles
        }))
      });

      // Step 2: Get faculty from the same department (try case-insensitive)
      const { data: departmentFaculty, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, email, department, designation, roles')
        .ilike('department', department); // Case-insensitive search

      console.log('🔍 Department Query Details:', {
        searchingForDepartment: department,
        departmentLength: department.length,
        error: facultyError,
        resultCount: departmentFaculty?.length || 0
      });

      if (facultyError) {
        console.error('🚨 Faculty query error:', facultyError);
        throw facultyError;
      }

      console.log('🔍 Department Faculty Results:', {
        department,
        facultyCount: departmentFaculty?.length || 0,
        faculty: departmentFaculty?.map(f => ({
          id: f.id,
          name: f.full_name,
          department: f.department,
          roles: f.roles
        }))
      });

      if (!departmentFaculty || departmentFaculty.length === 0) {
        console.log('🔍 No faculty found in department');
        return [];
      }

      // Step 2: Get faculty IDs (exclude current HOD)
      const facultyIds = departmentFaculty
        .filter(f => f.id !== hodId) // Exclude self
        .map(f => f.id);

      console.log('🔍 Faculty IDs to check for requests:', {
        allFacultyIds: departmentFaculty.map(f => f.id),
        excludedHODId: hodId,
        filteredFacultyIds: facultyIds
      });

      if (facultyIds.length === 0) {
        console.log('🔍 No other faculty in department');
        return [];
      }

      // Step 3: Get leave requests from these faculty
      let query = supabase
        .from('leave_requests')
        .select('*')
        .eq('status', 'pending')
        .in('faculty_id', facultyIds)
        .order('applied_date', { ascending: false });

      // Apply filters
      if (filters?.leave_type) {
        query = query.eq('leave_type', filters.leave_type);
      }
      if (filters?.start_date) {
        query = query.gte('start_date', filters.start_date);
      }
      if (filters?.end_date) {
        query = query.lte('end_date', filters.end_date);
      }

      const { data: requests, error } = await query;

      console.log('🔍 Leave Requests Found:', {
        error,
        requestsCount: requests?.length || 0,
        requests: requests?.map(r => ({
          id: r.id,
          faculty_id: r.faculty_id,
          status: r.status,
          leave_type: r.leave_type,
          total_days: r.total_days
        }))
      });

      if (error) {
        console.error('🚨 Leave requests query error:', error);
        throw error;
      }

      if (!requests || requests.length === 0) {
        console.log('🔍 No pending requests found from department faculty');
        return [];
      }

      // Step 4: Combine with faculty data and enrich affected classes
      const requestsWithFaculty = await Promise.all(requests.map(async (request) => {
        const enrichedRequest = {
          ...request,
          faculty: departmentFaculty.find(f => f.id === request.faculty_id)
        };

        // Enrich affected classes data if it exists but is incomplete
        if (enrichedRequest.affected_classes && Array.isArray(enrichedRequest.affected_classes)) {
          enrichedRequest.affected_classes = await this.enrichAffectedClassesData(enrichedRequest.affected_classes);
        }

        return enrichedRequest;
      }));

      // Step 5: Apply faculty name filter if provided
      let filteredData = requestsWithFaculty;
      if (filters?.faculty_name) {
        const searchTerm = filters.faculty_name.toLowerCase();
        filteredData = filteredData.filter(request =>
          request.faculty?.full_name?.toLowerCase().includes(searchTerm)
        );
      }

      console.log('🔍 FINAL RESULT:', {
        totalRequests: requests.length,
        finalRequests: filteredData.length,
        requests: filteredData.map(r => ({
          id: r.id,
          faculty: r.faculty?.full_name,
          leave_type: r.leave_type,
          days: r.total_days
        }))
      });

      return filteredData;
    } catch (error) {
      console.error('Error fetching pending requests for HOD:', error);
      throw error;
    }
  }

  /**
   * Get pending HOD leave requests for Principal approval
   */
  static async getPendingHODRequestsForPrincipal(
    filters?: {
      leave_type?: string;
      start_date?: string;
      end_date?: string;
      department?: string;
    }
  ): Promise<LeaveRequest[]> {
    try {
      console.log('🔍 Principal HOD Approval Query:', { filters });

      // First get all pending leave requests
      let query = supabase
        .from('leave_requests')
        .select('*')
        .eq('status', 'pending')
        .order('applied_date', { ascending: false });

      const { data: requests, error } = await query;

      if (error) throw error;

      if (!requests || requests.length === 0) {
        return [];
      }

      // Get faculty details for each request
      const facultyIds = requests.map(r => r.faculty_id);
      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, email, department, designation, roles')
        .in('id', facultyIds);

      if (facultyError) throw facultyError;

      // Combine requests with faculty data and filter for HOD requests only
      const requestsWithFaculty = await Promise.all(requests.map(async (request) => {
        const enrichedRequest = {
          ...request,
          faculty: facultyData?.find(f => f.id === request.faculty_id)
        };

        // Enrich affected classes data if it exists but is incomplete
        if (enrichedRequest.affected_classes && Array.isArray(enrichedRequest.affected_classes)) {
          enrichedRequest.affected_classes = await this.enrichAffectedClassesData(enrichedRequest.affected_classes);
        }

        return enrichedRequest;
      }));

      let hodRequests = requestsWithFaculty.filter(request =>
        request.faculty?.roles?.includes('hod')
      );

      console.log('🔍 Principal HOD Filter Results:', {
        totalRequests: requests.length,
        afterFacultyFilter: requestsWithFaculty.length,
        hodRequests: hodRequests.length,
        hodRequestDetails: hodRequests.map(r => ({
          id: r.id,
          faculty: r.faculty?.full_name,
          roles: r.faculty?.roles,
          department: r.department
        }))
      });

      // Apply additional filters
      if (filters?.leave_type) {
        hodRequests = hodRequests.filter(r => r.leave_type === filters.leave_type);
      }
      if (filters?.department) {
        hodRequests = hodRequests.filter(r => r.department === filters.department);
      }
      if (filters?.start_date) {
        hodRequests = hodRequests.filter(r => r.start_date >= filters.start_date!);
      }
      if (filters?.end_date) {
        hodRequests = hodRequests.filter(r => r.end_date <= filters.end_date!);
      }

      console.log('🔍 Final HOD Requests after filters:', hodRequests.length);

      return hodRequests;
    } catch (error) {
      console.error('Error fetching pending HOD requests for Principal:', error);
      throw error;
    }
  }

  /**
   * Approve a leave request (HOD or Principal)
   */
  static async approveLeaveRequest(
    requestId: string,
    approverId: string,
    approverRole: 'hod' | 'principal',
    approverDepartment: string
  ): Promise<void> {
    try {
      // Get the current request details (without JOIN)
      const { data: currentRequest, error: fetchError } = await supabase
        .from('leave_requests')
        .select('*')
        .eq('id', requestId)
        .single();

      if (fetchError) throw fetchError;
      if (!currentRequest) throw new Error('Leave request not found');

      // Get faculty details separately
      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details')
        .select('roles')
        .eq('id', currentRequest.faculty_id)
        .single();

      if (facultyError) {
        console.error('Error fetching faculty data:', facultyError);
        // Continue with approval even if faculty data fetch fails
      }

      // Determine approval level and final status
      const facultyRoles = facultyData?.roles || [];
      let approvalLevel: string;
      let finalStatus: string;

      console.log('🔍 Approval Logic:', {
        requestId,
        approverRole,
        facultyRoles,
        facultyId: currentRequest.faculty_id
      });

      if (approverRole === 'hod') {
        // HOD approving regular faculty request
        if (facultyRoles.includes('hod')) {
          throw new Error('HOD cannot approve another HOD request. This should go to Principal.');
        }
        approvalLevel = 'hod_approved';
        finalStatus = 'approved'; // Final approval for regular faculty
      } else if (approverRole === 'principal') {
        // Principal approving HOD request
        if (!facultyRoles.includes('hod')) {
          throw new Error('Principal should only approve HOD requests. Regular faculty requests go to HOD.');
        }
        approvalLevel = 'principal_approved';
        finalStatus = 'approved'; // Final approval for HOD
      } else {
        throw new Error('Invalid approver role');
      }

      console.log('🔍 Approval Decision:', { approvalLevel, finalStatus });

      // Update the leave request
      const { error: updateError } = await supabase
        .from('leave_requests')
        .update({
          status: finalStatus,
          approved_by: approverId,
          approved_date: new Date().toISOString(),
          approval_level: approvalLevel,
          approver_role: approverRole,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (updateError) throw updateError;

      console.log('✅ Leave request updated - database trigger will automatically update balance');

      // Create audit record
      await this.createApprovalAudit(
        requestId,
        approverId,
        approverRole,
        'approved',
        'pending',
        finalStatus,
        approvalLevel,
        approverDepartment
      );

    } catch (error) {
      console.error('Error approving leave request:', error);
      throw error;
    }
  }

  /**
   * Reject a leave request (HOD or Principal)
   */
  static async rejectLeaveRequest(
    requestId: string,
    approverId: string,
    approverRole: 'hod' | 'principal',
    rejectionReason: string,
    approverDepartment: string
  ): Promise<void> {
    try {
      // Update the leave request
      const { error: updateError } = await supabase
        .from('leave_requests')
        .update({
          status: 'rejected',
          approved_by: approverId,
          approved_date: new Date().toISOString(),
          rejection_reason: rejectionReason,
          approver_role: approverRole,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (updateError) throw updateError;

      // Create audit record
      await this.createApprovalAudit(
        requestId,
        approverId,
        approverRole,
        'rejected',
        'pending',
        'rejected',
        'rejected',
        approverDepartment,
        rejectionReason
      );

    } catch (error) {
      console.error('Error rejecting leave request:', error);
      throw error;
    }
  }

  /**
   * Create approval audit record
   */
  private static async createApprovalAudit(
    leaveRequestId: string,
    approverId: string,
    approverRole: string,
    action: 'approved' | 'rejected',
    previousStatus: string,
    newStatus: string,
    approvalLevel: string,
    department: string,
    rejectionReason?: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('leave_approval_audit')
        .insert({
          leave_request_id: leaveRequestId,
          approver_id: approverId,
          approver_role: approverRole,
          action: action,
          previous_status: previousStatus,
          new_status: newStatus,
          approval_level: approvalLevel,
          department: this.mapDepartmentName(department),
          rejection_reason: rejectionReason
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error creating approval audit:', error);
      throw error;
    }
  }

  /**
   * Get approval history for HOD (all requests they have approved/rejected)
   */
  static async getApprovalHistoryForHOD(
    hodId: string,
    department: string,
    filters?: {
      faculty_name?: string;
      leave_type?: string;
      status?: string;
      start_date?: string;
      end_date?: string;
    }
  ): Promise<LeaveRequest[]> {
    try {
      console.log('🔍 Getting approval history for HOD:', { hodId, department, filters });

      // First get all requests approved/rejected by this HOD
      let query = supabase
        .from('leave_requests')
        .select('*')
        .eq('approved_by', hodId)
        .in('status', ['approved', 'rejected'])
        .order('approved_date', { ascending: false });

      const { data: requests, error } = await query;

      if (error) throw error;

      if (!requests || requests.length === 0) {
        return [];
      }

      // Get faculty details for each request (same pattern as other methods)
      const facultyIds = requests.map(r => r.faculty_id);
      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, email, department, designation')
        .in('id', facultyIds);

      if (facultyError) {
        console.error('Error fetching faculty data:', facultyError);
      }

      // Create a map for quick lookup
      const facultyMap = new Map();
      if (facultyData) {
        facultyData.forEach(faculty => {
          facultyMap.set(faculty.id, faculty);
        });
      }

      // Combine requests with faculty details
      let historyData = requests.map(request => ({
        ...request,
        employee_details: facultyMap.get(request.faculty_id) || null
      }));

      // Apply filters
      if (filters?.faculty_name) {
        const searchTerm = filters.faculty_name.toLowerCase();
        historyData = historyData.filter(request =>
          request.employee_details?.full_name?.toLowerCase().includes(searchTerm)
        );
      }

      if (filters?.leave_type) {
        historyData = historyData.filter(request => request.leave_type === filters.leave_type);
      }

      if (filters?.status) {
        historyData = historyData.filter(request => request.status === filters.status);
      }

      if (filters?.start_date) {
        historyData = historyData.filter(request => request.start_date >= filters.start_date!);
      }

      if (filters?.end_date) {
        historyData = historyData.filter(request => request.end_date <= filters.end_date!);
      }

      console.log('🔍 Approval history results:', {
        totalRequests: requests?.length || 0,
        filteredRequests: historyData.length,
        facultyDataFound: facultyData?.length || 0
      });

      return historyData;
    } catch (error) {
      console.error('Error fetching approval history for HOD:', error);
      throw error;
    }
  }

  /**
   * Get approval history for a leave request
   */
  static async getApprovalHistory(requestId: string): Promise<ApprovalStep[]> {
    try {
      // Get approval audit records
      const { data: auditData, error } = await supabase
        .from('leave_approval_audit')
        .select('*')
        .eq('leave_request_id', requestId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      if (!auditData || auditData.length === 0) {
        return [];
      }

      // Get approver details separately
      const approverIds = auditData.map(record => record.approver_id);
      const { data: approverData, error: approverError } = await supabase
        .from('employee_details')
        .select('id, full_name')
        .in('id', approverIds);

      if (approverError) {
        console.error('Error fetching approver data:', approverError);
      }

      return auditData.map(record => {
        const approver = approverData?.find(a => a.id === record.approver_id);
        return {
          approver_id: record.approver_id,
          approver_name: approver?.full_name || 'Unknown',
          approver_role: record.approver_role,
          action: record.action,
          timestamp: record.created_at,
          rejection_reason: record.rejection_reason
        };
      });
    } catch (error) {
      console.error('Error fetching approval history:', error);
      throw error;
    }
  }

  /**
   * Get subject information from simplified mappings table
   */
  static async getSubjectFromSimplifiedMappings(subjectCode: string, department?: string): Promise<any> {
    try {
      let query = supabase
        .from('simplified_subject_faculty_mappings')
        .select('subject_code, subject_name, subject_type, hours_per_week, department, semester, section')
        .eq('subject_code', subjectCode);

      if (department) {
        query = query.eq('department', department);
      }

      const { data, error } = await query.limit(1);

      if (!error && data && data.length > 0) {
        console.log(`✅ Found subject in simplified mappings: ${subjectCode} -> ${data[0].subject_name}`);
        return {
          ...data[0],
          source: 'simplified_subject_faculty_mappings'
        };
      }
    } catch (error) {
      console.error('Error querying simplified mappings:', error);
    }
    return null;
  }
}
