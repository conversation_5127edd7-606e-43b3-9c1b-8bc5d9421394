import { supabase } from '@/integrations/supabase/client';

/**
 * Installation script for Student Authentication System
 * This script sets up the database table and initial configuration
 */

async function executeQuery(query: string): Promise<any> {
  const { data, error } = await supabase.rpc('execute_sql', { query });
  if (error) {
    throw new Error(`SQL Error: ${error.message}`);
  }
  return data;
}

async function installStudentAuthSystem() {
  try {
    console.log("🚀 Installing Student Authentication System...");
    
    // Step 1: Create student_auth table
    console.log("📋 Step 1: Creating student_auth table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS student_auth (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          usn TEXT NOT NULL UNIQUE,
          email TEXT NOT NULL UNIQUE,
          password_hash TEXT NOT NULL,
          is_verified BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
      );
    `);
    
    // Step 2: Add constraints
    console.log("🔒 Step 2: Adding validation constraints...");
    await executeQuery(`
      ALTER TABLE student_auth 
      ADD CONSTRAINT IF NOT EXISTS usn_format_check 
      CHECK (usn ~ '^1KS\\d{2}[A-Z]{2,4}\\d{3}$');
    `);
    
    await executeQuery(`
      ALTER TABLE student_auth 
      ADD CONSTRAINT IF NOT EXISTS email_format_check 
      CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$');
    `);
    
    // Step 3: Create indexes
    console.log("⚡ Step 3: Creating performance indexes...");
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_student_auth_usn ON student_auth(usn);
    `);
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_student_auth_email ON student_auth(email);
    `);
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_student_auth_is_verified ON student_auth(is_verified);
    `);
    
    // Step 4: Add comments
    console.log("📝 Step 4: Adding table documentation...");
    await executeQuery(`
      COMMENT ON TABLE student_auth IS 'Student authentication table for secure login system';
    `);
    await executeQuery(`
      COMMENT ON COLUMN student_auth.id IS 'Primary key UUID';
    `);
    await executeQuery(`
      COMMENT ON COLUMN student_auth.usn IS 'University Seat Number in format 1KS23CS001';
    `);
    await executeQuery(`
      COMMENT ON COLUMN student_auth.email IS 'Student email address for login and communication';
    `);
    await executeQuery(`
      COMMENT ON COLUMN student_auth.password_hash IS 'Bcrypt hashed password - never store plain text';
    `);
    await executeQuery(`
      COMMENT ON COLUMN student_auth.is_verified IS 'Email verification status';
    `);
    
    // Step 5: Create update trigger
    console.log("🔄 Step 5: Creating update trigger...");
    await executeQuery(`
      CREATE OR REPLACE FUNCTION update_student_auth_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = now();
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    await executeQuery(`
      DROP TRIGGER IF EXISTS trigger_update_student_auth_updated_at ON student_auth;
    `);
    
    await executeQuery(`
      CREATE TRIGGER trigger_update_student_auth_updated_at
          BEFORE UPDATE ON student_auth
          FOR EACH ROW
          EXECUTE FUNCTION update_student_auth_updated_at();
    `);
    
    // Step 6: Verify installation
    console.log("✅ Step 6: Verifying installation...");
    const tableStructure = await executeQuery(`
      SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = 'student_auth' 
      ORDER BY ordinal_position;
    `);
    
    console.log("📊 Table structure:", tableStructure);
    
    // Step 7: Test constraints
    console.log("🧪 Step 7: Testing constraints...");
    try {
      // Test USN constraint (should fail)
      await executeQuery(`
        INSERT INTO student_auth (usn, email, password_hash) 
        VALUES ('INVALID_USN', '<EMAIL>', 'dummy_hash');
      `);
      console.warn("⚠️  USN constraint test failed - invalid USN was accepted");
    } catch (error) {
      console.log("✅ USN constraint working correctly");
    }
    
    try {
      // Test email constraint (should fail)
      await executeQuery(`
        INSERT INTO student_auth (usn, email, password_hash) 
        VALUES ('1KS23CS001', 'invalid_email', 'dummy_hash');
      `);
      console.warn("⚠️  Email constraint test failed - invalid email was accepted");
    } catch (error) {
      console.log("✅ Email constraint working correctly");
    }
    
    // Clean up test data if any
    await executeQuery(`
      DELETE FROM student_auth WHERE usn IN ('INVALID_USN', '1KS23CS001') OR email IN ('<EMAIL>', 'invalid_email');
    `);
    
    console.log("🎉 Student Authentication System installed successfully!");
    console.log("");
    console.log("📋 Installation Summary:");
    console.log("✅ student_auth table created");
    console.log("✅ USN format validation (1KS23CS001)");
    console.log("✅ Email format validation");
    console.log("✅ Performance indexes created");
    console.log("✅ Auto-update trigger installed");
    console.log("✅ Table documentation added");
    console.log("");
    console.log("🚀 Next Steps:");
    console.log("1. Install dependencies: npm install bcryptjs @types/bcryptjs");
    console.log("2. Test the registration form at /student-registration");
    console.log("3. Test the login form at /student-login");
    console.log("4. Access the dashboard at /student-dashboard");
    console.log("");
    console.log("📖 For more information, see STUDENT_AUTHENTICATION_SYSTEM.md");
    
  } catch (error) {
    console.error("❌ Installation failed:", error);
    throw error;
  }
}

// Execute the installation
installStudentAuthSystem()
  .then(() => {
    console.log("✅ Installation script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Installation script failed:", error);
    process.exit(1);
  });
