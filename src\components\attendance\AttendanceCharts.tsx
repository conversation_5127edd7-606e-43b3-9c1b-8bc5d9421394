
import React from 'react';
import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, Re<PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

interface DailyAttendanceEntry {
  date: string;
  present: number;
  absent: number;
}

interface OverallAttendanceEntry {
  name: string;
  value: number;
}

interface ChartData {
  dailyData: DailyAttendanceEntry[];
  overallData: OverallAttendanceEntry[];
}

interface AttendanceChartsProps {
  chartData: ChartData;
}

const COLORS = ['#4ade80', '#f87171'];

const AttendanceCharts: React.FC<AttendanceChartsProps> = ({ chartData }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-card rounded-md border shadow p-4">
          <h3 className="text-lg font-medium mb-4">Daily Attendance Trend</h3>
          <div className="w-full h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <ReChartsBarChart
                data={chartData.dailyData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="present" fill="#4ade80" name="Present" />
                <Bar dataKey="absent" fill="#f87171" name="Absent" />
              </ReChartsBarChart>
            </ResponsiveContainer>
          </div>
        </div>
        
        <div className="bg-white dark:bg-card rounded-md border shadow p-4">
          <h3 className="text-lg font-medium mb-4">Overall Attendance Distribution</h3>
          <div className="w-full h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartData.overallData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                  label={({
                    cx,
                    cy,
                    midAngle,
                    innerRadius,
                    outerRadius,
                    percent,
                    index,
                    name,
                  }) => {
                    const RADIAN = Math.PI / 180;
                    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
                    const x = cx + radius * Math.cos(-midAngle * RADIAN);
                    const y = cy + radius * Math.sin(-midAngle * RADIAN);
                    return (
                      <text
                        x={x}
                        y={y}
                        fill="white"
                        textAnchor={x > cx ? 'start' : 'end'}
                        dominantBaseline="central"
                      >
                        {`${name} ${(percent * 100).toFixed(0)}%`}
                      </text>
                    );
                  }}
                >
                  {chartData.overallData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-card border rounded-md p-4 shadow">
          <h4 className="text-sm font-medium text-muted-foreground">Total Present</h4>
          <p className="text-3xl font-bold text-green-500 dark:text-green-400">
            {chartData.overallData.find(d => d.name === 'Present')?.value || 0}
          </p>
        </div>

        <div className="bg-card border rounded-md p-4 shadow">
          <h4 className="text-sm font-medium text-muted-foreground">Total Absent</h4>
          <p className="text-3xl font-bold text-red-500 dark:text-red-400">
            {chartData.overallData.find(d => d.name === 'Absent')?.value || 0}
          </p>
        </div>
        
        <div className="bg-white dark:bg-card border rounded-md p-4 shadow">
          <h4 className="text-sm font-medium text-muted-foreground">Average Attendance</h4>
          <p className="text-3xl font-bold text-blue-500">
            {chartData.overallData.reduce((sum, item) => sum + item.value, 0) === 0
              ? "0%"
              : `${((chartData.overallData.find(d => d.name === 'Present')?.value || 0) / 
                  chartData.overallData.reduce((sum, item) => sum + item.value, 0) * 100).toFixed(1)}%`
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default AttendanceCharts;
