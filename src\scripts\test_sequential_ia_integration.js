/**
 * Comprehensive Integration Test for Sequential IA Entry System
 * Tests data integrity from Sequential IA Entry → internal_assessments → Student Progress
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://milmyotuougemocvieof.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test configuration
const TEST_CONFIG = {
  facultyId: '7965c1a9-fee4-4ea1-843b-462b77c83222', // Dr. Vijayalaxmi Mekali
  studentId: '4b4b0737-262f-4bc5-bc11-a17b29b4ee54', // 1KS23CS001
  studentUsn: '1KS23CS001',
  subjectCode: 'BCS401',
  department: 'CSE',
  semester: '4',
  section: 'A',
  academicYear: '2025-2026'
};

async function runIntegrationTests() {
  console.log('🧪 Starting Sequential IA Integration Tests...');
  console.log('=' .repeat(80));

  try {
    // Test 1: Data Validation
    console.log('\n📋 Test 1: Data Validation');
    await testDataValidation();

    // Test 2: Student Mapping Verification
    console.log('\n👤 Test 2: Student Mapping Verification');
    await testStudentMapping();

    // Test 3: Subject Code Normalization
    console.log('\n📝 Test 3: Subject Code Normalization');
    await testSubjectCodeNormalization();

    // Test 4: Academic Year Alignment
    console.log('\n📅 Test 4: Academic Year Alignment');
    await testAcademicYearAlignment();

    // Test 5: Sequential IA Entry Simulation
    console.log('\n💾 Test 5: Sequential IA Entry Simulation');
    await testSequentialIAEntry();

    // Test 6: Student Progress Integration
    console.log('\n📊 Test 6: Student Progress Integration');
    await testStudentProgressIntegration();

    // Test 7: Real-time Verification
    console.log('\n🔄 Test 7: Real-time Verification');
    await testRealTimeVerification();

    console.log('\n🎉 All integration tests completed successfully!');

  } catch (error) {
    console.error('❌ Integration test suite failed:', error);
  }
}

async function testDataValidation() {
  console.log('Testing data validation rules...');

  // Test student ID validation
  const { data: student, error: studentError } = await supabase
    .from('class_students')
    .select('id, usn, student_name, semester, section, department')
    .eq('id', TEST_CONFIG.studentId)
    .single();

  if (studentError || !student) {
    console.log('❌ Student ID validation failed');
    return;
  }

  console.log(`✅ Student validation passed: ${student.student_name} (${student.usn})`);

  // Test subject code validation
  const { data: subjects, error: subjectError } = await supabase
    .from('timetable_slots')
    .select('subject_code, subject_name')
    .eq('department', TEST_CONFIG.department.toLowerCase())
    .eq('semester', TEST_CONFIG.semester)
    .eq('section', TEST_CONFIG.section);

  if (subjectError || !subjects || subjects.length === 0) {
    console.log('❌ Subject validation failed');
    return;
  }

  const subjectExists = subjects.find(s => s.subject_code === TEST_CONFIG.subjectCode);
  if (subjectExists) {
    console.log(`✅ Subject validation passed: ${subjectExists.subject_name}`);
  } else {
    console.log(`❌ Subject ${TEST_CONFIG.subjectCode} not found in timetable`);
  }
}

async function testStudentMapping() {
  console.log('Testing student ID mapping...');

  // Verify student exists and belongs to correct class
  const { data: student, error } = await supabase
    .from('class_students')
    .select('*')
    .eq('usn', TEST_CONFIG.studentUsn)
    .single();

  if (error || !student) {
    console.log(`❌ Student ${TEST_CONFIG.studentUsn} not found`);
    return;
  }

  // Check class match
  if (student.semester === TEST_CONFIG.semester && student.section === TEST_CONFIG.section) {
    console.log(`✅ Student class mapping correct: ${student.student_name} in ${student.semester}${student.section}`);
  } else {
    console.log(`❌ Class mismatch: Student in ${student.semester}${student.section}, test expects ${TEST_CONFIG.semester}${TEST_CONFIG.section}`);
  }

  // Verify student ID matches
  if (student.id === TEST_CONFIG.studentId) {
    console.log(`✅ Student ID mapping correct: ${student.id}`);
  } else {
    console.log(`❌ Student ID mismatch: Expected ${TEST_CONFIG.studentId}, got ${student.id}`);
  }
}

async function testSubjectCodeNormalization() {
  console.log('Testing subject code normalization...');

  const testCases = [
    { input: 'BCS401_THEORY', expected: 'BCS401' },
    { input: 'BCS401_LAB', expected: 'BCS401' },
    { input: 'BCS401', expected: 'BCS401' },
    { input: 'BCSL404_PRACTICAL', expected: 'BCSL404' }
  ];

  testCases.forEach(testCase => {
    const normalized = normalizeSubjectCode(testCase.input);
    const result = normalized === testCase.expected ? '✅' : '❌';
    console.log(`  ${result} ${testCase.input} → ${normalized} (expected: ${testCase.expected})`);
  });
}

function normalizeSubjectCode(subjectCode) {
  if (!subjectCode) return subjectCode;
  return subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
}

async function testAcademicYearAlignment() {
  console.log('Testing academic year format...');

  // Test current academic year format
  const academicYear = TEST_CONFIG.academicYear;
  const pattern = /^\d{4}-\d{4}$/;

  if (pattern.test(academicYear)) {
    const [startYear, endYear] = academicYear.split('-').map(Number);
    if (endYear === startYear + 1) {
      console.log(`✅ Academic year format valid: ${academicYear}`);
    } else {
      console.log(`❌ Academic year invalid: End year should be start year + 1`);
    }
  } else {
    console.log(`❌ Academic year format invalid: ${academicYear}. Expected YYYY-YYYY`);
  }
}

async function testSequentialIAEntry() {
  console.log('Testing Sequential IA Entry simulation...');

  // Simulate IA marks entry
  const testMarks = {
    student_id: TEST_CONFIG.studentId,
    subject_code: TEST_CONFIG.subjectCode,
    faculty_id: TEST_CONFIG.facultyId,
    department: TEST_CONFIG.department.toLowerCase(),
    semester: TEST_CONFIG.semester,
    section: TEST_CONFIG.section,
    academic_year: TEST_CONFIG.academicYear,
    ia1_marks: 22,
    ia2_marks: 20,
    ia3_marks: 24,
    assignment_marks: 8,
    created_by: TEST_CONFIG.facultyId,
    last_modified_by: TEST_CONFIG.facultyId,
    updated_at: new Date().toISOString()
  };

  try {
    const { data, error } = await supabase
      .from('internal_assessments')
      .upsert([testMarks], {
        onConflict: 'student_id,subject_code,academic_year'
      })
      .select();

    if (error) {
      console.log('❌ Sequential IA Entry simulation failed:', error.message);
      return;
    }

    console.log('✅ Sequential IA Entry simulation successful');
    console.log(`📝 Saved record: ${data[0].subject_code} - Total: ${(data[0].ia1_marks || 0) + (data[0].ia2_marks || 0) + (data[0].ia3_marks || 0) + (data[0].assignment_marks || 0)}`);

  } catch (error) {
    console.log('❌ Sequential IA Entry simulation error:', error.message);
  }
}

async function testStudentProgressIntegration() {
  console.log('Testing Student Progress integration...');

  try {
    // Fetch IA marks as Student Progress would
    const { data: iaData, error } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', TEST_CONFIG.studentId)
      .eq('academic_year', TEST_CONFIG.academicYear);

    if (error) {
      console.log('❌ Student Progress integration failed:', error.message);
      return;
    }

    if (!iaData || iaData.length === 0) {
      console.log('❌ No IA data found for Student Progress display');
      return;
    }

    console.log('✅ Student Progress integration successful');
    console.log(`📊 Found ${iaData.length} IA records for student`);

    // Verify data structure
    iaData.forEach(record => {
      const total = (record.ia1_marks || 0) + (record.ia2_marks || 0) + (record.ia3_marks || 0) + (record.assignment_marks || 0);
      console.log(`  📝 ${record.subject_code}: IA1=${record.ia1_marks}, IA2=${record.ia2_marks}, IA3=${record.ia3_marks}, Assignment=${record.assignment_marks}, Total=${total}`);
    });

  } catch (error) {
    console.log('❌ Student Progress integration error:', error.message);
  }
}

async function testRealTimeVerification() {
  console.log('Testing real-time verification...');

  try {
    // Update marks and verify immediate availability
    const updatedMarks = {
      student_id: TEST_CONFIG.studentId,
      subject_code: TEST_CONFIG.subjectCode,
      academic_year: TEST_CONFIG.academicYear,
      ia1_marks: 25, // Updated value
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('internal_assessments')
      .upsert([updatedMarks], {
        onConflict: 'student_id,subject_code,academic_year'
      })
      .select();

    if (error) {
      console.log('❌ Real-time verification failed:', error.message);
      return;
    }

    // Immediately verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', TEST_CONFIG.studentId)
      .eq('subject_code', TEST_CONFIG.subjectCode)
      .eq('academic_year', TEST_CONFIG.academicYear)
      .single();

    if (!verifyError && verifyData && verifyData.ia1_marks === 25) {
      console.log('✅ Real-time verification successful');
      console.log(`📊 Updated marks immediately available: IA1=${verifyData.ia1_marks}`);
      console.log(`🕒 Last updated: ${verifyData.updated_at}`);
    } else {
      console.log('❌ Real-time verification failed: Update not immediately available');
    }

  } catch (error) {
    console.log('❌ Real-time verification error:', error.message);
  }
}

// Run the integration tests
runIntegrationTests();
