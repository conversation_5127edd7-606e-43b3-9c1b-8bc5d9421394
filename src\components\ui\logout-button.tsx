import React from 'react';
import { LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLogout } from '@/hooks/useLogout';
import { cn } from '@/lib/utils';

interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  showIcon?: boolean;
  children?: React.ReactNode;
  onClick?: () => void; // Optional custom onClick handler
}

/**
 * Standardized logout button component that can be used anywhere in the application
 * Automatically handles logout for both faculty/admin and student users
 * Always redirects to the landing page after successful logout
 * OPTIMIZED: Uses fast logout for immediate response
 */
export const LogoutButton: React.FC<LogoutButtonProps> = ({
  variant = 'outline',
  size = 'default',
  className,
  showIcon = true,
  children,
  onClick
}) => {
  const { fastLogout, isLoggedIn } = useLogout();

  const handleLogout = () => {
    if (onClick) {
      onClick();
    }
    // PERFORMANCE FIX: Use fastLogout for immediate response
    fastLogout();
  };

  // Don't render if no user is logged in
  if (!isLoggedIn) {
    return null;
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleLogout}
      className={cn(
        "text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20",
        className
      )}
    >
      {showIcon && <LogOut className="h-4 w-4 mr-2" />}
      {children || 'Logout'}
    </Button>
  );
};
