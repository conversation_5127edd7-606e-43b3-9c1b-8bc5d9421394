/**
 * Debug script for Student Progress Card issues
 * Helps identify why subjects show as "Unknown Subject" and why IA marks don't appear
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'your-supabase-url';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-key';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test configuration
const TEST_CONFIG = {
  studentUsn: '1KS23CS001', // Replace with actual USN from screenshot
  academicYear: '2024-25',  // Use the academic year from screenshot
  department: 'cse',
  semester: '4',
  section: 'A'
};

async function debugStudentProgress() {
  console.log('🔍 Debugging Student Progress Card Issues\n');
  console.log('Test Configuration:', TEST_CONFIG);

  try {
    // Step 1: Check if student exists
    console.log('\n📋 Step 1: Checking Student Data');
    const { data: studentData, error: studentError } = await supabase
      .from('class_students')
      .select('*')
      .eq('usn', TEST_CONFIG.studentUsn.toUpperCase())
      .single();

    if (studentError || !studentData) {
      console.log('❌ Student not found:', studentError?.message);
      return;
    }

    console.log('✅ Student found:', {
      id: studentData.id,
      name: studentData.student_name,
      department: studentData.department,
      semester: studentData.semester,
      section: studentData.section,
      academic_year: studentData.academic_year
    });

    // Step 2: Check timetable subjects
    console.log('\n📋 Step 2: Checking Timetable Subjects');
    const { data: timetableSubjects, error: timetableError } = await supabase
      .from('timetable_slots')
      .select('subject_code, subject_name, subject_type, batch_name')
      .eq('department', TEST_CONFIG.department)
      .eq('semester', TEST_CONFIG.semester)
      .eq('section', TEST_CONFIG.section);

    if (timetableError) {
      console.log('❌ Error fetching timetable subjects:', timetableError.message);
    } else {
      console.log(`✅ Found ${timetableSubjects?.length || 0} timetable entries`);
      
      // Group by subject code
      const subjectMap = new Map();
      timetableSubjects?.forEach(slot => {
        if (!subjectMap.has(slot.subject_code)) {
          subjectMap.set(slot.subject_code, []);
        }
        subjectMap.get(slot.subject_code).push(slot);
      });

      console.log('📚 Subjects in timetable:');
      subjectMap.forEach((slots, subjectCode) => {
        const uniqueNames = [...new Set(slots.map(s => s.subject_name))];
        const types = [...new Set(slots.map(s => s.subject_type))];
        const batches = [...new Set(slots.map(s => s.batch_name).filter(Boolean))];
        
        console.log(`  ${subjectCode}: ${uniqueNames.join(', ')} (${types.join(', ')})${batches.length ? ` [Batches: ${batches.join(', ')}]` : ''}`);
      });
    }

    // Step 3: Check IA marks in database
    console.log('\n📋 Step 3: Checking IA Marks in Database');
    const { data: iaMarks, error: iaError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', studentData.id);

    if (iaError) {
      console.log('❌ Error fetching IA marks:', iaError.message);
    } else {
      console.log(`✅ Found ${iaMarks?.length || 0} IA records`);
      
      iaMarks?.forEach(record => {
        console.log(`  📝 ${record.subject_code} (${record.academic_year}): IA1=${record.ia1_marks}, IA2=${record.ia2_marks}, IA3=${record.ia3_marks}, Assignment=${record.assignment_marks}`);
      });
    }

    // Step 4: Check for academic year mismatches
    console.log('\n📋 Step 4: Checking Academic Year Consistency');
    const academicYears = {
      student: studentData.academic_year,
      test_config: TEST_CONFIG.academicYear,
      ia_records: [...new Set(iaMarks?.map(r => r.academic_year) || [])]
    };
    
    console.log('📅 Academic Years:', academicYears);
    
    if (academicYears.student !== academicYears.test_config) {
      console.log('⚠️ Academic year mismatch detected!');
      console.log(`   Student: ${academicYears.student}`);
      console.log(`   Test Config: ${academicYears.test_config}`);
    }

    // Step 5: Check attendance data
    console.log('\n📋 Step 5: Checking Attendance Data');
    const { data: attendanceData, error: attendanceError } = await supabase
      .from('usn_attendance')
      .select('subject_identifier, subject_name, COUNT(*) as total_records')
      .eq('student_usn', TEST_CONFIG.studentUsn.toUpperCase())
      .eq('department', TEST_CONFIG.department)
      .eq('semester', TEST_CONFIG.semester)
      .eq('section', TEST_CONFIG.section);

    if (attendanceError) {
      console.log('❌ Error fetching attendance:', attendanceError.message);
    } else {
      console.log(`✅ Found attendance data for ${attendanceData?.length || 0} subjects`);
      attendanceData?.forEach(record => {
        console.log(`  📊 ${record.subject_identifier}: ${record.subject_name} (${record.total_records} records)`);
      });
    }

    // Step 6: Test subject name enhancement
    console.log('\n📋 Step 6: Testing Subject Name Enhancement');
    const testSubjects = ['BCS401', 'BCS402', 'BCS402_LAB', 'BCS402_A1', 'BCS403'];
    
    testSubjects.forEach(subjectCode => {
      const timetableSubject = timetableSubjects?.find(s => s.subject_code === subjectCode);
      const originalName = timetableSubject?.subject_name || 'Unknown Subject';
      
      // Apply our enhancement logic
      const enhancedName = getEnhancedSubjectName(subjectCode, originalName);
      
      console.log(`  ${subjectCode}: "${originalName}" → "${enhancedName}"`);
    });

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

// Enhanced subject name function (same as in component)
function getEnhancedSubjectName(subjectCode, originalSubjectName) {
  const LAB_SUBJECT_MAPPINGS = {
    'BCS403': 'DBMS Lab',
    'BCS402': 'MC Lab',  
    'BCS401': 'ADA Lab',
    'BCS404': 'CN Lab',
    'BCS405': 'Software Engineering Lab',
    'BCSL403': 'DBMS Lab',
    'BCSL402': 'MC Lab',
    'BCSL401': 'ADA Lab',
    'BCSL404': 'CN Lab',
    'BCSL405': 'Software Engineering Lab',
  };

  const batchPattern = /^(.+)_([A-Z]\d+)$/;
  const match = subjectCode.match(batchPattern);
  const isLabBatch = !!match;
  
  if (isLabBatch) {
    const [, baseCode, batchSuffix] = match;
    const labName = LAB_SUBJECT_MAPPINGS[baseCode];
    if (labName) {
      return `${labName} (${batchSuffix})`;
    }
  }

  if (subjectCode.includes('_LAB')) {
    const normalizedCode = subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
    const labName = LAB_SUBJECT_MAPPINGS[normalizedCode];
    if (labName) {
      return labName;
    }
  }

  if (!originalSubjectName || originalSubjectName === 'Unknown Subject') {
    const normalizedCode = subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
    
    const isLikelyLab = normalizedCode.startsWith('BCSL') || 
                       subjectCode.includes('LAB') || 
                       subjectCode.includes('_LAB') ||
                       isLabBatch;
    
    if (isLikelyLab) {
      const labName = LAB_SUBJECT_MAPPINGS[normalizedCode];
      if (labName) {
        return labName;
      }
    }
  }

  return originalSubjectName || 'Unknown Subject';
}

// Run the debug script
debugStudentProgress();
