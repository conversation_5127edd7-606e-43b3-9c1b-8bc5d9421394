import { supabase } from "@/integrations/supabase/client";
import { AffectedClass, SubstituteFaculty } from './LeaveManagementService';

export interface QualifiedFaculty {
  id: string;
  full_name: string;
  department: string;
  designation: string;
  qualification_reason: string;
  teaching_assignments: {
    semester: string;
    section: string;
    subject_code: string;
    subject_name: string;
    subject_type: string;
    role: 'primary' | 'secondary';
  }[];
}

export class FacultyQualificationService {
  /**
   * Map full department names to short codes for database queries
   */
  private static mapDepartmentToShortCode(department: string): string {
    const departmentMap: Record<string, string> = {
      'Computer Science and Engineering': 'cse',
      'Computer Science & Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Information Science & Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Electronics & Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Electrical and Electronics Engineering': 'eee',
      'Electrical & Electronics Engineering': 'eee',
      'Information Technology': 'it'
    };

    // Return mapped value or original if no mapping found (in case it's already a short code)
    return departmentMap[department] || department;
  }

  /**
   * Get faculties qualified to teach a specific class based on their current assignments
   */
  static async getQualifiedFaculties(
    affectedClass: AffectedClass,
    department: string,
    academicYear: string = '2024-2025'
  ): Promise<QualifiedFaculty[]> {
    try {
      // Map department to short code for database queries
      const mappedDepartment = this.mapDepartmentToShortCode(department);

      console.log('🎯 Finding qualified faculties for class:', {
        semester: affectedClass.semester,
        section: affectedClass.section,
        subject_type: affectedClass.subject_type,
        subject_code: affectedClass.subject_code,
        originalDepartment: department,
        mappedDepartment: mappedDepartment,
        academicYear: academicYear
      });

      // DEBUG: Let's first check what Mrs. Shruthi T S and Dr. Jalaja actually teach
      const { data: debugFaculties, error: debugError } = await supabase
        .from('employee_details')
        .select('id, full_name')
        .or('full_name.ilike.%Shruthi%,full_name.ilike.%Jalaja%');

      if (!debugError && debugFaculties) {
        console.log('🔍 DEBUG: Found target faculties:', debugFaculties);

        for (const faculty of debugFaculties) {
          const { data: assignments, error: assignError } = await supabase
            .from('simplified_subject_faculty_mappings')
            .select('subject_code, subject_name, subject_type, semester, section, faculty_1_id, faculty_2_id')
            .eq('academic_year', academicYear)
            .eq('department', mappedDepartment)
            .or(`faculty_1_id.eq.${faculty.id},faculty_2_id.eq.${faculty.id}`);

          if (!assignError && assignments) {
            console.log(`📚 ${faculty.full_name} teaches:`, assignments.map(a => ({
              subject: `${a.subject_code} (${a.subject_type})`,
              class: `${a.semester}-${a.section}`,
              role: a.faculty_1_id === faculty.id ? 'Primary' : 'Secondary'
            })));
          }
        }
      }

      // DEBUG: Check if the table has any data at all
      console.log('🔍 DEBUG: Checking table with parameters:', { academicYear, originalDepartment: department, mappedDepartment });

      // First, check what academic years exist
      const { data: yearData, error: yearError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('academic_year')
        .limit(10);

      console.log('📅 Available academic years in table:', [...new Set(yearData?.map(d => d.academic_year) || [])]);

      // Check what departments exist
      const { data: deptData, error: deptError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('department')
        .limit(10);

      console.log('🏢 Available departments in table:', [...new Set(deptData?.map(d => d.department) || [])]);

      // Try with our specific parameters
      const { data: sampleData, error: sampleError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('subject_code, semester, section, faculty_1_id, faculty_2_id, academic_year, department')
        .eq('academic_year', academicYear)
        .eq('department', mappedDepartment)
        .limit(5);

      console.log('🔍 DEBUG: Sample mappings with our parameters:', sampleData?.length || 0, 'records found');
      if (sampleData && sampleData.length > 0) {
        console.log('📋 Sample data:', sampleData);
      } else {
        console.log('❌ No data found with our parameters:', { academicYear, originalDepartment: department, mappedDepartment });

        // Try with case-insensitive department search
        const { data: caseInsensitiveData, error: caseError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select('subject_code, semester, section, faculty_1_id, faculty_2_id, academic_year, department')
          .eq('academic_year', academicYear)
          .ilike('department', `%${mappedDepartment}%`)
          .limit(5);

        console.log('🔍 DEBUG: Case-insensitive department search:', caseInsensitiveData?.length || 0, 'records found');
        if (caseInsensitiveData && caseInsensitiveData.length > 0) {
          console.log('📋 Case-insensitive data:', caseInsensitiveData);
        }
      }

      // Query faculty mappings to find who teaches similar classes
      // Use multiple targeted queries with fallback strategy
      let mappings: any[] = [];

      // Try simplified table first
      const simplifiedQueryPromises = [
        // Query 1: Exact semester-section match (highest priority)
        supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            faculty_1_id,
            faculty_2_id,
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            department
          `)
          .eq('academic_year', academicYear)
          .eq('department', mappedDepartment)
          .eq('semester', affectedClass.semester)
          .eq('section', affectedClass.section),

        // Query 2: Same semester, different section (medium priority)
        supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            faculty_1_id,
            faculty_2_id,
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            department
          `)
          .eq('academic_year', academicYear)
          .eq('department', mappedDepartment)
          .eq('semester', affectedClass.semester)
          .neq('section', affectedClass.section),

        // Query 3: Same subject type (lower priority)
        supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            faculty_1_id,
            faculty_2_id,
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            department
          `)
          .eq('academic_year', academicYear)
          .eq('department', mappedDepartment)
          .eq('subject_type', affectedClass.subject_type)
          .neq('semester', affectedClass.semester)
      ];

      const simplifiedResults = await Promise.all(simplifiedQueryPromises);
      const [exactMatchResult, sameSemesterResult, sameSubjectTypeResult] = simplifiedResults;

      // Combine simplified results
      const simplifiedMappings = [
        ...(exactMatchResult.data || []),
        ...(sameSemesterResult.data || []),
        ...(sameSubjectTypeResult.data || [])
      ];

      if (simplifiedMappings.length > 0) {
        console.log(`📚 Found ${simplifiedMappings.length} mappings in simplified table`);
        mappings = simplifiedMappings;
      } else {
        console.log('⚠️ No data in simplified table, trying original subject_faculty_mappings table');

        // Fallback to original table
        const originalQueryPromises = [
          supabase
            .from('subject_faculty_mappings')
            .select(`
              faculty_1_id,
              faculty_2_id,
              subject_code,
              subject_name,
              subject_type,
              semester,
              section,
              department
            `)
            .eq('academic_year', academicYear)
            .eq('department', mappedDepartment)
            .eq('semester', affectedClass.semester)
            .eq('section', affectedClass.section),

          supabase
            .from('subject_faculty_mappings')
            .select(`
              faculty_1_id,
              faculty_2_id,
              subject_code,
              subject_name,
              subject_type,
              semester,
              section,
              department
            `)
            .eq('academic_year', academicYear)
            .eq('department', mappedDepartment)
            .eq('semester', affectedClass.semester)
            .neq('section', affectedClass.section),

          supabase
            .from('subject_faculty_mappings')
            .select(`
              faculty_1_id,
              faculty_2_id,
              subject_code,
              subject_name,
              subject_type,
              semester,
              section,
              department
            `)
            .eq('academic_year', academicYear)
            .eq('department', mappedDepartment)
            .eq('subject_type', affectedClass.subject_type)
            .neq('semester', affectedClass.semester)
        ];

        const originalResults = await Promise.all(originalQueryPromises);
        const originalMappings = [
          ...(originalResults[0].data || []),
          ...(originalResults[1].data || []),
          ...(originalResults[2].data || [])
        ];

        if (originalMappings.length > 0) {
          console.log(`📚 Found ${originalMappings.length} mappings in original table`);
          mappings = originalMappings;
        } else {
          console.log('⚠️ No data in original table either, trying timetable_slots as final fallback');

          // Final fallback: Use timetable_slots table
          const { data: timetableData, error: timetableError } = await supabase
            .from('timetable_slots')
            .select(`
              faculty_id,
              faculty2_id,
              subject_code,
              subject_name,
              subject_type,
              semester,
              section,
              department
            `)
            .eq('academic_year', academicYear)
            .eq('department', mappedDepartment);

          if (!timetableError && timetableData && timetableData.length > 0) {
            console.log(`📚 Found ${timetableData.length} slots in timetable_slots table`);
            // Convert timetable_slots format to mapping format
            mappings = timetableData.map(slot => ({
              faculty_1_id: slot.faculty_id,
              faculty_2_id: slot.faculty2_id,
              subject_code: slot.subject_code,
              subject_name: slot.subject_name,
              subject_type: slot.subject_type,
              semester: slot.semester,
              section: slot.section,
              department: slot.department
            }));
          }
        }
      }

      // Remove duplicates based on faculty IDs and mapping details
      const uniqueMappings = mappings.filter((mapping, index, arr) => {
        return arr.findIndex(m =>
          m.faculty_1_id === mapping.faculty_1_id &&
          m.faculty_2_id === mapping.faculty_2_id &&
          m.subject_code === mapping.subject_code &&
          m.semester === mapping.semester &&
          m.section === mapping.section
        ) === index;
      });

      mappings = uniqueMappings;
      console.log(`📚 Found ${mappings.length} faculty mappings to analyze`);

      // Build qualification map with priority levels
      const qualificationMap = new Map<string, {
        faculty_id: string;
        reasons: Set<string>;
        assignments: any[];
        priority: number; // 1 = highest, 3 = lowest
      }>();

      mappings.forEach(mapping => {
        // Check both primary and secondary faculty
        [mapping.faculty_1_id, mapping.faculty_2_id].forEach((facultyId, index) => {
          if (!facultyId) return;

          const role = index === 0 ? 'primary' : 'secondary';

          if (!qualificationMap.has(facultyId)) {
            qualificationMap.set(facultyId, {
              faculty_id: facultyId,
              reasons: new Set(),
              assignments: [],
              priority: 3 // Default to lowest priority
            });
          }

          const qualification = qualificationMap.get(facultyId)!;

          // Add assignment
          qualification.assignments.push({
            semester: mapping.semester,
            section: mapping.section,
            subject_code: mapping.subject_code,
            subject_name: mapping.subject_name,
            subject_type: mapping.subject_type,
            role
          });

          // Determine qualification reasons and priority
          if (mapping.semester === affectedClass.semester && mapping.section === affectedClass.section) {
            qualification.reasons.add(`Teaches ${mapping.semester}-${mapping.section}`);
            qualification.priority = Math.min(qualification.priority, 1); // Highest priority
            console.log(`✅ EXACT MATCH: Faculty ${facultyId} teaches ${mapping.semester}-${mapping.section} (Priority 1)`);
          }

          if (mapping.semester === affectedClass.semester && mapping.section !== affectedClass.section) {
            qualification.reasons.add(`Teaches ${mapping.semester} semester`);
            qualification.priority = Math.min(qualification.priority, 2); // Medium priority
            console.log(`⚠️ SEMESTER MATCH: Faculty ${facultyId} teaches ${mapping.semester}-${mapping.section} vs needed ${affectedClass.semester}-${affectedClass.section} (Priority 2)`);
          }

          if (mapping.subject_type === affectedClass.subject_type) {
            qualification.reasons.add(`Teaches ${mapping.subject_type} subjects`);
            if (qualification.priority === 3) { // Only set if no higher priority exists
              qualification.priority = 3; // Lower priority
            }
            console.log(`📝 SUBJECT TYPE MATCH: Faculty ${facultyId} teaches ${mapping.subject_type} subjects (Priority 3)`);
          }

          if (mapping.subject_code === affectedClass.subject_code) {
            qualification.reasons.add(`Teaches same subject (${mapping.subject_code})`);
            qualification.priority = Math.min(qualification.priority, 1); // Highest priority
            console.log(`🎯 SUBJECT CODE MATCH: Faculty ${facultyId} teaches ${mapping.subject_code} (Priority 1)`);
          }
        });
      });

      console.log(`📊 Qualification breakdown:`, {
        'Priority 1 (Exact match)': Array.from(qualificationMap.values()).filter(q => q.priority === 1).length,
        'Priority 2 (Same semester)': Array.from(qualificationMap.values()).filter(q => q.priority === 2).length,
        'Priority 3 (Same subject type)': Array.from(qualificationMap.values()).filter(q => q.priority === 3).length
      });

      // Get faculty details for qualified faculties
      const qualifiedFacultyIds = Array.from(qualificationMap.keys());

      if (qualifiedFacultyIds.length === 0) {
        console.log('❌ No qualified faculties found');
        return [];
      }

      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, department, designation')
        .in('id', qualifiedFacultyIds)
        .contains('roles', ['faculty']);

      if (facultyError) {
        console.error('Error fetching faculty details:', facultyError);
        return [];
      }

      // Build final qualified faculty list
      const qualifiedFaculties: QualifiedFaculty[] = (facultyData || []).map(faculty => {
        const qualification = qualificationMap.get(faculty.id)!;

        // DEBUG: Log each faculty's qualification details
        console.log(`👤 ${faculty.full_name}:`, {
          priority: qualification.priority,
          reasons: Array.from(qualification.reasons),
          assignments: qualification.assignments.map(a => `${a.semester}-${a.section} ${a.subject_code}`)
        });

        return {
          id: faculty.id,
          full_name: faculty.full_name,
          department: faculty.department || department,
          designation: faculty.designation || 'Faculty',
          qualification_reason: Array.from(qualification.reasons).join(', '),
          teaching_assignments: qualification.assignments
        };
      });

      // Sort by qualification priority and relevance
      qualifiedFaculties.sort((a, b) => {
        const aQualification = qualificationMap.get(a.id)!;
        const bQualification = qualificationMap.get(b.id)!;

        // First priority: qualification priority level (1 = highest, 3 = lowest)
        if (aQualification.priority !== bQualification.priority) {
          return aQualification.priority - bQualification.priority;
        }

        // Second priority: number of qualification reasons (more reasons = better match)
        const aReasonCount = aQualification.reasons.size;
        const bReasonCount = bQualification.reasons.size;
        if (aReasonCount !== bReasonCount) {
          return bReasonCount - aReasonCount; // More reasons first
        }

        // Third priority: alphabetical by name
        return a.full_name.localeCompare(b.full_name);
      });

      // Filter to show only the most relevant faculties (Priority 1 and 2)
      // Be more strict: only include Priority 3 if there are NO Priority 1 or 2 faculties
      const priority1Faculties = qualifiedFaculties.filter(faculty => {
        const qualification = qualificationMap.get(faculty.id)!;
        return qualification.priority === 1; // Exact matches only
      });

      const priority2Faculties = qualifiedFaculties.filter(faculty => {
        const qualification = qualificationMap.get(faculty.id)!;
        return qualification.priority === 2; // Same semester, different section
      });

      const priority3Faculties = qualifiedFaculties.filter(faculty => {
        const qualification = qualificationMap.get(faculty.id)!;
        return qualification.priority === 3; // Same subject type only
      });

      // SMART FILTERING: Show only faculties who teach the EXACT same semester-section
      let finalFaculties: QualifiedFaculty[] = [];

      if (priority1Faculties.length > 0) {
        // Show only Priority 1: Exact semester-section matches (e.g., same 6-A class)
        finalFaculties = priority1Faculties;
        console.log(`🎯 EXACT MATCH ONLY: Using ${priority1Faculties.length} faculties who teach the same ${affectedClass.semester}-${affectedClass.section} class`);
        console.log(`📋 Qualified faculties:`, priority1Faculties.map(f => `${f.full_name} (${f.qualification_reason})`));
      } else {
        // NO FALLBACK: If no exact semester-section matches, show none
        finalFaculties = [];
        console.log(`🚫 NO EXACT MATCHES: No faculties found who teach ${affectedClass.semester}-${affectedClass.section} class, showing no qualified faculties`);
        console.log(`📊 Available but not shown:`, {
          'Priority 2 (Same semester, different sections)': priority2Faculties.length,
          'Priority 3 (Same subject type)': priority3Faculties.length
        });
      }

      console.log(`✅ Found ${qualifiedFaculties.length} total qualified faculties, showing ${finalFaculties.length} most relevant`);
      console.log(`📋 Final selection breakdown:`, {
        'Priority 1 (Exact match)': finalFaculties.filter(f => qualificationMap.get(f.id)!.priority === 1).length,
        'Priority 2 (Same semester)': finalFaculties.filter(f => qualificationMap.get(f.id)!.priority === 2).length,
        'Priority 3 (Same subject type)': finalFaculties.filter(f => qualificationMap.get(f.id)!.priority === 3).length
      });

      return finalFaculties;

    } catch (error) {
      console.error('Error getting qualified faculties:', error);
      return [];
    }
  }

  /**
   * Convert qualified faculties to substitute faculty format with availability check
   */
  static async convertToSubstituteFaculties(
    qualifiedFaculties: QualifiedFaculty[],
    day: string,
    timeSlot: string,
    date: string,
    academicYear: string = '2024-2025'
  ): Promise<SubstituteFaculty[]> {
    try {
      if (qualifiedFaculties.length === 0) return [];

      const facultyIds = qualifiedFaculties.map(f => f.id);

      // Check availability for all qualified faculties
      const [timetableResult, leaveResult, availabilityResult] = await Promise.all([
        // Check timetable conflicts
        supabase
          .from('timetable_slots')
          .select('faculty_id, faculty2_id, subject_code, subject_name, semester, section, room_number')
          .eq('day', day)
          .eq('time_slot', timeSlot)
          .eq('academic_year', academicYear)
          .or(facultyIds.map(id => `faculty_id.eq.${id},faculty2_id.eq.${id}`).join(',')),

        // Check leave status
        supabase
          .from('leave_requests')
          .select('faculty_id')
          .eq('status', 'approved')
          .lte('start_date', date)
          .gte('end_date', date)
          .in('faculty_id', facultyIds),

        // Get availability schedules
        supabase
          .from('employee_details')
          .select('id, vacant_count_by_day')
          .in('id', facultyIds)
      ]);

      const timetableConflicts = timetableResult.data || [];
      const facultyOnLeave = new Set((leaveResult.data || []).map(leave => leave.faculty_id));
      const availabilityData = availabilityResult.data || [];

      // Create conflict and availability maps
      const conflictMap = new Map();
      timetableConflicts.forEach(conflict => {
        if (conflict.faculty_id) conflictMap.set(conflict.faculty_id, conflict);
        if (conflict.faculty2_id) conflictMap.set(conflict.faculty2_id, conflict);
      });

      const availabilityMap = new Map();
      availabilityData.forEach(faculty => {
        availabilityMap.set(faculty.id, faculty.vacant_count_by_day);
      });

      // Convert to substitute faculty format
      const substituteFaculties: SubstituteFaculty[] = qualifiedFaculties.map(qualified => {
        let availabilityStatus: 'available' | 'occupied' | 'on_leave' = 'available';
        let conflictDetails = `Qualified: ${qualified.qualification_reason}`;

        // Check leave status
        if (facultyOnLeave.has(qualified.id)) {
          availabilityStatus = 'on_leave';
          conflictDetails = 'Faculty is on approved leave for this date';
        } else {
          // Check schedule availability
          const facultySchedule = availabilityMap.get(qualified.id);
          let isScheduledAvailable = false;

          if (facultySchedule && typeof facultySchedule === 'object') {
            const daySlots = facultySchedule[day];
            if (Array.isArray(daySlots)) {
              isScheduledAvailable = daySlots.includes(timeSlot);
            }
          }

          if (!isScheduledAvailable) {
            availabilityStatus = 'occupied';
            conflictDetails = `Not scheduled to be available during ${timeSlot} on ${day}`;
          } else {
            // Check timetable conflicts
            const conflict = conflictMap.get(qualified.id);
            if (conflict) {
              availabilityStatus = 'occupied';
              const role = conflict.faculty2_id === qualified.id ? 'Assistant Faculty' : 'Primary Faculty';
              conflictDetails = `${role} - Teaching ${conflict.subject_code} to ${conflict.semester}-${conflict.section}`;
            }
          }
        }

        return {
          id: qualified.id,
          full_name: qualified.full_name,
          department: qualified.department,
          designation: qualified.designation,
          availability_status: availabilityStatus,
          conflict_details: conflictDetails,
          subject_expertise: qualified.teaching_assignments.map(a => a.subject_code)
        };
      });

      return substituteFaculties;

    } catch (error) {
      console.error('Error converting qualified faculties to substitute format:', error);
      return [];
    }
  }
}
