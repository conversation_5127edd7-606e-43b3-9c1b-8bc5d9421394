<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Student Progress IA Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .loading { color: #666; }
    </style>
</head>
<body>
    <h1>🧪 Test Student Progress IA Fix</h1>
    <div id="results"></div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        const supabaseUrl = 'https://milmyotuougemocvieof.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NjI0NzQsImV4cCI6MjA1MDUzODQ3NH0.Ej5Ej6bJYJJhUvSJKJhUvSJKJhUvSJKJhUvSJKJhUvS';

        const supabase = createClient(supabaseUrl, supabaseKey);
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        async function testStudentProgressFix() {
            addResult('🔄 Starting Student Progress IA Fix Test...', 'info');

            try {
                const testUSN = '1KS23CS001';
                addResult(`📋 Testing with student USN: ${testUSN}`, 'info');

                // Step 1: Get student info (simulating StudentProgressService)
                const { data: studentRecords, error: studentError } = await supabase
                    .from('class_students')
                    .select('id, usn, student_name, department, semester, section, academic_year')
                    .eq('usn', testUSN.toUpperCase());

                if (studentError || !studentRecords || studentRecords.length === 0) {
                    addResult(`❌ Student not found: ${studentError?.message}`, 'error');
                    return;
                }

                const selectedStudent = studentRecords[0];
                addResult(`✅ Student found: ${selectedStudent.student_name} (${selectedStudent.department}-${selectedStudent.semester}-${selectedStudent.section})`, 'success');

                // Step 2: Test IA marks fetch with academic year variations (simulating the fix)
                addResult('🔍 Testing IA marks fetch with academic year variations...', 'info');

                const academicYearVariations = [
                    selectedStudent.academic_year, // "2024-2025"
                    "2025-2026", // Current year where IA data is stored
                    "2024-25",
                    "2025-26"
                ];

                let iaData = null;
                let foundWithYear = null;

                for (const yearVariant of academicYearVariations) {
                    const { data: tempData, error: tempError } = await supabase
                        .from('internal_assessments')
                        .select('*')
                        .eq('student_id', selectedStudent.id)
                        .eq('department', selectedStudent.department.toLowerCase())
                        .eq('semester', selectedStudent.semester)
                        .eq('academic_year', yearVariant);

                    if (!tempError && tempData && tempData.length > 0) {
                        iaData = tempData;
                        foundWithYear = yearVariant;
                        addResult(`✅ Found ${tempData.length} IA records with academic year: ${yearVariant}`, 'success');
                        break;
                    }
                }

                if (!iaData || iaData.length === 0) {
                    // Try fallback approach
                    addResult('🔍 No records with strict filters, trying fallback approach...', 'info');
                    
                    const { data: fallbackData, error: fallbackError } = await supabase
                        .from('internal_assessments')
                        .select('*')
                        .eq('student_id', selectedStudent.id);

                    if (!fallbackError && fallbackData && fallbackData.length > 0) {
                        // Filter by department and semester
                        iaData = fallbackData.filter(record => {
                            const recordDept = (record.department || '').toLowerCase();
                            const targetDept = selectedStudent.department.toLowerCase();
                            const deptMatch = recordDept === targetDept;
                            const semesterMatch = record.semester === selectedStudent.semester;
                            return deptMatch && semesterMatch;
                        });
                        
                        if (iaData.length > 0) {
                            addResult(`✅ Found ${iaData.length} IA records using fallback approach`, 'success');
                        }
                    }
                }

                if (!iaData || iaData.length === 0) {
                    addResult('❌ No IA records found with any approach', 'error');
                    return;
                }

                // Step 3: Test data transformation (simulating the fixed logic)
                addResult('🔄 Testing data transformation with theory_marks...', 'info');

                const transformedData = iaData.map(record => {
                    const ia1 = record.ia1_marks !== null && record.ia1_marks !== undefined ? Number(record.ia1_marks) : 0;
                    const ia2 = record.ia2_marks !== null && record.ia2_marks !== undefined ? Number(record.ia2_marks) : 0;
                    const ia3 = record.ia3_marks !== null && record.ia3_marks !== undefined ? Number(record.ia3_marks) : 0;
                    const assignment = record.assignment_marks !== null && record.assignment_marks !== undefined ? Number(record.assignment_marks) : 0;
                    const theory = record.theory_marks !== null && record.theory_marks !== undefined ? Number(record.theory_marks) : 0;

                    const totalMarks = ia1 + ia2 + ia3 + assignment + theory;
                    const iaCount = [record.ia1_marks, record.ia2_marks, record.ia3_marks].filter(mark => mark !== null && mark !== undefined).length;
                    const averageMarks = iaCount > 0 ? (ia1 + ia2 + ia3) / iaCount : 0;

                    return {
                        subject_code: record.subject_code,
                        ia1_marks: record.ia1_marks !== null && record.ia1_marks !== undefined ? Number(record.ia1_marks) : undefined,
                        ia2_marks: record.ia2_marks !== null && record.ia2_marks !== undefined ? Number(record.ia2_marks) : undefined,
                        ia3_marks: record.ia3_marks !== null && record.ia3_marks !== undefined ? Number(record.ia3_marks) : undefined,
                        assignment_marks: record.assignment_marks !== null && record.assignment_marks !== undefined ? Number(record.assignment_marks) : undefined,
                        theory_marks: record.theory_marks !== null && record.theory_marks !== undefined ? Number(record.theory_marks) : undefined,
                        total_marks: totalMarks,
                        average_marks: Math.round(averageMarks * 100) / 100
                    };
                });

                // Step 4: Display results in a table
                addResult('✅ Data transformation successful! Here are the results:', 'success');

                let tableHTML = `
                    <table>
                        <thead>
                            <tr>
                                <th>Subject Code</th>
                                <th>IA1 (/25)</th>
                                <th>IA2 (/25)</th>
                                <th>IA3 (/25)</th>
                                <th>Assignment (/10)</th>
                                <th>Theory (/20)</th>
                                <th>Total</th>
                                <th>Average</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                transformedData.forEach(data => {
                    tableHTML += `
                        <tr>
                            <td><strong>${data.subject_code}</strong></td>
                            <td>${data.ia1_marks !== undefined ? data.ia1_marks : '-'}</td>
                            <td>${data.ia2_marks !== undefined ? data.ia2_marks : '-'}</td>
                            <td>${data.ia3_marks !== undefined ? data.ia3_marks : '-'}</td>
                            <td>${data.assignment_marks !== undefined ? data.assignment_marks : '-'}</td>
                            <td>${data.theory_marks !== undefined ? data.theory_marks : '-'}</td>
                            <td><strong>${data.total_marks}</strong></td>
                            <td><strong>${data.average_marks}</strong></td>
                        </tr>
                    `;
                });

                tableHTML += '</tbody></table>';
                addResult(tableHTML, 'success');

                addResult('🎉 Test completed successfully! The fix is working correctly.', 'success');
                addResult('✅ IA marks should now display properly in the Student Proctoring feature.', 'success');

            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }

        // Run the test when page loads
        testStudentProgressFix();
    </script>
</body>
</html>
