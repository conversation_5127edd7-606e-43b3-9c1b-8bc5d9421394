/**
 * Utility functions for subject code handling and conversion
 */

/**
 * Cache for subject mappings to avoid repeated database calls
 */
let subjectMappingsCache: Record<string, string> = {};
let mappingsCacheLoaded = false;

/**
 * Load subject mappings from database
 */
async function loadSubjectMappingsFromDB(): Promise<void> {
  if (mappingsCacheLoaded) return;

  try {
    console.log('🔄 Loading subject mappings from database (utils)...');

    const { supabase } = await import('@/integrations/supabase/client');
    const { data: subjects, error } = await supabase
      .from('subjects')
      .select('subject_code, subject_short_id')
      .not('subject_short_id', 'is', null);

    if (error) {
      console.error('❌ Error loading subject mappings (utils):', error);
      return;
    }

    // Build the mappings cache
    subjects?.forEach(subject => {
      // Only add mapping if subject_short_id is different from subject_code (valid short ID)
      if (subject.subject_short_id && subject.subject_short_id !== subject.subject_code) {
        subjectMappingsCache[subject.subject_code] = subject.subject_short_id;
      }
    });

    console.log(`✅ Loaded ${Object.keys(subjectMappingsCache).length} subject mappings from database (utils):`, subjectMappingsCache);
    mappingsCacheLoaded = true;

  } catch (error) {
    console.error('❌ Failed to load subject mappings (utils):', error);
  }
}

/**
 * Convert subject code to short ID (synchronous version for React components)
 * This uses cached mappings or fallback mappings
 */
export function convertToShortId(subjectCode: string): string {
  // Check cached database mappings first
  const dbMapping = subjectMappingsCache[subjectCode];
  if (dbMapping) {
    return dbMapping;
  }

  // Fallback to hardcoded mappings for special cases
  const fallbackMappings: Record<string, string> = {
    'BCS405A': 'DMS',  // Known working mapping
    'SKILL LAB': 'SKILL Lab',
    'TUTORIAL': 'TUTORIAL',
    'SEMINAR': 'SEMINAR',
    'PROJECT': 'PROJECT'
  };

  return fallbackMappings[subjectCode] || subjectCode;
}

/**
 * Convert subject code to short ID (async version for export functions)
 * This fetches mappings from the database dynamically
 */
export async function convertToShortIdAsync(subjectCode: string): Promise<string> {
  // Load mappings from database if not already loaded
  await loadSubjectMappingsFromDB();

  // Check database mappings first
  const dbMapping = subjectMappingsCache[subjectCode];
  if (dbMapping) {
    return dbMapping;
  }

  // Fallback to hardcoded mappings for special cases
  const fallbackMappings: Record<string, string> = {
    'BCS405A': 'DMS',  // Known working mapping
    'SKILL LAB': 'SKILL Lab',
    'TUTORIAL': 'TUTORIAL',
    'SEMINAR': 'SEMINAR',
    'PROJECT': 'PROJECT'
  };

  return fallbackMappings[subjectCode] || subjectCode;
}

/**
 * Get the display subject code (prefer short ID, fallback to conversion, then original code)
 * Synchronous version for React components
 */
export function getDisplaySubjectCode(subjectCode: string, subjectShortId?: string): string {
  // Use subject_short_id only if it's different from subject_code (i.e., it's actually a short ID)
  // If subject_short_id is the same as subject_code, it means the database has wrong data
  const isValidShortId = subjectShortId && subjectShortId !== subjectCode;
  return isValidShortId ? subjectShortId : convertToShortId(subjectCode);
}

/**
 * Get the display subject code (async version for export functions)
 */
export async function getDisplaySubjectCodeAsync(subjectCode: string, subjectShortId?: string): Promise<string> {
  // Use subject_short_id only if it's different from subject_code (i.e., it's actually a short ID)
  // If subject_short_id is the same as subject_code, it means the database has wrong data
  const isValidShortId = subjectShortId && subjectShortId !== subjectCode;
  return isValidShortId ? subjectShortId : await convertToShortIdAsync(subjectCode);
}

/**
 * Preload subject mappings from database for web interface
 * Call this early in the app lifecycle to populate the cache
 */
export async function preloadSubjectMappings(): Promise<void> {
  await loadSubjectMappingsFromDB();
}

/**
 * Check if a subject code has a short ID mapping
 */
export function hasShortIdMapping(subjectCode: string): boolean {
  return convertToShortId(subjectCode) !== subjectCode;
}
