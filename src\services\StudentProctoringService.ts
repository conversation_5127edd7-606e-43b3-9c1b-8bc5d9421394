import { supabase } from '@/integrations/supabase/client';

export interface UnassignedStudent {
  id: string;
  usn: string;
  student_name: string;
  email?: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
}

export interface ProctorStudent {
  id: string;
  student_usn: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  assigned_date: string;
  notes?: string;
  student_email?: string;
  student_mobile?: string;
}

export interface ProctoringAssignment {
  faculty_id: string;
  student_usn: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  notes?: string;
}

export class StudentProctoringService {
  /**
   * Get all unassigned students (students not assigned to any faculty as proctor)
   */
  static async getUnassignedStudents(
    department?: string,
    semester?: string,
    section?: string,
    academicYear: string = '2024-2025'  // Updated to match your database format
  ): Promise<UnassignedStudent[]> {
    try {
      console.log('🔍 Fetching unassigned students with params:', {
        department,
        semester,
        section,
        academicYear
      });

      const { data, error } = await supabase.rpc('get_unassigned_students', {
        p_department: department || null,
        p_semester: semester || null,
        p_section: section || null,
        p_academic_year: academicYear
      });

      if (error) {
        console.error('❌ Error fetching unassigned students:', error);
        throw error;
      }

      console.log('✅ Unassigned students data received:', data?.length || 0, 'students');
      return data || [];
    } catch (error) {
      console.error('❌ Error in getUnassignedStudents:', error);

      // Fallback: Try to get students directly from class_students table
      console.log('🔄 Attempting fallback method...');
      try {
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('class_students')
          .select('id, usn, student_name, email, department, semester, section, academic_year')
          .eq('academic_year', academicYear)
          .order('department, semester, section, student_name');

        if (fallbackError) {
          console.error('❌ Fallback method also failed:', fallbackError);
          throw fallbackError;
        }

        console.log('✅ Fallback data received:', fallbackData);
        return fallbackData?.map(student => ({
          id: student.id,
          usn: student.usn,
          student_name: student.student_name,
          email: student.email || '',
          department: student.department,
          semester: student.semester,
          section: student.section,
          academic_year: student.academic_year
        })) || [];
      } catch (fallbackError) {
        console.error('❌ Both methods failed:', fallbackError);
        throw error; // Throw original error
      }
    }
  }

  /**
   * Get students assigned to a specific faculty as proctor
   */
  static async getFacultyProctorStudents(
    facultyId: string,
    academicYear: string = '2024-2025'  // Updated to match your database format
  ): Promise<ProctorStudent[]> {
    try {
      const { data, error } = await supabase.rpc('get_faculty_proctor_students', {
        p_faculty_id: facultyId,
        p_academic_year: academicYear
      });

      if (error) {
        console.error('Error fetching faculty proctor students:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error in getFacultyProctorStudents:', error);
      throw error;
    }
  }

  /**
   * Assign selected students to a faculty as proctor
   */
  static async assignStudentsToFaculty(
    assignments: ProctoringAssignment[],
    assignedBy: string
  ): Promise<void> {
    try {
      const records = assignments.map(assignment => ({
        faculty_id: assignment.faculty_id,
        student_usn: assignment.student_usn,
        student_name: assignment.student_name,
        department: assignment.department,
        semester: assignment.semester,
        section: assignment.section,
        academic_year: assignment.academic_year,
        notes: assignment.notes,
        assigned_by: assignedBy,
        is_active: true
      }));

      const { error } = await supabase
        .from('faculty_student_proctoring')
        .insert(records);

      if (error) {
        console.error('Error assigning students to faculty:', error);

        // Handle duplicate assignment error with better message
        if (error.code === '23505') {
          const duplicateMatch = error.message.match(/\(([^)]+)\)/);
          const duplicateInfo = duplicateMatch ? duplicateMatch[1] : 'some students';
          throw new Error(`Some students are already assigned to a proctor: ${duplicateInfo}. Please refresh the page to see updated assignments.`);
        }

        throw error;
      }

      console.log(`✅ Successfully assigned ${assignments.length} students to faculty`);
    } catch (error) {
      console.error('Error in assignStudentsToFaculty:', error);
      throw error;
    }
  }

  /**
   * Remove a student from faculty's proctoring list
   */
  static async removeStudentFromProctoring(
    proctoringId: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('faculty_student_proctoring')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', proctoringId);

      if (error) {
        console.error('Error removing student from proctoring:', error);
        throw error;
      }

      console.log('✅ Successfully removed student from proctoring');
    } catch (error) {
      console.error('Error in removeStudentFromProctoring:', error);
      throw error;
    }
  }

  /**
   * Update proctoring notes for a student
   */
  static async updateProctoringNotes(
    proctoringId: string,
    notes: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('faculty_student_proctoring')
        .update({
          notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', proctoringId);

      if (error) {
        console.error('Error updating proctoring notes:', error);
        throw error;
      }

      console.log('✅ Successfully updated proctoring notes');
    } catch (error) {
      console.error('Error in updateProctoringNotes:', error);
      throw error;
    }
  }

  /**
   * Search students by name or USN
   */
  static async searchStudents(
    query: string,
    department?: string,
    semester?: string,
    section?: string,
    academicYear: string = '2024-25'
  ): Promise<UnassignedStudent[]> {
    try {
      // Get all unassigned students first
      const allStudents = await this.getUnassignedStudents(
        department,
        semester,
        section,
        academicYear
      );

      // Filter by search query (name or USN)
      const searchTerm = query.toLowerCase().trim();
      if (!searchTerm) {
        return allStudents;
      }

      return allStudents.filter(student =>
        student.student_name.toLowerCase().includes(searchTerm) ||
        student.usn.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('Error in searchStudents:', error);
      throw error;
    }
  }

  /**
   * Get proctoring statistics for a faculty
   */
  static async getFacultyProctoringStats(
    facultyId: string,
    academicYear: string = '2024-2025'  // Updated to match your database format
  ): Promise<{
    totalStudents: number;
    departmentBreakdown: { [department: string]: number };
    semesterBreakdown: { [semester: string]: number };
  }> {
    try {
      const students = await this.getFacultyProctorStudents(facultyId, academicYear);

      const stats = {
        totalStudents: students.length,
        departmentBreakdown: {} as { [department: string]: number },
        semesterBreakdown: {} as { [semester: string]: number }
      };

      students.forEach(student => {
        // Department breakdown
        stats.departmentBreakdown[student.department] =
          (stats.departmentBreakdown[student.department] || 0) + 1;

        // Semester breakdown
        stats.semesterBreakdown[student.semester] =
          (stats.semesterBreakdown[student.semester] || 0) + 1;
      });

      return stats;
    } catch (error) {
      console.error('Error in getFacultyProctoringStats:', error);
      throw error;
    }
  }
}
