
import { useState } from "react";
import { Upload, FileText, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

interface FileUploaderProps {
  file: File | null;
  setFile: (file: File | null) => void;
  uploadProgress: number;
  uploading: boolean;
  uploadComplete: boolean;
  onUploadClick: () => void;
}

export const FileUploader = ({ 
  file, 
  setFile, 
  uploadProgress, 
  uploading, 
  uploadComplete,
  onUploadClick 
}: FileUploaderProps) => {
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      
      // Check if file is CSV or Excel
      const validTypes = [
        'text/csv', 
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      
      if (!validTypes.includes(selectedFile.type)) {
        return;
      }
      
      setFile(selectedFile);
    }
  };

  return (
    <div className="space-y-4">
      <div className={`border-2 border-dashed rounded-lg p-6 text-center ${file ? 'border-primary' : 'border-border'}`}>
        {!file ? (
          <div className="flex flex-col items-center">
            <Upload className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm font-medium mb-1">Drag and drop your file here</p>
            <p className="text-xs text-muted-foreground mb-4">
              or browse to upload (CSV or Excel format)
            </p>
            <Button 
              variant="secondary" 
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              Select File
            </Button>
            <input
              id="fileInput"
              type="file"
              accept=".csv,.xls,.xlsx"
              className="hidden"
              onChange={handleFileChange}
            />
          </div>
        ) : (
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-primary/10 rounded">
              <FileText className="h-6 w-6 text-primary" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium truncate">{file.name}</p>
              <p className="text-xs text-muted-foreground">
                {(file.size / 1024).toFixed(2)} KB
              </p>
            </div>
            {uploadComplete ? (
              <div className="p-2 bg-accent/10 rounded">
                <Check className="h-5 w-5 text-accent" />
              </div>
            ) : (
              <Button
                variant="default"
                size="sm"
                onClick={() => setFile(null)}
              >
                Change
              </Button>
            )}
          </div>
        )}
      </div>
      
      {file && !uploadComplete && (
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm">Upload Progress</span>
            <span className="text-xs text-muted-foreground">
              {uploadProgress}%
            </span>
          </div>
          <Progress value={uploadProgress} className="h-2" />
          
          <Button 
            className="w-full" 
            onClick={onUploadClick} 
            disabled={uploading}
          >
            {uploading ? "Uploading..." : "Upload File"}
          </Button>
        </div>
      )}
    </div>
  );
};
