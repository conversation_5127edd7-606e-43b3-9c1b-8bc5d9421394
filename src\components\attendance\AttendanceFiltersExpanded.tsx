
import React from 'react';
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Filter } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SubjectOption } from "./types";

interface AttendanceFiltersExpandedProps {
  department: string;
  setDepartment: (value: string) => void;
  semester: string;
  setSemester: (value: string) => void;
  section: string;
  setSection: (value: string) => void;
  selectedSubject: string;
  setSelectedSubject: (value: string) => void;
  subjects: SubjectOption[];
  activeView: string;
  attendanceFilter: {
    enabled: boolean;
    type: "greater" | "less";
    value: number;
  };
  setAttendanceFilter: React.Dispatch<React.SetStateAction<{
    enabled: boolean;
    type: "greater" | "less";
    value: number;
  }>>;
  departments: { value: string; label: string; }[];
  semesters: { value: string; label: string; }[];
  sections: { value: string; label: string; }[];
}

const AttendanceFiltersExpanded: React.FC<AttendanceFiltersExpandedProps> = ({
  department,
  setDepartment,
  semester,
  setSemester,
  section,
  setSection,
  selectedSubject,
  setSelectedSubject,
  subjects,
  activeView,
  attendanceFilter,
  setAttendanceFilter,
  departments,
  semesters,
  sections
}) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="department">Department</Label>
          <Select value={department} onValueChange={setDepartment}>
            <SelectTrigger>
              <SelectValue placeholder="Select Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Departments</SelectLabel>
                {departments.map((dept) => (
                  <SelectItem key={dept.value} value={dept.value}>
                    {dept.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="semester">Semester</Label>
          <Select value={semester} onValueChange={setSemester}>
            <SelectTrigger>
              <SelectValue placeholder="Select Semester" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Semesters</SelectLabel>
                {semesters.map((sem) => (
                  <SelectItem key={sem.value} value={sem.value}>
                    {sem.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="section">Section</Label>
          <Select value={section} onValueChange={setSection}>
            <SelectTrigger>
              <SelectValue placeholder="Select Section" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Sections</SelectLabel>
                {sections.map((sec) => (
                  <SelectItem key={sec.value} value={sec.value}>
                    {sec.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div>
        <Label htmlFor="subject">Subject</Label>
        <Select value={selectedSubject} onValueChange={setSelectedSubject}>
          <SelectTrigger>
            <SelectValue placeholder="Select Subject" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Assigned Subjects</SelectLabel>
              {subjects.map((subject) => (
                <SelectItem key={subject.id} value={subject.id}>
                  {subject.code} - {subject.name}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
      
      {activeView === 'attendance' && attendanceFilter?.enabled && (
        <div className="bg-secondary/20 p-4 rounded-md">
          <h3 className="text-sm font-medium mb-2">Filter Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="filterType">Filter Type</Label>
              <Select 
                value={attendanceFilter.type} 
                onValueChange={(val: "greater" | "less") => setAttendanceFilter(prev => ({...prev, type: val}))}
              >
                <SelectTrigger id="filterType">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="greater">Greater than or equal</SelectItem>
                  <SelectItem value="less">Less than</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="filterValue">Percentage</Label>
              <Input 
                id="filterValue"
                type="number" 
                min="0" 
                max="100"
                value={attendanceFilter.value}
                onChange={(e) => setAttendanceFilter(prev => ({...prev, value: parseInt(e.target.value)}))}
                className="w-full"
              />
            </div>
            
            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => setAttendanceFilter(prev => ({...prev, enabled: !prev.enabled}))}
                className="w-full"
              >
                <Filter className="h-4 w-4 mr-2" />
                {attendanceFilter.enabled ? 'Disable Filter' : 'Enable Filter'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AttendanceFiltersExpanded;
