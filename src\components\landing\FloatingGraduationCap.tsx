import React from 'react';
import { GraduationCap } from 'lucide-react';

interface FloatingGraduationCapProps {
  /** Animation pattern: 'figure8', 'circular', 'drift', 'bounce' */
  pattern?: 'figure8' | 'circular' | 'drift' | 'bounce';
  /** Animation duration in seconds (8-15 recommended) */
  duration?: number;
  /** Size of the graduation cap icon */
  size?: number;
  /** Opacity level (0.6-0.8 recommended) */
  opacity?: number;
  /** Color theme: 'primary', 'accent', 'secondary' */
  colorTheme?: 'primary' | 'accent' | 'secondary';
  /** Animation delay in seconds */
  delay?: number;
  /** Starting position offset */
  startPosition?: { x: number; y: number };
  /** Z-index for layering */
  zIndex?: number;
}

/**
 * Floating Graduation Cap Component
 * Creates smooth, organic floating animations for graduation cap icons
 * Supports multiple animation patterns and customization options
 */
export const FloatingGraduationCap: React.FC<FloatingGraduationCapProps> = ({
  pattern = 'figure8',
  duration = 12,
  size = 28,
  opacity = 0.7,
  colorTheme = 'primary',
  delay = 0,
  startPosition = { x: 0, y: 0 },
  zIndex = 10
}) => {
  // Generate unique animation name for this instance
  const animationId = `float-${pattern}-${Math.random().toString(36).substr(2, 9)}`;

  // Color mapping based on theme
  const colorClasses = {
    primary: 'text-primary',
    accent: 'text-accent', 
    secondary: 'text-secondary'
  };

  // Animation keyframes based on pattern - FULL VIEWPORT MOVEMENT
  const getAnimationKeyframes = () => {
    // Use viewport-relative movements for full screen scatter
    const vwMovement = 15 + Math.random() * 20; // 15-35vw movement
    const vhMovement = 10 + Math.random() * 15; // 10-25vh movement
    const smallVwMovement = 5 + Math.random() * 10; // 5-15vw small movement
    const smallVhMovement = 3 + Math.random() * 8; // 3-11vh small movement

    switch (pattern) {
      case 'figure8':
        return `
          @keyframes ${animationId} {
            0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); opacity: ${opacity}; }
            12.5% { transform: translate(calc(-50% + ${vwMovement}vw), calc(-50% - ${smallVhMovement}vh)) rotate(45deg) scale(1.1); opacity: ${opacity * 0.8}; }
            25% { transform: translate(calc(-50% + ${vwMovement * 1.3}vw), -50%) rotate(90deg) scale(1); opacity: ${opacity}; }
            37.5% { transform: translate(calc(-50% + ${vwMovement}vw), calc(-50% + ${smallVhMovement}vh)) rotate(135deg) scale(0.9); opacity: ${opacity * 1.2}; }
            50% { transform: translate(-50%, calc(-50% + ${vhMovement}vh)) rotate(180deg) scale(1); opacity: ${opacity}; }
            62.5% { transform: translate(calc(-50% - ${vwMovement}vw), calc(-50% + ${smallVhMovement}vh)) rotate(225deg) scale(1.1); opacity: ${opacity * 0.8}; }
            75% { transform: translate(calc(-50% - ${vwMovement * 1.3}vw), -50%) rotate(270deg) scale(1); opacity: ${opacity}; }
            87.5% { transform: translate(calc(-50% - ${vwMovement}vw), calc(-50% - ${smallVhMovement}vh)) rotate(315deg) scale(0.9); opacity: ${opacity * 1.2}; }
            100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); opacity: ${opacity}; }
          }
        `;

      case 'circular':
        return `
          @keyframes ${animationId} {
            0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); opacity: ${opacity}; }
            25% { transform: translate(calc(-50% + ${vwMovement * 0.8}vw), calc(-50% - ${vhMovement * 0.8}vh)) rotate(90deg) scale(1.05); opacity: ${opacity * 0.7}; }
            50% { transform: translate(-50%, calc(-50% - ${vhMovement * 1.5}vh)) rotate(180deg) scale(0.95); opacity: ${opacity * 1.3}; }
            75% { transform: translate(calc(-50% - ${vwMovement * 0.8}vw), calc(-50% - ${vhMovement * 0.8}vh)) rotate(270deg) scale(1.05); opacity: ${opacity * 0.7}; }
            100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); opacity: ${opacity}; }
          }
        `;

      case 'drift':
        return `
          @keyframes ${animationId} {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(1); opacity: ${opacity}; }
            16% { transform: translate(calc(-50% + ${vwMovement}vw), calc(-50% - ${smallVhMovement}vh)) rotate(15deg) scale(1.05); opacity: ${opacity * 0.8}; }
            33% { transform: translate(calc(-50% + ${smallVwMovement}vw), calc(-50% - ${vhMovement}vh)) rotate(-10deg) scale(0.95); opacity: ${opacity * 1.2}; }
            50% { transform: translate(calc(-50% - ${smallVwMovement * 1.5}vw), calc(-50% - ${smallVhMovement * 1.5}vh)) rotate(20deg) scale(1.1); opacity: ${opacity * 0.9}; }
            66% { transform: translate(calc(-50% - ${vwMovement * 0.8}vw), calc(-50% + ${smallVhMovement * 0.5}vh)) rotate(-15deg) scale(0.9); opacity: ${opacity * 1.1}; }
            83% { transform: translate(calc(-50% - ${smallVwMovement}vw), calc(-50% + ${smallVhMovement * 1.5}vh)) rotate(10deg) scale(1.05); opacity: ${opacity * 0.85}; }
          }
        `;

      case 'bounce':
        return `
          @keyframes ${animationId} {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(1); opacity: ${opacity}; }
            25% { transform: translate(calc(-50% + ${smallVwMovement * 1.5}vw), calc(-50% - ${vhMovement * 1.2}vh)) rotate(10deg) scale(1.2); opacity: ${opacity * 0.6}; }
            50% { transform: translate(-50%, calc(-50% - ${smallVhMovement}vh)) rotate(0deg) scale(0.8); opacity: ${opacity * 1.4}; }
            75% { transform: translate(calc(-50% - ${smallVwMovement * 1.5}vw), calc(-50% - ${vhMovement * 1.2}vh)) rotate(-10deg) scale(1.2); opacity: ${opacity * 0.6}; }
          }
        `;

      default:
        return '';
    }
  };

  return (
    <>
      {/* Inject keyframes into the document */}
      <style>
        {getAnimationKeyframes()}
      </style>

      {/* Floating Graduation Cap - Positioned anywhere on screen */}
      <div
        className="fixed pointer-events-none"
        style={{
          // FULL VIEWPORT POSITIONING: Use percentage-based positioning for full scatter
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          transform: 'translate(-50%, -50%)', // Center the cap on its position
          zIndex,
          animation: `${animationId} ${duration}s ease-in-out infinite`,
          animationDelay: `${delay}s`,
          opacity,
          filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))',
          // Respect user's motion preferences
          animationPlayState: 'var(--animation-play-state, running)'
        }}
      >
        <div className="relative">
          {/* Subtle glow effect */}
          <div
            className={`absolute inset-0 ${colorClasses[colorTheme]} blur-sm opacity-30`}
            style={{ transform: 'scale(1.2)' }}
          >
            <GraduationCap size={size} />
          </div>

          {/* Main graduation cap icon */}
          <GraduationCap
            size={size}
            className={`relative ${colorClasses[colorTheme]} transition-all duration-300`}
          />
        </div>
      </div>
    </>
  );
};

/**
 * Floating Graduation Cap Fog Component
 * Creates many small graduation caps scattered like fog in the background
 * Generates random positions and animations for atmospheric effect
 */
export const FloatingGraduationCapFog: React.FC = () => {
  // Generate random positions and properties for many caps scattered everywhere
  const generateRandomCaps = (count: number, sizeRange: { min: number; max: number }, opacityRange: { min: number; max: number }) => {
    const caps = [];
    const patterns: Array<'figure8' | 'circular' | 'drift' | 'bounce'> = ['figure8', 'circular', 'drift', 'bounce'];
    const colorThemes: Array<'primary' | 'accent' | 'secondary'> = ['primary', 'accent', 'secondary'];

    for (let i = 0; i < count; i++) {
      caps.push({
        id: i,
        pattern: patterns[Math.floor(Math.random() * patterns.length)],
        duration: 8 + Math.random() * 15, // 8-23 seconds (longer for more variety)
        size: sizeRange.min + Math.random() * (sizeRange.max - sizeRange.min), // Variable size range
        opacity: opacityRange.min + Math.random() * (opacityRange.max - opacityRange.min), // Variable opacity
        colorTheme: colorThemes[Math.floor(Math.random() * colorThemes.length)],
        delay: Math.random() * 25, // 0-25 seconds delay (more staggered)
        startPosition: {
          // FULL VIEWPORT SCATTER: Spread across entire screen width and height
          x: Math.random() * window.innerWidth - window.innerWidth / 2, // Full width spread
          y: Math.random() * window.innerHeight - window.innerHeight / 2, // Full height spread
        },
        // Additional positioning for edge areas
        edgePosition: {
          left: Math.random() * 100, // 0-100% from left edge
          top: Math.random() * 100,  // 0-100% from top edge
        },
        zIndex: 1 + Math.floor(Math.random() * 4), // z-index 1-4 (more layers)
      });
    }
    return caps;
  };

  // Generate caps with guaranteed full-screen distribution
  const generateScatteredCaps = (count: number, sizeRange: { min: number; max: number }, opacityRange: { min: number; max: number }) => {
    const caps = [];
    const patterns: Array<'figure8' | 'circular' | 'drift' | 'bounce'> = ['figure8', 'circular', 'drift', 'bounce'];
    const colorThemes: Array<'primary' | 'accent' | 'secondary'> = ['primary', 'accent', 'secondary'];

    // Divide screen into grid sections to ensure even distribution
    const gridCols = Math.ceil(Math.sqrt(count));
    const gridRows = Math.ceil(count / gridCols);

    for (let i = 0; i < count; i++) {
      const gridX = i % gridCols;
      const gridY = Math.floor(i / gridCols);

      // Calculate base position within grid cell, then add randomness
      const baseLeft = (gridX / gridCols) * 100; // Base percentage
      const baseTop = (gridY / gridRows) * 100;

      // Add randomness within the grid cell
      const cellWidth = 100 / gridCols;
      const cellHeight = 100 / gridRows;
      const randomOffsetX = (Math.random() - 0.5) * cellWidth * 0.8; // 80% of cell width
      const randomOffsetY = (Math.random() - 0.5) * cellHeight * 0.8; // 80% of cell height

      caps.push({
        id: i,
        pattern: patterns[Math.floor(Math.random() * patterns.length)],
        duration: 8 + Math.random() * 15,
        size: sizeRange.min + Math.random() * (sizeRange.max - sizeRange.min),
        opacity: opacityRange.min + Math.random() * (opacityRange.max - opacityRange.min),
        colorTheme: colorThemes[Math.floor(Math.random() * colorThemes.length)],
        delay: Math.random() * 25,
        // Ensure caps are distributed across entire viewport
        position: {
          left: Math.max(5, Math.min(95, baseLeft + randomOffsetX)), // Keep within 5-95% range
          top: Math.max(5, Math.min(95, baseTop + randomOffsetY)),   // Keep within 5-95% range
        },
        zIndex: 1 + Math.floor(Math.random() * 4),
      });
    }
    return caps;
  };

  // Generate MUCH MORE caps with full-screen scatter for dense fog effect
  const desktopCaps = [
    ...generateScatteredCaps(120, { min: 4, max: 8 }, { min: 0.05, max: 0.12 }), // Micro dots everywhere
    ...generateScatteredCaps(80, { min: 8, max: 12 }, { min: 0.08, max: 0.18 }), // Tiny dots everywhere
    ...generateScatteredCaps(60, { min: 12, max: 16 }, { min: 0.12, max: 0.25 }), // Small caps everywhere
    ...generateScatteredCaps(40, { min: 16, max: 22 }, { min: 0.15, max: 0.3 }), // Medium caps everywhere
    ...generateScatteredCaps(20, { min: 22, max: 28 }, { min: 0.18, max: 0.35 }), // Larger caps scattered
  ];

  const tabletCaps = [
    ...generateScatteredCaps(80, { min: 4, max: 8 }, { min: 0.05, max: 0.12 }), // Micro dots
    ...generateScatteredCaps(50, { min: 8, max: 12 }, { min: 0.08, max: 0.18 }), // Tiny dots
    ...generateScatteredCaps(35, { min: 12, max: 16 }, { min: 0.12, max: 0.25 }), // Small caps
    ...generateScatteredCaps(25, { min: 16, max: 20 }, { min: 0.15, max: 0.28 }), // Medium caps
  ];

  const mobileCaps = [
    ...generateScatteredCaps(50, { min: 4, max: 8 }, { min: 0.05, max: 0.12 }), // Micro dots
    ...generateScatteredCaps(30, { min: 8, max: 12 }, { min: 0.08, max: 0.18 }), // Tiny dots
    ...generateScatteredCaps(20, { min: 12, max: 16 }, { min: 0.12, max: 0.22 }), // Small caps
  ];

  return (
    <>
      {/* CSS for respecting reduced motion preferences and responsive behavior */}
      <style>
        {`
          @media (prefers-reduced-motion: reduce) {
            :root {
              --animation-play-state: paused;
            }
          }

          @media (prefers-reduced-motion: no-preference) {
            :root {
              --animation-play-state: running;
            }
          }

          /* Responsive fog density */
          @media (max-width: 1024px) {
            .floating-fog-desktop {
              display: none;
            }
          }

          @media (max-width: 768px) {
            .floating-fog-tablet {
              display: none;
            }
          }

          @media (max-width: 640px) {
            .floating-fog-mobile-hidden {
              display: none;
            }
          }

          /* Fog container styling */
          .graduation-cap-fog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
          }

          /* Enhanced fog layers with different blur effects - More layers for density */
          .fog-layer-1 {
            filter: blur(0.3px);
          }

          .fog-layer-2 {
            filter: blur(0.8px);
          }

          .fog-layer-3 {
            filter: blur(1.3px);
          }

          .fog-layer-4 {
            filter: blur(1.8px);
          }

          .fog-layer-5 {
            filter: blur(2.3px);
          }

          /* Subtle gradient overlay for depth */
          .fog-gradient-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
              ellipse at center,
              transparent 30%,
              rgba(var(--background), 0.1) 70%,
              rgba(var(--background), 0.3) 100%
            );
            pointer-events: none;
          }
        `}
      </style>

      {/* Graduation Cap Fog Container */}
      <div className="graduation-cap-fog">
        {/* Fog Gradient Overlay for Depth */}
        <div className="fog-gradient-overlay"></div>

        {/* Desktop Fog - High Density with Layered Blur */}
        <div className="floating-fog-desktop">
          {/* Layer 1 - Sharpest (closest) - More caps */}
          <div className="fog-layer-1">
            {desktopCaps.slice(0, 120).map((cap, index) => (
              <div
                key={`desktop-layer1-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex + 2,
                  animation: `float-${cap.pattern}-${cap.id} ${cap.duration}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity,
                  filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-${cap.pattern}-${cap.id} {
                      0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
                      25% { transform: translate(calc(-50% + ${5 + Math.random() * 10}vw), calc(-50% - ${3 + Math.random() * 8}vh)) rotate(90deg) scale(${0.9 + Math.random() * 0.2}); }
                      50% { transform: translate(-50%, calc(-50% + ${5 + Math.random() * 10}vh)) rotate(180deg) scale(1); }
                      75% { transform: translate(calc(-50% - ${5 + Math.random() * 10}vw), calc(-50% - ${3 + Math.random() * 8}vh)) rotate(270deg) scale(${0.9 + Math.random() * 0.2}); }
                      100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>

          {/* Layer 2 - Medium Blur - More caps */}
          <div className="fog-layer-2">
            {desktopCaps.slice(120, 220).map((cap, index) => (
              <div
                key={`desktop-layer2-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex + 1,
                  animation: `float-drift-${cap.id} ${cap.duration}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity * 0.8,
                  filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.05))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-drift-${cap.id} {
                      0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
                      33% { transform: translate(calc(-50% + ${3 + Math.random() * 8}vw), calc(-50% - ${2 + Math.random() * 6}vh)) rotate(${Math.random() * 30 - 15}deg) scale(${0.8 + Math.random() * 0.3}); }
                      66% { transform: translate(calc(-50% - ${3 + Math.random() * 8}vw), calc(-50% + ${2 + Math.random() * 6}vh)) rotate(${Math.random() * 30 - 15}deg) scale(${0.8 + Math.random() * 0.3}); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>

          {/* Layer 3 - Most Blurred (furthest) - More caps */}
          <div className="fog-layer-3">
            {desktopCaps.slice(220).map((cap, index) => (
              <div
                key={`desktop-layer3-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex,
                  animation: `float-slow-${cap.id} ${cap.duration + 5}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity * 0.6,
                  filter: 'drop-shadow(0 1px 1px rgba(0, 0, 0, 0.03))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-slow-${cap.id} {
                      0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(0.9); }
                      50% { transform: translate(calc(-50% + ${2 + Math.random() * 5}vw), calc(-50% + ${2 + Math.random() * 5}vh)) rotate(${Math.random() * 20 - 10}deg) scale(${0.7 + Math.random() * 0.2}); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>

          {/* Layer 4 - Extra Blurred - Even more caps */}
          <div className="fog-layer-4">
            {desktopCaps.slice(280, 300).map((cap, index) => (
              <div
                key={`desktop-layer4-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex - 1,
                  animation: `float-ultra-slow-${cap.id} ${cap.duration + 8}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity * 0.4,
                  filter: 'drop-shadow(0 0.5px 1px rgba(0, 0, 0, 0.02))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-ultra-slow-${cap.id} {
                      0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(0.8); }
                      50% { transform: translate(calc(-50% + ${1 + Math.random() * 3}vw), calc(-50% + ${1 + Math.random() * 3}vh)) rotate(${Math.random() * 15 - 7.5}deg) scale(${0.6 + Math.random() * 0.15}); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>

          {/* Layer 5 - Maximum Blur - Background atmosphere */}
          <div className="fog-layer-5">
            {desktopCaps.slice(300).map((cap, index) => (
              <div
                key={`desktop-layer5-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex - 2,
                  animation: `float-atmosphere-${cap.id} ${cap.duration + 12}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity * 0.3,
                  filter: 'drop-shadow(0 0.5px 0.5px rgba(0, 0, 0, 0.01))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-atmosphere-${cap.id} {
                      0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(0.7); }
                      33% { transform: translate(calc(-50% + ${0.5 + Math.random() * 2}vw), calc(-50% - ${0.5 + Math.random() * 2}vh)) rotate(${Math.random() * 10 - 5}deg) scale(${0.5 + Math.random() * 0.1}); }
                      66% { transform: translate(calc(-50% - ${0.5 + Math.random() * 2}vw), calc(-50% + ${0.5 + Math.random() * 2}vh)) rotate(${Math.random() * 10 - 5}deg) scale(${0.5 + Math.random() * 0.1}); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>
        </div>

        {/* Tablet Fog - Medium Density */}
        <div className="floating-fog-tablet">
          <div className="fog-layer-1">
            {tabletCaps.slice(0, 80).map((cap, index) => (
              <div
                key={`tablet-layer1-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex + 1,
                  animation: `float-tablet-${cap.id} ${cap.duration}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity,
                  filter: 'drop-shadow(0 1px 3px rgba(0, 0, 0, 0.08))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-tablet-${cap.id} {
                      0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
                      50% { transform: translate(calc(-50% + ${4 + Math.random() * 8}vw), calc(-50% + ${3 + Math.random() * 6}vh)) rotate(180deg) scale(${0.8 + Math.random() * 0.3}); }
                      100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>

          <div className="fog-layer-2">
            {tabletCaps.slice(80).map((cap, index) => (
              <div
                key={`tablet-layer2-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex,
                  animation: `float-tablet-bg-${cap.id} ${cap.duration + 3}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity * 0.7,
                  filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.05))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-tablet-bg-${cap.id} {
                      0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(0.9); }
                      33% { transform: translate(calc(-50% + ${2 + Math.random() * 5}vw), calc(-50% - ${2 + Math.random() * 4}vh)) rotate(120deg) scale(${0.7 + Math.random() * 0.2}); }
                      66% { transform: translate(calc(-50% - ${2 + Math.random() * 5}vw), calc(-50% + ${2 + Math.random() * 4}vh)) rotate(240deg) scale(${0.7 + Math.random() * 0.2}); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>

          {/* Tablet Layer 3 - Extra background caps */}
          <div className="fog-layer-3">
            {tabletCaps.slice(150).map((cap, index) => (
              <div
                key={`tablet-layer3-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex - 1,
                  animation: `float-tablet-deep-${cap.id} ${cap.duration + 6}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity * 0.5,
                  filter: 'drop-shadow(0 0.5px 1px rgba(0, 0, 0, 0.03))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-tablet-deep-${cap.id} {
                      0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(0.8); }
                      50% { transform: translate(calc(-50% + ${1.5 + Math.random() * 4}vw), calc(-50% + ${1.5 + Math.random() * 4}vh)) rotate(${Math.random() * 15 - 7.5}deg) scale(${0.6 + Math.random() * 0.15}); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>
        </div>

        {/* Mobile Fog - Increased Density */}
        <div className="floating-fog-mobile">
          <div className="fog-layer-1">
            {mobileCaps.slice(0, 60).map((cap) => (
              <div
                key={`mobile-${cap.id}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex,
                  animation: `float-mobile-${cap.id} ${cap.duration}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity,
                  filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.06))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-mobile-${cap.id} {
                      0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
                      25% { transform: translate(calc(-50% + ${3 + Math.random() * 6}vw), calc(-50% - ${2 + Math.random() * 4}vh)) rotate(90deg) scale(${0.9 + Math.random() * 0.2}); }
                      50% { transform: translate(-50%, calc(-50% + ${3 + Math.random() * 6}vh)) rotate(180deg) scale(0.8); }
                      75% { transform: translate(calc(-50% - ${3 + Math.random() * 6}vw), calc(-50% - ${2 + Math.random() * 4}vh)) rotate(270deg) scale(${0.9 + Math.random() * 0.2}); }
                      100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>

          {/* Mobile Layer 2 - Background caps */}
          <div className="fog-layer-2">
            {mobileCaps.slice(60).map((cap, index) => (
              <div
                key={`mobile-layer2-${cap.id}-${index}`}
                className="fixed pointer-events-none"
                style={{
                  left: `${cap.position.left}%`,
                  top: `${cap.position.top}%`,
                  zIndex: cap.zIndex - 1,
                  animation: `float-mobile-bg-${cap.id} ${cap.duration + 4}s ease-in-out infinite`,
                  animationDelay: `${cap.delay}s`,
                  opacity: cap.opacity * 0.6,
                  filter: 'drop-shadow(0 0.5px 1px rgba(0, 0, 0, 0.04))',
                  animationPlayState: 'var(--animation-play-state, running)'
                }}
              >
                <GraduationCap
                  size={cap.size}
                  className={`${cap.colorTheme === 'primary' ? 'text-primary' : cap.colorTheme === 'accent' ? 'text-accent' : 'text-secondary'}`}
                />
                <style>
                  {`
                    @keyframes float-mobile-bg-${cap.id} {
                      0%, 100% { transform: translate(-50%, -50%) rotate(0deg) scale(0.8); }
                      50% { transform: translate(calc(-50% + ${2 + Math.random() * 4}vw), calc(-50% + ${2 + Math.random() * 4}vh)) rotate(${Math.random() * 12 - 6}deg) scale(${0.7 + Math.random() * 0.15}); }
                    }
                  `}
                </style>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

/**
 * Enhanced Multiple Floating Graduation Caps Component
 * Combines fog effect with prominent caps for layered visual effect
 */
export const FloatingGraduationCaps: React.FC = () => {
  return (
    <>
      {/* Background Fog Effect */}
      <FloatingGraduationCapFog />

      {/* Prominent Foreground Caps */}
      <FloatingGraduationCap
        pattern="figure8"
        duration={14}
        size={28}
        opacity={0.65}
        colorTheme="primary"
        delay={0}
        startPosition={{ x: 150, y: 120 }}
        zIndex={8}
      />

      <FloatingGraduationCap
        pattern="drift"
        duration={18}
        size={32}
        opacity={0.7}
        colorTheme="accent"
        delay={4}
        startPosition={{ x: -100, y: 250 }}
        zIndex={7}
      />

      <FloatingGraduationCap
        pattern="circular"
        duration={12}
        size={26}
        opacity={0.6}
        colorTheme="secondary"
        delay={8}
        startPosition={{ x: 200, y: 350 }}
        zIndex={9}
      />
    </>
  );
};
