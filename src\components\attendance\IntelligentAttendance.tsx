import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { 
  AttendanceService, 
  CurrentClassInfo, 
  AttendanceSession, 
  StudentWithAttendance,
  SMSNotificationResult 
} from '@/services/AttendanceService';
import { 
  Clock, 
  Users, 
  CheckCircle, 
  XCircle, 
  MessageSquare, 
  AlertCircle, 
  Calendar,
  BookOpen,
  MapPin,
  Loader2,
  Send
} from 'lucide-react';

const IntelligentAttendance: React.FC = () => {
  const [currentClasses, setCurrentClasses] = useState<CurrentClassInfo[]>([]);
  const [selectedClass, setSelectedClass] = useState<CurrentClassInfo | null>(null);
  const [attendanceSession, setAttendanceSession] = useState<AttendanceSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [sendingSMS, setSendingSMS] = useState(false);
  const [notes, setNotes] = useState('');
  const [smsResult, setSmsResult] = useState<SMSNotificationResult | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  const { user } = useAuth();
  const { department, fullName } = useUserDepartment();
  const { toast } = useToast();

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Load current classes on component mount
  useEffect(() => {
    if (user?.id && department) {
      detectCurrentClasses();
    }
  }, [user?.id, department]);

  const detectCurrentClasses = async () => {
    try {
      setLoading(true);
      const classes = await AttendanceService.detectCurrentClasses(
        user!.id,
        department,
        '2024-2025'
      );
      
      setCurrentClasses(classes);
      
      // Auto-select if only one class
      if (classes.length === 1) {
        setSelectedClass(classes[0]);
        await loadStudentsForClass(classes[0]);
      } else if (classes.length === 0) {
        toast({
          title: 'No Current Classes',
          description: 'No classes scheduled for the current time slot.',
          variant: 'default',
        });
      }
    } catch (error) {
      console.error('Error detecting current classes:', error);
      toast({
        title: 'Error',
        description: 'Failed to detect current classes. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadStudentsForClass = async (classInfo: CurrentClassInfo) => {
    try {
      setLoading(true);
      const today = new Date().toISOString().split('T')[0];
      
      const session = await AttendanceService.getStudentsForAttendance(
        classInfo.department,
        classInfo.semester,
        classInfo.section,
        classInfo.subject_code,
        today,
        classInfo.time_slot
      );

      // Set default status to 'present' for all students if not already marked
      const studentsWithDefaults = session.students.map(student => ({
        ...student,
        status: student.status || 'present' as const
      }));

      setAttendanceSession({
        ...session,
        students: studentsWithDefaults
      });
    } catch (error) {
      console.error('Error loading students:', error);
      toast({
        title: 'Error',
        description: 'Failed to load students for attendance.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClassSelection = async (classIndex: string) => {
    const classInfo = currentClasses[parseInt(classIndex)];
    setSelectedClass(classInfo);
    await loadStudentsForClass(classInfo);
  };

  const toggleStudentAttendance = (studentId: string) => {
    if (!attendanceSession) return;

    const updatedStudents = attendanceSession.students.map(student =>
      student.id === studentId
        ? { ...student, status: student.status === 'present' ? 'absent' : 'present' as const }
        : student
    );

    setAttendanceSession({
      ...attendanceSession,
      students: updatedStudents
    });
  };

  const markAllPresent = () => {
    if (!attendanceSession) return;

    const updatedStudents = attendanceSession.students.map(student => ({
      ...student,
      status: 'present' as const
    }));

    setAttendanceSession({
      ...attendanceSession,
      students: updatedStudents
    });
  };

  const markAllAbsent = () => {
    if (!attendanceSession) return;

    const updatedStudents = attendanceSession.students.map(student => ({
      ...student,
      status: 'absent' as const
    }));

    setAttendanceSession({
      ...attendanceSession,
      students: updatedStudents
    });
  };

  const submitAttendance = async () => {
    if (!attendanceSession || !user?.id) return;

    try {
      setSubmitting(true);

      const attendanceData = attendanceSession.students.map(student => ({
        student_id: student.id,
        subject_code: attendanceSession.subject_code,
        faculty_id: user.id,
        department: attendanceSession.department,
        semester: attendanceSession.semester,
        section: attendanceSession.section,
        attendance_date: attendanceSession.date,
        time_slot: attendanceSession.time_slot,
        period_number: attendanceSession.period_number,
        status: student.status!,
        notes: notes || undefined
      }));

      await AttendanceService.markAttendance(attendanceData, user.id);

      toast({
        title: 'Attendance Submitted',
        description: 'Attendance has been successfully recorded.',
      });

      // Refresh the session to show updated data
      if (selectedClass) {
        await loadStudentsForClass(selectedClass);
      }
    } catch (error) {
      console.error('Error submitting attendance:', error);
      toast({
        title: 'Submission Failed',
        description: 'Failed to submit attendance. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const sendSMSNotifications = async () => {
    if (!attendanceSession) return;

    try {
      setSendingSMS(true);
      
      const result = await AttendanceService.sendSMSForAbsentees(
        attendanceSession,
        fullName
      );

      setSmsResult(result);

      if (result.success) {
        toast({
          title: 'SMS Notifications Sent',
          description: `SMS sent to ${result.sent_count} parent(s) for absent students.`,
        });
      } else {
        toast({
          title: 'SMS Failed',
          description: `Failed to send SMS notifications. ${result.errors.join(', ')}`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error sending SMS:', error);
      toast({
        title: 'SMS Error',
        description: 'Failed to send SMS notifications.',
        variant: 'destructive',
      });
    } finally {
      setSendingSMS(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Detecting current classes...</p>
        </div>
      </div>
    );
  }

  const presentCount = attendanceSession?.students.filter(s => s.status === 'present').length || 0;
  const absentCount = attendanceSession?.students.filter(s => s.status === 'absent').length || 0;
  const totalStudents = attendanceSession?.students.length || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Mark Attendance</h1>
          <p className="text-muted-foreground">
            Intelligent attendance marking with automatic class detection
          </p>
        </div>
        <div className="text-right">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            {currentTime.toLocaleString()}
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            {currentTime.toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </div>
        </div>
      </div>

      {/* Current Classes Detection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Current Classes
          </CardTitle>
          <CardDescription>
            Classes detected for the current time slot
          </CardDescription>
        </CardHeader>
        <CardContent>
          {currentClasses.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No classes scheduled for the current time slot. You can still mark attendance for any class manually.
              </AlertDescription>
            </Alert>
          ) : currentClasses.length === 1 ? (
            <div className="flex items-center gap-4 p-4 border rounded-lg bg-blue-50">
              <div className="flex-1">
                <h3 className="font-semibold">{currentClasses[0].subject_name}</h3>
                <p className="text-sm text-muted-foreground">
                  {currentClasses[0].subject_code} • Semester {currentClasses[0].semester} Section {currentClasses[0].section}
                </p>
                <div className="flex items-center gap-4 mt-2 text-sm">
                  <Badge variant="outline">{currentClasses[0].time_slot}</Badge>
                  {currentClasses[0].room && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {currentClasses[0].room}
                    </div>
                  )}
                  <Badge variant={currentClasses[0].subject_type === 'lab' ? 'secondary' : 'default'}>
                    {currentClasses[0].subject_type}
                  </Badge>
                </div>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Multiple classes detected. Please select the class for attendance:
              </p>
              <Select onValueChange={handleClassSelection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a class" />
                </SelectTrigger>
                <SelectContent>
                  {currentClasses.map((classInfo, index) => (
                    <SelectItem key={index} value={index.toString()}>
                      {classInfo.subject_name} - {classInfo.semester}{classInfo.section} ({classInfo.time_slot})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          <div className="flex gap-2 mt-4">
            <Button variant="outline" onClick={detectCurrentClasses}>
              <Clock className="h-4 w-4 mr-2" />
              Refresh Classes
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Student List */}
      {attendanceSession && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Student Attendance
            </CardTitle>
            <CardDescription>
              {attendanceSession.subject_name} • {attendanceSession.time_slot} • 
              Semester {attendanceSession.semester} Section {attendanceSession.section}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Summary */}
            <div className="flex items-center gap-4 p-4 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium">{presentCount} Present</span>
              </div>
              <div className="flex items-center gap-2">
                <XCircle className="h-5 w-5 text-red-600" />
                <span className="font-medium">{absentCount} Absent</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="font-medium">{totalStudents} Total</span>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex gap-2">
              <Button variant="outline" onClick={markAllPresent}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Mark All Present
              </Button>
              <Button variant="outline" onClick={markAllAbsent}>
                <XCircle className="h-4 w-4 mr-2" />
                Mark All Absent
              </Button>
            </div>

            {/* Student List */}
            <div className="border rounded-lg">
              <div className="grid grid-cols-3 gap-4 p-3 border-b bg-muted/50 font-medium text-sm">
                <div>Roll Number</div>
                <div>Student Name</div>
                <div>Attendance Status</div>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {attendanceSession.students.map((student) => (
                  <div key={student.id} className="grid grid-cols-3 gap-4 p-3 border-b hover:bg-muted/50">
                    <div className="font-mono text-sm">{student.roll_number}</div>
                    <div>{student.student_name}</div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={student.status === 'present'}
                        onCheckedChange={() => toggleStudentAttendance(student.id)}
                      />
                      <Badge variant={student.status === 'present' ? 'default' : 'destructive'}>
                        {student.status === 'present' ? 'Present' : 'Absent'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any notes about this attendance session..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>

            {/* Submit Actions */}
            <div className="flex gap-4 pt-4">
              <Button 
                onClick={submitAttendance} 
                disabled={submitting}
                className="flex-1"
              >
                {submitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Submit Attendance
                  </>
                )}
              </Button>
              
              {attendanceSession.isExisting && absentCount > 0 && (
                <Button 
                  variant="outline" 
                  onClick={sendSMSNotifications}
                  disabled={sendingSMS}
                >
                  {sendingSMS ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Send SMS to Absentee Parents
                    </>
                  )}
                </Button>
              )}
            </div>

            {/* SMS Result */}
            {smsResult && (
              <Alert className={smsResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                <MessageSquare className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">
                      SMS Notification Result
                    </p>
                    <p>
                      Sent: {smsResult.sent_count} • Failed: {smsResult.failed_count}
                    </p>
                    {smsResult.errors.length > 0 && (
                      <div className="text-sm">
                        <p className="font-medium">Errors:</p>
                        <ul className="list-disc list-inside">
                          {smsResult.errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default IntelligentAttendance;
