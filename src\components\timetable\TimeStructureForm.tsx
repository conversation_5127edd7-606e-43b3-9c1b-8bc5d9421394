// Only updating the TimeStructure-related code in TimeStructureForm

import React, { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import { Clock, Coffee, Sun, Calendar } from "lucide-react";
import { TimetableService, TimeStructure } from "@/services/TimetableService";

const formSchema = z.object({
  // Department and Academic Year
  department: z.string().min(1, { message: "Department is required" }),
  academicYear: z.string().min(1, { message: "Academic year is required" }),
  
  // General Time Structure
  firstHalfStartTime: z.string().min(1, { message: "Start time is required" }),
  firstHalfEndTime: z.string().min(1, { message: "End time is required" }),
  theoryClassDuration: z.coerce.number().min(10, { message: "Duration must be at least 10 minutes" }),
  periodsInFirstHalf: z.coerce.number().min(1, { message: "At least 1 period required" }).max(8),
  
  // Breaks
  teaBreakStartTime: z.string().min(1, { message: "Start time is required" }),
  teaBreakEndTime: z.string().min(1, { message: "End time is required" }),
  lunchBreakStartTime: z.string().min(1, { message: "Start time is required" }),
  lunchBreakEndTime: z.string().min(1, { message: "End time is required" }),
  
  // Second Half
  secondHalfStartTime: z.string().min(1, { message: "Start time is required" }),
  secondHalfEndTime: z.string().min(1, { message: "End time is required" }),
  periodsInSecondHalf: z.coerce.number().min(1, { message: "At least 1 period required" }).max(8),
  
  // Output Layout Preferences
  workingDays: z.array(z.string()).min(1, { message: "Select at least one working day" }),
}).refine(data => {
  // Ensure tea break is within first half
  const teaBreakStart = convertTimeStringToMinutes(data.teaBreakStartTime);
  const teaBreakEnd = convertTimeStringToMinutes(data.teaBreakEndTime);
  const firstHalfStart = convertTimeStringToMinutes(data.firstHalfStartTime);
  const firstHalfEnd = convertTimeStringToMinutes(data.firstHalfEndTime);
  
  return teaBreakStart >= firstHalfStart && teaBreakEnd <= firstHalfEnd;
}, {
  message: "Tea break must be within first half time range",
  path: ["teaBreakStartTime"],
}).refine(data => {
  // Ensure lunch break connects first and second half
  const lunchBreakStart = convertTimeStringToMinutes(data.lunchBreakStartTime);
  const lunchBreakEnd = convertTimeStringToMinutes(data.lunchBreakEndTime);
  const firstHalfEnd = convertTimeStringToMinutes(data.firstHalfEndTime);
  const secondHalfStart = convertTimeStringToMinutes(data.secondHalfStartTime);
  
  return Math.abs(lunchBreakStart - firstHalfEnd) < 15 && Math.abs(lunchBreakEnd - secondHalfStart) < 15;
}, {
  message: "Lunch break should connect first and second half",
  path: ["lunchBreakStartTime"],
});

// Helper function to convert time string (e.g., "08:30") to minutes since midnight
function convertTimeStringToMinutes(timeString: string): number {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

interface TimeStructureFormProps {
  onSave?: (timeStructure: TimeStructure) => void;
}

const TimeStructureForm: React.FC<TimeStructureFormProps> = ({ onSave }) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [savedStructureId, setSavedStructureId] = useState<string | undefined>(undefined);
  
  // Default values based on common academic schedules
  const defaultValues = {
    department: "cse",
    academicYear: "2024-2025",
    firstHalfStartTime: "08:30",
    firstHalfEndTime: "12:25",
    theoryClassDuration: 55,
    periodsInFirstHalf: 4,
    
    teaBreakStartTime: "10:20",
    teaBreakEndTime: "10:35",
    lunchBreakStartTime: "12:25",
    lunchBreakEndTime: "13:15",
    
    secondHalfStartTime: "13:15",
    secondHalfEndTime: "16:00",
    periodsInSecondHalf: 3,
    
    workingDays: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  };
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });
  
  // Load saved time structure if available
  useEffect(() => {
    const loadTimeStructure = async () => {
      try {
        setIsLoading(true);
        const department = form.getValues("department");
        const academicYear = form.getValues("academicYear");
        
        if (department && academicYear) {
          console.log("Loading time structure for:", academicYear, department);
          const timeStructure = await TimetableService.fetchTimeStructure({
            academicYear,
            department
          });
          
          if (timeStructure) {
            console.log("Found existing time structure:", timeStructure);
            setSavedStructureId(timeStructure.id);
            // Update form with saved values - fix to use working_days instead of workingDays
            form.reset({
              department: timeStructure.department,
              academicYear: timeStructure.academic_year,
              firstHalfStartTime: timeStructure.first_half_start_time,
              firstHalfEndTime: timeStructure.first_half_end_time,
              theoryClassDuration: timeStructure.theory_class_duration,
              periodsInFirstHalf: timeStructure.periods_in_first_half,
              teaBreakStartTime: timeStructure.tea_break_start_time,
              teaBreakEndTime: timeStructure.tea_break_end_time,
              lunchBreakStartTime: timeStructure.lunch_break_start_time,
              lunchBreakEndTime: timeStructure.lunch_break_end_time,
              secondHalfStartTime: timeStructure.second_half_start_time,
              secondHalfEndTime: timeStructure.second_half_end_time,
              periodsInSecondHalf: timeStructure.periods_in_second_half,
              workingDays: Array.isArray(timeStructure.working_days) 
                ? timeStructure.working_days 
                : typeof timeStructure.working_days === 'string' 
                  ? JSON.parse(timeStructure.working_days) 
                  : defaultValues.workingDays
            });
            
            toast({
              title: "Time structure loaded",
              description: "Loaded existing time structure from database",
            });
          } else {
            console.log("No time structure found, using defaults");
          }
        }
      } catch (error) {
        console.error("Error loading time structure:", error);
        toast({
          title: "Error",
          description: "Failed to load saved time structure",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadTimeStructure();
  }, []);

  // Watch values for dynamic calculations
  const theoryClassDuration = form.watch("theoryClassDuration");
  const periodsInFirstHalf = form.watch("periodsInFirstHalf");
  const periodsInSecondHalf = form.watch("periodsInSecondHalf");
  const department = form.watch("department");
  const academicYear = form.watch("academicYear");
  
  // Calculate derived values
  const totalPeriods = periodsInFirstHalf + periodsInSecondHalf;
  
  const departments = [
    { id: "cse", name: "Computer Science" },
    { id: "it", name: "Information Technology" },
    { id: "ece", name: "Electronics & Communication" },
    { id: "eee", name: "Electrical & Electronics" },
    { id: "mech", name: "Mechanical Engineering" },
  ];
  
  const academicYears = ["2022-2023", "2023-2024", "2024-2025", "2025-2026"];
  
  // When department or academic year changes, try to load the time structure
  useEffect(() => {
    if (department && academicYear) {
      const loadTimeStructure = async () => {
        try {
          setIsLoading(true);
          console.log("Department or academic year changed, loading time structure");
          const timeStructure = await TimetableService.fetchTimeStructure({
            academicYear,
            department
          });
          
          if (timeStructure) {
            setSavedStructureId(timeStructure.id);
            // Update form with correct field names
            form.reset({
              department: timeStructure.department,
              academicYear: timeStructure.academic_year,
              firstHalfStartTime: timeStructure.first_half_start_time,
              firstHalfEndTime: timeStructure.first_half_end_time,
              theoryClassDuration: timeStructure.theory_class_duration,
              periodsInFirstHalf: timeStructure.periods_in_first_half,
              teaBreakStartTime: timeStructure.tea_break_start_time,
              teaBreakEndTime: timeStructure.tea_break_end_time,
              lunchBreakStartTime: timeStructure.lunch_break_start_time,
              lunchBreakEndTime: timeStructure.lunch_break_end_time,
              secondHalfStartTime: timeStructure.second_half_start_time,
              secondHalfEndTime: timeStructure.second_half_end_time,
              periodsInSecondHalf: timeStructure.periods_in_second_half,
              workingDays: Array.isArray(timeStructure.working_days) 
                ? timeStructure.working_days 
                : typeof timeStructure.working_days === 'string' 
                  ? JSON.parse(timeStructure.working_days) 
                  : defaultValues.workingDays
            });
          } else {
            // Reset to defaults but keep selected department and academic year
            form.reset({
              ...defaultValues,
              department,
              academicYear
            });
            setSavedStructureId(undefined);
          }
        } catch (error) {
          console.error("Error loading time structure:", error);
        } finally {
          setIsLoading(false);
        }
      };
      
      loadTimeStructure();
    }
  }, [department, academicYear]);
  
  async function onSubmit(data: z.infer<typeof formSchema>) {
    try {
      setIsLoading(true);
      console.log("Submitting time structure form:", data);
      
      // Ensure all required fields are provided for TimeStructure type with correct field names
      const timeStructure: TimeStructure = {
        id: savedStructureId,
        academic_year: data.academicYear,
        department: data.department,
        first_half_start_time: data.firstHalfStartTime,
        first_half_end_time: data.firstHalfEndTime,
        theory_class_duration: data.theoryClassDuration,
        periods_in_first_half: data.periodsInFirstHalf,
        tea_break_start_time: data.teaBreakStartTime,
        tea_break_end_time: data.teaBreakEndTime,
        lunch_break_start_time: data.lunchBreakStartTime,
        lunch_break_end_time: data.lunchBreakEndTime,
        second_half_start_time: data.secondHalfStartTime,
        second_half_end_time: data.secondHalfEndTime,
        periods_in_second_half: data.periodsInSecondHalf,
        working_days: data.workingDays
      };
      
      console.log("Saving time structure:", timeStructure);
      await TimetableService.saveTimeStructure(timeStructure);
      
      if (onSave) {
        console.log("Calling onSave callback with time structure");
        onSave(timeStructure);
      }
      
      toast({
        title: "Time structure saved",
        description: `Created a structure with ${data.periodsInFirstHalf + data.periodsInSecondHalf} periods across ${data.workingDays.length} days`,
      });
      
      // Update the saved structure ID for future updates
      if (!savedStructureId) {
        // If this was a new record, reload to get the ID
        const savedStructure = await TimetableService.fetchTimeStructure({
          academicYear: data.academicYear,
          department: data.department
        });
        
        if (savedStructure) {
          setSavedStructureId(savedStructure.id);
        }
      }
    } catch (error) {
      console.error("Error saving time structure:", error);
      toast({
        title: "Error",
        description: "Failed to save time structure",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }
  
  // ... keep the rest of the form rendering code
  
  return (
    // ... keep existing return statement
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Timetable Time Structure</CardTitle>
          <CardDescription>
            Define time structure and rules for auto-generating timetable grids
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Department and Academic Year selection */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Calendar className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">Department & Academic Year</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Department</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select department" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {departments.map((dept) => (
                              <SelectItem key={dept.id} value={dept.id}>
                                {dept.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="academicYear"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Academic Year</FormLabel>
                        <Select 
                          value={field.value} 
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select academic year" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {academicYears.map((year) => (
                              <SelectItem key={year} value={year}>
                                {year}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              <Separator />
              
              {/* General Time Structure */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Clock className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">1. General Time Structure</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstHalfStartTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Time of First Half</FormLabel>
                        <FormControl>
                          <Input type="time" {...field} />
                        </FormControl>
                        <FormDescription>First class start time (e.g., 08:30)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="firstHalfEndTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Time of First Half</FormLabel>
                        <FormControl>
                          <Input type="time" {...field} />
                        </FormControl>
                        <FormDescription>End time before lunch (e.g., 12:25)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="theoryClassDuration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Duration of One Theory Class</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min={10} 
                            max={120}
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>In minutes (e.g., 55)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="periodsInFirstHalf"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Periods in First Half</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min={1} 
                            max={8}
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>Number of class periods (e.g., 4)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              <Separator />
              
              {/* Breaks Configuration */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Coffee className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">2. Breaks Configuration</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Tea Break Time</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <FormField
                        control={form.control}
                        name="teaBreakStartTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Start</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="teaBreakEndTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>End</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Lunch Break Time</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <FormField
                        control={form.control}
                        name="lunchBreakStartTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Start</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="lunchBreakEndTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>End</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              {/* Second Half Structure */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Sun className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">3. Second Half Structure</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="secondHalfStartTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Time of Second Half</FormLabel>
                        <FormControl>
                          <Input type="time" {...field} />
                        </FormControl>
                        <FormDescription>Time after lunch (e.g., 13:15)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="secondHalfEndTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Time of Second Half</FormLabel>
                        <FormControl>
                          <Input type="time" {...field} />
                        </FormControl>
                        <FormDescription>End of last class (e.g., 16:00)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="periodsInSecondHalf"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Periods in Second Half</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min={1} 
                            max={8}
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>Number of class periods after lunch (e.g., 3)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              <Separator />
              
              
              
              <Separator />
              
              {/* Output Layout Preference */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Calendar className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">5. Output Layout Preference</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="workingDays"
                    render={() => (
                      <FormItem>
                        <FormLabel>Working Days</FormLabel>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2">
                          {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"].map((day) => (
                            <FormField
                              key={day}
                              control={form.control}
                              name="workingDays"
                              render={({ field }) => {
                                return (
                                  <FormItem
                                    key={day}
                                    className="flex flex-row items-center space-x-2 space-y-0"
                                  >
                                    <FormControl>
                                      <input
                                        type="checkbox"
                                        className="accent-primary h-4 w-4"
                                        checked={field.value?.includes(day)}
                                        onChange={(e) => {
                                          return e.target.checked
                                            ? field.onChange([...field.value, day])
                                            : field.onChange(field.value?.filter(
                                                (value) => value !== day
                                              )
                                            )
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="text-sm font-normal cursor-pointer">
                                      {day}
                                    </FormLabel>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Periods Summary</h4>
                    <div className="bg-muted p-3 rounded-lg">
                      <div className="grid grid-cols-1 gap-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">First half:</span>
                          <span>{periodsInFirstHalf} periods</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Second half:</span>
                          <span>{periodsInSecondHalf} periods</span>
                        </div>
                        <div className="flex justify-between font-medium">
                          <span>Total periods per day:</span>
                          <span>{totalPeriods}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => form.reset(defaultValues)}
                  disabled={isLoading}
                >
                  Reset to Default
                </Button>
                <Button 
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Saving..." : "Save Time Structure"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default TimeStructureForm;
