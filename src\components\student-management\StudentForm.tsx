import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Student } from '@/services/StudentService';
import { AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface StudentFormProps {
  student?: Student | null;
  onSubmit: (data: any) => void;
  onCancel: () => void;
  assignedClass?: {
    department: string;
    semester: string;
    section: string;
  };
  isClassTeacher?: boolean;
}

const StudentForm: React.FC<StudentFormProps> = ({
  student,
  onSubmit,
  onCancel,
  assignedClass,
  isClassTeacher = false
}) => {
  const [formData, setFormData] = useState({
    roll_number: '',
    student_name: '',
    department: assignedClass?.department || '',
    semester: assignedClass?.semester || '',
    section: assignedClass?.section || '',
    parent_name: '',
    father_phone: '',
    mother_phone: '',
    student_phone: '',
    parent_email: '',
    student_email: ''
  });
  const { toast } = useToast();

  useEffect(() => {
    if (student) {
      setFormData({
        roll_number: student.roll_number || '',
        student_name: student.student_name || '',
        department: student.department || assignedClass?.department || '',
        semester: student.semester || assignedClass?.semester || '',
        section: student.section || assignedClass?.section || '',
        parent_name: student.parent_name || '',
        father_phone: student.father_phone || '',
        mother_phone: student.mother_phone || '',
        student_phone: student.student_phone || '',
        parent_email: student.parent_email || '',
        student_email: student.student_email || ''
      });
    }
  }, [student, assignedClass]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{student ? 'Edit Student' : 'Add New Student'}</DialogTitle>
          <DialogDescription>
            {student ? 'Update student information' : 'Enter student details to add to the system'}
          </DialogDescription>
        </DialogHeader>

        {/* Class Teacher Restriction Alert */}
        {isClassTeacher && assignedClass && (
          <Alert className="border-blue-200 bg-blue-50">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Class Teacher Mode:</strong> Students will be added to {assignedClass.semester}{assignedClass.section} - {assignedClass.department.toUpperCase()}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Roll Number */}
            <div className="space-y-2">
              <Label htmlFor="roll_number">Roll Number *</Label>
              <Input
                id="roll_number"
                value={formData.roll_number}
                onChange={(e) => handleChange('roll_number', e.target.value)}
                placeholder="e.g., 1KS25CS001"
                required
                disabled={!!student} // Disable editing for existing students
              />
              <div className="text-xs text-muted-foreground">
                Format: 1KS + Year + Department + 3-digit number (e.g., 1KS25CS001)
              </div>
              {formData.roll_number && !student && (
                <Badge variant="outline" className="text-xs">
                  Default password: {formData.roll_number}
                </Badge>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="student_name">Student Name *</Label>
              <Input
                id="student_name"
                value={formData.student_name}
                onChange={(e) => handleChange('student_name', e.target.value)}
                placeholder="Full name"
                required
              />
            </div>

            {/* Department, Semester, Section - Read-only for class teachers */}
            <div className="space-y-2">
              <Label htmlFor="department">Department *</Label>
              <Input
                id="department"
                value={formData.department}
                onChange={(e) => handleChange('department', e.target.value)}
                placeholder="e.g., cse"
                required
                disabled={isClassTeacher}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="semester">Semester *</Label>
              <Input
                id="semester"
                value={formData.semester}
                onChange={(e) => handleChange('semester', e.target.value)}
                placeholder="e.g., 4"
                required
                disabled={isClassTeacher}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="section">Section *</Label>
              <Input
                id="section"
                value={formData.section}
                onChange={(e) => handleChange('section', e.target.value)}
                placeholder="e.g., A"
                required
                disabled={isClassTeacher}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="parent_name">Parent Name</Label>
              <Input
                id="parent_name"
                value={formData.parent_name}
                onChange={(e) => handleChange('parent_name', e.target.value)}
                placeholder="Parent/Guardian name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="father_phone">Father's Phone</Label>
              <Input
                id="father_phone"
                value={formData.father_phone}
                onChange={(e) => handleChange('father_phone', e.target.value)}
                placeholder="10-digit phone number"
                type="tel"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="mother_phone">Mother's Phone</Label>
              <Input
                id="mother_phone"
                value={formData.mother_phone}
                onChange={(e) => handleChange('mother_phone', e.target.value)}
                placeholder="10-digit phone number"
                type="tel"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="student_phone">Student Phone</Label>
              <Input
                id="student_phone"
                value={formData.student_phone}
                onChange={(e) => handleChange('student_phone', e.target.value)}
                placeholder="10-digit phone number"
                type="tel"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="parent_email">Parent Email</Label>
              <Input
                id="parent_email"
                value={formData.parent_email}
                onChange={(e) => handleChange('parent_email', e.target.value)}
                placeholder="<EMAIL>"
                type="email"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="student_email">Student Email</Label>
              <Input
                id="student_email"
                value={formData.student_email}
                onChange={(e) => handleChange('student_email', e.target.value)}
                placeholder="<EMAIL>"
                type="email"
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">
              {student ? 'Update Student' : 'Add Student'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default StudentForm;
