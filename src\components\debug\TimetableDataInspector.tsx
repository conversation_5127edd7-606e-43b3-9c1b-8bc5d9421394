import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  Search, 
  CheckCircle, 
  AlertTriangle,
  RefreshCw,
  Calendar
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface TimetableSlot {
  id: string;
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: string;
  section: string;
  day: string;
  time_slot: string;
  room_number?: string;
  batch_name?: string;
  department: string;
  faculty_id: string;
}

export default function TimetableDataInspector() {
  const [testing, setTesting] = useState(false);
  const [slotId, setSlotId] = useState('');
  const [facultyId, setFacultyId] = useState('');
  const [results, setResults] = useState<TimetableSlot[]>([]);
  const [error, setError] = useState<string>('');

  const testSlotLookup = async () => {
    if (!slotId.trim()) {
      setError('Please enter a timetable slot ID');
      return;
    }

    try {
      setTesting(true);
      setError('');
      setResults([]);

      console.log('🔍 Testing timetable slot lookup for:', slotId);

      const { data: slotData, error: slotError } = await supabase
        .from('timetable_slots')
        .select(`
          id,
          subject_code,
          subject_name,
          subject_type,
          semester,
          section,
          day,
          time_slot,
          room_number,
          batch_name,
          department,
          faculty_id
        `)
        .eq('id', slotId);

      if (slotError) {
        setError(`Error fetching slot: ${slotError.message}`);
        return;
      }

      if (!slotData || slotData.length === 0) {
        setError(`No timetable slot found with ID: ${slotId}`);
        return;
      }

      setResults(slotData);
      console.log('✅ Found timetable slot:', slotData);

    } catch (error) {
      console.error('Error testing slot lookup:', error);
      setError(`Error: ${error.message}`);
    } finally {
      setTesting(false);
    }
  };

  const testFacultySlots = async () => {
    if (!facultyId.trim()) {
      setError('Please enter a faculty ID');
      return;
    }

    try {
      setTesting(true);
      setError('');
      setResults([]);

      console.log('🔍 Testing faculty timetable slots for:', facultyId);

      const { data: slotsData, error: slotsError } = await supabase
        .from('timetable_slots')
        .select(`
          id,
          subject_code,
          subject_name,
          subject_type,
          semester,
          section,
          day,
          time_slot,
          room_number,
          batch_name,
          department,
          faculty_id
        `)
        .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`)
        .limit(10);

      if (slotsError) {
        setError(`Error fetching faculty slots: ${slotsError.message}`);
        return;
      }

      if (!slotsData || slotsData.length === 0) {
        setError(`No timetable slots found for faculty ID: ${facultyId}`);
        return;
      }

      setResults(slotsData);
      console.log('✅ Found faculty slots:', slotsData);

    } catch (error) {
      console.error('Error testing faculty slots:', error);
      setError(`Error: ${error.message}`);
    } finally {
      setTesting(false);
    }
  };

  const getDataQualityColor = (slot: TimetableSlot) => {
    const hasSubjectCode = slot.subject_code && slot.subject_code.trim() !== '';
    const hasSubjectName = slot.subject_name && slot.subject_name.trim() !== '';
    const hasSchedule = slot.day && slot.time_slot;

    if (hasSubjectCode && hasSubjectName && hasSchedule) {
      return 'border-green-500 bg-green-50';
    } else if (hasSubjectCode || hasSubjectName) {
      return 'border-yellow-500 bg-yellow-50';
    } else {
      return 'border-red-500 bg-red-50';
    }
  };

  const getQualityScore = (slot: TimetableSlot) => {
    let score = 0;
    if (slot.subject_code && slot.subject_code.trim() !== '') score += 25;
    if (slot.subject_name && slot.subject_name.trim() !== '') score += 25;
    if (slot.day && slot.time_slot) score += 25;
    if (slot.semester && slot.section) score += 25;
    return score;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-green-600" />
          Timetable Data Inspector
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Test Controls */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="slot_id">Timetable Slot ID</Label>
              <div className="flex gap-2">
                <Input
                  id="slot_id"
                  placeholder="Enter slot ID (e.g., 123)"
                  value={slotId}
                  onChange={(e) => setSlotId(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && testSlotLookup()}
                />
                <Button 
                  onClick={testSlotLookup}
                  disabled={testing || !slotId.trim()}
                  className="flex items-center gap-2"
                >
                  {testing ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  Test
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="faculty_id">Faculty ID</Label>
              <div className="flex gap-2">
                <Input
                  id="faculty_id"
                  placeholder="Enter faculty ID"
                  value={facultyId}
                  onChange={(e) => setFacultyId(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && testFacultySlots()}
                />
                <Button 
                  onClick={testFacultySlots}
                  disabled={testing || !facultyId.trim()}
                  className="flex items-center gap-2"
                >
                  {testing ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Database className="h-4 w-4" />}
                  Get Slots
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Results Display */}
        {results.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold">Timetable Slots Found</h4>
              <Badge variant="outline" className="text-sm">
                {results.length} Results
              </Badge>
            </div>

            <div className="grid gap-4">
              {results.map((slot, index) => (
                <Card key={index} className={`border-l-4 ${getDataQualityColor(slot)}`}>
                  <CardContent className="pt-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="font-semibold text-lg">
                          {slot.subject_code || 'No Code'} - {slot.subject_name || 'No Name'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {slot.day} • {slot.time_slot} • Semester {slot.semester}-{slot.section}
                        </div>
                        {slot.room_number && (
                          <div className="text-xs text-muted-foreground mt-1">
                            📍 {slot.room_number}
                          </div>
                        )}
                        {slot.batch_name && (
                          <div className="text-xs text-muted-foreground">
                            👥 {slot.batch_name}
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge variant="outline" className="text-xs">
                          {slot.subject_type}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          Quality: {getQualityScore(slot)}%
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-muted-foreground">
                      <div><strong>ID:</strong> {slot.id}</div>
                      <div><strong>Faculty:</strong> {slot.faculty_id}</div>
                      <div><strong>Department:</strong> {slot.department}</div>
                      <div><strong>Type:</strong> {slot.subject_type}</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <Alert className="border-blue-200 bg-blue-50">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription className="text-blue-800">
            <strong>How to use:</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• Enter a timetable slot ID to see its complete data</li>
              <li>• Enter a faculty ID to see all their assigned slots</li>
              <li>• Green border = Complete data, Yellow = Partial data, Red = Missing data</li>
              <li>• This helps debug why some classes show placeholder values in leave requests</li>
            </ul>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
