
import { AlertCircle } from "lucide-react";

export const UploadGuidelines = () => {
  return (
    <div className="text-xs space-y-2 text-muted-foreground mt-4">
      <p className="flex items-center">
        <AlertCircle className="h-3 w-3 mr-1" />
        File should contain Subject Name, Code, Faculty, Section, Credits, and Lab details
      </p>
      <p className="flex items-center">
        <AlertCircle className="h-3 w-3 mr-1" />
        For lab subjects, include Batch 1 and Batch 2 details in separate columns
      </p>
      <p className="flex items-center">
        <AlertCircle className="h-3 w-3 mr-1" />
        Maximum file size: 5MB
      </p>
    </div>
  );
};
