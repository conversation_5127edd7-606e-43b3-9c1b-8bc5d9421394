# IA Marks Integration Guide

## Overview

This guide documents the complete integration between the Internal Assessment (IA) marks entry system and the Student Progress card display system, ensuring seamless data flow and real-time synchronization.

## Integration Architecture

```
Faculty IA Entry → InternalAssessmentService → Database → StudentProgressService → Student Progress Card
```

## Key Features

### 1. **Subject Code Normalization**
- **Problem**: IA entry systems may use codes like `BCS401_THEORY`, while timetables use `BCS401`
- **Solution**: Automatic normalization removes suffixes (`_THEORY`, `_LAB`, `_PRACTICAL`, `_TUTORIAL`)
- **Result**: Consistent subject code matching across both systems

### 2. **Real-time Data Synchronization**
- **Trigger**: When faculty saves IA marks through any IA entry interface
- **Process**: Marks are immediately available in Student Progress display
- **Verification**: Updated timestamps track when marks were last modified

### 3. **Enhanced Data Enrichment**
- **Subject Names**: Automatically fetched from timetable and merged with IA data
- **Grade Calculation**: Real-time grade computation based on total marks
- **Placeholder Handling**: Shows "-" for missing data instead of errors

## Implementation Details

### Subject Code Normalization

Both services now include normalization functions:

```typescript
// InternalAssessmentService.ts
private static normalizeSubjectCode(subjectCode: string): string {
  if (!subjectCode) return subjectCode;
  return subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
}

// StudentProgressService.ts  
private static normalizeSubjectCode(subjectCode: string): string {
  if (!subjectCode) return subjectCode;
  return subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
}
```

### Enhanced IA Marks Processing

```typescript
// Process IA records with subject name enrichment
const iaRecords = (data || []).map(record => {
  const normalizedSubjectCode = this.normalizeSubjectCode(record.subject_code);
  const subjectInfo = allStudentSubjects.find(s => s.subject_code === normalizedSubjectCode);
  
  return {
    subject_code: normalizedSubjectCode,
    subject_name: subjectInfo?.subject_name || 'Unknown Subject',
    ia1_marks: record.ia1_marks,
    ia2_marks: record.ia2_marks,
    ia3_marks: record.ia3_marks,
    assignment_marks: record.assignment_marks,
    lab_marks: record.lab_marks,
    total_marks: totalMarks,
    average_marks: averageMarks,
    grade: this.calculateGrade(averageMarks),
    last_updated: record.updated_at
  };
});
```

### Real-time Refresh Capability

```typescript
// Refresh student progress after IA marks update
static async refreshStudentProgress(studentUsn: string): Promise<StudentProgressCard | null> {
  console.log(`🔄 Refreshing student progress data for USN: ${studentUsn}`);
  const progressData = await this.getStudentProgressCard(studentUsn);
  return progressData;
}
```

## Complete Workflow

### 1. Faculty Enters IA Marks

**Entry Points**:
- Internal Assessment Management interface
- Sequential IA Entry system
- Bulk IA upload

**Process**:
1. Faculty selects subject and students
2. Enters IA1, IA2, IA3, Assignment, and Lab marks
3. Clicks "Save IA Marks"

### 2. Data Processing

**InternalAssessmentService.saveIAMarks()**:
1. Normalizes subject codes (removes suffixes)
2. Maps department names to lowercase
3. Adds updated timestamps
4. Saves to `internal_assessments` table
5. Logs refresh trigger for affected students

### 3. Student Progress Display

**StudentProgressService.getStudentProgressCard()**:
1. Fetches student basic info
2. Gets attendance data with subject name enrichment
3. Fetches IA marks with normalization
4. Merges subject names from timetable
5. Calculates totals and grades
6. Returns complete progress data

### 4. Real-time Updates

**Automatic Synchronization**:
- IA marks are immediately available in Student Progress
- Updated timestamps ensure latest data is displayed
- Subject names are properly enriched
- Grades are recalculated automatically

## Database Schema

### internal_assessments Table

```sql
CREATE TABLE internal_assessments (
  id UUID PRIMARY KEY,
  student_id UUID REFERENCES class_students(id),
  subject_code TEXT NOT NULL,           -- Normalized (e.g., 'BCS401')
  department TEXT NOT NULL,             -- Lowercase (e.g., 'cse')
  semester TEXT NOT NULL,
  section TEXT NOT NULL,
  academic_year TEXT NOT NULL,
  ia1_marks INTEGER,                    -- 0-25
  ia2_marks INTEGER,                    -- 0-25
  ia3_marks INTEGER,                    -- 0-25
  assignment_marks INTEGER,             -- 0-10
  lab_marks INTEGER,                    -- 0-20 (for lab subjects)
  faculty_id UUID REFERENCES employee_details(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),   -- Tracks real-time updates
  created_by UUID,
  last_modified_by UUID,
  UNIQUE(student_id, subject_code, academic_year)
);
```

## Testing the Integration

### Manual Testing Steps

1. **Enter IA Marks**:
   ```
   - Go to Internal Assessment Management
   - Select a subject (e.g., BCS401)
   - Enter marks for students
   - Save the marks
   ```

2. **Verify Student Progress**:
   ```
   - Open Student Progress card for the same student
   - Verify IA marks appear correctly
   - Check subject names are displayed (not "Unknown Subject")
   - Confirm grades are calculated properly
   ```

3. **Test Subject Code Variations**:
   ```
   - Enter marks for subject with suffix (e.g., BCS401_THEORY)
   - Verify it appears as BCS401 in Student Progress
   - Check subject name is properly enriched
   ```

### Automated Testing

Run the integration test script:
```bash
node src/scripts/test_ia_integration.js
```

## Troubleshooting

### Common Issues

1. **Subject Names Show "Unknown Subject"**
   - **Cause**: Subject not found in timetable_slots
   - **Solution**: Ensure timetable is configured for the class
   - **Check**: Department case sensitivity (use lowercase 'cse')

2. **IA Marks Not Appearing**
   - **Cause**: Student ID mismatch or department case issues
   - **Solution**: Verify student exists in class_students table
   - **Check**: Use lowercase department names

3. **Subject Code Mismatches**
   - **Cause**: Suffix variations not handled
   - **Solution**: Normalization should handle automatically
   - **Check**: Verify normalizeSubjectCode function is working

### Debug Queries

```sql
-- Check IA marks for a student
SELECT * FROM internal_assessments 
WHERE student_id = '4b4b0737-262f-4bc5-bc11-a17b29b4ee54'
ORDER BY updated_at DESC;

-- Check subject codes in timetable
SELECT DISTINCT subject_code, subject_name 
FROM timetable_slots 
WHERE department = 'cse' AND semester = '4' AND section = 'A';

-- Verify student exists
SELECT id, usn, student_name 
FROM class_students 
WHERE usn = '1KS23CS001';
```

## Best Practices

1. **Always use normalized subject codes** in database storage
2. **Include updated_at timestamps** for real-time tracking
3. **Use lowercase department names** for consistency
4. **Enrich subject names** from timetable data
5. **Handle missing data gracefully** with "-" placeholders
6. **Test the complete workflow** after any changes

## API Methods

### StudentProgressService

- `getStudentProgressCard(studentUsn)` - Get complete progress data
- `refreshStudentProgress(studentUsn)` - Refresh after IA updates
- `validateIAMarksConsistency()` - Check data integrity

### InternalAssessmentService

- `saveIAMarks(iaData, modifiedBy)` - Save IA marks with integration
- `normalizeSubjectCode(code)` - Normalize subject codes

## Integration Benefits

1. **Seamless Data Flow**: Faculty enters marks → Students see progress immediately
2. **Data Consistency**: Subject codes normalized across both systems
3. **Real-time Updates**: No delays between entry and display
4. **Enhanced UX**: Proper subject names and grades displayed
5. **Error Prevention**: Graceful handling of missing or mismatched data

This integration ensures that the IA marks entry and Student Progress display systems work together seamlessly, providing a unified experience for both faculty and students.
