import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import TimeStructureForm from "@/components/timetable/TimeStructureForm";
import { TimeStructure } from "@/services/TimetableService";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";

const TimetableStructure = () => {
  const { toast } = useToast();
  const [debugInfo, setDebugInfo] = useState<string | null>(null);
  
  const handleTimeStructureSave = (timeStructure: TimeStructure) => {
    toast({
      title: "Time Structure Saved",
      description: "The time structure has been saved successfully."
    });
  };

  const handleDebugMappings = async () => {
    try {
      // Fetch theory mappings to debug
      const { data: mappingsData, error } = await supabase
        .from('subject_faculty_mappings')
        .select(`
          id,
          subject_id,
          subject_code,
          subject_name,
          hours_per_week,
          faculty_1_id,
          subjects(subject_short_id)
        `)
        .eq('subject_type', 'theory');

      if (error) {
        throw error;
      }

      setDebugInfo(JSON.stringify(mappingsData, null, 2));
      toast({
        title: "Debug Info Retrieved",
        description: `Found ${mappingsData.length} theory mappings`
      });
    } catch (err) {
      console.error("Error fetching debug info:", err);
      toast({
        title: "Error",
        description: "Failed to fetch debug information",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Timetable Structure</h2>
        <p className="text-muted-foreground">
          Define the time structure for timetables across the institution
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Time Structure Configuration</CardTitle>
          <CardDescription>
            Set up working days, class durations, and break times for all timetables
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TimeStructureForm onSave={handleTimeStructureSave} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Debug Tools</CardTitle>
          <CardDescription>
            Tools to help diagnose timetable generation issues
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleDebugMappings} variant="outline">
            Debug Theory Mappings
          </Button>
          
          {debugInfo && (
            <div className="mt-4">
              <h3 className="font-medium mb-2">Theory Mappings Debug Info:</h3>
              <pre className="bg-slate-100 p-4 rounded-md text-xs overflow-auto max-h-96">
                {debugInfo}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TimetableStructure;
