
import { toast as sonnerToast } from "sonner";

// Export the toast function
export const toast = sonnerToast;

// Define types needed by the toast components
export type ToastProps = {
  title?: string;
  description?: React.ReactNode;
  action?: React.ReactNode;
  variant?: "default" | "destructive" | "warning";
  [key: string]: any;
};

export type ToastActionElement = React.ReactElement;

// Re-export types from sonner
export type { ToasterProps } from "sonner";
