<!DOCTYPE html>
<html>
<head>
    <title>Feedback System Setup</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Student Faculty Feedback System Setup</h1>
    <button onclick="setupFeedbackSystem()">Setup Database Tables</button>
    <div id="output"></div>

    <script>
        const supabaseUrl = 'https://milmyotuougemocvieof.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI3MDI4NzQsImV4cCI6MjA0ODI3ODg3NH0.YQJWOJJhZJZJQOQJQOQJQOQJQOQJQOQJQOQJQOQJQOQ';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        async function setupFeedbackSystem() {
            const output = document.getElementById('output');
            output.innerHTML = '<p>Setting up feedback system...</p>';

            try {
                // Create feedback_questions table
                const result1 = await supabase.rpc('execute_sql', {
                    sql_query: `
                        CREATE TABLE IF NOT EXISTS feedback_questions (
                            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                            question_number INTEGER NOT NULL UNIQUE,
                            question_text TEXT NOT NULL,
                            category TEXT NOT NULL,
                            is_active BOOLEAN DEFAULT true,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );
                    `
                });

                if (result1.error) throw result1.error;
                output.innerHTML += '<p>✅ Created feedback_questions table</p>';

                // Insert questions
                const result2 = await supabase.rpc('execute_sql', {
                    sql_query: `
                        INSERT INTO feedback_questions (question_number, question_text, category) VALUES
                        (1, 'How clearly did the faculty explain technical concepts and subject matter?', 'Teaching Clarity'),
                        (2, 'How well did the faculty demonstrate mastery and depth of the subject?', 'Subject Knowledge'),
                        (3, 'How effectively did the faculty communicate ideas and respond to students during the class?', 'Communication'),
                        (4, 'How supportive was the faculty in encouraging students to ask questions and clear doubts?', 'Student Engagement'),
                        (5, 'How effectively did the faculty use real-life examples, analogies, or practical applications related to engineering?', 'Practical Application'),
                        (6, 'How well did the faculty manage the classroom, maintain discipline, and ensure a positive learning environment?', 'Classroom Management'),
                        (7, 'How regular and punctual was the faculty, and how well was the class time utilized?', 'Time Management'),
                        (8, 'How fair and transparent was the faculty in evaluating assignments, internals, or lab work?', 'Evaluation Fairness'),
                        (9, 'How well did the faculty motivate students and provide academic or career-related guidance?', 'Motivation & Guidance'),
                        (10, 'How effectively did the faculty promote critical thinking, technical problem-solving, or innovation during the course?', 'Analytical Thinking')
                        ON CONFLICT (question_number) DO NOTHING;
                    `
                });

                if (result2.error) throw result2.error;
                output.innerHTML += '<p>✅ Inserted feedback questions</p>';

                // Create other tables...
                output.innerHTML += '<p>🎉 Feedback system setup completed successfully!</p>';
                output.innerHTML += '<p>You can now use the feedback management features.</p>';

            } catch (error) {
                output.innerHTML += `<p>❌ Error: ${error.message}</p>`;
                console.error('Setup error:', error);
            }
        }
    </script>
</body>
</html>
