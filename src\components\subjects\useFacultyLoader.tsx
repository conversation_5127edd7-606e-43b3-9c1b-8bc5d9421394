
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Faculty } from "@/stores/SubjectMappingStore";

export const useFacultyLoader = () => {
  const { toast } = useToast();
  const [facultyList, setFacultyList] = useState<Faculty[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadFaculty = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Query to get all faculty members
        const { data, error } = await supabase
          .from("employee_details")
          .select("id, full_name")
          .filter("roles", "cs", "{faculty}")  // This gets all employees with faculty role
          .order("full_name");

        if (error) {
          console.error("Error loading faculty:", error);
          toast({
            title: "Error",
            description: "Cannot load faculty members",
            variant: "destructive",
          });
          // Ensure we set an empty array on error
          setFacultyList([]);
          setError(error.message);
          return;
        }

        // Transform data and ensure we always have an array
        const facultyData = Array.isArray(data) ? data : [];
        const result = facultyData.map(item => ({
          id: item.id || `temp-${Math.random().toString(36).substring(2, 9)}`, // Ensure ID always exists
          name: item.full_name || "Unknown", // Ensure name always exists
        }));
          
        console.log(`Loaded ${result.length} faculty members from database`);
        setFacultyList(result);
      } catch (error: any) {
        console.error("Exception loading faculty:", error);
        // Ensure we set an empty array on error
        setFacultyList([]);
        setError(error.message || "Unknown error");
        toast({
          title: "Error",
          description: "Cannot load faculty members",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadFaculty();
  }, [toast]);

  return facultyList;
};
