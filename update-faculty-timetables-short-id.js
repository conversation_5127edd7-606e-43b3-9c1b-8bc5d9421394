// <PERSON>ript to update faculty_timetables with correct subject_short_id values
// This fixes the issue where faculty PDF exports show BCS405A instead of DMS

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://ixqjqjqjqjqjqjqjqjqj.supabase.co'; // Replace with your actual URL
const supabaseKey = 'your-service-role-key'; // Replace with your service role key
const supabase = createClient(supabaseUrl, supabaseKey);

async function updateFacultyTimetablesShortId() {
  try {
    console.log('🔄 Starting faculty_timetables subject_short_id update...');

    // Step 1: Get all faculty_timetables entries that need updating
    const { data: facultyTimetables, error: fetchError } = await supabase
      .from('faculty_timetables')
      .select('id, subject_code, academic_year, department, semester')
      .is('subject_short_id', null); // Only get entries where subject_short_id is null

    if (fetchError) {
      console.error('❌ Error fetching faculty_timetables:', fetchError);
      return;
    }

    console.log(`📊 Found ${facultyTimetables.length} faculty_timetables entries to update`);

    if (facultyTimetables.length === 0) {
      console.log('✅ No entries need updating. All subject_short_id values are already populated.');
      return;
    }

    // Step 2: Get subject mappings for short IDs
    const subjectCodes = [...new Set(facultyTimetables.map(ft => ft.subject_code))];
    console.log(`🔍 Looking up subject_short_id for ${subjectCodes.length} unique subject codes:`, subjectCodes);

    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('subject_code, subject_short_id, academic_year, department, semester')
      .in('subject_code', subjectCodes);

    if (subjectsError) {
      console.error('❌ Error fetching subjects:', subjectsError);
      return;
    }

    console.log(`📚 Found ${subjects.length} subject mappings`);

    // Step 3: Create a mapping of subject_code -> subject_short_id
    const subjectMap = new Map();
    subjects.forEach(subject => {
      const key = `${subject.subject_code}-${subject.academic_year}-${subject.department}-${subject.semester}`;
      subjectMap.set(key, subject.subject_short_id);
    });

    // Step 4: Update faculty_timetables entries
    let updatedCount = 0;
    let skippedCount = 0;

    for (const ft of facultyTimetables) {
      const key = `${ft.subject_code}-${ft.academic_year}-${ft.department}-${ft.semester}`;
      const shortId = subjectMap.get(key);

      if (shortId) {
        const { error: updateError } = await supabase
          .from('faculty_timetables')
          .update({ 
            subject_short_id: shortId,
            updated_at: new Date().toISOString()
          })
          .eq('id', ft.id);

        if (updateError) {
          console.error(`❌ Error updating faculty_timetables entry ${ft.id}:`, updateError);
        } else {
          console.log(`✅ Updated ${ft.subject_code} -> ${shortId} for entry ${ft.id}`);
          updatedCount++;
        }
      } else {
        console.warn(`⚠️ No subject_short_id found for ${ft.subject_code} (${ft.academic_year}, ${ft.department}, ${ft.semester})`);
        skippedCount++;
      }
    }

    console.log('\n📊 Update Summary:');
    console.log(`✅ Successfully updated: ${updatedCount} entries`);
    console.log(`⚠️ Skipped (no mapping found): ${skippedCount} entries`);
    console.log(`📝 Total processed: ${facultyTimetables.length} entries`);

    if (updatedCount > 0) {
      console.log('\n🎉 Faculty timetables updated successfully!');
      console.log('📄 Faculty PDF exports should now show correct subject short IDs (e.g., DMS instead of BCS405A)');
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run the update
updateFacultyTimetablesShortId();
