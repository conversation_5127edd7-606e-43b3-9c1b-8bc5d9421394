import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import {
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  BookOpen,
  Calendar,
  TrendingUp
} from 'lucide-react';

import { FeedbackService } from '@/services/FeedbackService';
import { PendingFeedback, CompletedFeedback } from '@/types/feedback-system';
import StudentFeedbackForm from './StudentFeedbackForm';
import { supabase } from '@/integrations/supabase/client';

interface StudentFeedbackDashboardProps {
  studentUsn: string;
  studentName: string;
  department: string;
  semester: string;
  section: string;
}

const StudentFeedbackDashboard: React.FC<StudentFeedbackDashboardProps> = ({
  studentUsn,
  studentName,
  department,
  semester,
  section
}) => {
  const [pendingFeedback, setPendingFeedback] = useState<PendingFeedback[]>([]);
  const [completedFeedback, setCompletedFeedback] = useState<CompletedFeedback[]>([]);
  const [selectedFeedback, setSelectedFeedback] = useState<PendingFeedback | null>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalSubjects: 0,
    completedSubjects: 0,
    pendingSubjects: 0,
    completionPercentage: 0
  });

  const { toast } = useToast();

  useEffect(() => {
    loadFeedbackData();
  }, [studentUsn, department, semester, section]);

  const loadFeedbackData = async () => {
    try {
      setLoading(true);

      // TEMPORARY: Skip authentication since email auth is disabled
      console.log('⚠️ AUTHENTICATION BYPASSED - EMAIL AUTH DISABLED');

      // For testing: directly verify student exists in class_students
      const { data: studentRecord, error: studentError } = await supabase
        .from('class_students')
        .select('*')
        .eq('usn', studentUsn)
        .single();

      if (studentError || !studentRecord) {
        toast({
          title: "Student Record Error",
          description: "Student record not found. Please contact administrator.",
          variant: "destructive",
        });
        return;
      }

      console.log('✅ Student record found:', studentRecord);

      // Load completed feedback
      const completedData = await FeedbackService.getStudentCompletedFeedback(
        studentUsn,
        department,
        semester,
        section
      );

      setCompletedFeedback(completedData);

      // Load pending feedback
      const pendingData = await FeedbackService.getStudentPendingFeedback(
        studentUsn,
        department,
        semester,
        section
      );

      setPendingFeedback(pendingData);

      // Calculate stats
      const totalSubjects = pendingData.length + completedData.length;
      const completedSubjects = completedData.length;
      const pendingSubjects = pendingData.length;
      const completionPercentage = totalSubjects > 0 ? (completedSubjects / totalSubjects) * 100 : 0;

      setStats({
        totalSubjects,
        completedSubjects,
        pendingSubjects,
        completionPercentage
      });

    } catch (error) {
      console.error('Error loading feedback data:', error);
      toast({
        title: "Error",
        description: "Failed to load feedback data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStartFeedback = (feedback: PendingFeedback) => {
    setSelectedFeedback(feedback);
  };

  const handleSubmissionComplete = () => {
    setSelectedFeedback(null);
    loadFeedbackData();
    toast({
      title: "Feedback Submitted",
      description: "Thank you for your feedback!",
    });
  };

  const getUrgencyBadge = (deadline: string) => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const hoursRemaining = (deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursRemaining < 24) {
      return <Badge variant="destructive">Due Soon</Badge>;
    } else if (hoursRemaining < 72) {
      return <Badge variant="secondary">Due This Week</Badge>;
    } else {
      return <Badge variant="outline">Active</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="loading-spinner h-8 w-8 mx-auto"></div>
          <p className="text-muted-foreground">Loading feedback dashboard...</p>
        </div>
      </div>
    );
  }

  // If a feedback form is selected, show the form
  if (selectedFeedback) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => setSelectedFeedback(null)}
          >
            ← Back to Dashboard
          </Button>
          <div>
            <h2 className="text-xl font-semibold">Faculty Feedback</h2>
            <p className="text-muted-foreground">
              {selectedFeedback.faculty_name} - {selectedFeedback.subject_name}
            </p>
          </div>
        </div>

        <StudentFeedbackForm
          studentUsn={studentUsn}
          studentName={studentName}
          department={department}
          semester={semester}
          section={section}
          pendingFeedback={selectedFeedback}
          onSubmissionComplete={handleSubmissionComplete}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-1">
        <h1 className="text-2xl font-bold">Faculty Feedback</h1>
        <p className="text-muted-foreground">
          Provide feedback for your faculty members
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Subjects</p>
                <p className="text-2xl font-bold">{stats.totalSubjects}</p>
              </div>
              <BookOpen className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{stats.completedSubjects}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">{stats.pendingSubjects}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Progress</p>
                <p className="text-2xl font-bold">{Math.round(stats.completionPercentage)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Bar */}
      {stats.totalSubjects > 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Overall Progress</span>
                <span>{stats.completedSubjects} of {stats.totalSubjects} completed</span>
              </div>
              <Progress value={stats.completionPercentage} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pending Feedback */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Pending Feedback ({pendingFeedback.length})
          </CardTitle>
          <CardDescription>
            Complete feedback for the following faculty members
          </CardDescription>
        </CardHeader>
        <CardContent>
          {pendingFeedback.length === 0 ? (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Great! You have completed all available feedback forms.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {pendingFeedback.map((feedback, index) => (
                <div key={`${feedback.session_id}-${feedback.faculty_id}-${feedback.subject_code}`} 
                     className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="space-y-1 flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{feedback.faculty_name}</h4>
                      {getUrgencyBadge(feedback.deadline)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {feedback.subject_name} ({feedback.subject_code})
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <BookOpen className="h-3 w-3" />
                        {feedback.subject_type}
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Due: {new Date(feedback.deadline).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleStartFeedback(feedback)}
                    className="gap-2"
                  >
                    <MessageSquare className="h-4 w-4" />
                    Start Feedback
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Completed Feedback */}
      {completedFeedback.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Completed Feedback ({completedFeedback.length})
            </CardTitle>
            <CardDescription>
              Feedback you have already submitted
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {completedFeedback.map((feedback, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg bg-green-50/50">
                  <div className="space-y-1">
                    <h4 className="font-medium">{feedback.faculty_name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {feedback.subject_name} ({feedback.subject_code})
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Submitted: {new Date(feedback.submitted_at).toLocaleDateString()}
                    </p>
                  </div>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default StudentFeedbackDashboard;
