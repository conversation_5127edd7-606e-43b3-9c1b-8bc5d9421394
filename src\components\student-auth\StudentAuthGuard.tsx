import { ReactNode } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useStudentAuth } from "@/contexts/StudentAuthContext";

interface StudentAuthGuardProps {
  children: ReactNode;
}

const StudentAuthGuard = ({ children }: StudentAuthGuardProps) => {
  const { isAuthenticated, loading } = useStudentAuth();
  const location = useLocation();

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    // Redirect to the student login page with a return url
    return <Navigate to="/student-login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default StudentAuthGuard;
