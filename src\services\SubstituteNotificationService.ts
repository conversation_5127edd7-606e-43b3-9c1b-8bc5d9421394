import { supabase } from '@/integrations/supabase/client';

export interface SubstituteNotification {
  id: string;
  substitute_faculty_id: string;
  leave_request_id: string;
  original_faculty_name: string;
  affected_classes: any[];
  notification_type: 'new_assignment' | 'assignment_cancelled' | 'assignment_updated';
  message: string;
  created_at: string;
  read_at?: string;
}

export class SubstituteNotificationService {
  private static listeners: Map<string, (notification: SubstituteNotification) => void> = new Map();

  /**
   * Subscribe to real-time substitute assignment notifications
   */
  static subscribeToSubstituteAssignments(
    facultyId: string,
    onNotification: (notification: SubstituteNotification) => void
  ): () => void {
    console.log('🔔 Subscribing to substitute notifications for faculty:', facultyId);

    // Store the listener
    this.listeners.set(facultyId, onNotification);

    // Subscribe to leave_requests table changes
    const subscription = supabase
      .channel(`substitute-assignments-${facultyId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'leave_requests',
          filter: `status=eq.approved`
        },
        async (payload) => {
          console.log('🔔 Leave request approved, checking for substitute assignments:', payload);
          await this.handleLeaveRequestUpdate(payload.new, facultyId, onNotification);
        }
      )
      .subscribe();

    // Return unsubscribe function
    return () => {
      console.log('🔕 Unsubscribing from substitute notifications for faculty:', facultyId);
      this.listeners.delete(facultyId);
      subscription.unsubscribe();
    };
  }

  /**
   * Handle leave request updates and check for substitute assignments
   */
  private static async handleLeaveRequestUpdate(
    leaveRequest: any,
    facultyId: string,
    onNotification: (notification: SubstituteNotification) => void
  ): Promise<void> {
    try {
      if (!leaveRequest.affected_classes || !Array.isArray(leaveRequest.affected_classes)) {
        return;
      }

      // Check if this faculty is assigned as substitute for any affected classes
      const substituteAssignments = leaveRequest.affected_classes.filter(
        (cls: any) => cls.substitute_faculty_id === facultyId
      );

      if (substituteAssignments.length === 0) {
        return;
      }

      // Get original faculty name
      const { data: originalFaculty } = await supabase
        .from('employee_details')
        .select('full_name')
        .eq('id', leaveRequest.faculty_id)
        .single();

      // Create notification
      const notification: SubstituteNotification = {
        id: crypto.randomUUID(),
        substitute_faculty_id: facultyId,
        leave_request_id: leaveRequest.id,
        original_faculty_name: originalFaculty?.full_name || 'Unknown Faculty',
        affected_classes: substituteAssignments,
        notification_type: 'new_assignment',
        message: `You have been assigned to substitute for ${originalFaculty?.full_name || 'a faculty member'} for ${substituteAssignments.length} class(es).`,
        created_at: new Date().toISOString()
      };

      console.log('🔔 Sending substitute notification:', notification);
      onNotification(notification);

      // Store notification in database for persistence
      await this.storeNotification(notification);

    } catch (error) {
      console.error('Error handling leave request update:', error);
    }
  }

  /**
   * Store notification in database
   */
  private static async storeNotification(notification: SubstituteNotification): Promise<void> {
    try {
      const { error } = await supabase
        .from('substitute_notifications')
        .insert({
          id: notification.id,
          substitute_faculty_id: notification.substitute_faculty_id,
          leave_request_id: notification.leave_request_id,
          original_faculty_name: notification.original_faculty_name,
          affected_classes: notification.affected_classes,
          notification_type: notification.notification_type,
          message: notification.message,
          created_at: notification.created_at
        });

      if (error) {
        console.error('Error storing notification:', error);
        // Don't throw error - notification was still sent in real-time
      }
    } catch (error) {
      console.error('Error storing notification:', error);
    }
  }

  /**
   * Get unread notifications for a faculty member
   * FIXED: Use leave_requests table directly since substitute_notifications table doesn't exist
   */
  static async getUnreadNotifications(facultyId: string): Promise<SubstituteNotification[]> {
    try {
      console.log('🔔 Getting substitute assignments from leave_requests for faculty:', facultyId);

      // Query leave_requests directly to find substitute assignments
      const { data: leaveRequests, error } = await supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          affected_classes,
          status,
          start_date,
          end_date,
          reason,
          created_at,
          employee_details!faculty_id(full_name)
        `)
        .eq('status', 'approved')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error getting leave requests:', error);
        return []; // Return empty array instead of throwing
      }

      // Filter for leave requests that have substitute assignments for this faculty
      const substituteNotifications: SubstituteNotification[] = [];

      for (const request of leaveRequests || []) {
        if (request.affected_classes && Array.isArray(request.affected_classes)) {
          // Find classes where this faculty is assigned as substitute
          const substituteAssignments = [];

          for (const cls of request.affected_classes) {
            if (cls.substitute_faculty_id === facultyId) {
              // If the affected class data is incomplete (only has class_id), fetch full details
              if (cls.class_id && !cls.subject_code) {
                console.log('🔍 Fetching complete class details for class_id:', cls.class_id);

                // Extract the actual timetable slot ID (remove date suffix if present)
                // class_id format: "01f7ea0d-ed53-4a4b-9e3c-5ceebfedb36d-2025-06-06"
                // We need: "01f7ea0d-ed53-4a4b-9e3c-5ceebfedb36d"
                const parts = cls.class_id.split('-');
                const slotId = parts.length >= 5 ? parts.slice(0, 5).join('-') : cls.class_id;

                try {
                  const { data: timetableSlot, error: slotError } = await supabase
                    .from('timetable_slots')
                    .select('*')
                    .eq('id', slotId)
                    .single();

                  if (!slotError && timetableSlot) {
                    // Merge the timetable slot data with substitute assignment info
                    substituteAssignments.push({
                      ...cls,
                      subject_code: timetableSlot.subject_code,
                      subject_name: timetableSlot.subject_name,
                      subject_type: timetableSlot.subject_type,
                      time_slot: timetableSlot.time_slot,
                      day: timetableSlot.day,
                      semester: timetableSlot.semester,
                      section: timetableSlot.section,
                      room_number: timetableSlot.room_number,
                      batch_name: timetableSlot.batch_name,
                      department: timetableSlot.department
                    });
                  } else {
                    console.warn('Could not fetch timetable slot details for:', slotId);
                    substituteAssignments.push(cls);
                  }
                } catch (error) {
                  console.error('Error fetching timetable slot:', error);
                  substituteAssignments.push(cls);
                }
              } else {
                // Class data is already complete
                substituteAssignments.push(cls);
              }
            }
          }

          if (substituteAssignments.length > 0) {
            substituteNotifications.push({
              id: `substitute-${request.id}`,
              substitute_faculty_id: facultyId,
              original_faculty_id: request.faculty_id,
              original_faculty_name: request.employee_details?.full_name || 'Unknown Faculty',
              leave_request_id: request.id,
              affected_classes: substituteAssignments,
              notification_type: 'new_assignment',
              message: `You have been assigned as substitute for ${request.employee_details?.full_name} from ${request.start_date} to ${request.end_date}`,
              created_at: request.created_at,
              read_at: null // Always treat as unread for now
            });
          }
        }
      }

      console.log('✅ Found', substituteNotifications.length, 'substitute assignments');
      return substituteNotifications;

    } catch (error) {
      console.error('Error getting substitute assignments:', error);
      return []; // Return empty array to prevent UI crashes
    }
  }

  /**
   * Mark notification as read
   * FIXED: Since we don't have substitute_notifications table, just log the action
   */
  static async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      console.log('📖 Marking substitute notification as read:', notificationId);
      // Since we're using leave_requests directly, we can't mark individual notifications as read
      // This could be implemented by storing read status in localStorage or a user preferences table
      console.log('✅ Notification marked as read (logged only)');
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Check for new substitute assignments (polling fallback)
   */
  static async checkForNewAssignments(facultyId: string): Promise<SubstituteNotification[]> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Get approved leave requests from today that might have new assignments
      const { data: leaveRequests, error } = await supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          affected_classes,
          status,
          updated_at,
          employee_details!faculty_id(full_name)
        `)
        .eq('status', 'approved')
        .gte('updated_at', today)
        .order('updated_at', { ascending: false });

      if (error) throw error;

      const notifications: SubstituteNotification[] = [];

      for (const leaveRequest of leaveRequests || []) {
        if (!leaveRequest.affected_classes || !Array.isArray(leaveRequest.affected_classes)) {
          continue;
        }

        const substituteAssignments = leaveRequest.affected_classes.filter(
          (cls: any) => cls.substitute_faculty_id === facultyId
        );

        if (substituteAssignments.length > 0) {
          notifications.push({
            id: crypto.randomUUID(),
            substitute_faculty_id: facultyId,
            leave_request_id: leaveRequest.id,
            original_faculty_name: leaveRequest.employee_details?.full_name || 'Unknown Faculty',
            affected_classes: substituteAssignments,
            notification_type: 'new_assignment',
            message: `You have been assigned to substitute for ${leaveRequest.employee_details?.full_name || 'a faculty member'} for ${substituteAssignments.length} class(es).`,
            created_at: leaveRequest.updated_at
          });
        }
      }

      return notifications;
    } catch (error) {
      console.error('Error checking for new assignments:', error);
      return [];
    }
  }

  /**
   * Send toast notification to UI
   */
  static sendToastNotification(
    notification: SubstituteNotification,
    toast: (options: any) => void
  ): void {
    toast({
      title: 'New Substitute Assignment',
      description: notification.message,
      duration: 10000, // 10 seconds
      action: {
        label: 'View Classes',
        onClick: () => {
          // Navigate to attendance page
          window.location.href = '/attendance';
        }
      }
    });
  }
}

// Database schema for substitute_notifications table (for reference)
/*
CREATE TABLE IF NOT EXISTS substitute_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    substitute_faculty_id UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
    leave_request_id UUID NOT NULL REFERENCES leave_requests(id) ON DELETE CASCADE,
    original_faculty_name TEXT NOT NULL,
    affected_classes JSONB NOT NULL,
    notification_type TEXT NOT NULL CHECK (notification_type IN ('new_assignment', 'assignment_cancelled', 'assignment_updated')),
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(substitute_faculty_id, leave_request_id)
);

CREATE INDEX IF NOT EXISTS idx_substitute_notifications_faculty_unread 
ON substitute_notifications(substitute_faculty_id, read_at) 
WHERE read_at IS NULL;
*/
