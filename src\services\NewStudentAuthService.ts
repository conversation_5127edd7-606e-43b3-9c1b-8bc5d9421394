import { supabase } from "@/integrations/supabase/client";

export interface StudentAuthData {
  id: string;
  usn: string;
  email: string;
  is_verified: boolean;
  created_at: string;
}

export interface StudentRegistrationData {
  usn: string;
  email: string;
  password: string;
}

export interface StudentLoginCredentials {
  usn: string;
  password: string;
}

export interface StudentSession {
  student: StudentAuthData;
  token: string;
  expires_at: string;
}

export class NewStudentAuthService {
  // USN format validation regex: 1KS23CS001
  private static readonly USN_REGEX = /^1KS\d{2}[A-Z]{2,4}\d{3}$/;
  
  // Email format validation regex
  private static readonly EMAIL_REGEX = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  
  // Password requirements: minimum 8 characters, mix of letters and numbers
  private static readonly PASSWORD_REGEX = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;

  /**
   * Validate USN format
   */
  static validateUSN(usn: string): boolean {
    return this.USN_REGEX.test(usn);
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    return this.EMAIL_REGEX.test(email);
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string): { isValid: boolean; message?: string } {
    if (password.length < 8) {
      return { isValid: false, message: "Password must be at least 8 characters long" };
    }
    
    if (!this.PASSWORD_REGEX.test(password)) {
      return { isValid: false, message: "Password must contain at least one letter and one number" };
    }
    
    return { isValid: true };
  }

  /**
   * Simple hash function for browser compatibility
   * Note: This is a basic implementation. In production, use server-side hashing.
   */
  private static async hashPassword(password: string): Promise<string> {
    // Use Web Crypto API for browser compatibility
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'student_salt_2024');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Compare password with hash
   */
  private static async comparePassword(password: string, hash: string): Promise<boolean> {
    const hashedInput = await this.hashPassword(password);
    return hashedInput === hash;
  }

  /**
   * Check if USN already exists
   */
  static async checkUSNExists(usn: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('student_auth')
        .select('id')
        .eq('usn', usn)
        .maybeSingle();

      if (error) {
        console.error('Error checking USN existence:', error);
        return false; // Assume doesn't exist if we can't check
      }

      return !!data;
    } catch (error) {
      console.error('Error checking USN existence:', error);
      return false; // Assume doesn't exist if we can't check
    }
  }

  /**
   * Check if email already exists
   */
  static async checkEmailExists(email: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('student_auth')
        .select('id')
        .eq('email', email)
        .maybeSingle();

      if (error) {
        console.error('Error checking email existence:', error);
        return false; // Assume doesn't exist if we can't check
      }

      return !!data;
    } catch (error) {
      console.error('Error checking email existence:', error);
      return false; // Assume doesn't exist if we can't check
    }
  }

  /**
   * Register a new student
   */
  static async registerStudent(registrationData: StudentRegistrationData): Promise<StudentAuthData> {
    try {
      // Validate input data
      if (!this.validateUSN(registrationData.usn)) {
        throw new Error('Invalid USN format. Expected format: 1KS23CS001');
      }

      if (!this.validateEmail(registrationData.email)) {
        throw new Error('Invalid email format');
      }

      const passwordValidation = this.validatePassword(registrationData.password);
      if (!passwordValidation.isValid) {
        throw new Error(passwordValidation.message || 'Invalid password');
      }

      // Check for duplicates
      const [usnExists, emailExists] = await Promise.all([
        this.checkUSNExists(registrationData.usn),
        this.checkEmailExists(registrationData.email)
      ]);

      if (usnExists) {
        throw new Error('USN already registered');
      }

      if (emailExists) {
        throw new Error('Email already registered');
      }

      // Hash password
      const passwordHash = await this.hashPassword(registrationData.password);

      // Insert new student
      const { data, error } = await supabase
        .from('student_auth')
        .insert({
          usn: registrationData.usn.toUpperCase(),
          email: registrationData.email.toLowerCase(),
          password_hash: passwordHash,
          is_verified: false
        })
        .select('id, usn, email, is_verified, created_at')
        .single();

      if (error) {
        console.error('Registration error:', error);
        throw new Error('Failed to register student');
      }

      return data;
    } catch (error) {
      console.error('Student registration error:', error);
      throw error;
    }
  }

  /**
   * Bulk register students with default password
   */
  static async bulkRegisterStudents(
    studentsData: Array<{
      usn: string;
      email?: string;
      student_name?: string;
    }>,
    createdBy: string
  ): Promise<{ success: StudentAuthData[], errors: { row: number, error: string }[] }> {
    const success: StudentAuthData[] = [];
    const errors: { row: number, error: string }[] = [];
    const defaultPassword = 'Password@123';

    for (let i = 0; i < studentsData.length; i++) {
      try {
        const studentData = studentsData[i];

        // Validate USN format
        if (!this.validateUSN(studentData.usn)) {
          throw new Error('Invalid USN format. Expected format: 1KS23CS001');
        }

        // Generate email if not provided
        const email = studentData.email || `${studentData.usn.toLowerCase()}@student.college.edu`;

        // Check for duplicates
        const [usnExists, emailExists] = await Promise.all([
          this.checkUSNExists(studentData.usn),
          this.checkEmailExists(email)
        ]);

        if (usnExists) {
          throw new Error('USN already registered');
        }

        if (emailExists) {
          throw new Error('Email already registered');
        }

        // Hash default password
        const passwordHash = await this.hashPassword(defaultPassword);

        // Insert new student
        const { data, error } = await supabase
          .from('student_auth')
          .insert({
            usn: studentData.usn.toUpperCase(),
            email: email.toLowerCase(),
            password_hash: passwordHash,
            is_verified: false
          })
          .select('id, usn, email, is_verified, created_at')
          .single();

        if (error) {
          throw new Error(`Failed to create auth account: ${error.message}`);
        }

        success.push(data);
      } catch (error) {
        errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { success, errors };
  }

  /**
   * Get student details from class_students table
   */
  static async getStudentDetails(usn: string): Promise<{
    student_name: string;
    semester: string;
    section: string;
    department: string;
    email?: string;
  } | null> {
    try {
      const { data, error } = await supabase
        .from('class_students')
        .select('student_name, semester, section, department, email')
        .eq('usn', usn.toUpperCase())
        .single();

      if (error || !data) {
        console.log('Student details not found in class_students table for USN:', usn);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching student details:', error);
      return null;
    }
  }

  /**
   * Authenticate student login
   */
  static async authenticateStudent(credentials: StudentLoginCredentials): Promise<StudentAuthData | null> {
    try {
      // Validate USN format
      if (!this.validateUSN(credentials.usn)) {
        throw new Error('Invalid USN format');
      }

      // Get student record
      const { data: student, error } = await supabase
        .from('student_auth')
        .select('id, usn, email, password_hash, is_verified, created_at')
        .eq('usn', credentials.usn.toUpperCase())
        .single();

      if (error || !student) {
        return null; // Invalid USN
      }

      // Compare password
      const isPasswordValid = await this.comparePassword(credentials.password, student.password_hash);
      
      if (!isPasswordValid) {
        return null; // Invalid password
      }

      // Return student data (without password hash)
      return {
        id: student.id,
        usn: student.usn,
        email: student.email,
        is_verified: student.is_verified,
        created_at: student.created_at
      };
    } catch (error) {
      console.error('Authentication error:', error);
      throw error;
    }
  }

  /**
   * Generate session token (simple implementation)
   */
  static generateSessionToken(): string {
    return btoa(Math.random().toString(36).substring(2) + Date.now().toString(36));
  }

  /**
   * Create student session
   */
  static createSession(student: StudentAuthData): StudentSession {
    const token = this.generateSessionToken();
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour session

    return {
      student,
      token,
      expires_at: expiresAt.toISOString()
    };
  }

  /**
   * Validate session token (basic implementation)
   */
  static validateSession(session: StudentSession): boolean {
    const now = new Date();
    const expiresAt = new Date(session.expires_at);
    return now < expiresAt;
  }
}
