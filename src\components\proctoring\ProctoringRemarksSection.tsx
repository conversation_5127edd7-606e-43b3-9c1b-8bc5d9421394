import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import {
  MessageSquare,
  Save,
  Edit3,
  Calendar,
  User,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Trash2,
  MoreVertical
} from 'lucide-react';
import {
  ProctoringRemarksService,
  StudentProctoringData,
  ProctoringSession
} from '@/services/ProctoringRemarksService';
import { useAuth } from '@/contexts/AuthContext';

interface ProctoringRemarksSectionProps {
  studentId: string;
  studentUsn: string;
  studentName: string;
  academicYear?: string;
}

const ProctoringRemarksSection: React.FC<ProctoringRemarksSectionProps> = ({
  studentId,
  studentUsn,
  studentName,
  academicYear
}) => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [proctoringData, setProctoringData] = useState<StudentProctoringData | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingSession, setEditingSession] = useState<number | null>(null);
  const [editingText, setEditingText] = useState('');
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState<number | null>(null);

  useEffect(() => {
    loadProctoringRemarks();
  }, [studentId, academicYear]);

  const loadProctoringRemarks = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading proctoring remarks for student:', studentUsn);

      const data = await ProctoringRemarksService.getStudentProctoringRemarks(
        studentId,
        academicYear
      );

      setProctoringData(data);
      console.log('✅ Proctoring remarks loaded:', data);

    } catch (error) {
      console.error('❌ Error loading proctoring remarks:', error);
      toast({
        title: 'Error Loading Remarks',
        description: error instanceof Error ? error.message : 'Failed to load proctoring remarks',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditSession = (session: ProctoringSession) => {
    setEditingSession(session.session_number);
    setEditingText(session.remarks?.remarks_text || '');
  };

  const handleCancelEdit = () => {
    setEditingSession(null);
    setEditingText('');
  };

  const handleSaveRemarks = async (sessionNumber: 1 | 2 | 3) => {
    if (!user?.id || !editingText.trim()) {
      toast({
        title: 'Invalid Input',
        description: 'Please enter remarks before saving.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);
      console.log('💾 Saving remarks for session:', sessionNumber);

      await ProctoringRemarksService.saveProctoringRemarks(
        studentId,
        user.id,
        sessionNumber,
        editingText.trim(),
        academicYear
      );

      toast({
        title: 'Remarks Saved',
        description: `Session ${sessionNumber} remarks saved successfully.`,
      });

      // Reload data to show updated remarks
      await loadProctoringRemarks();

      // Reset editing state
      setEditingSession(null);
      setEditingText('');

    } catch (error) {
      console.error('❌ Error saving remarks:', error);
      toast({
        title: 'Error Saving Remarks',
        description: error instanceof Error ? error.message : 'Failed to save remarks',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteRemarks = async (sessionNumber: 1 | 2 | 3) => {
    if (!user?.id) {
      toast({
        title: 'Authentication Error',
        description: 'You must be logged in to delete remarks.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setDeleting(sessionNumber);
      console.log('🗑️ Deleting remarks for session:', sessionNumber);

      await ProctoringRemarksService.deleteProctoringRemarks(
        studentId,
        user.id,
        sessionNumber,
        academicYear
      );

      toast({
        title: 'Remarks Deleted',
        description: `Session ${sessionNumber} remarks deleted successfully.`,
      });

      // Reload data to show updated state
      await loadProctoringRemarks();

    } catch (error) {
      console.error('❌ Error deleting remarks:', error);
      toast({
        title: 'Error Deleting Remarks',
        description: error instanceof Error ? error.message : 'Failed to delete remarks',
        variant: 'destructive',
      });
    } finally {
      setDeleting(null);
    }
  };

  const canEditSession = (session: ProctoringSession): boolean => {
    if (!user?.id) return false;
    if (!session.hasRemarks) return true; // Can create new remarks
    return ProctoringRemarksService.canEditRemark(session.remarks!, user.id);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSessionBadgeVariant = (session: ProctoringSession) => {
    if (!session.hasRemarks) return 'outline';
    return 'default';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            Proctoring Remarks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading proctoring remarks...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!proctoringData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            Proctoring Remarks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load proctoring remarks data.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          Proctoring Remarks
        </CardTitle>
        <CardDescription>
          Faculty observations and remarks for {studentName} ({studentUsn}) - Academic Year {proctoringData.academic_year}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {proctoringData.sessions.map((session) => (
          <div key={session.session_number} className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold">Session {session.session_number}</h3>
                <Badge variant={getSessionBadgeVariant(session)}>
                  {session.hasRemarks ? 'Has Remarks' : 'No Remarks'}
                </Badge>
              </div>

              {editingSession !== session.session_number && (
                <div className="flex items-center gap-2">
                  {canEditSession(session) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditSession(session)}
                      className="flex items-center gap-1"
                    >
                      <Edit3 className="w-4 h-4" />
                      {session.hasRemarks ? 'Edit' : 'Add Remarks'}
                    </Button>
                  )}

                  {session.hasRemarks && canEditSession(session) && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          disabled={deleting === session.session_number}
                        >
                          {deleting === session.session_number ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <MoreVertical className="h-4 w-4" />
                          )}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleDeleteRemarks(session.session_number)}
                          className="text-red-600 focus:text-red-600"
                          disabled={deleting === session.session_number}
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete Remarks
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              )}
            </div>

            {editingSession === session.session_number ? (
              <div className="space-y-3">
                <Textarea
                  value={editingText}
                  onChange={(e) => setEditingText(e.target.value)}
                  placeholder={`Enter remarks for Session ${session.session_number}...`}
                  className="min-h-[120px]"
                  maxLength={1000}
                />
                <div className="text-sm text-gray-500 mb-2">
                  {editingText.length}/1000 characters
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleSaveRemarks(session.session_number)}
                    disabled={saving || !editingText.trim()}
                    className="flex items-center gap-1"
                  >
                    {saving ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Save className="w-4 h-4" />
                    )}
                    Save Remarks
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={saving}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                {session.hasRemarks && session.remarks ? (
                  <div className="space-y-3">
                    <div className="bg-gray-50 p-3 rounded-md">
                      <p className="text-sm leading-relaxed whitespace-pre-wrap">
                        {session.remarks.remarks_text}
                      </p>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        <span>{session.remarks.faculty_name}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>
                          {session.remarks.updated_at !== session.remarks.created_at
                            ? `Updated: ${formatDate(session.remarks.updated_at)}`
                            : `Created: ${formatDate(session.remarks.created_at)}`
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>No remarks yet for Session {session.session_number}</p>
                    {canEditSession(session) && (
                      <p className="text-xs mt-1">Click "Add Remarks" to add your observations</p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        ))}

        {/* Summary */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="w-4 h-4 text-blue-600" />
            <span className="font-medium text-blue-900">Proctoring Summary</span>
          </div>
          <div className="text-sm text-blue-800">
            {proctoringData.sessions.filter(s => s.hasRemarks).length} of 3 sessions have remarks recorded
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProctoringRemarksSection;
