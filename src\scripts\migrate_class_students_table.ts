import { supabase } from '@/integrations/supabase/client';

/**
 * Migration script to add new columns to class_students table
 * Adds: email, student_mobile, father_mobile, mother_mobile
 */

async function executeQuery(query: string): Promise<any> {
  const { data, error } = await supabase.rpc('execute_sql', { query });
  if (error) {
    throw new Error(`SQL Error: ${error.message}`);
  }
  return data;
}

async function migrateClassStudentsTable() {
  try {
    console.log("Starting class_students table migration...");
    
    // Step 1: Add new columns
    console.log("Step 1: Adding new columns...");
    await executeQuery(`
      ALTER TABLE class_students 
      ADD COLUMN IF NOT EXISTS email TEXT,
      ADD COLUMN IF NOT EXISTS student_mobile TEXT,
      ADD COLUMN IF NOT EXISTS father_mobile TEXT,
      ADD COLUMN IF NOT EXISTS mother_mobile TEXT;
    `);
    
    // Step 2: Add column comments
    console.log("Step 2: Adding column comments...");
    await executeQuery(`
      COMMENT ON COLUMN class_students.email IS 'Student email address from Excel import';
    `);
    await executeQuery(`
      COMMENT ON COLUMN class_students.student_mobile IS 'Student mobile number from Excel import';
    `);
    await executeQuery(`
      COMMENT ON COLUMN class_students.father_mobile IS 'Father mobile number from Excel import';
    `);
    await executeQuery(`
      COMMENT ON COLUMN class_students.mother_mobile IS 'Mother mobile number from Excel import';
    `);
    
    // Step 3: Create indexes for better performance
    console.log("Step 3: Creating indexes...");
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_class_students_email ON class_students(email);
    `);
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_class_students_student_mobile ON class_students(student_mobile);
    `);
    
    // Step 4: Verify table structure
    console.log("Step 4: Verifying table structure...");
    const tableStructure = await executeQuery(`
      SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default
      FROM information_schema.columns 
      WHERE table_name = 'class_students' 
      ORDER BY ordinal_position;
    `);
    
    console.log("Updated table structure:", tableStructure);
    
    console.log("Class_students table migration completed successfully!");
  } catch (error) {
    console.error("Error migrating class_students table:", error);
    throw error;
  }
}

// Execute the migration
migrateClassStudentsTable()
  .then(() => {
    console.log("Migration script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Migration script failed:", error);
    process.exit(1);
  });
