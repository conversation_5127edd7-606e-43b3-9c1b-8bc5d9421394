import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  BookOpen, 
  FlaskConical, 
  Clock, 
  Users, 
  MapPin, 
  ChevronDown, 
  ChevronUp,
  UserCheck,
  FileText
} from 'lucide-react';
import { TimetableSlot } from '@/services/AttendanceService';

interface SubstituteClassCardProps {
  classInfo: TimetableSlot;
  hasAttendance?: boolean;
  isLoading?: boolean;
  onClick: () => void;
  isSelected?: boolean;
}

export default function SubstituteClassCard({
  classInfo,
  hasAttendance = false,
  isLoading = false,
  onClick,
  isSelected = false
}: SubstituteClassCardProps) {
  const [isNotesExpanded, setIsNotesExpanded] = useState(false);

  const getSubjectIcon = () => {
    return classInfo.subject_type === 'lab' ? (
      <FlaskConical className="h-4 w-4" />
    ) : (
      <BookOpen className="h-4 w-4" />
    );
  };

  const getSubjectBadgeVariant = () => {
    return classInfo.subject_type === 'lab' ? 'secondary' : 'default';
  };

  return (
    <Card
      className={`cursor-pointer transition-all hover:shadow-md border-l-4 border-blue-500 bg-blue-50/50 dark:bg-blue-950/20 ${
        isSelected ? 'ring-2 ring-blue-500' : ''
      } ${hasAttendance ? 'border-green-200 bg-green-50' : ''}`}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header with SUBSTITUTE badge */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge className="bg-blue-600 text-white hover:bg-blue-700">
                <UserCheck className="h-3 w-3 mr-1" />
                SUBSTITUTE
              </Badge>
              <Badge variant={getSubjectBadgeVariant()}>
                {getSubjectIcon()}
                <span className="ml-1">{classInfo.subject_type}</span>
              </Badge>
              {hasAttendance && (
                <Badge variant="outline" className="text-green-600 border-green-600">
                  ✓ Marked
                </Badge>
              )}
            </div>
          </div>

          {/* Subject Information */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg">
                {classInfo.subject_code}
              </h3>
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                {classInfo.time_slot}
              </div>
            </div>
            
            <p className="text-sm text-muted-foreground">
              {classInfo.subject_name?.replace(' (SUBSTITUTE)', '')}
            </p>

            {/* Substitution Context */}
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md space-y-1">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <span className="font-medium">Substituting for:</span> {classInfo.original_faculty_name}
              </p>
              {classInfo.original_subject_code && classInfo.original_subject_name && (
                <p className="text-xs text-blue-700 dark:text-blue-300">
                  <span className="font-medium">Original class:</span> {classInfo.original_subject_code} - {classInfo.original_subject_name}
                </p>
              )}
            </div>
          </div>

          {/* Class Details */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Users className="h-3 w-3 text-muted-foreground" />
              <span>
                Semester {classInfo.semester} - Section {classInfo.section}
                {classInfo.batch_name && ` (${classInfo.batch_name})`}
              </span>
            </div>
            {classInfo.room && (
              <div className="flex items-center gap-2">
                <MapPin className="h-3 w-3 text-muted-foreground" />
                <span>{classInfo.room}</span>
              </div>
            )}
          </div>

          {/* Substitution Notes (Collapsible) */}
          {classInfo.substitution_notes && classInfo.substitution_notes.trim() !== '' && (
            <Collapsible open={isNotesExpanded} onOpenChange={setIsNotesExpanded}>
              <CollapsibleTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full justify-between p-2 h-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsNotesExpanded(!isNotesExpanded);
                  }}
                >
                  <div className="flex items-center gap-2">
                    <FileText className="h-3 w-3" />
                    <span className="text-xs">Substitution Instructions</span>
                  </div>
                  {isNotesExpanded ? (
                    <ChevronUp className="h-3 w-3" />
                  ) : (
                    <ChevronDown className="h-3 w-3" />
                  )}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="pt-2">
                <div className="p-2 bg-muted rounded-md">
                  <p className="text-xs text-muted-foreground">
                    {classInfo.substitution_notes}
                  </p>
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}

          {/* Action Hint */}
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground text-center">
              Click to mark attendance for this substitute class
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
