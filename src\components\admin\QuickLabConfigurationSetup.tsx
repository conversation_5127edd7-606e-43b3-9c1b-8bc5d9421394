import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { SemesterLabConfigurationService } from "@/services/SemesterLabConfigurationService";
import { Settings, Zap, Clock } from "lucide-react";

const formSchema = z.object({
  academicYear: z.string().min(1, "Academic year is required"),
  department: z.string().min(1, "Department is required"),
  semester: z.string().min(1, "Semester is required"),
  labDuration: z.coerce.number().min(2).max(3),
});

type FormValues = z.infer<typeof formSchema>;

export const QuickLabConfigurationSetup: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [recentConfigurations, setRecentConfigurations] = useState<any[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      academicYear: "2024-2025",
      department: "cse",
      semester: "",
      labDuration: 3,
    },
  });

  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);
    try {
      const configData = {
        academic_year: values.academicYear,
        department: values.department,
        semester: values.semester,
        default_lab_duration: values.labDuration,
        lab_duration_type: values.labDuration === 2 ? 'two_hour' as const : 'three_hour' as const,
        enable_morning_session: true,
        enable_mid_session: values.labDuration === 2, // Enable Mid Session for 2-hour labs
        enable_afternoon_session: true,
        enable_early_afternoon_session: values.labDuration === 2, // Enable Early Afternoon for 2-hour labs
        skill_lab_required: true,
        skill_lab_placement_preference: 'vacant_day' as const,
        skill_lab_duration: values.labDuration,
        max_labs_per_day: values.labDuration === 2 ? 2 : 1,
        allow_consecutive_labs: values.labDuration === 2,
        prefer_lab_distribution: true,
      };

      const savedConfig = await SemesterLabConfigurationService.saveLabConfiguration(configData);
      
      // Add to recent configurations
      setRecentConfigurations(prev => [
        {
          semester: values.semester,
          duration: values.labDuration,
          timeSlots: SemesterLabConfigurationService.getAvailableTimeSlots(savedConfig)
        },
        ...prev.slice(0, 4) // Keep only last 5
      ]);

      toast({
        title: "Configuration Saved",
        description: `${values.semester} semester configured for ${values.labDuration}-hour labs.`,
      });

      // Reset form
      form.reset({
        academicYear: values.academicYear,
        department: values.department,
        semester: "",
        labDuration: 3,
      });
    } catch (error) {
      console.error('Error saving configuration:', error);
      toast({
        title: "Error",
        description: "Failed to save configuration. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createPresetConfigurations = async () => {
    setIsLoading(true);
    try {
      // Create common configurations
      const presets = [
        { semester: "1", duration: 3 },
        { semester: "2", duration: 2 },
        { semester: "3", duration: 3 },
        { semester: "4", duration: 3 },
        { semester: "5", duration: 2 },
        { semester: "6", duration: 2 },
        { semester: "7", duration: 2 },
        { semester: "8", duration: 2 },
      ];

      for (const preset of presets) {
        const configData = {
          academic_year: "2024-2025",
          department: "cse",
          semester: preset.semester,
          default_lab_duration: preset.duration,
          lab_duration_type: preset.duration === 2 ? 'two_hour' as const : 'three_hour' as const,
          enable_morning_session: true,
          enable_mid_session: preset.duration === 2,
          enable_afternoon_session: true,
          enable_early_afternoon_session: preset.duration === 2,
          skill_lab_required: true,
          skill_lab_placement_preference: 'vacant_day' as const,
          skill_lab_duration: preset.duration,
          max_labs_per_day: preset.duration === 2 ? 2 : 1,
          allow_consecutive_labs: preset.duration === 2,
          prefer_lab_distribution: true,
        };

        await SemesterLabConfigurationService.saveLabConfiguration(configData);
      }

      toast({
        title: "Preset Configurations Created",
        description: "All 8 semesters have been configured with recommended lab durations.",
      });
    } catch (error) {
      console.error('Error creating presets:', error);
      toast({
        title: "Error",
        description: "Failed to create preset configurations.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Quick Lab Configuration Setup
        </CardTitle>
        <CardDescription>
          Configure any semester for any lab duration (2-hour or 3-hour labs)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <FormField
                control={form.control}
                name="academicYear"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Academic Year</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="2023-2024">2023-2024</SelectItem>
                        <SelectItem value="2024-2025">2024-2025</SelectItem>
                        <SelectItem value="2025-2026">2025-2026</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="cse">Computer Science</SelectItem>
                        <SelectItem value="ece">Electronics & Communication</SelectItem>
                        <SelectItem value="mech">Mechanical</SelectItem>
                        <SelectItem value="eee">Electrical & Electronics</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="semester"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Semester</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select semester" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {[1, 2, 3, 4, 5, 6, 7, 8].map((sem) => (
                          <SelectItem key={sem} value={sem.toString()}>
                            {sem} Semester
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="labDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lab Duration</FormLabel>
                    <Select value={field.value.toString()} onValueChange={(value) => field.onChange(parseInt(value))}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="2">2 Hours</SelectItem>
                        <SelectItem value="3">3 Hours</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex gap-4">
              <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {isLoading ? "Saving..." : "Save Configuration"}
              </Button>
              
              <Button 
                type="button" 
                variant="outline" 
                onClick={createPresetConfigurations} 
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Zap className="h-4 w-4" />
                Create All Presets
              </Button>
            </div>
          </form>
        </Form>

        {recentConfigurations.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Recent Configurations:</h4>
            <div className="grid grid-cols-1 gap-2">
              {recentConfigurations.map((config, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <span>{config.semester} Semester</span>
                  <Badge variant={config.duration === 2 ? "secondary" : "default"}>
                    {config.duration}-hour labs
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">How It Works:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>Any semester</strong> can be configured for <strong>any lab duration</strong></li>
            <li>• <strong>2-hour labs</strong> get: Morning, Mid Session, Early Afternoon options</li>
            <li>• <strong>3-hour labs</strong> get: Morning, Afternoon options</li>
            <li>• <strong>Unconfigured semesters</strong> show all options for flexibility</li>
            <li>• Changes take effect immediately in Subject Allotment</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
