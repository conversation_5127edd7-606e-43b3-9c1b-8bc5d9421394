// setupSemesterConfigurations.ts
import { SemesterLabConfigurationService } from "@/services/SemesterLabConfigurationService";

export const setupSemesterConfigurations = async () => {
  try {
    console.log('Setting up semester lab configurations...');

    // 4th semester configuration (3-hour labs) - Traditional approach
    const config4th = {
      academic_year: "2024-2025",
      department: "cse",
      semester: "4",
      default_lab_duration: 3,
      lab_duration_type: 'three_hour' as const,
      enable_morning_session: true,
      enable_mid_session: false,
      enable_afternoon_session: true,
      enable_early_afternoon_session: false,
      skill_lab_required: true,
      skill_lab_placement_preference: 'vacant_day' as const,
      skill_lab_duration: 3,
      max_labs_per_day: 1,
      allow_consecutive_labs: false,
      prefer_lab_distribution: true,
    };

    // 6th semester configuration (2-hour labs) - Modern approach
    const config6th = {
      academic_year: "2024-2025",
      department: "cse",
      semester: "6",
      default_lab_duration: 2,
      lab_duration_type: 'two_hour' as const,
      enable_morning_session: true,
      enable_mid_session: true,
      enable_afternoon_session: false,
      enable_early_afternoon_session: true,
      skill_lab_required: true,
      skill_lab_placement_preference: 'vacant_day' as const,
      skill_lab_duration: 2,
      max_labs_per_day: 2,
      allow_consecutive_labs: true,
      prefer_lab_distribution: true,
    };

    // 2nd semester configuration (2-hour labs) - For testing
    const config2nd = {
      academic_year: "2024-2025",
      department: "cse",
      semester: "2",
      default_lab_duration: 2,
      lab_duration_type: 'two_hour' as const,
      enable_morning_session: true,
      enable_mid_session: true,
      enable_afternoon_session: false,
      enable_early_afternoon_session: true,
      skill_lab_required: true,
      skill_lab_placement_preference: 'vacant_day' as const,
      skill_lab_duration: 2,
      max_labs_per_day: 2,
      allow_consecutive_labs: true,
      prefer_lab_distribution: true,
    };

    // Save configurations
    console.log('Saving 4th semester configuration...');
    const saved4th = await SemesterLabConfigurationService.saveLabConfiguration(config4th);
    console.log('4th semester config saved:', saved4th);

    console.log('Saving 6th semester configuration...');
    const saved6th = await SemesterLabConfigurationService.saveLabConfiguration(config6th);
    console.log('6th semester config saved:', saved6th);

    console.log('Saving 2nd semester configuration...');
    const saved2nd = await SemesterLabConfigurationService.saveLabConfiguration(config2nd);
    console.log('2nd semester config saved:', saved2nd);

    // Test the configurations
    console.log('\nTesting configurations...');
    
    // Test 4th semester (should show 3-hour options)
    const test4th = await SemesterLabConfigurationService.getLabConfiguration("2024-2025", "cse", "4");
    const slots4th = SemesterLabConfigurationService.getAvailableTimeSlots(test4th!);
    console.log('4th semester time slots:', slots4th);

    // Test 6th semester (should show 2-hour options)
    const test6th = await SemesterLabConfigurationService.getLabConfiguration("2024-2025", "cse", "6");
    const slots6th = SemesterLabConfigurationService.getAvailableTimeSlots(test6th!);
    console.log('6th semester time slots:', slots6th);

    // Test 2nd semester (should show 2-hour options)
    const test2nd = await SemesterLabConfigurationService.getLabConfiguration("2024-2025", "cse", "2");
    const slots2nd = SemesterLabConfigurationService.getAvailableTimeSlots(test2nd!);
    console.log('2nd semester time slots:', slots2nd);

    console.log('\nSemester configurations setup complete!');
    
    return {
      success: true,
      configurations: [saved4th, saved6th, saved2nd],
      timeSlots: {
        '4th': slots4th,
        '6th': slots6th,
        '2nd': slots2nd
      }
    };
  } catch (error) {
    console.error('Error setting up semester configurations:', error);
    return {
      success: false,
      error: error
    };
  }
};

// Function to test a specific semester configuration
export const testSemesterConfiguration = async (
  academicYear: string,
  department: string,
  semester: string
) => {
  try {
    console.log(`Testing configuration for ${semester} semester...`);
    
    const config = await SemesterLabConfigurationService.getLabConfiguration(
      academicYear,
      department,
      semester
    );
    
    console.log('Configuration:', config);
    
    if (config) {
      const timeSlots = SemesterLabConfigurationService.getAvailableTimeSlots(config);
      console.log('Available time slots:', timeSlots);
      
      return {
        success: true,
        config,
        timeSlots
      };
    } else {
      console.log('No configuration found');
      return {
        success: false,
        error: 'No configuration found'
      };
    }
  } catch (error) {
    console.error('Error testing configuration:', error);
    return {
      success: false,
      error
    };
  }
};

// Function to run from browser console for quick setup
export const quickSetup = async () => {
  console.log('🚀 Starting quick semester configuration setup...');
  const result = await setupSemesterConfigurations();
  
  if (result.success) {
    console.log('✅ Setup completed successfully!');
    console.log('📋 Summary:');
    console.log('- 4th semester: 3-hour labs (Morning + Afternoon)');
    console.log('- 6th semester: 2-hour labs (Morning + Mid + Early Afternoon)');
    console.log('- 2nd semester: 2-hour labs (Morning + Mid + Early Afternoon)');
    console.log('\n🧪 Now test by:');
    console.log('1. Go to Subject Allotment');
    console.log('2. Select 2nd or 6th semester');
    console.log('3. Add a lab subject');
    console.log('4. Check Time of Day dropdown for 2-hour options');
  } else {
    console.log('❌ Setup failed:', result.error);
  }
  
  return result;
};

// Make functions available globally for console access
if (typeof window !== 'undefined') {
  (window as any).setupSemesterConfigurations = setupSemesterConfigurations;
  (window as any).testSemesterConfiguration = testSemesterConfiguration;
  (window as any).quickSetup = quickSetup;
}
