// ClassAvailabilityService.ts
import { supabase } from "@/integrations/supabase/client";
import { TimeStructure } from "./TimetableService";

/**
 * Service to manage class-level availability for timetable scheduling
 * Similar to faculty availability but tracks when classes/sections are occupied by lab sessions
 */
export class ClassAvailabilityService {
  /**
   * Initialize class availability for a specific semester-section after lab allocation
   * This creates a "vacant_by_day" and "vacant_count_by_day" structure for the class
   */
  static async initializeClassAvailability(
    academicYear: string,
    department: string,
    semester: string,
    section: string,
    timeStructure: TimeStructure
  ): Promise<{
    vacant_by_day: Record<string, number>;
    vacant_count_by_day: Record<string, string[]>;
  }> {
    console.log(`🏫 Initializing class availability for ${semester}-${section}...`);

    const allTimeSlots = this.getAllTimeSlots(timeStructure);
    const days = timeStructure.working_days;

    // Initialize with all slots available
    const vacant_by_day: Record<string, number> = {};
    const vacant_count_by_day: Record<string, string[]> = {};

    days.forEach(day => {
      vacant_by_day[day] = allTimeSlots.length;
      vacant_count_by_day[day] = [...allTimeSlots];
    });

    // Get all lab slots for this class
    const { data: labSlots, error: labError } = await supabase
      .from("timetable_slots")
      .select("day, time_slot, col_span, subject_code, subject_type")
      .eq("academic_year", academicYear)
      .eq("department", department)
      .eq("semester", semester)
      .eq("section", section)
      .eq("subject_type", "lab");

    if (labError) {
      console.error("Error fetching lab slots:", labError);
      throw labError;
    }

    console.log(`Found ${labSlots?.length || 0} lab slots for ${semester}-${section}`);

    // Remove lab-occupied periods from class availability
    if (labSlots && labSlots.length > 0) {
      for (const labSlot of labSlots) {
        const occupiedPeriods = this.getPeriodsOccupiedByLab(labSlot, allTimeSlots, timeStructure);
        
        console.log(`Lab ${labSlot.subject_code} on ${labSlot.day} occupies periods: ${occupiedPeriods.join(', ')}`);

        // Remove occupied periods from availability
        if (vacant_count_by_day[labSlot.day]) {
          vacant_count_by_day[labSlot.day] = vacant_count_by_day[labSlot.day].filter(
            slot => !occupiedPeriods.includes(slot)
          );
          vacant_by_day[labSlot.day] = vacant_count_by_day[labSlot.day].length;
        }
      }
    }

    // Log the final availability
    console.log(`📊 Class ${semester}-${section} availability after lab allocation:`);
    days.forEach(day => {
      console.log(`  ${day}: ${vacant_by_day[day]} slots available - ${vacant_count_by_day[day].join(', ')}`);
    });

    return {
      vacant_by_day,
      vacant_count_by_day
    };
  }

  /**
   * Update class availability when a theory slot is allocated
   */
  static updateClassAvailabilityForTheorySlot(
    classAvailability: {
      vacant_by_day: Record<string, number>;
      vacant_count_by_day: Record<string, string[]>;
    },
    day: string,
    timeSlot: string
  ): void {
    if (classAvailability.vacant_count_by_day[day]) {
      // Remove the time slot from available slots
      classAvailability.vacant_count_by_day[day] = classAvailability.vacant_count_by_day[day].filter(
        slot => slot !== timeSlot
      );
      // Update the count
      classAvailability.vacant_by_day[day] = classAvailability.vacant_count_by_day[day].length;
    }
  }

  /**
   * Check if a class is available at a specific time
   */
  static isClassAvailable(
    classAvailability: {
      vacant_by_day: Record<string, number>;
      vacant_count_by_day: Record<string, string[]>;
    },
    day: string,
    timeSlot: string
  ): boolean {
    return classAvailability.vacant_count_by_day[day]?.includes(timeSlot) || false;
  }

  /**
   * Get all available time slots for a class on a specific day
   */
  static getAvailableSlots(
    classAvailability: {
      vacant_by_day: Record<string, number>;
      vacant_count_by_day: Record<string, string[]>;
    },
    day: string
  ): string[] {
    return classAvailability.vacant_count_by_day[day] || [];
  }

  /**
   * Generate time slots based on time structure
   */
  private static getAllTimeSlots(timeStructure: TimeStructure): string[] {
    const slots: string[] = [];
    const dur = timeStructure.theory_class_duration;

    // 1. Before tea break
    let cur = timeStructure.first_half_start_time;
    const teaStart = timeStructure.tea_break_start_time;

    while (cur < teaStart) {
      const end = this.addMinutes(cur, dur);
      if (end <= teaStart) {
        slots.push(`${cur}-${end}`);
      }
      cur = end;
    }

    // 2. After tea break, before lunch
    cur = timeStructure.tea_break_end_time;
    const lunchStart = timeStructure.lunch_break_start_time;

    while (cur < lunchStart) {
      const end = this.addMinutes(cur, dur);
      if (end <= lunchStart) {
        slots.push(`${cur}-${end}`);
      }
      cur = end;
    }

    // 3. After lunch
    cur = timeStructure.lunch_break_end_time;
    const dayEnd = timeStructure.second_half_end_time ||
      this.addMinutes(cur, timeStructure.periods_in_second_half * dur);

    while (cur < dayEnd) {
      const end = this.addMinutes(cur, dur);
      slots.push(`${cur}-${end}`);
      cur = end;
    }

    return slots;
  }

  /**
   * Determine which periods a lab occupies
   */
  private static getPeriodsOccupiedByLab(
    labSlot: any,
    allTimeSlots: string[],
    timeStructure: TimeStructure
  ): string[] {
    const occupiedPeriods: string[] = [];
    const [labStartTime] = labSlot.time_slot.split('-');
    const periodsToOccupy = labSlot.col_span || 3;

    const startIndex = allTimeSlots.findIndex(slot => slot.startsWith(labStartTime));

    if (startIndex >= 0) {
      occupiedPeriods.push(allTimeSlots[startIndex]);

      let currentIndex = startIndex;
      let periodsAdded = 1;

      while (periodsAdded < periodsToOccupy && currentIndex + 1 < allTimeSlots.length) {
        currentIndex++;
        const [currentStart] = allTimeSlots[currentIndex].split('-');

        // Skip breaks
        if (currentStart === timeStructure.tea_break_start_time ||
            currentStart === timeStructure.lunch_break_start_time) {
          continue;
        }

        occupiedPeriods.push(allTimeSlots[currentIndex]);
        periodsAdded++;
      }
    }

    return occupiedPeriods;
  }

  /**
   * Add minutes to time string
   */
  private static addMinutes(time: string, mins: number): string {
    const [h, m] = time.split(":").map(Number);
    const total = h * 60 + m + mins;
    const nh = Math.floor(total / 60);
    const nm = total % 60;
    return `${String(nh).padStart(2, "0")}:${String(nm).padStart(2, "0")}`;
  }
}
