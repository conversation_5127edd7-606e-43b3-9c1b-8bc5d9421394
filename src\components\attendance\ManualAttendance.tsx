import React, { useState, useEffect, memo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useFacultyStudents } from '@/hooks/useFacultyStudents';
import { useSubstituteNotifications } from '@/hooks/useSubstituteNotifications';
import { AttendanceService } from '@/services/AttendanceService';
import SubstituteClassCard from '@/components/attendance/SubstituteClassCard';
import { AttendanceAccessControl } from './AttendanceAccessControl';
import { useAttendanceAccessControl } from '@/hooks/useAttendanceAccessControl';
import { AttendanceAccessControlService } from '@/services/AttendanceAccessControlService';
import {
  Calendar,
  Clock,
  Users,
  BookOpen,
  FlaskConical,
  MapPin,
  Check,
  X,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle2,
  Edit,
  ShieldX
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface TodaysClass {
  id: string;
  subject_code: string;
  subject_name: string;
  subject_type: string;
  time_slot: string;
  period_number?: number;
  semester: string;
  section: string;
  department: string;
  room_number?: string;
  batch_name?: string;
  // Substitute-specific fields
  is_substitute?: boolean;
  original_faculty_name?: string;
  original_faculty_id?: string;
  original_subject_code?: string;
  original_subject_name?: string;
  substitute_faculty_subject_code?: string;
  substitute_faculty_subject_name?: string;
  leave_request_id?: string;
  substitution_notes?: string;
  // NEW: Lab access control fields
  is_substitute_access?: boolean; // True when secondary faculty gets access due to primary on leave
  substitute_reason?: string; // Reason for substitute access
  access_level?: 'full' | 'substitute' | 'disabled'; // Access level for the faculty
  is_disabled?: boolean; // True when class is visible but not accessible
  disabled_reason?: string; // Reason why class is disabled
  primary_faculty_name?: string; // Name of primary faculty (for disabled classes)
}

interface StudentAttendance {
  id: string;
  usn: string;
  student_name: string;
  status: 'present' | 'absent';
  attendance_id?: string;
  is_from_batch?: boolean;
}

interface AttendanceSession {
  students: StudentAttendance[];
  classInfo: {
    subject_code: string;
    subject_type: string;
    semester: string;
    section: string;
    department: string;
    batch_name?: string;
    date: string;
  };
  hasExistingAttendance?: boolean;
  attendanceCount?: number;
  error?: {
    type: 'LAB_BATCH_NOT_CONFIGURED' | 'LAB_BATCH_MISSING';
    batchName?: string;
    message: string;
  };
}

// CRITICAL FIX: Memoize the component to prevent unnecessary re-renders
const ManualAttendance: React.FC = memo(() => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [todaysClasses, setTodaysClasses] = useState<TodaysClass[]>([]);
  const [selectedClass, setSelectedClass] = useState<TodaysClass | null>(null);
  const [attendanceSession, setAttendanceSession] = useState<AttendanceSession | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [loadingClasses, setLoadingClasses] = useState(false);
  const [classAttendanceStatus, setClassAttendanceStatus] = useState<Record<string, boolean>>({});
  const [statusLoading, setStatusLoading] = useState<Record<string, boolean>>({});
  const [statusCheckInProgress, setStatusCheckInProgress] = useState(false);

  const { user } = useAuth();
  const { department } = useUserDepartment();
  const { toast } = useToast();
  const { unreadCount, checkForNewAssignments } = useSubstituteNotifications();

  // Access control for selected class
  const { accessResult, loading: accessLoading, recheckAccess } = useAttendanceAccessControl({
    facultyId: user?.id || '',
    classInfo: selectedClass ? {
      subject_code: selectedClass.subject_code,
      subject_type: selectedClass.subject_type,
      semester: selectedClass.semester,
      section: selectedClass.section,
      department: selectedClass.department,
      batch_name: selectedClass.batch_name,
      time_slot: selectedClass.time_slot
    } : {
      subject_code: '',
      subject_type: 'theory',
      semester: '',
      section: '',
      department: '',
    },
    date: selectedDate,
    enabled: !!selectedClass && !!user?.id
  });

  // State to track access control for each class card
  const [classAccessResults, setClassAccessResults] = useState<Record<string, any>>({});

  // Load classes when component mounts or when date/user/department changes
  useEffect(() => {
    if (user?.id && department && selectedDate) {
      loadTodaysClasses();
    }
  }, [user?.id, department, selectedDate]);

  // Clear selected class and attendance session when date changes
  useEffect(() => {
    setSelectedClass(null);
    setAttendanceSession(null);
    // CRITICAL FIX: Also clear status when date changes to force fresh check
    setClassAttendanceStatus({});
    setStatusLoading({});
    setStatusCheckInProgress(false); // Clear any ongoing status checks
  }, [selectedDate]);

  const loadTodaysClasses = async () => {
    if (!user?.id || !department) return;

    try {
      setLoadingClasses(true);

      const classesData = await AttendanceService.getTodaysClasses(
        user.id,
        department,
        selectedDate
      );

      setTodaysClasses(classesData.classes);

      // DEBUGGING: Log the classes data received from service
      console.log('🔍 COMPONENT: Received classes from AttendanceService:');
      classesData.classes.forEach((c, index) => {
        console.log(`  Class ${index + 1}:`, {
          subject_code: c.subject_code,
          subject_type: c.subject_type,
          semester: c.semester,
          section: c.section,
          access_level: c.access_level,
          is_disabled: c.is_disabled,
          is_substitute_access: c.is_substitute_access,
          primary_faculty_name: c.primary_faculty_name,
          disabled_reason: c.disabled_reason,
          allProperties: Object.keys(c)
        });
      });

      // Check attendance status for each class
      await checkAttendanceStatusForClasses(classesData.classes);

      // Check access control for each class
      await checkAccessControlForClasses(classesData.classes);

      // Clear selected class if it's not in today's classes
      if (selectedClass && !classesData.classes.find(c => c.id === selectedClass.id)) {
        setSelectedClass(null);
        setAttendanceSession(null);
      }
    } catch (error) {
      console.error('Error loading today\'s classes:', error);
      toast({
        title: 'Error',
        description: 'Failed to load today\'s classes.',
        variant: 'destructive',
      });
    } finally {
      setLoadingClasses(false);
    }
  };

  const checkAttendanceStatusForClasses = async (classes: TodaysClass[]) => {
    if (!user?.id || !department) return;

    // CRITICAL FIX: Prevent concurrent status checks to avoid race conditions
    if (statusCheckInProgress) {
      console.log('🔒 STATUS CHECK: Skipping concurrent status check to prevent race conditions');
      return;
    }

    setStatusCheckInProgress(true);
    console.log('🔍 STATUS CHECK: Starting attendance status check for', classes.length, 'classes');

    // CRITICAL FIX: Set loading state for all classes at once to prevent flickering
    const initialLoadingMap: Record<string, boolean> = {};
    classes.forEach(classInfo => {
      initialLoadingMap[classInfo.id] = true;
    });
    setStatusLoading(prev => ({ ...prev, ...initialLoadingMap }));

    // CRITICAL FIX: Don't reset existing status to prevent flickering during re-renders
    // Only initialize status for classes that don't have a status yet
    setClassAttendanceStatus(prev => {
      const newStatusMap = { ...prev };
      classes.forEach(classInfo => {
        // Only set to false if the class doesn't already have a status
        if (!(classInfo.id in newStatusMap)) {
          newStatusMap[classInfo.id] = false;
        }
      });
      return newStatusMap;
    });

    // CRITICAL FIX: Check all classes in parallel for better performance
    const statusPromises = classes.map(async (classInfo) => {
      try {
        console.log(`🔍 STATUS CHECK: Checking ${classInfo.subject_code} (${classInfo.subject_type}) at ${classInfo.time_slot}`);

        // CRITICAL FIX: Use the same logic as saving to check attendance status
        const hasAttendance = await AttendanceService.checkAttendanceStatus(
          {
            subject_code: classInfo.subject_code,
            subject_type: classInfo.subject_type,
            semester: classInfo.semester,
            section: classInfo.section,
            department: classInfo.department,
            batch_name: classInfo.batch_name,
            time_slot: classInfo.time_slot, // CRITICAL FIX: Include time_slot for proper filtering
            // Pass substitute context for proper identifier creation
            is_substitute: classInfo.is_substitute,
            original_faculty_id: classInfo.original_faculty_id,
            leave_request_id: classInfo.leave_request_id
          },
          selectedDate,
          user.id,
          department
        );

        console.log(`✅ STATUS CHECK: ${classInfo.subject_code} at ${classInfo.time_slot} status: ${hasAttendance ? 'MARKED' : 'NOT MARKED'}`);
        return { classId: classInfo.id, hasAttendance, timeSlot: classInfo.time_slot, subjectCode: classInfo.subject_code };
      } catch (error) {
        console.error(`❌ STATUS CHECK: Error checking ${classInfo.subject_code}:`, error);
        return { classId: classInfo.id, hasAttendance: false, timeSlot: classInfo.time_slot, subjectCode: classInfo.subject_code };
      }
    });

    // CRITICAL FIX: Wait for all status checks to complete, then update state once
    try {
      const results = await Promise.all(statusPromises);

      const finalStatusMap: Record<string, boolean> = {};
      const finalLoadingMap: Record<string, boolean> = {};

      results.forEach(({ classId, hasAttendance, timeSlot, subjectCode }) => {
        finalStatusMap[classId] = hasAttendance;
        finalLoadingMap[classId] = false;
        console.log(`🎯 STATUS RESULT: ${subjectCode} at ${timeSlot} (ID: ${classId}) = ${hasAttendance ? 'MARKED' : 'PENDING'}`);
      });

      console.log('✅ STATUS CHECK: Final status map:', finalStatusMap);

      // CRITICAL FIX: Update states using functional updates to preserve existing values
      setClassAttendanceStatus(prev => ({ ...prev, ...finalStatusMap }));
      setStatusLoading(prev => ({ ...prev, ...finalLoadingMap }));
    } catch (error) {
      console.error('❌ STATUS CHECK: Error in parallel status checking:', error);

      // CRITICAL FIX: Clear loading state even on error
      const errorLoadingMap: Record<string, boolean> = {};
      classes.forEach(classInfo => {
        errorLoadingMap[classInfo.id] = false;
      });
      setStatusLoading(prev => ({ ...prev, ...errorLoadingMap }));
    } finally {
      // CRITICAL FIX: Always clear the status check flag to allow future checks
      setStatusCheckInProgress(false);
      console.log('🔓 STATUS CHECK: Status check completed, flag cleared');
    }
  };

  const checkAccessControlForClasses = async (classes: TodaysClass[]) => {
    if (!user?.id) return;

    console.log('🔐 ACCESS CONTROL: Checking access for all classes');
    const accessResults: Record<string, any> = {};

    for (const classInfo of classes) {
      try {
        const result = await AttendanceAccessControlService.checkAttendanceAccess(
          user.id,
          {
            subject_code: classInfo.subject_code,
            subject_type: classInfo.subject_type,
            semester: classInfo.semester,
            section: classInfo.section,
            department: classInfo.department,
            batch_name: classInfo.batch_name,
            time_slot: classInfo.time_slot
          },
          selectedDate
        );

        accessResults[classInfo.id] = result;
        console.log(`🔐 ACCESS CONTROL: Class ${classInfo.subject_code} - ${result.canAccess ? 'ALLOWED' : 'BLOCKED'}`);
      } catch (error) {
        console.error(`🔐 ACCESS CONTROL: Error checking access for ${classInfo.subject_code}:`, error);
        // Fallback to allow access on error
        accessResults[classInfo.id] = { canAccess: true, reason: 'error_fallback' };
      }
    }

    setClassAccessResults(accessResults);
  };

  const handleClassSelect = async (classInfo: TodaysClass) => {
    console.log('🚀 ManualAttendance: handleClassSelect called with:', classInfo.subject_code);
    console.log('🔍 ManualAttendance: user?.id:', user?.id, 'department:', department);

    if (!user?.id || !department) {
      console.error('❌ ManualAttendance: Validation failed - missing user or department');
      return;
    }

    // Check if class is disabled (visible but not accessible)
    if (classInfo.is_disabled) {
      console.log('🚫 CLASS DISABLED: Blocking class selection - class is disabled');
      toast({
        title: 'Class Not Available',
        description: classInfo.disabled_reason || 'Primary faculty handles attendance for this lab class.',
        variant: 'default',
      });
      return;
    }

    // Check access control before allowing selection
    const accessResult = classAccessResults[classInfo.id];
    if (accessResult && !accessResult.canAccess) {
      console.log('🔐 ACCESS CONTROL: Blocking class selection - access denied');
      toast({
        title: 'Access Restricted',
        description: accessResult.blockingInfo
          ? `This class is being handled by substitute faculty: ${accessResult.blockingInfo.substitute_faculty_name}`
          : 'You do not have permission to mark attendance for this class.',
        variant: 'destructive',
      });
      return;
    }

    try {
      console.log('✅ ManualAttendance: Starting attendance loading...');
      setLoading(true);
      setSelectedClass(classInfo);

      console.log('📞 ManualAttendance: About to call AttendanceService...');
      // Use the new dynamic student loading service
      const sessionData = await AttendanceService.getStudentsForManualAttendanceWithDynamicLoading(
        {
          subject_code: classInfo.subject_code,
          subject_type: classInfo.subject_type,
          semester: classInfo.semester,
          section: classInfo.section,
          department: classInfo.department,
          batch_name: classInfo.batch_name,
          time_slot: classInfo.time_slot, // CRITICAL FIX: Include time_slot for proper filtering
          // Pass substitute context for proper identifier creation
          is_substitute: classInfo.is_substitute,
          original_faculty_id: classInfo.original_faculty_id,
          leave_request_id: classInfo.leave_request_id
        },
        selectedDate,
        user.id,
        department
      );

      console.log('✅ ManualAttendance: Received session data:', sessionData);
      setAttendanceSession(sessionData);

      // Show info about the student list source
      if (sessionData.students.length > 0) {
        const sourceInfo = sessionData.students[0].is_from_batch
          ? `Loaded ${sessionData.students.length} students from batch ${classInfo.batch_name}`
          : `Loaded ${sessionData.students.length} students from class list`;

        console.log(sourceInfo);
      } else {
        console.log('⚠️ ManualAttendance: No students found in session data');
      }
    } catch (error) {
      console.error('❌ ManualAttendance: Error loading students:', error);

      // Handle specific lab batch configuration errors
      if (error instanceof Error) {
        if (error.message.startsWith('LAB_BATCH_NOT_CONFIGURED:')) {
          const batchName = error.message.split(':')[1];
          setAttendanceSession({
            students: [],
            classInfo: {
              ...classInfo,
              date: selectedDate
            },
            hasExistingAttendance: false,
            attendanceCount: 0,
            error: {
              type: 'LAB_BATCH_NOT_CONFIGURED',
              batchName: batchName,
              message: `Lab batch "${batchName}" students have not been configured yet.`
            }
          });
          return; // Don't show toast, we'll show a custom message in the UI
        } else if (error.message.startsWith('LAB_BATCH_MISSING:')) {
          setAttendanceSession({
            students: [],
            classInfo: {
              ...classInfo,
              date: selectedDate
            },
            hasExistingAttendance: false,
            attendanceCount: 0,
            error: {
              type: 'LAB_BATCH_MISSING',
              message: 'Lab class is missing batch assignment information.'
            }
          });
          return; // Don't show toast, we'll show a custom message in the UI
        }
      }

      toast({
        title: 'Error',
        description: 'Failed to load students for attendance.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleAttendance = (studentId: string) => {
    if (!attendanceSession) return;

    setAttendanceSession({
      ...attendanceSession,
      students: attendanceSession.students.map(student =>
        student.id === studentId
          ? { ...student, status: student.status === 'present' ? 'absent' : 'present' }
          : student
      )
    });
  };

  const handleSaveAttendance = async () => {
    if (!attendanceSession || !selectedClass || !user?.id) return;

    try {
      setSaving(true);

      const attendanceData = attendanceSession.students.map(student => ({
        student_id: student.id,
        subject_code: selectedClass.subject_code,
        faculty_id: user.id,
        department: selectedClass.department,
        semester: selectedClass.semester,
        section: selectedClass.section,
        attendance_date: selectedDate,
        time_slot: selectedClass.time_slot,
        status: student.status,
        attendance_id: student.attendance_id,
        // Substitute-specific metadata
        is_substitute_marking: selectedClass.is_substitute || false,
        original_faculty_id: selectedClass.is_substitute ? selectedClass.original_faculty_id : undefined,
        leave_request_id: selectedClass.leave_request_id,
        substitution_notes: selectedClass.substitution_notes
      }));

      // CRITICAL FIX: Pass classInfo to ensure correct subject type detection
      await AttendanceService.saveManualAttendance(attendanceData, user.id, {
        subject_type: selectedClass.subject_type,
        batch_name: selectedClass.batch_name,
        // Pass substitute context
        is_substitute: selectedClass.is_substitute,
        original_faculty_id: selectedClass.original_faculty_id,
        original_faculty_name: selectedClass.original_faculty_name,
        leave_request_id: selectedClass.leave_request_id
      });

      toast({
        title: 'Success',
        description: `Attendance saved for ${attendanceData.length} students.`,
      });

      // CRITICAL FIX: Immediately update the status for the current class to prevent flickering
      setClassAttendanceStatus(prev => ({
        ...prev,
        [selectedClass.id]: true
      }));

      // CRITICAL FIX: Also clear any loading state for this class
      setStatusLoading(prev => ({
        ...prev,
        [selectedClass.id]: false
      }));

      console.log(`🎯 SAVE SUCCESS: Updated status for ${selectedClass.subject_code} at ${selectedClass.time_slot} (ID: ${selectedClass.id}) to MARKED`);

      // Reload the session to show updated data
      await handleClassSelect(selectedClass);

      // CRITICAL FIX: Don't refresh other classes' status to prevent race conditions and status shifting
      // The status should remain stable unless explicitly refreshed by user action
      console.log('🔒 STABILITY: Skipping status refresh for other classes to prevent status shifting');
    } catch (error) {
      console.error('Error saving attendance:', error);
      toast({
        title: 'Error',
        description: 'Failed to save attendance.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const presentCount = attendanceSession?.students.filter(s => s.status === 'present').length || 0;
  const absentCount = attendanceSession?.students.filter(s => s.status === 'absent').length || 0;
  const totalStudents = attendanceSession?.students.length || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">Manual Attendance</h1>
            {unreadCount > 0 && (
              <Badge className="bg-blue-600 text-white">
                {unreadCount} New Substitute Assignment{unreadCount > 1 ? 's' : ''}
              </Badge>
            )}
          </div>
          <p className="text-muted-foreground">
            Mark attendance manually for your classes and substitute assignments
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="date">Date:</Label>
            <Input
              id="date"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-auto"
            />
          </div>
          <Button
            variant="outline"
            onClick={async () => {
              await loadTodaysClasses();
              await checkForNewAssignments();
            }}
            disabled={loadingClasses}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loadingClasses ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Access Level Summary for Lab Classes */}
      {todaysClasses.some(c => c.subject_type === 'lab') && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-blue-800 mb-3 flex items-center gap-2">
            <FlaskConical className="h-4 w-4" />
            Lab Class Access Levels Guide
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
            <div className="flex items-center gap-2 p-2 bg-green-100 rounded border border-green-300">
              <div className="bg-green-500 text-white rounded-full p-1 flex-shrink-0">
                <Check className="h-3 w-3" />
              </div>
              <div>
                <div className="font-medium text-green-800">Primary Faculty</div>
                <div className="text-green-700">Full access to mark attendance</div>
              </div>
            </div>
            <div className="flex items-center gap-2 p-2 bg-orange-100 rounded border border-orange-300">
              <div className="bg-orange-500 text-white rounded-full p-1 flex-shrink-0">
                <ShieldX className="h-3 w-3" />
              </div>
              <div>
                <div className="font-medium text-orange-800">Substitute Access</div>
                <div className="text-orange-700">Primary faculty on leave</div>
              </div>
            </div>
            <div className="flex items-center gap-2 p-2 bg-red-100 rounded border border-red-300">
              <div className="bg-red-500 text-white rounded-full p-1 flex-shrink-0">
                <X className="h-3 w-3" />
              </div>
              <div>
                <div className="font-medium text-red-800">Secondary Faculty</div>
                <div className="text-red-700">Restricted access</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Today's Classes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Classes for {new Date(selectedDate).toLocaleDateString()}
          </CardTitle>
          <CardDescription>
            Select a class to mark attendance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingClasses ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">Loading classes...</p>
              </div>
            </div>
          ) : todaysClasses.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">No classes scheduled for this date.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Check if this is a substitute-only faculty */}
              {todaysClasses.length > 0 && todaysClasses.every(c => c.is_substitute) && (
                <Alert className="border-blue-200 bg-blue-50">
                  <Users className="h-4 w-4" />
                  <AlertDescription className="text-blue-800">
                    <strong>Substitute Faculty Assignment</strong>
                    <br />
                    You have been assigned as substitute faculty for today. All classes shown below are substitute assignments.
                  </AlertDescription>
                </Alert>
              )}

              {/* Regular Classes */}
              {todaysClasses.filter(c => !c.is_substitute).length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    Regular Classes
                  </h3>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {todaysClasses.filter(c => !c.is_substitute).map((classInfo) => {
                      const hasAttendance = classAttendanceStatus[classInfo.id];
                      const isStatusLoading = statusLoading[classInfo.id];
                      const accessResult = classAccessResults[classInfo.id];
                      const isBlocked = accessResult && !accessResult.canAccess;
                      const isDisabled = classInfo.is_disabled || false;

                      // DEBUGGING: Force visual differentiation for lab classes
                      const isLabClass = classInfo.subject_type === 'lab';
                      const isSecondaryFaculty = isLabClass && isDisabled;
                      const isPrimaryFaculty = isLabClass && classInfo.access_level === 'full';
                      const isSubstituteAccess = isLabClass && classInfo.is_substitute_access;

                      // Log for debugging
                      if (isLabClass) {
                        console.log(`🎨 LAB CLASS STYLING: ${classInfo.subject_code} Sem ${classInfo.semester} Sec ${classInfo.section}`, {
                          access_level: classInfo.access_level,
                          is_disabled: isDisabled,
                          is_substitute_access: classInfo.is_substitute_access,
                          isSecondaryFaculty,
                          isPrimaryFaculty,
                          isSubstituteAccess,
                          expectedColor: isSecondaryFaculty ? 'RED' : isSubstituteAccess ? 'ORANGE' : isPrimaryFaculty ? 'GREEN' : 'DEFAULT'
                        });
                      }



                      return (
                        <Card
                          key={classInfo.id}
                          className={`transition-all relative ${
                            isBlocked
                              ? 'cursor-not-allowed opacity-60 border-red-200 bg-red-50 dark:bg-red-950/20'
                              : isSecondaryFaculty
                              ? 'cursor-not-allowed opacity-70 border-red-500 bg-red-100 dark:bg-red-950/20 border-2'
                              : isSubstituteAccess
                              ? 'cursor-pointer hover:shadow-md border-orange-500 bg-orange-100 dark:bg-orange-950/20 border-2'
                              : isPrimaryFaculty
                              ? 'cursor-pointer hover:shadow-md border-green-500 bg-green-100 dark:bg-green-950/20 border-2'
                              : 'cursor-pointer hover:shadow-md'
                          } ${
                            selectedClass?.id === classInfo.id ? 'ring-2 ring-primary' : ''
                          } ${hasAttendance && !isDisabled ? 'border-green-200 bg-green-50' : ''}`}
                          onClick={() => !isBlocked && !isSecondaryFaculty && handleClassSelect(classInfo)}
                        >
                          {/* Enhanced Access Level Indicators */}
                          {isSecondaryFaculty && (
                            <div className="absolute top-2 right-2 z-10">
                              <div className="bg-red-500 text-white rounded-full p-1 shadow-md">
                                <X className="h-4 w-4" />
                              </div>
                            </div>
                          )}
                          {isSubstituteAccess && (
                            <div className="absolute top-2 right-2 z-10">
                              <div className="bg-orange-500 text-white rounded-full p-1 shadow-md">
                                <ShieldX className="h-4 w-4" />
                              </div>
                            </div>
                          )}
                          {isPrimaryFaculty && (
                            <div className="absolute top-2 right-2 z-10">
                              <div className="bg-green-500 text-white rounded-full p-1 shadow-md">
                                <Check className="h-4 w-4" />
                              </div>
                            </div>
                          )}
                          <CardContent className="p-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Badge variant={classInfo.subject_type === 'lab' ? 'secondary' : 'default'}>
                                    {classInfo.subject_type === 'lab' ? (
                                      <FlaskConical className="h-3 w-3 mr-1" />
                                    ) : (
                                      <BookOpen className="h-3 w-3 mr-1" />
                                    )}
                                    {classInfo.subject_type.toUpperCase()}
                                  </Badge>
                                  {/* Enhanced Lab Access Control Badges */}
                                  {isLabClass && (
                                    <>
                                      {isSubstituteAccess ? (
                                        <Badge variant="outline" className="text-orange-800 border-orange-500 bg-orange-100 font-medium">
                                          <ShieldX className="h-3 w-3 mr-1" />
                                          Substitute Access
                                        </Badge>
                                      ) : isSecondaryFaculty ? (
                                        <Badge variant="outline" className="text-red-700 border-red-500 bg-red-100 font-medium">
                                          <X className="h-3 w-3 mr-1" />
                                          Secondary Faculty - Restricted
                                        </Badge>
                                      ) : isPrimaryFaculty ? (
                                        <Badge variant="outline" className="text-green-700 border-green-500 bg-green-100 font-medium">
                                          <Check className="h-3 w-3 mr-1" />
                                          Primary Faculty - Full Access
                                        </Badge>
                                      ) : null}
                                    </>
                                  )}
                                  {/* Access Control Badge */}
                                  {isBlocked && accessResult?.blockingInfo ? (
                                    <Badge variant="destructive" className="text-red-800 border-red-300 bg-red-100">
                                      <ShieldX className="h-3 w-3 mr-1" />
                                      Substitute: {accessResult.blockingInfo.substitute_faculty_name.split(' ').slice(-1)[0]}
                                    </Badge>
                                  ) : (
                                    <>
                                      {/* CRITICAL FIX: Improved status badge rendering with stable display */}
                                      {isStatusLoading ? (
                                        <Badge variant="outline" className="text-blue-700 border-blue-300 bg-blue-50">
                                          <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                                          Checking...
                                        </Badge>
                                      ) : hasAttendance ? (
                                        <Badge variant="outline" className="text-green-700 border-green-300 bg-green-100">
                                          <CheckCircle2 className="h-3 w-3 mr-1" />
                                          Marked
                                        </Badge>
                                      ) : (
                                        <Badge variant="outline" className="text-gray-500 border-gray-300 bg-gray-50">
                                          <Clock className="h-3 w-3 mr-1" />
                                          Pending
                                        </Badge>
                                      )}
                                    </>
                                  )}
                                </div>
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <Clock className="h-3 w-3" />
                                  {classInfo.time_slot}
                                </div>
                              </div>

                              <div>
                                <h3 className="font-semibold">{classInfo.subject_code}</h3>
                                <p className="text-sm text-muted-foreground">{classInfo.subject_name}</p>
                              </div>

                              <div className="flex items-center justify-between text-sm">
                                <span>Sem {classInfo.semester} - Sec {classInfo.section}</span>
                                {classInfo.batch_name && (
                                  <Badge variant="outline" className="text-xs">
                                    Batch {classInfo.batch_name}
                                  </Badge>
                                )}
                              </div>

                              {classInfo.room_number && (
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <MapPin className="h-3 w-3" />
                                  {classInfo.room_number}
                                </div>
                              )}

                              {isBlocked ? (
                                <div className="flex items-center gap-1 text-xs text-red-700 mt-2">
                                  <ShieldX className="h-3 w-3" />
                                  Access restricted - handled by substitute
                                </div>
                              ) : classInfo.subject_type === 'lab' ? (
                                // Enhanced messaging for lab classes
                                <>
                                  {classInfo.is_substitute_access ? (
                                    <div className="flex items-center gap-2 text-xs text-orange-700 mt-2 p-2 bg-orange-100 rounded border border-orange-300">
                                      <ShieldX className="h-4 w-4 text-orange-600 flex-shrink-0" />
                                      <div>
                                        <div className="font-medium">Substitute access granted</div>
                                        <div className="text-orange-600">
                                          {classInfo.primary_faculty_name ? `${classInfo.primary_faculty_name} is on leave` : 'Primary faculty is on leave'}
                                        </div>
                                      </div>
                                    </div>
                                  ) : isDisabled ? (
                                    <div className="flex items-center gap-2 text-xs text-red-700 mt-2 p-2 bg-red-100 rounded border border-red-300">
                                      <X className="h-4 w-4 text-red-600 flex-shrink-0" />
                                      <div>
                                        <div className="font-medium">You are secondary faculty</div>
                                        <div className="text-red-600">
                                          {classInfo.primary_faculty_name ? `${classInfo.primary_faculty_name} handles attendance` : 'Primary faculty handles attendance'}
                                        </div>
                                      </div>
                                    </div>
                                  ) : classInfo.access_level === 'full' ? (
                                    <div className="flex items-center gap-2 text-xs text-green-700 mt-2 p-2 bg-green-100 rounded border border-green-300">
                                      <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                                      <div>
                                        <div className="font-medium">You are the primary faculty</div>
                                        <div className="text-green-600">
                                          {hasAttendance ? 'Click to edit attendance' : 'Click to mark attendance'}
                                        </div>
                                      </div>
                                    </div>
                                  ) : hasAttendance ? (
                                    <div className="flex items-center gap-1 text-xs text-green-700 mt-2">
                                      <Edit className="h-3 w-3" />
                                      Click to edit attendance
                                    </div>
                                  ) : null}
                                </>
                              ) : hasAttendance ? (
                                <div className="flex items-center gap-1 text-xs text-green-700 mt-2">
                                  <Edit className="h-3 w-3" />
                                  Click to edit attendance
                                </div>
                              ) : (
                                <div className="flex items-center gap-1 text-xs text-blue-700 mt-2">
                                  <Users className="h-3 w-3" />
                                  Click to mark attendance
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Substitute Classes */}
              {todaysClasses.filter(c => c.is_substitute).length > 0 && (
                <div className="space-y-4">
                  <h3 className={`text-sm font-medium flex items-center gap-2 ${
                    todaysClasses.every(c => c.is_substitute)
                      ? 'text-blue-700' // More prominent if only substitute classes
                      : 'text-muted-foreground'
                  }`}>
                    <Users className="h-4 w-4 text-blue-600" />
                    {todaysClasses.every(c => c.is_substitute) ? 'Your Substitute Assignments' : 'Substitute Classes'}
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                      {todaysClasses.filter(c => c.is_substitute).length} assignments
                    </Badge>
                  </h3>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {todaysClasses.filter(c => c.is_substitute).map((classInfo) => (
                      <SubstituteClassCard
                        key={classInfo.id}
                        classInfo={classInfo}
                        hasAttendance={classAttendanceStatus[classInfo.id]}
                        isLoading={statusLoading[classInfo.id]}
                        onClick={() => handleClassSelect(classInfo)}
                        isSelected={selectedClass?.id === classInfo.id}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Attendance Marking Interface */}
      {selectedClass && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Mark Attendance - {selectedClass.subject_code}
                </CardTitle>
                <CardDescription>
                  {selectedClass.subject_name?.replace(' (SUBSTITUTE)', '')} • Semester {selectedClass.semester} Section {selectedClass.section}
                  {selectedClass.batch_name && ` • Batch ${selectedClass.batch_name}`}
                  {selectedClass.is_substitute && (
                    <div className="mt-1 p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                      <span className="text-sm text-blue-800 dark:text-blue-200">
                        🔄 <strong>Substitute Class:</strong> You are marking attendance for {selectedClass.original_faculty_name}
                        {selectedClass.substitution_notes && (
                          <div className="mt-1 text-xs">
                            <strong>Instructions:</strong> {selectedClass.substitution_notes}
                          </div>
                        )}
                      </span>
                    </div>
                  )}
                  {/* Enhanced Lab Access Control Indicators */}
                  {selectedClass.subject_type === 'lab' && (
                    <>
                      {selectedClass.is_substitute_access ? (
                        <div className="mt-1 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-md border border-orange-300">
                          <div className="flex items-center gap-2 mb-2">
                            <ShieldX className="h-5 w-5 text-orange-600" />
                            <span className="text-sm font-semibold text-orange-800 dark:text-orange-200">
                              Substitute Access Granted
                            </span>
                          </div>
                          <div className="text-sm text-orange-700 dark:text-orange-300">
                            You are marking attendance as substitute for{' '}
                            <strong>{selectedClass.primary_faculty_name || 'the primary faculty'}</strong> who is on approved leave.
                          </div>
                          <div className="mt-2 text-xs text-orange-600 dark:text-orange-400">
                            This access is temporary and will be revoked when the primary faculty returns.
                          </div>
                        </div>
                      ) : selectedClass.access_level === 'full' ? (
                        <div className="mt-1 p-3 bg-green-100 dark:bg-green-900/30 rounded-md border border-green-300">
                          <div className="flex items-center gap-2 mb-2">
                            <Check className="h-5 w-5 text-green-600" />
                            <span className="text-sm font-semibold text-green-800 dark:text-green-200">
                              Primary Faculty - Full Access
                            </span>
                          </div>
                          <div className="text-sm text-green-700 dark:text-green-300">
                            You are the primary faculty for this lab class and have full access to mark attendance.
                          </div>
                        </div>
                      ) : selectedClass.is_disabled ? (
                        <div className="mt-1 p-3 bg-red-100 dark:bg-red-900/30 rounded-md border border-red-300">
                          <div className="flex items-center gap-2 mb-2">
                            <X className="h-5 w-5 text-red-600" />
                            <span className="text-sm font-semibold text-red-800 dark:text-red-200">
                              Secondary Faculty - Access Restricted
                            </span>
                          </div>
                          <div className="text-sm text-red-700 dark:text-red-300">
                            You are secondary faculty for this lab class.{' '}
                            <strong>{selectedClass.primary_faculty_name || 'The primary faculty'}</strong> handles attendance marking.
                          </div>
                          <div className="mt-2 text-xs text-red-600 dark:text-red-400">
                            Access will be granted automatically if the primary faculty goes on approved leave.
                          </div>
                        </div>
                      ) : null}
                    </>
                  )}
                </CardDescription>
              </div>
              {attendanceSession && (
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{presentCount}</div>
                    <div className="text-sm text-muted-foreground">Present</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{absentCount}</div>
                    <div className="text-sm text-muted-foreground">Absent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{totalStudents}</div>
                    <div className="text-sm text-muted-foreground">Total</div>
                  </div>
                </div>
              )}
            </div>
          </CardHeader>

          {/* Access Control Check */}
          {selectedClass && accessResult && (
            <div className="px-6 pb-4">
              <AttendanceAccessControl
                accessResult={accessResult}
                classInfo={{
                  subject_code: selectedClass.subject_code,
                  subject_name: selectedClass.subject_name,
                  semester: selectedClass.semester,
                  section: selectedClass.section,
                  time_slot: selectedClass.time_slot
                }}
                date={selectedDate}
                onRetry={recheckAccess}
              />
            </div>
          )}

          <CardContent>
            {selectedClass.is_disabled ? (
              <div className="text-center py-12">
                <div className="bg-red-100 rounded-full p-4 w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                  <X className="h-10 w-10 text-red-500" />
                </div>
                <h3 className="text-lg font-semibold text-red-800 mb-2">Secondary Faculty - Access Restricted</h3>
                <div className="max-w-md mx-auto">
                  <p className="text-red-600 mb-4">
                    You are assigned as <strong>secondary faculty</strong> for this lab class.
                  </p>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p className="text-sm text-red-700">
                      <strong>Primary Faculty:</strong> {selectedClass.primary_faculty_name || 'Not specified'}
                    </p>
                    <p className="text-sm text-red-600 mt-1">
                      The primary faculty is responsible for marking attendance for this class.
                    </p>
                  </div>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <p className="text-sm text-orange-700 font-medium mb-1">
                      🔄 Automatic Access Grant
                    </p>
                    <p className="text-sm text-orange-600">
                      You will automatically gain access to mark attendance when the primary faculty is on approved leave.
                    </p>
                  </div>
                </div>
              </div>
            ) : loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Loading students...</p>
                </div>
              </div>
            ) : attendanceSession?.error ? (
              <div className="text-center py-12">
                <div className="bg-orange-100 rounded-full p-4 w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                  <FlaskConical className="h-10 w-10 text-orange-600" />
                </div>
                <h3 className="text-lg font-semibold text-orange-800 mb-2">Lab Batch Configuration Required</h3>
                <div className="max-w-lg mx-auto">
                  <p className="text-orange-700 mb-4">
                    {attendanceSession.error.message}
                  </p>

                  {attendanceSession.error.type === 'LAB_BATCH_NOT_CONFIGURED' && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                      <h4 className="text-sm font-medium text-orange-800 mb-2">What needs to be done:</h4>
                      <ul className="text-sm text-orange-700 space-y-1 text-left">
                        <li>• Lab batch "{attendanceSession.error.batchName}" students need to be uploaded</li>
                        <li>• Each lab batch typically has 15-20 students from the main class</li>
                        <li>• Contact the Class Teacher or HOD to set up batch assignments</li>
                        <li>• Once configured, you'll see only the specific batch students here</li>
                      </ul>
                    </div>
                  )}

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-sm text-blue-700 font-medium mb-1">
                      📋 Why this matters
                    </p>
                    <p className="text-sm text-blue-600">
                      Lab classes are divided into smaller batches for practical sessions.
                      Showing all 60+ class students would be incorrect for lab attendance.
                    </p>
                  </div>

                  <div className="mt-6">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedClass(null);
                        setAttendanceSession(null);
                      }}
                      className="mr-2"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Close
                    </Button>
                    <Button
                      onClick={() => handleClassSelect(selectedClass!)}
                      disabled={loading}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Retry
                    </Button>
                  </div>
                </div>
              </div>
            ) : attendanceSession ? (
              <div className="space-y-4">
                {/* Quick Actions */}
                <div className="flex items-center gap-2 p-4 bg-muted/50 rounded-lg">
                  <span className="text-sm font-medium">Quick Actions:</span>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={accessResult && !accessResult.canAccess}
                    onClick={() => {
                      if (!attendanceSession) return;
                      setAttendanceSession({
                        ...attendanceSession,
                        students: attendanceSession.students.map(student => ({
                          ...student,
                          status: 'present'
                        }))
                      });
                    }}
                  >
                    <Check className="h-4 w-4 mr-1" />
                    Mark All Present
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={accessResult && !accessResult.canAccess}
                    onClick={() => {
                      if (!attendanceSession) return;
                      setAttendanceSession({
                        ...attendanceSession,
                        students: attendanceSession.students.map(student => ({
                          ...student,
                          status: 'absent'
                        }))
                      });
                    }}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Mark All Absent
                  </Button>
                </div>

                {/* Students Table */}
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-16">#</TableHead>
                        <TableHead className="w-32">USN</TableHead>
                        <TableHead>Student Name</TableHead>
                        <TableHead className="w-32 text-center">Status</TableHead>
                        <TableHead className="w-24 text-center">Action</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {attendanceSession.students.map((student, index) => (
                        <TableRow key={student.id} className="hover:bg-muted/50">
                          <TableCell className="font-mono text-sm">
                            {index + 1}
                          </TableCell>
                          <TableCell className="font-mono text-sm font-bold">
                            {student.usn}
                          </TableCell>
                          <TableCell className="font-medium">
                            {student.student_name}
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={student.status === 'present' ? 'default' : 'destructive'}
                              className={
                                student.status === 'present'
                                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                  : 'bg-red-100 text-red-800 hover:bg-red-200'
                              }
                            >
                              {student.status === 'present' ? (
                                <Check className="h-3 w-3 mr-1" />
                              ) : (
                                <X className="h-3 w-3 mr-1" />
                              )}
                              {student.status === 'present' ? 'Present' : 'Absent'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              disabled={accessResult && !accessResult.canAccess}
                              onClick={() => toggleAttendance(student.id)}
                              className="h-8 w-8 p-0"
                            >
                              {student.status === 'present' ? (
                                <X className="h-4 w-4 text-red-600" />
                              ) : (
                                <Check className="h-4 w-4 text-green-600" />
                              )}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Save Button */}
                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-muted-foreground">
                    {attendanceSession.hasExistingAttendance
                      ? (
                        <div className="flex items-center gap-2">
                          <Edit className="h-4 w-4 text-orange-600" />
                          <span className="text-orange-700">Editing existing attendance record</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <CheckCircle2 className="h-4 w-4 text-blue-600" />
                          <span className="text-blue-700">Creating new attendance record</span>
                        </div>
                      )
                    }
                  </div>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        disabled={saving || (accessResult && !accessResult.canAccess)}
                        className="min-w-32"
                      >
                        {saving ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Attendance
                          </>
                        )}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Attendance</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to save attendance for {selectedClass.subject_code}?
                          <br />
                          <br />
                          <strong>Summary:</strong>
                          <br />
                          • Present: {presentCount} students
                          <br />
                          • Absent: {absentCount} students
                          <br />
                          • Total: {totalStudents} students
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleSaveAttendance}>
                          Save Attendance
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Select a class to mark attendance.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
});

// CRITICAL FIX: Add display name for debugging
ManualAttendance.displayName = 'ManualAttendance';

export default ManualAttendance;
