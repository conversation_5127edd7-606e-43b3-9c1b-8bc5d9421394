import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import {
  Brain,
  FileText,
  Target,
  Calendar,
  BarChart3,
  Upload,
  BookOpen,
  Users,
  Clock,
  GraduationCap,
  Zap,
  CheckCircle,
  AlertCircle,
  Trash2,
  Loader2
} from 'lucide-react';

import CourseMaterialUpload from './CourseMaterialUpload';
import ComprehensiveQuizCreator from './ComprehensiveQuizCreator';
import { ComprehensiveQuizService, CourseMaterial, QuizTemplate } from '@/services/ComprehensiveQuizService';

const ComprehensiveQuizDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('materials');
  const [materials, setMaterials] = useState<CourseMaterial[]>([]);
  const [templates, setTemplates] = useState<QuizTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [cleaning, setCleaning] = useState(false);
  const [stats, setStats] = useState({
    totalMaterials: 0,
    processedMaterials: 0,
    totalTemplates: 0,
    aiGeneratedTemplates: 0,
    totalModules: 0,
    syllabusCount: 0
  });

  const { user } = useAuth();
  const { toast } = useToast();

  // Load data on component mount
  useEffect(() => {
    if (user?.id) {
      loadData();
    }
  }, [user?.id]);

  const loadData = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);

      // Load materials and templates in parallel
      const [materialsData, templatesData] = await Promise.all([
        ComprehensiveQuizService.getFacultyCourseMaterials(user.id),
        ComprehensiveQuizService.getFacultyQuizTemplates(user.id)
      ]);

      setMaterials(materialsData);
      setTemplates(templatesData);

      // Calculate statistics
      const processedMaterials = materialsData.filter(m => m.processing_status === 'completed');
      const syllabusCount = materialsData.filter(m => m.content_type === 'syllabus').length;
      const totalModules = materialsData.reduce((acc, m) => {
        return acc + (m.ai_detected_modules?.length || 0);
      }, 0);
      const aiGeneratedTemplates = templatesData.filter(t => t.is_ai_generated).length;

      setStats({
        totalMaterials: materialsData.length,
        processedMaterials: processedMaterials.length,
        totalTemplates: templatesData.length,
        aiGeneratedTemplates,
        totalModules,
        syllabusCount
      });

    } catch (error) {
      console.error('Failed to load data:', error);
      toast({
        title: 'Loading Failed',
        description: 'Failed to load quiz management data.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMaterialUploaded = (material: CourseMaterial) => {
    setMaterials(prev => [material, ...prev]);
    setStats(prev => ({ ...prev, totalMaterials: prev.totalMaterials + 1 }));

    // Auto-refresh materials to get processing status updates
    setTimeout(() => {
      loadData();
    }, 3000);

    // Also refresh after a longer delay to catch completed processing
    setTimeout(() => {
      loadData();
    }, 10000);
  };

  const handleTemplateCreated = (template: QuizTemplate) => {
    setTemplates(prev => {
      const existing = prev.find(t => t.id === template.id);
      if (existing) {
        return prev.map(t => t.id === template.id ? template : t);
      }
      return [template, ...prev];
    });

    setStats(prev => ({
      ...prev,
      totalTemplates: prev.totalTemplates + (templates.find(t => t.id === template.id) ? 0 : 1),
      aiGeneratedTemplates: template.is_ai_generated ? prev.aiGeneratedTemplates + 1 : prev.aiGeneratedTemplates
    }));
  };

  const handleCleanupAll = async () => {
    if (!user?.id) return;

    const confirmed = window.confirm(
      'Are you sure you want to delete ALL course materials and quiz templates? This action cannot be undone.'
    );

    if (!confirmed) return;

    try {
      setCleaning(true);

      await ComprehensiveQuizService.cleanupFacultyMaterials(user.id);

      // Reset state
      setMaterials([]);
      setTemplates([]);
      setStats({
        totalMaterials: 0,
        processedMaterials: 0,
        totalTemplates: 0,
        aiGeneratedTemplates: 0,
        totalModules: 0,
        syllabusCount: 0
      });

      toast({
        title: 'Cleanup Successful',
        description: 'All course materials and quiz templates have been deleted.',
      });

    } catch (error) {
      console.error('Cleanup failed:', error);
      toast({
        title: 'Cleanup Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred.',
        variant: 'destructive',
      });
    } finally {
      setCleaning(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        <span className="ml-2">Loading AI Quiz Management System...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="h-6 w-6 text-purple-600" />
            AI-Powered Quiz Management
          </h1>
          <p className="text-muted-foreground">
            Comprehensive quiz creation with intelligent module detection and AI question generation
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCleanupAll}
            disabled={cleaning || (materials.length === 0 && templates.length === 0)}
            className="text-red-600 border-red-200 hover:bg-red-50"
          >
            {cleaning ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Cleaning...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Start Fresh
              </>
            )}
          </Button>
          <Badge variant="outline" className="text-purple-600">
            <Zap className="h-3 w-3 mr-1" />
            Powered by Gemini AI
          </Badge>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Upload className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Materials</p>
                <p className="text-2xl font-bold">{stats.totalMaterials}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">Processed</p>
                <p className="text-2xl font-bold">{stats.processedMaterials}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <GraduationCap className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Syllabi</p>
                <p className="text-2xl font-bold">{stats.syllabusCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Modules</p>
                <p className="text-2xl font-bold">{stats.totalModules}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-indigo-600" />
              <div>
                <p className="text-sm font-medium">Templates</p>
                <p className="text-2xl font-bold">{stats.totalTemplates}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">AI Generated</p>
                <p className="text-2xl font-bold">{stats.aiGeneratedTemplates}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="materials" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Course Materials
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Quiz Templates
          </TabsTrigger>
          <TabsTrigger value="schedule" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Schedule Quizzes
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Course Materials Tab */}
        <TabsContent value="materials" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Course Material Management
              </CardTitle>
              <CardDescription>
                Upload syllabus, textbooks, and reference materials for AI-powered analysis and module detection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CourseMaterialUpload
                onMaterialUploaded={handleMaterialUploaded}
                materials={materials}
              />
            </CardContent>
          </Card>

          {/* Materials List with Processing Status */}
          {materials.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Uploaded Materials</CardTitle>
                <CardDescription>
                  Track the status of your uploaded course materials and AI processing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {materials.map((material) => (
                    <div key={material.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {material.content_type === 'syllabus' && <GraduationCap className="h-4 w-4" />}
                        {material.content_type === 'textbook' && <BookOpen className="h-4 w-4" />}
                        {material.content_type === 'reference' && <FileText className="h-4 w-4" />}
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{material.file_name}</span>
                            <Badge variant="outline" className="text-xs">
                              {material.content_type}
                            </Badge>
                            {material.material_identifier && (
                              <Badge variant="outline" className="text-xs">
                                {material.material_identifier}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {material.subject_code} - {material.subject_name} | 
                            Semester {material.semester} Section {material.section}
                          </p>
                          {material.content_type === 'syllabus' && material.ai_detected_modules && (
                            <p className="text-xs text-blue-600 mt-1">
                              🤖 Detected {material.ai_detected_modules.length} modules
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {material.processing_status === 'pending' && <Clock className="h-4 w-4 text-yellow-500" />}
                        {material.processing_status === 'processing' && <Brain className="h-4 w-4 text-blue-500 animate-pulse" />}
                        {material.processing_status === 'completed' && <CheckCircle className="h-4 w-4 text-green-500" />}
                        {material.processing_status === 'failed' && <AlertCircle className="h-4 w-4 text-red-500" />}
                        <Badge 
                          variant={material.processing_status === 'completed' ? 'default' : 'outline'}
                          className={
                            material.processing_status === 'completed' ? 'text-green-600' :
                            material.processing_status === 'processing' ? 'text-blue-600' :
                            material.processing_status === 'failed' ? 'text-red-600' :
                            'text-yellow-600'
                          }
                        >
                          {material.processing_status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Quiz Templates Tab */}
        <TabsContent value="templates" className="space-y-4">
          <ComprehensiveQuizCreator
            materials={materials}
            onTemplateCreated={handleTemplateCreated}
            templates={templates}
          />
        </TabsContent>

        {/* Schedule Quizzes Tab */}
        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardContent className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-muted-foreground mb-2">Quiz Scheduling</p>
              <p className="text-sm text-muted-foreground">
                Quiz scheduling functionality will be implemented in the next phase.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardContent className="text-center py-8">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-muted-foreground mb-2">Quiz Analytics</p>
              <p className="text-sm text-muted-foreground">
                Analytics and reporting functionality will be implemented in the next phase.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ComprehensiveQuizDashboard;
