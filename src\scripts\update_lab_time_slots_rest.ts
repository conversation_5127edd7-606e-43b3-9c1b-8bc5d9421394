// <PERSON>ript to update lab_time_slots table to reference simplified_subject_faculty_mappings
// instead of subject_faculty_mappings using Supabase REST API

import { supabase } from "../integrations/supabase/client";

async function updateLabTimeSlots() {
  try {
    console.log("Starting lab_time_slots table update...");
    
    // Step 1: Check if simplified_mapping_id column exists, if not, create it
    console.log("Step 1: Checking if simplified_mapping_id column exists...");
    
    // We'll use a workaround to check if the column exists by trying to select it
    const { data: columnCheck, error: columnCheckError } = await supabase
      .from('lab_time_slots')
      .select('simplified_mapping_id')
      .limit(1);
    
    let columnExists = true;
    
    if (columnCheckError && columnCheckError.message.includes('column "simplified_mapping_id" does not exist')) {
      columnExists = false;
      console.log("Column simplified_mapping_id does not exist, creating it...");
      
      // We need to use RPC to execute raw SQL for adding the column
      // First, check if we have the pgcall function available
      const { data: pgcallCheck, error: pgcallError } = await supabase.rpc('pgcall', { 
        query: "SELECT 1" 
      });
      
      if (pgcallError) {
        console.error("Error checking pgcall function:", pgcallError);
        console.log("pgcall function not available, cannot add column directly");
        console.log("Please add the simplified_mapping_id column manually using SQL:");
        console.log("ALTER TABLE lab_time_slots ADD COLUMN simplified_mapping_id UUID;");
        throw new Error("Cannot add column without pgcall function");
      }
      
      // Add the column using pgcall
      const { data: addColumnData, error: addColumnError } = await supabase.rpc('pgcall', { 
        query: "ALTER TABLE lab_time_slots ADD COLUMN simplified_mapping_id UUID;" 
      });
      
      if (addColumnError) {
        console.error("Error adding simplified_mapping_id column:", addColumnError);
        throw addColumnError;
      }
      
      console.log("Column simplified_mapping_id added successfully");
    } else {
      console.log("Column simplified_mapping_id already exists");
    }
    
    // Step 2: Get all lab time slots
    console.log("Step 2: Getting all lab time slots...");
    const { data: labTimeSlots, error: getLabTimeSlotsError } = await supabase
      .from('lab_time_slots')
      .select('id, mapping_id');
    
    if (getLabTimeSlotsError) {
      console.error("Error getting lab time slots:", getLabTimeSlotsError);
      throw getLabTimeSlotsError;
    }
    
    console.log(`Found ${labTimeSlots?.length || 0} lab time slots`);
    
    if (!labTimeSlots || labTimeSlots.length === 0) {
      console.log("No lab time slots found, nothing to update");
      return;
    }
    
    // Step 3: Get all subject faculty mappings referenced by lab time slots
    const mappingIds = [...new Set(labTimeSlots.map(slot => slot.mapping_id))];
    console.log(`Found ${mappingIds.length} unique mapping IDs`);
    
    // Process in batches of 100 to avoid query size limitations
    const batchSize = 100;
    const subjectFacultyMappings = [];
    
    for (let i = 0; i < mappingIds.length; i += batchSize) {
      const batchIds = mappingIds.slice(i, i + batchSize);
      console.log(`Getting batch ${i / batchSize + 1} of subject faculty mappings (${batchIds.length} IDs)...`);
      
      const { data: batchMappings, error: batchError } = await supabase
        .from('subject_faculty_mappings')
        .select('id, academic_year, department, semester, section, subject_code, faculty_1_id')
        .in('id', batchIds);
      
      if (batchError) {
        console.error(`Error getting batch ${i / batchSize + 1} of subject faculty mappings:`, batchError);
        throw batchError;
      }
      
      subjectFacultyMappings.push(...batchMappings);
    }
    
    console.log(`Retrieved ${subjectFacultyMappings.length} subject faculty mappings`);
    
    // Step 4: Find matching simplified subject faculty mappings
    console.log("Step 4: Finding matching simplified subject faculty mappings...");
    
    const mappingIdToSimplifiedId = new Map();
    const missingMappings = [];
    
    for (const sfm of subjectFacultyMappings) {
      // Find matching simplified mapping
      const { data: matchingSimplified, error: matchingError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('id')
        .eq('academic_year', sfm.academic_year)
        .eq('department', sfm.department)
        .eq('semester', sfm.semester)
        .eq('section', sfm.section)
        .eq('subject_code', sfm.subject_code)
        .eq('faculty_1_id', sfm.faculty_1_id)
        .limit(1);
      
      if (matchingError) {
        console.error(`Error finding matching simplified mapping for ${sfm.id}:`, matchingError);
        continue;
      }
      
      if (matchingSimplified && matchingSimplified.length > 0) {
        mappingIdToSimplifiedId.set(sfm.id, matchingSimplified[0].id);
      } else {
        missingMappings.push(sfm.id);
      }
    }
    
    console.log(`Found ${mappingIdToSimplifiedId.size} matching simplified mappings`);
    console.log(`Missing ${missingMappings.length} simplified mappings`);
    
    // Step 5: Create missing simplified mappings
    if (missingMappings.length > 0) {
      console.log("Step 5: Creating missing simplified mappings...");
      
      for (const missingId of missingMappings) {
        // Get full details of the missing mapping
        const { data: fullMapping, error: fullMappingError } = await supabase
          .from('subject_faculty_mappings')
          .select('*')
          .eq('id', missingId)
          .single();
        
        if (fullMappingError) {
          console.error(`Error getting full details of mapping ${missingId}:`, fullMappingError);
          continue;
        }
        
        // Create the simplified mapping
        const { data: newMapping, error: createMappingError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .insert({
            academic_year: fullMapping.academic_year,
            department: fullMapping.department,
            semester: fullMapping.semester,
            section: fullMapping.section,
            subject_id: fullMapping.subject_id,
            subject_code: fullMapping.subject_code,
            subject_name: fullMapping.subject_name,
            subject_type: fullMapping.subject_type,
            faculty_1_id: fullMapping.faculty_1_id,
            faculty_2_id: fullMapping.faculty_2_id,
            hours_per_week: fullMapping.hours_per_week,
            classroom: fullMapping.classroom,
            slots_per_week: fullMapping.slots_per_week
          })
          .select()
          .single();
        
        if (createMappingError) {
          console.error(`Error creating simplified mapping for ${missingId}:`, createMappingError);
          continue;
        }
        
        mappingIdToSimplifiedId.set(missingId, newMapping.id);
        console.log(`Created simplified mapping ${newMapping.id} for original mapping ${missingId}`);
      }
    }
    
    // Step 6: Update lab time slots with simplified mapping IDs
    console.log("Step 6: Updating lab time slots with simplified mapping IDs...");
    
    let updatedCount = 0;
    let errorCount = 0;
    
    for (const labTimeSlot of labTimeSlots) {
      const simplifiedId = mappingIdToSimplifiedId.get(labTimeSlot.mapping_id);
      
      if (simplifiedId) {
        const { error: updateError } = await supabase
          .from('lab_time_slots')
          .update({ simplified_mapping_id: simplifiedId })
          .eq('id', labTimeSlot.id);
        
        if (updateError) {
          console.error(`Error updating lab time slot ${labTimeSlot.id}:`, updateError);
          errorCount++;
        } else {
          updatedCount++;
        }
      } else {
        console.error(`Could not find simplified mapping for lab time slot ${labTimeSlot.id} with mapping_id ${labTimeSlot.mapping_id}`);
        errorCount++;
      }
    }
    
    console.log(`Updated ${updatedCount} lab time slots, encountered ${errorCount} errors`);
    
    if (errorCount > 0) {
      throw new Error(`Failed to update ${errorCount} lab time slots`);
    }
    
    console.log("Lab_time_slots table update completed successfully!");
  } catch (error) {
    console.error("Error updating lab_time_slots table:", error);
    throw error;
  }
}

// Execute the function
updateLabTimeSlots()
  .then(() => {
    console.log("Script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Script failed:", error);
    process.exit(1);
  });
