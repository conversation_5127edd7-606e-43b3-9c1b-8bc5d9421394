import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { 
  ClipboardCheck, 
  Users, 
  BookOpen, 
  Building2, 
  RefreshCw, 
  Save, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  TrendingUp,
  Award,
  Target
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FacultyAssignmentService } from '@/services/FacultyAssignmentService';
import { InternalAssessmentService } from '@/services/InternalAssessmentService';
import { SequentialIAService, SequentialIASession, SequentialIAStudent } from '@/services/SequentialIAService';

// Enhanced interfaces for sequential IA
interface FacultySubject {
  subject_code: string;
  subject_name: string;
  subject_type: 'theory' | 'laboratory' | 'elective';
  semester: string;
  section: string;
  display_name: string;
  key: string;
  batch_name?: string;
}

export default function SequentialIAManagement() {
  const { user } = useAuth();
  const { toast } = useToast();
  const { 
    department, 
    departmentName, 
    fullName, 
    loading: departmentLoading, 
    error: departmentError 
  } = useUserDepartment();

  // State management
  const [facultySubjects, setFacultySubjects] = useState<FacultySubject[]>([]);
  const [selectedSubjectKey, setSelectedSubjectKey] = useState<string>('');
  const [sequentialIASession, setSequentialIASession] = useState<SequentialIASession | null>(null);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load faculty subjects on component mount
  useEffect(() => {
    if (user?.id && department) {
      loadFacultySubjects();
    }
  }, [user?.id, department]);

  // Load faculty teaching assignments
  const loadFacultySubjects = useCallback(async () => {
    if (!user?.id || !department) return;

    try {
      setAssignmentsLoading(true);
      console.log('🔄 Loading faculty subjects for sequential IA...');

      const assignments = await FacultyAssignmentService.getFacultyAssignments(user.id, department);

      console.log('📊 Faculty assignments loaded:', {
        totalAssignments: assignments.assignments.length,
        subjects: assignments.subjects.length
      });

      // Transform assignments to faculty subjects
      const transformedSubjects: FacultySubject[] = assignments.assignments.map(assignment => {
        const displayName = `${assignment.subject_code} - ${assignment.subject_name} (Sem ${assignment.semester} - Sec ${assignment.section})`;
        const key = `${assignment.subject_code}-${assignment.semester}-${assignment.section}-${assignment.subject_type}`;

        return {
          subject_code: assignment.subject_code,
          subject_name: assignment.subject_name,
          subject_type: assignment.subject_type as 'theory' | 'laboratory' | 'elective',
          semester: assignment.semester,
          section: assignment.section,
          display_name: displayName,
          key: key
        };
      });

      // Sort subjects by semester, then section, then subject code
      transformedSubjects.sort((a, b) => {
        if (a.semester !== b.semester) return parseInt(a.semester) - parseInt(b.semester);
        if (a.section !== b.section) return a.section.localeCompare(b.section);
        return a.subject_code.localeCompare(b.subject_code);
      });

      setFacultySubjects(transformedSubjects);

      console.log('✅ Faculty subjects loaded for sequential IA:', transformedSubjects.length);

      if (transformedSubjects.length === 0) {
        toast({
          title: 'No Subjects Found',
          description: 'No teaching assignments found for your account. Please contact administrator.',
          variant: 'default',
        });
      }
    } catch (error) {
      console.error('Error loading faculty subjects:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your teaching assignments.',
        variant: 'destructive',
      });
    } finally {
      setAssignmentsLoading(false);
    }
  }, [user?.id, department, toast]);

  // Load sequential IA session
  const loadSequentialIASession = useCallback(async () => {
    if (!user?.id || !department || !selectedSubjectKey) {
      toast({
        title: 'Missing Information',
        description: 'Please select a subject.',
        variant: 'default',
      });
      return;
    }

    // Find the selected subject
    const selectedSubject = facultySubjects.find(s => s.key === selectedSubjectKey);
    if (!selectedSubject) {
      toast({
        title: 'Subject Not Found',
        description: 'Selected subject not found in your assignments.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);
      console.log('🔄 Loading sequential IA session for:', selectedSubject);

      // Get current academic year
      const currentYear = new Date().getFullYear();
      const academicYear = `${currentYear}-${currentYear + 1}`;

      // First, get students using existing logic
      const basicSession = await InternalAssessmentService.getStudentsForIAWithDynamicLoading(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear
      );

      if (basicSession.students.length === 0) {
        toast({
          title: 'No Students Found',
          description: `No students found for ${selectedSubject.subject_type} subject "${selectedSubject.subject_code}" (Sem ${selectedSubject.semester} - Sec ${selectedSubject.section}).`,
          variant: 'destructive',
        });
        return;
      }

      // Create sequential IA session
      const sequentialSession = await SequentialIAService.getSequentialIASession(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear,
        basicSession.students,
        selectedSubject.subject_name,
        selectedSubject.subject_type
      );

      setSequentialIASession(sequentialSession);

      toast({
        title: 'Sequential IA Session Loaded',
        description: `Loaded ${sequentialSession.students.length} students. Current phase: ${sequentialSession.current_phase}`,
      });

    } catch (error) {
      console.error('❌ Error loading sequential IA session:', error);
      
      let errorMessage = 'Failed to load sequential IA session.';
      if (error instanceof Error) {
        if (error.message.includes('access')) {
          errorMessage = `You do not have access to teach this subject. Please contact administrator.`;
        } else if (error.message.includes('students')) {
          errorMessage = `Unable to load student list. Please verify that students have been uploaded.`;
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: 'Failed to Load Sequential IA Session',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, department, selectedSubjectKey, facultySubjects, toast]);

  // Handle marks change for current phase
  const handleMarksChange = (studentId: string, field: string, value: string) => {
    if (!sequentialIASession) return;

    const numValue = value === '' ? undefined : parseFloat(value);

    // Validate marks based on field and current phase
    if (numValue !== undefined) {
      let maxValue = 0;
      switch (field) {
        case 'current_phase_marks':
          maxValue = 25; // IA marks are out of 25
          break;
        case 'assignment_marks':
          maxValue = 10; // Assignment marks are out of 10
          break;
        case 'lab_internal_marks':
          maxValue = 20; // Lab internal marks are out of 20
          break;
        default:
          maxValue = 100;
      }

      if (numValue < 0 || numValue > maxValue) {
        toast({
          title: 'Invalid Marks',
          description: `Marks must be between 0 and ${maxValue}`,
          variant: 'destructive',
        });
        return;
      }
    }

    setSequentialIASession({
      ...sequentialIASession,
      students: sequentialIASession.students.map(student =>
        student.id === studentId
          ? { ...student, [field]: numValue }
          : student
      )
    });
  };

  // Save current phase marks
  const handleSaveCurrentPhase = useCallback(async () => {
    if (!sequentialIASession || !user?.id || !department) {
      toast({
        title: 'Error',
        description: 'Missing required information for saving marks.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);

      // Collect marks data for current phase
      const marksData: Array<{
        student_id: string;
        ia_phase: string;
        marks: number;
      }> = [];

      // Add current phase marks
      sequentialIASession.students.forEach(student => {
        if (student.current_phase_marks !== undefined && student.current_phase_marks !== null) {
          marksData.push({
            student_id: student.id,
            ia_phase: sequentialIASession.current_phase,
            marks: student.current_phase_marks
          });
        }

        // Add assignment marks (during IA1 phase)
        if (sequentialIASession.available_phases.includes('ASSIGNMENT') &&
            student.assignment_marks !== undefined && student.assignment_marks !== null) {
          marksData.push({
            student_id: student.id,
            ia_phase: 'ASSIGNMENT',
            marks: student.assignment_marks
          });
        }

        // Add lab internal marks (for lab subjects)
        if (sequentialIASession.available_phases.includes('LAB_INTERNAL') &&
            student.lab_internal_marks !== undefined && student.lab_internal_marks !== null) {
          marksData.push({
            student_id: student.id,
            ia_phase: 'LAB_INTERNAL',
            marks: student.lab_internal_marks
          });
        }
      });

      if (marksData.length === 0) {
        toast({
          title: 'No Changes',
          description: 'Please enter marks for at least one student.',
          variant: 'default',
        });
        return;
      }

      console.log('💾 Saving sequential IA marks:', {
        currentPhase: sequentialIASession.current_phase,
        marksCount: marksData.length,
        subjectCode: sequentialIASession.phase_status.subject_code
      });

      // Save marks using Sequential IA Service
      await SequentialIAService.saveCurrentPhaseMarks(
        user.id,
        department,
        sequentialIASession.phase_status.subject_code,
        sequentialIASession.phase_status.semester,
        sequentialIASession.phase_status.section,
        sequentialIASession.phase_status.academic_year,
        marksData
      );

      // Check and advance phase if complete
      const updatedPhaseStatus = await SequentialIAService.checkAndAdvancePhase(
        user.id,
        department,
        sequentialIASession.phase_status.subject_code,
        sequentialIASession.phase_status.semester,
        sequentialIASession.phase_status.section,
        sequentialIASession.phase_status.academic_year,
        sequentialIASession.students.length
      );

      // Show success message
      const phaseAdvanced = updatedPhaseStatus.current_phase !== sequentialIASession.current_phase;

      toast({
        title: 'Success',
        description: phaseAdvanced
          ? `${sequentialIASession.current_phase} marks saved successfully! Advanced to ${updatedPhaseStatus.current_phase} phase.`
          : `${sequentialIASession.current_phase} marks saved successfully for ${marksData.length} entries.`,
      });

      // Reload the session to show updated data
      await loadSequentialIASession();

    } catch (error) {
      console.error('❌ Error saving sequential IA marks:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save IA marks.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  }, [sequentialIASession, user?.id, department, toast, loadSequentialIASession]);

  // Show loading state while fetching department info
  if (departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading department information...</span>
        </div>
      </div>
    );
  }

  // Show error if department info couldn't be loaded
  if (departmentError || !department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {departmentError || 'Unable to load your department information. Please contact administrator.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Target className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold">Sequential Internal Assessment</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadFacultySubjects}
            disabled={assignmentsLoading}
          >
            {assignmentsLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh Subjects
          </Button>
        </div>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <strong>Faculty:</strong> {fullName} | <strong>Department:</strong> {departmentName}
            </div>
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <BookOpen className="h-4 w-4" />
                <span>{facultySubjects.length} Subjects</span>
              </div>
              {sequentialIASession && (
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{sequentialIASession.students.length} Students</span>
                </div>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Subject Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Select Subject for Sequential IA Entry
          </CardTitle>
          <CardDescription>
            Choose a subject to begin sequential IA entry workflow. Each IA phase must be completed before the next becomes available.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {assignmentsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 mx-auto mb-4 animate-spin text-blue-600" />
                <p className="text-muted-foreground">Loading your teaching assignments...</p>
              </div>
            </div>
          ) : facultySubjects.length === 0 ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No teaching assignments found. Please contact administrator to verify your subject assignments.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Your Teaching Subjects ({facultySubjects.length} subjects found)
                </label>
                <Select value={selectedSubjectKey} onValueChange={setSelectedSubjectKey}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a subject to begin sequential IA entry" />
                  </SelectTrigger>
                  <SelectContent>
                    {facultySubjects.map((subject) => (
                      <SelectItem key={subject.key} value={subject.key}>
                        <div className="flex items-center gap-2">
                          <Badge variant={
                            subject.subject_type === 'theory' ? 'default' :
                            subject.subject_type === 'laboratory' ? 'secondary' : 'outline'
                          }>
                            {subject.subject_type.toUpperCase()}
                          </Badge>
                          <span>{subject.display_name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedSubjectKey && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-blue-800">Selected Subject</h4>
                      <p className="text-sm text-blue-600">
                        {facultySubjects.find(s => s.key === selectedSubjectKey)?.display_name}
                      </p>
                    </div>
                    <Button
                      onClick={loadSequentialIASession}
                      disabled={loading}
                      className="min-w-[140px]"
                    >
                      {loading ? (
                        <div className="flex items-center gap-2">
                          <RefreshCw className="h-4 w-4 animate-spin" />
                          Loading...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Target className="h-4 w-4" />
                          Start Sequential IA
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sequential IA Session */}
      {sequentialIASession && (
        <>
          {/* Phase Indicator */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5" />
                    {sequentialIASession.subject_name}
                    <Badge variant={
                      sequentialIASession.subject_type === 'theory' ? 'default' :
                      sequentialIASession.subject_type === 'laboratory' ? 'secondary' : 'outline'
                    }>
                      {sequentialIASession.subject_type.toUpperCase()}
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    Semester {sequentialIASession.phase_status.semester} Section {sequentialIASession.phase_status.section} • Academic Year {sequentialIASession.phase_status.academic_year}
                  </CardDescription>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-primary">
                    Currently Entering: {sequentialIASession.current_phase} Marks
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Sequential IA Entry Workflow
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Phase Progress Indicators */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                {/* IA1 Phase */}
                <div className={`p-4 rounded-lg border-2 ${
                  sequentialIASession.phase_summary.ia1.completed
                    ? 'border-green-200 bg-green-50'
                    : sequentialIASession.current_phase === 'IA1'
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {sequentialIASession.phase_summary.ia1.completed ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : sequentialIASession.current_phase === 'IA1' ? (
                      <Clock className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                    <span className="font-medium">IA1 (25 marks)</span>
                  </div>
                  <div className="text-sm">
                    {sequentialIASession.phase_summary.ia1.students_completed}/{sequentialIASession.phase_summary.ia1.total_students} students completed
                  </div>
                  {sequentialIASession.phase_summary.ia1.completed && (
                    <Badge variant="outline" className="mt-2 text-green-600 border-green-600">
                      Completed
                    </Badge>
                  )}
                  {sequentialIASession.current_phase === 'IA1' && (
                    <Badge variant="outline" className="mt-2 text-blue-600 border-blue-600">
                      Active
                    </Badge>
                  )}
                </div>

                {/* IA2 Phase */}
                <div className={`p-4 rounded-lg border-2 ${
                  sequentialIASession.phase_summary.ia2.completed
                    ? 'border-green-200 bg-green-50'
                    : sequentialIASession.current_phase === 'IA2'
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {sequentialIASession.phase_summary.ia2.completed ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : sequentialIASession.current_phase === 'IA2' ? (
                      <Clock className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                    <span className="font-medium">IA2 (25 marks)</span>
                  </div>
                  <div className="text-sm">
                    {sequentialIASession.phase_summary.ia2.students_completed}/{sequentialIASession.phase_summary.ia2.total_students} students completed
                  </div>
                  {sequentialIASession.phase_summary.ia2.completed && (
                    <Badge variant="outline" className="mt-2 text-green-600 border-green-600">
                      Completed
                    </Badge>
                  )}
                  {sequentialIASession.current_phase === 'IA2' && (
                    <Badge variant="outline" className="mt-2 text-blue-600 border-blue-600">
                      Active
                    </Badge>
                  )}
                </div>

                {/* IA3 Phase */}
                <div className={`p-4 rounded-lg border-2 ${
                  sequentialIASession.phase_summary.ia3.completed
                    ? 'border-green-200 bg-green-50'
                    : sequentialIASession.current_phase === 'IA3'
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {sequentialIASession.phase_summary.ia3.completed ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : sequentialIASession.current_phase === 'IA3' ? (
                      <Clock className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                    <span className="font-medium">IA3 (25 marks)</span>
                  </div>
                  <div className="text-sm">
                    {sequentialIASession.phase_summary.ia3.students_completed}/{sequentialIASession.phase_summary.ia3.total_students} students completed
                  </div>
                  {sequentialIASession.phase_summary.ia3.completed && (
                    <Badge variant="outline" className="mt-2 text-green-600 border-green-600">
                      Completed
                    </Badge>
                  )}
                  {sequentialIASession.current_phase === 'IA3' && (
                    <Badge variant="outline" className="mt-2 text-blue-600 border-blue-600">
                      Active
                    </Badge>
                  )}
                </div>
              </div>

              {/* Current Phase Information */}
              {sequentialIASession.current_phase !== 'COMPLETED' && (
                <Alert className="border-blue-200 bg-blue-50">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800">
                    <strong>Current Phase:</strong> {sequentialIASession.current_phase} Entry •
                    <strong> Available Columns:</strong> {sequentialIASession.available_phases.join(', ')} •
                    <strong> Next Phase:</strong> {
                      sequentialIASession.current_phase === 'IA1' ? 'IA2 (unlocks after IA1 completion)' :
                      sequentialIASession.current_phase === 'IA2' ? 'IA3 (unlocks after IA2 completion)' :
                      'All phases completed'
                    }
                  </AlertDescription>
                </Alert>
              )}

              {sequentialIASession.current_phase === 'COMPLETED' && (
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    <strong>All IA phases completed!</strong> All internal assessment marks have been successfully entered for this subject.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Sequential IA Entry Table */}
          {sequentialIASession.current_phase !== 'COMPLETED' && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <ClipboardCheck className="h-5 w-5" />
                      {sequentialIASession.current_phase} Marks Entry
                    </CardTitle>
                    <CardDescription>
                      Enter marks for the current phase. All students must have marks entered to advance to the next phase.
                    </CardDescription>
                  </div>
                  <Button
                    onClick={handleSaveCurrentPhase}
                    disabled={saving || sequentialIASession.current_phase === 'COMPLETED'}
                    className="min-w-[120px]"
                  >
                    {saving ? (
                      <div className="flex items-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Saving...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Save className="h-4 w-4" />
                        Save {sequentialIASession.current_phase}
                      </div>
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium">S.No</th>
                        <th className="text-left p-3 font-medium">USN</th>
                        <th className="text-left p-3 font-medium">Student Name</th>

                        {/* Current Phase Column */}
                        <th className="text-center p-3 font-medium bg-blue-50">
                          {sequentialIASession.current_phase} (25)
                        </th>

                        {/* Assignment Column (available during IA1) */}
                        {sequentialIASession.available_phases.includes('ASSIGNMENT') && (
                          <th className="text-center p-3 font-medium bg-yellow-50">
                            Assignment (10)
                          </th>
                        )}

                        {/* Lab Internal Column (for lab subjects) */}
                        {sequentialIASession.available_phases.includes('LAB_INTERNAL') && (
                          <th className="text-center p-3 font-medium bg-purple-50">
                            Lab Internal (20)
                          </th>
                        )}

                        {/* Historical Columns (Read-only) */}
                        {sequentialIASession.phase_summary.ia1.completed && sequentialIASession.current_phase !== 'IA1' && (
                          <th className="text-center p-3 font-medium bg-gray-50 text-gray-600">
                            IA1 ✓
                          </th>
                        )}
                        {sequentialIASession.phase_summary.ia2.completed && sequentialIASession.current_phase !== 'IA2' && (
                          <th className="text-center p-3 font-medium bg-gray-50 text-gray-600">
                            IA2 ✓
                          </th>
                        )}
                        {sequentialIASession.phase_summary.ia3.completed && sequentialIASession.current_phase !== 'IA3' && (
                          <th className="text-center p-3 font-medium bg-gray-50 text-gray-600">
                            IA3 ✓
                          </th>
                        )}

                        <th className="text-center p-3 font-medium">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {sequentialIASession.students.map((student, index) => (
                        <tr key={student.id} className="border-b hover:bg-gray-50">
                          <td className="p-3">{index + 1}</td>
                          <td className="p-3 font-mono">{student.usn}</td>
                          <td className="p-3">{student.student_name}</td>

                          {/* Current Phase Marks Input */}
                          <td className="p-3 text-center bg-blue-50">
                            <Input
                              type="number"
                              min="0"
                              max="25"
                              step="0.5"
                              value={student.current_phase_marks || ''}
                              onChange={(e) => handleMarksChange(student.id, 'current_phase_marks', e.target.value)}
                              className="w-20 text-center"
                              placeholder="0"
                            />
                          </td>

                          {/* Assignment Marks Input (during IA1) */}
                          {sequentialIASession.available_phases.includes('ASSIGNMENT') && (
                            <td className="p-3 text-center bg-yellow-50">
                              <Input
                                type="number"
                                min="0"
                                max="10"
                                step="0.5"
                                value={student.assignment_marks || ''}
                                onChange={(e) => handleMarksChange(student.id, 'assignment_marks', e.target.value)}
                                className="w-20 text-center"
                                placeholder="0"
                              />
                            </td>
                          )}

                          {/* Lab Internal Marks Input (for lab subjects) */}
                          {sequentialIASession.available_phases.includes('LAB_INTERNAL') && (
                            <td className="p-3 text-center bg-purple-50">
                              <Input
                                type="number"
                                min="0"
                                max="20"
                                step="0.5"
                                value={student.lab_internal_marks || ''}
                                onChange={(e) => handleMarksChange(student.id, 'lab_internal_marks', e.target.value)}
                                className="w-20 text-center"
                                placeholder="0"
                              />
                            </td>
                          )}

                          {/* Historical Marks (Read-only) */}
                          {sequentialIASession.phase_summary.ia1.completed && sequentialIASession.current_phase !== 'IA1' && (
                            <td className="p-3 text-center bg-gray-50 text-gray-600">
                              {student.ia1_marks || '-'}
                            </td>
                          )}
                          {sequentialIASession.phase_summary.ia2.completed && sequentialIASession.current_phase !== 'IA2' && (
                            <td className="p-3 text-center bg-gray-50 text-gray-600">
                              {student.ia2_marks || '-'}
                            </td>
                          )}
                          {sequentialIASession.phase_summary.ia3.completed && sequentialIASession.current_phase !== 'IA3' && (
                            <td className="p-3 text-center bg-gray-50 text-gray-600">
                              {student.ia3_marks || '-'}
                            </td>
                          )}

                          {/* Status Indicator */}
                          <td className="p-3 text-center">
                            {student.current_phase_completed ? (
                              <Badge variant="outline" className="text-green-600 border-green-600">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Done
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-orange-600 border-orange-600">
                                <Clock className="h-3 w-3 mr-1" />
                                Pending
                              </Badge>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Entry Summary */}
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Total Students:</span>
                      <span className="ml-2">{sequentialIASession.students.length}</span>
                    </div>
                    <div>
                      <span className="font-medium">{sequentialIASession.current_phase} Completed:</span>
                      <span className="ml-2">
                        {sequentialIASession.students.filter(s => s.current_phase_completed).length}
                      </span>
                    </div>
                    {sequentialIASession.available_phases.includes('ASSIGNMENT') && (
                      <div>
                        <span className="font-medium">Assignment Completed:</span>
                        <span className="ml-2">
                          {sequentialIASession.students.filter(s => s.assignment_completed).length}
                        </span>
                      </div>
                    )}
                    {sequentialIASession.available_phases.includes('LAB_INTERNAL') && (
                      <div>
                        <span className="font-medium">Lab Internal Completed:</span>
                        <span className="ml-2">
                          {sequentialIASession.students.filter(s => s.lab_internal_completed).length}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}
