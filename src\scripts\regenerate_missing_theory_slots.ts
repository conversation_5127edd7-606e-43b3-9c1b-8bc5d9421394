// Script to regenerate missing theory slots after elective conflict resolution
import { supabase } from '../integrations/supabase/client';
import { TheorySlotGenerator } from '../services/TheorySlotGenerator';
import { TimetableService } from '../services/TimetableService';

async function regenerateMissingTheorySlots() {
  console.log('🔄 Starting regeneration of missing theory slots...');
  
  const params = {
    academicYear: '2024-2025',
    department: 'cse',
    semester: '6',
    section: 'A'
  };

  try {
    // 1. Get theory mappings that need more slots
    console.log('📋 Fetching theory mappings...');
    const { data: theoryMappingsData, error: mappingsError } = await supabase
      .from('simplified_subject_faculty_mappings')
      .select(`
        id,
        subject_code,
        subject_name,
        faculty_1_id,
        hours_per_week,
        subject_id
      `)
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .eq('semester', params.semester)
      .eq('section', params.section)
      .eq('subject_type', 'theory')
      .not('subject_code', 'like', 'DELETED_%');

    if (mappingsError) {
      throw mappingsError;
    }

    // 2. Get faculty names
    const facultyIds = [...new Set(theoryMappingsData.map(m => m.faculty_1_id))];
    const { data: facultyData } = await supabase
      .from('employee_details')
      .select('id, full_name')
      .in('id', facultyIds);

    const facultyMap = new Map(facultyData?.map(f => [f.id, f.full_name]) || []);

    // 3. Get subject short IDs
    const subjectIds = [...new Set(theoryMappingsData.map(m => m.subject_id))];
    const { data: subjectsData } = await supabase
      .from('subjects')
      .select('id, subject_short_id')
      .in('id', subjectIds);

    const subjectShortIdMap = new Map(subjectsData?.map(s => [s.id, s.subject_short_id]) || []);

    // 4. Transform mappings
    const theoryMappings = theoryMappingsData.map(mapping => ({
      id: mapping.id,
      subject_code: mapping.subject_code,
      subject_name: mapping.subject_name,
      subject_short_id: subjectShortIdMap.get(mapping.subject_id),
      faculty_id: mapping.faculty_1_id,
      faculty_name: facultyMap.get(mapping.faculty_1_id) || 'Unknown Faculty',
      weekly_hours: mapping.hours_per_week || 0
    }));

    console.log(`Found ${theoryMappings.length} theory subjects to process`);

    // 5. Check current slot counts
    const { data: currentSlots } = await supabase
      .from('timetable_slots')
      .select('subject_code, COUNT(*)')
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .eq('semester', params.semester)
      .eq('section', params.section)
      .eq('subject_type', 'theory')
      .group('subject_code');

    const currentSlotCounts = new Map(
      currentSlots?.map(s => [s.subject_code, s.count]) || []
    );

    // 6. Filter mappings that need more slots
    const mappingsNeedingSlots = theoryMappings.filter(mapping => {
      const currentCount = currentSlotCounts.get(mapping.subject_code) || 0;
      const needed = mapping.weekly_hours - currentCount;
      if (needed > 0) {
        console.log(`📚 ${mapping.subject_code}: needs ${needed} more slots (has ${currentCount}/${mapping.weekly_hours})`);
        return true;
      }
      return false;
    });

    if (mappingsNeedingSlots.length === 0) {
      console.log('✅ All theory subjects have sufficient slots');
      return;
    }

    console.log(`🎯 Regenerating slots for ${mappingsNeedingSlots.length} subjects`);

    // 7. Generate missing slots using the fixed TheorySlotGenerator
    const theoryGen = new TheorySlotGenerator();
    const newSlots = await theoryGen.generateTheorySlots(
      mappingsNeedingSlots,
      params
    );

    console.log(`✅ Successfully generated ${newSlots.length} new theory slots`);

    // 8. Show final allocation status
    console.log('\n📊 Final allocation status:');
    for (const mapping of theoryMappings) {
      const { data: finalSlots } = await supabase
        .from('timetable_slots')
        .select('COUNT(*)')
        .eq('academic_year', params.academicYear)
        .eq('department', params.department)
        .eq('semester', params.semester)
        .eq('section', params.section)
        .eq('subject_type', 'theory')
        .eq('subject_code', mapping.subject_code)
        .single();

      const finalCount = finalSlots?.count || 0;
      const status = finalCount >= mapping.weekly_hours ? '✅' : '⚠️';
      console.log(`${status} ${mapping.subject_code}: ${finalCount}/${mapping.weekly_hours} slots`);
    }

  } catch (error) {
    console.error('❌ Error regenerating theory slots:', error);
    throw error;
  }
}

// Export for use in other scripts
export { regenerateMissingTheorySlots };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  regenerateMissingTheorySlots()
    .then(() => {
      console.log('🎉 Theory slot regeneration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Theory slot regeneration failed:', error);
      process.exit(1);
    });
}
