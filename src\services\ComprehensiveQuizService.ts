/**
 * Comprehensive Quiz Management Service
 * Orchestrates the entire quiz creation, scheduling, and management workflow
 */

import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveGeminiService } from './ComprehensiveGeminiService';

export interface CourseUploadRequest {
  faculty_id: string;
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  content_type: 'syllabus' | 'textbook' | 'reference' | 'notes';
  file: File;
  material_identifier?: string; // e.g., "Textbook 1", "Reference Material A"
}

export interface CourseMaterial {
  id: string;
  faculty_id: string;
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  content_type: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size_bytes: number;
  material_identifier?: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  extracted_text?: string;
  ai_detected_modules?: any[];
  module_content_mapping?: Record<string, any>;
  chapter_mappings?: ChapterMapping[];  // Chapter-to-module mappings
  ai_analysis_metadata?: any;
  processing_error?: string;
  created_at: string;
  processed_date?: string;
}

export interface ChapterMapping {
  module_name: string;
  chapter_references: string[];  // e.g., ["Chapter 1 (1.1-1.4)", "Chapter 2 (2.1-2.5)"]
  textbook_sections: string[];   // e.g., ["1.1", "1.2", "1.3", "1.4", "2.1", "2.2", "2.3", "2.4", "2.5"]
  content_scope: string;         // Description of content boundaries
}

export interface ContentWeighting {
  textbook_based: number;      // 80-95%, default 80% (MANDATORY MINIMUM)
  syllabus_based: number;      // 5-15%, default 10% (REDUCED)
  cross_referenced: number;    // 2-8%, default 5% (REDUCED)
  ai_synthesis: number;        // 2-5%, default 5% (REDUCED)
}

export interface QuizTemplate {
  id: string;
  faculty_id: string;
  title: string;
  description?: string;
  instructions?: string;
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  target_module: string;
  duration_minutes: number;
  total_marks: number;
  passing_marks: number;
  difficulty_level: string;
  question_types: string[];
  source_material_ids: string[];
  content_weighting?: ContentWeighting;
  module_focus_percentage: number;
  ai_generation_metadata?: any;
  review_status: 'pending' | 'reviewed' | 'approved' | 'rejected';
  status: 'draft' | 'ai_generated' | 'reviewed' | 'published';
  is_ai_generated: boolean;
  created_at: string;
  updated_at: string;
}

export interface QuizQuestion {
  id: string;
  quiz_template_id: string;
  question_text: string;
  question_type: string;
  options: Record<string, string>;
  correct_answer: string;
  explanation?: string;
  marks: number;
  difficulty_level: string;
  topic_tags: string[];
  module_reference: string;
  source_content_type: string;
  ai_generation_prompt?: string;
  faculty_edited: boolean;
  edit_history: any[];
  is_ai_generated: boolean;
  ai_confidence_score: number;
  source_material_reference: string;
  question_order: number;
  review_status: string;
  created_at: string;
  updated_at: string;
}

export class ComprehensiveQuizService {

  /**
   * STEP 1: Upload and process course materials
   */
  static async uploadCourseMaterial(request: CourseUploadRequest): Promise<CourseMaterial> {
    try {
      console.log('📤 Uploading course material:', {
        contentType: request.content_type,
        fileName: request.file.name,
        fileSize: request.file.size,
        facultyId: request.faculty_id,
        department: request.department
      });

      // First, let's test if we can access the current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        throw new Error('User not authenticated');
      }
      console.log('👤 Current user:', user.id, user.email);

      // 1. Upload file to Supabase Storage
      const fileExt = request.file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${user.id}/${request.content_type}/${fileName}`;

      console.log('📁 Storage upload details:', {
        bucket: 'course-materials',
        filePath,
        fileType: request.file.type,
        fileSize: request.file.size
      });

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('course-materials')
        .upload(filePath, request.file);

      if (uploadError) {
        console.error('💥 Storage upload error:', uploadError);
        throw uploadError;
      }

      console.log('✅ File uploaded to storage:', uploadData);

      // 2. Create database record
      // Use the authenticated user's ID to comply with RLS policy
      const materialData = {
        faculty_id: user.id, // Use authenticated user ID instead of request.faculty_id
        subject_code: request.subject_code,
        subject_name: request.subject_name,
        department: request.department,
        semester: request.semester,
        section: request.section,
        academic_year: request.academic_year,
        content_type: request.content_type,
        file_name: request.file.name,
        file_path: uploadData.path,
        file_type: fileExt?.toLowerCase() || 'unknown',
        file_size_bytes: request.file.size,
        material_identifier: request.material_identifier || null,
        processing_status: 'pending' as const
        // Note: ai_detected_modules, module_content_mapping, ai_analysis_metadata
        // have default values in the database, so we don't need to specify them
      };

      console.log('💾 Database insert data:', materialData);

      const { data: material, error: dbError } = await supabase
        .from('course_materials')
        .insert(materialData)
        .select()
        .single();

      if (dbError) {
        console.error('💥 Database insert error:', dbError);
        throw dbError;
      }

      console.log('✅ Database record created:', material);

      // 3. Start AI processing in background
      this.processCourseMaterialAsync(material.id);

      console.log('✅ Course material uploaded successfully:', material.id);
      return material;

    } catch (error) {
      console.error('❌ Failed to upload course material:', error);

      // Provide more specific error messages
      if (error && typeof error === 'object' && 'message' in error) {
        const errorMessage = (error as any).message;
        if (errorMessage.includes('storage')) {
          throw new Error(`File upload failed: ${errorMessage}`);
        } else if (errorMessage.includes('duplicate') || errorMessage.includes('unique')) {
          throw new Error('A material with this information already exists');
        } else if (errorMessage.includes('permission') || errorMessage.includes('policy')) {
          throw new Error('Permission denied. Please check your access rights.');
        } else {
          throw new Error(`Upload failed: ${errorMessage}`);
        }
      }

      throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process course material with AI (async)
   */
  private static async processCourseMaterialAsync(materialId: string): Promise<void> {
    try {
      console.log('🤖 Starting AI processing for material:', materialId);

      // Update status to processing
      await supabase
        .from('course_materials')
        .update({ processing_status: 'processing' })
        .eq('id', materialId);

      // Get material details
      const { data: material, error } = await supabase
        .from('course_materials')
        .select('*')
        .eq('id', materialId)
        .single();

      if (error || !material) throw new Error('Material not found');

      // Download file from storage
      const { data: fileData, error: downloadError } = await supabase.storage
        .from('course-materials')
        .download(material.file_path);

      if (downloadError) throw downloadError;

      // Extract text content (simplified - you might want to use a proper PDF parser)
      const extractedText = await this.extractTextFromFile(fileData, material.file_type);

      let aiAnalysis = {};
      let detectedModules: any[] = [];
      let moduleMapping = {};
      let chapterMappings: any[] = [];

      // If this is a syllabus, detect modules and chapter mappings using AI
      if (material.content_type === 'syllabus') {
        console.log('📚 Detecting modules from syllabus...');
        const moduleDetection = await ComprehensiveGeminiService.detectModulesFromSyllabus(
          extractedText,
          material.subject_name
        );

        detectedModules = moduleDetection.detected_modules;
        moduleMapping = moduleDetection.detected_modules.reduce((acc, module) => {
          acc[`module_${module.module_number}`] = {
            title: module.module_title,
            description: module.module_description,
            topics: module.key_topics,
            difficulty: module.difficulty_level,
            content_preview: module.content_preview
          };
          return acc;
        }, {} as Record<string, any>);

        // Extract chapter mappings for textbook-focused question generation
        console.log('📖 Extracting chapter mappings from syllabus...');
        chapterMappings = ComprehensiveGeminiService.extractChapterMappings(extractedText);
        console.log('✅ Found chapter mappings:', chapterMappings.length);

        aiAnalysis = {
          total_modules: moduleDetection.total_modules,
          syllabus_overview: moduleDetection.syllabus_overview,
          subject_complexity: moduleDetection.subject_complexity,
          recommended_distribution: moduleDetection.recommended_question_distribution,
          ai_confidence: moduleDetection.ai_confidence_score,
          chapter_mappings_count: chapterMappings.length,
          processed_at: new Date().toISOString()
        };
      }

      // Update material with processed data
      await supabase
        .from('course_materials')
        .update({
          processing_status: 'completed',
          extracted_text: extractedText,
          ai_detected_modules: detectedModules,
          module_content_mapping: moduleMapping,
          chapter_mappings: chapterMappings,
          ai_analysis_metadata: aiAnalysis,
          processed_date: new Date().toISOString()
        })
        .eq('id', materialId);

      console.log('✅ AI processing completed for material:', materialId);

    } catch (error) {
      console.error('❌ AI processing failed:', error);
      
      // Update status to failed
      await supabase
        .from('course_materials')
        .update({
          processing_status: 'failed',
          processing_error: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('id', materialId);
    }
  }

  /**
   * Extract text from uploaded file
   */
  private static async extractTextFromFile(fileData: Blob, fileType: string): Promise<string> {
    try {
      if (fileType === 'txt') {
        return await fileData.text();
      }

      // For PDF files, we'll use a simple approach for now
      // In a production environment, you'd want to use a proper PDF parsing service
      if (fileType === 'pdf') {
        // For now, return a sample syllabus content to test the AI processing
        // In production, you would integrate with a PDF parsing service
        return `
MICROCONTROLLERS SYLLABUS
Course Code: BCS402
Credits: 4

Module 1: Introduction to Microcontrollers (10 hours)
- Overview of microcontrollers vs microprocessors
- Architecture of 8051 microcontroller
- Pin diagram and functional blocks
- Memory organization and addressing modes

Module 2: 8051 Programming (12 hours)
- Instruction set of 8051
- Assembly language programming
- Addressing modes and data transfer instructions
- Arithmetic and logical operations
- Jump and call instructions

Module 3: Interfacing and I/O Operations (10 hours)
- I/O port programming
- Interfacing LEDs, switches, and displays
- Timer and counter programming
- Interrupt handling and programming
- Serial communication programming

Module 4: Advanced Interfacing (8 hours)
- ADC and DAC interfacing
- Motor control and PWM
- Sensor interfacing
- LCD and keyboard interfacing
- Real-time clock interfacing

Module 5: Microcontroller Applications (8 hours)
- Embedded system design principles
- Case studies and applications
- Project development methodology
- Debugging and testing techniques
- Industry applications and trends
        `;
      }

      // For other file types, try to read as text
      return await fileData.text();

    } catch (error) {
      console.error('Error extracting text from file:', error);
      return `[Error extracting text from ${fileType} file: ${error instanceof Error ? error.message : 'Unknown error'}]`;
    }
  }

  /**
   * Get faculty's course materials
   */
  static async getFacultyCourseMaterials(facultyId: string): Promise<CourseMaterial[]> {
    const { data, error } = await supabase
      .from('course_materials')
      .select('*')
      .eq('faculty_id', facultyId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Get detected modules from syllabus materials
   */
  static async getDetectedModules(facultyId: string, subjectCode: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('course_materials')
      .select('ai_detected_modules, module_content_mapping')
      .eq('faculty_id', facultyId)
      .eq('subject_code', subjectCode)
      .eq('content_type', 'syllabus')
      .eq('processing_status', 'completed');

    if (error) throw error;

    // Combine all detected modules from all syllabus materials
    const allModules: any[] = [];
    data?.forEach(material => {
      if (material.ai_detected_modules) {
        allModules.push(...material.ai_detected_modules);
      }
    });

    return allModules;
  }

  /**
   * STEP 2: Create quiz template for specific module
   */
  static async createModuleQuizTemplate(
    facultyId: string,
    templateData: {
      title: string;
      description?: string;
      subject_code: string;
      subject_name: string;
      department: string;
      semester: string;
      section: string;
      academic_year: string;
      target_module: string;
      duration_minutes: number;
      total_marks: number;
      difficulty_level: string;
      source_material_ids: string[];
      content_weighting?: ContentWeighting;
    }
  ): Promise<QuizTemplate> {
    try {
      const quizTemplate = {
        faculty_id: facultyId,
        ...templateData,
        instructions: `Multiple choice quiz on ${templateData.target_module}. Choose the correct answer for each question.`,
        passing_marks: Math.floor(templateData.total_marks * 0.6), // 60% passing
        question_types: ['multiple_choice'],
        content_weighting: templateData.content_weighting || {
          textbook_based: 80,      // MANDATORY MINIMUM 80%
          syllabus_based: 10,      // REDUCED to 10%
          cross_referenced: 5,     // REDUCED to 5%
          ai_synthesis: 5          // REDUCED to 5%
        },
        module_focus_percentage: 100.0,
        review_status: 'pending' as const,
        status: 'draft' as const,
        is_ai_generated: false
      };

      const { data: template, error } = await supabase
        .from('quiz_templates')
        .insert(quizTemplate)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Quiz template created:', template.id);
      return template;

    } catch (error) {
      console.error('❌ Failed to create quiz template:', error);
      throw new Error(`Template creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * STEP 3: Generate AI questions for quiz template
   */
  static async generateQuestionsForTemplate(
    templateId: string,
    questionCount: number = 20
  ): Promise<QuizQuestion[]> {
    try {
      console.log('🎯 Generating questions for template:', templateId);

      // Get template and associated materials
      const { data: template, error: templateError } = await supabase
        .from('quiz_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (templateError || !template) throw new Error('Template not found');

      // Get course materials
      const { data: materials, error: materialsError } = await supabase
        .from('course_materials')
        .select('*')
        .in('id', template.source_material_ids)
        .eq('processing_status', 'completed');

      if (materialsError) throw materialsError;

      if (!materials || materials.length === 0) {
        throw new Error('No processed course materials found');
      }

      // Find syllabus material and extract module content
      const syllabusMaterial = materials.find(m => m.content_type === 'syllabus');
      if (!syllabusMaterial) {
        throw new Error('No syllabus material found');
      }

      // Get module content from mapping
      const moduleMapping = syllabusMaterial.module_content_mapping || {};
      const targetModuleKey = Object.keys(moduleMapping).find(key => 
        moduleMapping[key].title === template.target_module
      );

      if (!targetModuleKey) {
        throw new Error(`Module "${template.target_module}" not found in syllabus`);
      }

      const moduleContent = moduleMapping[targetModuleKey];
      
      // Get textbook content if available
      const textbookMaterials = materials.filter(m => m.content_type === 'textbook');
      const textbookContent = textbookMaterials.map(m => m.extracted_text).join('\n\n');

      // Extract chapter mappings from syllabus content
      let chapterMapping: any = undefined;
      if (syllabusMaterial.extracted_text) {
        const chapterMappings = ComprehensiveGeminiService.extractChapterMappings(syllabusMaterial.extracted_text);
        // Find the mapping for the current module
        chapterMapping = chapterMappings.find(mapping =>
          mapping.module_name.toLowerCase().includes(template.target_module.toLowerCase()) ||
          template.target_module.toLowerCase().includes(mapping.module_name.toLowerCase())
        );

        if (chapterMapping) {
          console.log('📖 Found chapter mapping for module:', chapterMapping);
        } else {
          console.log('⚠️ No chapter mapping found for module:', template.target_module);
        }
      }

      // Generate questions using Gemini AI with content weighting and chapter mapping
      const questionGeneration = await ComprehensiveGeminiService.generateModuleQuestions({
        module_content: `${moduleContent.description}\n\nKey Topics: ${moduleContent.topics.join(', ')}\n\n${moduleContent.content_preview}`,
        textbook_content: textbookContent || undefined,
        module_title: template.target_module,
        subject_name: template.subject_name,
        difficulty_level: template.difficulty_level as any,
        question_count: questionCount,
        content_weighting: template.content_weighting || {
          textbook_based: 80,      // MANDATORY MINIMUM 80%
          syllabus_based: 10,      // REDUCED to 10%
          cross_referenced: 5,     // REDUCED to 5%
          ai_synthesis: 5          // REDUCED to 5%
        },
        chapter_mapping: chapterMapping
      });

      // Save generated questions to database
      const questionsToInsert = questionGeneration.questions.map((q, index) => ({
        quiz_template_id: templateId,
        question_text: q.question_text,
        question_type: 'multiple_choice',
        options: {
          A: q.option_a,
          B: q.option_b,
          C: q.option_c,
          D: q.option_d
        },
        correct_answer: q.correct_answer,
        explanation: q.explanation,
        marks: 1,
        difficulty_level: q.difficulty_level,
        topic_tags: q.topic_tags,
        module_reference: template.target_module,
        source_content_type: q.source_type || 'syllabus_based',
        faculty_edited: false,
        edit_history: [],
        is_ai_generated: true,
        ai_confidence_score: q.ai_confidence,
        source_material_reference: q.source_reference,
        ai_generation_prompt: q.chapter_reference ? `Chapter: ${q.chapter_reference}, Sections: ${q.section_numbers?.join(', ') || 'N/A'}` : undefined,
        question_order: index + 1,
        review_status: 'pending'
      }));

      const { data: questions, error: questionsError } = await supabase
        .from('quiz_questions')
        .insert(questionsToInsert)
        .select();

      if (questionsError) throw questionsError;

      // Update template status and metadata
      await supabase
        .from('quiz_templates')
        .update({ 
          status: 'ai_generated',
          is_ai_generated: true,
          ai_generation_metadata: questionGeneration.generation_metadata
        })
        .eq('id', templateId);

      console.log('✅ Questions generated successfully:', questions?.length);
      return questions || [];

    } catch (error) {
      console.error('❌ Question generation failed:', error);
      throw new Error(`Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get quiz templates for faculty
   */
  static async getFacultyQuizTemplates(facultyId: string): Promise<QuizTemplate[]> {
    const { data, error } = await supabase
      .from('quiz_templates')
      .select('*')
      .eq('faculty_id', facultyId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Get questions for a quiz template
   */
  static async getTemplateQuestions(templateId: string): Promise<QuizQuestion[]> {
    const { data, error } = await supabase
      .from('quiz_questions')
      .select('*')
      .eq('quiz_template_id', templateId)
      .order('question_order');

    if (error) throw error;
    return data || [];
  }

  /**
   * Delete a quiz template and all its questions
   */
  static async deleteQuizTemplate(templateId: string): Promise<void> {
    try {
      console.log('🗑️ Deleting quiz template:', templateId);

      // Delete all questions for this template first (due to foreign key constraint)
      const { error: questionsError } = await supabase
        .from('quiz_questions')
        .delete()
        .eq('quiz_template_id', templateId);

      if (questionsError) {
        console.error('Error deleting questions:', questionsError);
        throw questionsError;
      }

      // Delete the template
      const { error: templateError } = await supabase
        .from('quiz_templates')
        .delete()
        .eq('id', templateId);

      if (templateError) {
        console.error('Error deleting template:', templateError);
        throw templateError;
      }

      console.log('✅ Quiz template deleted successfully');

    } catch (error) {
      console.error('❌ Failed to delete quiz template:', error);
      throw error;
    }
  }

  /**
   * Clean up all materials for a faculty (for fresh start)
   */
  static async cleanupFacultyMaterials(facultyId: string): Promise<void> {
    try {
      console.log('🧹 Cleaning up all materials for faculty:', facultyId);

      // Get all materials to clean up storage files
      const { data: materials, error: fetchError } = await supabase
        .from('course_materials')
        .select('file_path')
        .eq('faculty_id', facultyId);

      if (fetchError) {
        console.error('Error fetching materials for cleanup:', fetchError);
      }

      // Delete storage files
      if (materials && materials.length > 0) {
        const filePaths = materials.map(m => m.file_path);
        const { error: storageError } = await supabase.storage
          .from('course-materials')
          .remove(filePaths);

        if (storageError) {
          console.error('Error deleting storage files:', storageError);
        } else {
          console.log('✅ Storage files deleted:', filePaths.length);
        }
      }

      // Delete quiz questions first (foreign key constraint)
      // First get the quiz template IDs for this faculty
      const { data: templateIds, error: templateIdsError } = await supabase
        .from('quiz_templates')
        .select('id')
        .eq('faculty_id', facultyId);

      if (templateIdsError) {
        console.error('Error fetching quiz template IDs:', templateIdsError);
      }

      // Delete quiz questions if there are templates
      if (templateIds && templateIds.length > 0) {
        const templateIdArray = templateIds.map(t => t.id);
        const { error: questionsError } = await supabase
          .from('quiz_questions')
          .delete()
          .in('quiz_template_id', templateIdArray);

        if (questionsError) {
          console.error('Error deleting quiz questions:', questionsError);
        } else {
          console.log('✅ Quiz questions deleted for templates:', templateIdArray.length);
        }
      }

      // Delete quiz templates
      const { error: templatesError } = await supabase
        .from('quiz_templates')
        .delete()
        .eq('faculty_id', facultyId);

      if (templatesError) {
        console.error('Error deleting quiz templates:', templatesError);
      }

      // Delete course materials
      const { error: dbError } = await supabase
        .from('course_materials')
        .delete()
        .eq('faculty_id', facultyId);

      if (dbError) {
        console.error('Error deleting database records:', dbError);
        throw dbError;
      }

      console.log('✅ Faculty materials and quiz templates cleaned up successfully');

    } catch (error) {
      console.error('❌ Failed to cleanup faculty materials:', error);
      throw error;
    }
  }
}
