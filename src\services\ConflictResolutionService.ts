// ConflictResolutionService.ts
/**
 * This service implements an automatic conflict resolution system for lab-theory slot conflicts.
 *
 * Required database tables:
 *
 * 1. timetable_audit_log - To store the history of automatic theory slot rescheduling
 *    CREATE TABLE IF NOT EXISTS public.timetable_audit_log (
 *      id UUID PRIMARY KEY,
 *      action_type TEXT NOT NULL,
 *      slot_id UUID NOT NULL,
 *      subject_code TEXT NOT NULL,
 *      faculty_id UUID NOT NULL,
 *      original_day TEXT NOT NULL,
 *      original_time_slot TEXT NOT NULL,
 *      new_day TEXT NOT NULL,
 *      new_time_slot TEXT NOT NULL,
 *      reason TEXT,
 *      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
 *      academic_year TEXT NOT NULL,
 *      department TEXT NOT NULL,
 *      semester TEXT NOT NULL,
 *      section TEXT NOT NULL
 *    );
 *
 * 2. timetable_conflicts - To store conflicts that need resolution
 *    CREATE TABLE IF NOT EXISTS public.timetable_conflicts (
 *      id UUID PRIMARY KEY,
 *      faculty_id UUID NOT NULL,
 *      day TEXT NOT NULL,
 *      time_slot TEXT NOT NULL,
 *      conflict_type TEXT NOT NULL,
 *      academic_year TEXT NOT NULL,
 *      department TEXT NOT NULL,
 *      semester TEXT NOT NULL,
 *      section TEXT NOT NULL,
 *      other_semester TEXT NOT NULL,
 *      other_section TEXT NOT NULL,
 *      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
 *      is_resolved BOOLEAN DEFAULT FALSE,
 *      resolved_at TIMESTAMP WITH TIME ZONE
 *    );
 */
import { supabase } from "@/integrations/supabase/client";
import { TimetableService, TimetableSlot, TimeStructure } from "./TimetableService";
import { FacultyAvailabilityRecalculator } from "./FacultyAvailabilityService";
import { v4 as uuidv4 } from "uuid";
import { toast } from "@/components/ui/use-toast";
import { IntelligentConflictResolutionService } from "./IntelligentConflictResolutionService";
import { ConflictDetectionParams } from "@/types/ConflictResolution";

/**
 * Service to handle automatic conflict resolution between lab and theory slots
 */
export class ConflictResolutionService {
  /**
   * Intelligent conflict resolution for any type of scheduling conflict
   * Uses advanced algorithms to find optimal slot swapping solutions
   *
   * @param params Parameters for conflict detection and resolution
   * @returns Detailed resolution result with applied swaps and updates
   */
  static async resolveAllConflictsIntelligently(
    params: ConflictDetectionParams
  ): Promise<{
    success: boolean;
    resolvedConflicts: number;
    unresolvedConflicts: number;
    appliedSwaps: number;
    facultyUpdates: number;
    warnings: string[];
    executionTime: number;
  }> {
    try {
      console.log("Starting intelligent conflict resolution for:", params);

      const result = await IntelligentConflictResolutionService.resolveConflicts(params);

      return {
        success: result.success,
        resolvedConflicts: result.resolvedConflicts.length,
        unresolvedConflicts: result.unresolvedConflicts.length,
        appliedSwaps: result.appliedSwaps.length,
        facultyUpdates: result.facultyAvailabilityUpdates.length,
        warnings: result.warnings,
        executionTime: result.executionTime
      };
    } catch (error) {
      console.error("Error in intelligent conflict resolution:", error);
      return {
        success: false,
        resolvedConflicts: 0,
        unresolvedConflicts: 0,
        appliedSwaps: 0,
        facultyUpdates: 0,
        warnings: [`Error: ${error instanceof Error ? error.message : "Unknown error"}`],
        executionTime: 0
      };
    }
  }

  /**
   * Check if a new lab slot conflicts with any existing theory slots for the same faculty
   * If conflicts are found, attempt to automatically reschedule the theory slots
   * LAB SLOTS ARE FIXED AND IMMOVABLE - only theory slots will be moved
   * Also checks if the lab slot would create a situation where a theory class would be scheduled
   * immediately after a lab session for the same faculty across any semester/section
   *
   * @param labSlot The new lab slot being added (FIXED and IMMOVABLE)
   * @param timeStructure The time structure for the department
   * @returns Object containing success status and any unresolved conflicts
   */
  static async checkAndResolveLabTheoryConflicts(
    labSlot: TimetableSlot,
    timeStructure: TimeStructure
  ): Promise<{
    success: boolean;
    resolvedConflicts: number;
    unresolvedConflicts: Array<{
      theorySlot: TimetableSlot;
      reason: string;
    }>;
  }> {
    try {
      console.log(`Checking for conflicts with new lab slot: ${labSlot.subject_code} on ${labSlot.day} at ${labSlot.time_slot}`);

      // Get all faculty IDs involved in this lab slot
      const facultyIds = [labSlot.faculty_id];
      if (labSlot.faculty2_id) {
        facultyIds.push(labSlot.faculty2_id);
      }

      // Get all time slots that this lab will occupy
      const allTimeSlots = this.getAllTimeSlots(timeStructure);
      const occupiedPeriods = this.getPeriodsOccupiedByLab(labSlot, allTimeSlots, timeStructure);

      console.log(`Lab will occupy periods: ${occupiedPeriods.join(', ')}`);

      // Find all theory slots for these faculty members that conflict with the lab slot
      const conflictingTheorySlots: TimetableSlot[] = [];

      for (const period of occupiedPeriods) {
        const { data: conflicts } = await supabase
          .from("timetable_slots")
          .select("*")
          .in("faculty_id", facultyIds)
          .eq("day", labSlot.day)
          .eq("time_slot", period)
          .eq("subject_type", "theory");

        if (conflicts && conflicts.length > 0) {
          conflictingTheorySlots.push(...conflicts);
        }
      }

      // Check for theory slots that would be immediately after this lab
      // This implements the cross-semester/section constraint
      if (occupiedPeriods.length > 0) {
        const lastPeriod = occupiedPeriods[occupiedPeriods.length - 1];
        const lastPeriodIndex = allTimeSlots.indexOf(lastPeriod);

        // If there's a next period after the lab
        if (lastPeriodIndex >= 0 && lastPeriodIndex + 1 < allTimeSlots.length) {
          const nextPeriod = allTimeSlots[lastPeriodIndex + 1];

          // Check if any faculty member has a theory class in the next period
          // across ANY semester or section
          for (const facultyId of facultyIds) {
            const { data: theoryAfterLab } = await supabase
              .from("timetable_slots")
              .select("*")
              .eq("faculty_id", facultyId)
              .eq("day", labSlot.day)
              .eq("time_slot", nextPeriod)
              .eq("subject_type", "theory");

            if (theoryAfterLab && theoryAfterLab.length > 0) {
              console.log(`Found ${theoryAfterLab.length} theory slots immediately after lab for faculty ${facultyId}`);
              conflictingTheorySlots.push(...theoryAfterLab);
            }
          }
        }
      }

      if (conflictingTheorySlots.length === 0) {
        console.log("No conflicts found with theory slots");
        return {
          success: true,
          resolvedConflicts: 0,
          unresolvedConflicts: []
        };
      }

      console.log(`Found ${conflictingTheorySlots.length} conflicting theory slots`);

      // Track resolved and unresolved conflicts
      const resolvedConflicts: TimetableSlot[] = [];
      const unresolvedConflicts: Array<{
        theorySlot: TimetableSlot;
        reason: string;
      }> = [];

      // Process each conflicting theory slot
      for (const theorySlot of conflictingTheorySlots) {
        console.log(`Attempting to reschedule theory slot: ${theorySlot.subject_code} on ${theorySlot.day} at ${theorySlot.time_slot}`);

        // Calculate flexibility score for this theory slot
        const alternativeSlots = await this.findAlternativeTheorySlots(theorySlot, timeStructure);
        const flexibilityScore = alternativeSlots.length;

        console.log(`Theory slot has flexibility score: ${flexibilityScore} with ${alternativeSlots.length} alternative slots`);

        if (alternativeSlots.length === 0) {
          // No alternative slots available
          unresolvedConflicts.push({
            theorySlot,
            reason: "No alternative slots available for this theory subject"
          });
          continue;
        }

        // Try to reschedule the theory slot
        const rescheduled = await this.rescheduleTheorySlot(theorySlot, alternativeSlots, timeStructure);

        if (rescheduled.success) {
          resolvedConflicts.push(theorySlot);
          console.log(`Successfully rescheduled theory slot to ${rescheduled.newSlot?.time_slot} on ${rescheduled.newSlot?.day}`);
        } else {
          unresolvedConflicts.push({
            theorySlot,
            reason: rescheduled.reason || "Failed to reschedule theory slot"
          });
        }
      }

      // Log the results
      console.log(`Resolved ${resolvedConflicts.length} conflicts, ${unresolvedConflicts.length} conflicts remain unresolved`);

      // Create audit log entries for all rescheduled slots
      if (resolvedConflicts.length > 0) {
        await this.createReschedulingAuditLog(resolvedConflicts, labSlot);

        // Perform final comprehensive recalculation for all affected faculty
        try {
          const allAffectedFacultyIds = [...new Set([
            ...facultyIds,
            ...resolvedConflicts.map(slot => slot.faculty_id).filter(Boolean)
          ])];

          await FacultyAvailabilityRecalculator.recalculateForFaculty(
            allAffectedFacultyIds,
            timeStructure,
            labSlot.academic_year
          );

          console.log(`✅ Final recalculation completed for ${allAffectedFacultyIds.length} faculty members after conflict resolution`);
        } catch (error) {
          console.error("❌ Error in final faculty availability recalculation:", error);
        }
      }

      return {
        success: unresolvedConflicts.length === 0,
        resolvedConflicts: resolvedConflicts.length,
        unresolvedConflicts
      };
    } catch (error) {
      console.error("Error checking and resolving conflicts:", error);
      return {
        success: false,
        resolvedConflicts: 0,
        unresolvedConflicts: [{
          theorySlot: labSlot as TimetableSlot,
          reason: `Error during conflict resolution: ${error}`
        }]
      };
    }
  }

  /**
   * Find alternative time slots for a theory subject that needs to be rescheduled
   *
   * @param theorySlot The theory slot that needs to be rescheduled
   * @param timeStructure The time structure for the department
   * @returns Array of alternative time slots
   */
  private static async findAlternativeTheorySlots(
    theorySlot: TimetableSlot,
    timeStructure: TimeStructure
  ): Promise<Array<{ day: string; time_slot: string; score: number }>> {
    try {
      // Get faculty availability data
      const { data: faculty } = await supabase
        .from("employee_details")
        .select("vacant_by_day, vacant_count_by_day")
        .eq("id", theorySlot.faculty_id)
        .single();

      if (!faculty) {
        console.error(`Faculty ${theorySlot.faculty_id} not found`);
        return [];
      }

      const vacant_by_day = faculty.vacant_by_day || {};
      const vacant_count_by_day = faculty.vacant_count_by_day || {};

      // Get all existing slots for this faculty to avoid creating new conflicts
      const { data: existingSlots } = await supabase
        .from("timetable_slots")
        .select("day, time_slot, subject_type")
        .eq("faculty_id", theorySlot.faculty_id);

      // Get all lab slots for this faculty ACROSS ALL SEMESTERS AND SECTIONS
      // This is important to prevent scheduling theory classes immediately after lab sessions
      // regardless of which semester/section each is for
      const { data: allFacultyLabSlots } = await supabase
        .from("timetable_slots")
        .select("*")
        .eq("faculty_id", theorySlot.faculty_id)
        .eq("subject_type", "lab");

      // Also check for faculty2_id to include labs where this faculty is the secondary instructor
      const { data: secondaryFacultyLabSlots } = await supabase
        .from("timetable_slots")
        .select("*")
        .eq("faculty2_id", theorySlot.faculty_id)
        .eq("subject_type", "lab");

      // Combine all lab slots where this faculty is involved
      const allLabSlots = [
        ...(allFacultyLabSlots || []),
        ...(secondaryFacultyLabSlots || [])
      ];

      console.log(`Found ${allLabSlots.length} lab slots across all semesters/sections for faculty ${theorySlot.faculty_id}`);

      // Find slots that immediately follow lab sessions
      const slotsAfterLabs: Record<string, Set<string>> = {};
      const allTimeSlots = this.getAllTimeSlots(timeStructure);

      timeStructure.working_days.forEach(day => {
        slotsAfterLabs[day] = new Set<string>();
      });

      // Identify slots that immediately follow lab sessions across all semesters/sections
      allLabSlots.forEach(lab => {
        const occupiedPeriods = this.getPeriodsOccupiedByLab(lab as TimetableSlot, allTimeSlots, timeStructure);
        if (occupiedPeriods.length > 0) {
          const lastPeriod = occupiedPeriods[occupiedPeriods.length - 1];
          const lastPeriodIndex = allTimeSlots.indexOf(lastPeriod);

          // If there's a next period after the lab, mark it as a slot after lab
          if (lastPeriodIndex >= 0 && lastPeriodIndex + 1 < allTimeSlots.length) {
            const nextPeriod = allTimeSlots[lastPeriodIndex + 1];
            slotsAfterLabs[lab.day].add(nextPeriod);
            console.log(`Marked ${nextPeriod} on ${lab.day} as unavailable (follows lab ${lab.subject_code} in ${lab.semester}-${lab.section})`);
          }
        }
      });

      // Collect all possible alternative slots
      const alternativeSlots: Array<{ day: string; time_slot: string; score: number }> = [];

      // Check each day for available slots
      timeStructure.working_days.forEach(day => {
        // Skip if no vacant slots on this day
        if (!vacant_count_by_day[day] || !Array.isArray(vacant_count_by_day[day]) || vacant_count_by_day[day].length === 0) {
          return;
        }

        // Check each available slot on this day
        vacant_count_by_day[day].forEach(slot => {
          // Skip if this slot immediately follows a lab session (cross-semester/section constraint)
          if (slotsAfterLabs[day].has(slot)) {
            return;
          }

          // Skip if faculty already has a slot at this time (avoid creating new conflicts)
          if (existingSlots?.some(existing =>
            existing.day === day &&
            existing.time_slot === slot &&
            existing.subject_type !== 'theory'
          )) {
            return;
          }

          // Calculate a score for this slot (higher is better)
          let score = 10; // Base score

          // Prefer days with more available slots (more flexibility)
          score += (vacant_by_day[day] || 0);

          // Prefer different day than the original slot to distribute load
          if (day !== theorySlot.day) {
            score += 5;
          }

          // Add to alternatives
          alternativeSlots.push({
            day,
            time_slot: slot,
            score
          });
        });
      });

      // Sort by score (descending)
      return alternativeSlots.sort((a, b) => b.score - a.score);
    } catch (error) {
      console.error("Error finding alternative theory slots:", error);
      return [];
    }
  }

  /**
   * Reschedule a theory slot to one of the alternative slots
   *
   * @param theorySlot The theory slot to reschedule
   * @param alternativeSlots Array of alternative slots
   * @param timeStructure The time structure for the department
   * @returns Object indicating success and the new slot details
   */
  private static async rescheduleTheorySlot(
    theorySlot: TimetableSlot,
    alternativeSlots: Array<{ day: string; time_slot: string; score: number }>,
    timeStructure: TimeStructure
  ): Promise<{
    success: boolean;
    newSlot?: TimetableSlot;
    reason?: string;
  }> {
    try {
      // Try each alternative slot until one works
      for (const alternative of alternativeSlots) {
        console.log(`Trying to reschedule to ${alternative.day} at ${alternative.time_slot} (score: ${alternative.score})`);

        // Check if this alternative would create new conflicts
        const { data: conflicts } = await supabase
          .from("timetable_slots")
          .select("*")
          .eq("faculty_id", theorySlot.faculty_id)
          .eq("day", alternative.day)
          .eq("time_slot", alternative.time_slot);

        if (conflicts && conflicts.length > 0) {
          console.log(`Alternative slot has conflicts, trying next option`);
          continue;
        }

        // Update the theory slot with the new day and time
        const updatedSlot = {
          ...theorySlot,
          day: alternative.day,
          time_slot: alternative.time_slot
        };

        // Update the timetable_slots table
        const { error: updateError } = await supabase
          .from("timetable_slots")
          .update({
            day: alternative.day,
            time_slot: alternative.time_slot
          })
          .eq("id", theorySlot.id);

        if (updateError) {
          console.error("Error updating theory slot:", updateError);
          continue;
        }

        // Use our new comprehensive faculty availability recalculator
        try {
          await FacultyAvailabilityRecalculator.recalculateForFaculty(
            [theorySlot.faculty_id],
            timeStructure,
            theorySlot.academic_year
          );
          console.log(`✅ Recalculated availability after conflict resolution for faculty ${theorySlot.faculty_id}`);
        } catch (availabilityError) {
          console.error("❌ Error recalculating faculty availability:", availabilityError);
          // Try to rollback the slot update
          await supabase
            .from("timetable_slots")
            .update({
              day: theorySlot.day,
              time_slot: theorySlot.time_slot
            })
            .eq("id", theorySlot.id);
          continue;
        }

        // Successfully rescheduled
        return {
          success: true,
          newSlot: updatedSlot
        };
      }

      // If we get here, all alternatives failed
      return {
        success: false,
        reason: "All alternative slots failed validation or created new conflicts"
      };
    } catch (error) {
      console.error("Error rescheduling theory slot:", error);
      return {
        success: false,
        reason: `Error during rescheduling: ${error}`
      };
    }
  }

  /**
   * Create audit log entries for all rescheduled slots
   *
   * @param rescheduledSlots Array of rescheduled theory slots
   * @param labSlot The lab slot that caused the rescheduling
   */
  private static async createReschedulingAuditLog(
    rescheduledSlots: TimetableSlot[],
    labSlot: TimetableSlot
  ): Promise<void> {
    try {
      const auditEntries = rescheduledSlots.map(slot => ({
        id: uuidv4(),
        action_type: 'theory_slot_rescheduled',
        slot_id: slot.id,
        subject_code: slot.subject_code,
        faculty_id: slot.faculty_id,
        original_day: slot.day,
        original_time_slot: slot.time_slot,
        new_day: slot.day, // This will be updated in the database
        new_time_slot: slot.time_slot, // This will be updated in the database
        reason: `Conflict with lab slot ${labSlot.subject_code} on ${labSlot.day} at ${labSlot.time_slot}`,
        created_at: new Date().toISOString(),
        academic_year: slot.academic_year,
        department: slot.department,
        semester: slot.semester,
        section: slot.section
      }));

      try {
        const { error } = await supabase
          .from('timetable_audit_log')
          .insert(auditEntries);

        if (error) {
          console.error("Error creating audit log entries:", error);

          // If the table doesn't exist, log a warning
          if (error.code === '42P01') { // PostgreSQL code for undefined_table
            console.warn("timetable_audit_log table does not exist. Please create it using the SQL in the comments at the top of this file.");
          }
        } else {
          console.log(`Created ${auditEntries.length} audit log entries`);
        }
      } catch (dbError) {
        console.error("Database error creating audit log entries:", dbError);

        // Log the rescheduling actions to console as a fallback
        console.log("Rescheduling actions (not saved to database):");
        auditEntries.forEach(entry => {
          console.log(`- Rescheduled ${entry.subject_code} from ${entry.original_day} at ${entry.original_time_slot} to ${entry.new_day} at ${entry.new_time_slot}`);
        });
      }
    } catch (error) {
      console.error("Error creating audit log:", error);
    }
  }

  /**
   * Get all possible time slots based on time structure
   */
  private static getAllTimeSlots(ts: TimeStructure): string[] {
    const slots: string[] = [];
    const dur = ts.theory_class_duration;

    // 1. Before tea break
    let cur = ts.first_half_start_time;
    const teaStart = ts.tea_break_start_time;

    while (cur < teaStart) {
      const end = this.addMinutes(cur, dur);
      // Only add if this is a valid period (not extending into tea break)
      if (end <= teaStart) {
        slots.push(`${cur}-${end}`);
      }
      cur = end;
    }

    // 2. After tea break, before lunch
    cur = ts.tea_break_end_time;
    const lunchStart = ts.lunch_break_start_time;

    while (cur < lunchStart) {
      const end = this.addMinutes(cur, dur);
      // Only add if this is a valid period (not extending into lunch break)
      if (end <= lunchStart) {
        slots.push(`${cur}-${end}`);
      }
      cur = end;
    }

    // 3. After lunch
    cur = ts.lunch_break_end_time;
    const dayEnd = ts.second_half_end_time ||
      this.addMinutes(cur, ts.periods_in_second_half * dur);

    while (cur < dayEnd) {
      const end = this.addMinutes(cur, dur);
      slots.push(`${cur}-${end}`);
      cur = end;
    }

    return slots;
  }

  /**
   * Add minutes to "HH:MM" format
   */
  private static addMinutes(time: string, mins: number): string {
    const [h, m] = time.split(":").map(Number);
    const total = h * 60 + m + mins;
    const nh = Math.floor(total / 60);
    const nm = total % 60;
    return `${String(nh).padStart(2, "0")}:${String(nm).padStart(2, "0")}`;
  }

  /**
   * Determine which actual periods a lab occupies based on its start time and duration
   */
  private static getPeriodsOccupiedByLab(
    labSlot: TimetableSlot,
    allTimeSlots: string[],
    timeStructure: TimeStructure
  ): string[] {
    const occupiedPeriods: string[] = [];

    // Parse the lab time slot
    const [labStartTime] = labSlot.time_slot.split('-');

    // Determine if this is a morning or afternoon lab
    const isMorningLab = this.timeIsBefore(labStartTime, timeStructure.lunch_break_start_time);

    // For morning labs: typically 3 periods (8:30-9:25, 9:25-10:20, 10:35-11:30)
    // For afternoon labs: typically 3 periods (13:15-14:10, 14:10-15:05, 15:05-16:00)
    const periodsToOccupy = labSlot.col_span || 3; // Default to 3 periods if not specified

    // Find the starting index in allTimeSlots
    const startIndex = allTimeSlots.findIndex(slot => slot.startsWith(labStartTime));

    if (startIndex >= 0) {
      // Add the starting period
      occupiedPeriods.push(allTimeSlots[startIndex]);

      // Add subsequent periods, accounting for tea break
      let currentIndex = startIndex;
      let periodsAdded = 1;

      while (periodsAdded < periodsToOccupy && currentIndex + 1 < allTimeSlots.length) {
        currentIndex++;

        // Skip tea break if needed
        const [currentStart] = allTimeSlots[currentIndex].split('-');
        if (isMorningLab &&
          currentStart === timeStructure.tea_break_start_time) {
          // Skip tea break period
          continue;
        }

        occupiedPeriods.push(allTimeSlots[currentIndex]);
        periodsAdded++;
      }
    } else {
      console.warn(`Could not find starting period for lab ${labSlot.subject_code} at ${labSlot.time_slot}`);
    }

    return occupiedPeriods;
  }

  /**
   * Helper to compare time strings (HH:MM format)
   */
  private static timeIsBefore(time1: string, time2: string): boolean {
    const [h1, m1] = time1.split(':').map(Number);
    const [h2, m2] = time2.split(':').map(Number);

    return h1 < h2 || (h1 === h2 && m1 < m2);
  }
}
