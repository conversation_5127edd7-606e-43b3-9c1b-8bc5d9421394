import { supabase } from '@/integrations/supabase/client';

/**
 * Row Level Security (RLS) Policies for AI Quiz Management System
 * 
 * This script sets up comprehensive RLS policies that integrate with existing
 * authentication and role-based access control without modifying existing tables.
 */

async function executeQuery(query: string) {
  const { data, error } = await supabase.rpc('execute_sql', {
    sql_query: query
  });
  
  if (error) {
    console.error('❌ Query failed:', error);
    throw error;
  }
  
  return data;
}

async function setupAIQuizRLSPolicies() {
  try {
    console.log("🔒 Setting up RLS policies for AI Quiz Management System...");

    // Course Materials Policies
    console.log("📚 Setting up course_materials policies...");
    
    // Faculty can manage their own course materials
    await executeQuery(`
      CREATE POLICY "Faculty can manage own course materials" ON course_materials
      FOR ALL USING (
        faculty_id = auth.uid()
      );
    `);

    // Quiz Templates Policies
    console.log("📝 Setting up quiz_templates policies...");
    
    // Faculty can manage their own quiz templates
    await executeQuery(`
      CREATE POLICY "Faculty can manage own quiz templates" ON quiz_templates
      FOR ALL USING (
        faculty_id = auth.uid()
      );
    `);

    // Quiz Questions Policies
    console.log("❓ Setting up quiz_questions policies...");
    
    // Faculty can manage questions for their quiz templates
    await executeQuery(`
      CREATE POLICY "Faculty can manage own quiz questions" ON quiz_questions
      FOR ALL USING (
        quiz_template_id IN (
          SELECT id FROM quiz_templates WHERE faculty_id = auth.uid()
        )
      );
    `);

    // Quiz Schedules Policies
    console.log("📅 Setting up quiz_schedules policies...");
    
    // Faculty can manage their own quiz schedules
    await executeQuery(`
      CREATE POLICY "Faculty can manage own quiz schedules" ON quiz_schedules
      FOR ALL USING (
        faculty_id = auth.uid()
      );
    `);

    // Students can view scheduled quizzes for their class
    await executeQuery(`
      CREATE POLICY "Students can view their scheduled quizzes" ON quiz_schedules
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM class_students cs
          WHERE cs.usn = (
            SELECT usn FROM student_auth WHERE id = auth.uid()
          )
          AND cs.department = quiz_schedules.target_department
          AND cs.semester = quiz_schedules.target_semester
          AND (quiz_schedules.target_section IS NULL OR cs.section = quiz_schedules.target_section)
          AND cs.academic_year = quiz_schedules.academic_year
        )
      );
    `);

    // Student Quiz Attempts Policies
    console.log("👨‍🎓 Setting up student_quiz_attempts policies...");
    
    // Students can manage their own attempts
    await executeQuery(`
      CREATE POLICY "Students can manage own quiz attempts" ON student_quiz_attempts
      FOR ALL USING (
        student_usn = (
          SELECT usn FROM student_auth WHERE id = auth.uid()
        )
      );
    `);

    // Faculty can view attempts for their quizzes
    await executeQuery(`
      CREATE POLICY "Faculty can view attempts for own quizzes" ON student_quiz_attempts
      FOR SELECT USING (
        quiz_schedule_id IN (
          SELECT id FROM quiz_schedules WHERE faculty_id = auth.uid()
        )
      );
    `);

    // Student Quiz Answers Policies
    console.log("✍️ Setting up student_quiz_answers policies...");
    
    // Students can manage their own answers
    await executeQuery(`
      CREATE POLICY "Students can manage own quiz answers" ON student_quiz_answers
      FOR ALL USING (
        attempt_id IN (
          SELECT id FROM student_quiz_attempts 
          WHERE student_usn = (
            SELECT usn FROM student_auth WHERE id = auth.uid()
          )
        )
      );
    `);

    // Faculty can view answers for their quizzes
    await executeQuery(`
      CREATE POLICY "Faculty can view answers for own quizzes" ON student_quiz_answers
      FOR SELECT USING (
        attempt_id IN (
          SELECT sa.id FROM student_quiz_attempts sa
          JOIN quiz_schedules qs ON sa.quiz_schedule_id = qs.id
          WHERE qs.faculty_id = auth.uid()
        )
      );
    `);

    console.log("🔧 Setting up helper functions...");

    // Function to get faculty's teaching assignments
    await executeQuery(`
      CREATE OR REPLACE FUNCTION get_faculty_teaching_assignments(faculty_uuid UUID)
      RETURNS TABLE (
        subject_code TEXT,
        department TEXT,
        semester TEXT,
        section TEXT,
        academic_year TEXT
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT DISTINCT 
          sfm.subject_code,
          sfm.department,
          sfm.semester,
          sfm.section,
          sfm.academic_year
        FROM simplified_subject_faculty_mappings sfm
        WHERE sfm.faculty_1_id = faculty_uuid 
           OR sfm.faculty_2_id = faculty_uuid;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `);

    // Function to get students for a faculty's classes
    await executeQuery(`
      CREATE OR REPLACE FUNCTION get_faculty_students(faculty_uuid UUID)
      RETURNS TABLE (
        usn TEXT,
        student_name TEXT,
        department TEXT,
        semester TEXT,
        section TEXT,
        academic_year TEXT
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT DISTINCT 
          cs.usn,
          cs.student_name,
          cs.department,
          cs.semester,
          cs.section,
          cs.academic_year
        FROM class_students cs
        WHERE EXISTS (
          SELECT 1 FROM get_faculty_teaching_assignments(faculty_uuid) fta
          WHERE fta.department = cs.department
            AND fta.semester = cs.semester
            AND fta.section = cs.section
            AND fta.academic_year = cs.academic_year
        );
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `);

    // Function to check if student can access a quiz
    await executeQuery(`
      CREATE OR REPLACE FUNCTION can_student_access_quiz(
        student_usn_param TEXT,
        quiz_schedule_id_param UUID
      )
      RETURNS BOOLEAN AS $$
      DECLARE
        quiz_info RECORD;
        student_info RECORD;
      BEGIN
        -- Get quiz information
        SELECT target_department, target_semester, target_section, academic_year, start_time, end_time
        INTO quiz_info
        FROM quiz_schedules
        WHERE id = quiz_schedule_id_param;
        
        IF NOT FOUND THEN
          RETURN FALSE;
        END IF;
        
        -- Get student information
        SELECT department, semester, section, academic_year
        INTO student_info
        FROM class_students
        WHERE usn = student_usn_param;
        
        IF NOT FOUND THEN
          RETURN FALSE;
        END IF;
        
        -- Check if student matches quiz target criteria
        IF student_info.department = quiz_info.target_department
           AND student_info.semester = quiz_info.target_semester
           AND student_info.academic_year = quiz_info.academic_year
           AND (quiz_info.target_section IS NULL OR student_info.section = quiz_info.target_section)
           AND NOW() BETWEEN quiz_info.start_time AND quiz_info.end_time
        THEN
          RETURN TRUE;
        END IF;
        
        RETURN FALSE;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `);

    console.log("📊 Setting up analytics functions...");

    // Function to get quiz analytics for faculty
    await executeQuery(`
      CREATE OR REPLACE FUNCTION get_quiz_analytics(faculty_uuid UUID, quiz_schedule_id_param UUID DEFAULT NULL)
      RETURNS TABLE (
        quiz_title TEXT,
        total_students INTEGER,
        attempted_students INTEGER,
        average_score DECIMAL,
        highest_score DECIMAL,
        lowest_score DECIMAL,
        completion_rate DECIMAL
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          qs.title,
          COUNT(DISTINCT cs.usn)::INTEGER as total_students,
          COUNT(DISTINCT sqa.student_usn)::INTEGER as attempted_students,
          COALESCE(AVG(sqa.percentage), 0)::DECIMAL as average_score,
          COALESCE(MAX(sqa.percentage), 0)::DECIMAL as highest_score,
          COALESCE(MIN(sqa.percentage), 0)::DECIMAL as lowest_score,
          CASE 
            WHEN COUNT(DISTINCT cs.usn) > 0 
            THEN (COUNT(DISTINCT sqa.student_usn)::DECIMAL / COUNT(DISTINCT cs.usn)::DECIMAL * 100)
            ELSE 0 
          END as completion_rate
        FROM quiz_schedules qs
        LEFT JOIN class_students cs ON (
          cs.department = qs.target_department
          AND cs.semester = qs.target_semester
          AND cs.academic_year = qs.academic_year
          AND (qs.target_section IS NULL OR cs.section = qs.target_section)
        )
        LEFT JOIN student_quiz_attempts sqa ON (
          sqa.quiz_schedule_id = qs.id 
          AND sqa.student_usn = cs.usn
          AND sqa.status = 'submitted'
        )
        WHERE qs.faculty_id = faculty_uuid
          AND (quiz_schedule_id_param IS NULL OR qs.id = quiz_schedule_id_param)
        GROUP BY qs.id, qs.title;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `);

    console.log("✅ RLS policies and helper functions setup completed!");
    console.log("");
    console.log("🔒 Security features implemented:");
    console.log("  • Faculty can only access their own quiz data");
    console.log("  • Students can only access quizzes for their enrolled classes");
    console.log("  • Time-based access control for quiz availability");
    console.log("  • Department/semester/section based access control");
    console.log("");
    console.log("🔧 Helper functions created:");
    console.log("  • get_faculty_teaching_assignments()");
    console.log("  • get_faculty_students()");
    console.log("  • can_student_access_quiz()");
    console.log("  • get_quiz_analytics()");
    console.log("");
    console.log("🎉 AI Quiz System is now secure and ready for integration!");

  } catch (error) {
    console.error('❌ RLS setup failed:', error);
    throw error;
  }
}

// Export the setup function
export { setupAIQuizRLSPolicies };

// Auto-run the RLS setup
setupAIQuizRLSPolicies()
  .then(() => {
    console.log('✅ RLS setup completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ RLS setup failed:', error);
    process.exit(1);
  });
