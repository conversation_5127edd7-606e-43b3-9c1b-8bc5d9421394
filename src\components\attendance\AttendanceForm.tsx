
import React from 'react';
import { format } from "date-fns";
import { Calendar as CalendarIcon, Search, Check, X } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/hooks/use-toast";
import DataTable from "@/components/ui/DataTable";

interface Student {
  id: string;
  name: string;
  department: string;
  semester: string;
  section: string;
  enrollment_year: string;
  attendance?: {
    status: "present" | "absent" | null;
  };
}

interface AttendanceFormProps {
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  students: Student[];
  isLoadingStudents: boolean;
  isLoadingAttendance: boolean;
  handleSaveAttendance: () => void;
  isButtonDisabled: boolean;
  isDateDisabled: (date: Date) => boolean;
  saveAttendanceMutation: {
    isPending: boolean;
  };
  department: string;
  semester: string;
  section: string;
  selectedSubject: string;
}

const AttendanceForm: React.FC<AttendanceFormProps> = ({
  selectedDate,
  setSelectedDate,
  searchQuery,
  setSearchQuery,
  students,
  isLoadingStudents,
  isLoadingAttendance,
  handleSaveAttendance,
  isButtonDisabled,
  isDateDisabled,
  saveAttendanceMutation,
  department,
  semester,
  section,
  selectedSubject,
}) => {
  const queryClient = useQueryClient();

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const attendanceColumns = [
    {
      id: "name",
      header: "Student Name",
      accessorKey: "name",
    },
    {
      id: "status",
      header: "Attendance",
      accessorKey: "attendance.status",
      cell: (info: any) => {
        const student = info.row.original;
        const status = student.attendance?.status;
        
        return (
          <div className="flex justify-center">
            <Button
              variant={status === 'present' ? 'default' : 'outline'}
              size="sm"
              className={`mr-2 ${status === 'present' ? 'bg-green-500 hover:bg-green-600' : ''}`}
              onClick={() => {
                const updatedStudents = students.map(s => 
                  s.id === student.id ? { ...s, attendance: { status: 'present' } } : s
                );
                queryClient.setQueryData(['students', department, semester, section], updatedStudents);
              }}
            >
              <Check className="h-4 w-4" />
            </Button>
            <Button
              variant={status === 'absent' ? 'default' : 'outline'}
              size="sm"
              className={`${status === 'absent' ? 'bg-red-500 hover:bg-red-600' : ''}`}
              onClick={() => {
                const updatedStudents = students.map(s => 
                  s.id === student.id ? { ...s, attendance: { status: 'absent' } } : s
                );
                queryClient.setQueryData(['students', department, semester, section], updatedStudents);
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    }
  ];

  return (
    <div className="space-y-6">
      <div className="hidden md:grid grid-cols-1 gap-4">
        <div>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDate ? (
                  format(selectedDate, "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                disabled={isDateDisabled}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search students..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="block md:hidden">
        <div className="mb-4">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDate ? (
                  format(selectedDate, "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                disabled={isDateDisabled}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search students..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="mt-2 text-sm text-muted-foreground">
          {selectedSubject && (
            <div className="inline-flex items-center bg-secondary/20 text-secondary-foreground rounded-md px-2 py-1 mr-1 mb-1">
              Subject: {selectedSubject}
            </div>
          )}
          
          {department && (
            <div className="inline-flex items-center bg-secondary/20 text-secondary-foreground rounded-md px-2 py-1 mr-1 mb-1">
              Dept: {department.toUpperCase()}
            </div>
          )}
          
          {semester && (
            <div className="inline-flex items-center bg-secondary/20 text-secondary-foreground rounded-md px-2 py-1 mr-1 mb-1">
              Sem: {semester}
            </div>
          )}
          
          {section && (
            <div className="inline-flex items-center bg-secondary/20 text-secondary-foreground rounded-md px-2 py-1 mr-1 mb-1">
              Sec: {section}
            </div>
          )}
        </div>
      </div>

      {isLoadingStudents || isLoadingAttendance ? (
        <div className="flex justify-center items-center h-64">
          <p>Loading students...</p>
        </div>
      ) : students.length === 0 ? (
        <div className="flex flex-col justify-center items-center h-64 space-y-4">
          <p className="text-center">No students found. Please select a department, semester, and section.</p>
        </div>
      ) : (
        <>
          <DataTable 
            columns={attendanceColumns} 
            data={filteredStudents} 
          />
          
          <div className="block md:hidden">
            <div className="flex justify-between items-center text-sm mt-2">
              <div>
                <span className="text-green-500 font-medium">
                  {filteredStudents.filter(s => s.attendance?.status === 'present').length} Present
                </span>
              </div>
              <div>
                <span className="text-red-500 font-medium">
                  {filteredStudents.filter(s => s.attendance?.status === 'absent').length} Absent
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">
                  {filteredStudents.filter(s => s.attendance?.status === null).length} Not marked
                </span>
              </div>
            </div>
          </div>
        </>
      )}

      <div className="flex justify-end">
        <Button
          onClick={handleSaveAttendance}
          disabled={isButtonDisabled || saveAttendanceMutation.isPending}
          className="relative"
        >
          {saveAttendanceMutation.isPending ? "Saving..." : "Save Attendance"}
        </Button>
      </div>

      {isButtonDisabled && isDateDisabled(selectedDate) && (
        <div className="text-red-500 text-sm text-center">
          Note: You can only update attendance for the past 7 days.
        </div>
      )}
    </div>
  );
};

export default AttendanceForm;
