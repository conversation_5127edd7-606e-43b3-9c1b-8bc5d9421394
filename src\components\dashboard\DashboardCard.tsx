
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import IconWrapper from "@/components/icons/IconWrapper";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface DashboardCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description?: string;
  colorClass?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  interactive?: boolean;
  onClick?: () => void;
}

const DashboardCard = ({
  title,
  value,
  icon,
  description,
  colorClass = "text-primary bg-primary/10",
  trend,
  interactive = false,
  onClick,
}: DashboardCardProps) => {
  return (
    <Card
      className={cn(
        "transition-all duration-300 hover:shadow-medium",
        interactive && "cursor-pointer hover:scale-[1.02] hover:border-primary/20",
        onClick && "cursor-pointer"
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between pb-3">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        <div className={cn(
          "p-2.5 rounded-xl transition-all duration-200 hover:scale-110",
          colorClass
        )}>
          <IconWrapper icon={icon} className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex items-baseline justify-between">
          <div className="text-3xl font-bold tracking-tight">{value}</div>
          {trend && (
            <div className={cn(
              "flex items-center text-xs font-medium px-2 py-1 rounded-full",
              trend.isPositive
                ? "text-success bg-success/10"
                : "text-destructive bg-destructive/10"
            )}>
              <span className="mr-1">
                {trend.isPositive ? "↗" : "↘"}
              </span>
              {Math.abs(trend.value)}%
            </div>
          )}
        </div>
        {description && (
          <p className="text-xs text-muted-foreground leading-relaxed">{description}</p>
        )}
      </CardContent>
    </Card>
  );
};

export default DashboardCard;
