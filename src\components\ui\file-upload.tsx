import React from 'react';
import { cn } from '@/lib/utils';

interface FileUploadProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const FileUpload = React.forwardRef<HTMLDivElement, FileUploadProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "border-dashed border-2 border-border rounded-xl p-6 bg-card shadow-sm hover:bg-muted/50 transition-all cursor-pointer",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

FileUpload.displayName = 'FileUpload';

export { FileUpload };
