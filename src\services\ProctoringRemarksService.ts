import { supabase } from '@/integrations/supabase/client';

export interface ProctoringRemark {
  id: string;
  student_id: string;
  faculty_id: string;
  session_number: 1 | 2 | 3;
  remarks_text: string;
  academic_year: string;
  created_at: string;
  updated_at: string;
  faculty_name?: string; // Populated when fetching with faculty details
}

export interface ProctoringSession {
  session_number: 1 | 2 | 3;
  remarks?: ProctoringRemark;
  hasRemarks: boolean;
}

export interface StudentProctoringData {
  student_id: string;
  student_usn: string;
  student_name: string;
  academic_year: string;
  sessions: ProctoringSession[];
}

export class ProctoringRemarksService {
  /**
   * Get current academic year in YYYY-YYYY format
   */
  private static getCurrentAcademicYear(): string {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

    // Academic year typically starts in June/July
    if (currentMonth >= 6) {
      return `${currentYear}-${currentYear + 1}`;
    } else {
      return `${currentYear - 1}-${currentYear}`;
    }
  }

  /**
   * Get all proctoring remarks for a student in the current academic year
   */
  static async getStudentProctoringRemarks(
    studentId: string,
    academicYear?: string
  ): Promise<StudentProctoringData> {
    try {
      const year = academicYear || this.getCurrentAcademicYear();

      console.log('📋 Fetching proctoring remarks for student:', { studentId, academicYear: year });

      // Get student basic info
      const { data: studentData, error: studentError } = await supabase
        .from('class_students')
        .select('id, usn, student_name')
        .eq('id', studentId)
        .single();

      if (studentError || !studentData) {
        throw new Error(`Student not found: ${studentError?.message}`);
      }

      // Get all remarks for this student in the academic year
      const { data: remarksData, error: remarksError } = await supabase
        .from('proctoring_remarks')
        .select(`
          id,
          student_id,
          faculty_id,
          session_number,
          remarks_text,
          academic_year,
          created_at,
          updated_at
        `)
        .eq('student_id', studentId)
        .eq('academic_year', year)
        .order('session_number', { ascending: true });

      if (remarksError) {
        console.error('❌ Error fetching proctoring remarks:', remarksError);
        throw new Error(`Failed to fetch proctoring remarks: ${remarksError.message}`);
      }

      // Get faculty names for the remarks
      const facultyIds = [...new Set(remarksData?.map(r => r.faculty_id) || [])];
      let facultyMap = new Map<string, string>();

      if (facultyIds.length > 0) {
        const { data: facultyData, error: facultyError } = await supabase
          .from('employee_details')
          .select('id, full_name')
          .in('id', facultyIds);

        if (!facultyError && facultyData) {
          facultyMap = new Map(facultyData.map(f => [f.id, f.full_name]));
          console.log('✅ Loaded faculty names:', Object.fromEntries(facultyMap));
        } else {
          console.error('❌ Error loading faculty names:', facultyError);
        }
      }

      // Create sessions structure (1, 2, 3)
      const sessions: ProctoringSession[] = [1, 2, 3].map(sessionNum => {
        const remark = remarksData?.find(r => r.session_number === sessionNum);
        return {
          session_number: sessionNum as 1 | 2 | 3,
          remarks: remark ? {
            ...remark,
            faculty_name: facultyMap.get(remark.faculty_id) || 'Unknown Faculty'
          } : undefined,
          hasRemarks: !!remark
        };
      });

      console.log(`✅ Found ${remarksData?.length || 0} proctoring remarks for student ${studentData.usn}`);

      return {
        student_id: studentData.id,
        student_usn: studentData.usn,
        student_name: studentData.student_name,
        academic_year: year,
        sessions
      };

    } catch (error) {
      console.error('❌ Error in getStudentProctoringRemarks:', error);
      throw error;
    }
  }

  /**
   * Save or update proctoring remarks for a specific session
   */
  static async saveProctoringRemarks(
    studentId: string,
    facultyId: string,
    sessionNumber: 1 | 2 | 3,
    remarksText: string,
    academicYear?: string
  ): Promise<ProctoringRemark> {
    try {
      const year = academicYear || this.getCurrentAcademicYear();

      console.log('💾 Saving proctoring remarks:', {
        studentId,
        facultyId,
        sessionNumber,
        academicYear: year,
        remarksLength: remarksText.length
      });

      if (!remarksText.trim()) {
        throw new Error('Remarks text cannot be empty');
      }

      // Upsert the remark (insert or update if exists)
      const { data, error } = await supabase
        .from('proctoring_remarks')
        .upsert({
          student_id: studentId,
          faculty_id: facultyId,
          session_number: sessionNumber,
          remarks_text: remarksText.trim(),
          academic_year: year,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'student_id,faculty_id,session_number,academic_year'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error saving proctoring remarks:', error);
        throw new Error(`Failed to save remarks: ${error.message}`);
      }

      console.log('✅ Proctoring remarks saved successfully');
      return data;

    } catch (error) {
      console.error('❌ Error in saveProctoringRemarks:', error);
      throw error;
    }
  }

  /**
   * Delete proctoring remarks for a specific session
   */
  static async deleteProctoringRemarks(
    studentId: string,
    facultyId: string,
    sessionNumber: 1 | 2 | 3,
    academicYear?: string
  ): Promise<void> {
    try {
      const year = academicYear || this.getCurrentAcademicYear();

      console.log('🗑️ Deleting proctoring remarks:', {
        studentId,
        facultyId,
        sessionNumber,
        academicYear: year
      });

      // First check if the remark exists and belongs to the faculty
      const { data: existingRemark, error: checkError } = await supabase
        .from('proctoring_remarks')
        .select('id, faculty_id')
        .eq('student_id', studentId)
        .eq('faculty_id', facultyId)
        .eq('session_number', sessionNumber)
        .eq('academic_year', year)
        .single();

      if (checkError || !existingRemark) {
        throw new Error('Remark not found or you do not have permission to delete it');
      }

      // Delete the remark
      const { error } = await supabase
        .from('proctoring_remarks')
        .delete()
        .eq('student_id', studentId)
        .eq('faculty_id', facultyId)
        .eq('session_number', sessionNumber)
        .eq('academic_year', year);

      if (error) {
        console.error('❌ Error deleting proctoring remarks:', error);
        throw new Error(`Failed to delete remarks: ${error.message}`);
      }

      console.log('✅ Proctoring remarks deleted successfully');

    } catch (error) {
      console.error('❌ Error in deleteProctoringRemarks:', error);
      throw error;
    }
  }

  /**
   * Get proctoring remarks summary for multiple students (for faculty dashboard)
   */
  static async getProctoringRemarksSummary(
    studentIds: string[],
    academicYear?: string
  ): Promise<Map<string, { totalRemarks: number; latestSession: number }>> {
    try {
      const year = academicYear || this.getCurrentAcademicYear();

      if (studentIds.length === 0) {
        return new Map();
      }

      const { data, error } = await supabase
        .from('proctoring_remarks')
        .select('student_id, session_number')
        .in('student_id', studentIds)
        .eq('academic_year', year);

      if (error) {
        console.error('❌ Error fetching proctoring remarks summary:', error);
        return new Map();
      }

      const summary = new Map<string, { totalRemarks: number; latestSession: number }>();

      // Process the data to create summary
      studentIds.forEach(studentId => {
        const studentRemarks = data?.filter(r => r.student_id === studentId) || [];
        const sessions = studentRemarks.map(r => r.session_number);

        summary.set(studentId, {
          totalRemarks: studentRemarks.length,
          latestSession: sessions.length > 0 ? Math.max(...sessions) : 0
        });
      });

      return summary;

    } catch (error) {
      console.error('❌ Error in getProctoringRemarksSummary:', error);
      return new Map();
    }
  }

  /**
   * Check if faculty can edit a specific remark
   */
  static canEditRemark(remark: ProctoringRemark, currentFacultyId: string): boolean {
    return remark.faculty_id === currentFacultyId;
  }

  /**
   * Validate session number
   */
  static isValidSessionNumber(sessionNumber: number): sessionNumber is 1 | 2 | 3 {
    return [1, 2, 3].includes(sessionNumber);
  }
}
