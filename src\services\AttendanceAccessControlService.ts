import { supabase } from "@/integrations/supabase/client";

export interface AttendanceAccessResult {
  canAccess: boolean;
  reason?: string;
  blockingInfo?: {
    substitute_faculty_id: string;
    substitute_faculty_name: string;
    leave_request_id: string;
    original_faculty_name: string;
    affected_class: any;
  };
}

export interface SubstituteAssignment {
  leave_request_id: string;
  original_faculty_id: string;
  original_faculty_name: string;
  substitute_faculty_id: string;
  substitute_faculty_name: string;
  start_date: string;
  end_date: string;
  affected_class: any;
}

/**
 * Service to handle attendance access control for substitute faculty assignments
 */
export class AttendanceAccessControlService {
  
  /**
   * Check if a faculty member can mark attendance for a specific class on a specific date
   */
  static async checkAttendanceAccess(
    facultyId: string,
    classInfo: {
      subject_code: string;
      subject_type: string;
      semester: string;
      section: string;
      department: string;
      batch_name?: string;
      time_slot?: string;
    },
    date: string
  ): Promise<AttendanceAccessResult> {
    try {
      console.log('🔐 ACCESS CONTROL: Checking attendance access for faculty:', facultyId);
      console.log('🔐 ACCESS CONTROL: Class info:', classInfo);
      console.log('🔐 ACCESS CONTROL: Date:', date);

      // First check if this faculty is on approved leave for this date
      const isOnLeave = await this.checkFacultyLeaveStatus(facultyId, date);
      if (isOnLeave) {
        console.log('🔐 ACCESS CONTROL: Faculty is on approved leave for this date');
        return {
          canAccess: false,
          reason: 'faculty_on_leave'
        };
      }

      // Check if this class is covered by an active substitute assignment
      const substituteAssignment = await this.getActiveSubstituteAssignment(
        classInfo,
        date
      );

      if (substituteAssignment) {
        // If there's a substitute assignment, check if this faculty is involved
        if (substituteAssignment.original_faculty_id === facultyId) {
          // Original faculty is blocked from marking attendance
          return {
            canAccess: false,
            reason: 'substitute_assigned',
            blockingInfo: {
              substitute_faculty_id: substituteAssignment.substitute_faculty_id,
              substitute_faculty_name: substituteAssignment.substitute_faculty_name,
              leave_request_id: substituteAssignment.leave_request_id,
              original_faculty_name: substituteAssignment.original_faculty_name,
              affected_class: substituteAssignment.affected_class
            }
          };
        } else if (substituteAssignment.substitute_faculty_id === facultyId) {
          // Substitute faculty has access
          return {
            canAccess: true,
            reason: 'substitute_access'
          };
        }
      }

      // No substitute assignment or faculty not involved - normal access
      return {
        canAccess: true,
        reason: 'normal_access'
      };

    } catch (error) {
      console.error('🔐 ACCESS CONTROL: Error checking attendance access:', error);
      // In case of error, allow access to prevent blocking legitimate attendance
      return {
        canAccess: true,
        reason: 'error_fallback'
      };
    }
  }

  /**
   * Get active substitute assignment for a specific class and date
   */
  static async getActiveSubstituteAssignment(
    classInfo: {
      subject_code: string;
      subject_type: string;
      semester: string;
      section: string;
      department: string;
      batch_name?: string;
      time_slot?: string;
    },
    date: string
  ): Promise<SubstituteAssignment | null> {
    try {
      console.log('🔍 SUBSTITUTE CHECK: Looking for active substitute assignments');

      // Get approved leave requests that cover this date
      const { data: leaveRequests, error } = await supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          start_date,
          end_date,
          affected_classes,
          status,
          employee_details!faculty_id(full_name)
        `)
        .eq('status', 'approved')
        .lte('start_date', date)
        .gte('end_date', date);

      if (error) {
        console.error('🔍 SUBSTITUTE CHECK: Error fetching leave requests:', error);
        return null;
      }

      if (!leaveRequests || leaveRequests.length === 0) {
        console.log('🔍 SUBSTITUTE CHECK: No active leave requests found for date:', date);
        return null;
      }

      console.log(`🔍 SUBSTITUTE CHECK: Found ${leaveRequests.length} active leave requests`);

      // Check each leave request for matching affected classes
      for (const request of leaveRequests) {
        if (!request.affected_classes || !Array.isArray(request.affected_classes)) {
          continue;
        }

        for (const affectedClass of request.affected_classes) {
          // Get the actual class details using class_id
          const classDetails = await this.getClassDetailsFromId(affectedClass.class_id);

          if (classDetails) {
            // Check if this affected class matches the requested class
            if (this.doesClassMatch(classInfo, classDetails)) {
              // Check if there's a substitute assigned
              if (affectedClass.substitute_faculty_id) {
                console.log('🔍 SUBSTITUTE CHECK: Found matching substitute assignment');

                // Get substitute faculty details
                const { data: substituteFaculty, error: subError } = await supabase
                  .from('employee_details')
                  .select('id, full_name')
                  .eq('id', affectedClass.substitute_faculty_id)
                  .single();

                if (subError) {
                  console.error('🔍 SUBSTITUTE CHECK: Error fetching substitute faculty:', subError);
                  continue;
                }

                return {
                  leave_request_id: request.id,
                  original_faculty_id: request.faculty_id,
                  original_faculty_name: request.employee_details?.full_name || 'Unknown Faculty',
                  substitute_faculty_id: affectedClass.substitute_faculty_id,
                  substitute_faculty_name: substituteFaculty?.full_name || 'Unknown Substitute',
                  start_date: request.start_date,
                  end_date: request.end_date,
                  affected_class: { ...affectedClass, ...classDetails }
                };
              }
            }
          }
        }
      }

      console.log('🔍 SUBSTITUTE CHECK: No matching substitute assignment found');
      return null;

    } catch (error) {
      console.error('🔍 SUBSTITUTE CHECK: Error checking substitute assignments:', error);
      return null;
    }
  }

  /**
   * Get class details from timetable_slots using class_id
   */
  static async getClassDetailsFromId(classId: string): Promise<any | null> {
    try {
      // Extract the actual timetable slot ID from the class_id
      // class_id format: "01f7ea0d-ed53-4a4b-9e3c-5ceebfedb36d-2025-06-06"
      const timetableSlotId = classId.split('-').slice(0, 5).join('-'); // Get the UUID part

      console.log('🔍 CLASS DETAILS: Fetching class details for ID:', timetableSlotId);

      const { data: classDetails, error } = await supabase
        .from('timetable_slots')
        .select('*')
        .eq('id', timetableSlotId)
        .single();

      if (error) {
        console.error('🔍 CLASS DETAILS: Error fetching class details:', error);
        return null;
      }

      console.log('🔍 CLASS DETAILS: Found class details:', classDetails);
      return classDetails;

    } catch (error) {
      console.error('🔍 CLASS DETAILS: Error parsing class ID:', error);
      return null;
    }
  }

  /**
   * Check if a class matches an affected class from leave request
   */
  static doesClassMatch(
    classInfo: {
      subject_code: string;
      subject_type: string;
      semester: string;
      section: string;
      department: string;
      batch_name?: string;
      time_slot?: string;
    },
    timetableSlot: any
  ): boolean {
    try {
      // Basic matching criteria
      const subjectMatch = classInfo.subject_code === timetableSlot.subject_code;
      const semesterMatch = classInfo.semester === timetableSlot.semester;
      const sectionMatch = classInfo.section === timetableSlot.section;

      // Department matching (handle variations)
      const departmentVariants = [
        classInfo.department,
        classInfo.department.toLowerCase(),
        'cse',
        'CSE',
        'Computer Science and Engineering'
      ];
      const timetableDepartmentVariants = [
        timetableSlot.department,
        timetableSlot.department?.toLowerCase(),
        'cse',
        'CSE',
        'Computer Science and Engineering'
      ];

      const departmentMatch = departmentVariants.some(variant =>
        timetableDepartmentVariants.includes(variant)
      );

      // Time slot matching (if available)
      let timeSlotMatch = true;
      if (classInfo.time_slot && timetableSlot.time_slot) {
        timeSlotMatch = classInfo.time_slot === timetableSlot.time_slot;
      }

      // Batch matching for lab subjects
      let batchMatch = true;
      if (classInfo.subject_type === 'laboratory' || classInfo.subject_type === 'lab') {
        if (classInfo.batch_name && timetableSlot.batch_name) {
          batchMatch = classInfo.batch_name === timetableSlot.batch_name;
        }
      }

      const isMatch = subjectMatch && semesterMatch && sectionMatch &&
                     departmentMatch && timeSlotMatch && batchMatch;

      console.log('🔍 CLASS MATCH CHECK:', {
        classInfo: {
          subject_code: classInfo.subject_code,
          semester: classInfo.semester,
          section: classInfo.section,
          department: classInfo.department,
          time_slot: classInfo.time_slot,
          batch_name: classInfo.batch_name
        },
        timetableSlot: {
          subject_code: timetableSlot.subject_code,
          semester: timetableSlot.semester,
          section: timetableSlot.section,
          department: timetableSlot.department,
          time_slot: timetableSlot.time_slot,
          batch_name: timetableSlot.batch_name
        },
        matches: {
          subject: subjectMatch,
          semester: semesterMatch,
          section: sectionMatch,
          department: departmentMatch,
          timeSlot: timeSlotMatch,
          batch: batchMatch
        },
        finalResult: isMatch
      });

      return isMatch;

    } catch (error) {
      console.error('🔍 CLASS MATCH CHECK: Error checking class match:', error);
      return false;
    }
  }

  /**
   * Check if a faculty member is on approved leave for a specific date
   */
  static async checkFacultyLeaveStatus(
    facultyId: string,
    date: string
  ): Promise<boolean> {
    try {
      console.log('🔍 LEAVE CHECK: Checking if faculty is on leave:', facultyId, 'for date:', date);

      const { data: leaveRequests, error } = await supabase
        .from('leave_requests')
        .select('id, start_date, end_date, status')
        .eq('faculty_id', facultyId)
        .eq('status', 'approved')
        .lte('start_date', date)
        .gte('end_date', date);

      if (error) {
        console.error('🔍 LEAVE CHECK: Error checking leave status:', error);
        return false;
      }

      const isOnLeave = leaveRequests && leaveRequests.length > 0;
      console.log('🔍 LEAVE CHECK: Faculty leave status:', isOnLeave);

      return isOnLeave;

    } catch (error) {
      console.error('🔍 LEAVE CHECK: Error checking faculty leave status:', error);
      return false;
    }
  }

  /**
   * Get all substitute assignments for a faculty member on a specific date
   */
  static async getSubstituteAssignmentsForFaculty(
    facultyId: string,
    date: string
  ): Promise<SubstituteAssignment[]> {
    try {
      console.log('🔍 FACULTY SUBSTITUTES: Getting substitute assignments for faculty:', facultyId);

      const { data: leaveRequests, error } = await supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          start_date,
          end_date,
          affected_classes,
          status,
          employee_details!faculty_id(full_name)
        `)
        .eq('status', 'approved')
        .lte('start_date', date)
        .gte('end_date', date);

      if (error) throw error;

      const assignments: SubstituteAssignment[] = [];

      for (const request of leaveRequests || []) {
        if (!request.affected_classes || !Array.isArray(request.affected_classes)) {
          continue;
        }

        for (const affectedClass of request.affected_classes) {
          if (affectedClass.substitute_faculty_id === facultyId) {
            // Get the actual class details
            const classDetails = await this.getClassDetailsFromId(affectedClass.class_id);

            // Get substitute faculty details
            const { data: substituteFaculty } = await supabase
              .from('employee_details')
              .select('full_name')
              .eq('id', facultyId)
              .single();

            assignments.push({
              leave_request_id: request.id,
              original_faculty_id: request.faculty_id,
              original_faculty_name: request.employee_details?.full_name || 'Unknown Faculty',
              substitute_faculty_id: facultyId,
              substitute_faculty_name: substituteFaculty?.full_name || 'Unknown Substitute',
              start_date: request.start_date,
              end_date: request.end_date,
              affected_class: { ...affectedClass, ...classDetails }
            });
          }
        }
      }

      console.log(`🔍 FACULTY SUBSTITUTES: Found ${assignments.length} substitute assignments`);
      return assignments;

    } catch (error) {
      console.error('🔍 FACULTY SUBSTITUTES: Error getting substitute assignments:', error);
      return [];
    }
  }
}
