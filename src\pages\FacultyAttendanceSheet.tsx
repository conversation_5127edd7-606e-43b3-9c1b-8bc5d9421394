import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useAttendanceRealtime } from '@/hooks/useAttendanceRealtime';
import {
  FacultyAttendanceSheetService,
  type AttendanceSheetData,
  type FacultySubjectSheet
} from '@/services/FacultyAttendanceSheetService';
import { FileDown, Loader2, Users, Calendar, RefreshCw, Grid3X3 } from 'lucide-react';
import { format } from 'date-fns';

const FacultyAttendanceSheet: React.FC = () => {
  const [subjects, setSubjects] = useState<FacultySubjectSheet[]>([]);
  const [selectedSubject, setSelectedSubject] = useState<FacultySubjectSheet | null>(null);
  const [attendanceSheet, setAttendanceSheet] = useState<AttendanceSheetData | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingSubjects, setLoadingSubjects] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [filters, setFilters] = useState({
    date_from: '',
    date_to: ''
  });

  const { user } = useAuth();
  const { department } = useUserDepartment();
  const { toast } = useToast();

  // Load faculty subjects on component mount
  useEffect(() => {
    if (user?.id && department) {
      loadFacultySubjects();
    }
  }, [user?.id, department]);

  // Real-time attendance updates
  useAttendanceRealtime({
    facultyId: user?.id || '',
    subjectCode: selectedSubject?.subject_code || '',
    semester: selectedSubject?.semester || '',
    section: selectedSubject?.section || '',
    subjectType: selectedSubject?.subject_type || '',
    batchName: selectedSubject?.batch_name,
    onAttendanceUpdate: () => {
      if (selectedSubject) {
        generateAttendanceSheet();
        toast({
          title: 'Attendance Updated',
          description: 'Attendance sheet refreshed with latest data.',
        });
      }
    },
    enabled: autoRefresh && !!selectedSubject
  });

  // Auto-refresh functionality (fallback)
  useEffect(() => {
    if (autoRefresh && selectedSubject) {
      const interval = setInterval(() => {
        generateAttendanceSheet();
      }, 60000); // Refresh every 60 seconds as fallback

      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedSubject]);

  const loadFacultySubjects = async () => {
    if (!user?.id || !department) return;

    try {
      setLoadingSubjects(true);
      const facultySubjects = await FacultyAttendanceSheetService.getFacultySubjectsForSheets(user.id, department);
      setSubjects(facultySubjects);
      
      if (facultySubjects.length === 0) {
        toast({
          title: 'No Subjects Found',
          description: 'No subjects assigned to you were found in the timetable.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error loading faculty subjects:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your assigned subjects.',
        variant: 'destructive',
      });
    } finally {
      setLoadingSubjects(false);
    }
  };

  const generateAttendanceSheet = async () => {
    if (!user?.id || !department || !selectedSubject) return;

    try {
      setLoading(true);
      const sheetData = await FacultyAttendanceSheetService.generateAttendanceSheet(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        selectedSubject.subject_type,
        selectedSubject.batch_name,
        filters.date_from,
        filters.date_to
      );
      
      setAttendanceSheet(sheetData);
      
      toast({
        title: 'Attendance Sheet Generated',
        description: `Sheet generated for ${selectedSubject.subject_code} with ${sheetData.students.length} students.`,
      });
    } catch (error) {
      console.error('Error generating attendance sheet:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate attendance sheet.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const exportToCSV = () => {
    if (!attendanceSheet) return;

    const csvContent = FacultyAttendanceSheetService.exportToCSV(attendanceSheet);
    const filename = FacultyAttendanceSheetService.generateExportFilename(
      attendanceSheet,
      filters.date_from,
      filters.date_to,
      'csv'
    );

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: 'Export Successful',
      description: `Attendance sheet exported as ${filename}`,
    });
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd');
    } catch {
      return dateString;
    }
  };

  const getStatusBadgeVariant = (percentage: number) => {
    if (percentage >= 75) return 'default';
    if (percentage >= 50) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Faculty Attendance Sheet</h1>
          <p className="text-muted-foreground">
            Dynamic attendance tracking grid with real-time updates
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
          </Button>
        </div>
      </div>

      {/* Subject Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Grid3X3 className="h-5 w-5" />
            Subject Selection
          </CardTitle>
          <CardDescription>
            Select a subject to generate its attendance sheet
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Label htmlFor="subject">Subject</Label>
              <Select
                value={selectedSubject ? `${selectedSubject.subject_code}-${selectedSubject.semester}-${selectedSubject.section}-${selectedSubject.subject_type}-${selectedSubject.batch_name || 'no-batch'}` : ''}
                onValueChange={(value) => {
                  const subject = subjects.find(s => `${s.subject_code}-${s.semester}-${s.section}-${s.subject_type}-${s.batch_name || 'no-batch'}` === value);
                  setSelectedSubject(subject || null);
                  setAttendanceSheet(null);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem
                      key={`${subject.subject_code}-${subject.semester}-${subject.section}-${subject.subject_type}-${subject.batch_name || 'no-batch'}`}
                      value={`${subject.subject_code}-${subject.semester}-${subject.section}-${subject.subject_type}-${subject.batch_name || 'no-batch'}`}
                    >
                      {subject.subject_code} - {subject.subject_name}
                      {subject.subject_type === 'theory' ? ' (Theory)' : ' (Lab)'}
                      {subject.batch_name ? ` - Batch ${subject.batch_name}` : ''}
                      {' '}(Sem {subject.semester}, Sec {subject.section})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button
              variant="outline"
              onClick={loadFacultySubjects}
              disabled={loadingSubjects}
            >
              {loadingSubjects ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Refresh Subjects'
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Date Range Filter
          </CardTitle>
          <CardDescription>
            Filter attendance sheet by date range (optional)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="date_from">From Date</Label>
              <Input
                id="date_from"
                type="date"
                value={filters.date_from}
                onChange={(e) => setFilters(prev => ({ ...prev, date_from: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="date_to">To Date</Label>
              <Input
                id="date_to"
                type="date"
                value={filters.date_to}
                onChange={(e) => setFilters(prev => ({ ...prev, date_to: e.target.value }))}
              />
            </div>
            <div className="flex items-end gap-2">
              <Button
                onClick={generateAttendanceSheet}
                disabled={!selectedSubject || loading}
                className="flex-1"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Grid3X3 className="h-4 w-4 mr-2" />
                )}
                Generate Sheet
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setFilters({ date_from: '', date_to: '' });
                  setAttendanceSheet(null);
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Sheet Display */}
      {attendanceSheet && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Grid3X3 className="h-5 w-5" />
                  Attendance Sheet - {attendanceSheet.subject_code}
                </CardTitle>
                <CardDescription>
                  {attendanceSheet.subject_name}
                  {attendanceSheet.subject_type === 'theory' ? ' (Theory)' : ' (Lab)'}
                  {attendanceSheet.batch_name ? ` - Batch ${attendanceSheet.batch_name}` : ''}
                  {' '}(Sem {attendanceSheet.semester}, Sec {attendanceSheet.section})
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateAttendanceSheet}
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2" />
                  )}
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportToCSV}
                >
                  <FileDown className="h-4 w-4 mr-2" />
                  Export CSV
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{attendanceSheet.total_classes_conducted}</div>
                <div className="text-sm text-blue-700">Classes Conducted</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{attendanceSheet.students.length}</div>
                <div className="text-sm text-green-700">Total Students</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {attendanceSheet.students.length > 0
                    ? Math.round(attendanceSheet.students.reduce((sum, s) => sum + s.attendance_percentage, 0) / attendanceSheet.students.length)
                    : 0}%
                </div>
                <div className="text-sm text-purple-700">Average Attendance</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {format(new Date(attendanceSheet.last_updated), 'MMM dd, HH:mm')}
                </div>
                <div className="text-sm text-orange-700">Last Updated</div>
              </div>
            </div>

            {/* Attendance Grid */}
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="sticky left-0 bg-background z-10 min-w-[120px]">USN</TableHead>
                    <TableHead className="sticky left-[120px] bg-background z-10 min-w-[200px]">Student Name</TableHead>
                    {attendanceSheet.class_dates.map((sessionKey) => (
                      <TableHead key={sessionKey} className="text-center min-w-[120px]">
                        {FacultyAttendanceSheetService.formatSessionHeader(sessionKey)}
                      </TableHead>
                    ))}
                    <TableHead className="text-center min-w-[80px]">Total</TableHead>
                    <TableHead className="text-center min-w-[80px]">Attended</TableHead>
                    <TableHead className="text-center min-w-[100px]">Percentage</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {attendanceSheet.students.map((student) => (
                    <TableRow key={student.student_id}>
                      <TableCell className="sticky left-0 bg-background z-10 font-medium">
                        {student.usn}
                      </TableCell>
                      <TableCell className="sticky left-[120px] bg-background z-10">
                        {student.student_name}
                      </TableCell>
                      {attendanceSheet.class_dates.map((sessionKey) => (
                        <TableCell key={sessionKey} className="text-center">
                          {student.sequential_numbers[sessionKey] === 'A' ? (
                            <Badge variant="destructive" className="text-xs">A</Badge>
                          ) : student.sequential_numbers[sessionKey] ? (
                            <Badge variant="default" className="text-xs">
                              {student.sequential_numbers[sessionKey]}
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                      ))}
                      <TableCell className="text-center font-medium">
                        {student.total_classes}
                      </TableCell>
                      <TableCell className="text-center font-medium">
                        {student.classes_attended}
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge variant={getStatusBadgeVariant(student.attendance_percentage)}>
                          {student.attendance_percentage}%
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {attendanceSheet.students.length === 0 && (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Attendance Data</h3>
                <p className="text-muted-foreground">
                  No attendance records found for this subject. Start marking attendance to see the sheet.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FacultyAttendanceSheet;
