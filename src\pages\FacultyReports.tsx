import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import {
  FacultyReportsService,
  SubjectAttendanceReport,
  ReportFilters,
  StudentAttendanceSummary
} from '@/services/FacultyReportsService';
import { AttendanceService } from '@/services/AttendanceService';
import {
  FileText,
  Download,
  Filter,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Calendar,
  BookOpen,
  BarChart3,
  Refresh<PERSON>w
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FacultySubject {
  subject_code: string;
  subject_name: string;
  semester: string;
  section: string;
  department: string;
  subject_type: string;
  batch_name?: string;
}

const FacultyReports: React.FC = () => {
  const [subjects, setSubjects] = useState<FacultySubject[]>([]);
  const [selectedSubject, setSelectedSubject] = useState<FacultySubject | null>(null);
  const [report, setReport] = useState<SubjectAttendanceReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingSubjects, setLoadingSubjects] = useState(false);
  const [filters, setFilters] = useState<ReportFilters>({
    date_from: '',
    date_to: '',
    show_below_percentage: undefined,
    show_above_percentage: undefined,
    semester: '',
    section: '',
    subjectCode: ''
  });

  // Enhanced report state (now supports both single subject and comprehensive reports)
  const [comprehensiveReport, setComprehensiveReport] = useState<any>(null);
  const [reportMode, setReportMode] = useState<'single' | 'comprehensive'>('single');


  const { user } = useAuth();
  const { department } = useUserDepartment();
  const { toast } = useToast();

  // Load faculty subjects when component mounts
  useEffect(() => {
    if (user?.id && department) {
      loadFacultySubjects();
    }
  }, [user?.id, department]);

  const loadFacultySubjects = async () => {
    if (!user?.id || !department) return;

    try {
      setLoadingSubjects(true);
      const facultySubjects = await FacultyReportsService.getFacultySubjects(user.id, department);
      setSubjects(facultySubjects);

      if (facultySubjects.length === 0) {
        toast({
          title: 'No Subjects Found',
          description: 'No subjects assigned to you were found in the timetable.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error loading faculty subjects:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your assigned subjects.',
        variant: 'destructive',
      });
    } finally {
      setLoadingSubjects(false);
    }
  };

  const generateReport = async () => {
    if (!user?.id || !department) return;

    try {
      setLoading(true);

      if (reportMode === 'comprehensive') {
        // Generate comprehensive report from usn_attendance table
        console.log('📊 Generating comprehensive faculty report from usn_attendance table...');

        const reportData = await AttendanceService.getFacultyAttendanceReport(
          user.id,
          department,
          filters.semester || undefined,
          filters.section || undefined,
          filters.subjectCode || undefined,
          filters.date_from || undefined,
          filters.date_to || undefined,
          undefined
        );

        console.log('📊 Raw report data received:', reportData);
        console.log('📊 Number of subjects found:', reportData.subjects?.length || 0);
        console.log('📊 Summary data:', reportData.summary);

        // Apply filtering logic
        const filteredReport = {
          ...reportData,
          subjects: reportData.subjects.map(subject => {
            console.log(`📊 Processing subject: ${subject.subject_code} (${subject.subject_type})`);
            console.log(`📊 Students before filtering: ${subject.students.length}`);

            const filteredStudents = subject.students.filter(student => {
              const percentage = student.attendance_percentage;

              if (filters.show_below_percentage && filters.show_above_percentage) {
                return percentage >= filters.show_above_percentage && percentage < filters.show_below_percentage;
              } else if (filters.show_below_percentage) {
                return percentage < filters.show_below_percentage;
              } else if (filters.show_above_percentage) {
                return percentage >= filters.show_above_percentage;
              }
              return true;
            });

            console.log(`📊 Students after filtering: ${filteredStudents.length}`);

            return {
              ...subject,
              students: filteredStudents
            };
          })
        };

        setComprehensiveReport(filteredReport);
        setReport(null); // Clear single subject report

        const totalFilteredStudents = filteredReport.subjects.reduce((sum, subject) => sum + subject.students.length, 0);

        toast({
          title: 'Comprehensive Report Generated',
          description: `Report generated with ${filteredReport.subjects.length} subjects and ${totalFilteredStudents} students.`,
        });
      } else {
        // Generate single subject report (existing functionality)
        if (!selectedSubject) {
          toast({
            title: 'No Subject Selected',
            description: 'Please select a subject to generate a report.',
            variant: 'destructive',
          });
          return;
        }

        const reportData = await FacultyReportsService.generateSubjectReport(
          user.id,
          department,
          selectedSubject.subject_code,
          selectedSubject.semester,
          selectedSubject.section,
          selectedSubject.subject_type,
          selectedSubject.batch_name,
          filters
        );

        setReport(reportData);
        setComprehensiveReport(null); // Clear comprehensive report

        toast({
          title: 'Report Generated',
          description: `Report generated for ${selectedSubject.subject_code} with ${reportData.students.length} students.`,
        });
      }
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate attendance report.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };



  const getStatusBadge = (student: StudentAttendanceSummary) => {
    switch (student.status) {
      case 'good':
        return (
          <Badge variant="outline" className="text-green-700 border-green-300 bg-green-50">
            <CheckCircle className="h-3 w-3 mr-1" />
            Good
          </Badge>
        );
      case 'warning':
        return (
          <Badge variant="outline" className="text-yellow-700 border-yellow-300 bg-yellow-50">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Warning
          </Badge>
        );
      case 'critical':
        return (
          <Badge variant="outline" className="text-red-700 border-red-300 bg-red-50">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Critical
          </Badge>
        );
      default:
        return null;
    }
  };

  const getAttendanceColor = (percentage: number) => {
    if (percentage >= 75) return 'text-green-600';
    if (percentage >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const exportToCSV = () => {
    if (!report) return;

    const headers = ['USN', 'Student Name', 'Total Classes', 'Classes Attended', 'Attendance %', 'Status'];
    const csvContent = [
      headers.join(','),
      ...report.students.map(student => [
        student.usn,
        `"${student.student_name}"`,
        student.total_classes,
        student.classes_attended,
        student.attendance_percentage,
        student.status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    // Create descriptive filename based on filters
    let filename = `${report.subject_code}_attendance_report`;
    if (filters.show_below_percentage && filters.show_above_percentage) {
      filename += `_${filters.show_above_percentage}to${filters.show_below_percentage}percent`;
    } else if (filters.show_below_percentage) {
      filename += `_below_${filters.show_below_percentage}percent`;
    } else if (filters.show_above_percentage) {
      filename += `_above_${filters.show_above_percentage}percent`;
    }
    if (filters.date_from || filters.date_to) {
      filename += '_filtered';
    }
    filename += '.csv';

    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: 'Export Successful',
      description: 'Attendance report exported to CSV file.',
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2 mb-6">
        <FileText className="h-6 w-6 text-primary" />
        <h1 className="text-3xl font-bold">Faculty Attendance Reports</h1>
      </div>



      {/* Enhanced Subject Selection with Report Mode */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Generate Faculty Attendance Report
          </CardTitle>
          <CardDescription>
            Choose between single subject detailed report or comprehensive multi-subject overview
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Report Mode Toggle */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
            <Label className="font-medium">Report Type:</Label>
            <div className="flex gap-2">
              <Button
                variant={reportMode === 'single' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setReportMode('single');
                  setComprehensiveReport(null);
                  setReport(null);
                }}
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Single Subject
              </Button>
              <Button
                variant={reportMode === 'comprehensive' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setReportMode('comprehensive');
                  setComprehensiveReport(null);
                  setReport(null);
                }}
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                All My Subjects
              </Button>
            </div>
          </div>

          {/* Subject Selection */}
          <div className="space-y-4">
            {reportMode === 'single' ? (
              <div className="p-4 bg-blue-50 rounded-lg">
                <Label className="font-medium">Select Subject for Detailed Report</Label>
                <Select
                  value={selectedSubject ? `${selectedSubject.subject_code}-${selectedSubject.semester}-${selectedSubject.section}-${selectedSubject.subject_type}-${selectedSubject.batch_name || 'no-batch'}` : ''}
                  onValueChange={(value) => {
                    const subject = subjects.find(s => `${s.subject_code}-${s.semester}-${s.section}-${s.subject_type}-${s.batch_name || 'no-batch'}` === value);
                    setSelectedSubject(subject || null);
                    setReport(null);
                    setComprehensiveReport(null);
                  }}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Select a subject" />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map((subject) => (
                      <SelectItem
                        key={`${subject.subject_code}-${subject.semester}-${subject.section}-${subject.subject_type}-${subject.batch_name || 'no-batch'}`}
                        value={`${subject.subject_code}-${subject.semester}-${subject.section}-${subject.subject_type}-${subject.batch_name || 'no-batch'}`}
                      >
                        {subject.subject_code} - {subject.subject_name}
                        {subject.subject_type === 'theory' ? ' (Theory)' : ' (Lab)'}
                        {subject.batch_name ? ` - Batch ${subject.batch_name}` : ''}
                        {' '}(Sem {subject.semester}, Sec {subject.section})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="p-4 bg-green-50 rounded-lg">
                <Label className="font-medium">Comprehensive Report Options</Label>
                <p className="text-sm text-muted-foreground mb-3">
                  Generate a comprehensive report for all your assigned subjects, or filter by specific subject.
                </p>
                <Select
                  value={filters.subjectCode || 'all'}
                  onValueChange={(value) => {
                    if (value === 'all') {
                      setFilters(prev => ({
                        ...prev,
                        subjectCode: '',
                        semester: '',
                        section: ''
                      }));
                    } else {
                      const subject = subjects.find(s =>
                        `${s.subject_code}-${s.semester}-${s.section}-${s.subject_type}-${s.batch_name || 'no-batch'}` === value
                      );
                      if (subject) {
                        setFilters(prev => ({
                          ...prev,
                          subjectCode: subject.subject_code,
                          semester: subject.semester,
                          section: subject.section
                        }));
                      }
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All My Subjects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">📊 All My Subjects (Comprehensive)</SelectItem>
                    {subjects.map((subject) => (
                      <SelectItem
                        key={`${subject.subject_code}-${subject.semester}-${subject.section}-${subject.subject_type}-${subject.batch_name || 'no-batch'}`}
                        value={`${subject.subject_code}-${subject.semester}-${subject.section}-${subject.subject_type}-${subject.batch_name || 'no-batch'}`}
                      >
                        🎯 {subject.subject_code} - {subject.subject_name}
                        {subject.subject_type === 'theory' ? ' (Theory)' : ' (Lab)'}
                        {subject.batch_name ? ` - Batch ${subject.batch_name}` : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {filters.subjectCode && (
                  <div className="mt-2 p-2 bg-white rounded border border-green-200">
                    <p className="text-sm font-medium text-green-800">
                      📊 Will generate report for: {filters.subjectCode}
                    </p>
                  </div>
                )}
              </div>
            )}

            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={loadFacultySubjects}
                disabled={loadingSubjects}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loadingSubjects ? 'animate-spin' : ''}`} />
                Refresh Subjects
              </Button>
            </div>

            {subjects.length === 0 && !loadingSubjects && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  No subjects found. Make sure you have classes assigned in the timetable.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      {(selectedSubject || reportMode === 'comprehensive') && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Report Filters
            </CardTitle>
            <CardDescription>
              Configure filters for the attendance report. By default, all students are shown. Use percentage filters to identify students needing attention or recognition.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="date_from">From Date</Label>
                <Input
                  id="date_from"
                  type="date"
                  value={filters.date_from}
                  onChange={(e) => setFilters(prev => ({ ...prev, date_from: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="date_to">To Date</Label>
                <Input
                  id="date_to"
                  type="date"
                  value={filters.date_to}
                  onChange={(e) => setFilters(prev => ({ ...prev, date_to: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="show_below">Students Below %</Label>
                <Select
                  value={filters.show_below_percentage?.toString() || 'all'}
                  onValueChange={(value) => setFilters(prev => ({
                    ...prev,
                    show_below_percentage: value === 'all' ? undefined : Number(value)
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Students" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Students</SelectItem>
                    <SelectItem value="90">Below 90%</SelectItem>
                    <SelectItem value="80">Below 80%</SelectItem>
                    <SelectItem value="75">Below 75% (Standard)</SelectItem>
                    <SelectItem value="70">Below 70%</SelectItem>
                    <SelectItem value="60">Below 60%</SelectItem>
                    <SelectItem value="50">Below 50% (Critical)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Show students with attendance below selected percentage
                </p>
              </div>
              <div>
                <Label htmlFor="show_above">Students Above %</Label>
                <Select
                  value={filters.show_above_percentage?.toString() || 'all'}
                  onValueChange={(value) => setFilters(prev => ({
                    ...prev,
                    show_above_percentage: value === 'all' ? undefined : Number(value)
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Students" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Students</SelectItem>
                    <SelectItem value="90">Above 90% (Excellent)</SelectItem>
                    <SelectItem value="80">Above 80%</SelectItem>
                    <SelectItem value="75">Above 75% (Meeting Req.)</SelectItem>
                    <SelectItem value="50">Above 50% (Passing)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  Show students with attendance above selected percentage
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button onClick={generateReport} disabled={loading}>
                {loading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Generate Report
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setFilters({
                    date_from: '',
                    date_to: '',
                    show_below_percentage: undefined,
                    show_above_percentage: undefined,
                    semester: '',
                    section: '',
                    subjectCode: ''
                  });
                  setReport(null);
                  setComprehensiveReport(null);
                }}
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* NEW: Comprehensive Report Results */}
      {comprehensiveReport && (
        <>
          {/* Comprehensive Report Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Comprehensive Faculty Report Summary
              </CardTitle>
              <CardDescription>
                Overview of all subjects with theory/lab separation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{comprehensiveReport.summary.total_subjects}</div>
                  <div className="text-sm text-blue-700">Total Subjects</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{comprehensiveReport.summary.total_students}</div>
                  <div className="text-sm text-green-700">Total Students</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{comprehensiveReport.summary.total_classes}</div>
                  <div className="text-sm text-purple-700">Total Classes</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{comprehensiveReport.summary.average_attendance}%</div>
                  <div className="text-sm text-orange-700">Average Attendance</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {comprehensiveReport.subjects.reduce((sum: number, subject: any) => sum + subject.students.length, 0)}
                  </div>
                  <div className="text-sm text-purple-700">
                    {filters.show_below_percentage && filters.show_above_percentage
                      ? `Students ${filters.show_above_percentage}%-${filters.show_below_percentage}%`
                      : filters.show_below_percentage
                      ? `Students Below ${filters.show_below_percentage}%`
                      : filters.show_above_percentage
                      ? `Students Above ${filters.show_above_percentage}%`
                      : 'Students Shown'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Subject-wise Reports */}
          {comprehensiveReport.subjects.map((subject: any, index: number) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  {subject.subject_code} ({subject.subject_type.toUpperCase()})
                </CardTitle>
                <CardDescription>
                  {subject.total_classes} classes conducted • {subject.total_students} students • {subject.average_attendance}% average attendance
                </CardDescription>
              </CardHeader>
              <CardContent>
                {subject.students.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>USN</TableHead>
                        <TableHead>Student Name</TableHead>
                        <TableHead>Classes Attended</TableHead>
                        <TableHead>Total Classes</TableHead>
                        <TableHead>Attendance %</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {subject.students.map((student: any, studentIndex: number) => (
                        <TableRow key={studentIndex}>
                          <TableCell className="font-medium">{student.student_usn}</TableCell>
                          <TableCell>{student.student_name}</TableCell>
                          <TableCell>{student.classes_attended}</TableCell>
                          <TableCell>{student.total_classes}</TableCell>
                          <TableCell>
                            <span className={getAttendanceColor(student.attendance_percentage)}>
                              {student.attendance_percentage}%
                            </span>
                          </TableCell>
                          <TableCell>
                            {student.attendance_percentage >= 75 ? (
                              <Badge variant="outline" className="text-green-700 border-green-300 bg-green-50">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Good
                              </Badge>
                            ) : student.attendance_percentage >= 50 ? (
                              <Badge variant="outline" className="text-yellow-700 border-yellow-300 bg-yellow-50">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Warning
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-red-700 border-red-300 bg-red-50">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Critical
                              </Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground">No students found matching the criteria.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </>
      )}

      {/* Report Results */}
      {report && (
        <>
          {/* Report Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Report Summary
              </CardTitle>
              <CardDescription>
                {report.subject_code} - {report.subject_name} (Semester {report.semester}, Section {report.section})
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{report.total_classes_conducted}</div>
                  <div className="text-sm text-blue-700">Classes Conducted</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{report.total_students}</div>
                  <div className="text-sm text-green-700">Total Students</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{report.average_attendance_percentage}%</div>
                  <div className="text-sm text-purple-700">Average Attendance</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{report.students.length}</div>
                  <div className="text-sm text-orange-700">
                    {filters.show_below_percentage && filters.show_above_percentage
                      ? `Students ${filters.show_above_percentage}%-${filters.show_below_percentage}%`
                      : filters.show_below_percentage
                      ? `Students Below ${filters.show_below_percentage}%`
                      : filters.show_above_percentage
                      ? `Students Above ${filters.show_above_percentage}%`
                      : 'Students Shown'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Student Attendance Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Student Attendance Details
                  </CardTitle>
                  <CardDescription>
                    {filters.show_below_percentage && filters.show_above_percentage
                      ? `Students with attendance between ${filters.show_above_percentage}% and ${filters.show_below_percentage}%`
                      : filters.show_below_percentage
                      ? `Students with attendance below ${filters.show_below_percentage}% - requiring attention`
                      : filters.show_above_percentage
                      ? `Students with attendance above ${filters.show_above_percentage}% - performing well`
                      : 'Complete attendance information for all students in the class'
                    }
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  onClick={exportToCSV}
                  disabled={!report || report.students.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export CSV
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {report.students.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {filters.show_below_percentage && filters.show_above_percentage
                      ? `No students found with attendance between ${filters.show_above_percentage}% and ${filters.show_below_percentage}%.`
                      : filters.show_below_percentage
                      ? `No students found with attendance below ${filters.show_below_percentage}%. This is good news - all students meet the attendance requirement!`
                      : filters.show_above_percentage
                      ? `No students found with attendance above ${filters.show_above_percentage}%.`
                      : 'No students found. This may indicate no attendance has been marked yet for this subject.'
                    }
                  </p>
                  {(filters.show_below_percentage || filters.show_above_percentage) && (
                    <p className="text-sm text-muted-foreground mt-2">
                      Clear the filters to see all students in the class.
                    </p>
                  )}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>USN</TableHead>
                        <TableHead>Student Name</TableHead>
                        <TableHead className="text-center">Total Classes</TableHead>
                        <TableHead className="text-center">Classes Attended</TableHead>
                        <TableHead className="text-center">Attendance %</TableHead>
                        <TableHead className="text-center">Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {report.students.map((student) => (
                        <TableRow key={student.student_id}>
                          <TableCell className="font-medium">{student.usn}</TableCell>
                          <TableCell>{student.student_name}</TableCell>
                          <TableCell className="text-center">{student.total_classes}</TableCell>
                          <TableCell className="text-center">{student.classes_attended}</TableCell>
                          <TableCell className={`text-center font-semibold ${getAttendanceColor(student.attendance_percentage)}`}>
                            {student.attendance_percentage}%
                          </TableCell>
                          <TableCell className="text-center">
                            {getStatusBadge(student)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default FacultyReports;
