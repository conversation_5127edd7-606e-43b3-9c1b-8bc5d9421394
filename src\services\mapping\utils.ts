
// src/services/mapping/utils.ts

/**
 * Helper to normalize names by stripping titles, dots, trimming & lowercasing
 * @param s The string to normalize
 * @returns The normalized string
 */
export function normalizeName(s: string): string {
  if (!s) return "";
  return s
    .replace(/^(Mr|Ms|Mrs|Dr)\.?\s+/i, "")   // remove honorific
    .replace(/\./g, "")                      // remove dots
    .trim()
    .toLowerCase();
}
