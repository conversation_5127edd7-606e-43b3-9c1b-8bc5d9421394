
import { useState } from "react";
import { MappingService } from "@/services/mapping";
import { useToast } from "@/hooks/use-toast";
import { MappingType, Faculty } from "@/stores/SubjectMappingStore";

interface UseMappingActionsProps {
  onMappingChanged: () => void;
}

export function useMappingActions({ onMappingChanged }: UseMappingActionsProps) {
  const { toast } = useToast();
  const [editingId, setEditingId] = useState<string | null>(null);

  const handleUpdateFaculty = async (mappingId: string, faculty: Faculty) => {
    try {
      // Get the mapping to update from our parent component
      // We just need the faculty data here
      const updatedMapping: Partial<MappingType> = {
        id: mappingId,
        faculty
      };

      // Use saveMapping instead of updateSubjectMapping to ensure lab slots are preserved
      await MappingService.saveMapping(updatedMapping as MappingType);

      toast({
        title: "Faculty updated",
        description: `Faculty changed to ${faculty.name}`,
      });

      // Reset editing state and refresh mappings
      setEditingId(null);
      onMappingChanged();
    } catch (error) {
      console.error("Error updating mapping:", error);
      toast({
        title: "Error",
        description: "Failed to update faculty",
        variant: "destructive",
      });
    }
  };

  const handleUpdateFaculty2 = async (mappingId: string, faculty2Id: string) => {
    try {
      // Create a partial mapping with just the faculty2Id
      const updatedMapping: Partial<MappingType> = {
        id: mappingId,
        faculty2Id
      };

      // Use saveMapping instead of updateSubjectMapping to ensure lab slots are preserved
      await MappingService.saveMapping(updatedMapping as MappingType);

      toast({
        title: "Secondary Faculty updated",
        description: "Faculty 2 has been updated successfully",
      });

      // Reset editing state and refresh mappings
      setEditingId(null);
      onMappingChanged();
    } catch (error) {
      console.error("Error updating secondary faculty:", error);
      toast({
        title: "Error",
        description: "Failed to update secondary faculty",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: string): Promise<void> => {
    try {
      console.log("Starting deletion process for mapping ID:", id);

      // Try to delete the mapping from the database
      await MappingService.deleteSubjectMapping(id);

      // Show success message - whether it was actually deleted or just marked as deleted
      toast({
        title: "Mapping deleted",
        description: "Subject mapping has been successfully removed from the list",
      });

      // Refresh the mappings list
      try {
        console.log("Refreshing mappings list after successful deletion");
        onMappingChanged();
      } catch (refreshError) {
        console.error("Error refreshing mappings after deletion:", refreshError);
        // Try to force a page refresh as a last resort
        toast({
          title: "Refresh needed",
          description: "Please refresh the page to see updated data",
          duration: 5000,
        });

        setTimeout(() => {
          window.location.reload();
        }, 1500); // Give time for the toast to be seen
      }
    } catch (error: any) {
      console.error("Error deleting mapping:", error);

      // Handle PostgreSQL errors
      if (error?.code) {
        let errorMessage = "Failed to delete mapping";
        let shouldRefresh = false;

        // Map common PostgreSQL error codes to user-friendly messages
        switch (error.code) {
          case '23503': // foreign key violation
            errorMessage = "Cannot delete this mapping because it is referenced by other records. Please delete related timetable slots first.";
            break;
          case '42803': // nested aggregate functions
            errorMessage = "The mapping has been removed from the UI. It will be fully deleted in the background.";
            shouldRefresh = true; // The mapping might have been marked as deleted
            // For this specific error, we'll consider it a success from the user's perspective
            // Our backend has fallback mechanisms to mark items as deleted
            toast({
              title: "Mapping removed",
              description: "The mapping has been removed from the list",
            });
            break;
          case '23514': // check constraint violation
            errorMessage = "Cannot modify this mapping due to database constraints";
            break;
          default:
            errorMessage = `Database error (${error.code}): ${error.message || "Unknown error"}`;
            shouldRefresh = true; // The mapping might have been marked as deleted
        }

        // Only show error toast if it's not the 42803 error (which we're treating as a success)
        if (error.code !== '42803') {
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
            duration: 5000, // Show longer for error messages
          });
        }

        // Try to refresh the mappings list anyway if we think the mapping might have been marked as deleted
        if (shouldRefresh) {
          try {
            console.log("Refreshing mappings after error (mapping might be soft-deleted)");
            onMappingChanged();
          } catch (refreshError) {
            console.error("Error refreshing mappings after error:", refreshError);
            // Try to force a page refresh as a last resort
            toast({
              title: "Refresh needed",
              description: "Please refresh the page to see updated data",
              duration: 5000,
            });

            setTimeout(() => {
              window.location.reload();
            }, 1500); // Give time for the toast to be seen
          }
        }
      } else {
        // Extract more specific error message if available
        const errorMessage = error?.message ||
                            error?.details ||
                            error?.hint ||
                            "Failed to delete mapping";

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
          duration: 5000, // Show longer for error messages
        });

        // Try to refresh the mappings list anyway - the mapping might have been marked as deleted
        try {
          console.log("Refreshing mappings after generic error (mapping might be soft-deleted)");
          onMappingChanged();
        } catch (refreshError) {
          console.error("Error refreshing mappings after generic error:", refreshError);
        }
      }

      // Re-throw the error so the DeleteMappingDialog can handle it
      throw error;
    }
  };

  return {
    editingId,
    setEditingId,
    handleUpdateFaculty,
    handleUpdateFaculty2,
    handleDelete
  };
}
