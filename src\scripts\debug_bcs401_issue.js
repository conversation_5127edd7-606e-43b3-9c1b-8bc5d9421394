/**
 * Debug script specifically for BCS401 "Unknown Subject" issue
 * Investigates why BCS401 is not getting its subject name from timetable
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://milmyotuougemocvieof.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1MjE5NzQsImV4cCI6MjA1MDA5Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test configuration - using the student from the screenshot
const TEST_CONFIG = {
  studentUsn: '1KS23CS001', // Replace with actual USN from screenshot
  academicYear: '2024-25',
  department: 'cse',
  semester: '4',
  section: 'A'
};

async function debugBCS401Issue() {
  console.log('🔍 Debugging BCS401 "Unknown Subject" Issue\n');
  console.log('Test Configuration:', TEST_CONFIG);

  try {
    // Step 1: Check if student exists and get their details
    console.log('\n📋 Step 1: Getting Student Details');
    const { data: studentData, error: studentError } = await supabase
      .from('class_students')
      .select('*')
      .eq('usn', TEST_CONFIG.studentUsn.toUpperCase())
      .single();

    if (studentError || !studentData) {
      console.log('❌ Student not found:', studentError?.message);
      return;
    }

    console.log('✅ Student found:', {
      id: studentData.id,
      name: studentData.student_name,
      department: studentData.department,
      semester: studentData.semester,
      section: studentData.section,
      academic_year: studentData.academic_year
    });

    // Use student's actual class details
    const actualDepartment = studentData.department;
    const actualSemester = studentData.semester;
    const actualSection = studentData.section;

    // Step 2: Check what's in timetable_slots for this exact class
    console.log('\n📋 Step 2: Checking Timetable Slots for Student\'s Class');
    console.log(`Querying: department=${actualDepartment}, semester=${actualSemester}, section=${actualSection}`);

    const { data: timetableData, error: timetableError } = await supabase
      .from('timetable_slots')
      .select('subject_code, subject_name, subject_type, batch_name')
      .eq('department', actualDepartment)
      .eq('semester', actualSemester)
      .eq('section', actualSection);

    if (timetableError) {
      console.log('❌ Error fetching timetable:', timetableError.message);
    } else {
      console.log(`✅ Found ${timetableData?.length || 0} timetable entries`);

      // Check if BCS401 is in the timetable
      const bcs401Entries = timetableData?.filter(slot => slot.subject_code === 'BCS401') || [];
      console.log(`🔍 BCS401 entries found: ${bcs401Entries.length}`);

      if (bcs401Entries.length > 0) {
        console.log('✅ BCS401 found in timetable:');
        bcs401Entries.forEach(entry => {
          console.log(`  - Subject Name: "${entry.subject_name}"`);
          console.log(`  - Subject Type: "${entry.subject_type}"`);
          console.log(`  - Batch Name: "${entry.batch_name}"`);
        });
      } else {
        console.log('❌ BCS401 NOT found in timetable for this class');
      }

      // Show all subjects in timetable for comparison
      console.log('\n📚 All subjects in timetable for this class:');
      const uniqueSubjects = Array.from(
        new Map(timetableData?.map(s => [s.subject_code, s]) || []).values()
      );
      uniqueSubjects.forEach(subject => {
        console.log(`  ${subject.subject_code}: "${subject.subject_name}" (${subject.subject_type})`);
      });
    }

    // Step 3: Check IA records for BCS401
    console.log('\n📋 Step 3: Checking IA Records for BCS401');
    const { data: iaRecords, error: iaError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', studentData.id)
      .eq('subject_code', 'BCS401');

    if (iaError) {
      console.log('❌ Error fetching IA records:', iaError.message);
    } else {
      console.log(`✅ Found ${iaRecords?.length || 0} IA records for BCS401`);
      iaRecords?.forEach(record => {
        console.log(`  - Academic Year: ${record.academic_year}`);
        console.log(`  - Department: ${record.department}`);
        console.log(`  - Semester: ${record.semester}`);
        console.log(`  - Section: ${record.section}`);
        console.log(`  - Subject Name in IA: "${record.subject_name}"`);
        console.log(`  - IA1: ${record.ia1_marks}, IA2: ${record.ia2_marks}, IA3: ${record.ia3_marks}`);
      });
    }

    // Step 4: Check if there are any department/semester/section mismatches
    console.log('\n📋 Step 4: Checking for Data Mismatches');

    // Check if BCS401 exists with different department/semester/section combinations
    const { data: allBCS401, error: allBCS401Error } = await supabase
      .from('timetable_slots')
      .select('subject_code, subject_name, department, semester, section')
      .eq('subject_code', 'BCS401');

    if (!allBCS401Error && allBCS401) {
      console.log(`🔍 BCS401 found in other classes: ${allBCS401.length} entries`);
      allBCS401.forEach(entry => {
        const isMatch = entry.department === actualDepartment &&
                       entry.semester === actualSemester &&
                       entry.section === actualSection;
        console.log(`  ${entry.department}-${entry.semester}-${entry.section}: "${entry.subject_name}" ${isMatch ? '✅ MATCH' : '❌ NO MATCH'}`);
      });
    }

    // Step 5: Test the subject filtering logic
    console.log('\n📋 Step 5: Testing Subject Filtering Logic');

    if (timetableData && timetableData.length > 0) {
      // Apply the same filtering logic as the service
      const allSubjects = Array.from(
        new Map(timetableData.map(s => [s.subject_code, s])).values()
      );

      const filteredSubjects = allSubjects.filter(subject => {
        const subjectName = (subject.subject_name || '').toLowerCase();
        const subjectCode = (subject.subject_code || '').toLowerCase();

        // Filter out skill lab and tutorial subjects
        return !subjectName.includes('skill lab') &&
               !subjectName.includes('tutorial') &&
               !subjectCode.includes('skill') &&
               !subjectCode.includes('tutorial');
      });

      console.log(`📊 Before filtering: ${allSubjects.length} subjects`);
      console.log(`📊 After filtering: ${filteredSubjects.length} subjects`);

      const bcs401Filtered = filteredSubjects.find(s => s.subject_code === 'BCS401');
      if (bcs401Filtered) {
        console.log(`✅ BCS401 survived filtering: "${bcs401Filtered.subject_name}"`);
      } else {
        console.log('❌ BCS401 was filtered out or not present');
      }
    }

    // Step 6: Simulate the exact lookup logic from the service
    console.log('\n📋 Step 6: Simulating Service Lookup Logic');

    if (iaRecords && iaRecords.length > 0 && timetableData) {
      const record = iaRecords[0]; // Take first IA record

      // Normalize subject code (same as service)
      const normalizedSubjectCode = record.subject_code.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
      console.log(`🔍 Normalized subject code: ${record.subject_code} -> ${normalizedSubjectCode}`);

      // Find subject in timetable (same as service)
      const allStudentSubjects = Array.from(
        new Map(timetableData.map(s => [s.subject_code, s])).values()
      ).filter(subject => {
        const subjectName = (subject.subject_name || '').toLowerCase();
        const subjectCode = (subject.subject_code || '').toLowerCase();
        return !subjectName.includes('skill lab') &&
               !subjectName.includes('tutorial') &&
               !subjectCode.includes('skill') &&
               !subjectCode.includes('tutorial');
      });

      const subjectInfo = allStudentSubjects.find(s => s.subject_code === normalizedSubjectCode);
      console.log(`🔍 Subject lookup result:`, subjectInfo);

      const originalSubjectName = subjectInfo?.subject_name || record.subject_name;
      console.log(`🔍 Original subject name: "${originalSubjectName}"`);

      // Apply display name logic (same as service)
      const displayName = getSubjectDisplayName(record.subject_code, originalSubjectName);
      console.log(`🔍 Final display name: "${displayName}"`);
    }

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

// Subject display name function (same as service)
function getSubjectDisplayName(subjectCode, originalSubjectName) {
  const LAB_SUBJECT_MAPPINGS = {
    'BCS403': 'DBMS Lab',
    'BCS402': 'MC Lab',
    'BCS401': 'ADA Lab',
    'BCS404': 'CN Lab',
    'BCS405': 'Software Engineering Lab',
    'BCSL403': 'DBMS Lab',
    'BCSL402': 'MC Lab',
    'BCSL401': 'ADA Lab',
    'BCSL404': 'CN Lab',
    'BCSL405': 'Software Engineering Lab',
  };

  // Detect batch suffix pattern
  const batchPattern = /^(.+)_([A-Z]\d+)$/;
  const match = subjectCode.match(batchPattern);
  const isLabBatch = !!match;

  // For lab batches (with batch suffixes like _A1, _B2), always use lab mappings
  if (isLabBatch) {
    const [, baseCode, batchSuffix] = match;
    const labName = LAB_SUBJECT_MAPPINGS[baseCode];
    if (labName) {
      return batchSuffix ? `${labName} (${batchSuffix})` : labName;
    }
  }

  // For subjects with explicit _LAB suffix, use lab mappings
  if (subjectCode.includes('_LAB')) {
    const normalizedCode = subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
    const labName = LAB_SUBJECT_MAPPINGS[normalizedCode];
    if (labName) {
      return labName;
    }
  }

  // For BCSL prefixed subjects (clear lab indicators), use lab mappings only if unknown
  if (subjectCode.startsWith('BCSL') && (!originalSubjectName || originalSubjectName === 'Unknown Subject')) {
    const labName = LAB_SUBJECT_MAPPINGS[subjectCode];
    if (labName) {
      return labName;
    }
  }

  return originalSubjectName || 'Unknown Subject';
}

// Run the debug script
debugBCS401Issue();
