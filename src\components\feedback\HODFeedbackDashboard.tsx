import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useAuth } from '@/contexts/AuthContext';
import {
  Plus,
  Calendar,
  Users,
  BarChart3,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp
} from 'lucide-react';

import { FeedbackService } from '@/services/FeedbackService';
import {
  FeedbackSession,
  FeedbackReport,
  CreateFeedbackSessionRequest
} from '@/types/feedback-system';

import CreateFeedbackSessionDialog from './CreateFeedbackSessionDialog';
import FeedbackReportsView from './FeedbackReportsView';
import FeedbackAnalyticsView from './FeedbackAnalyticsView';
import HODFacultyReports from './HODFacultyReports';

const HODFeedbackDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [activeSessions, setActiveSessions] = useState<FeedbackSession[]>([]);
  const [completedSessions, setCompletedSessions] = useState<FeedbackSession[]>([]);
  const [reports, setReports] = useState<FeedbackReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [stats, setStats] = useState({
    totalSessions: 0,
    activeSessions: 0,
    totalResponses: 0,
    completionRate: 0
  });

  const { user } = useAuth();
  const { department, departmentName } = useUserDepartment();
  const { toast } = useToast();

  useEffect(() => {
    if (department) {
      loadDashboardData();
    }
  }, [department]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load active sessions
      const activeSessionsData = await FeedbackService.getActiveFeedbackSessions(department!);
      setActiveSessions(activeSessionsData);

      // Load completed sessions
      const completedSessionsData = await FeedbackService.getCompletedFeedbackSessions(department!);
      setCompletedSessions(completedSessionsData);

      // Calculate stats
      setStats({
        totalSessions: activeSessionsData.length + completedSessionsData.length,
        activeSessions: activeSessionsData.length,
        totalResponses: 0, // Will be calculated from reports
        completionRate: 0 // Will be calculated from reports
      });

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: "Error",
        description: "Failed to load feedback dashboard data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSession = async (request: CreateFeedbackSessionRequest) => {
    try {
      await FeedbackService.createFeedbackSession(request, user!.id);

      toast({
        title: "Success",
        description: "Feedback session created successfully",
      });

      setShowCreateDialog(false);
      loadDashboardData();
    } catch (error) {
      console.error('Error creating feedback session:', error);
      toast({
        title: "Error",
        description: "Failed to create feedback session",
        variant: "destructive",
      });
    }
  };

  const handleDeactivateSession = async (sessionId: string) => {
    try {
      await FeedbackService.deactivateFeedbackSession(sessionId);

      toast({
        title: "Success",
        description: "Feedback session deactivated successfully",
      });

      loadDashboardData();
    } catch (error) {
      console.error('Error deactivating session:', error);
      toast({
        title: "Error",
        description: "Failed to deactivate feedback session",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="loading-spinner h-8 w-8 mx-auto"></div>
          <p className="text-muted-foreground">Loading feedback dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container-modern py-responsive space-y-8">
      {/* Header */}
      <div className="page-header flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-1">
          <h1 className="page-title">Faculty Feedback Management</h1>
          <p className="text-muted-foreground">
            Manage student feedback collection for {departmentName}. Create sessions for current or past months.
          </p>
        </div>
        <Button
          onClick={() => setShowCreateDialog(true)}
          className="gap-2"
        >
          <Plus className="h-4 w-4" />
          Create Feedback Session
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sessions</p>
                <p className="text-2xl font-bold">{stats.totalSessions}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Sessions</p>
                <p className="text-2xl font-bold">{stats.activeSessions}</p>
              </div>
              <Clock className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Responses</p>
                <p className="text-2xl font-bold">{stats.totalResponses}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">{stats.completionRate.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="faculty-reports">Faculty Reports</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Active Sessions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Active Feedback Sessions
              </CardTitle>
              <CardDescription>
                Currently running feedback collection sessions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {activeSessions.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No active feedback sessions. Create a new session to start collecting feedback.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  {activeSessions.map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <h4 className="font-medium">{session.session_name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {session.semester && session.section
                            ? `${session.semester} Semester, Section ${session.section}`
                            : 'All Classes'
                          }
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Ends: {new Date(session.end_date).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="default">Active</Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeactivateSession(session.id)}
                        >
                          Deactivate
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions">
          {/* Sessions management content will be added here */}
          <Card>
            <CardHeader>
              <CardTitle>Session Management</CardTitle>
              <CardDescription>Manage all feedback sessions</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Session management interface coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports">
          <FeedbackReportsView department={department!} />
        </TabsContent>

        <TabsContent value="faculty-reports">
          <HODFacultyReports department={department!} departmentName={departmentName!} />
        </TabsContent>

        <TabsContent value="analytics">
          <FeedbackAnalyticsView department={department!} />
        </TabsContent>


      </Tabs>

      {/* Create Session Dialog */}
      <CreateFeedbackSessionDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleCreateSession}
        department={department!}
      />
    </div>
  );
};

export default HODFeedbackDashboard;
