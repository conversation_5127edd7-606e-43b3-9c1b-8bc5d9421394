
// src/components/subjects/SimpleSubjectEntryForm.tsx
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

import SubjectService, { Subject, NewSubject } from "@/services/SubjectService";

import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";

const schema = z.object({
  academicYear: z.string().min(1, "Academic Year is required"),
  department: z.string().min(1, "Department is required"),
  semester: z.string().min(1, "Semester is required"),
  subjectCode: z.string().min(1, "Subject Code is required"),
  subjectName: z.string().min(1, "Subject Name is required"),
  subjectShortId: z.string().min(1, "Subject Short ID is required").max(10, "Must be 10 characters or less"),
  subjectType: z.enum(["theory", "laboratory", "elective"], {
    required_error: "Please select a subject type",
  }),
});
type FormValues = z.infer<typeof schema>;

export default function SubjectEntryForm() {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      academicYear: "",
      department: "",
      semester: "",
      subjectCode: "",
      subjectName: "",
      subjectShortId: "",
      subjectType: "theory",
    },
  });

  // Static options – replace with your dynamic data if needed
  const academicYears = ["2022-2023", "2023-2024", "2024-2025"];
  const departments = [
    { id: "cse", label: "Computer Science" },
    { id: "ece", label: "Electronics & Communication" },
    { id: "me", label: "Mechanical Engineering" },
    { id: "eee", label: "Electrical & Electronics" },
  ];
  const semesters = ["1", "2", "3", "4", "5", "6", "7", "8"];

  // Load everything once
  useEffect(() => {
    SubjectService.fetchAll().then(setSubjects).catch(console.error);
  }, []);

  // Watch the three selects:
  const watchYear = form.watch("academicYear");
  const watchDept = form.watch("department");
  const watchSem = form.watch("semester");
  const watchCode = form.watch("subjectCode");

  // Auto-generate short ID when code changes - only if field is empty
  useEffect(() => {
    if (watchCode && !form.getValues("subjectShortId")) {
      // Generate a default short ID, but don't force uppercase
      const shortId = watchCode.substring(0, 3);
      form.setValue("subjectShortId", shortId);
    }
  }, [watchCode, form]);

  // Filter to only those matching the selection
  const filtered = subjects.filter(
    (s) =>
      s.academic_year === watchYear &&
      s.department === watchDept &&
      s.semester === watchSem
  );

  // Create or update
  const onSubmit = async (vals: FormValues) => {
    // Create a payload with the exact values from the form
    const payload: NewSubject = {
      academic_year: vals.academicYear,
      department: vals.department,
      semester: vals.semester,
      subject_code: vals.subjectCode,
      subject_name: vals.subjectName,
      subject_short_id: vals.subjectShortId, // Preserve exactly as entered
      subject_type: vals.subjectType,
    };

    try {
      let saved: Subject;
      if (editingId) {
        saved = await SubjectService.update(editingId, payload);
        setSubjects((prev) =>
          prev.map((s) => (s.id === editingId ? saved : s))
        );
      } else {
        saved = await SubjectService.create(payload);
        setSubjects((prev) => [saved, ...prev]);
      }
      form.reset({
        academicYear: watchYear,
        department: watchDept,
        semester: watchSem,
        subjectCode: "",
        subjectName: "",
        subjectShortId: "",
        subjectType: "theory",
      });
      setEditingId(null);
    } catch (err) {
      console.error(err);
    }
  };

  // Prefill form to edit
  const startEdit = (s: Subject) => {
    form.reset({
      academicYear: s.academic_year,
      department: s.department,
      semester: s.semester,
      subjectCode: s.subject_code,
      subjectName: s.subject_name,
      subjectShortId: s.subject_short_id,
      subjectType: s.subject_type,
    });
    setEditingId(s.id);
  };

  const handleDelete = async (id: string) => {
    await SubjectService.remove(id);
    setSubjects((prev) => prev.filter((s) => s.id !== id));
  };

  return (
    <div className="container mx-auto p-6">
      <Form {...form}>
        {/* Year / Dept / Sem */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
          <FormField
            control={form.control}
            name="academicYear"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Academic Year</FormLabel>
                <FormControl>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select year" />
                    </SelectTrigger>
                    <SelectContent>
                      {academicYears.map((y) => (
                        <SelectItem key={y} value={y}>
                          {y}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="department"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Department</FormLabel>
                <FormControl>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select dept" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((d) => (
                        <SelectItem key={d.id} value={d.id}>
                          {d.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="semester"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Semester</FormLabel>
                <FormControl>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select sem" />
                    </SelectTrigger>
                    <SelectContent>
                      {semesters.map((sem) => (
                        <SelectItem key={sem} value={sem}>
                          {sem}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Code / Name / Short ID / Type */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
          <FormField
            control={form.control}
            name="subjectCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Subject Code</FormLabel>
                <FormControl>
                  <Input placeholder="CS301" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="subjectName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Subject Name</FormLabel>
                <FormControl>
                  <Input placeholder="Data Structures" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="subjectShortId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Subject Short ID (case sensitive)</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="e.g. ADA Lab" 
                    {...field} 
                    maxLength={10}
                    onChange={(e) => {
                      // Store the exact value as entered by the user
                      field.onChange(e.target.value);
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Enter exactly as you want it to appear (case will be preserved)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="subjectType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Subject Type</FormLabel>
                <div className="flex space-x-6 mt-1">
                  {["theory", "laboratory", "elective"].map((type) => (
                    <label
                      key={type}
                      className="flex items-center space-x-2"
                    >
                      <input
                        type="radio"
                        value={type}
                        checked={field.value === type}
                        onChange={() => field.onChange(type)}
                        className="h-4 w-4"
                      />
                      <span className="capitalize">{type}</span>
                    </label>
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end">
          <Button onClick={form.handleSubmit(onSubmit)}>
            {editingId ? "Update Subject" : "Add Subject"}
          </Button>
        </div>
      </Form>

      {/* Only show the table once Year+Dept+Sem are selected */}
      {watchYear && watchDept && watchSem && (
        <div className="mt-8 rounded-md border overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Year</TableHead>
                <TableHead>Dept</TableHead>
                <TableHead>Sem</TableHead>
                <TableHead>Code</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Short ID</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filtered.length > 0 ? (
                filtered.map((s) => (
                  <TableRow key={s.id}>
                    <TableCell>{s.academic_year}</TableCell>
                    <TableCell className="uppercase">
                      {s.department}
                    </TableCell>
                    <TableCell>{s.semester}</TableCell>
                    <TableCell>{s.subject_code}</TableCell>
                    <TableCell>{s.subject_name}</TableCell>
                    <TableCell className="font-medium">
                      {s.subject_short_id}
                    </TableCell>
                    <TableCell className="capitalize">
                      {s.subject_type}
                    </TableCell>
                    <TableCell className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => startEdit(s)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(s.id)}
                      >
                        Delete
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-4">
                    No subjects added for this Year / Dept / Sem.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
