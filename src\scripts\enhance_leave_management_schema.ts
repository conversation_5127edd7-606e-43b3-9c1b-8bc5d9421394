import { supabase } from '@/integrations/supabase/client';

/**
 * Enhanced Leave Management System Database Schema Migration
 * 
 * This script adds the necessary fields and tables to support:
 * - Class impact analysis
 * - Intelligent faculty substitution
 * - Attendance system integration
 * - Audit trails for substitutions
 */

async function executeQuery(query: string, description: string): Promise<void> {
  try {
    console.log(`🔄 ${description}...`);
    const { error } = await supabase.rpc('execute_sql', { sql_query: query });
    
    if (error) {
      console.error(`❌ Error ${description.toLowerCase()}:`, error);
      throw error;
    }
    
    console.log(`✅ ${description} completed successfully`);
  } catch (error) {
    console.error(`❌ Failed to ${description.toLowerCase()}:`, error);
    throw error;
  }
}

async function enhanceLeaveManagementSchema() {
  try {
    console.log("🚀 Starting Enhanced Leave Management System Schema Migration...");
    
    // Step 1: Add new columns to leave_requests table
    console.log("📋 Step 1: Enhancing leave_requests table...");
    await executeQuery(`
      -- Add new columns for enhanced leave management
      ALTER TABLE leave_requests 
      ADD COLUMN IF NOT EXISTS affected_classes JSONB DEFAULT '[]'::jsonb,
      ADD COLUMN IF NOT EXISTS substitution_status TEXT DEFAULT 'pending' CHECK (substitution_status IN ('pending', 'assigned', 'approved', 'rejected')),
      ADD COLUMN IF NOT EXISTS total_classes_affected INTEGER DEFAULT 0;
      
      -- Add indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_leave_requests_substitution_status ON leave_requests(substitution_status);
      CREATE INDEX IF NOT EXISTS idx_leave_requests_affected_classes ON leave_requests USING GIN (affected_classes);
      CREATE INDEX IF NOT EXISTS idx_leave_requests_date_range ON leave_requests(start_date, end_date);
    `, "Adding enhanced fields to leave_requests table");

    // Step 2: Create leave_substitutions table for detailed tracking
    console.log("📋 Step 2: Creating leave_substitutions table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS leave_substitutions (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          leave_request_id UUID NOT NULL REFERENCES leave_requests(id) ON DELETE CASCADE,
          class_date DATE NOT NULL,
          day_of_week TEXT NOT NULL,
          time_slot TEXT NOT NULL,
          period_number INTEGER,
          subject_code TEXT NOT NULL,
          subject_name TEXT NOT NULL,
          subject_type TEXT NOT NULL,
          semester TEXT NOT NULL,
          section TEXT NOT NULL,
          department TEXT NOT NULL,
          room_number TEXT,
          batch_name TEXT,
          original_faculty_id UUID NOT NULL REFERENCES employee_details(id),
          substitute_faculty_id UUID REFERENCES employee_details(id),
          substitution_notes TEXT,
          substitution_status TEXT DEFAULT 'pending' CHECK (substitution_status IN ('pending', 'assigned', 'confirmed', 'completed', 'cancelled')),
          attendance_marked BOOLEAN DEFAULT FALSE,
          attendance_marked_by UUID REFERENCES employee_details(id),
          attendance_marked_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Add indexes for performance
      CREATE INDEX IF NOT EXISTS idx_leave_substitutions_leave_request ON leave_substitutions(leave_request_id);
      CREATE INDEX IF NOT EXISTS idx_leave_substitutions_original_faculty ON leave_substitutions(original_faculty_id);
      CREATE INDEX IF NOT EXISTS idx_leave_substitutions_substitute_faculty ON leave_substitutions(substitute_faculty_id);
      CREATE INDEX IF NOT EXISTS idx_leave_substitutions_class_date ON leave_substitutions(class_date);
      CREATE INDEX IF NOT EXISTS idx_leave_substitutions_time_slot ON leave_substitutions(time_slot);
      CREATE INDEX IF NOT EXISTS idx_leave_substitutions_subject ON leave_substitutions(subject_code, semester, section);
      CREATE INDEX IF NOT EXISTS idx_leave_substitutions_status ON leave_substitutions(substitution_status);
    `, "Creating leave_substitutions table");

    // Step 3: Create substitution_audit table for tracking changes
    console.log("📋 Step 3: Creating substitution_audit table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS substitution_audit (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          leave_request_id UUID NOT NULL REFERENCES leave_requests(id) ON DELETE CASCADE,
          substitution_id UUID REFERENCES leave_substitutions(id) ON DELETE CASCADE,
          action_type TEXT NOT NULL CHECK (action_type IN ('created', 'assigned', 'updated', 'confirmed', 'completed', 'cancelled')),
          performed_by UUID NOT NULL REFERENCES employee_details(id),
          performed_by_role TEXT NOT NULL,
          previous_values JSONB,
          new_values JSONB,
          notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Add indexes for audit queries
      CREATE INDEX IF NOT EXISTS idx_substitution_audit_leave_request ON substitution_audit(leave_request_id);
      CREATE INDEX IF NOT EXISTS idx_substitution_audit_substitution ON substitution_audit(substitution_id);
      CREATE INDEX IF NOT EXISTS idx_substitution_audit_performed_by ON substitution_audit(performed_by);
      CREATE INDEX IF NOT EXISTS idx_substitution_audit_action_type ON substitution_audit(action_type);
      CREATE INDEX IF NOT EXISTS idx_substitution_audit_created_at ON substitution_audit(created_at);
    `, "Creating substitution_audit table");

    // Step 4: Create faculty_availability_cache table for performance
    console.log("📋 Step 4: Creating faculty_availability_cache table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS faculty_availability_cache (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          faculty_id UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
          date DATE NOT NULL,
          day_of_week TEXT NOT NULL,
          time_slot TEXT NOT NULL,
          availability_status TEXT NOT NULL CHECK (availability_status IN ('available', 'occupied', 'on_leave')),
          conflict_details JSONB,
          subject_expertise TEXT[],
          last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(faculty_id, date, time_slot)
      );

      -- Add indexes for fast availability lookups
      CREATE INDEX IF NOT EXISTS idx_faculty_availability_cache_faculty ON faculty_availability_cache(faculty_id);
      CREATE INDEX IF NOT EXISTS idx_faculty_availability_cache_date ON faculty_availability_cache(date);
      CREATE INDEX IF NOT EXISTS idx_faculty_availability_cache_time_slot ON faculty_availability_cache(time_slot);
      CREATE INDEX IF NOT EXISTS idx_faculty_availability_cache_status ON faculty_availability_cache(availability_status);
      CREATE INDEX IF NOT EXISTS idx_faculty_availability_cache_lookup ON faculty_availability_cache(date, time_slot, availability_status);
    `, "Creating faculty_availability_cache table");

    // Step 5: Create triggers for automatic audit logging
    console.log("📋 Step 5: Creating audit triggers...");
    await executeQuery(`
      -- Function to log substitution changes
      CREATE OR REPLACE FUNCTION log_substitution_changes()
      RETURNS TRIGGER AS $$
      BEGIN
          IF TG_OP = 'INSERT' THEN
              INSERT INTO substitution_audit (
                  leave_request_id,
                  substitution_id,
                  action_type,
                  performed_by,
                  performed_by_role,
                  new_values
              ) VALUES (
                  NEW.leave_request_id,
                  NEW.id,
                  'created',
                  COALESCE(NEW.substitute_faculty_id, NEW.original_faculty_id),
                  'system',
                  to_jsonb(NEW)
              );
              RETURN NEW;
          ELSIF TG_OP = 'UPDATE' THEN
              INSERT INTO substitution_audit (
                  leave_request_id,
                  substitution_id,
                  action_type,
                  performed_by,
                  performed_by_role,
                  previous_values,
                  new_values
              ) VALUES (
                  NEW.leave_request_id,
                  NEW.id,
                  CASE 
                      WHEN OLD.substitution_status != NEW.substitution_status THEN NEW.substitution_status
                      ELSE 'updated'
                  END,
                  COALESCE(NEW.substitute_faculty_id, NEW.original_faculty_id),
                  'system',
                  to_jsonb(OLD),
                  to_jsonb(NEW)
              );
              RETURN NEW;
          END IF;
          RETURN NULL;
      END;
      $$ LANGUAGE plpgsql;

      -- Create trigger for leave_substitutions
      DROP TRIGGER IF EXISTS trigger_log_substitution_changes ON leave_substitutions;
      CREATE TRIGGER trigger_log_substitution_changes
          AFTER INSERT OR UPDATE ON leave_substitutions
          FOR EACH ROW EXECUTE FUNCTION log_substitution_changes();
    `, "Creating audit triggers");

    // Step 6: Create helper functions for substitution management
    console.log("📋 Step 6: Creating helper functions...");
    await executeQuery(`
      -- Function to get faculty availability for a specific time slot
      CREATE OR REPLACE FUNCTION get_faculty_availability(
          p_department TEXT,
          p_date DATE,
          p_time_slot TEXT
      )
      RETURNS TABLE (
          faculty_id UUID,
          full_name TEXT,
          availability_status TEXT,
          conflict_details TEXT
      ) AS $$
      BEGIN
          RETURN QUERY
          SELECT 
              ed.id,
              ed.full_name,
              CASE 
                  WHEN lr.id IS NOT NULL THEN 'on_leave'
                  WHEN ts.faculty_id IS NOT NULL THEN 'occupied'
                  ELSE 'available'
              END as availability_status,
              CASE 
                  WHEN lr.id IS NOT NULL THEN 'On approved leave'
                  WHEN ts.faculty_id IS NOT NULL THEN 
                      'Teaching ' || ts.subject_code || ' to ' || ts.semester || '-' || ts.section
                  ELSE NULL
              END as conflict_details
          FROM employee_details ed
          LEFT JOIN leave_requests lr ON (
              ed.id = lr.faculty_id 
              AND lr.status = 'approved'
              AND p_date BETWEEN lr.start_date::date AND lr.end_date::date
          )
          LEFT JOIN timetable_slots ts ON (
              ed.id = ts.faculty_id 
              AND ts.day = to_char(p_date, 'Day')
              AND ts.time_slot = p_time_slot
          )
          WHERE ed.department ILIKE p_department
          AND 'faculty' = ANY(ed.roles)
          ORDER BY 
              CASE 
                  WHEN lr.id IS NOT NULL THEN 3
                  WHEN ts.faculty_id IS NOT NULL THEN 2
                  ELSE 1
              END,
              ed.full_name;
      END;
      $$ LANGUAGE plpgsql;

      -- Function to update substitution status
      CREATE OR REPLACE FUNCTION update_substitution_status(
          p_substitution_id UUID,
          p_new_status TEXT,
          p_updated_by UUID,
          p_notes TEXT DEFAULT NULL
      )
      RETURNS BOOLEAN AS $$
      BEGIN
          UPDATE leave_substitutions 
          SET 
              substitution_status = p_new_status,
              updated_at = NOW(),
              substitution_notes = COALESCE(p_notes, substitution_notes)
          WHERE id = p_substitution_id;
          
          -- Log the status change
          INSERT INTO substitution_audit (
              substitution_id,
              action_type,
              performed_by,
              performed_by_role,
              notes
          ) VALUES (
              p_substitution_id,
              p_new_status,
              p_updated_by,
              'manual',
              p_notes
          );
          
          RETURN FOUND;
      END;
      $$ LANGUAGE plpgsql;
    `, "Creating helper functions");

    // Step 7: Add RLS policies for security
    console.log("📋 Step 7: Setting up Row Level Security...");
    await executeQuery(`
      -- Enable RLS on new tables
      ALTER TABLE leave_substitutions ENABLE ROW LEVEL SECURITY;
      ALTER TABLE substitution_audit ENABLE ROW LEVEL SECURITY;
      ALTER TABLE faculty_availability_cache ENABLE ROW LEVEL SECURITY;

      -- RLS policies for leave_substitutions
      CREATE POLICY "Faculty can view their own substitutions" ON leave_substitutions
          FOR SELECT USING (
              original_faculty_id = auth.uid() OR 
              substitute_faculty_id = auth.uid()
          );

      CREATE POLICY "HOD can view department substitutions" ON leave_substitutions
          FOR SELECT USING (
              EXISTS (
                  SELECT 1 FROM employee_details 
                  WHERE id = auth.uid() 
                  AND 'hod' = ANY(roles)
                  AND department = leave_substitutions.department
              )
          );

      CREATE POLICY "Faculty can update their substitutions" ON leave_substitutions
          FOR UPDATE USING (
              substitute_faculty_id = auth.uid() OR
              EXISTS (
                  SELECT 1 FROM employee_details 
                  WHERE id = auth.uid() 
                  AND ('hod' = ANY(roles) OR 'admin' = ANY(roles))
              )
          );

      -- RLS policies for substitution_audit
      CREATE POLICY "Users can view audit logs for their actions" ON substitution_audit
          FOR SELECT USING (
              performed_by = auth.uid() OR
              EXISTS (
                  SELECT 1 FROM employee_details 
                  WHERE id = auth.uid() 
                  AND ('hod' = ANY(roles) OR 'admin' = ANY(roles))
              )
          );

      -- RLS policies for faculty_availability_cache
      CREATE POLICY "Faculty can view availability cache" ON faculty_availability_cache
          FOR SELECT USING (
              faculty_id = auth.uid() OR
              EXISTS (
                  SELECT 1 FROM employee_details 
                  WHERE id = auth.uid() 
                  AND ('faculty' = ANY(roles) OR 'hod' = ANY(roles) OR 'admin' = ANY(roles))
              )
          );
    `, "Setting up Row Level Security policies");

    console.log("✅ Enhanced Leave Management System Schema Migration completed successfully!");
    console.log("");
    console.log("📊 Summary of changes:");
    console.log("  • Enhanced leave_requests table with substitution fields");
    console.log("  • Created leave_substitutions table for detailed tracking");
    console.log("  • Created substitution_audit table for change history");
    console.log("  • Created faculty_availability_cache for performance");
    console.log("  • Added audit triggers for automatic logging");
    console.log("  • Created helper functions for substitution management");
    console.log("  • Implemented Row Level Security policies");
    console.log("");
    console.log("🎉 The Enhanced Leave Management System is now ready to use!");

  } catch (error) {
    console.error("❌ Schema migration failed:", error);
    throw error;
  }
}

// Execute the migration
if (require.main === module) {
  enhanceLeaveManagementSchema()
    .then(() => {
      console.log("🎉 Migration completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Migration failed:", error);
      process.exit(1);
    });
}

export { enhanceLeaveManagementSchema };
