/**
 * Test script for StudentProgressCard component enhancement
 * Tests the enhanced subject name mapping in the component
 */

// Mock the component's enhanced subject name mapping function
function getEnhancedSubjectName(subjectCode, originalSubjectName) {
  // Lab subject mappings (same as in StudentProgressCard component)
  const LAB_SUBJECT_MAPPINGS = {
    'BCS403': 'DBMS Lab',    // DBMS Lab
    'BCS402': 'MC Lab',      // MC Lab
    'BCS401': 'ADA Lab',     // ADA Lab
    'BCS404': 'CN Lab',      // CN Lab
    'BCS405': 'Software Engineering Lab',  // SE Lab
    'BCSL403': 'DBMS Lab',   // DBMS Lab (alternative code)
    'BCSL402': 'MC Lab',     // MC Lab (alternative code)
    'BCSL401': 'ADA Lab',    // ADA Lab (alternative code)
    'BCSL404': 'CN Lab',     // CN Lab (alternative code)
    'BCSL405': 'Software Engineering Lab',  // SE Lab (alternative code)
  };

  // Detect batch suffix pattern
  const batchPattern = /^(.+)_([A-Z]\d+)$/;
  const match = subjectCode.match(batchPattern);
  const isLabBatch = !!match;

  // For lab batches (with batch suffixes like _A1, _B2), always use lab mappings
  if (isLabBatch) {
    const [, baseCode, batchSuffix] = match;
    const labName = LAB_SUBJECT_MAPPINGS[baseCode];
    if (labName) {
      return `${labName} (${batchSuffix})`;
    }
  }

  // For subjects with explicit _LAB suffix, use lab mappings
  if (subjectCode.includes('_LAB')) {
    const normalizedCode = subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
    const labName = LAB_SUBJECT_MAPPINGS[normalizedCode];
    if (labName) {
      return labName;
    }
  }

  // For regular subjects, only use lab mappings if:
  // 1. No original name provided OR original name is "Unknown Subject"
  // 2. AND the subject code suggests it's a lab (starts with BCSL or has lab indicators)
  if (!originalSubjectName || originalSubjectName === 'Unknown Subject') {
    const normalizedCode = subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');

    // Only apply lab mappings for codes that clearly indicate lab subjects
    const isLikelyLab = normalizedCode.startsWith('BCSL') ||
                       subjectCode.includes('LAB') ||
                       subjectCode.includes('_LAB') ||
                       isLabBatch;

    if (isLikelyLab) {
      const labName = LAB_SUBJECT_MAPPINGS[normalizedCode];
      if (labName) {
        return labName;
      }
    }
  }

  return originalSubjectName || 'Unknown Subject';
}

// Mock the component's combineSubjectData function
function combineSubjectData(attendance, iaMarks) {
  const subjectMap = new Map();

  // Add attendance data
  attendance.forEach(att => {
    const enhancedSubjectName = getEnhancedSubjectName(att.subject_code, att.subject_name);
    subjectMap.set(att.subject_code, {
      subject_code: att.subject_code,
      subject_name: enhancedSubjectName,
      attendance_percentage: att.percentage,
      total_classes: att.total_classes,
      attended_classes: att.attended_classes,
    });
  });

  // Add IA marks data
  iaMarks.forEach(ia => {
    const existing = subjectMap.get(ia.subject_code);
    const enhancedSubjectName = getEnhancedSubjectName(ia.subject_code, ia.subject_name);

    if (existing) {
      // Update existing entry, prefer enhanced name
      subjectMap.set(ia.subject_code, {
        ...existing,
        subject_name: enhancedSubjectName,
        ia1_marks: ia.ia1_marks,
        ia2_marks: ia.ia2_marks,
        ia3_marks: ia.ia3_marks,
        assignment_marks: ia.assignment_marks,
        lab_marks: ia.lab_marks,
        total_marks: ia.total_marks,
        average_marks: ia.average_marks,
        grade: ia.grade,
      });
    } else {
      // Create new entry for subjects with only IA data
      subjectMap.set(ia.subject_code, {
        subject_code: ia.subject_code,
        subject_name: enhancedSubjectName,
        attendance_percentage: 0,
        total_classes: 0,
        attended_classes: 0,
        ia1_marks: ia.ia1_marks,
        ia2_marks: ia.ia2_marks,
        ia3_marks: ia.ia3_marks,
        assignment_marks: ia.assignment_marks,
        lab_marks: ia.lab_marks,
        total_marks: ia.total_marks,
        average_marks: ia.average_marks,
        grade: ia.grade,
      });
    }
  });

  return Array.from(subjectMap.values()).sort((a, b) => a.subject_code.localeCompare(b.subject_code));
}

// Test cases
function runComponentTests() {
  console.log('🧪 Testing StudentProgressCard Component Enhancement\n');

  // Test 1: Component-level subject name enhancement
  console.log('📋 Test 1: Component Subject Name Enhancement');
  const enhancementTests = [
    { input: 'BCS402_A1', originalName: 'Unknown Subject', expected: 'MC Lab (A1)' },
    { input: 'BCS402_A1', originalName: null, expected: 'MC Lab (A1)' },
    { input: 'BCS402_A1', originalName: undefined, expected: 'MC Lab (A1)' },
    { input: 'BCS403_B2', originalName: 'Unknown Subject', expected: 'DBMS Lab (B2)' },
    { input: 'BCS405_A1', originalName: 'Unknown Subject', expected: 'Software Engineering Lab (A1)' },
    { input: 'BCS402', originalName: 'Unknown Subject', expected: 'Unknown Subject' }, // Theory subject should NOT map to lab
    { input: 'BCS401_LAB', originalName: 'Unknown Subject', expected: 'ADA Lab' }, // Explicit _LAB should map to lab
    { input: 'BCSL402', originalName: 'Unknown Subject', expected: 'MC Lab' }, // BCSL prefix should map to lab
    { input: 'BCS401', originalName: 'Algorithm Design and Analysis', expected: 'Algorithm Design and Analysis' },
  ];

  enhancementTests.forEach(test => {
    const result = getEnhancedSubjectName(test.input, test.originalName);
    const passed = result === test.expected;
    console.log(`  ${passed ? '✅' : '❌'} ${test.input} (${test.originalName || 'null'}) → ${result}`);
    if (!passed) {
      console.log(`    Expected: ${test.expected}`);
    }
  });

  // Test 2: Data combination with enhancement
  console.log('\n📋 Test 2: Data Combination with Enhancement');

  // Mock attendance data with batch suffixes
  const mockAttendance = [
    { subject_code: 'BCS402_A1', subject_name: 'Unknown Subject', percentage: 85, total_classes: 20, attended_classes: 17 },
    { subject_code: 'BCS403_B1', subject_name: 'Unknown Subject', percentage: 90, total_classes: 18, attended_classes: 16 },
    { subject_code: 'BCS401', subject_name: 'Algorithm Design and Analysis', percentage: 75, total_classes: 25, attended_classes: 19 },
  ];

  // Mock IA data
  const mockIA = [
    { subject_code: 'BCS402_A1', subject_name: 'Unknown Subject', ia1_marks: 20, ia2_marks: 22, total_marks: 42, average_marks: 21, grade: 'A' },
    { subject_code: 'BCS404_A2', subject_name: 'Unknown Subject', ia1_marks: 18, ia2_marks: 19, total_marks: 37, average_marks: 18.5, grade: 'B+' },
  ];

  const combinedData = combineSubjectData(mockAttendance, mockIA);

  console.log('  Combined data results:');
  combinedData.forEach(subject => {
    const expectedNames = {
      'BCS402_A1': 'MC Lab (A1)',
      'BCS403_B1': 'DBMS Lab (B1)',
      'BCS401': 'Algorithm Design and Analysis',
      'BCS404_A2': 'CN Lab (A2)'
    };

    const expected = expectedNames[subject.subject_code];
    const passed = subject.subject_name === expected;
    console.log(`    ${passed ? '✅' : '❌'} ${subject.subject_code}: "${subject.subject_name}"`);
    if (!passed) {
      console.log(`      Expected: "${expected}"`);
    }
  });

  console.log('\n🎉 Component enhancement test completed!');
}

// Run the tests
runComponentTests();
