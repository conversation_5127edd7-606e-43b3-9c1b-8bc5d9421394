import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { StudentService } from '@/services/StudentService';
import { SimpleStudentAuthService } from '@/services/SimpleStudentAuthService';
import { UserPlus, AlertCircle, CheckCircle, X, Shield, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface StudentAuthBulkUploadProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  facultyId?: string;
}

const StudentAuthBulkUpload: React.FC<StudentAuthBulkUploadProps> = ({
  isOpen,
  onClose,
  onSuccess,
  facultyId
}) => {
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedSemester, setSelectedSemester] = useState<string>('');
  const [selectedSection, setSelectedSection] = useState<string>('');
  const [selectedAcademicYear, setSelectedAcademicYear] = useState<string>('2024-2025');
  const [uploading, setUploading] = useState(false);
  const [clearing, setClearing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResults, setUploadResults] = useState<{
    success: any[];
    errors: { row: number; error: string }[];
  } | null>(null);
  const { toast } = useToast();

  const departments = [
    { value: 'CSE', label: 'Computer Science and Engineering' },
    { value: 'ECE', label: 'Electronics and Communication Engineering' },
    { value: 'EEE', label: 'Electrical and Electronics Engineering' },
    { value: 'MECH', label: 'Mechanical Engineering' },
    { value: 'CIVIL', label: 'Civil Engineering' },
    { value: 'ISE', label: 'Information Science and Engineering' }
  ];

  const semesters = ['1', '2', '3', '4', '5', '6', '7', '8'];
  const sections = ['A', 'B', 'C', 'D'];
  const academicYears = ['2024-2025', '2023-2024', '2022-2023'];

  const handleCreateAuthAccounts = async () => {
    if (!selectedDepartment || !selectedSemester || !selectedSection) {
      toast({
        title: 'Missing Information',
        description: 'Please select department, semester, and section',
        variant: 'destructive',
      });
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      setUploadProgress(25);

      const results = await StudentService.bulkCreateStudentAuthAccounts(
        selectedDepartment,
        selectedSemester,
        selectedSection,
        selectedAcademicYear,
        facultyId || 'system'
      );

      setUploadProgress(100);
      setUploadResults(results);

      if (results.success.length > 0) {
        toast({
          title: 'Authentication accounts created',
          description: `Successfully created ${results.success.length} student authentication accounts${
            results.errors.length > 0 ? ` with ${results.errors.length} errors` : ''
          }`,
        });
        onSuccess();
      }
    } catch (error) {
      toast({
        title: 'Creation failed',
        description: error instanceof Error ? error.message : 'Failed to create authentication accounts',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
    }
  };

  const handleClearAllAuthAccounts = async () => {
    if (!confirm('⚠️ WARNING: This will delete ALL existing student authentication accounts. Are you sure you want to continue? This action cannot be undone.')) {
      return;
    }

    setClearing(true);
    try {
      await SimpleStudentAuthService.clearAllAuthAccounts();
      toast({
        title: 'All auth accounts cleared',
        description: 'All student authentication accounts have been deleted successfully.',
      });
      setUploadResults(null);
    } catch (error) {
      toast({
        title: 'Clear failed',
        description: error instanceof Error ? error.message : 'Failed to clear authentication accounts',
        variant: 'destructive',
      });
    } finally {
      setClearing(false);
    }
  };

  const resetForm = () => {
    setSelectedDepartment('');
    setSelectedSemester('');
    setSelectedSection('');
    setUploadResults(null);
    setUploadProgress(0);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Create Student Authentication Accounts
          </DialogTitle>
          <DialogDescription>
            Create authentication accounts for students from existing class data.
            This will allow students to log in to the student portal.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Information Alert */}
          <Alert className="border-blue-200 bg-blue-50">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Important:</strong> This will create/update authentication accounts for all students in the selected class.
              Default password will be "Password@123" and students must change it on first login.
              <br />
              <strong>Fresh Semester:</strong> If students already have accounts, their passwords will be reset to default.
            </AlertDescription>
          </Alert>

          {/* Clear All Accounts Option */}
          <div className="p-4 border-2 border-red-300 rounded-lg bg-red-50">
            <div className="flex items-center gap-3 mb-3">
              <Trash2 className="h-6 w-6 text-red-600" />
              <div className="flex-1">
                <h3 className="font-semibold text-red-800">Fresh Semester Start</h3>
                <p className="text-sm text-red-700">Clear all existing authentication accounts before creating new ones</p>
                <p className="text-xs text-red-600 mt-1">⚠️ This will delete ALL student accounts permanently</p>
              </div>
              <Button
                variant="destructive"
                onClick={handleClearAllAuthAccounts}
                disabled={clearing || uploading}
                className="min-w-[140px]"
              >
                {clearing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Clearing...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear All Accounts
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Class Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                <SelectTrigger>
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept.value} value={dept.value}>
                      {dept.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="semester">Semester</Label>
              <Select value={selectedSemester} onValueChange={setSelectedSemester}>
                <SelectTrigger>
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  {semesters.map((sem) => (
                    <SelectItem key={sem} value={sem}>
                      Semester {sem}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="section">Section</Label>
              <Select value={selectedSection} onValueChange={setSelectedSection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  {sections.map((sec) => (
                    <SelectItem key={sec} value={sec}>
                      Section {sec}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="academic-year">Academic Year</Label>
              <Select value={selectedAcademicYear} onValueChange={setSelectedAcademicYear}>
                <SelectTrigger>
                  <SelectValue placeholder="Select academic year" />
                </SelectTrigger>
                <SelectContent>
                  {academicYears.map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-2">
              <Label>Creation Progress</Label>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {/* Upload Results */}
          {uploadResults && (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Badge variant="default" className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  {uploadResults.success.length} Successful
                </Badge>
                {uploadResults.errors.length > 0 && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <X className="h-3 w-3" />
                    {uploadResults.errors.length} Errors
                  </Badge>
                )}
              </div>

              {uploadResults.errors.length > 0 && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">Creation Errors:</p>
                      {uploadResults.errors.slice(0, 5).map((error, index) => (
                        <p key={index} className="text-sm">
                          Row {error.row}: {error.error}
                        </p>
                      ))}
                      {uploadResults.errors.length > 5 && (
                        <p className="text-sm text-muted-foreground">
                          ... and {uploadResults.errors.length - 5} more errors
                        </p>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={resetForm} disabled={uploading || clearing}>
            Reset
          </Button>
          <Button variant="outline" onClick={onClose} disabled={uploading || clearing}>
            Close
          </Button>
          <Button
            onClick={handleCreateAuthAccounts}
            disabled={!selectedDepartment || !selectedSemester || !selectedSection || uploading || clearing}
            className="min-w-[150px]"
          >
            {uploading ? 'Creating Accounts...' : 'Create Auth Accounts'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StudentAuthBulkUpload;
