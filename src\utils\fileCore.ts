
/**
 * Core file reading utilities
 */

/**
 * Reads a file as text
 */
export const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target?.result as string);
    reader.onerror = (e) => reject(e);
    reader.readAsText(file);
  });
};

/**
 * Reads a file as ArrayBuffer
 */
export const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target?.result as ArrayBuffer);
    reader.onerror = (e) => reject(e);
    reader.readAsArrayBuffer(file);
  });
};

/**
 * Determines file type based on file extension
 */
export const getFileType = (fileName: string): 'excel' | 'json' | 'csv' | null => {
  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    return 'excel';
  } else if (fileName.endsWith('.json')) {
    return 'json';
  } else if (fileName.endsWith('.csv')) {
    return 'csv';
  }
  return null;
};
