
// src/services/mapping/ManualMappingService.ts
import { supabase } from "@/integrations/supabase/client";
import { FacultyAvailabilityService } from "@/services/FacultyAvailabilityService";
import { ManualMappingInput, LabSlotInput } from "./types";
import { MappingType } from "@/stores/SubjectMappingStore";

export class ManualMappingService {
  /**
   * Create a mapping using a simplified approach that focuses only on the essential relationship
   * between employee_details and subject_faculty_mappings
   */
  static async createMappingSimplified(
    mapping: ManualMappingInput,
    slots?: LabSlotInput[]
  ): Promise<{ mappingId: string }> {
    try {
      console.log("Attempting to create mapping with simplified approach");

      // Ensure faculty_2_id is properly handled (null instead of undefined)
      const faculty2Id = mapping.faculty_2_id === undefined ? null : mapping.faculty_2_id;

      // Create a simplified mapping object with all required fields
      const simplifiedMapping = {
        academic_year: mapping.academic_year,
        department: mapping.department,
        semester: mapping.semester,
        section: mapping.section,
        subject_id: mapping.subject_id, // Include subject_id as it's required
        subject_code: mapping.subject_code,
        subject_name: mapping.subject_name,
        subject_type: mapping.subject_type,
        faculty_1_id: mapping.faculty_1_id,
        faculty_2_id: faculty2Id, // Use the processed faculty2Id
        hours_per_week: mapping.hours_per_week || 0,
        classroom: mapping.classroom || "Not specified",
        slots_per_week: mapping.slots_per_week
      };

      console.log("Inserting mapping with data:", JSON.stringify(simplifiedMapping));

      // Try using the raw SQL insert approach first
      try {
        return await this.createMappingWithRawSQL(simplifiedMapping);
      } catch (rawSqlError) {
        console.error("Raw SQL insert failed:", rawSqlError);

        // Fall back to the original approach if raw SQL fails
        console.log("Falling back to standard insert approach");

        // Insert without using select() to avoid aggregate function issues
        const { error: insertError } = await supabase
          .from("subject_faculty_mappings")
          .insert([simplifiedMapping]);

        if (insertError) {
          console.error("Error with simplified insert:", insertError);
          throw insertError;
        }

        console.log("Insert succeeded, trying to fetch the mapping ID");

        // Try to fetch the mapping we just created using a combination of fields that should be unique
        const { data: fetchedData, error: fetchError } = await supabase
          .from("subject_faculty_mappings")
          .select("id")
          .eq("academic_year", mapping.academic_year)
          .eq("department", mapping.department)
          .eq("semester", mapping.semester)
          .eq("section", mapping.section)
          .eq("subject_code", mapping.subject_code)
          .eq("faculty_1_id", mapping.faculty_1_id)
          .order("id", { ascending: false })
          .limit(1);

        if (fetchError) {
          console.error("Error fetching mapping ID:", fetchError);
          // Don't throw here, just return unknown ID since insert succeeded
          return { mappingId: "unknown" };
        }

        if (!fetchedData || fetchedData.length === 0 || !fetchedData[0].id) {
          console.warn("Failed to fetch mapping ID after successful insert");
          return { mappingId: "unknown" };
        }

        const mappingId = fetchedData[0].id;
        console.log("Successfully created mapping with ID:", mappingId);

        // For lab slots, we'll handle them separately if needed
        // This simplifies the relationship between tables
        if (slots?.length && mappingId) {
          console.log("Lab slots provided, but skipping insertion to simplify relationships");
          // We're intentionally not inserting lab slots to simplify the database structure
        }

        return { mappingId };
      }
    } catch (error) {
      console.error("Error in createMappingSimplified:", error);
      throw error;
    }
  }

  /**
   * Create a mapping using raw SQL to bypass potential issues with the Supabase client
   * This approach uses a direct SQL insert statement to avoid nested aggregate function errors
   */
  static async createMappingWithRawSQL(mapping: any): Promise<{ mappingId: string }> {
    try {
      console.log("Attempting to create mapping with raw SQL approach");

      // Prepare the SQL parameters
      const params = {
        academic_year: mapping.academic_year,
        department: mapping.department,
        semester: mapping.semester,
        section: mapping.section,
        subject_id: mapping.subject_id,
        subject_code: mapping.subject_code,
        subject_name: mapping.subject_name,
        subject_type: mapping.subject_type,
        faculty_1_id: mapping.faculty_1_id,
        faculty_2_id: mapping.faculty_2_id,
        hours_per_week: mapping.hours_per_week || 0,
        classroom: mapping.classroom || "Not specified",
        slots_per_week: mapping.slots_per_week
      };

      // Execute a raw SQL query using Supabase's rpc function
      const { data, error } = await supabase.rpc('insert_subject_faculty_mapping', params);

      if (error) {
        console.error("Error with raw SQL insert:", error);

        // If the stored procedure doesn't exist, try a direct SQL query
        console.log("Stored procedure not found, trying direct SQL query");

        // Use a direct SQL query as a fallback
        const { data: directData, error: directError } = await supabase.rpc('execute_sql', {
          sql_query: `
            INSERT INTO subject_faculty_mappings (
              academic_year, department, semester, section, subject_id,
              subject_code, subject_name, subject_type, faculty_1_id,
              faculty_2_id, hours_per_week, classroom, slots_per_week
            ) VALUES (
              '${params.academic_year}', '${params.department}', '${params.semester}',
              '${params.section}', '${params.subject_id}', '${params.subject_code}',
              '${params.subject_name}', '${params.subject_type}', '${params.faculty_1_id}',
              ${params.faculty_2_id ? `'${params.faculty_2_id}'` : 'NULL'},
              ${params.hours_per_week}, '${params.classroom}',
              ${params.slots_per_week ? params.slots_per_week : 'NULL'}
            ) RETURNING id;
          `
        });

        if (directError) {
          console.error("Error with direct SQL query:", directError);
          throw directError;
        }

        if (!directData || !directData.id) {
          console.warn("Direct SQL query succeeded but no ID returned");
          return { mappingId: "unknown" };
        }

        console.log("Successfully created mapping with direct SQL, ID:", directData.id);
        return { mappingId: directData.id };
      }

      if (!data || !data.id) {
        console.warn("Raw SQL insert succeeded but no ID returned");
        return { mappingId: "unknown" };
      }

      console.log("Successfully created mapping with raw SQL, ID:", data.id);
      return { mappingId: data.id };
    } catch (error) {
      console.error("Error in createMappingWithRawSQL:", error);
      throw error;
    }
  }

  static async createMapping(
    mapping: ManualMappingInput,
    slots?: LabSlotInput[]
  ): Promise<{ mappingId: string }> {
    try {
      console.log("Attempting to create mapping with standard approach");

      // Try the raw SQL approach first
      try {
        console.log("Using raw SQL approach to avoid aggregate function issues");
        return await this.createMappingWithRawSQL(mapping);
      } catch (rawSqlError) {
        console.error("Raw SQL approach failed:", rawSqlError);

        // Fall back to the simplified approach if raw SQL fails
        console.log("Falling back to simplified approach");
        return await this.createMappingSimplified(mapping, slots);
      }
    } catch (error) {
      console.error("All standard approaches failed:", error);

      // As a last resort, try a direct fetch approach
      try {
        console.log("Trying last resort direct fetch approach");

        // Make a direct fetch request to the Supabase REST API
        const response = await fetch("https://milmyotuougemocvieof.supabase.co/rest/v1/subject_faculty_mappings", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3ODQ1NDksImV4cCI6MjA2MjM2MDU0OX0.k0-iFFSCDOyyPMwYTE4PfX8YyRgjMswpf5duIrxc2mI",
            "Prefer": "return=minimal"
          },
          body: JSON.stringify({
            academic_year: mapping.academic_year,
            department: mapping.department,
            semester: mapping.semester,
            section: mapping.section,
            subject_id: mapping.subject_id,
            subject_code: mapping.subject_code,
            subject_name: mapping.subject_name,
            subject_type: mapping.subject_type,
            faculty_1_id: mapping.faculty_1_id,
            faculty_2_id: mapping.faculty_2_id,
            hours_per_week: mapping.hours_per_week || 0,
            classroom: mapping.classroom || "Not specified",
            slots_per_week: mapping.slots_per_week
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Direct fetch failed:", errorText);
          throw new Error(`Direct fetch failed: ${errorText}`);
        }

        console.log("Direct fetch succeeded, trying to get the mapping ID");

        // Try to fetch the mapping we just created
        const { data: fetchedData, error: fetchError } = await supabase
          .from("subject_faculty_mappings")
          .select("id")
          .eq("academic_year", mapping.academic_year)
          .eq("department", mapping.department)
          .eq("semester", mapping.semester)
          .eq("section", mapping.section)
          .eq("subject_code", mapping.subject_code)
          .eq("faculty_1_id", mapping.faculty_1_id)
          .order("created_at", { ascending: false })
          .limit(1);

        if (fetchError) {
          console.error("Error fetching mapping ID after direct fetch:", fetchError);
          return { mappingId: "unknown" };
        }

        if (!fetchedData || fetchedData.length === 0) {
          console.warn("Failed to fetch mapping ID after direct fetch");
          return { mappingId: "unknown" };
        }

        const mappingId = fetchedData[0].id;
        console.log("Successfully retrieved mapping ID after direct fetch:", mappingId);
        return { mappingId };
      } catch (lastResortError) {
        console.error("Last resort approach failed:", lastResortError);

        // If all else fails, return unknown ID
        console.warn("All approaches failed, returning unknown ID");
        return { mappingId: "unknown" };
      }
    }
  }

  static async updateMapping(
    id: string,
    updates: Partial<ManualMappingInput>
  ): Promise<void> {
    try {
      console.log(`Updating mapping ${id} with:`, updates);

      // Ensure faculty_2_id is properly handled
      if (updates.faculty_2_id === undefined) {
        // Don't include faculty_2_id in the update if it's not provided
        delete updates.faculty_2_id;
      }

      // Try using the stored procedure first
      try {
        console.log("Using stored procedure for update");

        // Only pass faculty IDs to the stored procedure
        const { error: rpcError } = await supabase.rpc('update_subject_faculty_mapping', {
          mapping_id: id,
          faculty_1_id: updates.faculty_1_id,
          faculty_2_id: updates.faculty_2_id
        });

        if (rpcError) {
          console.error("Error updating with stored procedure:", rpcError);
          throw rpcError;
        }

        console.log(`Successfully updated mapping ${id} with stored procedure`);
        return;
      } catch (rpcError) {
        console.error("Stored procedure update failed:", rpcError);

        // Fall back to standard update
        console.log("Falling back to standard update");

        const { error } = await supabase
          .from("subject_faculty_mappings")
          .update(updates)
          .eq("id", id);

        if (error) {
          console.error("Error updating mapping:", error);

          // If standard update fails, try direct fetch
          if (error.code === '42803') {
            console.log("Detected aggregate function error, trying direct fetch");

            // Make a direct fetch request to the Supabase REST API
            const response = await fetch(`https://milmyotuougemocvieof.supabase.co/rest/v1/subject_faculty_mappings?id=eq.${id}`, {
              method: "PATCH",
              headers: {
                "Content-Type": "application/json",
                "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3ODQ1NDksImV4cCI6MjA2MjM2MDU0OX0.k0-iFFSCDOyyPMwYTE4PfX8YyRgjMswpf5duIrxc2mI",
                "Prefer": "return=minimal"
              },
              body: JSON.stringify(updates)
            });

            if (!response.ok) {
              const errorText = await response.text();
              console.error("Direct fetch update failed:", errorText);
              throw new Error(`Direct fetch update failed: ${errorText}`);
            }

            console.log(`Successfully updated mapping ${id} with direct fetch`);
            return;
          }

          throw error;
        }

        console.log(`Successfully updated mapping ${id}`);
      }
    } catch (error) {
      console.error(`Failed to update mapping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a mapping using a simplified approach that focuses only on the essential relationship
   * between employee_details and subject_faculty_mappings
   */
  static async deleteWithSimplifiedApproach(id: string): Promise<void> {
    try {
      console.log("Attempting to delete mapping with simplified approach for ID:", id);

      // Skip deleting related records to simplify the relationship
      // We're focusing only on the relationship between employee_details and subject_faculty_mappings

      // Try to mark the mapping as deleted first (soft delete)
      // This is often more reliable than trying to delete the record
      const randomSuffix = Math.random().toString(36).substring(2, 7);
      const deletedCode = `DELETED_${randomSuffix}`;

      const { error: markDeletedError } = await supabase
        .from("subject_faculty_mappings")
        .update({ subject_code: deletedCode })
        .eq("id", id);

      if (markDeletedError) {
        console.error("Error marking mapping as deleted:", markDeletedError);

        // If marking as deleted fails, try a direct delete
        console.log("Trying direct delete as fallback");

        const { error: deleteError } = await supabase
          .from("subject_faculty_mappings")
          .delete()
          .eq("id", id);

        if (deleteError) {
          console.error("Error with direct delete:", deleteError);
          throw deleteError;
        } else {
          console.log("Successfully deleted mapping with direct delete");
        }
      } else {
        console.log("Successfully marked mapping as deleted (soft delete)");
      }
    } catch (error) {
      console.error("Error in deleteWithSimplifiedApproach:", error);
      throw error;
    }
  }

  static async deleteMapping(
    id: string
  ): Promise<void> {
    try {
      console.log("Starting deletion process for mapping ID:", id);

      // First, get the mapping details to know which faculty members are affected
      const { data: mapping, error: mappingError } = await supabase
        .from("subject_faculty_mappings")
        .select("*")
        .eq("id", id)
        .single();

      if (mappingError) {
        console.error("Error fetching mapping details:", mappingError);
        // Continue with deletion even if we can't get mapping details
      }

      // Check if this is a lab subject
      const isLabSubject = mapping && (mapping.subject_type === 'laboratory' || mapping.subject_type === 'lab');

      // If it's a lab subject, get the lab time slots before deleting
      let labTimeSlots: any[] = [];
      if (isLabSubject) {
        console.log(`Fetching lab time slots for mapping ${id}`);

        const { data: labSlots, error: labSlotsError } = await supabase
          .from("lab_time_slots")
          .select("*")
          .eq("mapping_id", id);

        if (labSlotsError) {
          console.error("Error fetching lab slots:", labSlotsError);
        } else if (labSlots) {
          labTimeSlots = labSlots;
        }

        console.log(`Found ${labTimeSlots.length} lab time slots for mapping ${id}`);
      }

      // Try using the stored procedure first
      try {
        console.log("Using stored procedure for deletion");

        const { error: rpcError } = await supabase.rpc('delete_subject_faculty_mapping', {
          mapping_id: id
        });

        if (rpcError) {
          console.error("Error deleting with stored procedure:", rpcError);
          throw rpcError;
        }

        console.log(`Successfully deleted mapping ${id} with stored procedure`);

        // If it was a lab subject and we found lab time slots, restore faculty availability
        if (isLabSubject && labTimeSlots.length > 0 && mapping) {
          await this.restoreFacultyAvailability(mapping, labTimeSlots);
        }

        return;
      } catch (rpcError) {
        console.error("Stored procedure deletion failed:", rpcError);

        // Fall back to simplified approach
        console.log("Falling back to simplified approach");

        try {
          await this.deleteWithSimplifiedApproach(id);

          // If it was a lab subject and we found lab time slots, restore faculty availability
          if (isLabSubject && labTimeSlots.length > 0 && mapping) {
            await this.restoreFacultyAvailability(mapping, labTimeSlots);
          }

          return;
        } catch (simplifiedError) {
          console.error("Simplified approach failed:", simplifiedError);

          // If the simplified approach fails, try a direct delete as a fallback
          console.log("Falling back to direct delete");

          const { error: deleteError } = await supabase
            .from("subject_faculty_mappings")
            .delete()
            .eq("id", id);

          if (deleteError) {
            console.error("Error with direct delete:", deleteError);

            // If we get the nested aggregate error, try a direct fetch delete
            if (deleteError.code === '42803') {
              console.log("Detected aggregate function error (42803). Trying direct fetch delete...");

              // Make a direct fetch request to the Supabase REST API
              const response = await fetch(`https://milmyotuougemocvieof.supabase.co/rest/v1/subject_faculty_mappings?id=eq.${id}`, {
                method: "DELETE",
                headers: {
                  "Content-Type": "application/json",
                  "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3ODQ1NDksImV4cCI6MjA2MjM2MDU0OX0.k0-iFFSCDOyyPMwYTE4PfX8YyRgjMswpf5duIrxc2mI",
                  "Prefer": "return=minimal"
                }
              });

              if (!response.ok) {
                const errorText = await response.text();
                console.error("Direct fetch delete failed:", errorText);

                // If direct fetch delete fails, try soft delete
                console.log("Direct fetch delete failed, trying soft delete");

                const randomSuffix = Math.random().toString(36).substring(2, 7);
                const { error: softDeleteError } = await supabase
                  .from("subject_faculty_mappings")
                  .update({
                    subject_code: `DELETED_${randomSuffix}`
                  })
                  .eq("id", id);

                if (softDeleteError) {
                  console.error("Error with soft delete:", softDeleteError);
                  throw softDeleteError;
                } else {
                  console.log("Successfully marked mapping as deleted (soft delete)");

                  // If it was a lab subject and we found lab time slots, restore faculty availability
                  if (isLabSubject && labTimeSlots.length > 0 && mapping) {
                    await this.restoreFacultyAvailability(mapping, labTimeSlots);
                  }

                  return;
                }
              }

              console.log("Successfully deleted mapping with direct fetch");

              // If it was a lab subject and we found lab time slots, restore faculty availability
              if (isLabSubject && labTimeSlots.length > 0 && mapping) {
                await this.restoreFacultyAvailability(mapping, labTimeSlots);
              }

              return;
            } else {
              throw deleteError;
            }
          } else {
            console.log("Successfully deleted mapping with direct delete");

            // If it was a lab subject and we found lab time slots, restore faculty availability
            if (isLabSubject && labTimeSlots.length > 0 && mapping) {
              await this.restoreFacultyAvailability(mapping, labTimeSlots);
            }

            return;
          }
        }
      }
    } catch (error) {
      console.error("All approaches to delete mapping failed:", error);

      // As a last resort, try a very basic soft delete
      try {
        console.log("Trying last resort basic soft delete");

        // Generate a unique deleted code
        const randomSuffix = Math.random().toString(36).substring(2, 7);
        const deletedCode = `DELETED_${randomSuffix}`;

        // Try to update with minimal fields using direct fetch
        const response = await fetch(`https://milmyotuougemocvieof.supabase.co/rest/v1/subject_faculty_mappings?id=eq.${id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3ODQ1NDksImV4cCI6MjA2MjM2MDU0OX0.k0-iFFSCDOyyPMwYTE4PfX8YyRgjMswpf5duIrxc2mI",
            "Prefer": "return=minimal"
          },
          body: JSON.stringify({ subject_code: deletedCode })
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Last resort soft delete failed:", errorText);
          throw new Error(`Last resort soft delete failed: ${errorText}`);
        }

        console.log("Successfully marked mapping as deleted with last resort approach");
        return;
      } catch (lastResortError) {
        console.error("Last resort approach failed:", lastResortError);

        // If all else fails, just log the error and return
        console.warn("All deletion approaches failed, but continuing execution");
        return;
      }
    }
  }

  /**
   * Helper method to restore faculty availability after deleting a lab mapping
   */
  private static async restoreFacultyAvailability(mapping: any, labTimeSlots: any[]): Promise<void> {
    try {
      console.log(`Restoring faculty availability for ${labTimeSlots.length} lab time slots`);

      // Process each lab time slot to restore faculty availability
      for (const slot of labTimeSlots) {
        try {
          await FacultyAvailabilityService.restoreFacultyAvailabilityFromLabSlots(
            mapping.faculty_1_id,
            mapping.faculty_2_id,
            slot.day,
            slot.time_of_day
          );
          console.log(`Restored faculty availability for lab slot on ${slot.day} at ${slot.time_of_day}`);
        } catch (availabilityError) {
          console.error("Error restoring faculty availability:", availabilityError);
          // Continue with the next slot even if this one fails
        }
      }
    } catch (error) {
      console.error("Error in restoreFacultyAvailability:", error);
      // Don't throw the error to avoid interrupting the deletion process
    }
  }

  static async createSubjectMapping(mapping: MappingType): Promise<{ mappingId: string }> {
    console.log("Creating subject mapping:", mapping);

    // Get the faculty_2_id from the secondary faculty
    let faculty2Id = null;

    // For lab subjects, faculty2Id is required
    if (mapping.subject.type === 'lab' || mapping.subject.type === 'laboratory') {
      // Use the faculty2Id from the mapping object
      faculty2Id = mapping.faculty2Id || null;
      console.log("Lab subject detected, faculty2Id:", faculty2Id);
    }

    // Transform from UI model to database model
    const dbMapping: ManualMappingInput = {
      academic_year: mapping.academicYear,
      department: mapping.department,
      semester: mapping.semester,
      section: mapping.section,
      subject_id: mapping.subject.id,
      subject_code: mapping.subject.code,
      subject_name: mapping.subject.name,
      subject_type: mapping.subject.type === "lab" ? "laboratory" : mapping.subject.type,
      faculty_1_id: mapping.faculty.id,
      faculty_2_id: faculty2Id, // Save the second faculty ID if present
      hours_per_week: mapping.hoursPerWeek,
      classroom: mapping.classroom,
      slots_per_week: mapping.slotsPerWeek
    };

    console.log("Transformed mapping for database:", dbMapping);

    const labSlots = mapping.labSlots?.map(slot => ({
      day: slot.day,
      time_of_day: slot.timeOfDay,
      batch_name: slot.batch
    }));

    return this.createMapping(dbMapping, labSlots);
  }

  static async updateSubjectMapping(id: string, mapping: MappingType): Promise<void> {
    console.log("Updating subject mapping:", id, mapping);

    // Transform from UI model to database model for update
    const updates: Partial<ManualMappingInput> = {};

    // Update faculty_1_id if faculty is provided
    if (mapping.faculty) {
      updates.faculty_1_id = mapping.faculty.id;
    }

    // Update faculty_2_id if faculty2Id is provided
    if (mapping.faculty2Id !== undefined) {
      // Convert empty string to null for the database
      updates.faculty_2_id = mapping.faculty2Id === "" ? null : mapping.faculty2Id;
      console.log("Updating faculty_2_id to:", updates.faculty_2_id);
    }

    console.log("Applying updates:", updates);
    return this.updateMapping(id, updates);
  }

  static async deleteSubjectMapping(id: string): Promise<void> {
    return ManualMappingService.deleteMapping(id);
  }
}