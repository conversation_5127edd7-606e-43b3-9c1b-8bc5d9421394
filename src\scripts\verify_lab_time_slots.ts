// Script to verify that lab_time_slots table has been successfully updated
// to reference simplified_subject_faculty_mappings instead of subject_faculty_mappings

import { supabase } from "../integrations/supabase/client";

async function verifyLabTimeSlots() {
  try {
    console.log("Verifying lab_time_slots table update...");
    
    // Step 1: Check if simplified_mapping_id column exists
    console.log("Step 1: Checking if simplified_mapping_id column exists...");
    
    const { data: columnCheck, error: columnCheckError } = await supabase
      .from('lab_time_slots')
      .select('simplified_mapping_id')
      .limit(1);
    
    if (columnCheckError && columnCheckError.message.includes('column "simplified_mapping_id" does not exist')) {
      console.error("Column simplified_mapping_id does not exist");
      return false;
    }
    
    console.log("Column simplified_mapping_id exists");
    
    // Step 2: Check if all lab time slots have a simplified_mapping_id
    console.log("Step 2: Checking if all lab time slots have a simplified_mapping_id...");
    
    const { data: missingCount, error: missingCountError } = await supabase
      .from('lab_time_slots')
      .select('id', { count: 'exact' })
      .is('simplified_mapping_id', null);
    
    if (missingCountError) {
      console.error("Error checking for missing simplified_mapping_id:", missingCountError);
      return false;
    }
    
    if (missingCount && missingCount.length > 0) {
      console.error(`Found ${missingCount.length} lab time slots with missing simplified_mapping_id`);
      return false;
    }
    
    console.log("All lab time slots have a simplified_mapping_id");
    
    // Step 3: Check if all simplified_mapping_id values reference valid simplified_subject_faculty_mappings
    console.log("Step 3: Checking if all simplified_mapping_id values reference valid simplified_subject_faculty_mappings...");
    
    // Get all unique simplified_mapping_id values
    const { data: allSlots, error: allSlotsError } = await supabase
      .from('lab_time_slots')
      .select('simplified_mapping_id');
    
    if (allSlotsError) {
      console.error("Error getting all lab time slots:", allSlotsError);
      return false;
    }
    
    const uniqueSimplifiedIds = [...new Set(allSlots.map(slot => slot.simplified_mapping_id))];
    console.log(`Found ${uniqueSimplifiedIds.length} unique simplified_mapping_id values`);
    
    // Check if all simplified_mapping_id values exist in simplified_subject_faculty_mappings
    let allValid = true;
    
    // Process in batches of 100 to avoid query size limitations
    const batchSize = 100;
    
    for (let i = 0; i < uniqueSimplifiedIds.length; i += batchSize) {
      const batchIds = uniqueSimplifiedIds.slice(i, i + batchSize);
      console.log(`Checking batch ${i / batchSize + 1} of simplified_mapping_id values (${batchIds.length} IDs)...`);
      
      const { data: batchMappings, error: batchError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('id')
        .in('id', batchIds);
      
      if (batchError) {
        console.error(`Error checking batch ${i / batchSize + 1} of simplified_mapping_id values:`, batchError);
        allValid = false;
        continue;
      }
      
      const foundIds = new Set(batchMappings.map(mapping => mapping.id));
      
      for (const id of batchIds) {
        if (!foundIds.has(id)) {
          console.error(`Simplified mapping ID ${id} does not exist in simplified_subject_faculty_mappings`);
          allValid = false;
        }
      }
    }
    
    if (!allValid) {
      console.error("Some simplified_mapping_id values do not reference valid simplified_subject_faculty_mappings");
      return false;
    }
    
    console.log("All simplified_mapping_id values reference valid simplified_subject_faculty_mappings");
    
    // Step 4: Check if the mapping_id column has been renamed to old_mapping_id
    console.log("Step 4: Checking if the mapping_id column has been renamed...");
    
    const { data: mappingIdCheck, error: mappingIdCheckError } = await supabase
      .from('lab_time_slots')
      .select('mapping_id')
      .limit(1);
    
    if (mappingIdCheckError && mappingIdCheckError.message.includes('column "mapping_id" does not exist')) {
      console.log("Column mapping_id has been renamed");
      
      // Check if old_mapping_id exists
      const { data: oldMappingIdCheck, error: oldMappingIdCheckError } = await supabase
        .from('lab_time_slots')
        .select('old_mapping_id')
        .limit(1);
      
      if (oldMappingIdCheckError && oldMappingIdCheckError.message.includes('column "old_mapping_id" does not exist')) {
        console.log("Column old_mapping_id does not exist, it has been dropped");
      } else {
        console.log("Column old_mapping_id exists");
      }
    } else {
      console.log("Column mapping_id still exists, it has not been renamed");
    }
    
    console.log("Lab_time_slots table verification completed successfully!");
    return true;
  } catch (error) {
    console.error("Error verifying lab_time_slots table:", error);
    return false;
  }
}

// Execute the function
verifyLabTimeSlots()
  .then((success) => {
    if (success) {
      console.log("Verification completed successfully");
      process.exit(0);
    } else {
      console.error("Verification failed");
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error("Verification script failed:", error);
    process.exit(1);
  });
