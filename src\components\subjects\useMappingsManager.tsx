
import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { MappingType, Faculty } from "@/stores/SubjectMappingStore";

interface UseMappingsManagerProps {
  year: string;
  dept: string;
  sem: string;
  section: string;
  filtersValid: boolean;
  facultyList: Faculty[];
}

export function useMappingsManager({
  year,
  dept,
  sem,
  section,
  filtersValid,
  facultyList = [], // Provide default empty array
}: UseMappingsManagerProps) {
  const [mappings, setMappings] = useState<MappingType[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const loadMappings = useCallback(async () => {
    if (!filtersValid) {
      setMappings([]);
      return;
    }

    try {
      setLoading(true);

      // Try the simplified table first
      const { data: simplifiedData, error: simplifiedError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          id,
          academic_year,
          department,
          semester,
          section,
          subject_id,
          subject_code,
          subject_name,
          subject_type,
          faculty_1_id,
          faculty_2_id,
          hours_per_week,
          classroom,
          slots_per_week,
          subjects(subject_short_id)
        `)
        .eq('academic_year', year)
        .eq('department', dept)
        .eq('semester', sem)
        .eq('section', section)
        .not('subject_code', 'like', 'DELETED_%');

      // If simplified table query fails or returns no data, try the original table
      let mappingsData;
      let error;

      if (simplifiedError || !simplifiedData || simplifiedData.length === 0) {
        console.log("No data found in simplified table, trying original table");

        const { data: originalData, error: originalError } = await supabase
          .from('subject_faculty_mappings')
          .select(`
            id,
            academic_year,
            department,
            semester,
            section,
            subject_id,
            subject_code,
            subject_name,
            subject_type,
            faculty_1_id,
            faculty_2_id,
            hours_per_week,
            classroom,
            slots_per_week,
            subjects(subject_short_id)
          `)
          .eq('academic_year', year)
          .eq('department', dept)
          .eq('semester', sem)
          .eq('section', section)
          .not('subject_code', 'like', 'DELETED_%');

        mappingsData = originalData;
        error = originalError;
      } else {
        console.log(`Found ${simplifiedData.length} records in simplified table`);
        mappingsData = simplifiedData;
        error = simplifiedError;
      }

      if (error) throw error;

      // Ensure mappingsData is an array
      const safeData = Array.isArray(mappingsData) ? mappingsData : [];

      // Initialize an empty object for lab slots
      const labSlots: Record<string, any[]> = {};

      // Fetch lab time slots for lab subjects
      const labMappings = safeData.filter(m =>
        m.subject_type === 'laboratory' ||
        m.subject_type === 'lab'
      );

      if (labMappings && labMappings.length > 0) {
        const { data: labSlotsData, error: labSlotsError } = await supabase
          .from('lab_time_slots')
          .select('*')
          .in('mapping_id', labMappings.map(m => m.id));

        if (labSlotsError) throw labSlotsError;

        // Group lab slots by mapping_id - ensure it's always an array
        if (labSlotsData && Array.isArray(labSlotsData)) {
          labSlotsData.forEach(slot => {
            if (!labSlots[slot.mapping_id]) labSlots[slot.mapping_id] = [];
            labSlots[slot.mapping_id].push({
              day: slot.day || 'Monday',
              timeOfDay: slot.time_of_day || 'Morning',
              batch: slot.batch_name || 'A',
            });
          });
        }
      }

      // Ensure facultyList is an array
      const safeFacultyList = Array.isArray(facultyList) ? facultyList : [];

      // Process mappings data with safe access to all properties
      const processedMappings: MappingType[] = safeData.map(mapping => {
        // Ensure mapping itself is not null or undefined
        if (!mapping) {
          console.warn('Received null or undefined mapping');
          return null;
        }

        // Find faculty details - provide defaults if not found
        const facultyDetails = safeFacultyList.find(f => f.id === mapping.faculty_1_id) || {
          id: mapping.faculty_1_id || 'unknown',
          name: 'Unknown Faculty'
        };

        // Find second faculty details if ID exists
        const faculty2Details = mapping.faculty_2_id
          ? safeFacultyList.find(f => f.id === mapping.faculty_2_id)
          : undefined;

        // Map the subject type to the correct format with a safe default
        const subjectType: "theory" | "lab" | "elective" =
          mapping.subject_type === 'laboratory' ? 'lab' :
          mapping.subject_type === 'elective' ? 'elective' : 'theory';

        // Generate a default shortId if not available
        const shortId = mapping.subjects?.subject_short_id ||
          (mapping.subject_code ? mapping.subject_code.substring(0, 3).toUpperCase() : 'SUB');

        return {
          id: mapping.id || 'temp-id-' + Math.random().toString(36).substring(2, 9),
          academicYear: mapping.academic_year || year,
          department: mapping.department || dept,
          semester: mapping.semester || sem,
          section: mapping.section || section,
          subject: {
            id: mapping.subject_id || 'unknown',
            code: mapping.subject_code || 'N/A',
            name: mapping.subject_name || 'Unknown Subject',
            type: subjectType,
            shortId: shortId
          },
          subject_type: mapping.subject_type || 'theory',
          faculty: {
            id: mapping.faculty_1_id || 'unknown',
            name: facultyDetails?.name || 'Unknown Faculty'
          },
          faculty2Id: mapping.faculty_2_id || undefined,
          faculty2Name: faculty2Details?.name || undefined,
          hoursPerWeek: mapping.hours_per_week || 0,
          classroom: mapping.classroom || '',
          slotsPerWeek: mapping.slots_per_week || undefined,
          labSlots: labSlots[mapping.id] || [], // Always ensure this is an array
        };
      }).filter(Boolean) as MappingType[]; // Filter out any null entries

      setMappings(processedMappings);
    } catch (error) {
      console.error('Error loading mappings:', error);
      toast({
        title: 'Error loading mappings',
        description: 'Could not load subject-faculty mappings',
        variant: 'destructive'
      });
      setMappings([]);
    } finally {
      setLoading(false);
    }
  }, [year, dept, sem, section, filtersValid, facultyList, toast]);

  // Initial load
  useEffect(() => {
    loadMappings();
  }, [loadMappings]);

  return { mappings, loadMappings, loading };
}
