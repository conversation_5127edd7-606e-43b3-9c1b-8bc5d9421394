
import { Column } from "./types";

// Enhanced safe cell renderer with better error handling
export function renderCellContent(row: any, column: Column) {
  if (!row) return null;

  try {
    if (column.cell) {
      // Create a safe wrapper with fallbacks
      const info = {
        row: {
          original: row
        },
        getValue: () => row[column.accessorKey] != null ? row[column.accessorKey] : null
      };
      return column.cell(info);
    }
    // Direct access to data property when no cell renderer is provided
    return row[column.accessorKey] != null ? row[column.accessorKey] : null;
  } catch (error) {
    console.error("Error rendering cell:", error);
    return "Error";
  }
}
