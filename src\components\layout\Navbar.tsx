
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Bell, Calendar, LogOut, Menu, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import IconWrapper from "@/components/icons/IconWrapper";
import { useAuth } from "@/contexts/AuthContext";
import { useUserRole } from "@/hooks/useUserRole";
import { useLogout } from "@/hooks/useLogout";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import LeaveNotificationBadge from "@/components/notifications/LeaveNotificationBadge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { supabase } from "@/integrations/supabase/client";

interface NavbarProps {
  toggleSidebar: () => void;
}

interface UserProfile {
  id: string;
  roles: string[];
  username?: string;
  full_name?: string;
}

const Navbar = ({ toggleSidebar }: NavbarProps) => {
  const { user } = useAuth();
  const { isHOD, isPrincipal } = useUserRole();
  const { fastLogout } = useLogout();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  useEffect(() => {
    async function fetchUserProfile() {
      if (user?.id) {
        const { data, error } = await supabase
          .from('employee_details')
          .select('id, roles, full_name')
          .eq('id', user.id)
          .single();

        if (data && !error) {
          setUserProfile({
            id: data.id,
            roles: data.roles || [],
            full_name: data.full_name,
            username: user.email // Use email as username
          });
        } else {
          console.error('Error fetching user profile:', error);
        }
      }
    }

    fetchUserProfile();
  }, [user]);

  return (
    <header className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border sticky top-0 z-50 shadow-soft">
      <div className="flex h-16 items-center px-responsive">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="md:hidden hover:bg-accent/50 transition-all duration-200"
        >
          <IconWrapper icon={Menu} />
          <span className="sr-only">Toggle sidebar</span>
        </Button>

        <Link to="/" className="flex items-center gap-2 mr-6 group transition-all duration-200 hover:scale-105">
          <IconWrapper icon={Calendar} className="h-6 w-6 text-primary group-hover:text-primary-hover transition-colors duration-200" />
          <span className="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">EduFlow</span>
        </Link>

        <div className="ml-auto flex items-center gap-3">
          {/* Theme Toggle */}
          <div className="transition-all duration-200 hover:scale-105">
            <ThemeToggle />
          </div>

          {/* Show leave notifications for HODs and Principals */}
          {(isHOD || isPrincipal) ? (
            <div className="transition-all duration-200 hover:scale-105">
              <LeaveNotificationBadge />
            </div>
          ) : (
            <Button variant="ghost" size="icon" className="hover:bg-accent/50 transition-all duration-200 hover:scale-105">
              <IconWrapper icon={Bell} />
              <span className="sr-only">Notifications</span>
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full hover:bg-accent/50 transition-all duration-200 hover:scale-105 ring-2 ring-transparent hover:ring-accent/20"
              >
                <IconWrapper icon={User} />
                <span className="sr-only">User menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64 animate-slide-down">
              <DropdownMenuLabel className="text-sm font-medium">
                {userProfile?.full_name || userProfile?.username || user?.email || 'My Account'}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-xs text-muted-foreground">
                Roles: <span className="ml-2 font-medium text-foreground">{userProfile?.roles?.join(', ') || 'User'}</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="hover:bg-accent/50 transition-colors duration-200">
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-accent/50 transition-colors duration-200">
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={fastLogout}
                className="text-destructive hover:bg-destructive/10 hover:text-destructive transition-colors duration-200"
              >
                <LogOut className="mr-2 h-4 w-4" />Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
