// TimetableService.ts
import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from 'uuid';

export interface TimeStructure {
  id?: string;
  academic_year: string;
  department: string;
  first_half_start_time: string;
  first_half_end_time: string;
  second_half_start_time: string;
  second_half_end_time: string;
  lunch_break_start_time: string;
  lunch_break_end_time: string;
  tea_break_start_time: string;
  tea_break_end_time: string;
  theory_class_duration: number;
  two_credits_lab_duration: number;
  three_credits_lab_duration: number;
  periods_in_first_half: number;
  periods_in_second_half: number;
  working_days: string[];
  auto_merge_labs: boolean;
}

export interface TimetableSlot {
  id?: string;
  academic_year: string;
  department: string;
  semester: string;
  section: string;
  day: string;
  time_slot: string;
  subject_id: string;
  subject_code: string;
  subject_name: string;
  subject_type: string;
  faculty_id: string;
  faculty_name: string;
  room_number?: string;
  subject_short_id?: string;
  batch_name?: string;
  is_lab_start?: boolean;
  is_processed?: boolean;
  col_span?: number;
  faculty2_id?: string;
  faculty2_name?: string;
  is_hidden?: boolean;
  is_full_session_lab?: boolean;
  session?: string;
  created_at?: string;
  updated_at?: string;
  // Additional properties for display and export
  isContinuedLab?: boolean;
  showContinuedBadge?: boolean;
  displayText?: string;
  customTextColor?: string;
}

export class TimetableService {
  // Placeholder UUID for unassigned faculty
  static readonly UNASSIGNED_FACULTY_ID = "00000000-0000-0000-0000-000000000000";

  /**
   * Ensures that an unassigned faculty record exists in the employee_details table
   * This is used for skill labs which don't have an assigned faculty
   */
  static async ensureUnassignedFacultyExists(): Promise<string> {
    try {
      // Check if the unassigned faculty already exists
      const { data: existingFaculty, error: checkError } = await supabase
        .from('employee_details')
        .select('id')
        .eq('id', this.UNASSIGNED_FACULTY_ID)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
        console.error("Error checking for unassigned faculty:", checkError);
        throw checkError;
      }

      // If the faculty already exists, return its ID
      if (existingFaculty) {
        console.log("Unassigned faculty already exists");
        return this.UNASSIGNED_FACULTY_ID;
      }

      // Create the unassigned faculty record
      const { error: insertError } = await supabase
        .from('employee_details')
        .insert({
          id: this.UNASSIGNED_FACULTY_ID,
          full_name: "Unassigned Faculty",
          email: "<EMAIL>",
          department: "System",
          designation: "System",
          roles: ["system"],
          vacant_by_day: {},
          vacant_count_by_day: {}
        })
        .select()
        .single();

      if (insertError) {
        console.error("Error creating unassigned faculty:", insertError);
        throw insertError;
      }

      console.log("Created unassigned faculty record");
      return this.UNASSIGNED_FACULTY_ID;
    } catch (error) {
      console.error("Error ensuring unassigned faculty exists:", error);
      throw error;
    }
  }
  static async fetchTimeStructure({ academicYear, department }: { academicYear: string; department: string; }): Promise<TimeStructure | null> {
    const { data, error } = await supabase
      .from('time_structure')
      .select('*')
      .eq('academic_year', academicYear)
      .eq('department', department)
      .single();

    if (error || !data) {
      console.error("Error fetching time structure:", error);
      return null;
    }

    return data;
  }

  static async saveTimeStructure(timeStructure: TimeStructure): Promise<TimeStructure> {
    const payload = {
      id: timeStructure.id,
      academic_year: timeStructure.academic_year,
      department: timeStructure.department,
      first_half_start_time: timeStructure.first_half_start_time,
      first_half_end_time: timeStructure.first_half_end_time,
      second_half_start_time: timeStructure.second_half_start_time,
      second_half_end_time: timeStructure.second_half_end_time,
      lunch_break_start_time: timeStructure.lunch_break_start_time,
      lunch_break_end_time: timeStructure.lunch_break_end_time,
      tea_break_start_time: timeStructure.tea_break_start_time,
      tea_break_end_time: timeStructure.tea_break_end_time,
      theory_class_duration: timeStructure.theory_class_duration,
      two_credits_lab_duration: timeStructure.two_credits_lab_duration,
      three_credits_lab_duration: timeStructure.three_credits_lab_duration,
      periods_in_first_half: timeStructure.periods_in_first_half,
      periods_in_second_half: timeStructure.periods_in_second_half,
      working_days: timeStructure.working_days,
      auto_merge_labs: timeStructure.auto_merge_labs
    };
    let result: TimeStructure;
    if (timeStructure.id) {
      const { data, error } = await supabase
        .from('time_structure')
        .update(payload)
        .eq('id', timeStructure.id)
        .select()
        .single();
      if (error) throw error;
      result = data as TimeStructure;
    } else {
      const { data, error } = await supabase
        .from('time_structure')
        .insert(payload)
        .select()
        .single();
      if (error) throw error;
      result = data as TimeStructure;
    }
    return result;
  }

  static async fetchSubjects({ academicYear, department, semester }: {
    academicYear: string;
    department: string;
    semester: string;
    section?: string;
  }): Promise<any[]> {
    const { data, error } = await supabase
      .from('subjects')
      .select('*')
      .eq('academic_year', academicYear)
      .eq('department', department)
      .eq('semester', semester);

    if (error) {
      console.error("Error fetching subjects:", error);
      throw error;
    }

    return data || [];
  }

  static async fetchTimetable({ academicYear, department, semester, section }: {
    academicYear: string;
    department: string;
    semester: string;
    section: string;
  }): Promise<TimetableSlot[]> {
    console.log("Fetching timetable data:", { academicYear, department, semester, section });

    // First get the timetable slots
    const { data, error } = await supabase
      .from('timetable_slots')
      .select('*')
      .eq('academic_year', academicYear)
      .eq('department', department)
      .eq('semester', semester)
      .eq('section', section);

    if (error) {
      console.error("Error fetching timetable slots:", error);
      throw error;
    }

    console.log(`Found ${data?.length || 0} timetable slots`);

    // Now get the subject faculty mappings to ensure we have the correct faculty IDs
    // Try the simplified table first
    let mappings: any[] = [];
    let mappingsError: any = null;

    try {
      const { data, error } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          id,
          subject_code,
          faculty_1_id,
          faculty_2_id
        `)
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .not('subject_code', 'like', 'DELETED_%');

      mappings = data;
      mappingsError = error;

      if (error || !data || data.length === 0) {
        console.log("No mappings found in simplified table, trying original table");

        // Fall back to the original table if needed
        const { data: originalData, error: originalError } = await supabase
          .from('subject_faculty_mappings')
          .select(`
            id,
            subject_code,
            faculty_1_id,
            faculty_2_id
          `)
          .eq('academic_year', academicYear)
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section);

        mappings = originalData;
        mappingsError = originalError;
      }
    } catch (error) {
      console.error("Error fetching mappings from both tables:", error);
      mappingsError = error;
    }

    if (mappingsError) {
      console.error("Error fetching subject faculty mappings:", mappingsError);
      return data || [];
    }

    // Create a mapping from subject code to faculty IDs
    const subjectToFacultyMap = new Map();
    mappings.forEach(mapping => {
      subjectToFacultyMap.set(mapping.subject_code, {
        faculty1Id: mapping.faculty_1_id,
        faculty2Id: mapping.faculty_2_id
      });
    });

    // Update the slots with the correct faculty IDs
    const updatedSlots = data?.map(slot => {
      // Skip skill labs - they should remain unassigned
      if (slot.subject_code === "SKILL LAB") {
        console.log(`Preserving unassigned faculty for Skill Lab slot ${slot.id}`);
        return slot;
      }

      const facultyInfo = subjectToFacultyMap.get(slot.subject_code);
      if (facultyInfo) {
        return {
          ...slot,
          faculty_id: facultyInfo.faculty1Id,
          faculty2_id: facultyInfo.faculty2Id
        };
      }
      return slot;
    }) || [];

    return updatedSlots;
  }

  static async fetchLabSlotMappings({ academicYear, department, semester, section }: {
    academicYear: string;
    department: string;
    semester: string;
    section: string;
  }): Promise<{
    mappings: any[];
    labSlots: { mapping_id: string; day: string; time_of_day: string; batch_name?: string }[];
    facultyMap: Record<string, string>;
    subjectMap: Record<string, string>;
  }> {
    console.log("Fetching lab slot mappings:", { academicYear, department, semester, section });

    // 1) Fetch the subject mappings of type lab
    // Try the simplified table first
    let mappings: any[] = [];
    let mappingsError: any = null;

    try {
      // Try the simplified table first
      const { data: simplifiedData, error: simplifiedError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          id,
          subject_id,
          subject_code,
          subject_name,
          faculty_1_id,
          faculty_2_id,
          classroom,
          subject_type,
          slots_per_week
        `)
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['laboratory', 'lab'])
        .not('subject_code', 'like', 'DELETED_%');

      if (simplifiedError || !simplifiedData || simplifiedData.length === 0) {
        console.log("No lab mappings found in simplified table, trying original table");

        // Fall back to the original table
        const { data: originalData, error: originalError } = await supabase
          .from('subject_faculty_mappings')
          .select(`
            id,
            subject_id,
            subject_code,
            subject_name,
            faculty_1_id,
            faculty_2_id,
            classroom,
            subject_type,
            slots_per_week
          `)
          .eq('academic_year', academicYear)
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section)
          .in('subject_type', ['laboratory', 'lab']);

        mappings = originalData;
        mappingsError = originalError;
      } else {
        console.log(`Found ${simplifiedData.length} lab mappings in simplified table`);
        mappings = simplifiedData;
        mappingsError = simplifiedError;
      }
    } catch (error) {
      console.error("Error fetching lab mappings from both tables:", error);
      mappingsError = error;
    }

    if (mappingsError) {
      console.error("Error fetching lab mappings:", mappingsError);
      throw mappingsError;
    }

    if (!mappings || mappings.length === 0) {
      console.log("No laboratory mappings found in either table");
      return { mappings: [], labSlots: [], facultyMap: {}, subjectMap: {} };
    }

    // 2) Fetch the lab time slots for these mappings
    const { data: labSlots, error: labSlotsError } = await supabase
      .from('lab_time_slots')
      .select('*')
      .in('mapping_id', mappings.map(m => m.id));
    if (labSlotsError) {
      console.error("Error fetching lab_time_slots:", labSlotsError);
      throw labSlotsError;
    }

    console.log(`Found ${labSlots?.length || 0} lab time slots for ${mappings.length} lab mappings`);

    // If we don't have enough lab slots, we might need to create default ones
    const expectedSlots = mappings.reduce((total, mapping) => total + (mapping.slots_per_week || 1), 0);
    if (!labSlots || labSlots.length < expectedSlots) {
      console.warn(`Not enough lab slots found (${labSlots?.length || 0} for expected ${expectedSlots}). Consider creating default slots.`);

      // Create default slots for mappings with missing slots
      const defaultSlots = [];
      const workingDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
      const timeOfDays = ['Morning', 'Afternoon'];

      for (const mapping of mappings) {
        const existingSlots = labSlots?.filter(slot => slot.mapping_id === mapping.id) || [];
        const slotsNeeded = (mapping.slots_per_week || 1) - existingSlots.length;

        if (slotsNeeded > 0) {
          console.log(`Creating ${slotsNeeded} default slots for ${mapping.subject_code}`);

          // Generate section-specific batch names
          const batch1Name = `${section}1`;
          const batch2Name = `${section}2`;

          for (let i = 0; i < slotsNeeded; i++) {
            const dayIndex = (mappings.indexOf(mapping) + i) % workingDays.length;
            const timeIndex = i % timeOfDays.length;
            const batchName = i % 2 === 0 ? batch1Name : batch2Name;

            defaultSlots.push({
              mapping_id: mapping.id,
              day: workingDays[dayIndex],
              time_of_day: timeOfDays[timeIndex],
              batch_name: batchName,
              slot_order: i + 1
            });
          }
        }
      }

      // Insert default slots if needed
      if (defaultSlots.length > 0) {
        const { data: insertedSlots, error: insertError } = await supabase
          .from('lab_time_slots')
          .insert(defaultSlots)
          .select();

        if (insertError) {
          console.error("Error inserting default lab slots:", insertError);
        } else {
          console.log(`Inserted ${insertedSlots?.length || 0} default lab slots`);

          // Update faculty availability for each inserted lab slot
          if (insertedSlots && insertedSlots.length > 0) {
            console.log("Updating faculty availability for default lab slots");

            // Create a mapping of mapping_id to faculty IDs
            const mappingToFacultyMap = new Map();

            // Build the mapping from mappings
            for (const mapping of mappings) {
              mappingToFacultyMap.set(mapping.id, {
                faculty1Id: mapping.faculty_1_id,
                faculty2Id: mapping.faculty_2_id
              });
            }

            // Update faculty availability for each lab slot
            for (const slot of insertedSlots) {
              try {
                const facultyInfo = mappingToFacultyMap.get(slot.mapping_id);

                if (facultyInfo) {
                  // Import the FacultyAvailabilityService dynamically to avoid circular dependencies
                  const { FacultyAvailabilityService } = await import('./FacultyAvailabilityService');

                  await FacultyAvailabilityService.updateFacultyAvailabilityFromLabSlots(
                    facultyInfo.faculty1Id,
                    facultyInfo.faculty2Id,
                    slot.day,
                    slot.time_of_day
                  );
                  console.log(`Updated faculty availability for lab slot on ${slot.day} at ${slot.time_of_day}`);
                }
              } catch (availabilityError) {
                console.error("Error updating faculty availability:", availabilityError);
                // Continue with the next slot even if this one fails
              }
            }
          }

          // Add the newly inserted slots to our result
          if (insertedSlots) {
            if (!labSlots) {
              return {
                mappings,
                labSlots: insertedSlots,
                facultyMap: await this.buildFacultyMap(mappings),
                subjectMap: await this.buildSubjectMap(mappings)
              };
            }
            labSlots.push(...insertedSlots);
          }
        }
      }
    }

    return {
      mappings,
      labSlots: labSlots || [],
      facultyMap: await this.buildFacultyMap(mappings),
      subjectMap: await this.buildSubjectMap(mappings)
    };
  }

  // Helper methods to reduce code duplication
  private static async buildFacultyMap(mappings: any[]): Promise<Record<string, string>> {
    const facultyIds = new Set<string>();
    mappings.forEach(m => {
      if (m.faculty_1_id) facultyIds.add(m.faculty_1_id);
      if (m.faculty_2_id) facultyIds.add(m.faculty_2_id);
    });

    if (facultyIds.size === 0) return {};

    // Try to fetch faculty details with the correct column name (full_name instead of name)
    try {
      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details') // or 'faculty' table
        .select('id, full_name')  // Use full_name instead of name
        .in('id', Array.from(facultyIds));

      if (facultyError) {
        console.error("Error fetching faculty names:", facultyError);
        return {};
      }

      const facultyMap: Record<string, string> = {};
      facultyData?.forEach(f => {
        facultyMap[f.id] = f.full_name;
      });

      return facultyMap;
    } catch (error) {
      console.error("Error building faculty map:", error);
      return {};
    }
  }

  private static async buildSubjectMap(mappings: any[]): Promise<Record<string, string>> {
    const subjectIds = mappings.map(m => m.subject_id);
    const { data: subjectsData, error: subjectsError } = await supabase
      .from('subjects')
      .select('id, subject_short_id')
      .in('id', subjectIds);
    if (subjectsError) {
      console.error("Error fetching subjects for short IDs:", subjectsError);
      return {};
    }

    const subjectMap: Record<string, string> = {};
    subjectsData?.forEach(s => {
      subjectMap[s.id] = s.subject_short_id;
    });

    return subjectMap;
  }

  static async saveTimetableSlot(slot: TimetableSlot): Promise<TimetableSlot> {
    // Remove any custom ID prefixes
    if (slot.id && slot.id.startsWith('lab-')) {
      delete slot.id;
    }

    const cleanedSlot = { ...slot };
    // Keep important lab fields for proper rendering
    // delete (cleanedSlot as any).is_lab_start;  // Keep this for lab spanning logic
    // delete (cleanedSlot as any).is_processed; // Keep this for lab spanning logic
    // delete (cleanedSlot as any).col_span;     // Keep this for lab spanning logic
    // delete (cleanedSlot as any).is_hidden;    // Keep this for lab spanning logic
    // delete (cleanedSlot as any).is_full_session_lab; // Keep this for lab spanning logic

    // For skill labs, ensure the unassigned faculty exists and use the placeholder ID
    if (cleanedSlot.subject_code === "SKILL LAB") {
      await this.ensureUnassignedFacultyExists();
      cleanedSlot.faculty_id = this.UNASSIGNED_FACULTY_ID;
    }

    try {
      // The error suggests that subject_id should reference subject_faculty_mappings, not subjects
      console.log(`Looking for valid mapping for subject code ${cleanedSlot.subject_code}`);

      // Try the simplified table first
      let validMapping: any[] = [];
      let mappingError: any = null;

      try {
        // Try the simplified table first
        const { data: simplifiedMapping, error: simplifiedError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select('id')
          .eq('subject_code', cleanedSlot.subject_code)
          .eq('academic_year', cleanedSlot.academic_year)
          .eq('department', cleanedSlot.department)
          .eq('semester', cleanedSlot.semester)
          .eq('section', cleanedSlot.section)
          .not('subject_code', 'like', 'DELETED_%')
          .limit(1);

        if (simplifiedError || !simplifiedMapping || simplifiedMapping.length === 0) {
          console.log("No valid mapping found in simplified table, trying original table");

          // Fall back to the original table
          const { data: originalMapping, error: originalError } = await supabase
            .from('subject_faculty_mappings')
            .select('id')
            .eq('subject_code', cleanedSlot.subject_code)
            .eq('academic_year', cleanedSlot.academic_year)
            .eq('department', cleanedSlot.department)
            .eq('semester', cleanedSlot.semester)
            .eq('section', cleanedSlot.section)
            .limit(1);

          validMapping = originalMapping;
          mappingError = originalError;
        } else {
          console.log(`Found valid mapping in simplified table: ${simplifiedMapping[0].id}`);
          validMapping = simplifiedMapping;
          mappingError = simplifiedError;
        }
      } catch (error) {
        console.error("Error finding valid mapping from both tables:", error);
        mappingError = error;
      }

      if (mappingError) {
        console.error("Error finding valid mapping:", mappingError);
        throw mappingError;
      }

      if (!validMapping || validMapping.length === 0) {
        console.error(`No valid mapping found for subject code ${cleanedSlot.subject_code}`);
        throw new Error(`No valid mapping found for subject code ${cleanedSlot.subject_code}`);
      }

      console.log(`Found valid mapping: ${validMapping[0].id}`);

      // Use the mapping's ID as the subject_id
      cleanedSlot.subject_id = validMapping[0].id;

      // Only treat as existing if it's the same subject_id at that date/time
      const { data: existing, error: lookupError } = await supabase
        .from('timetable_slots')
        .select('id')
        .eq('academic_year', cleanedSlot.academic_year)
        .eq('department', cleanedSlot.department)
        .eq('semester', cleanedSlot.semester)
        .eq('section', cleanedSlot.section)
        .eq('day', cleanedSlot.day)
        .eq('time_slot', cleanedSlot.time_slot)
        .eq('subject_id', cleanedSlot.subject_id);
      if (lookupError) {
        console.error("Error checking existing slot:", lookupError);
      }

      if (existing?.length && !cleanedSlot.id) {
        cleanedSlot.id = existing[0].id;
        console.log("Updating existing slot for same subject:", cleanedSlot.id);
      }

      console.log("Saving timetable slot:", cleanedSlot);
      const { data, error: upsertError } = await supabase
        .from('timetable_slots')
        .upsert(cleanedSlot)
        .select()
        .single();
      if (upsertError) throw upsertError;
      return data!;
    } catch (err) {
      console.error("Error saving timetable slot:", err);
      throw err;
    }
  }

  static async deleteTimetableSlot(slotId: string): Promise<void> {
    const { error } = await supabase
      .from('timetable_slots')
      .delete()
      .eq('id', slotId);
    if (error) throw error;
  }

  static async clearTimetable({ academicYear, department, semester, section, subject_type }: {
    academicYear: string;
    department: string;
    semester: string;
    section: string;
    subject_type?: string;
  }): Promise<string[]> {
    console.log("Clearing timetable for:", { academicYear, department, semester, section, subject_type });

    try {
      // First, get the faculty members affected by this timetable clearing
      // This needs to be done before deleting the slots
      let affectedFacultyQuery = supabase
        .from('timetable_slots')
        .select('faculty_id, faculty2_id')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section);

      if (subject_type) {
        affectedFacultyQuery = affectedFacultyQuery.eq('subject_type', subject_type);
      }

      const { data: affectedSlots, error: affectedError } = await affectedFacultyQuery;

      if (affectedError) {
        console.error("Error fetching affected faculty members:", affectedError);
        throw new Error(`Failed to fetch affected faculty members: ${affectedError.message}`);
      }

      // Extract unique faculty IDs from the affected slots
      const affectedFacultyIds = new Set<string>();

      affectedSlots?.forEach(slot => {
        if (slot.faculty_id) affectedFacultyIds.add(slot.faculty_id);
        if (slot.faculty2_id) affectedFacultyIds.add(slot.faculty2_id);
      });

      console.log(`Found ${affectedFacultyIds.size} faculty members affected by this timetable clearing`);

      // Clear timetable_slots
      let query = supabase
        .from('timetable_slots')
        .delete()
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section);
      if (subject_type) {
        query = query.eq('subject_type', subject_type);
      }
      const { error: slotsError } = await query;
      if (slotsError) {
        console.error("Error clearing timetable slots:", slotsError);
        throw new Error(`Failed to clear timetable slots: ${slotsError.message}`);
      }

      // Clear class_timetables
      let classQuery = supabase
        .from('class_timetables')
        .delete()
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section);
      if (subject_type) {
        classQuery = classQuery.eq('subject_type', subject_type);
      }
      const { error: classError } = await classQuery;
      if (classError) {
        console.error("Error clearing class timetables:", classError);
        throw new Error(`Failed to clear class timetables: ${classError.message}`);
      }

      // Clear faculty_timetables
      let facultyQuery = supabase
        .from('faculty_timetables')
        .delete()
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section);
      if (subject_type) {
        facultyQuery = facultyQuery.eq('subject_type', subject_type);
      }
      const { error: facultyError } = await facultyQuery;
      if (facultyError) {
        console.error("Error clearing faculty timetables:", facultyError);
        throw new Error(`Failed to clear faculty timetables: ${facultyError.message}`);
      }

      console.log(`Successfully cleared timetable for ${semester} semester, Section ${section}`);

      // Get the time structure for this department and academic year
      // This is needed for recalculating faculty availability
      const { data: timeStructureData, error: timeStructureError } = await supabase
        .from('time_structure')
        .select('*')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .single();

      if (timeStructureError) {
        console.error("Error fetching time structure:", timeStructureError);
        throw new Error(`Failed to fetch time structure: ${timeStructureError.message}`);
      }

      if (!timeStructureData) {
        console.error("No time structure found for this department and academic year");
        throw new Error("No time structure found for this department and academic year");
      }

      // Convert the time structure data to the expected format
      const timeStructure: TimeStructure = {
        academic_year: timeStructureData.academic_year,
        department: timeStructureData.department,
        first_half_start_time: timeStructureData.first_half_start_time,
        first_half_end_time: timeStructureData.first_half_end_time,
        second_half_start_time: timeStructureData.second_half_start_time,
        second_half_end_time: timeStructureData.second_half_end_time,
        tea_break_start_time: timeStructureData.tea_break_start_time,
        tea_break_end_time: timeStructureData.tea_break_end_time,
        lunch_break_start_time: timeStructureData.lunch_break_start_time,
        lunch_break_end_time: timeStructureData.lunch_break_end_time,
        theory_class_duration: timeStructureData.theory_class_duration,
        two_credits_lab_duration: timeStructureData.two_credits_lab_duration || 120,
        three_credits_lab_duration: timeStructureData.three_credits_lab_duration || 180,
        periods_in_first_half: timeStructureData.periods_in_first_half,
        periods_in_second_half: timeStructureData.periods_in_second_half,
        working_days: timeStructureData.working_days,
        auto_merge_labs: timeStructureData.auto_merge_labs || false
      };

      // Recalculate faculty availability based on their remaining commitments
      // This ensures faculty members with lab slots in other semester-sections
      // maintain their correct availability status
      if (affectedFacultyIds.size > 0) {
        try {
          // Import the FacultyAvailabilityService dynamically to avoid circular dependencies
          const { FacultyAvailabilityService } = await import('./FacultyAvailabilityService');

          // Call the improved recalculateFacultyAvailabilityAfterClear function
          await FacultyAvailabilityService.recalculateFacultyAvailabilityAfterClear(
            Array.from(affectedFacultyIds),
            timeStructure,
            academicYear
          );

          console.log("Faculty availability recalculated successfully");
        } catch (availabilityError) {
          console.error("Error recalculating faculty availability:", availabilityError);
          // Continue even if availability recalculation fails
        }
      }

      // Return the list of affected faculty IDs
      return Array.from(affectedFacultyIds);
    } catch (error) {
      console.error("Error in clearTimetable:", error);
      throw error;
    }
  }

  static async createDefaultLabSlots({ academicYear, department, semester, section }: {
    academicYear: string;
    department: string;
    semester: string;
    section: string;
  }): Promise<void> {
    console.log("Creating default lab slots for:", { academicYear, department, semester, section });

    // 1) Get lab mappings directly
    // Try the simplified table first
    let mappings: any[] = [];
    let mappingsError: any = null;

    try {
      // Try the simplified table first
      const { data: simplifiedData, error: simplifiedError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          id,
          subject_id,
          subject_code,
          subject_name,
          faculty_1_id,
          faculty_2_id,
          classroom,
          subject_type
        `)
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['laboratory', 'lab'])
        .not('subject_code', 'like', 'DELETED_%');

      if (simplifiedError || !simplifiedData || simplifiedData.length === 0) {
        console.log("No lab mappings found in simplified table, trying original table");

        // Fall back to the original table
        const { data: originalData, error: originalError } = await supabase
          .from('subject_faculty_mappings')
          .select(`
            id,
            subject_id,
            subject_code,
            subject_name,
            faculty_1_id,
            faculty_2_id,
            classroom,
            subject_type
          `)
          .eq('academic_year', academicYear)
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section)
          .in('subject_type', ['laboratory', 'lab']);

        mappings = originalData || [];
        mappingsError = originalError;
      } else {
        console.log(`Found ${simplifiedData.length} lab mappings in simplified table`);
        mappings = simplifiedData;
        mappingsError = simplifiedError;
      }
    } catch (error) {
      console.error("Error fetching lab mappings from both tables:", error);
      mappingsError = error;
    }

    if (mappingsError) throw mappingsError;
    if (!mappings || mappings.length === 0) {
      console.log("No lab mappings found in either table");
      return;
    }

    console.log(`Found ${mappings.length} lab mappings`);

    // 2) Check if these mappings already have lab slots
    const { data: existingSlots, error: existingError } = await supabase
      .from('lab_time_slots')
      .select('mapping_id')
      .in('mapping_id', mappings.map(m => m.id));

    if (existingError) throw existingError;

    // Create a set of mapping IDs that already have slots
    const mappingsWithSlots = new Set(existingSlots?.map(s => s.mapping_id) || []);

    // Filter mappings that don't have slots yet
    const mappingsNeedingSlots = mappings.filter(m => !mappingsWithSlots.has(m.id));

    console.log(`${mappingsNeedingSlots.length} mappings need default slots`);

    // 3) Create default lab slots (TWO per mapping - one for each batch)
    const defaultSlots = [];
    const workingDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

    for (let i = 0; i < mappingsNeedingSlots.length; i++) {
      const mapping = mappingsNeedingSlots[i];

      // Generate section-specific batch names
      const batch1Name = `${section}1`;
      const batch2Name = `${section}2`;

      // Create two slots for each mapping (using section-based batch names)
      for (let batchIndex = 0; batchIndex < 2; batchIndex++) {
        // Assign to different days if possible
        const dayIndex = (i * 2 + batchIndex) % workingDays.length;
        const day = workingDays[dayIndex];

        // Alternate between morning and afternoon
        const timeOfDay = batchIndex % 2 === 0 ? 'Morning' : 'Afternoon';

        // Create default batch with section-specific batch names
        const defaultSlot = {
          mapping_id: mapping.id,
          day,
          time_of_day: timeOfDay,
          batch_name: batchIndex === 0 ? batch1Name : batch2Name,
          slot_order: batchIndex + 1
        };

        defaultSlots.push(defaultSlot);
      }
    }

    // Insert the default slots if any
    if (defaultSlots.length > 0) {
      console.log(`Inserting ${defaultSlots.length} default lab slots`);
      const { data, error } = await supabase
        .from('lab_time_slots')
        .insert(defaultSlots)
        .select();

      if (error) {
        console.error("Error inserting default lab slots:", error);
        throw error;
      }

      console.log(`Successfully inserted ${data?.length || 0} default lab slots`);

      // Update faculty availability for each inserted lab slot
      if (data && data.length > 0) {
        console.log("Updating faculty availability for default lab slots");

        // Create a mapping of mapping_id to faculty IDs
        const mappingToFacultyMap = new Map();

        // Build the mapping from mappings
        for (const mapping of mappingsNeedingSlots) {
          mappingToFacultyMap.set(mapping.id, {
            faculty1Id: mapping.faculty_1_id,
            faculty2Id: mapping.faculty_2_id
          });
        }

        // Update faculty availability for each lab slot
        for (const slot of data) {
          try {
            const facultyInfo = mappingToFacultyMap.get(slot.mapping_id);

            if (facultyInfo) {
              // Import the FacultyAvailabilityService dynamically to avoid circular dependencies
              const { FacultyAvailabilityService } = await import('./FacultyAvailabilityService');

              await FacultyAvailabilityService.updateFacultyAvailabilityFromLabSlots(
                facultyInfo.faculty1Id,
                facultyInfo.faculty2Id,
                slot.day,
                slot.time_of_day
              );
              console.log(`Updated faculty availability for lab slot on ${slot.day} at ${slot.time_of_day}`);
            }
          } catch (availabilityError) {
            console.error("Error updating faculty availability:", availabilityError);
            // Continue with the next slot even if this one fails
          }
        }
      }
    }
  }

  static async updateFacultyAssignments({ academicYear, department, semester, section }: {
    academicYear: string;
    department: string;
    semester: string;
    section: string;
  }): Promise<void> {
    console.log("Updating faculty assignments for:", { academicYear, department, semester, section });

    try {
      // First, get all subject faculty mappings
      // Try the simplified table first
      let mappings: any[] = [];
      let mappingsError: any = null;

      try {
        // Try the simplified table first
        const { data: simplifiedData, error: simplifiedError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            id,
            subject_code,
            subject_name,
            faculty_1_id,
            faculty_2_id
          `)
          .eq('academic_year', academicYear)
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section)
          .not('subject_code', 'like', 'DELETED_%');

        if (simplifiedError || !simplifiedData || simplifiedData.length === 0) {
          console.log("No mappings found in simplified table, trying original table");

          // Fall back to the original table
          const { data: originalData, error: originalError } = await supabase
            .from('subject_faculty_mappings')
            .select(`
              id,
              subject_code,
              subject_name,
              faculty_1_id,
              faculty_2_id
            `)
            .eq('academic_year', academicYear)
            .eq('department', department)
            .eq('semester', semester)
            .eq('section', section);

          mappings = originalData || [];
          mappingsError = originalError;
        } else {
          console.log(`Found ${simplifiedData.length} mappings in simplified table`);
          mappings = simplifiedData;
          mappingsError = simplifiedError;
        }
      } catch (error) {
        console.error("Error fetching mappings from both tables:", error);
        mappingsError = error;
      }

      if (mappingsError) throw mappingsError;

      // Then get all timetable slots
      const { data: slots, error: slotsError } = await supabase
        .from('timetable_slots')
        .select('*')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section);

      if (slotsError) throw slotsError;

      // Create a mapping from subject code to faculty IDs
      const subjectToFacultyMap = new Map();
      mappings.forEach(mapping => {
        subjectToFacultyMap.set(mapping.subject_code, {
          faculty1Id: mapping.faculty_1_id,
          faculty2Id: mapping.faculty_2_id,
          subjectName: mapping.subject_name
        });
      });

      // Update each slot with the correct faculty IDs
      for (const slot of slots) {
        // Skip skill labs - they should remain unassigned
        if (slot.subject_code === "SKILL LAB") {
          console.log(`Skipping faculty assignment for Skill Lab slot ${slot.id}`);
          continue;
        }

        const facultyInfo = subjectToFacultyMap.get(slot.subject_code);
        if (facultyInfo) {
          // Only update if the faculty IDs are different
          if (slot.faculty_id !== facultyInfo.faculty1Id || slot.faculty2_id !== facultyInfo.faculty2Id) {
            const { error: updateError } = await supabase
              .from('timetable_slots')
              .update({
                faculty_id: facultyInfo.faculty1Id,
                faculty2_id: facultyInfo.faculty2Id
              })
              .eq('id', slot.id);

            if (updateError) {
              console.error(`Error updating slot ${slot.id}:`, updateError);
            } else {
              console.log(`Updated slot ${slot.id} for ${slot.subject_code} with faculty IDs:`, {
                faculty1: facultyInfo.faculty1Id,
                faculty2: facultyInfo.faculty2Id
              });
            }
          }
        }
      }

      console.log("Faculty assignments updated successfully");
    } catch (error) {
      console.error("Error updating faculty assignments:", error);
      throw error;
    }
  }

  /**
   * Get the correct time slot string for lab sessions based on time of day and semester configuration
   */
  private static async getLabTimeSlot(timeOfDay: string, slot: TimetableSlot, academicYear?: string, department?: string, semester?: string): Promise<string> {
    // Check if this is a lab slot
    if (slot.subject_type === 'lab' || slot.subject_type === 'laboratory') {
      // Try to get semester-specific configuration if available
      if (academicYear && department && semester) {
        try {
          const { SemesterLabConfigurationService } = await import('@/services/SemesterLabConfigurationService');
          const config = await SemesterLabConfigurationService.getLabConfiguration(academicYear, department, semester);

          if (config) {
            const timeSlotMapping = SemesterLabConfigurationService.getTimeSlotMapping(timeOfDay, config);
            if (timeSlotMapping) {
              return timeSlotMapping;
            }
          }
        } catch (error) {
          console.warn('Could not load semester lab configuration, falling back to defaults:', error);
        }
      }

      // Fallback to standard lab time slots for backward compatibility
      const morningLabSlot = '08:30-11:30'; // Morning lab hours: 8:30 AM to 11:30 AM
      const afternoonLabSlot = '13:15-16:00'; // Afternoon lab hours: 1:15 PM to 4:00 PM

      // Return the appropriate time slot based on time of day
      if (slot.time_slot.startsWith('08:30') ||
          (slot.time_slot && slot.time_slot.split('-')[0] < '12:00')) {
        return morningLabSlot;
      } else {
        return afternoonLabSlot;
      }
    }

    // If not a lab slot, return the original time slot
    return slot.time_slot;
  }

  /**
   * Save timetable slots to class_timetables table
   */
  static async saveToClassTimetables(params: {
    academicYear: string;
    department: string;
    semester: string;
    section: string;
    slots: TimetableSlot[];
  }): Promise<boolean> {
    try {
      const { academicYear, department, semester, section, slots } = params;

      console.log(`Saving ${slots.length} slots to class_timetables`);

      // Ensure the unassigned faculty exists before saving
      await this.ensureUnassignedFacultyExists();

      // First delete any existing entries
      await supabase
        .from('class_timetables')
        .delete()
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section);

      // Map slots to class_timetables format
      const classTimetableEntries = slots.map(slot => {
        // Determine the correct time slot for labs
        const timeSlot = this.getLabTimeSlot('', slot);

        // Create a base object with common fields
        const entry: any = {
          id: uuidv4(),
          academic_year: slot.academic_year,
          department: slot.department,
          semester: slot.semester,
          section: slot.section,
          day: slot.day,
          time_slot: timeSlot,
          subject_code: slot.subject_code,
          subject_type: slot.subject_type,
          room_number: slot.room_number || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // For skill labs, use a placeholder faculty ID
        // and mark the faculty name as "Unassigned" for display purposes
        if (slot.subject_code === "SKILL LAB") {
          // Use a placeholder UUID for skill labs instead of null to satisfy not-null constraint
          entry.faculty_id = this.UNASSIGNED_FACULTY_ID; // Placeholder ID for unassigned
          entry.faculty_name = "Unassigned";
          entry.faculty2_id = null;
          entry.faculty2_name = null;
          console.log(`Using placeholder faculty ID for Skill Lab in class_timetables`);
        } else {
          // For regular subjects, use the assigned faculty
          entry.faculty_id = slot.faculty_id;
          entry.faculty_name = slot.faculty_name || 'Not Assigned';
          entry.faculty2_id = slot.faculty2_id || null;
          entry.faculty2_name = slot.faculty2_name || null;
        }

        // Only add subject_name if it exists in the slot
        if (slot.subject_name) {
          entry.subject_name = slot.subject_name;
        } else {
          entry.subject_name = 'Unknown Subject';
        }

        return entry;
      });

      console.log(`Prepared ${classTimetableEntries.length} entries for class_timetables`);

      // Insert in batches to avoid payload size limits
      const batchSize = 100;
      for (let i = 0; i < classTimetableEntries.length; i += batchSize) {
        const batch = classTimetableEntries.slice(i, i + batchSize);
        const { error } = await supabase.from('class_timetables').insert(batch);
        if (error) {
          console.error("Error inserting batch to class_timetables:", error);
          throw error;
        }
      }

      console.log("Successfully saved to class_timetables");
      return true;
    } catch (error) {
      console.error("Error saving to class_timetables:", error);
      throw error;
    }
  }

  /**
   * Save timetable slots to faculty_timetables table
   */
  static async saveToFacultyTimetables(params: {
    academicYear: string;
    department: string;
    slots: TimetableSlot[];
  }): Promise<boolean> {
    try {
      const { academicYear, slots } = params;

      console.log(`Saving slots for faculty to faculty_timetables`);

      // Get unique faculty IDs (both primary and secondary)
      const facultyIds = new Set<string>();
      slots.forEach(slot => {
        if (slot.faculty_id) facultyIds.add(slot.faculty_id);
        if (slot.faculty2_id) facultyIds.add(slot.faculty2_id);
      });

      console.log(`Found ${facultyIds.size} unique faculty IDs`);

      if (facultyIds.size === 0) {
        console.warn("No faculty IDs found in slots, skipping faculty_timetables update");
        return true;
      }

      // Delete existing entries for these faculty members
      await supabase
        .from('faculty_timetables')
        .delete()
        .eq('academic_year', academicYear)
        .in('faculty_id', Array.from(facultyIds));

      // Create entries for both faculty1 and faculty2
      const facultyTimetableEntries = [];

      for (const slot of slots) {
        // Skip skill labs - they should not appear in faculty timetables
        if (slot.subject_code === "SKILL LAB") {
          console.log(`Skipping Skill Lab slot ${slot.id} for faculty timetables`);
          continue;
        }

        // Determine the correct time slot for labs
        const timeSlot = this.getLabTimeSlot('', slot);

        // Create base entry data
        const baseEntry = {
          academic_year: slot.academic_year,
          department: slot.department,
          day: slot.day,
          time_slot: timeSlot,
          subject_code: slot.subject_code,
          subject_short_id: slot.subject_short_id || slot.subject_code, // Include subject_short_id
          subject_name: slot.subject_name || 'Unknown Subject',
          subject_type: slot.subject_type,
          semester: slot.semester,
          section: slot.section,
          room_number: slot.room_number || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Add entry for faculty1 if exists
        if (slot.faculty_id) {
          facultyTimetableEntries.push({
            ...baseEntry,
            id: uuidv4(),
            faculty_id: slot.faculty_id,
            faculty2_id: slot.faculty2_id || null
          });
        }

        // Add entry for faculty2 if exists
        if (slot.faculty2_id) {
          facultyTimetableEntries.push({
            ...baseEntry,
            id: uuidv4(),
            faculty_id: slot.faculty2_id,
            faculty2_id: slot.faculty_id || null
          });
        }
      }

      console.log(`Prepared ${facultyTimetableEntries.length} entries for faculty_timetables`);

      // Insert in batches to avoid payload size limits
      const batchSize = 100;
      for (let i = 0; i < facultyTimetableEntries.length; i += batchSize) {
        const batch = facultyTimetableEntries.slice(i, i + batchSize);
        const { error } = await supabase.from('faculty_timetables').insert(batch);
        if (error) {
          console.error("Error inserting batch to faculty_timetables:", error);
          throw error;
        }
      }

      console.log("Successfully saved to faculty_timetables");
      return true;
    } catch (error) {
      console.error("Error saving to faculty_timetables:", error);
      throw error;
    }
  }
}
