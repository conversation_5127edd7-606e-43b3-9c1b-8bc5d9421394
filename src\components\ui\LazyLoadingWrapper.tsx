import React, { Suspense, useEffect } from 'react';
import { ModernLoading } from './modern-loading';

interface LazyLoadingWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loadingText?: string;
  componentName?: string;
}

/**
 * Wrapper component for lazy-loaded routes with professional loading state and performance monitoring
 */
export const LazyLoadingWrapper: React.FC<LazyLoadingWrapperProps> = ({
  children,
  fallback,
  loadingText = "Loading...",
  componentName
}) => {
  const startTime = React.useRef(performance.now());

  useEffect(() => {
    const endTime = performance.now();
    const loadTime = endTime - startTime.current;

    if (componentName && process.env.NODE_ENV === 'development') {
      console.log(`🚀 Lazy Load: ${componentName} loaded in ${loadTime.toFixed(2)}ms`);
    }
  }, [componentName]);

  const defaultFallback = (
    <div className="min-h-[60vh] flex items-center justify-center">
      <ModernLoading
        size="lg"
        text={loadingText}
        variant="dots"
      />
    </div>
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
};

/**
 * Higher-order component for wrapping lazy-loaded components
 */
export const withLazyLoading = <P extends object>(
  Component: React.ComponentType<P>,
  loadingText?: string
) => {
  return React.forwardRef<any, P>((props, ref) => (
    <LazyLoadingWrapper loadingText={loadingText}>
      <Component {...props} ref={ref} />
    </LazyLoadingWrapper>
  ));
};

export default LazyLoadingWrapper;
