import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useAttendanceRealtime } from '@/hooks/useAttendanceRealtime';
import {
  FacultyAnalyticsService,
  type DashboardData,
  type AttendanceTrendData,
  type StudentPerformanceData,
  type ClassSessionAnalytics,
  type SubjectAnalysis,
  type PredictiveInsight
} from '@/services/FacultyAnalyticsService';
import {
  FacultyAssignmentService,
  type FacultyAssignmentSummary
} from '@/services/FacultyAssignmentService';
import FacultyTimetableGrid from '@/components/faculty/FacultyTimetableGrid';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  ScatterChart,
  Scatter
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Minus,
  BarChart3,
  PieChart as PieChartIcon,
  Users,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle2,
  RefreshCw,
  Download,
  Eye,
  Target,
  FileText
} from 'lucide-react';

const FacultyAnalyticsDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [facultyAssignments, setFacultyAssignments] = useState<FacultyAssignmentSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedTab, setSelectedTab] = useState('overview');
  // Removed filters - dashboard shows all data automatically

  const { user } = useAuth();
  const { department } = useUserDepartment();
  const { toast } = useToast();

  // Real-time updates for all faculty assignments
  useAttendanceRealtime({
    facultyId: user?.id || '',
    subjectCode: '', // Monitor all subjects
    semester: '', // Monitor all semesters
    section: '', // Monitor all sections
    subjectType: '',
    onAttendanceUpdate: () => {
      if (autoRefresh) {
        loadDashboardData();
        toast({
          title: 'Dashboard Updated',
          description: 'Analytics refreshed with latest attendance data.',
        });
      }
    },
    enabled: autoRefresh
  });

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        loadDashboardData();
      }, 300000); // Refresh every 5 minutes

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // Load faculty assignments on component mount
  useEffect(() => {
    if (user?.id && department) {
      loadFacultyAssignments();
    }
  }, [user?.id, department]);

  // Load dashboard data when assignments are loaded or filters change
  useEffect(() => {
    if (user?.id && department && facultyAssignments) {
      loadDashboardData();
    }
  }, [user?.id, department, facultyAssignments]);

  const loadFacultyAssignments = async () => {
    if (!user?.id || !department) return;

    try {
      setAssignmentsLoading(true);
      const assignments = await FacultyAssignmentService.getFacultyAssignments(
        user.id,
        department
      );
      setFacultyAssignments(assignments);

      console.log('📊 Faculty assignments loaded:', {
        totalAssignments: assignments.assignments.length,
        totalTimetableSlots: assignments.timetable.length,
        weeklyTimetableKeys: Object.keys(assignments.weeklyTimetable),
        sampleAssignment: assignments.assignments[0],
        sampleTimetableSlot: assignments.timetable[0]
      });

      // No need to set filters - we'll use all assignments automatically
    } catch (error) {
      console.error('Error loading faculty assignments:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your teaching assignments.',
        variant: 'destructive',
      });
    } finally {
      setAssignmentsLoading(false);
    }
  };

  const loadDashboardData = async () => {
    if (!user?.id || !department) return;

    try {
      setLoading(true);

      // Use default filters for all faculty assignments
      const serviceFilters = {
        semester: '', // All semesters
        section: '', // All sections
        subject_code: '', // All subjects
        date_from: '', // All dates
        date_to: '', // All dates
        attendance_threshold: 75 // Default threshold
      };

      const data = await FacultyAnalyticsService.generateDashboardData(
        user.id,
        department,
        serviceFilters
      );
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load analytics dashboard.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'stable') => {
    switch (direction) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRiskBadgeVariant = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (!dashboardData || assignmentsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className={`h-8 w-8 mx-auto mb-4 ${(loading || assignmentsLoading) ? 'animate-spin' : ''}`} />
            <p className="text-muted-foreground">
              {assignmentsLoading ? 'Loading your teaching assignments...' :
               loading ? 'Loading analytics dashboard...' :
               'No data available'}
            </p>
            {assignmentsLoading && (
              <p className="text-sm text-muted-foreground mt-2">
                Fetching your subjects and class assignments...
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Teaching Analytics</h1>
          <p className="text-muted-foreground">
            Insights into your classes, student performance, and attendance patterns
          </p>
          {facultyAssignments && facultyAssignments.assignments.length > 0 && (
            <p className="text-sm text-muted-foreground mt-1">
              Teaching {facultyAssignments.subjects.length} subjects across {facultyAssignments.semesterSections.length} classes
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={loadDashboardData}
            disabled={loading}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Removed Analytics Filters - Dashboard auto-shows all faculty data */}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Subjects</p>
                <p className="text-2xl font-bold">{dashboardData.summary.total_subjects}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Students</p>
                <p className="text-2xl font-bold">{dashboardData.summary.total_students}</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Classes Conducted</p>
                <p className="text-2xl font-bold">{dashboardData.summary.total_classes}</p>
              </div>
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Attendance</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">{dashboardData.summary.average_attendance}%</p>
                  {getTrendIcon(dashboardData.summary.trend_direction)}
                  <span className="text-sm text-muted-foreground">
                    {dashboardData.summary.trend_percentage}%
                  </span>
                </div>
              </div>
              <Target className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="timetable">My Timetable</TabsTrigger>
          <TabsTrigger value="students">Students</TabsTrigger>
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Attendance Trends Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Attendance Trends Over Time
                </CardTitle>
                <CardDescription>
                  Daily attendance percentages across all subjects
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={dashboardData.attendance_trends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                    />
                    <YAxis domain={[0, 100]} />
                    <Tooltip
                      labelFormatter={(value) => new Date(value).toLocaleDateString()}
                      formatter={(value: any) => [`${value}%`, 'Attendance']}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="attendance_percentage"
                      stroke="#8884d8"
                      strokeWidth={2}
                      dot={{ fill: '#8884d8', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Subject Performance Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Subject Performance Overview
                </CardTitle>
                <CardDescription>
                  Attendance performance across your assigned subjects
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {dashboardData.subject_analysis.slice(0, 6).map((subject, index) => (
                    <div key={`${subject.subject_code}-${subject.semester}-${subject.section}`} className="flex items-center justify-between p-3 bg-muted rounded">
                      <div>
                        <span className="font-medium">{subject.subject_code}</span>
                        <span className="text-sm text-muted-foreground ml-2">
                          Sem {subject.semester} - Sec {subject.section}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${subject.attendance_percentage}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium w-12 text-right">
                          {subject.attendance_percentage}%
                        </span>
                        {subject.trend_direction === 'up' ? (
                          <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : subject.trend_direction === 'down' ? (
                          <TrendingDown className="h-4 w-4 text-red-600" />
                        ) : (
                          <Minus className="h-4 w-4 text-gray-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* At-Risk Students Alert */}
          {dashboardData.predictive_insights.length > 0 && (
            <Card className="border-orange-200 bg-orange-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-800">
                  <AlertTriangle className="h-5 w-5" />
                  Students Requiring Attention
                </CardTitle>
                <CardDescription className="text-orange-700">
                  {dashboardData.predictive_insights.length} students below attendance threshold
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {dashboardData.predictive_insights.slice(0, 6).map((insight) => (
                    <div key={insight.student_usn} className="p-4 bg-white rounded-lg border">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{insight.student_name}</h4>
                        <Badge variant="destructive">{insight.current_percentage}%</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">USN: {insight.student_usn}</p>
                      <div className="space-y-1">
                        {insight.risk_factors.slice(0, 2).map((factor, index) => (
                          <p key={index} className="text-xs text-orange-700">• {factor}</p>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
                {dashboardData.predictive_insights.length > 6 && (
                  <div className="mt-4 text-center">
                    <Button variant="outline" onClick={() => setSelectedTab('insights')}>
                      View All {dashboardData.predictive_insights.length} Students
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* My Timetable Tab */}
        <TabsContent value="timetable" className="space-y-6">
          <FacultyTimetableGrid
            weeklyTimetable={facultyAssignments?.weeklyTimetable || {}}
            loading={assignmentsLoading}
          />

          {/* Subject Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Subject Performance Summary
              </CardTitle>
              <CardDescription>
                Quick overview of attendance performance across your subjects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {dashboardData.subject_analysis.map((subject) => (
                  <div key={`${subject.subject_code}-${subject.semester}-${subject.section}`} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">{subject.subject_code}</h4>
                      <Badge variant={subject.attendance_percentage >= 75 ? 'default' : 'destructive'}>
                        {subject.attendance_percentage}%
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Sem {subject.semester} - Sec {subject.section}
                    </p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{subject.total_students} students</span>
                      <span>{subject.total_classes} classes</span>
                      <span className={`${
                        subject.trend_direction === 'up' ? 'text-green-600' :
                        subject.trend_direction === 'down' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {subject.trend_direction === 'up' ? '↗' :
                         subject.trend_direction === 'down' ? '↘' : '→'} {subject.trend_percentage}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Students Tab */}
        <TabsContent value="students" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Student Performance Heatmap
              </CardTitle>
              <CardDescription>
                Individual student attendance patterns across subjects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.student_performance.slice(0, 10).map((student) => (
                  <div key={student.student_usn} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-semibold">{student.student_name}</h4>
                        <p className="text-sm text-muted-foreground">USN: {student.student_usn}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getRiskBadgeVariant(student.risk_level)}>
                          {student.overall_percentage}%
                        </Badge>
                        <Badge variant="outline">{student.risk_level.toUpperCase()} RISK</Badge>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {student.subjects.map((subject) => (
                        <div key={subject.subject_code} className="p-2 bg-muted rounded text-sm">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{subject.subject_code}</span>
                            <Badge
                              variant={getRiskBadgeVariant(subject.risk_level)}
                              className="text-xs"
                            >
                              {subject.attendance_percentage}%
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <span className="text-xs text-muted-foreground">
                              {subject.attended_classes}/{subject.total_classes}
                            </span>
                            <span className={`text-xs ${
                              subject.trend === 'improving' ? 'text-green-600' :
                              subject.trend === 'declining' ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {subject.trend === 'improving' ? '↗' :
                               subject.trend === 'declining' ? '↘' : '→'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sessions Tab */}
        <TabsContent value="sessions" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Time Slot Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Time Slot Analysis
                </CardTitle>
                <CardDescription>
                  Attendance performance by time slots
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={dashboardData.session_analytics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time_slot" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip formatter={(value: any) => [`${value}%`, 'Average Attendance']} />
                    <Bar dataKey="average_attendance" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Day of Week Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Day of Week Performance
                </CardTitle>
                <CardDescription>
                  Attendance patterns by days of the week
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day) => {
                    const dayData = dashboardData.session_analytics.filter(s => s.day_of_week === day);
                    const avgAttendance = dayData.length > 0 ?
                      dayData.reduce((sum, s) => sum + s.average_attendance, 0) / dayData.length : 0;

                    return (
                      <div key={day} className="flex items-center justify-between p-3 bg-muted rounded">
                        <span className="font-medium">{day}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${avgAttendance}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium w-12 text-right">
                            {Math.round(avgAttendance)}%
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Session Details Table */}
          <Card>
            <CardHeader>
              <CardTitle>Session Details</CardTitle>
              <CardDescription>
                Detailed breakdown of all class sessions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Time Slot</th>
                      <th className="text-left p-2">Day</th>
                      <th className="text-left p-2">Subject Type</th>
                      <th className="text-right p-2">Avg Attendance</th>
                      <th className="text-right p-2">Total Sessions</th>
                      <th className="text-right p-2">Trend</th>
                    </tr>
                  </thead>
                  <tbody>
                    {dashboardData.session_analytics.map((session, index) => (
                      <tr key={index} className="border-b hover:bg-muted/50">
                        <td className="p-2 font-medium">{session.time_slot}</td>
                        <td className="p-2">{session.day_of_week}</td>
                        <td className="p-2">
                          <Badge variant={session.subject_type === 'lab' ? 'secondary' : 'default'}>
                            {session.subject_type.toUpperCase()}
                          </Badge>
                        </td>
                        <td className="p-2 text-right">
                          <Badge variant={session.average_attendance >= 75 ? 'default' : 'destructive'}>
                            {session.average_attendance}%
                          </Badge>
                        </td>
                        <td className="p-2 text-right">{session.total_sessions}</td>
                        <td className="p-2 text-right">
                          <span className={`text-sm ${
                            session.attendance_trend > 0 ? 'text-green-600' :
                            session.attendance_trend < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {session.attendance_trend > 0 ? '+' : ''}{session.attendance_trend}%
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          {/* Predictive Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Predictive Insights & Recommendations
              </CardTitle>
              <CardDescription>
                AI-powered insights for student intervention and support
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData.predictive_insights.map((insight) => (
                  <div key={insight.student_usn} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold">{insight.student_name}</h4>
                        <p className="text-sm text-muted-foreground">USN: {insight.student_usn}</p>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm text-muted-foreground">Current:</span>
                          <Badge variant="destructive">{insight.current_percentage}%</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">Predicted:</span>
                          <Badge variant={insight.predicted_percentage >= 75 ? 'default' : 'destructive'}>
                            {insight.predicted_percentage}%
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-medium text-sm mb-2 text-red-700">Risk Factors:</h5>
                        <ul className="space-y-1">
                          {insight.risk_factors.map((factor, index) => (
                            <li key={index} className="text-sm text-red-600">• {factor}</li>
                          ))}
                        </ul>
                        {insight.subjects_at_risk.length > 0 && (
                          <div className="mt-2">
                            <span className="text-sm font-medium">Subjects at risk: </span>
                            {insight.subjects_at_risk.map((subject, index) => (
                              <Badge key={index} variant="outline" className="mr-1 text-xs">
                                {subject}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                      <div>
                        <h5 className="font-medium text-sm mb-2 text-blue-700">Recommended Actions:</h5>
                        <ul className="space-y-1">
                          {insight.recommended_actions.map((action, index) => (
                            <li key={index} className="text-sm text-blue-600">• {action}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Recommended actions based on your analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                  <Users className="h-5 w-5 mb-2" />
                  <span className="font-medium">Contact At-Risk Students</span>
                  <span className="text-sm text-muted-foreground">
                    {dashboardData.predictive_insights.length} students need attention
                  </span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                  <Calendar className="h-5 w-5 mb-2" />
                  <span className="font-medium">Schedule Extra Classes</span>
                  <span className="text-sm text-muted-foreground">
                    For subjects below 75% attendance
                  </span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                  <FileText className="h-5 w-5 mb-2" />
                  <span className="font-medium">Generate Reports</span>
                  <span className="text-sm text-muted-foreground">
                    Export detailed analytics
                  </span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FacultyAnalyticsDashboard;
