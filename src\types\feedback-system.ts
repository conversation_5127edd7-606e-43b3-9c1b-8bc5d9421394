// Student Faculty Feedback System Types

export interface FeedbackQuestion {
  id: string;
  question_number: number;
  question_text: string;
  category: string;
  is_active: boolean;
  created_at: string;
}

export interface FeedbackSession {
  id: string;
  session_name: string;
  academic_year: string;
  department: string;
  semester?: string;
  section?: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface StudentFeedbackResponse {
  id: string;
  session_id: string;
  student_usn: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  faculty_id: string;
  subject_code: string;
  subject_name: string;
  responses: FeedbackQuestionResponse[];
  submitted_at: string;
  is_submitted: boolean;
}

export interface FeedbackQuestionResponse {
  question_id: string;
  question_number: number;
  rating: number; // 1-10 scale
}

export interface FeedbackReport {
  id: string;
  session_id: string;
  faculty_id: string;
  faculty_name: string;
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section: string;
  total_responses: number;
  average_ratings: FeedbackQuestionAverage[];
  overall_percentage: number;
  anonymized_responses: AnonymizedResponse[];
  generated_at: string;
}

export interface FeedbackQuestionAverage {
  question_id: string;
  question_number: number;
  question_text: string;
  average_rating: number;
  total_marks: number;
  possible_marks: number;
  percentage: number;
}

export interface AnonymizedResponse {
  masked_usn: string; // First 5 characters + ****
  responses: FeedbackQuestionResponse[];
  submitted_at: string;
}

// Dashboard Data Interfaces
export interface HODFeedbackDashboard {
  active_sessions: FeedbackSession[];
  completed_sessions: FeedbackSession[];
  department_reports: FeedbackReport[];
  submission_statistics: {
    total_students: number;
    submitted_responses: number;
    pending_responses: number;
    completion_percentage: number;
  };
}

export interface StudentFeedbackDashboard {
  available_sessions: FeedbackSession[];
  pending_feedback: PendingFeedback[];
  completed_feedback: CompletedFeedback[];
  submission_status: {
    total_subjects: number;
    completed_subjects: number;
    pending_subjects: number;
  };
}

export interface PendingFeedback {
  session_id: string;
  faculty_id: string;
  faculty_name: string;
  subject_code: string;
  subject_name: string;
  subject_type: string;
  deadline: string;
}

export interface CompletedFeedback {
  session_id: string;
  faculty_name: string;
  subject_code: string;
  subject_name: string;
  submitted_at: string;
}

export interface ClassTeacherFeedbackTracking {
  session_id: string;
  session_name: string;
  department: string;
  semester: string;
  section: string;
  total_students: number;
  total_subjects: number;
  submission_summary: SubjectSubmissionSummary[];
  student_submission_status: StudentSubmissionStatus[];
}

export interface SubjectSubmissionSummary {
  faculty_id: string;
  faculty_name: string;
  subject_code: string;
  subject_name: string;
  total_students: number;
  submitted_count: number;
  pending_count: number;
  completion_percentage: number;
}

export interface StudentSubmissionStatus {
  student_usn: string;
  student_name: string;
  total_subjects: number;
  completed_subjects: number;
  pending_subjects: string[]; // Subject codes
  completion_percentage: number;
}

// Form Interfaces
export interface CreateFeedbackSessionRequest {
  session_name: string;
  academic_year: string;
  department: string;
  semester?: string;
  section?: string;
  start_date: string;
  end_date: string;
}

export interface SubmitFeedbackRequest {
  session_id: string;
  faculty_id: string;
  subject_code: string;
  responses: FeedbackQuestionResponse[];
}

// Analytics Interfaces
export interface FeedbackAnalytics {
  session_id: string;
  department_summary: DepartmentFeedbackSummary;
  faculty_rankings: FacultyRanking[];
  question_analysis: QuestionAnalysis[];
  trends: FeedbackTrend[];
}

export interface DepartmentFeedbackSummary {
  department: string;
  total_faculty: number;
  total_responses: number;
  average_rating: number;
  overall_percentage: number;
  top_performers: string[];
  improvement_areas: string[];
}

export interface FacultyRanking {
  faculty_id: string;
  faculty_name: string;
  subject_code: string;
  subject_name: string;
  overall_percentage: number;
  total_responses: number;
  rank: number;
}

export interface QuestionAnalysis {
  question_id: string;
  question_text: string;
  department_average: number;
  highest_rating: number;
  lowest_rating: number;
  standard_deviation: number;
}

export interface FeedbackTrend {
  session_name: string;
  session_date: string;
  overall_percentage: number;
  response_count: number;
}

// HOD Faculty Reports Interfaces
export interface HODFacultyReportData {
  sessions: FeedbackSession[];
  facultyReports: HODFacultyReport[];
  departmentSummary: HODDepartmentSummary;
  totalReports: number;
}

export interface HODFacultyReport {
  faculty_id: string;
  faculty_name: string;
  employee_id?: string;
  subjects: HODSubjectReport[];
  total_responses: number;
  average_rating: number;
  overall_percentage: number;
  performance_metrics?: FacultyPerformanceMetrics;
}

export interface HODSubjectReport {
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: string;
  section: string;
  total_responses: number;
  overall_percentage: number;
  average_ratings: FeedbackQuestionAverage[];
}

export interface HODDepartmentSummary {
  department: string;
  total_faculty: number;
  total_reports: number;
  average_rating: number;
  total_responses: number;
  top_performers: FacultyPerformanceSummary[];
  improvement_areas: FacultyPerformanceSummary[];
}

export interface FacultyPerformanceSummary {
  faculty_id: string;
  faculty_name: string;
  average_rating: number;
  total_responses: number;
}

export interface FacultyPerformanceMetrics {
  total_responses: number;
  average_rating: number;
  rating_distribution: { [key: number]: number };
  strengths: QuestionPerformance[];
  improvement_areas: QuestionPerformance[];
}

export interface QuestionPerformance {
  question_id: string;
  average: number;
}

export interface ConsolidatedDepartmentReport {
  session_id: string;
  department: string;
  generated_by: string;
  generated_at: string;
  faculty_summary: ConsolidatedFacultySummary[];
  department_metrics: ConsolidatedDepartmentMetrics;
}

export interface ConsolidatedFacultySummary {
  faculty_id: string;
  faculty_name: string;
  employee_id: string;
  subjects_taught: ConsolidatedSubjectInfo[];
  total_responses: number;
  average_rating: number;
  performance_metrics: FacultyPerformanceMetrics;
}

export interface ConsolidatedSubjectInfo {
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: number;
  section: string;
  response_count: number;
}

export interface ConsolidatedDepartmentMetrics {
  total_faculty: number;
  total_responses: number;
  average_department_rating: number;
  top_performers: ConsolidatedFacultySummary[];
  improvement_areas: ConsolidatedFacultySummary[];
}
