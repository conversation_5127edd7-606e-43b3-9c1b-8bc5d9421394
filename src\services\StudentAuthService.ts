import { supabase } from "@/integrations/supabase/client";

export interface StudentAuthData {
  student_id: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  first_login: boolean;
}

export interface StudentLoginCredentials {
  roll_number: string;
  password: string;
}

export interface PasswordChangeData {
  student_id: string;
  current_password: string;
  new_password: string;
}

export interface PasswordResetData {
  roll_number: string;
  student_email: string;
}

export class StudentAuthService {
  /**
   * Authenticate student with roll number and password
   */
  static async authenticateStudent(credentials: StudentLoginCredentials): Promise<StudentAuthData | null> {
    try {
      const { data, error } = await supabase.rpc('authenticate_student', {
        p_roll_number: credentials.roll_number,
        p_password: credentials.password
      });

      if (error) {
        console.error('Authentication error:', error);
        throw new Error('Invalid roll number or password');
      }

      if (!data || data.length === 0) {
        return null;
      }

      const studentData = data[0];
      
      // Update login tracking
      await supabase.rpc('update_student_login', {
        p_student_id: studentData.student_id
      });

      return {
        student_id: studentData.student_id,
        student_name: studentData.student_name,
        department: studentData.department,
        semester: studentData.semester,
        section: studentData.section,
        first_login: studentData.first_login
      };
    } catch (error) {
      console.error('Error authenticating student:', error);
      throw error;
    }
  }

  /**
   * Change student password
   */
  static async changePassword(passwordData: PasswordChangeData): Promise<void> {
    try {
      // First verify current password
      const { data: student, error: verifyError } = await supabase
        .from('students')
        .select('roll_number')
        .eq('id', passwordData.student_id)
        .eq('password_hash', passwordData.current_password)
        .single();

      if (verifyError || !student) {
        throw new Error('Current password is incorrect');
      }

      // Update password
      const { error: updateError } = await supabase
        .from('students')
        .update({ 
          password_hash: passwordData.new_password,
          first_login: false
        })
        .eq('id', passwordData.student_id);

      if (updateError) {
        throw new Error('Failed to update password');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }

  /**
   * Generate password reset token and send email
   */
  static async requestPasswordReset(resetData: PasswordResetData): Promise<void> {
    try {
      // Verify student exists and email matches
      const { data: student, error: studentError } = await supabase
        .from('students')
        .select('id, student_name, student_email')
        .eq('roll_number', resetData.roll_number)
        .eq('student_email', resetData.student_email)
        .eq('is_active', true)
        .single();

      if (studentError || !student) {
        throw new Error('Student not found or email does not match');
      }

      // Generate reset token
      const { data: token, error: tokenError } = await supabase.rpc('generate_password_reset_token', {
        p_roll_number: resetData.roll_number
      });

      if (tokenError || !token) {
        throw new Error('Failed to generate reset token');
      }

      // Send reset email (this would integrate with your email service)
      await this.sendPasswordResetEmail(student.student_email, student.student_name, token);
    } catch (error) {
      console.error('Error requesting password reset:', error);
      throw error;
    }
  }

  /**
   * Reset password using token
   */
  static async resetPassword(token: string, newPassword: string): Promise<boolean> {
    try {
      const { data: success, error } = await supabase.rpc('reset_student_password', {
        p_token: token,
        p_new_password: newPassword
      });

      if (error) {
        throw new Error('Failed to reset password');
      }

      return success;
    } catch (error) {
      console.error('Error resetting password:', error);
      throw error;
    }
  }

  /**
   * Get student profile by ID
   */
  static async getStudentProfile(studentId: string): Promise<StudentAuthData | null> {
    try {
      const { data, error } = await supabase
        .from('students')
        .select('id, student_name, department, semester, section, first_login')
        .eq('id', studentId)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        return null;
      }

      return {
        student_id: data.id,
        student_name: data.student_name,
        department: data.department,
        semester: data.semester,
        section: data.section,
        first_login: data.first_login
      };
    } catch (error) {
      console.error('Error fetching student profile:', error);
      return null;
    }
  }

  /**
   * Send password reset email (placeholder - integrate with actual email service)
   */
  private static async sendPasswordResetEmail(email: string, studentName: string, token: string): Promise<void> {
    // This is a placeholder for email integration
    // In production, you would integrate with Supabase Edge Functions or external email service
    console.log(`Password reset email would be sent to ${email} for ${studentName} with token: ${token}`);
    
    // For now, we'll just log the reset link
    const resetLink = `${window.location.origin}/student-auth?tab=reset-password&token=${token}`;
    console.log(`Reset link: ${resetLink}`);
    
    // TODO: Implement actual email sending
    // This could be done via:
    // 1. Supabase Edge Functions
    // 2. External email service (SendGrid, Mailgun, etc.)
    // 3. SMTP integration
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string): { isValid: boolean; message: string } {
    if (password.length < 6) {
      return { isValid: false, message: 'Password must be at least 6 characters long' };
    }
    
    if (!/(?=.*[a-z])/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one lowercase letter' };
    }
    
    if (!/(?=.*[A-Z])/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one uppercase letter' };
    }
    
    if (!/(?=.*\d)/.test(password)) {
      return { isValid: false, message: 'Password must contain at least one number' };
    }
    
    return { isValid: true, message: 'Password is valid' };
  }

  /**
   * Check if roll number exists
   */
  static async checkRollNumberExists(rollNumber: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('students')
        .select('id')
        .eq('roll_number', rollNumber)
        .eq('is_active', true)
        .single();

      return !error && !!data;
    } catch (error) {
      return false;
    }
  }
}
