
import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";
import { TimetableService, TimeStructure } from "./TimetableService";
import { TimeSlotValidationService } from "./TimeSlotValidationService";
import { useSubjectMappingStore } from "@/stores/SubjectMappingStore";

export class LabSlotGenerator {
  private unallocatedLabs: string[] = [];

  getUnallocatedLabs(): string[] {
    return this.unallocatedLabs;
  }

  async generateLabSlots({ academicYear, department, semester, section }: {
    academicYear: string;
    department: string;
    semester: string;
    section: string;
  }) {
    this.unallocatedLabs = [];
    try {
      console.log("Starting lab slot generation for:", { academicYear, department, semester, section });

      // Step 1: Fetch the time structure
      const timeStructure = await TimetableService.fetchTimeStructure({ academicYear, department });
      if (!timeStructure) {
        console.error("No time structure found");
        throw new Error("Time structure not defined for this selection");
      }

      // Step 2: Fetch laboratory subject mappings
      console.log("Fetching laboratory subject mappings...");

      // Try the simplified table first
      const { data: simplifiedData, error: simplifiedError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          id,
          academic_year,
          department,
          semester,
          section,
          subject_id,
          subject_code,
          subject_name,
          subject_type,
          faculty_1_id,
          faculty_2_id,
          hours_per_week,
          classroom,
          slots_per_week,
          subjects(subject_short_id)
        `)
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['laboratory', 'lab'])
        .not('subject_code', 'like', 'DELETED_%');

      // If simplified table query fails or returns no data, try the original table
      let labMappings;
      let mappingsError;

      if (simplifiedError || !simplifiedData || simplifiedData.length === 0) {
        console.log("No lab mappings found in simplified table, trying original table");

        // Fall back to the original table
        const { data: originalData, error: originalError } = await supabase
          .from('subject_faculty_mappings')
          .select(`
            id,
            academic_year,
            department,
            semester,
            section,
            subject_id,
            subject_code,
            subject_name,
            subject_type,
            faculty_1_id,
            faculty_2_id,
            hours_per_week,
            classroom,
            slots_per_week,
            subjects(subject_short_id)
          `)
          .eq('academic_year', academicYear)
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section)
          .in('subject_type', ['laboratory', 'lab']);

        labMappings = originalData;
        mappingsError = originalError;
      } else {
        console.log(`Found ${simplifiedData.length} lab mappings in simplified table`);
        labMappings = simplifiedData;
        mappingsError = simplifiedError;
      }

      if (mappingsError) {
        console.error("Error fetching lab mappings:", mappingsError);
        throw mappingsError;
      }

      // Log all lab mappings to identify any issues
      console.log(`Found ${labMappings?.length || 0} lab mappings:`,
        labMappings?.map(m => `${m.subject_code} - ${m.subject_name}`));

      // Verify we have lab mappings
      if (!labMappings || labMappings.length === 0) {
        console.log("No laboratory subject mappings found");
        return [];
      }

      console.log(`Found ${labMappings.length} laboratory subject mappings:`, labMappings);

      // Step 3: Fetch faculty details for each mapping
      const facultyIds = new Set<string>();
      labMappings.forEach(mapping => {
        if (mapping.faculty_1_id) facultyIds.add(mapping.faculty_1_id);
        if (mapping.faculty_2_id) facultyIds.add(mapping.faculty_2_id);
      });

      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name')
        .in('id', Array.from(facultyIds));

      if (facultyError) {
        console.error("Error fetching faculty details:", facultyError);
        throw facultyError;
      }

      // Create a map of faculty IDs to names
      const facultyMap: Record<string, string> = {};
      facultyData?.forEach(faculty => {
        facultyMap[faculty.id] = faculty.full_name;
      });

      // Step 3.5: Fetch faculty timetables across ALL classes
      const facultyIdsForTimetables = labMappings.flatMap(mapping => {
        const ids = [];
        if (mapping.faculty_1_id) ids.push(mapping.faculty_1_id);
        if (mapping.faculty_2_id) ids.push(mapping.faculty_2_id);
        return ids;
      });

      const { data: facultySlots, error: facultyTimetableError } = await supabase
        .from('timetable_slots')
        .select('*')
        .eq('academic_year', academicYear)
        .in('faculty_id', facultyIdsForTimetables);

      if (facultyTimetableError) {
        console.error("Error fetching faculty timetables:", facultyTimetableError);
        throw facultyTimetableError;
      }

      console.log(`Found ${facultySlots?.length || 0} existing slots for faculties across all classes`);

      // Simplified - removed complex faculty timetable logic

      // When creating lab slots, check facultyTimetables instead of just local assignments
      // ...rest of the method with modified faculty clash checking

      // Step 4: Fetch lab time slots for batch information
      const { data: labSlotData, error: labSlotError } = await supabase
        .from('lab_time_slots')
        .select('*')
        .in('mapping_id', labMappings.map(mapping => mapping.id));

      if (labSlotError) {
        console.error("Error fetching lab time slots:", labSlotError);
        throw labSlotError;
      }

      console.log(`Found ${labSlotData?.length || 0} lab time slots`);

      // Group lab time slots by mapping_id and log to identify missing slots
      const labSlotsByMapping: Record<string, Array<any>> = {};
      if (labSlotData) {
        labSlotData.forEach(slot => {
          if (!labSlotsByMapping[slot.mapping_id]) {
            labSlotsByMapping[slot.mapping_id] = [];
          }
          labSlotsByMapping[slot.mapping_id].push(slot);
        });
      }

      // Log which mappings have slots and which don't
      labMappings?.forEach(mapping => {
        const slots = labSlotsByMapping[mapping.id] || [];
        console.log(`${mapping.subject_code} - ${mapping.subject_name}: ${slots.length} slots`);
        if (slots.length === 0) {
          console.warn(`WARNING: No lab time slots found for ${mapping.subject_code} - ${mapping.subject_name}`);
        }
      });

      // Step 5: Clear existing lab slots before generating new ones
      await TimetableService.clearTimetable({
        academicYear,
        department,
        semester,
        section,
        subject_type: 'lab'
      });

      // Step 6: Generate timetable slots for each lab subject
      const generatedSlots = [];

      // Add logging to track batch creation
      console.log(`Processing ${labMappings.length} lab mappings for slot generation`);

      for (const mapping of labMappings) {
        // Skip if no batch information is available
        const batchSlots = labSlotsByMapping[mapping.id];

        // Log detailed information about each mapping and its slots
        console.log(`Lab mapping ${mapping.subject_code}: ${batchSlots?.length || 0} batch slots`);

        if (!batchSlots || batchSlots.length === 0) {
          console.warn(`No batch information found for mapping ${mapping.id} (${mapping.subject_name}). Creating default slots.`);

          // Create default slots for this mapping - ensure we create at least 1 slot
          // This is where we might need to add code to create default slots

          // ... existing code for default slot creation if any ...
        }

        // Calculate hours per slot (lab session)
        const hoursPerWeek = mapping.hours_per_week || 0;
        const slotsPerWeek = mapping.slots_per_week || 1;
        const hoursPerSlot = Math.ceil(hoursPerWeek / slotsPerWeek);
        const periodsPerSlot = hoursPerSlot;

        for (const batchSlot of batchSlots) {
          // Get day and time slot information
          const day = batchSlot.day;
          const timeOfDay = batchSlot.time_of_day;
          const batchName = batchSlot.batch_name;

          // Parse time slot information using the validation service
          const timeSlotInfo = TimeSlotValidationService.parseTimeSlotInfo(timeOfDay, timeStructure);

          let startPeriod = 1;
          let actualDuration = periodsPerSlot;
          let timeSlots: string[] = [];

          if (timeSlotInfo) {
            startPeriod = timeSlotInfo.periods[0] || 1;
            actualDuration = timeSlotInfo.duration;
            timeSlots = timeSlotInfo.timeSlots;

            console.log(`Lab ${mapping.subject_code} on ${day}: Using ${timeSlotInfo.isFlexibleFormat ? 'flexible' : 'predefined'} format`);
            console.log(`  Time: ${timeSlotInfo.startTime}-${timeSlotInfo.endTime}`);
            console.log(`  Duration: ${actualDuration} periods`);
            console.log(`  Periods: ${timeSlotInfo.periods.join(', ')}`);
            console.log(`  Time slots: ${timeSlots.join(', ')}`);
          } else {
            console.warn(`Could not parse time slot info for ${timeOfDay}, using defaults`);

            // Fallback logic for backward compatibility
            if (timeOfDay === 'Afternoon') {
              startPeriod = timeStructure.periods_in_first_half + 1; // Start after lunch
            } else if (timeOfDay === 'Evening') {
              startPeriod = timeStructure.periods_in_first_half + Math.floor(timeStructure.periods_in_second_half / 2) + 1;
            }

            // Generate simple time slots based on time structure
            const allSlots = [];
            let currentTime = timeStructure.first_half_start_time;
            for (let i = 0; i < 7; i++) { // Generate 7 periods
              const endTime = this.addMinutesToTime(currentTime, timeStructure.theory_class_duration);
              allSlots.push(`${currentTime}-${endTime}`);
              currentTime = endTime;
              // Skip tea break and lunch break times
              if (currentTime === timeStructure.tea_break_start_time) {
                currentTime = timeStructure.tea_break_end_time;
              }
              if (currentTime === timeStructure.lunch_break_start_time) {
                currentTime = timeStructure.lunch_break_end_time;
              }
            }
            timeSlots = allSlots.slice(startPeriod - 1, startPeriod - 1 + actualDuration);
          }

          // Create lab slots for this batch using the actual time slots
          for (let periodOffset = 0; periodOffset < actualDuration; periodOffset++) {
            const currentPeriod = startPeriod + periodOffset;

            // Get the actual time slot for this period
            const timeSlot = timeSlots[periodOffset] || "";

            if (!timeSlot) {
              console.warn(`No time slot found for period offset ${periodOffset} in lab ${mapping.subject_code}`);
              continue;
            }

            // Create regular lab slot
            const timetableSlot = {
              id: uuidv4(),
              academic_year: academicYear,
              department: department,
              semester: semester,
              section: section,
              day: day,
              time_slot: timeSlot,
              subject_id: mapping.subject_id,
              subject_code: mapping.subject_code,
              subject_name: mapping.subject_name,
              subject_type: 'lab',
              faculty_id: mapping.faculty_1_id,
              faculty_name: facultyMap[mapping.faculty_1_id] || 'Unknown Faculty',
              faculty2_id: mapping.faculty_2_id,
              faculty2_name: mapping.faculty_2_id ? (facultyMap[mapping.faculty_2_id] || 'Unknown Faculty') : undefined,
              room_number: mapping.classroom,
              subject_short_id: mapping.subjects?.subject_short_id,
              batch_name: batchName,
              is_lab_start: periodOffset === 0,
              is_processed: periodOffset !== 0,
              is_fixed: true,
              priority: 'HIGH',
              movable: false,
              col_span: actualDuration,
              spans_tea_break: false, // Simplified - no tea break spanning logic
              is_hidden: periodOffset !== 0,
              is_full_session_lab: true
            };

            // Save the slot to the database
            try {
              console.log(`Saving lab slot for ${mapping.subject_code}, day: ${day}, period: ${currentPeriod}, batch: ${batchName}`);
              const { data, error } = await supabase.from('timetable_slots').insert(timetableSlot).select().single();

              if (error) {
                console.error(`Error saving lab slot:`, error);
              } else {
                console.log(`Lab slot saved successfully:`, data);
                generatedSlots.push(data);
              }
            } catch (error) {
              console.error(`Error saving lab slot:`, error);
            }


          }
        }
      }

      console.log(`Total lab slots generated: ${generatedSlots.length}`);
      return generatedSlots;
    } catch (error) {
      console.error("Error generating lab slots:", error);
      throw error;
    }
  }

  // Simple helper method to add minutes to time
  private addMinutesToTime(timeStr: string, minutes: number): string {
    const [hours, mins] = timeStr.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, mins, 0, 0);
    date.setMinutes(date.getMinutes() + minutes);
    return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  }
}
