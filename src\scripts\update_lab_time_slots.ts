// Script to update lab_time_slots table to reference simplified_subject_faculty_mappings
// instead of subject_faculty_mappings

import { supabase } from "../integrations/supabase/client";
import fs from 'fs';
import path from 'path';

async function executeQuery(query: string): Promise<any> {
  console.log("Executing query:", query);
  const { data, error } = await supabase.rpc('pgcall', { query });
  
  if (error) {
    console.error("Error executing query:", error);
    throw error;
  }
  
  console.log("Query result:", data);
  return data;
}

async function updateLabTimeSlots() {
  try {
    console.log("Starting lab_time_slots table update...");
    
    // Step 1: Create a new column to reference simplified_subject_faculty_mappings
    console.log("Step 1: Adding simplified_mapping_id column...");
    await executeQuery(`
      ALTER TABLE lab_time_slots 
      ADD COLUMN IF NOT EXISTS simplified_mapping_id UUID;
    `);
    
    // Step 2: Update the new column with corresponding IDs from simplified_subject_faculty_mappings
    console.log("Step 2: Updating simplified_mapping_id with corresponding values...");
    await executeQuery(`
      UPDATE lab_time_slots AS lts
      SET simplified_mapping_id = ssm.id
      FROM subject_faculty_mappings AS sfm
      JOIN simplified_subject_faculty_mappings AS ssm ON 
          sfm.academic_year = ssm.academic_year AND
          sfm.department = ssm.department AND
          sfm.semester = ssm.semester AND
          sfm.section = ssm.section AND
          sfm.subject_code = ssm.subject_code AND
          sfm.faculty_1_id = ssm.faculty_1_id
      WHERE lts.mapping_id = sfm.id;
    `);
    
    // Step 3: Check if any rows couldn't be updated (missing simplified mappings)
    console.log("Step 3: Checking for missing mappings...");
    const missingMappings = await executeQuery(`
      SELECT COUNT(*) AS missing_mappings
      FROM lab_time_slots
      WHERE simplified_mapping_id IS NULL;
    `);
    
    if (missingMappings && missingMappings[0]?.missing_mappings > 0) {
      console.warn(`Warning: ${missingMappings[0].missing_mappings} lab time slots could not be mapped to simplified_subject_faculty_mappings`);
      
      // Get details of the missing mappings
      const missingDetails = await executeQuery(`
        SELECT lts.id, lts.mapping_id, sfm.subject_code, sfm.academic_year, sfm.department, sfm.semester, sfm.section
        FROM lab_time_slots lts
        JOIN subject_faculty_mappings sfm ON lts.mapping_id = sfm.id
        WHERE lts.simplified_mapping_id IS NULL;
      `);
      
      console.log("Missing mapping details:", missingDetails);
      
      // Create the missing mappings in simplified_subject_faculty_mappings
      console.log("Creating missing mappings in simplified_subject_faculty_mappings...");
      await executeQuery(`
        INSERT INTO simplified_subject_faculty_mappings (
          id, academic_year, department, semester, section, 
          subject_id, subject_code, subject_name, subject_type,
          faculty_1_id, faculty_2_id, hours_per_week, classroom, slots_per_week
        )
        SELECT 
          gen_random_uuid(), sfm.academic_year, sfm.department, sfm.semester, sfm.section,
          sfm.subject_id, sfm.subject_code, sfm.subject_name, sfm.subject_type,
          sfm.faculty_1_id, sfm.faculty_2_id, sfm.hours_per_week, sfm.classroom, sfm.slots_per_week
        FROM subject_faculty_mappings sfm
        JOIN lab_time_slots lts ON lts.mapping_id = sfm.id
        WHERE lts.simplified_mapping_id IS NULL
        ON CONFLICT DO NOTHING;
      `);
      
      // Update the simplified_mapping_id again
      console.log("Updating simplified_mapping_id for previously missing mappings...");
      await executeQuery(`
        UPDATE lab_time_slots AS lts
        SET simplified_mapping_id = ssm.id
        FROM subject_faculty_mappings AS sfm
        JOIN simplified_subject_faculty_mappings AS ssm ON 
            sfm.academic_year = ssm.academic_year AND
            sfm.department = ssm.department AND
            sfm.semester = ssm.semester AND
            sfm.section = ssm.section AND
            sfm.subject_code = ssm.subject_code AND
            sfm.faculty_1_id = ssm.faculty_1_id
        WHERE lts.mapping_id = sfm.id AND lts.simplified_mapping_id IS NULL;
      `);
    }
    
    // Check again if any rows still couldn't be updated
    const stillMissingMappings = await executeQuery(`
      SELECT COUNT(*) AS still_missing
      FROM lab_time_slots
      WHERE simplified_mapping_id IS NULL;
    `);
    
    if (stillMissingMappings && stillMissingMappings[0]?.still_missing > 0) {
      console.error(`Error: ${stillMissingMappings[0].still_missing} lab time slots still could not be mapped to simplified_subject_faculty_mappings`);
      throw new Error("Could not update all lab time slots with simplified mappings");
    }
    
    // Step 4: Add foreign key constraint to the new column
    console.log("Step 4: Adding foreign key constraint...");
    await executeQuery(`
      ALTER TABLE lab_time_slots
      ADD CONSTRAINT lab_time_slots_simplified_mapping_id_fkey
      FOREIGN KEY (simplified_mapping_id)
      REFERENCES simplified_subject_faculty_mappings(id)
      ON DELETE CASCADE;
    `);
    
    // Step 5: Make the new column NOT NULL
    console.log("Step 5: Setting simplified_mapping_id to NOT NULL...");
    await executeQuery(`
      ALTER TABLE lab_time_slots
      ALTER COLUMN simplified_mapping_id SET NOT NULL;
    `);
    
    // Step 6: Drop the old foreign key constraint
    console.log("Step 6: Dropping old foreign key constraint...");
    const constraints = await executeQuery(`
      SELECT conname
      FROM pg_constraint
      WHERE conrelid = 'lab_time_slots'::regclass
      AND contype = 'f'
      AND confrelid = 'subject_faculty_mappings'::regclass;
    `);
    
    if (constraints && constraints.length > 0) {
      for (const constraint of constraints) {
        await executeQuery(`
          ALTER TABLE lab_time_slots
          DROP CONSTRAINT ${constraint.conname};
        `);
      }
    }
    
    // Step 7: Rename the columns to maintain backward compatibility
    console.log("Step 7: Renaming columns...");
    await executeQuery(`
      ALTER TABLE lab_time_slots
      RENAME COLUMN mapping_id TO old_mapping_id;
    `);
    
    await executeQuery(`
      ALTER TABLE lab_time_slots
      RENAME COLUMN simplified_mapping_id TO mapping_id;
    `);
    
    // Step 8: Drop the old column if no longer needed
    console.log("Step 8: Dropping old column...");
    await executeQuery(`
      ALTER TABLE lab_time_slots
      DROP COLUMN old_mapping_id;
    `);
    
    console.log("Lab_time_slots table update completed successfully!");
  } catch (error) {
    console.error("Error updating lab_time_slots table:", error);
    throw error;
  }
}

// Execute the function
updateLabTimeSlots()
  .then(() => {
    console.log("Script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Script failed:", error);
    process.exit(1);
  });
