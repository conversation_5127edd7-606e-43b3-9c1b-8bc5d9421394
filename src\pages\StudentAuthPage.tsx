import React, { useState } from 'react';
import StudentAuth from '@/components/auth/StudentAuth';
import StudentDashboard from '@/components/student/StudentDashboard';
import { StudentAuthData } from '@/services/StudentAuthService';
import { useLogout } from '@/hooks/useLogout';

const StudentAuthPage: React.FC = () => {
  const [studentData, setStudentData] = useState<StudentAuthData | null>(null);
  const [showChangePassword, setShowChangePassword] = useState(false);
  const { logout } = useLogout();

  const handleLogin = (authData: StudentAuthData) => {
    setStudentData(authData);
  };

  const handleLogout = () => {
    setStudentData(null);
    setShowChangePassword(false);
    logout(); // Use standardized logout
  };

  const handleChangePassword = () => {
    setShowChangePassword(true);
  };

  // If student is logged in, show dashboard
  if (studentData && !showChangePassword) {
    return (
      <StudentDashboard
        studentData={studentData}
        onLogout={handleLogout}
        onChangePassword={handleChangePassword}
      />
    );
  }

  // Show authentication form (including change password)
  return (
    <StudentAuth 
      onLogin={handleLogin}
    />
  );
};

export default StudentAuthPage;
