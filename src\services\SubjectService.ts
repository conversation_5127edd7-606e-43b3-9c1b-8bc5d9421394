
import { supabase } from "@/integrations/supabase/client";
import { FacultyAvailabilityService } from "@/services/FacultyAvailabilityService";

// Matches your `subjects` table schema (with snake_case)
export type Subject = {
  id: string;
  academic_year: string;
  department: string;
  semester: string;
  subject_code: string;
  subject_name: string;
  subject_short_id: string;
  subject_type: "theory" | "laboratory" | "elective";
  created_at: string;
};

export interface NewSubject {
  academic_year: string;
  department: string;
  semester: string;
  subject_code: string;
  subject_name: string;
  subject_short_id: string;
  subject_type: "theory" | "laboratory" | "elective";
}

const SubjectService = {
  async fetchAll(): Promise<Subject[]> {
    const { data, error } = await supabase
      .from("subjects")
      .select(
        "id, academic_year, department, semester, subject_code, subject_name, subject_short_id, subject_type, created_at"
      )
      .order("created_at", { ascending: false });
    if (error) throw error;
    return data || [];
  },

  async create(subject: NewSubject): Promise<Subject> {
    console.log("Creating subject with short_id:", subject.subject_short_id);

    // Create a new object to avoid any reference issues
    const subjectToCreate = {
      academic_year: subject.academic_year,
      department: subject.department,
      semester: subject.semester,
      subject_code: subject.subject_code,
      subject_name: subject.subject_name,
      subject_short_id: subject.subject_short_id, // Preserve exactly as entered
      subject_type: subject.subject_type
    };

    const { data, error } = await supabase
      .from("subjects")
      .insert(subjectToCreate)
      .select()
      .single();

    if (error) throw error;
    console.log("Created subject with short_id:", data.subject_short_id);
    return data;
  },

  async update(
    id: string,
    updates: Partial<NewSubject>
  ): Promise<Subject> {
    console.log("Updating subject with short_id:", updates.subject_short_id);

    // Create a new object with only the fields that are being updated
    const updatesToApply: any = {};

    if (updates.academic_year !== undefined) updatesToApply.academic_year = updates.academic_year;
    if (updates.department !== undefined) updatesToApply.department = updates.department;
    if (updates.semester !== undefined) updatesToApply.semester = updates.semester;
    if (updates.subject_code !== undefined) updatesToApply.subject_code = updates.subject_code;
    if (updates.subject_name !== undefined) updatesToApply.subject_name = updates.subject_name;
    if (updates.subject_short_id !== undefined) updatesToApply.subject_short_id = updates.subject_short_id;
    if (updates.subject_type !== undefined) updatesToApply.subject_type = updates.subject_type;

    const { data, error } = await supabase
      .from("subjects")
      .update(updatesToApply)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;
    console.log("Updated subject with short_id:", data.subject_short_id);
    return data;
  },

  async remove(id: string): Promise<void> {
    // First, find all mappings that use this subject
    const { data: mappings, error: mappingsError } = await supabase
      .from("subject_faculty_mappings")
      .select("id")
      .eq("subject_id", id);

    if (mappingsError) throw mappingsError;

    // If there are mappings, delete related lab slots first
    if (mappings && mappings.length > 0) {
      const mappingIds = mappings.map(m => m.id);

      // Get lab time slots for these mappings before deleting them
      const { data: labTimeSlots, error: fetchLabSlotsError } = await supabase
        .from("lab_time_slots")
        .select("*")
        .in("mapping_id", mappingIds);

      if (fetchLabSlotsError) {
        console.error("Error fetching lab time slots:", fetchLabSlotsError);
        // Continue with deletion even if we can't get lab time slots
      }

      // Create a map of lab slots by mapping ID
      const labSlotsByMapping: Record<string, any[]> = {};
      if (labTimeSlots && labTimeSlots.length > 0) {
        for (const slot of labTimeSlots) {
          if (!labSlotsByMapping[slot.mapping_id]) {
            labSlotsByMapping[slot.mapping_id] = [];
          }
          labSlotsByMapping[slot.mapping_id].push(slot);
        }
      }

      // Delete lab time slots for these mappings
      const { error: labSlotsError } = await supabase
        .from("lab_time_slots")
        .delete()
        .in("mapping_id", mappingIds);

      if (labSlotsError) throw labSlotsError;

      // Delete timetable slots that use these mappings as subject_id
      const { error: timetableSlotsError } = await supabase
        .from("timetable_slots")
        .delete()
        .in("subject_id", mappingIds);

      if (timetableSlotsError) throw timetableSlotsError;

      // Restore faculty availability for lab mappings
      if (labTimeSlots && labTimeSlots.length > 0) {
        console.log(`Restoring faculty availability for ${labTimeSlots.length} lab time slots`);

        // Process each mapping
        for (const mapping of mappings) {
          // Check if this is a lab subject
          const isLabSubject = mapping.subject_type === 'laboratory' || mapping.subject_type === 'lab';

          // If it's a lab subject and we have lab slots for it, restore faculty availability
          if (isLabSubject && labSlotsByMapping[mapping.id] && labSlotsByMapping[mapping.id].length > 0) {
            const slots = labSlotsByMapping[mapping.id];

            // Process each lab time slot to restore faculty availability
            for (const slot of slots) {
              try {
                await FacultyAvailabilityService.restoreFacultyAvailabilityFromLabSlots(
                  mapping.faculty_1_id,
                  mapping.faculty_2_id,
                  slot.day,
                  slot.time_of_day
                );
                console.log(`Restored faculty availability for lab slot on ${slot.day} at ${slot.time_of_day}`);
              } catch (availabilityError) {
                console.error("Error restoring faculty availability:", availabilityError);
                // Continue with the next slot even if this one fails
              }
            }
          }
        }
      }

      // Delete the mappings themselves
      const { error: deleteMappingsError } = await supabase
        .from("subject_faculty_mappings")
        .delete()
        .in("id", mappingIds);

      if (deleteMappingsError) throw deleteMappingsError;
    }

    // Finally, delete the subject itself
    const { error } = await supabase
      .from("subjects")
      .delete()
      .eq("id", id);

    if (error) throw error;
  },
};

export default SubjectService;
