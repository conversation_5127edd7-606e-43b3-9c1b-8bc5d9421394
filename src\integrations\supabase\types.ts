export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      class_teachers: {
        Row: {
          id: string
          faculty_id: string
          academic_year: string
          department: string
          semester: string
          section: string
          assigned_at: string
          assigned_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          faculty_id: string
          academic_year: string
          department: string
          semester: string
          section: string
          assigned_at?: string
          assigned_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          faculty_id?: string
          academic_year?: string
          department?: string
          semester?: string
          section?: string
          assigned_at?: string
          assigned_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "class_teachers_faculty_id_fkey"
            columns: ["faculty_id"]
            isOneToOne: false
            referencedRelation: "employee_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_teachers_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      batch_students: {
        Row: {
          id: string
          usn: string
          student_name: string
          batch_name: string
          academic_year: string
          department: string
          semester: string
          section: string
          class_teacher_id: string | null
          uploaded_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          usn: string
          student_name: string
          batch_name: string
          academic_year: string
          department: string
          semester: string
          section: string
          class_teacher_id?: string | null
          uploaded_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          usn?: string
          student_name?: string
          batch_name?: string
          academic_year?: string
          department?: string
          semester?: string
          section?: string
          class_teacher_id?: string | null
          uploaded_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "batch_students_class_teacher_id_fkey"
            columns: ["class_teacher_id"]
            isOneToOne: false
            referencedRelation: "employee_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "batch_students_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      class_students: {
        Row: {
          id: string
          usn: string
          student_name: string
          email: string | null
          student_mobile: string | null
          father_mobile: string | null
          mother_mobile: string | null
          academic_year: string
          department: string
          semester: string
          section: string
          class_teacher_id: string | null
          uploaded_by: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          usn: string
          student_name: string
          email?: string | null
          student_mobile?: string | null
          father_mobile?: string | null
          mother_mobile?: string | null
          academic_year: string
          department: string
          semester: string
          section: string
          class_teacher_id?: string | null
          uploaded_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          usn?: string
          student_name?: string
          email?: string | null
          student_mobile?: string | null
          father_mobile?: string | null
          mother_mobile?: string | null
          academic_year?: string
          department?: string
          semester?: string
          section?: string
          class_teacher_id?: string | null
          uploaded_by?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "class_students_class_teacher_id_fkey"
            columns: ["class_teacher_id"]
            isOneToOne: false
            referencedRelation: "employee_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "class_students_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      elective_selections: {
        Row: {
          id: string
          student_id: string | null
          subject_code: string
          subject_name: string
          department: string
          semester: string
          academic_year: string
          selected_at: string | null
        }
        Insert: {
          id?: string
          student_id?: string | null
          subject_code: string
          subject_name: string
          department: string
          semester: string
          academic_year: string
          selected_at?: string | null
        }
        Update: {
          id?: string
          student_id?: string | null
          subject_code?: string
          subject_name?: string
          department?: string
          semester?: string
          academic_year?: string
          selected_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "elective_selections_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      faculty_availability_history: {
        Row: {
          id: string
          faculty_id: string
          academic_year: string
          department: string
          semester: string
          section: string
          vacant_by_day: Record<string, number> | null
          vacant_count_by_day: Record<string, string[]> | null
          created_at: string
        }
        Insert: {
          id?: string
          faculty_id: string
          academic_year: string
          department: string
          semester: string
          section: string
          vacant_by_day?: Record<string, number> | null
          vacant_count_by_day?: Record<string, string[]> | null
          created_at?: string
        }
        Update: {
          id?: string
          faculty_id?: string
          academic_year?: string
          department?: string
          semester?: string
          section?: string
          vacant_by_day?: Record<string, number> | null
          vacant_count_by_day?: Record<string, string[]> | null
          created_at?: string
        }
      }
      employee_details: {
        Row: {
          created_at: string | null
          department: string | null
          designation: string | null
          email: string
          full_name: string
          id: string
          phone: string | null
          roles: string[] | null
          updated_at: string | null
          vacant_by_day?: Record<string, number> | null
          vacant_count_by_day?: Record<string, string[]> | null
        }
        Insert: {
          created_at?: string | null
          department?: string | null
          designation?: string | null
          email: string
          full_name: string
          id?: string
          phone?: string | null
          roles?: string[] | null
          updated_at?: string | null
          vacant_by_day?: Record<string, number> | null
          vacant_count_by_day?: Record<string, string[]> | null
        }
        Update: {
          created_at?: string | null
          department?: string | null
          designation?: string | null
          email?: string
          full_name?: string
          id?: string
          phone?: string | null
          roles?: string[] | null
          updated_at?: string | null
          vacant_by_day?: Record<string, number> | null
          vacant_count_by_day?: Record<string, string[]> | null
        }
        Relationships: []
      }
      lab_time_slots: {
        Row: {
          batch_name: string
          day: string
          id: string
          mapping_id: string
          slot_order: number
          time_of_day: string
        }
        Insert: {
          batch_name: string
          day: string
          id?: string
          mapping_id: string
          slot_order: number
          time_of_day?: string
        }
        Update: {
          batch_name?: string
          day?: string
          id?: string
          mapping_id?: string
          slot_order?: number
          time_of_day?: string
        }
        Relationships: []
      }
      lab_batches: {
        Row: {
          id: string
          batch_name: string
          department: string
          semester: string
          section: string
          subject_code: string
          academic_year: string
          created_at: string
          created_by: string | null
        }
        Insert: {
          id?: string
          batch_name: string
          department: string
          semester: string
          section: string
          subject_code: string
          academic_year: string
          created_at?: string
          created_by?: string | null
        }
        Update: {
          id?: string
          batch_name?: string
          department?: string
          semester?: string
          section?: string
          subject_code?: string
          academic_year?: string
          created_at?: string
          created_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lab_batches_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      lab_batch_students: {
        Row: {
          id: string
          lab_batch_id: string | null
          student_id: string | null
          class_teacher_id: string | null
          uploaded_by: string | null
          created_at: string | null
          uploaded_at: string | null
        }
        Insert: {
          id?: string
          lab_batch_id?: string | null
          student_id?: string | null
          class_teacher_id?: string | null
          uploaded_by?: string | null
          created_at?: string | null
          uploaded_at?: string | null
        }
        Update: {
          id?: string
          lab_batch_id?: string | null
          student_id?: string | null
          class_teacher_id?: string | null
          uploaded_by?: string | null
          created_at?: string | null
          uploaded_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lab_batch_students_lab_batch_id_fkey"
            columns: ["lab_batch_id"]
            isOneToOne: false
            referencedRelation: "lab_batches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lab_batch_students_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lab_batch_students_class_teacher_id_fkey"
            columns: ["class_teacher_id"]
            isOneToOne: false
            referencedRelation: "employee_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lab_batch_students_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          department: string | null
          designation: string | null
          full_name: string | null
          id: string
          phone: string | null
          role: string | null
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          department?: string | null
          designation?: string | null
          full_name?: string | null
          id: string
          phone?: string | null
          role?: string | null
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          department?: string | null
          designation?: string | null
          full_name?: string | null
          id?: string
          phone?: string | null
          role?: string | null
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      semester_lab_configurations: {
        Row: {
          academic_year: string
          allow_consecutive_labs: boolean | null
          created_at: string | null
          created_by: string | null
          default_lab_duration: number
          department: string
          enable_afternoon_session: boolean | null
          enable_early_afternoon_session: boolean | null
          enable_mid_session: boolean | null
          enable_morning_session: boolean | null
          id: string
          lab_duration_type: string
          last_modified_by: string | null
          max_labs_per_day: number | null
          prefer_lab_distribution: boolean | null
          semester: string
          skill_lab_duration: number | null
          skill_lab_placement_preference: string | null
          skill_lab_preferred_day: string | null
          skill_lab_required: boolean | null
          updated_at: string | null
        }
        Insert: {
          academic_year: string
          allow_consecutive_labs?: boolean | null
          created_at?: string | null
          created_by?: string | null
          default_lab_duration?: number
          department: string
          enable_afternoon_session?: boolean | null
          enable_early_afternoon_session?: boolean | null
          enable_mid_session?: boolean | null
          enable_morning_session?: boolean | null
          id?: string
          lab_duration_type?: string
          last_modified_by?: string | null
          max_labs_per_day?: number | null
          prefer_lab_distribution?: boolean | null
          semester: string
          skill_lab_duration?: number | null
          skill_lab_placement_preference?: string | null
          skill_lab_preferred_day?: string | null
          skill_lab_required?: boolean | null
          updated_at?: string | null
        }
        Update: {
          academic_year?: string
          allow_consecutive_labs?: boolean | null
          created_at?: string | null
          created_by?: string | null
          default_lab_duration?: number
          department?: string
          enable_afternoon_session?: boolean | null
          enable_early_afternoon_session?: boolean | null
          enable_mid_session?: boolean | null
          enable_morning_session?: boolean | null
          id?: string
          lab_duration_type?: string
          last_modified_by?: string | null
          max_labs_per_day?: number | null
          prefer_lab_distribution?: boolean | null
          semester?: string
          skill_lab_duration?: number | null
          skill_lab_placement_preference?: string | null
          skill_lab_preferred_day?: string | null
          skill_lab_required?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "semester_lab_configurations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "semester_lab_configurations_last_modified_by_fkey"
            columns: ["last_modified_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      student_attendance: {
        Row: {
          created_at: string
          date: string
          faculty_id: string
          id: string
          status: string
          student_id: string
          subject_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          date: string
          faculty_id: string
          id?: string
          status: string
          student_id: string
          subject_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          date?: string
          faculty_id?: string
          id?: string
          status?: string
          student_id?: string
          subject_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "student_attendance_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      student_auth: {
        Row: {
          id: string
          usn: string
          email: string
          password_hash: string
          is_verified: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          usn: string
          email: string
          password_hash: string
          is_verified?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          usn?: string
          email?: string
          password_hash?: string
          is_verified?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      students: {
        Row: {
          created_at: string
          created_by: string | null
          department: string
          enrollment_year: string | null
          father_phone: string | null
          first_login: boolean | null
          id: string
          is_active: boolean | null
          last_login: string | null
          mother_phone: string | null
          name: string | null
          parent_email: string | null
          parent_name: string | null
          password_hash: string | null
          password_reset_expires: string | null
          password_reset_token: string | null
          roll_number: string | null
          section: string
          semester: string
          student_email: string | null
          student_name: string | null
          student_phone: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          department: string
          enrollment_year?: string | null
          father_phone?: string | null
          first_login?: boolean | null
          id?: string
          is_active?: boolean | null
          last_login?: string | null
          mother_phone?: string | null
          name?: string | null
          parent_email?: string | null
          parent_name?: string | null
          password_hash?: string | null
          password_reset_expires?: string | null
          password_reset_token?: string | null
          roll_number?: string | null
          section: string
          semester: string
          student_email?: string | null
          student_name?: string | null
          student_phone?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          department?: string
          enrollment_year?: string | null
          father_phone?: string | null
          first_login?: boolean | null
          id?: string
          is_active?: boolean | null
          last_login?: string | null
          mother_phone?: string | null
          name?: string | null
          parent_email?: string | null
          parent_name?: string | null
          password_hash?: string | null
          password_reset_expires?: string | null
          password_reset_token?: string | null
          roll_number?: string | null
          section?: string
          semester?: string
          student_email?: string | null
          student_name?: string | null
          student_phone?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "students_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      subject_faculty_mappings: {
        Row: {
          academic_year: string
          classroom: string
          department: string
          faculty_1_id: string
          faculty_2_id: string | null
          hours_per_week: number
          id: string
          section: string
          semester: string
          slots_per_week: number | null
          subject_code: string
          subject_id: string
          subject_name: string
          subject_type: string
        }
        Insert: {
          academic_year: string
          classroom: string
          department: string
          faculty_1_id: string
          faculty_2_id?: string | null
          hours_per_week: number
          id?: string
          section: string
          semester: string
          slots_per_week?: number | null
          subject_code: string
          subject_id: string
          subject_name: string
          subject_type: string
        }
        Update: {
          academic_year?: string
          classroom?: string
          department?: string
          faculty_1_id?: string
          faculty_2_id?: string | null
          hours_per_week?: number
          id?: string
          section?: string
          semester?: string
          slots_per_week?: number | null
          subject_code?: string
          subject_id?: string
          subject_name?: string
          subject_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "subject_faculty_mappings_faculty_1_id_fkey"
            columns: ["faculty_1_id"]
            isOneToOne: false
            referencedRelation: "employee_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subject_faculty_mappings_faculty_2_id_fkey"
            columns: ["faculty_2_id"]
            isOneToOne: false
            referencedRelation: "employee_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subject_faculty_mappings_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      subjects: {
        Row: {
          academic_year: string
          created_at: string
          department: string
          id: string
          semester: string
          subject_code: string
          subject_name: string
          subject_short_id: string
          subject_type: Database["public"]["Enums"]["subject_type_enum"]
          updated_at: string
        }
        Insert: {
          academic_year: string
          created_at?: string
          department: string
          id?: string
          semester: string
          subject_code: string
          subject_name: string
          subject_short_id: string
          subject_type: Database["public"]["Enums"]["subject_type_enum"]
          updated_at?: string
        }
        Update: {
          academic_year?: string
          created_at?: string
          department?: string
          id?: string
          semester?: string
          subject_code?: string
          subject_name?: string
          subject_short_id?: string
          subject_type?: Database["public"]["Enums"]["subject_type_enum"]
          updated_at?: string
        }
        Relationships: []
      }
      time_structure: {
        Row: {
          academic_year: string
          auto_merge_labs: boolean
          created_at: string
          department: string
          first_half_end_time: string
          first_half_start_time: string
          id: string
          lunch_break_end_time: string
          lunch_break_start_time: string
          periods_in_first_half: number
          periods_in_second_half: number
          second_half_end_time: string
          second_half_start_time: string
          tea_break_end_time: string
          tea_break_start_time: string
          theory_class_duration: number
          three_credits_lab_duration: number
          two_credits_lab_duration: number
          updated_at: string
          working_days: string[]
        }
        Insert: {
          academic_year: string
          auto_merge_labs?: boolean
          created_at?: string
          department: string
          first_half_end_time: string
          first_half_start_time: string
          id?: string
          lunch_break_end_time: string
          lunch_break_start_time: string
          periods_in_first_half: number
          periods_in_second_half: number
          second_half_end_time: string
          second_half_start_time: string
          tea_break_end_time: string
          tea_break_start_time: string
          theory_class_duration: number
          three_credits_lab_duration: number
          two_credits_lab_duration: number
          updated_at?: string
          working_days: string[]
        }
        Update: {
          academic_year?: string
          auto_merge_labs?: boolean
          created_at?: string
          department?: string
          first_half_end_time?: string
          first_half_start_time?: string
          id?: string
          lunch_break_end_time?: string
          lunch_break_start_time?: string
          periods_in_first_half?: number
          periods_in_second_half?: number
          second_half_end_time?: string
          second_half_start_time?: string
          tea_break_end_time?: string
          tea_break_start_time?: string
          theory_class_duration?: number
          three_credits_lab_duration?: number
          two_credits_lab_duration?: number
          updated_at?: string
          working_days?: string[]
        }
        Relationships: []
      }
      timetable_slots: {
        Row: {
          academic_year: string
          batch_name: string | null
          col_span: number | null
          created_at: string | null
          day: string
          department: string
          faculty_id: string
          faculty_name: string
          faculty2_id: string | null
          faculty2_name: string | null
          id: string
          is_full_session_lab: boolean | null
          is_hidden: boolean | null
          is_lab_start: boolean | null
          is_processed: boolean | null
          room_number: string | null
          section: string
          semester: string
          session: string | null
          subject_code: string
          subject_id: string
          subject_name: string
          subject_short_id: string | null
          subject_type: string
          time_slot: string
          updated_at: string | null
        }
        Insert: {
          academic_year: string
          batch_name?: string | null
          col_span?: number | null
          created_at?: string | null
          day: string
          department: string
          faculty_id: string
          faculty_name: string
          faculty2_id?: string | null
          faculty2_name?: string | null
          id?: string
          is_full_session_lab?: boolean | null
          is_hidden?: boolean | null
          is_lab_start?: boolean | null
          is_processed?: boolean | null
          room_number?: string | null
          section: string
          semester: string
          session?: string | null
          subject_code: string
          subject_id: string
          subject_name: string
          subject_short_id?: string | null
          subject_type: string
          time_slot: string
          updated_at?: string | null
        }
        Update: {
          academic_year?: string
          batch_name?: string | null
          col_span?: number | null
          created_at?: string | null
          day?: string
          department?: string
          faculty_id?: string
          faculty_name?: string
          faculty2_id?: string | null
          faculty2_name?: string | null
          id?: string
          is_full_session_lab?: boolean | null
          is_hidden?: boolean | null
          is_lab_start?: boolean | null
          is_processed?: boolean | null
          room_number?: string | null
          section?: string
          semester?: string
          session?: string | null
          subject_code?: string
          subject_id?: string
          subject_name?: string
          subject_short_id?: string | null
          subject_type?: string
          time_slot?: string
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      subject_type_enum: "theory" | "laboratory" | "elective"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      subject_type_enum: ["theory", "laboratory", "elective"],
    },
  },
} as const
