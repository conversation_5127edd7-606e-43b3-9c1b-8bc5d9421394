import React, { useState } from 'react';
import { 
  Users, 
  Calendar, 
  BarChart3, 
  Settings, 
  Bell, 
  Star,
  TrendingUp,
  Activity,
  Shield,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { ModernLayout, ModernGrid, ModernCard, ModernButtonGroup, ModernSection } from '@/components/layout/ModernLayout';
import { ModernLoading, ModernSkeleton, ModernProgress } from '@/components/ui/modern-loading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DashboardCard from '@/components/dashboard/DashboardCard';

/**
 * Design Showcase Page
 * Demonstrates the modern, responsive design system
 * This page showcases all the enhanced UI components and responsive features
 */
const DesignShowcase = () => {
  const [activeTab, setActiveTab] = useState('components');
  const [loading, setLoading] = useState(false);

  const handleLoadingDemo = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 3000);
  };

  return (
    <ModernLayout
      title="Modern Design Showcase"
      description="Experience the enhanced, responsive design system with professional styling and smooth animations"
      actions={
        <ModernButtonGroup>
          <Button variant="outline" onClick={handleLoadingDemo}>
            Demo Loading
          </Button>
          <Button>Get Started</Button>
        </ModernButtonGroup>
      }
    >
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="cards">Cards & Layouts</TabsTrigger>
          <TabsTrigger value="responsive">Responsive</TabsTrigger>
          <TabsTrigger value="animations">Animations</TabsTrigger>
        </TabsList>

        <TabsContent value="components" className="space-y-8">
          <ModernSection title="Enhanced Buttons" description="Professional button styling with hover effects and variants">
            <ModernGrid cols={3} gap="md">
              <div className="space-y-3">
                <h4 className="font-medium">Primary Variants</h4>
                <div className="space-y-2">
                  <Button className="w-full">Default Primary</Button>
                  <Button variant="secondary" className="w-full">Secondary</Button>
                  <Button variant="outline" className="w-full">Outline</Button>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium">Status Variants</h4>
                <div className="space-y-2">
                  <Button variant="success" className="w-full">Success</Button>
                  <Button variant="warning" className="w-full">Warning</Button>
                  <Button variant="destructive" className="w-full">Destructive</Button>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="font-medium">Sizes</h4>
                <div className="space-y-2">
                  <Button size="xs" className="w-full">Extra Small</Button>
                  <Button size="sm" className="w-full">Small</Button>
                  <Button size="lg" className="w-full">Large</Button>
                  <Button size="xl" className="w-full">Extra Large</Button>
                </div>
              </div>
            </ModernGrid>
          </ModernSection>

          <ModernSection title="Enhanced Inputs" description="Modern input styling with smooth transitions">
            <ModernGrid cols={2} gap="lg">
              <div className="space-y-4">
                <Input placeholder="Standard input with hover effects" />
                <Input placeholder="Focus for ring animation" />
                <Input placeholder="Disabled input" disabled />
              </div>
              <div className="space-y-4">
                <Input type="email" placeholder="Email input" />
                <Input type="password" placeholder="Password input" />
                <Input type="search" placeholder="Search input" />
              </div>
            </ModernGrid>
          </ModernSection>

          <ModernSection title="Loading States" description="Professional loading indicators">
            <ModernGrid cols={4} gap="md">
              <div className="text-center space-y-3">
                <ModernLoading variant="spinner" size="md" />
                <p className="text-sm text-muted-foreground">Spinner</p>
              </div>
              <div className="text-center space-y-3">
                <ModernLoading variant="dots" size="md" />
                <p className="text-sm text-muted-foreground">Dots</p>
              </div>
              <div className="text-center space-y-3">
                <ModernLoading variant="pulse" size="md" />
                <p className="text-sm text-muted-foreground">Pulse</p>
              </div>
              <div className="text-center space-y-3">
                <ModernProgress value={75} showValue />
                <p className="text-sm text-muted-foreground">Progress</p>
              </div>
            </ModernGrid>
          </ModernSection>
        </TabsContent>

        <TabsContent value="cards" className="space-y-8">
          <ModernSection title="Dashboard Cards" description="Enhanced dashboard cards with trends and interactions">
            <ModernGrid cols={4} gap="lg">
              <DashboardCard
                title="Total Users"
                value="2,847"
                icon={Users}
                description="Active system users"
                colorClass="text-primary bg-primary/10"
                trend={{ value: 12, isPositive: true }}
                interactive
              />
              <DashboardCard
                title="Active Sessions"
                value="1,234"
                icon={Activity}
                description="Current active sessions"
                colorClass="text-success bg-success/10"
                trend={{ value: 8, isPositive: true }}
                interactive
              />
              <DashboardCard
                title="System Load"
                value="67%"
                icon={BarChart3}
                description="Current system utilization"
                colorClass="text-warning bg-warning/10"
                trend={{ value: 3, isPositive: false }}
                interactive
              />
              <DashboardCard
                title="Uptime"
                value="99.9%"
                icon={CheckCircle}
                description="System availability"
                colorClass="text-success bg-success/10"
                interactive
              />
            </ModernGrid>
          </ModernSection>

          <ModernSection title="Modern Cards" description="Enhanced card layouts with elevation and interactions">
            <ModernGrid cols={3} gap="lg">
              <ModernCard
                title="Interactive Card"
                description="Hover to see the scale effect"
                interactive
                elevated
              >
                <p className="text-muted-foreground">
                  This card demonstrates the interactive hover effects with smooth scaling and shadow transitions.
                </p>
              </ModernCard>
              <ModernCard
                title="Elevated Card"
                description="Enhanced shadow styling"
                elevated
              >
                <p className="text-muted-foreground">
                  This card shows the elevated styling with enhanced shadows for better visual hierarchy.
                </p>
              </ModernCard>
              <ModernCard
                title="Standard Card"
                description="Clean, minimal styling"
              >
                <p className="text-muted-foreground">
                  This is a standard card with the base styling and subtle shadow effects.
                </p>
              </ModernCard>
            </ModernGrid>
          </ModernSection>
        </TabsContent>

        <TabsContent value="responsive" className="space-y-8">
          <ModernSection title="Responsive Grid System" description="Adaptive layouts for all screen sizes">
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-3">4-Column Grid (Responsive)</h4>
                <ModernGrid cols={4} gap="md">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <Card key={i} className="p-4 text-center">
                      <div className="text-sm font-medium">Item {i + 1}</div>
                    </Card>
                  ))}
                </ModernGrid>
              </div>
              <div>
                <h4 className="font-medium mb-3">3-Column Grid (Responsive)</h4>
                <ModernGrid cols={3} gap="lg">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <Card key={i} className="p-6 text-center">
                      <div className="text-lg font-medium">Column {i + 1}</div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Responsive column that adapts to screen size
                      </p>
                    </Card>
                  ))}
                </ModernGrid>
              </div>
            </div>
          </ModernSection>

          <ModernSection title="Responsive Typography" description="Text that scales beautifully across devices">
            <div className="space-y-4">
              <h1 className="text-responsive-2xl font-bold">Responsive Heading 1</h1>
              <h2 className="text-responsive-xl font-semibold">Responsive Heading 2</h2>
              <h3 className="text-responsive-lg font-medium">Responsive Heading 3</h3>
              <p className="text-responsive-base">
                This is responsive body text that adapts to different screen sizes for optimal readability.
              </p>
              <p className="text-responsive-sm text-muted-foreground">
                This is responsive small text for captions and secondary information.
              </p>
            </div>
          </ModernSection>
        </TabsContent>

        <TabsContent value="animations" className="space-y-8">
          <ModernSection title="Modern Animations" description="Smooth, professional animations and transitions">
            <ModernGrid cols={2} gap="lg">
              <ModernCard title="Fade In Animation" className="animate-fade-in">
                <p className="text-muted-foreground">
                  This card demonstrates the fade-in animation effect.
                </p>
              </ModernCard>
              <ModernCard title="Slide Up Animation" className="animate-slide-up">
                <p className="text-muted-foreground">
                  This card demonstrates the slide-up animation effect.
                </p>
              </ModernCard>
              <ModernCard title="Scale In Animation" className="animate-scale-in">
                <p className="text-muted-foreground">
                  This card demonstrates the scale-in animation effect.
                </p>
              </ModernCard>
              <ModernCard title="Bounce In Animation" className="animate-bounce-in">
                <p className="text-muted-foreground">
                  This card demonstrates the bounce-in animation effect.
                </p>
              </ModernCard>
            </ModernGrid>
          </ModernSection>

          {loading && (
            <ModernSection title="Loading Demo" description="Professional loading states">
              <ModernCard>
                <ModernLoading size="lg" text="Loading demonstration..." />
              </ModernCard>
            </ModernSection>
          )}

          <ModernSection title="Skeleton Loading" description="Content placeholders while loading">
            <ModernGrid cols={2} gap="lg">
              <ModernCard>
                <ModernSkeleton lines={4} avatar button />
              </ModernCard>
              <ModernCard>
                <ModernSkeleton lines={3} />
              </ModernCard>
            </ModernGrid>
          </ModernSection>
        </TabsContent>
      </Tabs>
    </ModernLayout>
  );
};

export default DesignShowcase;
