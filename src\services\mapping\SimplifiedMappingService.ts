// src/services/mapping/SimplifiedMappingService.ts
import { supabase } from "@/integrations/supabase/client";
import { MappingType } from "@/stores/SubjectMappingStore";
import { v4 as uuidv4 } from 'uuid';
import { FacultyAvailabilityService } from "@/services/FacultyAvailabilityService";

// Define the structure for the simplified mapping
interface SimplifiedMapping {
  academic_year: string;
  department: string;
  semester: string;
  section: string;
  subject_id: string;
  subject_code: string;
  subject_name: string;
  subject_type: string;
  faculty_1_id: string;
  faculty_2_id?: string | null;
  hours_per_week: number;
  classroom: string;
  slots_per_week?: number | null;
}

export class SimplifiedMappingService {
  /**
   * Create a new subject-faculty mapping in the simplified table
   */
  static async createMapping(mapping: MappingType): Promise<{ mappingId: string }> {
    try {
      console.log("Creating simplified mapping:", mapping);

      // Get the faculty_2_id from the secondary faculty
      let faculty2Id = null;

      // For lab subjects, faculty2Id is required
      if (mapping.subject.type === 'lab' || mapping.subject.type === 'laboratory') {
        faculty2Id = mapping.faculty2Id || null;
        console.log("Lab subject detected, faculty2Id:", faculty2Id);
      }

      // Transform from UI model to database model
      const dbMapping: SimplifiedMapping = {
        academic_year: mapping.academicYear,
        department: mapping.department,
        semester: mapping.semester,
        section: mapping.section,
        subject_id: mapping.subject.id,
        subject_code: mapping.subject.code,
        subject_name: mapping.subject.name,
        subject_type: mapping.subject.type === "lab" ? "laboratory" : mapping.subject.type,
        faculty_1_id: mapping.faculty.id,
        faculty_2_id: faculty2Id,
        hours_per_week: mapping.hoursPerWeek || 0,
        classroom: mapping.classroom || "Not specified",
        slots_per_week: mapping.slotsPerWeek
      };

      console.log("Transformed mapping for database:", dbMapping);

      // Try to insert with select first
      try {
        const { data, error } = await supabase
          .from("simplified_subject_faculty_mappings")
          .insert([dbMapping])
          .select("id")
          .single();

        if (error) {
          console.error("Error with insert and select:", error);
          throw error;
        }

        if (!data || !data.id) {
          console.warn("Insert with select succeeded but no ID returned");
          throw new Error("No ID returned from insert with select");
        }

        console.log("Successfully created mapping with ID:", data.id);
        return { mappingId: data.id };
      } catch (selectError) {
        console.error("Insert with select failed, trying without select:", selectError);

        // Fall back to insert without select
        const { error: insertError } = await supabase
          .from("simplified_subject_faculty_mappings")
          .insert([dbMapping]);

        if (insertError) {
          console.error("Error with simplified insert:", insertError);
          throw insertError;
        }

        console.log("Insert succeeded, trying to fetch the mapping ID");

        // Try to fetch the mapping we just created using a combination of fields that should be unique
        const { data: fetchedData, error: fetchError } = await supabase
          .from("simplified_subject_faculty_mappings")
          .select("id")
          .eq("academic_year", dbMapping.academic_year)
          .eq("department", dbMapping.department)
          .eq("semester", dbMapping.semester)
          .eq("section", dbMapping.section)
          .eq("subject_code", dbMapping.subject_code)
          .eq("faculty_1_id", dbMapping.faculty_1_id)
          .order("created_at", { ascending: false })
          .limit(1);

        if (fetchError) {
          console.error("Error fetching mapping ID:", fetchError);
          // Return unknown ID but don't throw error since insert succeeded
          return { mappingId: "unknown" };
        }

        if (!fetchedData || fetchedData.length === 0 || !fetchedData[0].id) {
          console.warn("Failed to fetch mapping ID after successful insert");
          return { mappingId: "unknown" };
        }

        const mappingId = fetchedData[0].id;
        console.log("Successfully created mapping with ID:", mappingId);

        return { mappingId };
      }
    } catch (error) {
      console.error("Error in createMapping:", error);
      throw error;
    }
  }

  /**
   * Update an existing subject-faculty mapping
   */
  static async updateMapping(id: string, mapping: Partial<MappingType>): Promise<void> {
    try {
      console.log(`Updating simplified mapping ${id} with:`, mapping);

      const updates: Partial<SimplifiedMapping> = {};

      // Update faculty_1_id if faculty is provided
      if (mapping.faculty) {
        updates.faculty_1_id = mapping.faculty.id;
      }

      // Update faculty_2_id if faculty2Id is provided
      if (mapping.faculty2Id !== undefined) {
        // Convert empty string to null for the database
        updates.faculty_2_id = mapping.faculty2Id === "" ? null : mapping.faculty2Id;
        console.log("Updating faculty_2_id to:", updates.faculty_2_id);
      }

      // Update other fields if provided
      if (mapping.hoursPerWeek !== undefined) {
        updates.hours_per_week = mapping.hoursPerWeek;
      }

      if (mapping.classroom !== undefined) {
        updates.classroom = mapping.classroom;
      }

      if (mapping.slotsPerWeek !== undefined) {
        updates.slots_per_week = mapping.slotsPerWeek;
      }

      console.log("Applying updates:", updates);

      const { error } = await supabase
        .from("simplified_subject_faculty_mappings")
        .update(updates)
        .eq("id", id);

      if (error) {
        console.error("Error updating mapping:", error);
        throw error;
      }

      console.log(`Successfully updated mapping ${id}`);
    } catch (error) {
      console.error(`Failed to update mapping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a subject-faculty mapping
   */
  static async deleteMapping(id: string): Promise<void> {
    try {
      console.log(`Deleting simplified mapping ${id}`);

      // First, get the mapping details to know which faculty members are affected
      const { data: mapping, error: mappingError } = await supabase
        .from("simplified_subject_faculty_mappings")
        .select("*")
        .eq("id", id)
        .single();

      if (mappingError) {
        console.error("Error fetching mapping details:", mappingError);
        throw mappingError;
      }

      // Check if this is a lab subject
      const isLabSubject = mapping.subject_type === 'laboratory' || mapping.subject_type === 'lab';

      // If it's a lab subject, get the lab time slots before deleting
      let labTimeSlots: any[] = [];
      if (isLabSubject) {
        console.log(`Fetching lab time slots for mapping ${id}`);

        // Check if the simplified_mapping_id column exists
        const { data: labSlots, error: labSlotsError } = await supabase
          .from("lab_time_slots")
          .select("*")
          .eq("simplified_mapping_id", id);

        if (labSlotsError) {
          // If the column doesn't exist, try with mapping_id
          console.log("Error fetching lab slots with simplified_mapping_id, trying with mapping_id");

          const { data: legacyLabSlots, error: legacyLabSlotsError } = await supabase
            .from("lab_time_slots")
            .select("*")
            .eq("mapping_id", id);

          if (legacyLabSlotsError) {
            console.error("Error fetching lab slots with mapping_id:", legacyLabSlotsError);
          } else if (legacyLabSlots) {
            labTimeSlots = legacyLabSlots;
          }
        } else if (labSlots) {
          labTimeSlots = labSlots;
        }

        console.log(`Found ${labTimeSlots.length} lab time slots for mapping ${id}`);
      }

      // Now delete the mapping
      const { error } = await supabase
        .from("simplified_subject_faculty_mappings")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting mapping:", error);

        // If delete fails, try soft delete
        console.log("Trying soft delete as fallback");

        const randomSuffix = Math.random().toString(36).substring(2, 7);
        const deletedCode = `DELETED_${randomSuffix}`;

        const { error: softDeleteError } = await supabase
          .from("simplified_subject_faculty_mappings")
          .update({ subject_code: deletedCode })
          .eq("id", id);

        if (softDeleteError) {
          console.error("Error with soft delete:", softDeleteError);
          throw softDeleteError;
        }

        console.log(`Successfully marked mapping ${id} as deleted (soft delete)`);
      } else {
        console.log(`Successfully deleted mapping ${id}`);
      }

      // If it was a lab subject and we found lab time slots, restore faculty availability
      if (isLabSubject && labTimeSlots.length > 0) {
        console.log(`Restoring faculty availability for ${labTimeSlots.length} lab time slots`);

        // Process each lab time slot to restore faculty availability
        for (const slot of labTimeSlots) {
          try {
            await FacultyAvailabilityService.restoreFacultyAvailabilityFromLabSlots(
              mapping.faculty_1_id,
              mapping.faculty_2_id,
              slot.day,
              slot.time_of_day
            );
            console.log(`Restored faculty availability for lab slot on ${slot.day} at ${slot.time_of_day}`);
          } catch (availabilityError) {
            console.error("Error restoring faculty availability:", availabilityError);
            // Continue with the next slot even if this one fails
          }
        }
      }
    } catch (error) {
      console.error(`Failed to delete mapping ${id}:`, error);
      throw error;
    }
  }

  /**
   * Save a mapping with lab slots
   */
  static async saveMapping(mapping: MappingType): Promise<MappingType> {
    try {
      console.log("Saving simplified mapping with lab slots:", mapping);

      // Check if this is a partial update (only updating faculty or faculty2Id)
      const isPartialUpdate = mapping.id && (
        (mapping.faculty && !mapping.subject) ||
        (mapping.faculty2Id !== undefined && !mapping.subject)
      );

      if (isPartialUpdate) {
        console.log("Detected partial update for mapping ID:", mapping.id);

        // Get the existing mapping first
        const { data: existingMapping, error: fetchError } = await supabase
          .from("simplified_subject_faculty_mappings")
          .select("*")
          .eq("id", mapping.id)
          .single();

        if (fetchError) {
          console.error("Error fetching existing mapping:", fetchError);
          throw fetchError;
        }

        if (!existingMapping) {
          throw new Error(`Mapping with ID ${mapping.id} not found`);
        }

        // Create update payload with only the fields that need to be updated
        const updates: Partial<SimplifiedMapping> = {};

        if (mapping.faculty) {
          updates.faculty_1_id = mapping.faculty.id;
        }

        if (mapping.faculty2Id !== undefined) {
          updates.faculty_2_id = mapping.faculty2Id === "" ? null : mapping.faculty2Id;
        }

        // Apply the updates
        console.log("Applying partial updates:", updates);
        const { data: updatedMapping, error: updateError } = await supabase
          .from("simplified_subject_faculty_mappings")
          .update(updates)
          .eq("id", mapping.id)
          .select()
          .single();

        if (updateError) {
          console.error("Error updating mapping:", updateError);
          throw updateError;
        }

        // Return the updated mapping
        return {
          ...mapping,
          id: mapping.id
        };
      }

      // This is a full update or new mapping

      // Get the faculty_2_id from the secondary faculty
      let faculty2Id = null;

      // For lab subjects, faculty2Id is required
      if (mapping.subject && (mapping.subject.type === 'lab' || mapping.subject.type === 'laboratory')) {
        faculty2Id = mapping.faculty2Id || null;
        console.log("Lab subject detected, faculty2Id:", faculty2Id);
      }

      // Transform from UI model to database model
      const dbMapping: Partial<SimplifiedMapping> = {};

      // Only include fields that are provided in the mapping
      if (mapping.academicYear) dbMapping.academic_year = mapping.academicYear;
      if (mapping.department) dbMapping.department = mapping.department;
      if (mapping.semester) dbMapping.semester = mapping.semester;
      if (mapping.section) dbMapping.section = mapping.section;

      if (mapping.subject) {
        dbMapping.subject_id = mapping.subject.id;
        dbMapping.subject_code = mapping.subject.code;
        dbMapping.subject_name = mapping.subject.name;
        dbMapping.subject_type = mapping.subject.type === "lab" ? "laboratory" : mapping.subject.type;
      }

      if (mapping.faculty) {
        dbMapping.faculty_1_id = mapping.faculty.id;
      }

      if (mapping.faculty2Id !== undefined) {
        dbMapping.faculty_2_id = faculty2Id;
      }

      if (mapping.hoursPerWeek !== undefined) {
        dbMapping.hours_per_week = mapping.hoursPerWeek || 0;
      }

      if (mapping.classroom !== undefined) {
        dbMapping.classroom = mapping.classroom || "Not specified";
      }

      if (mapping.slotsPerWeek !== undefined) {
        dbMapping.slots_per_week = mapping.slotsPerWeek;
      }

      let result;
      if (mapping.id) {
        // Update existing mapping
        console.log(`Updating existing mapping with ID: ${mapping.id}`);
        const { data, error } = await supabase
          .from("simplified_subject_faculty_mappings")
          .update(dbMapping)
          .eq("id", mapping.id)
          .select()
          .single();

        if (error) {
          console.error("Error updating simplified mapping:", error);
          throw error;
        }
        result = data;
      } else {
        // For new mappings, ensure all required fields are present
        if (!dbMapping.academic_year || !dbMapping.department || !dbMapping.semester ||
            !dbMapping.section || !dbMapping.subject_id || !dbMapping.subject_code ||
            !dbMapping.subject_name || !dbMapping.subject_type || !dbMapping.faculty_1_id) {
          throw new Error("Missing required fields for creating a new mapping");
        }

        // Create new mapping
        console.log("Creating new simplified mapping");
        const { data, error } = await supabase
          .from("simplified_subject_faculty_mappings")
          .insert(dbMapping)
          .select()
          .single();

        if (error) {
          console.error("Error creating simplified mapping:", error);
          throw error;
        }
        result = data;
      }

      // Now handle lab slots if this is a lab subject and we have lab slots to save
      const isLabSubject = mapping.subject && (
        mapping.subject.type === 'laboratory' || mapping.subject.type === 'lab'
      );

      if (isLabSubject && mapping.labSlots) {
        // First, check if simplified_mapping_id column exists in lab_time_slots table
        console.log("Checking if simplified_mapping_id column exists in lab_time_slots table");
        const { data: columnCheck, error: columnCheckError } = await supabase
          .from('lab_time_slots')
          .select('simplified_mapping_id')
          .limit(1)
          .maybeSingle();

        const hasSimplifiedMappingId = !columnCheckError || !columnCheckError.message.includes('column "simplified_mapping_id" does not exist');
        console.log(`simplified_mapping_id column exists: ${hasSimplifiedMappingId}`);

        // First, delete any existing lab slots for this mapping
        if (mapping.id) {
          console.log(`Deleting existing lab slots for mapping ID: ${mapping.id}`);

          if (hasSimplifiedMappingId) {
            // Delete using simplified_mapping_id
            await supabase
              .from('lab_time_slots')
              .delete()
              .eq('simplified_mapping_id', mapping.id);
          } else {
            // Delete using mapping_id (legacy)
            await supabase
              .from('lab_time_slots')
              .delete()
              .eq('mapping_id', mapping.id);
          }
        }

        // Then insert new lab slots if provided
        if (mapping.labSlots && mapping.labSlots.length > 0) {
          console.log(`Inserting ${mapping.labSlots.length} lab slots`);

          const labSlotPayload = mapping.labSlots.map((slot, index) => {
            const basePayload = {
              day: slot.day,
              time_of_day: slot.timeOfDay,
              batch_name: slot.batch || `${mapping.section || ''}${index + 1}`,
              slot_order: index + 1
            };

            // Add the appropriate mapping ID field based on schema
            if (hasSimplifiedMappingId) {
              return {
                ...basePayload,
                simplified_mapping_id: result.id,
                mapping_id: result.id // Use the same ID for both fields to satisfy foreign key constraints
              };
            } else {
              return {
                ...basePayload,
                mapping_id: result.id
              };
            }
          });

          console.log("Lab slot payload:", labSlotPayload);

          const { error: labSlotError } = await supabase
            .from('lab_time_slots')
            .insert(labSlotPayload);

          if (labSlotError) {
            console.error("Error saving lab slots:", labSlotError);
            throw labSlotError;
          }

          // Update faculty availability for each lab slot
          console.log("Updating faculty availability for lab slots");
          for (const slot of mapping.labSlots) {
            try {
              // Call the new function to update faculty availability
              await FacultyAvailabilityService.updateFacultyAvailabilityFromLabSlots(
                result.faculty_1_id,
                result.faculty_2_id,
                slot.day,
                slot.timeOfDay
              );
              console.log(`Updated faculty availability for lab slot on ${slot.day} at ${slot.timeOfDay}`);
            } catch (availabilityError) {
              console.error("Error updating faculty availability:", availabilityError);
              // Continue with the next slot even if this one fails
            }
          }
        }
      }

      return {
        ...mapping,
        id: result.id
      };
    } catch (error) {
      console.error("Error in saveMapping:", error);
      throw error;
    }
  }

  /**
   * Get all mappings for a specific semester-section
   */
  static async getMappings(academicYear: string, department: string, semester: string, section: string): Promise<any[]> {
    try {
      console.log(`Fetching mappings for ${academicYear}, ${department}, ${semester}, ${section}`);

      const { data, error } = await supabase
        .from("simplified_subject_faculty_mappings")
        .select(`
          id,
          academic_year,
          department,
          semester,
          section,
          subject_id,
          subject_code,
          subject_name,
          subject_type,
          faculty_1_id,
          faculty_2_id,
          hours_per_week,
          classroom,
          slots_per_week,
          subjects(subject_short_id)
        `)
        .eq("academic_year", academicYear)
        .eq("department", department)
        .eq("semester", semester)
        .eq("section", section)
        .not("subject_code", "like", "DELETED_%");

      if (error) {
        console.error("Error fetching mappings:", error);
        throw error;
      }

      // Transform the data to match the expected format
      const transformedData = (data || []).map(mapping => {
        return {
          ...mapping,
          subject: {
            id: mapping.subject_id,
            code: mapping.subject_code,
            name: mapping.subject_name,
            type: mapping.subject_type,
            shortId: mapping.subjects?.subject_short_id
          }
        };
      });

      return transformedData;
    } catch (error) {
      console.error("Error in getMappings:", error);
      throw error;
    }
  }
}
