
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { TimetableService } from "@/services/TimetableService";

export type SubjectType = { 
  id: string; 
  code: string; 
  name: string; 
  type: "theory" | "lab" | "elective" 
};

interface UseSubjectLoaderProps {
  year: string;
  dept: string;
  sem: string;
  section: string;
  filtersValid: boolean;
}

export const useSubjectLoader = ({
  year,
  dept,
  sem,
  section,
  filtersValid
}: UseSubjectLoaderProps) => {
  const { toast } = useToast();
  const [subjects, setSubjects] = useState<SubjectType[]>([]);
  
  useEffect(() => {
    if (!filtersValid) {
      setSubjects([]);
      return;
    }
    
    TimetableService.fetchSubjects({ academicYear: year, department: dept, semester: sem, section })
      .then(subs => {
        setSubjects(subs.map(s => ({
          id: s.id,
          code: s.subject_code,
          name: s.subject_name,
          type: s.subject_type === "laboratory" ? "lab" :
              s.subject_type === "elective" ? "elective" : "theory"
        })));
      })
      .catch(() => toast({ 
        title: "Error", 
        description: "Cannot load subjects", 
        variant: "destructive" 
      }));
  }, [filtersValid, year, dept, sem, section, toast]);

  return subjects;
};
