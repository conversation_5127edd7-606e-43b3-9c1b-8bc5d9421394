
import React, { lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { AuthProvider } from "@/contexts/AuthContext";
import { StudentAuthProvider } from "@/contexts/StudentAuthContext";
import RequireAuth from "@/components/auth/RequireAuth";
import RoleBasedRoute from "@/components/auth/RoleBasedRoute";
import { LazyLoadingWrapper } from "@/components/ui/LazyLoadingWrapper";
import PerformanceMonitor from "@/components/debug/PerformanceMonitor";
import { preloadSubjectMappings } from "@/utils/subjectUtils";

// Core components that need to be loaded immediately
import Navbar from "@/components/layout/Navbar";
import Sidebar from "@/components/layout/Sidebar";

// Lazy load all page components for better performance
const NotFound = lazy(() => import("./pages/NotFound"));
const Dashboard = lazy(() => import("./pages/Dashboard"));
const Timetable = lazy(() => import("./pages/Timetable"));
const Subjects = lazy(() => import("./pages/Subjects"));
const UserManagement = lazy(() => import("./pages/UserManagement"));
const TimetableStructure = lazy(() => import("./pages/TimetableStructure"));
const Auth = lazy(() => import("./pages/Auth"));
const ClassTeacherManagement = lazy(() => import("./pages/ClassTeacherManagement"));
const FacultyAvailabilityReset = lazy(() => import("./pages/FacultyAvailabilityReset"));
const StudentManagement = lazy(() => import("./pages/StudentManagement"));
const AttendanceManagement = lazy(() => import("./pages/AttendanceManagement"));
const SequentialIAEntry = lazy(() => import("./components/internal-assessment/SequentialIAEntry"));
const LeaveManagement = lazy(() => import("./pages/LeaveManagement"));
const LeaveApprovals = lazy(() => import("./pages/LeaveApprovals"));
const HODLeaveApprovals = lazy(() => import("./pages/HODLeaveApprovals"));
const AcademicReports = lazy(() => import("./pages/AcademicReports"));
const FacultyReports = lazy(() => import("./pages/FacultyReports"));
const FacultyAttendanceSheet = lazy(() => import("./pages/FacultyAttendanceSheet"));
const FacultyAnalyticsDashboard = lazy(() => import("./pages/FacultyAnalyticsDashboard"));
const StudentAuthPage = lazy(() => import("./pages/StudentAuthPage"));
const StudentRegistrationPage = lazy(() => import("./pages/StudentRegistrationPage"));
const StudentLoginPage = lazy(() => import("./pages/StudentLoginPage"));
const StudentDashboardPage = lazy(() => import("./pages/StudentDashboardPage"));
const LandingPage = lazy(() => import("./pages/LandingPage"));
const ClassTeacherStudentManagement = lazy(() => import("./components/class-teacher/ClassTeacherStudentManagement"));
const AIQuizManagement = lazy(() => import("./pages/AIQuizManagement"));
const DesignShowcase = lazy(() => import("./pages/DesignShowcase"));
const FeedbackManagement = lazy(() => import("./pages/FeedbackManagement"));
const FeedbackTracking = lazy(() => import("./pages/FeedbackTracking"));
const StudentProctoring = lazy(() => import("./components/proctoring/StudentProctoring"));

// Create a new query client instance outside of the component
const queryClient = new QueryClient();

const App: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = React.useState(true);

  // Preload subject mappings on app start
  React.useEffect(() => {
    preloadSubjectMappings().catch(console.error);
  }, []);



  // CRITICAL FIX: Memoize MainLayout to prevent unnecessary re-renders
  const MainLayout = React.memo(({ children }: { children: React.ReactNode }) => {
    // CRITICAL FIX: Memoize the toggle function to prevent re-renders
    const toggleSidebar = React.useCallback(() => {
      setSidebarOpen(prev => !prev);
    }, []);

    return (
      <div className="min-h-screen bg-background antialiased">
        <Navbar toggleSidebar={toggleSidebar} />

        <div className="flex">
          <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

          <div className={`flex-1 transition-all duration-300 ease-in-out ${sidebarOpen ? "md:ml-64" : ""}`}>
            <main className="min-h-[calc(100vh-4rem)] animate-fade-in overflow-x-hidden">
              {children}
            </main>
          </div>
        </div>
      </div>
    );
  });

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        <TooltipProvider>
          <AuthProvider>
            <StudentAuthProvider>
              <Toaster />
              <Sonner />
              <PerformanceMonitor />
              <BrowserRouter>
            <Routes>
              <Route path="/" element={
                <LazyLoadingWrapper loadingText="Loading landing page...">
                  <LandingPage />
                </LazyLoadingWrapper>
              } />
              <Route path="/auth" element={
                <LazyLoadingWrapper loadingText="Loading authentication...">
                  <Auth />
                </LazyLoadingWrapper>
              } />
              <Route path="/student-auth" element={
                <LazyLoadingWrapper loadingText="Loading student authentication...">
                  <StudentAuthPage />
                </LazyLoadingWrapper>
              } />
              <Route path="/student-registration" element={
                <LazyLoadingWrapper loadingText="Loading student registration...">
                  <StudentRegistrationPage />
                </LazyLoadingWrapper>
              } />
              <Route path="/student-login" element={
                <LazyLoadingWrapper loadingText="Loading student login...">
                  <StudentLoginPage />
                </LazyLoadingWrapper>
              } />
              <Route path="/student-dashboard" element={
                <LazyLoadingWrapper loadingText="Loading student dashboard...">
                  <StudentDashboardPage />
                </LazyLoadingWrapper>
              } />
              <Route
                path="/dashboard"
                element={
                  <RequireAuth>
                    <MainLayout>
                      <LazyLoadingWrapper loadingText="Loading dashboard...">
                        <Dashboard />
                      </LazyLoadingWrapper>
                    </MainLayout>
                  </RequireAuth>
                }
              />
              <Route
                path="/timetable"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['timetable_coordinator']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading timetable...">
                          <Timetable />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/subjects"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['timetable_coordinator']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading subjects...">
                          <Subjects />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/users"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['college_admin']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading user management...">
                          <UserManagement />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/timetable-structure"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['timetable_coordinator']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading timetable structure...">
                          <TimetableStructure />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/class-teacher"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['timetable_coordinator']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading class teacher management...">
                          <ClassTeacherManagement />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/faculty-availability-reset"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['timetable_coordinator']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading faculty availability reset...">
                          <FacultyAvailabilityReset />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/students"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['college_admin', 'timetable_coordinator', 'class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading student management...">
                          <StudentManagement />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/attendance"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['faculty', 'class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading attendance management...">
                          <AttendanceManagement />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/sequential-ia-entry"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['faculty']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading internal assessment...">
                          <SequentialIAEntry />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/leave-management"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['faculty']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading leave management...">
                          <LeaveManagement />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/leave-approvals"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['hod']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading leave approvals...">
                          <LeaveApprovals />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/hod-leave-approvals"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['principal']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading HOD leave approvals...">
                          <HODLeaveApprovals />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/academic-reports"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading academic reports...">
                          <AcademicReports />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/faculty-reports"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['faculty', 'class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading faculty reports...">
                          <FacultyReports />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/faculty-attendance-sheet"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['faculty', 'class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading attendance sheet...">
                          <FacultyAttendanceSheet />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/faculty-analytics"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['faculty', 'class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading analytics dashboard...">
                          <FacultyAnalyticsDashboard />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/class-teacher-students"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading student management...">
                          <ClassTeacherStudentManagement />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/ai-quiz-management"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['faculty', 'class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading AI quiz management...">
                          <AIQuizManagement />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/student-proctoring"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['faculty', 'class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading student proctoring...">
                          <StudentProctoring />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/feedback-management"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['hod']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading feedback management...">
                          <FeedbackManagement />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/feedback-tracking"
                element={
                  <RequireAuth>
                    <RoleBasedRoute allowedRoles={['class_teacher']}>
                      <MainLayout>
                        <LazyLoadingWrapper loadingText="Loading feedback tracking...">
                          <FeedbackTracking />
                        </LazyLoadingWrapper>
                      </MainLayout>
                    </RoleBasedRoute>
                  </RequireAuth>
                }
              />
              <Route
                path="/design-showcase"
                element={
                  <RequireAuth>
                    <MainLayout>
                      <LazyLoadingWrapper loadingText="Loading design showcase...">
                        <DesignShowcase />
                      </LazyLoadingWrapper>
                    </MainLayout>
                  </RequireAuth>
                }
              />
              <Route path="*" element={
                <LazyLoadingWrapper loadingText="Loading page...">
                  <NotFound />
                </LazyLoadingWrapper>
              } />
            </Routes>
            </BrowserRouter>
            </StudentAuthProvider>
          </AuthProvider>
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
