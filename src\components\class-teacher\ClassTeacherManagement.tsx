import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { ClassTeacherService, FacultyForSection } from '@/services/ClassTeacherService';
import { Users, UserCheck, AlertCircle, BookOpen, Building2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { defaultFilterOptions } from '@/components/subjects/AllotmentFilterOptions';
import { useUserDepartment } from '@/hooks/useUserDepartment';

const ClassTeacherManagement: React.FC = () => {
  const [academicYear, setAcademicYear] = useState<string>('');
  const [semester, setSemester] = useState<string>('');
  const [section, setSection] = useState<string>('');
  const [faculty, setFaculty] = useState<FacultyForSection[]>([]);
  const [loading, setLoading] = useState(false);
  const [assigningFaculty, setAssigningFaculty] = useState<string | null>(null);
  const { toast } = useToast();

  // Get user's department information
  const { department, departmentName, fullName, loading: departmentLoading, error: departmentError } = useUserDepartment();

  // Get filter options from the same source as Subject Allotment
  const filterOptions = defaultFilterOptions;

  // Set current academic year as default
  useEffect(() => {
    if (!academicYear && filterOptions.yearsList.length > 0) {
      setAcademicYear(filterOptions.yearsList[0]); // Current academic year
    }
  }, [academicYear, filterOptions.yearsList]);

  // Load faculty when all required fields are selected and department is available
  useEffect(() => {
    if (academicYear && department && semester && section && !departmentLoading) {
      loadFacultyForSection();
    } else {
      setFaculty([]);
    }
  }, [academicYear, department, semester, section, departmentLoading]);

  const loadFacultyForSection = async () => {
    if (!department) {
      toast({
        title: 'Error',
        description: 'User department not found. Please contact administrator.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);
      const facultyData = await ClassTeacherService.getFacultyForSection(
        academicYear,
        department,
        semester,
        section
      );
      setFaculty(facultyData);
    } catch (error) {
      console.error('Error loading faculty:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to load faculty for this section.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAssignClassTeacher = async (facultyId: string) => {
    if (!department) {
      toast({
        title: 'Error',
        description: 'User department not found. Please contact administrator.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setAssigningFaculty(facultyId);

      await ClassTeacherService.assignClassTeacher(
        facultyId,
        academicYear,
        department,
        semester,
        section
      );

      toast({
        title: 'Success',
        description: 'Class teacher assigned successfully.',
      });

      // Reload faculty data to update the UI
      await loadFacultyForSection();
    } catch (error) {
      console.error('Error assigning class teacher:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to assign class teacher.',
        variant: 'destructive',
      });
    } finally {
      setAssigningFaculty(null);
    }
  };

  const handleRemoveClassTeacher = async () => {
    if (!department) {
      toast({
        title: 'Error',
        description: 'User department not found. Please contact administrator.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);

      await ClassTeacherService.removeClassTeacher(
        academicYear,
        department,
        semester,
        section
      );

      toast({
        title: 'Success',
        description: 'Class teacher assignment removed successfully.',
      });

      // Reload faculty data to update the UI
      await loadFacultyForSection();
    } catch (error) {
      console.error('Error removing class teacher:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove class teacher assignment.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const currentClassTeacher = faculty.find(f => f.isClassTeacher);

  // Show loading state while fetching department info
  if (departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading department information...</span>
        </div>
      </div>
    );
  }

  // Show error if department info couldn't be loaded
  if (departmentError || !department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {departmentError || 'Unable to load your department information. Please contact administrator to set your department.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Users className="h-6 w-6 text-primary" />
        <h1 className="text-3xl font-bold">Class Teacher Management</h1>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Managing:</strong> {departmentName} Department
          {fullName && <span className="ml-4"><strong>User:</strong> {fullName}</span>}
        </AlertDescription>
      </Alert>

      {/* Selection Form */}
      <Card>
        <CardHeader>
          <CardTitle>Select Semester & Section</CardTitle>
          <CardDescription>
            Choose the academic year, semester, and section to manage class teacher assignments for {departmentName}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Academic Year</label>
              <Select value={academicYear} onValueChange={setAcademicYear}>
                <SelectTrigger>
                  <SelectValue placeholder="Select academic year" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.yearsList.map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Semester</label>
              <Select value={semester} onValueChange={setSemester}>
                <SelectTrigger>
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.semsList.map((sem) => (
                    <SelectItem key={sem} value={sem}>
                      Semester {sem}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Section</label>
              <Select value={section} onValueChange={setSection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.sectionsList.map((sec) => (
                    <SelectItem key={sec} value={sec}>
                      Section {sec}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Faculty List */}
      {academicYear && semester && section && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Faculty Teaching in {departmentName} - Semester {semester} Section {section} ({academicYear})
            </CardTitle>
            <CardDescription>
              Faculty members from {departmentName} handling subjects for this semester-section combination.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : faculty.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  No faculty members found for {departmentName} - Semester {semester} Section {section} ({academicYear}).
                  <br />
                  <br />
                  <strong>Possible reasons:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>No subject mappings exist for this semester-section combination</li>
                    <li>Faculty assignments are missing in the subject allotment</li>
                    <li>No faculty members are registered in the {departmentName} department</li>
                    <li>The selected academic year, semester, or section may not have any active subjects</li>
                  </ul>
                  <br />
                  <strong>Next steps:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Ensure subject allotments are completed for this semester-section</li>
                    <li>Verify faculty members are properly registered with the {departmentName} department</li>
                    <li>Check that the academic year, semester, and section values are correct</li>
                  </ul>
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-4">
                {currentClassTeacher && (
                  <Alert className="border-green-200 bg-green-50">
                    <UserCheck className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      <strong>{currentClassTeacher.full_name}</strong> is currently assigned as the class teacher for this section.
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRemoveClassTeacher}
                        disabled={loading}
                        className="ml-4"
                      >
                        Remove Assignment
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid gap-4">
                  {faculty.map((facultyMember) => (
                    <div
                      key={facultyMember.id}
                      className={`p-4 border rounded-lg ${
                        facultyMember.isClassTeacher ? 'border-green-300 bg-green-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-lg">{facultyMember.full_name}</h3>
                            {facultyMember.isClassTeacher && (
                              <Badge variant="default" className="bg-green-600">
                                <UserCheck className="h-3 w-3 mr-1" />
                                Class Teacher
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            {facultyMember.designation} • {facultyMember.email}
                          </p>
                          <div className="space-y-1">
                            <p className="text-sm font-medium">Subjects Teaching:</p>
                            <div className="flex flex-wrap gap-1">
                              {facultyMember.subjects.map((subject, index) => (
                                <Badge
                                  key={index}
                                  variant={subject.includes('No subjects assigned') ? "outline" : "secondary"}
                                  className={`text-xs ${subject.includes('No subjects assigned') ? 'text-orange-600 border-orange-300' : ''}`}
                                >
                                  {subject}
                                </Badge>
                              ))}
                            </div>
                            {facultyMember.subjects.some(subject => subject.includes('No subjects assigned')) && (
                              <p className="text-xs text-orange-600 mt-1">
                                ⚠️ This faculty is not currently teaching in this semester-section but can still be assigned as class teacher.
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="ml-4">
                          {!facultyMember.isClassTeacher && !currentClassTeacher && (
                            <Button
                              onClick={() => handleAssignClassTeacher(facultyMember.id)}
                              disabled={assigningFaculty === facultyMember.id}
                              className="min-w-[120px]"
                            >
                              {assigningFaculty === facultyMember.id ? (
                                <div className="flex items-center gap-2">
                                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                                  Assigning...
                                </div>
                              ) : (
                                'Assign as Class Teacher'
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ClassTeacherManagement;
