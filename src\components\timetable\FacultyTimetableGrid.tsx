import React, { useEffect, useState } from 'react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TimetableService, TimeStructure } from '@/services/TimetableService';
import { TimetableExportService, ExportData } from '@/services/TimetableExportService';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from '@/components/ui/use-toast';

import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Download, RefreshCw } from 'lucide-react';
import { getDisplaySubjectCode } from '@/utils/subjectUtils';

interface FacultyTimetableGridProps {
  academicYear: string;
  department: string;
  facultyId: string;
  facultyName: string;
}

interface FacultyTimetableSlot {
  id: string;
  academic_year: string;
  department: string;
  semester: string;
  section: string;
  day: string;
  time_slot: string;
  subject_code: string;
  subject_name: string;
  subject_short_id: string;
  subject_type: string;
  room_number?: string;
}

export const FacultyTimetableGrid: React.FC<FacultyTimetableGridProps> = ({
  academicYear,
  department,
  facultyId,
  facultyName,
}) => {
  const [days, setDays] = useState<string[]>([]);
  const [timeSlots, setTimeSlots] = useState<any[]>([]);
  const [timetableData, setTimetableData] = useState<FacultyTimetableSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [timeStructure, setTimeStructure] = useState<TimeStructure | null>(null);
  const [processedTimetable, setProcessedTimetable] = useState<Record<string, Record<string, any>>>({});
  const { toast } = useToast();

  // Load time structure
  useEffect(() => {
    const loadTimeStructure = async () => {
      if (department && academicYear) {
        try {
          const structure = await TimetableService.fetchTimeStructure({
            academicYear,
            department
          });

          if (structure) {
            setTimeStructure(structure);
            setDays(structure.working_days);
            generateTimeSlots(structure);
          } else {
            toast({
              title: "Time Structure Missing",
              description: "No time structure found for the selected department and academic year.",
              variant: "destructive"
            });
          }
        } catch (error) {
          console.error("Error loading time structure:", error);
          toast({
            title: "Error",
            description: "Failed to load time structure.",
            variant: "destructive"
          });
        }
      }
    };

    loadTimeStructure();
  }, [department, academicYear, toast]);

  // Load faculty timetable data
  useEffect(() => {
    const loadData = async () => {
      if (!facultyName || !academicYear) return;

      setIsLoading(true);
      try {
        console.log(`Loading timetable for faculty name: ${facultyName}`);

        // Use the RPC function that uses your exact SQL query
        const { data, error } = await supabase.rpc('get_faculty_timetable_by_name', {
          p_faculty_name: facultyName,
          p_academic_year: academicYear
        });

        if (error) {
          console.error("RPC error:", error);

          // Fall back to the direct query approach
          await loadTimetableFromFacultyTimetables();
          return;
        }

        console.log(`RPC returned ${data?.length || 0} slots`);
        setTimetableData(data || []);

        // Log the days found in the data
        if (data && data.length > 0) {
          const days = [...new Set(data.map(slot => slot.day))];
          console.log("Days found in RPC:", days);

          // Log slots by day
          days.forEach(day => {
            const slotsForDay = data.filter(slot => slot.day === day);
            console.log(`${day}: ${slotsForDay.length} slots`);
            slotsForDay.forEach(slot => {
              console.log(`  - ${slot.subject_code} (${slot.time_slot})`);
            });
          });
        }
      } catch (error) {
        console.error("Error loading faculty timetable:", error);
        toast({
          title: "Error",
          description: "Failed to load faculty timetable data.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [facultyName, academicYear, supabase, toast]);

  // Add this function to load data directly from faculty_timetables table
  const loadTimetableFromFacultyTimetables = async () => {
    if (!facultyName || !academicYear) return;

    setIsLoading(true);
    try {
      console.log(`Loading timetable for faculty name: ${facultyName}`);

      // Use the exact SQL query that works, adapted for Supabase
      const { data, error } = await supabase
        .from('faculty_timetables')
        .select(`
          id,
          day,
          time_slot,
          subject_code,
          subject_name,
          subject_type,
          semester,
          section,
          department,
          room_number
        `)
        .eq('academic_year', academicYear)
        .order('day')
        .order('time_slot');

      if (error) {
        console.error("Error querying faculty_timetables:", error);

        // Try with a join query similar to your SQL
        console.log("Trying with join query...");
        const { data: joinData, error: joinError } = await supabase
          .from('faculty_timetables')
          .select(`
            id,
            day,
            time_slot,
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            department,
            room_number
          `)
          .eq('academic_year', academicYear)
          .filter('faculty_id', 'in', `(
            select id from employee_details where full_name = '${facultyName}'
          )`)
          .order('day')
          .order('time_slot');

        if (joinError) {
          throw joinError;
        }

        console.log(`Join query returned ${joinData?.length || 0} slots`);
        setTimetableData(joinData || []);
        return;
      }

      console.log(`Query returned ${data?.length || 0} slots`);

      // Filter the data to only include slots for this faculty
      const filteredData = data?.filter(slot => {
        // We need to check if this slot belongs to the faculty
        // Since we don't have the faculty_id in the result, we'll use a custom RPC
        return true; // For now, include all slots
      }) || [];

      console.log(`Filtered to ${filteredData.length} slots for ${facultyName}`);
      setTimetableData(filteredData);

      // Log the days found in the data
      if (filteredData.length > 0) {
        const days = [...new Set(filteredData.map(slot => slot.day))];
        console.log("Days found in query:", days);

        // Log slots by day
        days.forEach(day => {
          const slotsForDay = filteredData.filter(slot => slot.day === day);
          console.log(`${day}: ${slotsForDay.length} slots`);
          slotsForDay.forEach(slot => {
            console.log(`  - ${slot.subject_code} (${slot.time_slot})`);
          });
        });
      }
    } catch (error) {
      console.error("Error loading from faculty_timetables:", error);
      toast({
        title: "Error",
        description: "Failed to load faculty timetable data.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add this function as a fallback using your exact SQL
  const loadWithDirectSQL = async () => {
    if (!facultyName || !academicYear) return;

    setIsLoading(true);
    try {
      console.log(`Loading with direct SQL for faculty name: ${facultyName}`);

      // Use the SQL query you provided
      const { data, error } = await supabase.from('faculty_timetables')
        .select(`
          id,
          day,
          time_slot,
          subject_code,
          subject_name,
          subject_type,
          semester,
          section,
          department,
          room_number
        `)
        .eq('academic_year', academicYear)
        .filter('faculty_id', 'in', `(
          select id from employee_details where full_name = '${facultyName}'
        )`)
        .order('day')
        .order('time_slot');

      if (error) throw error;

      console.log(`Direct SQL returned ${data?.length || 0} slots`);
      setTimetableData(data || []);

      // Log the days found in the data
      if (data && data.length > 0) {
        const days = [...new Set(data.map(slot => slot.day))];
        console.log("Days found in direct SQL:", days);

        // Log slots by day
        days.forEach(day => {
          const slotsForDay = data.filter(slot => slot.day === day);
          console.log(`${day}: ${slotsForDay.length} slots`);
          slotsForDay.forEach(slot => {
            console.log(`  - ${slot.subject_code} (${slot.time_slot})`);
          });
        });
      }
    } catch (error) {
      console.error("Error with direct SQL:", error);
      toast({
        title: "Error",
        description: "Failed to load faculty timetable data with SQL.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add a button to trigger the direct SQL as a fallback
  const RefreshButton = () => (
    <Button
      variant="outline"
      size="sm"
      onClick={loadWithDirectSQL}
      className="ml-2"
    >
      Refresh Data
    </Button>
  );

  // Process timetable data for display
  useEffect(() => {
    if (!timeStructure || !timetableData.length) {
      setProcessedTimetable({});
      return;
    }

    console.log("Processing timetable data for display...");
    console.log("Working days from time structure:", days);
    console.log("Time slots:", timeSlots.map(slot => slot.value));
    console.log("Timetable data length:", timetableData.length);

    // Get all unique days from the data
    const uniqueDaysInData = [...new Set(timetableData.map(slot => slot.day))];
    console.log("Unique days in data:", uniqueDaysInData);

    // Make sure we're using all days from both the time structure and the data
    const allDays = [...new Set([...days, ...uniqueDaysInData])];
    console.log("All days to display:", allDays);

    // If days from the data aren't in the time structure, update the days state
    if (uniqueDaysInData.some(day => !days.includes(day))) {
      console.log("Updating days state to include all days from data");
      setDays(allDays);
    }

    const processed: Record<string, Record<string, any>> = {};

    // Initialize the structure with all days and time slots
    allDays.forEach(day => {
      processed[day] = {};
      timeSlots.forEach(slot => {
        processed[day][slot.value] = [];
      });
    });

    // Populate with timetable data
    timetableData.forEach(slot => {
      // Make sure the day and time_slot exist in our structure
      if (!processed[slot.day]) {
        console.log(`Creating missing day in processed structure: ${slot.day}`);
        processed[slot.day] = {};
        timeSlots.forEach(ts => {
          processed[slot.day][ts.value] = [];
        });
      }

      if (!processed[slot.day][slot.time_slot]) {
        console.log(`Creating missing time slot in processed structure: ${slot.day} ${slot.time_slot}`);
        processed[slot.day][slot.time_slot] = [];
      }

      processed[slot.day][slot.time_slot].push(slot);
    });

    // Log the processed structure
    console.log("Processed timetable structure:");
    Object.keys(processed).forEach(day => {
      const filledSlots = Object.keys(processed[day]).filter(ts => processed[day][ts].length > 0);
      console.log(`${day}: ${filledSlots.length} filled time slots`);
      filledSlots.forEach(ts => {
        console.log(`  - ${ts}: ${processed[day][ts].length} slots`);
      });
    });

    setProcessedTimetable(processed);
  }, [timetableData, timeStructure, days, timeSlots]);

  // Helper function to generate time slots based on time structure
  const generateTimeSlots = (structure: TimeStructure) => {
    const slots: any[] = [];
    let periodCounter = 1;

    // First half periods
    let currentTime = structure.first_half_start_time;
    while (currentTime < structure.tea_break_start_time) {
      const endTime = addMinutes(currentTime, structure.period_duration);
      slots.push({
        label: `Period ${periodCounter} (${currentTime}-${endTime})`,
        value: `${currentTime}-${endTime}`,
        type: 'regular'
      });
      currentTime = endTime;
      periodCounter++;
    }

    // Tea break
    slots.push({
      label: `Tea Break (${structure.tea_break_start_time}-${structure.tea_break_end_time})`,
      value: `${structure.tea_break_start_time}-${structure.tea_break_end_time}`,
      type: 'break'
    });

    // Second half before lunch
    currentTime = structure.tea_break_end_time;
    while (currentTime < structure.lunch_break_start_time) {
      const endTime = addMinutes(currentTime, structure.period_duration);
      slots.push({
        label: `Period ${periodCounter} (${currentTime}-${endTime})`,
        value: `${currentTime}-${endTime}`,
        type: 'regular'
      });
      currentTime = endTime;
      periodCounter++;
    }

    // Lunch break
    slots.push({
      label: `Lunch Break (${structure.lunch_break_start_time}-${structure.lunch_break_end_time})`,
      value: `${structure.lunch_break_start_time}-${structure.lunch_break_end_time}`,
      type: 'break'
    });

    // Second half after lunch
    currentTime = structure.lunch_break_end_time;
    for (let i = 0; i < structure.periods_in_second_half; i++) {
      const endTime = addMinutes(currentTime, structure.period_duration);
      slots.push({
        label: `Period ${periodCounter} (${currentTime}-${endTime})`,
        value: `${currentTime}-${endTime}`,
        type: 'regular'
      });
      currentTime = endTime;
      periodCounter++;
    }

    setTimeSlots(slots);
  };

  // Helper function to add minutes to a time string
  const addMinutes = (timeStr: string, minutes: number): string => {
    const [hours, mins] = timeStr.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, mins, 0, 0);
    date.setMinutes(date.getMinutes() + minutes);
    return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  // Render a cell with timetable data
  const renderCell = (day: string, timeSlot: string) => {
    // Find all slots for this day and time slot
    const slots = timetableData.filter(
      slot => slot.day.toLowerCase() === day.toLowerCase() && slot.time_slot === timeSlot
    );

    // Debug logging for this specific cell
    if (slots.length > 0) {
      console.log(`Found ${slots.length} slots for ${day} at ${timeSlot}`);
    }

    if (slots.length === 0) {
      return <TableCell key={`${day}-${timeSlot}`} className="p-1 border" />;
    }

    // Group theory slots by subject short ID to combine them in display
    const theorySlots = slots.filter(slot => slot.subject_type === 'theory');
    const labSlots = slots.filter(slot => slot.subject_type !== 'theory');

    // Group theory slots by subject short ID
    const groupedTheorySlots: Record<string, FacultyTimetableSlot[]> = {};
    theorySlots.forEach(slot => {
      const key = getDisplaySubjectCode(slot.subject_code, slot.subject_short_id);
      if (!groupedTheorySlots[key]) {
        groupedTheorySlots[key] = [];
      }
      groupedTheorySlots[key].push(slot);
    });

    return (
      <TableCell key={`${day}-${timeSlot}`} className="p-1 border">
        {/* Render grouped theory slots with enhanced display format */}
        {Object.entries(groupedTheorySlots).map(([subjectId, slotsGroup]) => {
          // Get room number from the first slot (assuming all slots for same subject have same room)
          const roomNumber = slotsGroup[0]?.room_number;

          // Create the enhanced display format: "SubjectID(SemSection) SubjectID(SemSection) Room"
          const formattedText = slotsGroup.map(slot =>
            `${subjectId}(${slot.semester}${slot.section})`
          ).join(' ');

          // Format: "ML(6A) ML(6B) 301"
          const displayText = roomNumber
            ? `${formattedText} ${roomNumber}`
            : formattedText;

          return (
            <div key={`theory-${subjectId}`} className="p-2 mb-1 text-xs bg-green-50 rounded border border-green-200">
              <div className="font-semibold leading-tight text-green-800">
                {subjectId} {roomNumber ? `(${roomNumber})` : ''}
              </div>
              <div className="text-xs text-green-600 mt-0.5">{displayText}</div>
            </div>
          );
        })}

        {/* Render lab slots with compact layout */}
        {labSlots.map((slot: FacultyTimetableSlot) => (
          <div key={slot.id} className="p-2 mb-1 text-xs bg-blue-50 rounded border border-blue-200">
            <div className="font-semibold leading-tight text-blue-800">
              {getDisplaySubjectCode(slot.subject_code, slot.subject_short_id)}
            </div>
            <div className="text-xs text-blue-600 mt-0.5">
              Sem {slot.semester}, Sec {slot.section}
              {slot.room_number && ` • Room: ${slot.room_number}`}
            </div>
          </div>
        ))}
      </TableCell>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  const debugInfo = React.useMemo(() => {
    if (!timetableData.length) return null;

    // Count slots by day
    const slotsByDay = timetableData.reduce((acc, slot) => {
      acc[slot.day] = (acc[slot.day] || 0) + 1;
      return acc;
    }, {});

    // Count slots by semester and section
    const slotsBySemesterSection = timetableData.reduce((acc, slot) => {
      const key = `Sem ${slot.semester}, Sec ${slot.section}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});

    return (
      <div className="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded">
        <details>
          <summary className="cursor-pointer font-medium">Debug Information</summary>
          <div className="mt-2">
            <p>Total slots: {timetableData.length}</p>
            <p>Slots by day:</p>
            <ul className="list-disc pl-5">
              {Object.entries(slotsByDay).map(([day, count]) => (
                <li key={day}>{day}: {count}</li>
              ))}
            </ul>
            <p>Slots by class:</p>
            <ul className="list-disc pl-5">
              {Object.entries(slotsBySemesterSection).map(([semSec, count]) => (
                <li key={semSec}>{semSec}: {count}</li>
              ))}
            </ul>
          </div>
        </details>
      </div>
    );
  }, [timetableData]);

  // Export functionality
  const handleExport = async (format: "pdf" | "excel") => {
    if (!timeStructure) {
      toast({
        title: "Missing Time Structure",
        description: "Time structure is required for export.",
        variant: "destructive"
      });
      return;
    }

    if (timetableData.length === 0) {
      toast({
        title: "No Timetable Data",
        description: `No timetable data found for ${facultyName}.`,
        variant: "destructive"
      });
      return;
    }

    try {
      // Convert faculty timetable data to TimetableSlot format
      const exportTimetableData = timetableData.map((slot: FacultyTimetableSlot) => ({
        id: slot.id,
        day: slot.day,
        time_slot: slot.time_slot,
        subject_code: slot.subject_code,
        subject_short_id: slot.subject_code,
        subject_name: slot.subject_name,
        subject_type: slot.subject_type,
        semester: slot.semester,
        section: slot.section,
        department: slot.department,
        room_number: slot.room_number,
        faculty_id: facultyId,
        academic_year: academicYear
      }));

      // Prepare export data
      const exportData: ExportData = {
        timetableData: exportTimetableData,
        timeStructure: timeStructure,
        metadata: {
          academicYear,
          department,
          facultyName,
          selectedFaculty: facultyId,
          view: 'faculty'
        }
      };

      // Show loading toast
      toast({
        title: `Exporting as ${format.toUpperCase()}`,
        description: `Preparing ${facultyName}'s timetable for download...`
      });

      // Export based on format
      if (format === 'pdf') {
        await TimetableExportService.exportToPDF(exportData);
      } else {
        await TimetableExportService.exportToExcel(exportData);
      }

      // Show success toast
      toast({
        title: "Export Successful",
        description: `${facultyName}'s timetable has been exported as ${format.toUpperCase()}.`,
        variant: "default"
      });

    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export Failed",
        description: "Failed to export timetable. Please try again.",
        variant: "destructive"
      });
    }
  };

  const RefreshButton = () => (
    <Button
      variant="outline"
      size="sm"
      onClick={() => window.location.reload()}
      className="ml-4"
    >
      <RefreshCw className="w-4 h-4 mr-2" />
      Refresh
    </Button>
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">
          {facultyName}'s Timetable - {academicYear}
        </h2>
        <div className="flex items-center gap-2">
          {timetableData.length > 0 && timeStructure && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleExport("pdf")}
              >
                <Download className="w-4 h-4 mr-2" />
                Export PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleExport("excel")}
              >
                <Download className="w-4 h-4 mr-2" />
                Export Excel
              </Button>
            </>
          )}
          <RefreshButton />
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-2">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-[300px] w-full" />
        </div>
      ) : timetableData.length === 0 ? (
        <div className="p-4 border rounded-lg bg-card">
          <p className="text-center text-muted-foreground">
            No timetable data found for this faculty member.
          </p>
        </div>
      ) : (
        <div className="border rounded-lg bg-card">
          <Table>
            <TableCaption>Faculty Timetable for {facultyName}</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">Time / Day</TableHead>
                {days.map((day) => (
                  <TableHead key={day} className="text-center">
                    {day}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {timeSlots.map((slot) => (
                <TableRow key={slot.value}>
                  <TableCell className="font-medium text-xs">
                    {slot.label}
                  </TableCell>
                  {days.map((day) => renderCell(day, slot.value))}
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Add a debug section showing raw data */}
          <details className="p-2 text-xs">
            <summary className="cursor-pointer font-medium">Raw Data ({timetableData.length} slots)</summary>
            <div className="mt-2 overflow-auto max-h-[300px]">
              <table className="w-full border-collapse border">
                <thead>
                  <tr>
                    <th className="border p-1">Day</th>
                    <th className="border p-1">Time</th>
                    <th className="border p-1">Subject</th>
                    <th className="border p-1">Type</th>
                    <th className="border p-1">Class</th>
                  </tr>
                </thead>
                <tbody>
                  {timetableData.map((slot, index) => (
                    <tr key={index}>
                      <td className="border p-1">{slot.day}</td>
                      <td className="border p-1">{slot.time_slot}</td>
                      <td className="border p-1">{slot.subject_code}</td>
                      <td className="border p-1">{slot.subject_type}</td>
                      <td className="border p-1">Sem {slot.semester}, Sec {slot.section}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </details>
        </div>
      )}
    </div>
  );
};

