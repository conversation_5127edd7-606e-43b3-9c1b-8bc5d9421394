import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { TimetableSlot, TimeStructure } from './TimetableService';

export interface ExportData {
  timetableData: TimetableSlot[];
  timeStructure: TimeStructure;
  metadata: {
    academicYear: string;
    department: string;
    semester?: string;
    section?: string;
    facultyName?: string;
    selectedFaculty?: string;
    view: 'class' | 'faculty';
  };
}

export interface ProcessedTimetableData {
  days: string[];
  timeSlots: Array<{
    time: string;
    label: string;
    isPeriod: boolean;
    isBreak: boolean;
  }>;
  timetableGrid: Record<string, Record<string, TimetableSlot[]>>;
  labSpans?: Record<string, Record<string, { colSpan: number; isStart: boolean; labId: string }>>;
}

export class TimetableExportService {
  // FORCE RELOAD: v2.0 - Fixed getLabDisplayName issue
  /**
   * Generate the exact time structure as shown in the webapp
   */
  static generateWebAppTimeStructure(timeStructure: TimeStructure) {
    const slots = [];

    // First half periods
    const firstHalfStart = new Date(`1970-01-01T${timeStructure.first_half_start_time}`);
    const theoryDuration = timeStructure.theory_class_duration;

    let currentTime = new Date(firstHalfStart);
    for (let i = 0; i < timeStructure.periods_in_first_half; i++) {
      const endTime = new Date(currentTime.getTime() + theoryDuration * 60000);
      slots.push({
        time: this.formatTime(currentTime),
        label: `${this.formatTime(currentTime)}-${this.formatTime(endTime)}`,
        isPeriod: true,
        isBreak: false,
        periodNumber: i + 1
      });
      currentTime = new Date(endTime);
    }

    // Tea break
    slots.push({
      time: 'tea_break',
      label: 'Tea Break',
      isPeriod: false,
      isBreak: true,
      periodNumber: null
    });

    // Second half periods
    const secondHalfStart = new Date(`1970-01-01T${timeStructure.second_half_start_time}`);
    currentTime = new Date(secondHalfStart);

    for (let i = 0; i < timeStructure.periods_in_second_half; i++) {
      const endTime = new Date(currentTime.getTime() + theoryDuration * 60000);
      slots.push({
        time: this.formatTime(currentTime),
        label: `${this.formatTime(currentTime)}-${this.formatTime(endTime)}`,
        isPeriod: true,
        isBreak: false,
        periodNumber: timeStructure.periods_in_first_half + i + 1
      });
      currentTime = new Date(endTime);

      // Add lunch break after the appropriate period
      if (i === Math.floor(timeStructure.periods_in_second_half / 2) - 1) {
        slots.push({
          time: 'lunch_break',
          label: 'Lunch Break',
          isPeriod: false,
          isBreak: true,
          periodNumber: null
        });
      }
    }

    return slots;
  }

  /**
   * Process data to match exact webapp structure
   */
  static processDataForWebAppExport(exportData: ExportData) {
    const { timetableData, timeStructure, metadata } = exportData;

    // Generate exact time structure as webapp
    const timeSlots = this.generateWebAppTimeStructure(timeStructure);
    const days = timeStructure.working_days;

    console.log('Generated webapp time structure:', timeSlots);

    // Initialize grid with all slots (including breaks)
    const timetableGrid: Record<string, Record<string, TimetableSlot[]>> = {};
    days.forEach(day => {
      timetableGrid[day] = {};
      timeSlots.forEach(slot => {
        timetableGrid[day][slot.time] = [];
      });
    });

    // Populate grid with timetable data
    timetableData.forEach(slot => {
      // Find matching time slot
      const matchingTimeSlot = timeSlots.find(ts =>
        ts.isPeriod && ts.time === slot.time_slot
      );

      if (matchingTimeSlot && timetableGrid[slot.day]) {
        timetableGrid[slot.day][matchingTimeSlot.time].push(slot);
      } else {
        console.warn('Could not match slot:', slot.time_slot, 'Available:', timeSlots.map(ts => ts.time));
      }
    });

    return {
      days,
      timeSlots,
      timetableGrid
    };
  }

  /**
   * Expand a multi-hour lab slot into individual period slots
   */
  private static expandLabSlot(labSlot: TimetableSlot): TimetableSlot[] {
    const expandedSlots: TimetableSlot[] = [];

    // Define standard period time slots
    const standardPeriods = [
      "08:30-09:25", // Period 1
      "09:25-10:20", // Period 2
      "10:35-11:30", // Period 3
      "11:30-12:25", // Period 4
      "13:15-14:10", // Period 5
      "14:10-15:05", // Period 6
      "15:05-16:00"  // Period 7
    ];

    // Map multi-hour lab slots to individual periods
    if (labSlot.time_slot === "08:30-11:30") {
      // 3-hour morning lab: Periods 1, 2, 3
      // IMPORTANT: Period 3 is 10:35-11:30, NOT 11:30-12:25
      expandedSlots.push(
        { ...labSlot, time_slot: "08:30-09:25" },  // Period 1
        { ...labSlot, time_slot: "09:25-10:20" },  // Period 2
        { ...labSlot, time_slot: "10:35-11:30" }   // Period 3 (after tea break)
      );
      console.log(`🔧 Expanded 08:30-11:30 lab to: P1(08:30-09:25), P2(09:25-10:20), P3(10:35-11:30)`);
    } else if (labSlot.time_slot === "13:15-16:00") {
      // 3-hour afternoon lab: Periods 5, 6, 7
      expandedSlots.push(
        { ...labSlot, time_slot: "13:15-14:10" },
        { ...labSlot, time_slot: "14:10-15:05" },
        { ...labSlot, time_slot: "15:05-16:00" }
      );
      console.log(`🔧 Expanded 13:15-16:00 lab to: P5(13:15-14:10), P6(14:10-15:05), P7(15:05-16:00)`);
    } else if (labSlot.time_slot === "08:30-10:20") {
      // 2-hour morning lab: Periods 1, 2
      expandedSlots.push(
        { ...labSlot, time_slot: "08:30-09:25" },  // Period 1
        { ...labSlot, time_slot: "09:25-10:20" }   // Period 2
      );
      console.log(`🔧 Expanded 08:30-10:20 lab to: P1(08:30-09:25), P2(09:25-10:20)`);
    } else if (labSlot.time_slot === "10:35-12:25") {
      // 2-hour mid-morning lab: Periods 3, 4
      expandedSlots.push(
        { ...labSlot, time_slot: "10:35-11:30" },  // Period 3
        { ...labSlot, time_slot: "11:30-12:25" }   // Period 4
      );
      console.log(`🔧 Expanded 10:35-12:25 lab to: P3(10:35-11:30), P4(11:30-12:25)`);
    } else if (labSlot.time_slot === "13:15-15:05") {
      // 2-hour afternoon lab: Periods 5, 6
      expandedSlots.push(
        { ...labSlot, time_slot: "13:15-14:10" },  // Period 5
        { ...labSlot, time_slot: "14:10-15:05" }   // Period 6
      );
      console.log(`🔧 Expanded 13:15-15:05 lab to: P5(13:15-14:10), P6(14:10-15:05)`);
    } else if (labSlot.time_slot === "14:10-16:00") {
      // 2-hour late afternoon lab: Periods 6, 7
      expandedSlots.push(
        { ...labSlot, time_slot: "14:10-15:05" },  // Period 6
        { ...labSlot, time_slot: "15:05-16:00" }   // Period 7
      );
      console.log(`🔧 Expanded 14:10-16:00 lab to: P6(14:10-15:05), P7(15:05-16:00)`);
    } else if (standardPeriods.includes(labSlot.time_slot)) {
      // Already a single period - return as is
      expandedSlots.push(labSlot);
      console.log(`🔧 Single period lab: ${labSlot.time_slot} (no expansion needed)`);
    } else {
      // Unknown format - try to parse and expand dynamically
      console.warn(`Unknown lab time slot format: ${labSlot.time_slot}`);

      // Try to parse the time slot and map to periods dynamically
      const timeSlotMatch = labSlot.time_slot.match(/^(\d{2}:\d{2})-(\d{2}:\d{2})$/);
      if (timeSlotMatch) {
        const [, startTime, endTime] = timeSlotMatch;
        console.log(`🔍 Attempting to parse time slot: ${startTime} to ${endTime}`);

        // Find which standard periods this time slot spans
        const spanningPeriods = standardPeriods.filter(period => {
          const [periodStart, periodEnd] = period.split('-');
          return (startTime <= periodStart && endTime >= periodEnd) ||
                 (startTime >= periodStart && startTime < periodEnd) ||
                 (endTime > periodStart && endTime <= periodEnd);
        });

        if (spanningPeriods.length > 0) {
          console.log(`🔧 Mapped ${labSlot.time_slot} to periods:`, spanningPeriods);
          spanningPeriods.forEach(period => {
            expandedSlots.push({ ...labSlot, time_slot: period });
          });
        } else {
          // Fallback: keep original time slot
          expandedSlots.push(labSlot);
        }
      } else {
        // Fallback: keep original time slot
        expandedSlots.push(labSlot);
      }
    }

    return expandedSlots;
  }

  /**
   * Process timetable data into a grid format for export matching web interface exactly
   */
  static async processDataForExport(exportData: ExportData): Promise<ProcessedTimetableData> {
    console.log('🔄 FORCE RELOAD CHECK: TimetableExportService v2.0 loaded successfully');
    const { timetableData, timeStructure, metadata } = exportData;

    // DEBUG: Log all the raw data to understand the issue
    console.log('🔍 RAW TIMETABLE DATA FOR DEBUGGING:');
    timetableData.forEach((slot, index) => {
      console.log(`  ${index + 1}. ${slot.day} ${slot.time_slot}: ${slot.subject_code} | type: "${slot.subject_type}" | batch: "${slot.batch_name || 'N/A'}"`);
    });

    console.log('🔍 PROCESSING EXPORT DATA:', {
      timetableDataCount: timetableData.length,
      sampleData: timetableData.slice(0, 3),
      metadata,
      timeStructure: {
        periods_in_first_half: timeStructure.periods_in_first_half,
        periods_in_second_half: timeStructure.periods_in_second_half
      }
    });

    // Apply faculty filtering if in faculty view and data comes from timetable_slots table
    let filteredTimetableData = timetableData;
    if (metadata.view === 'faculty' && metadata.selectedFaculty) {
      console.log(`🎯 FACULTY VIEW: Processing data for faculty ID: ${metadata.selectedFaculty}`);
      console.log(`📊 Original data count: ${timetableData.length}`);

      // Check if data is already faculty-specific (from faculty_timetables table)
      // If all slots have the same faculty_id as selectedFaculty, it's already filtered
      const allSlotsMatchFaculty = timetableData.every(slot =>
        slot.faculty_id === metadata.selectedFaculty
      );

      if (allSlotsMatchFaculty && timetableData.length > 0) {
        console.log(`✅ Data is already faculty-specific (from faculty_timetables table), skipping filtering`);
        filteredTimetableData = timetableData;
      } else {
        console.log(`🔍 Data appears to be from timetable_slots table, applying faculty filtering`);

        // Apply the same filtering logic as TimetableGrid.tsx
        filteredTimetableData = timetableData.filter(slot => {
          const isPrimaryFaculty = slot.faculty_id === metadata.selectedFaculty;
          const isSecondaryFaculty = slot.faculty2_id === metadata.selectedFaculty;
          const isLabSubject = slot.subject_type === 'lab' || slot.subject_type === 'laboratory';
          const isSkillLab = slot.subject_code === "SKILL LAB";

          // Always exclude skill labs in faculty view
          if (isSkillLab) {
            return false;
          }

          // Handle tutorial slots - check faculty assignment for faculty view
          const isTutorial = slot.subject_code === 'TUTORIAL' || slot.subject_type === 'tutorial';
          if (isTutorial) {
            const isAssignedToFaculty = slot.faculty_id === metadata.selectedFaculty || slot.faculty2_id === metadata.selectedFaculty;
            return isAssignedToFaculty;
          }

          if (isPrimaryFaculty) {
            return true;
          } else if (isSecondaryFaculty && isLabSubject) {
            return true;
          } else {
            return false;
          }
        });

        console.log(`🎯 FACULTY FILTERING RESULT: ${filteredTimetableData.length} slots after filtering`);
      }

      console.log('📋 Final filtered slots:');
      filteredTimetableData.forEach((slot, index) => {
        console.log(`  ${index + 1}. ${slot.day} ${slot.time_slot}: ${slot.subject_code} (${slot.subject_type})`);
      });
    }

    // Debug: Log all timetable data to see the actual structure
    console.log('📊 PROCESSED TIMETABLE DATA:');
    filteredTimetableData.forEach((slot, index) => {
      console.log(`  ${index + 1}. ${slot.day} ${slot.time_slot}: ${slot.subject_code} (type: "${slot.subject_type}", batch: "${slot.batch_name || 'N/A'}")`);
    });

    // Debug: Check for lab slots specifically
    const labSlots = filteredTimetableData.filter(slot =>
      slot.subject_type === 'lab' ||
      slot.subject_type === 'laboratory' ||
      slot.subject_type === 'skill_lab' ||
      slot.subject_code?.toLowerCase().includes('lab')
    );
    console.log(`🧪 FOUND ${labSlots.length} LAB SLOTS:`, labSlots.map(s => `${s.subject_code} (${s.subject_type}) ${s.time_slot}`));

    // Expand multi-hour lab slots into individual period slots
    console.log('🔧 EXPANDING MULTI-HOUR LAB SLOTS...');
    const expandedTimetableData = [];

    filteredTimetableData.forEach(slot => {
      const isLabSlot = slot.subject_type === 'lab' ||
                        slot.subject_type === 'laboratory' ||
                        slot.subject_type === 'skill_lab' ||
                        slot.subject_code?.toLowerCase().includes('lab');

      if (isLabSlot) {
        // Check if this is a multi-hour lab slot
        const expandedSlots = this.expandLabSlot(slot);
        console.log(`  ${slot.subject_code} ${slot.time_slot} -> expanded to ${expandedSlots.length} periods:`, expandedSlots.map(s => s.time_slot));
        expandedTimetableData.push(...expandedSlots);
      } else {
        // Regular theory slot - add as is
        expandedTimetableData.push(slot);
      }
    });

    console.log(`📊 EXPANDED DATA: ${filteredTimetableData.length} -> ${expandedTimetableData.length} slots`);

    // Use expanded data for processing
    const processedTimetableData = expandedTimetableData;

    // Use fixed time slot to period mapping (matching web UI exactly)
    const standardTimeSlotMapping = [
      { start: "08:30", end: "09:25", period: 1 },
      { start: "09:25", end: "10:20", period: 2 },
      { start: "10:35", end: "11:30", period: 3 }, // After tea break
      { start: "11:30", end: "12:25", period: 4 },
      { start: "13:15", end: "14:10", period: 5 }, // After lunch break
      { start: "14:10", end: "15:05", period: 6 },
      { start: "15:05", end: "16:00", period: 7 },
    ];

    // Get unique time slots from expanded timetable data
    const actualTimeSlots = [...new Set(processedTimetableData.map(slot => slot.time_slot))].sort();
    console.log('Actual time slots from data:', actualTimeSlots);

    // Map actual time slots to periods using the standard mapping
    const timeSlotToPeriodMap = new Map<string, number>();
    actualTimeSlots.forEach(timeSlot => {
      // Try to match with standard mapping
      const mapping = standardTimeSlotMapping.find(std => {
        const stdTimeSlot = `${std.start}-${std.end}`;
        return stdTimeSlot === timeSlot;
      });

      if (mapping) {
        timeSlotToPeriodMap.set(timeSlot, mapping.period);
        console.log(`Mapped ${timeSlot} to Period ${mapping.period}`);
      } else {
        console.warn(`Could not map time slot ${timeSlot} to a standard period`);
      }
    });

    // Create time slots structure with proper period numbers and break positioning
    // Always show all periods 1-7 regardless of whether data exists
    const timeSlots: Array<{time: string; label: string; isPeriod: boolean; isBreak: boolean; periodNumber?: number}> = [];

    // Add periods 1-2
    for (let period = 1; period <= 2; period++) {
      const timeSlot = actualTimeSlots.find(ts => timeSlotToPeriodMap.get(ts) === period);
      timeSlots.push({
        time: timeSlot || `period_${period}`, // Use placeholder if no actual time slot
        label: `PERIOD ${period}`,
        isPeriod: true,
        isBreak: false,
        periodNumber: period
      });
    }

    // Add TEA BREAK
    timeSlots.push({
      time: 'tea_break',
      label: 'TEA BREAK',
      isPeriod: false,
      isBreak: true
    });

    // Add periods 3-4
    for (let period = 3; period <= 4; period++) {
      const timeSlot = actualTimeSlots.find(ts => timeSlotToPeriodMap.get(ts) === period);
      timeSlots.push({
        time: timeSlot || `period_${period}`, // Use placeholder if no actual time slot
        label: `PERIOD ${period}`,
        isPeriod: true,
        isBreak: false,
        periodNumber: period
      });
    }

    // Add LUNCH BREAK
    timeSlots.push({
      time: 'lunch_break',
      label: 'LUNCH BREAK',
      isPeriod: false,
      isBreak: true
    });

    // Add periods 5-7
    for (let period = 5; period <= 7; period++) {
      const timeSlot = actualTimeSlots.find(ts => timeSlotToPeriodMap.get(ts) === period);
      timeSlots.push({
        time: timeSlot || `period_${period}`, // Use placeholder if no actual time slot
        label: `PERIOD ${period}`,
        isPeriod: true,
        isBreak: false,
        periodNumber: period
      });
    }

    const days = timeStructure.working_days;

    console.log('Final time slots for export:', timeSlots);
    console.log('Working days:', days);

    // Initialize grid and lab spans
    const timetableGrid: Record<string, Record<string, TimetableSlot[]>> = {};
    const labSpans: Record<string, Record<string, { colSpan: number; isStart: boolean; labId: string }>> = {};

    // Get all time slots (actual + placeholders) for grid initialization
    const allTimeSlots = timeSlots.filter(ts => ts.isPeriod).map(ts => ts.time);

    // Also include all actual time slots from the data to prevent undefined array errors
    const allActualTimeSlots = [...new Set([...allTimeSlots, ...actualTimeSlots])];
    console.log('All time slots for grid initialization:', allActualTimeSlots);

    days.forEach(day => {
      timetableGrid[day] = {};
      labSpans[day] = {};
      allActualTimeSlots.forEach(timeSlot => {
        timetableGrid[day][timeSlot] = [];
        labSpans[day][timeSlot] = { colSpan: 1, isStart: true, labId: '' };
      });
    });

    // Process lab slots with proper merging logic matching web UI
    const processedLabSlots = new Set<string>();

    for (const slot of processedTimetableData) {
      // CRITICAL FIX: Include batch and section in slot key to ensure different batches are treated separately
      const slotKey = `${slot.day}-${slot.time_slot}-${slot.subject_code}-${slot.section || 'no-section'}-${slot.batch_name || 'no-batch'}`;

      const isLabSlot = slot.subject_type === 'lab' ||
                        slot.subject_type === 'laboratory' ||
                        slot.subject_type === 'skill_lab' ||
                        slot.subject_code?.toLowerCase().includes('lab');

      // Special debug for Thursday to see what's being skipped
      if (slot.day === 'Thursday' && isLabSlot) {
        console.log(`🔍 THURSDAY SLOT CHECK: ${slot.time_slot} | slotKey: ${slotKey} | alreadyProcessed: ${processedLabSlots.has(slotKey)}`);
      }

      if (isLabSlot && !processedLabSlots.has(slotKey)) {
        console.log(`🧪 Processing lab slot: ${slot.subject_code} ${slot.section} (${slot.batch_name}) on ${slot.day} at ${slot.time_slot}`);

        // Special debug for Thursday
        if (slot.day === 'Thursday') {
          console.log(`🔍 THURSDAY DEBUG: Processing ${slot.subject_code} at ${slot.time_slot}`);
        }

        try {
          // Find all lab slots for this subject on this day

          // Special debug for Thursday - check what we're filtering
          if (slot.day === 'Thursday') {
            console.log(`🔍 THURSDAY DEBUG: About to filter ${processedTimetableData.length} total slots for ${slot.subject_code} on ${slot.day}`);
          }

        const relatedLabSlots = processedTimetableData.filter(s => {
          const sIsLabSlot = s.subject_type === 'lab' ||
                            s.subject_type === 'laboratory' ||
                            s.subject_type === 'skill_lab' ||
                            s.subject_code?.toLowerCase().includes('lab');

          // CRITICAL FIX: Ensure exact batch matching - different batches should be separate labs
          const batchMatch = s.batch_name === slot.batch_name;
          const subjectMatch = s.subject_code === slot.subject_code;
          const dayMatch = s.day === slot.day;

          // Also match section to ensure we're grouping the right labs together
          const sectionMatch = s.section === slot.section;

          // Debug logging for Monday to see what's happening
          if (slot.day === 'Monday') {
            console.log(`   🔍 Checking slot: ${s.day} ${s.time_slot} ${s.subject_code} ${s.section} (${s.batch_name}) | dayMatch: ${dayMatch}, subjectMatch: ${subjectMatch}, sectionMatch: ${sectionMatch}, isLab: ${sIsLabSlot}, batchMatch: ${batchMatch}`);
          }

          return dayMatch && subjectMatch && sectionMatch && sIsLabSlot && batchMatch;
        }).sort((a, b) => a.time_slot.localeCompare(b.time_slot));

        console.log(`   Found ${relatedLabSlots.length} related slots:`, relatedLabSlots.map(s => `${s.time_slot} (P${timeSlotToPeriodMap.get(s.time_slot) || '?'})`));

        // Special debug for Thursday - check if we reach this point
        if (slot.day === 'Thursday') {
          console.log(`🔍 THURSDAY DEBUG: About to check if ${relatedLabSlots.length} > 1`);
        }

        // Special debug for Thursday
        if (slot.day === 'Thursday') {
          console.log(`🔍 THURSDAY DEBUG: Found ${relatedLabSlots.length} related slots for ${slot.subject_code}`);
          console.log(`🔍 THURSDAY DEBUG: All processedTimetableData for Thursday:`,
            processedTimetableData.filter(s => s.day === 'Thursday').map(s =>
              `${s.time_slot} | ${s.subject_code} | batch: ${s.batch_name} | type: ${s.subject_type}`
            )
          );
          relatedLabSlots.forEach((s, i) => {
            console.log(`   ${i + 1}. ${s.time_slot} | subject: ${s.subject_code} | batch: ${s.batch_name} | type: ${s.subject_type}`);
          });

          if (relatedLabSlots.length <= 1) {
            console.log(`🔍 THURSDAY DEBUG: Only ${relatedLabSlots.length} related slots found - will be treated as single period lab`);
          }
        }

        if (relatedLabSlots.length > 1) {
          // Multi-period lab - check if consecutive or can be grouped into consecutive blocks
          const timeSlotIndices = relatedLabSlots.map(s => actualTimeSlots.indexOf(s.time_slot));
          console.log(`   🔍 Time slot indices:`, timeSlotIndices);
          console.log(`   🔍 Actual time slots:`, actualTimeSlots);

          // Check if all slots are consecutive
          const isFullyConsecutive = timeSlotIndices.every((index, i) =>
            i === 0 || index === timeSlotIndices[i - 1] + 1
          );

          // If not fully consecutive, check for consecutive groups
          const consecutiveGroups = [];
          let currentGroup = [timeSlotIndices[0]];

          for (let i = 1; i < timeSlotIndices.length; i++) {
            if (timeSlotIndices[i] === timeSlotIndices[i - 1] + 1) {
              // Consecutive - add to current group
              currentGroup.push(timeSlotIndices[i]);
            } else {
              // Gap found - start new group
              if (currentGroup.length > 1) {
                consecutiveGroups.push(currentGroup);
              }
              currentGroup = [timeSlotIndices[i]];
            }
          }

          // Add the last group if it has multiple slots
          if (currentGroup.length > 1) {
            consecutiveGroups.push(currentGroup);
          }

          console.log(`   🔍 Is fully consecutive: ${isFullyConsecutive}`);
          console.log(`   🔍 Consecutive groups found:`, consecutiveGroups);

          // Special debug for Thursday
          if (slot.day === 'Thursday') {
            console.log(`🔍 THURSDAY DEBUG: Is fully consecutive: ${isFullyConsecutive}`);
            console.log(`🔍 THURSDAY DEBUG: Found ${consecutiveGroups.length} consecutive groups:`, consecutiveGroups);
          }

          if (isFullyConsecutive || consecutiveGroups.length > 0) {
            // Handle fully consecutive labs (original logic)
            if (isFullyConsecutive) {
              // Determine lab type and spanning logic
              const firstSlot = relatedLabSlots[0];
              const firstTimeSlotIndex = actualTimeSlots.indexOf(firstSlot.time_slot);
              const firstPeriodNumber = timeSlotToPeriodMap.get(firstSlot.time_slot) || 1;

              console.log(`🧪 Lab Analysis: ${firstSlot.subject_code} starts at period ${firstPeriodNumber}, spans ${relatedLabSlots.length} periods`);

              // Check if this is a 3-hour morning lab (starts in period 1 or 2, spans 3 periods)
              const is3HourMorningLab = relatedLabSlots.length === 3 && firstPeriodNumber <= 2;

            if (is3HourMorningLab) {
              // 3-hour morning lab: Split into two parts to avoid spanning across tea break
              // Part 1: Periods 1+2 merged (before tea break)
              // Part 2: Period 3 separate (after tea break, marked as continuation)
              const labKey = `${slot.day}-${slot.subject_code}-${slot.batch_name || 'default'}`;
              console.log(`🔧 Processing 3-hour morning lab: ${labKey} - split into P1+P2 merged, P3 separate`);

              // Part 1: Merge Periods 1+2 (before tea break)
              labSpans[slot.day][firstSlot.time_slot] = {
                colSpan: 2, // Only P1 + P2
                isStart: true,
                labId: labKey
              };
              console.log(`   ✅ Set span for ${firstSlot.time_slot}: colSpan=2 (spans P1+P2), isStart=true`);

              // Mark Period 2 as continuation
              if (relatedLabSlots.length > 1) {
                labSpans[slot.day][relatedLabSlots[1].time_slot] = {
                  colSpan: 0,
                  isStart: false,
                  labId: labKey
                };
                console.log(`   ✅ Set continuation for ${relatedLabSlots[1].time_slot}: colSpan=0, isStart=false`);
              }

              // Add first slot to grid (will span P1+P2)
              timetableGrid[slot.day][firstSlot.time_slot].push(firstSlot);
              console.log(`   ✅ Added to grid: ${slot.day} ${firstSlot.time_slot} -> ${firstSlot.subject_code} (${firstSlot.batch_name}) - spans P1+P2`);

              // Part 2: Add Period 3 separately (after tea break, marked as continuation)
              if (relatedLabSlots.length > 2) {
                const thirdSlot = relatedLabSlots[2];
                const thirdSlotPeriod = timeSlotToPeriodMap.get(thirdSlot.time_slot);
                console.log(`🔍 Period 3 Debug: thirdSlot=${thirdSlot.time_slot}, mapped_period=${thirdSlotPeriod}, expected_period=3`);
                console.log(`🔍 All related slots:`, relatedLabSlots.map((s, i) => `[${i}] ${s.time_slot} -> P${timeSlotToPeriodMap.get(s.time_slot)}`));

                // Get short ID for the lab
                const labShortId = await this.convertToShortIdAsync(thirdSlot.subject_code, thirdSlot.subject_type, thirdSlot.batch_name);
                timetableGrid[slot.day][thirdSlot.time_slot].push({
                  ...thirdSlot,
                  isContinuedLab: true,
                  showContinuedBadge: true,
                  displayText: `${labShortId} (Cont.)\n(${thirdSlot.batch_name || 'A1'})`
                });
                console.log(`   ✅ Added Period 3: ${slot.day} ${thirdSlot.time_slot} -> ${thirdSlot.subject_code} (continuation)`);
              }
            } else {
              // Regular consecutive lab - merge all periods
              const labKey = `${slot.day}-${slot.subject_code}-${slot.batch_name || 'default'}`;
              console.log(`🔧 Processing regular consecutive lab: ${labKey} (${relatedLabSlots.length} periods)`);

              // Mark first slot as start with full span
              labSpans[slot.day][firstSlot.time_slot] = {
                colSpan: relatedLabSlots.length,
                isStart: true,
                labId: labKey
              };
              console.log(`   ✅ Set span for ${firstSlot.time_slot}: colSpan=${relatedLabSlots.length}, isStart=true`);

              // Mark subsequent slots as continuation
              for (let i = 1; i < relatedLabSlots.length; i++) {
                labSpans[slot.day][relatedLabSlots[i].time_slot] = {
                  colSpan: 0,
                  isStart: false,
                  labId: labKey
                };
                console.log(`   ✅ Set continuation for ${relatedLabSlots[i].time_slot}: colSpan=0, isStart=false`);
              }

              // Add first slot to grid (will span multiple columns)
              timetableGrid[slot.day][firstSlot.time_slot].push(firstSlot);
              console.log(`   ✅ Added to grid: ${slot.day} ${firstSlot.time_slot} -> ${firstSlot.subject_code} (${firstSlot.batch_name})`);
            }

            } else {
              // Handle multiple consecutive groups (e.g., Thursday P1-P3 and P5-P7)
              console.log(`🔧 Processing multiple consecutive groups: ${consecutiveGroups.length} groups found`);

              for (let groupIndex = 0; groupIndex < consecutiveGroups.length; groupIndex++) {
                const group = consecutiveGroups[groupIndex];
                const groupSlots = group.map(index => relatedLabSlots.find(s => actualTimeSlots.indexOf(s.time_slot) === index)).filter(Boolean);

                if (groupSlots.length > 1) {
                  const groupFirstSlot = groupSlots[0];
                  const groupFirstPeriod = timeSlotToPeriodMap.get(groupFirstSlot.time_slot) || 1;
                  const labKey = `${slot.day}-${slot.subject_code}-${slot.batch_name || 'default'}-group${groupIndex}`;

                  console.log(`   🔧 Processing group ${groupIndex + 1}: P${groupFirstPeriod}-P${groupFirstPeriod + groupSlots.length - 1} (${groupSlots.length} periods)`);

                  // Check if this is a 3-hour morning lab group
                  const isGroupMorningLab = groupSlots.length === 3 && groupFirstPeriod <= 2;

                  if (isGroupMorningLab) {
                    // Handle as 3-hour morning lab (P1+P2 merged, P3 separate)
                    labSpans[slot.day][groupFirstSlot.time_slot] = {
                      colSpan: 2,
                      isStart: true,
                      labId: labKey
                    };

                    if (groupSlots.length > 1) {
                      labSpans[slot.day][groupSlots[1].time_slot] = {
                        colSpan: 0,
                        isStart: false,
                        labId: labKey
                      };
                    }

                    timetableGrid[slot.day][groupFirstSlot.time_slot].push(groupFirstSlot);

                    // Add Period 3 separately
                    if (groupSlots.length > 2) {
                      const thirdSlot = groupSlots[2];
                      const labShortId = await this.convertToShortIdAsync(thirdSlot.subject_code, thirdSlot.subject_type, thirdSlot.batch_name);
                      timetableGrid[slot.day][thirdSlot.time_slot].push({
                        ...thirdSlot,
                        isContinuedLab: true,
                        showContinuedBadge: true,
                        displayText: `${labShortId} (Cont.)\n(${thirdSlot.batch_name || 'A1'})`
                      });
                    }

                    console.log(`   ✅ Added 3-hour morning lab group: ${labKey}`);
                  } else {
                    // Handle as regular consecutive lab
                    labSpans[slot.day][groupFirstSlot.time_slot] = {
                      colSpan: groupSlots.length,
                      isStart: true,
                      labId: labKey
                    };

                    for (let i = 1; i < groupSlots.length; i++) {
                      labSpans[slot.day][groupSlots[i].time_slot] = {
                        colSpan: 0,
                        isStart: false,
                        labId: labKey
                      };
                    }

                    timetableGrid[slot.day][groupFirstSlot.time_slot].push(groupFirstSlot);
                    console.log(`   ✅ Added regular consecutive lab group: ${labKey} (${groupSlots.length} periods)`);
                  }
                }
              }
            }

            // Mark all related slots as processed
            for (const s of relatedLabSlots) {
              processedLabSlots.add(`${s.day}-${s.time_slot}-${s.subject_code}-${s.section || 'no-section'}-${s.batch_name || 'no-batch'}`);
            }
          } else {
            // Non-consecutive lab slots - treat as separate
            for (const s of relatedLabSlots) {
              timetableGrid[s.day][s.time_slot].push(s);
              processedLabSlots.add(`${s.day}-${s.time_slot}-${s.subject_code}-${s.section || 'no-section'}-${s.batch_name || 'no-batch'}`);
            }
          }
        } else {
          // Single period lab
          timetableGrid[slot.day][slot.time_slot].push(slot);
          processedLabSlots.add(slotKey);
        }

        } catch (error) {
          console.error(`❌ ERROR processing lab slot ${slot.subject_code} on ${slot.day} at ${slot.time_slot}:`, error);

          // Special debug for Thursday
          if (slot.day === 'Thursday') {
            console.error(`🔍 THURSDAY ERROR: Failed to process ${slot.subject_code} at ${slot.time_slot}`, error);
          }

          // Fallback: treat as single period lab
          timetableGrid[slot.day][slot.time_slot].push(slot);
          processedLabSlots.add(slotKey);
        }
      }
    }

    // Add non-lab slots normally
    for (const slot of processedTimetableData) {
      const isLabSlot = slot.subject_type === 'lab' ||
                        slot.subject_type === 'laboratory' ||
                        slot.subject_type === 'skill_lab' ||
                        slot.subject_code?.toLowerCase().includes('lab');

      if (!isLabSlot) {
        if (timetableGrid[slot.day] && timetableGrid[slot.day][slot.time_slot] !== undefined) {
          timetableGrid[slot.day][slot.time_slot].push(slot);
          console.log(`📚 Successfully placed theory slot: ${slot.subject_code} in ${slot.day} ${slot.time_slot}`);
        } else {
          console.warn('Could not place non-lab slot:', {
            day: slot.day,
            time_slot: slot.time_slot,
            subject: slot.subject_code
          });
        }
      }
    }

    console.log('Final timetable grid with lab spans:', timetableGrid);
    console.log('Lab spans:', labSpans);

    return {
      days,
      timeSlots,
      timetableGrid,
      labSpans
    };
  }

  /**
   * Generate time slots from time structure
   */
  private static generateTimeSlots(timeStructure: TimeStructure) {
    const slots = [];

    // First half periods
    const firstHalfStart = new Date(`1970-01-01T${timeStructure.first_half_start_time}`);
    const firstHalfEnd = new Date(`1970-01-01T${timeStructure.first_half_end_time}`);
    const theoryDuration = timeStructure.theory_class_duration;

    let currentTime = new Date(firstHalfStart);
    for (let i = 0; i < timeStructure.periods_in_first_half; i++) {
      const endTime = new Date(currentTime.getTime() + theoryDuration * 60000);
      slots.push({
        time: this.formatTime(currentTime),
        label: `Period ${i + 1}`,
        isPeriod: true,
        isBreak: false
      });
      currentTime = new Date(endTime);
    }

    // Tea break
    slots.push({
      time: 'tea_break',
      label: 'Tea Break',
      isPeriod: false,
      isBreak: true
    });

    // Second half periods
    const secondHalfStart = new Date(`1970-01-01T${timeStructure.second_half_start_time}`);
    currentTime = new Date(secondHalfStart);

    for (let i = 0; i < timeStructure.periods_in_second_half; i++) {
      const endTime = new Date(currentTime.getTime() + theoryDuration * 60000);
      slots.push({
        time: this.formatTime(currentTime),
        label: `Period ${timeStructure.periods_in_first_half + i + 1}`,
        isPeriod: true,
        isBreak: false
      });
      currentTime = new Date(endTime);

      // Add lunch break after the appropriate period
      if (i === Math.floor(timeStructure.periods_in_second_half / 2) - 1) {
        slots.push({
          time: 'lunch_break',
          label: 'Lunch Break',
          isPeriod: false,
          isBreak: true
        });
      }
    }

    return slots;
  }

  /**
   * Format time for display
   */
  private static formatTime(date: Date): string {
    return date.toTimeString().slice(0, 5);
  }



  /**
   * Cache for subject mappings to avoid repeated database calls
   */
  private static subjectMappingsCache: Record<string, string> = {};
  private static mappingsCacheLoaded = false;

  /**
   * Load subject mappings from database
   */
  private static async loadSubjectMappingsFromDB(): Promise<void> {
    if (this.mappingsCacheLoaded) return;

    try {
      console.log('🔄 Loading subject mappings from database...');

      const { supabase } = await import('@/integrations/supabase/client');
      const { data: subjects, error } = await supabase
        .from('subjects')
        .select('subject_code, subject_short_id')
        .not('subject_short_id', 'is', null);

      if (error) {
        console.error('❌ Error loading subject mappings:', error);
        return;
      }

      // Build the mappings cache
      subjects?.forEach(subject => {
        // Only add mapping if subject_short_id is different from subject_code (valid short ID)
        if (subject.subject_short_id && subject.subject_short_id !== subject.subject_code) {
          this.subjectMappingsCache[subject.subject_code] = subject.subject_short_id;
        }
      });

      console.log(`✅ Loaded ${Object.keys(this.subjectMappingsCache).length} subject mappings from database:`, this.subjectMappingsCache);
      this.mappingsCacheLoaded = true;

    } catch (error) {
      console.error('❌ Failed to load subject mappings:', error);
    }
  }

  /**
   * Convert subject code to short ID during export processing
   * This fetches mappings from the database dynamically
   */
  private static async convertToShortIdAsync(subjectCode: string, subjectType?: string, batchName?: string): Promise<string> {
    // Load mappings from database if not already loaded
    await this.loadSubjectMappingsFromDB();

    // IMPORTANT: Same subject code (e.g., BCS403) can be used for both theory and lab
    // We must check subject_type first before using any mappings

    const isTheorySubject = subjectType === 'theory';
    const isLabSubject = subjectType === 'lab' || subjectType === 'laboratory' || subjectType === 'skill_lab';

    console.log(`🔍 SUBJECT TYPE CHECK: ${subjectCode} | type: "${subjectType}" | isTheory: ${isTheorySubject} | isLab: ${isLabSubject}`);

    // For theory subjects, use theory mappings regardless of database
    if (isTheorySubject) {
      const theoryMappings: Record<string, string> = {
        'BCS403': 'DBMS',      // DBMS Theory
        'BCS402': 'MC',        // MC Theory
        'BCS401': 'ADA',       // ADA Theory
        'BCS404': 'CN',        // CN Theory
        'BCS405A': 'DMS',      // DMS Theory
      };

      const theoryMapping = theoryMappings[subjectCode];
      if (theoryMapping) {
        console.log(`📝 CONVERT TO SHORT ID (THEORY): "${subjectCode}" -> "${theoryMapping}" (THEORY OVERRIDE)`);
        return theoryMapping;
      }
    }

    // For lab subjects, use lab mappings
    if (isLabSubject) {
      const labMappings: Record<string, string> = {
        'BCS403': 'DBMS Lab',    // DBMS Lab
        'BCS402': 'MC Lab',      // MC Lab
        'BCS401': 'ADA Lab',     // ADA Lab
        'BCS404': 'CN Lab',      // CN Lab
        'BCSL403': 'DBMS Lab',   // DBMS Lab (alternative code)
        'BCSL402': 'MC Lab',     // MC Lab (alternative code)
        'BCSL401': 'ADA Lab',    // ADA Lab (alternative code)
        'BCSL404': 'ADA Lab',    // ADA Lab (alternative code)
      };

      const labMapping = labMappings[subjectCode];
      if (labMapping) {
        console.log(`📝 CONVERT TO SHORT ID (LAB): "${subjectCode}" -> "${labMapping}" (LAB MAPPING)`);
        return labMapping;
      }
    }

    // Fallback to database mappings only if no specific type mapping found
    const dbMapping = this.subjectMappingsCache[subjectCode];
    if (dbMapping) {
      console.log(`📝 CONVERT TO SHORT ID (DB FALLBACK): "${subjectCode}" -> "${dbMapping}" (DATABASE)`);
      return dbMapping;
    }



    // Fallback to hardcoded mappings for special cases
    const fallbackMappings: Record<string, string> = {
      'BCS405A': 'DMS',  // Known working mapping
      'SKILL LAB': 'SKILL Lab',
      'TUTORIAL': 'TUTORIAL',
      'SEMINAR': 'SEMINAR',
      'PROJECT': 'PROJECT'
    };

    const fallbackMapping = fallbackMappings[subjectCode];
    if (fallbackMapping) {
      console.log(`📝 CONVERT TO SHORT ID (FALLBACK): "${subjectCode}" -> "${fallbackMapping}" (CONVERTED)`);
      return fallbackMapping;
    }

    // No mapping found
    console.log(`📝 CONVERT TO SHORT ID: "${subjectCode}" -> "${subjectCode}" (NO CHANGE)`);
    return subjectCode;
  }



  /**
   * TEMPORARY: Fallback function to prevent errors (should not be used)
   */
  private static async getLabDisplayName(subjectCode: string): Promise<string> {
    console.warn('⚠️ getLabDisplayName called - this should not happen! Using convertToShortIdAsync instead.');
    return await this.convertToShortIdAsync(subjectCode, 'unknown', undefined);
  }

  /**
   * Format slot content for PDF export to match simplified web interface display
   */
  private static async formatSlotForPDF(slot: TimetableSlot, view: 'class' | 'faculty', selectedFaculty?: string): Promise<string> {
    // Convert subject code to short ID during processing
    const originalCode = slot.subject_code;
    const shortId = slot.subject_short_id;
    const convertedCode = await this.convertToShortIdAsync(slot.subject_code, slot.subject_type, slot.batch_name);

    // Use subject_short_id only if it's different from subject_code (i.e., it's actually a short ID)
    // If subject_short_id is the same as subject_code, it means the database has wrong data
    const isValidShortId = shortId && shortId !== originalCode;
    const subjectCode = isValidShortId ? shortId : convertedCode;

    console.log(`🔍 PDF FORMAT DEBUG:`, {
      originalCode,
      shortId,
      convertedCode,
      isValidShortId,
      finalSubjectCode: subjectCode,
      view,
      slotType: slot.subject_type,
      isTargetSubject: originalCode === 'BCS405A' ? '🎯 TARGET SUBJECT!' : '',
      decision: isValidShortId ? 'USED DATABASE SHORT_ID' : 'USED CONVERSION'
    });

    // For class view - show minimal content like web interface
    if (view === 'class') {
      const isLabSlot = slot.subject_type === 'lab' ||
                        slot.subject_type === 'laboratory' ||
                        slot.subject_type === 'skill_lab';

      // Theory slots: just subject code
      if (!isLabSlot) {
        return subjectCode;
      }

      // Check if this is a continued lab (Period 3 of 3-hour morning lab)
      if ((slot as any).isContinuedLab && (slot as any).displayText) {
        return (slot as any).displayText;
      }

      // Lab slots: use subject short ID + batch if available
      if (slot.batch_name) {
        return `${subjectCode}\n(${slot.batch_name})`;
      }

      return subjectCode;
    }

    // For faculty view - show subject code with class info
    if (view === 'faculty') {
      let content = subjectCode;

      // Check if this is a lab slot
      const isLabSlot = slot.subject_type === 'lab' ||
                        slot.subject_type === 'laboratory' ||
                        slot.subject_type === 'skill_lab';

      if (isLabSlot) {
        // For lab slots in faculty view: "PW-1 6A (A2)" format
        // Add section info directly after subject code (no newline)
        if (slot.semester && slot.section) {
          content += ` ${slot.semester}${slot.section}`;
        }

        // Add batch info in parentheses
        if (slot.batch_name) {
          content += ` (${slot.batch_name})`;
        }
      } else {
        // For theory slots in faculty view: show on separate lines
        if (slot.semester && slot.section) {
          content += `\n${slot.semester}${slot.section}`;
        }
      }

      console.log(`📄 PDF CONTENT FINAL: "${content}" (for ${originalCode})`);
      return content;
    }

    return subjectCode;
  }

  /**
   * Format slot content for Excel export to match simplified web interface display
   */
  private static async formatSlotForExcel(slot: TimetableSlot, view: 'class' | 'faculty', selectedFaculty?: string): Promise<string> {
    // Use subject_short_id only if it's different from subject_code (i.e., it's actually a short ID)
    const isValidShortId = slot.subject_short_id && slot.subject_short_id !== slot.subject_code;
    const subjectCode = isValidShortId ? slot.subject_short_id : await this.convertToShortIdAsync(slot.subject_code, slot.subject_type, slot.batch_name);

    // For class view - show minimal content like web interface
    if (view === 'class') {
      // Theory slots: just subject code
      if (slot.subject_type !== 'lab') {
        return subjectCode;
      }

      // Lab slots: subject code + batch if available
      if (slot.batch_name) {
        return `${subjectCode} (${slot.batch_name})`;
      }

      return subjectCode;
    }

    // For faculty view - show subject code with class info
    if (view === 'faculty') {
      let content = subjectCode;

      // Check if this is a lab slot
      const isLabSlot = slot.subject_type === 'lab' ||
                        slot.subject_type === 'laboratory' ||
                        slot.subject_type === 'skill_lab';

      if (isLabSlot) {
        // For lab slots in faculty view: "PW-1 6A (A2)" format
        // Add section info directly after subject code
        if (slot.semester && slot.section) {
          content += ` ${slot.semester}${slot.section}`;
        }

        // Add batch info in parentheses
        if (slot.batch_name) {
          content += ` (${slot.batch_name})`;
        }
      } else {
        // For theory slots in faculty view: show with separators
        if (slot.semester && slot.section) {
          content += ` | ${slot.semester}${slot.section}`;
        }
      }

      return content;
    }

    return subjectCode;
  }

  /**
   * Get display content for a slot matching TimetableCellContent logic
   */
  private static async getSlotDisplayContent(slot: TimetableSlot, view: 'class' | 'faculty') {
    // Check if this is a continued lab card
    if (slot.isContinuedLab || slot.showContinuedBadge) {
      const semesterSection = view === "faculty" && slot.semester && slot.section ? `${slot.semester}${slot.section}` : '';
      return {
        mainLine: (slot.subject_short_id && slot.subject_short_id !== slot.subject_code) ? slot.subject_short_id : await this.convertToShortIdAsync(slot.subject_code, slot.subject_type, slot.batch_name),
        subtitle: view === "faculty" ? semesterSection : (slot.batch_name ? `(${slot.batch_name})` : ''),
        isCompact: true,
        showContinuedBadge: true,
        excludeFaculty: true
      };
    }

    // Check if custom displayText is provided
    if (slot.displayText) {
      const semesterSection = view === "faculty" && slot.semester && slot.section ? `${slot.semester}${slot.section}` : '';
      return {
        mainLine: slot.displayText,
        subtitle: view === "faculty" ? semesterSection : (slot.batch_name ? `(${slot.batch_name})` : ''),
        isCompact: true
      };
    }

    const subjectShortId = (slot.subject_short_id && slot.subject_short_id !== slot.subject_code) ? slot.subject_short_id : await this.convertToShortIdAsync(slot.subject_code, slot.subject_type, slot.batch_name);
    const isLab = slot.subject_type === 'lab';

    if (isLab) {
      // Lab slots
      if (view === "faculty") {
        const semesterSection = slot.semester && slot.section ? `${slot.semester}${slot.section}` : '';
        const batchInfo = slot.batch_name ? ` (${slot.batch_name})` : '';
        return {
          mainLine: subjectShortId,
          subtitle: semesterSection + batchInfo,
          isCompact: true,
          showFacultyRole: true
        };
      } else {
        return {
          mainLine: subjectShortId,
          subtitle: slot.batch_name ? `(${slot.batch_name})` : '',
          isCompact: true
        };
      }
    } else {
      // Theory slots
      if (view === "faculty") {
        const semesterSection = slot.semester && slot.section ? `${slot.semester}${slot.section}` : '';
        const subjectType = slot.subject_type ? ` (${slot.subject_type.toUpperCase()})` : '';
        return {
          mainLine: subjectShortId,
          subtitle: semesterSection + subjectType,
          isCompact: true,
          showFacultyRole: true
        };
      } else {
        return {
          mainLine: subjectShortId,
          subtitle: '',
          isCompact: true
        };
      }
    }
  }

  /**
   * Export timetable as PDF - Exact visual replica of webapp
   */
  static async exportToPDF(exportData: ExportData): Promise<void> {
    const { metadata } = exportData;

    console.log('🚀 PDF EXPORT STARTED for:', metadata.facultyName);

    const processedData = await this.processDataForExport(exportData);

    const doc = new jsPDF('l', 'mm', 'a4'); // Landscape orientation
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Title styling to match webapp
    const title = metadata.view === 'faculty'
      ? `Faculty Timetable - ${metadata.facultyName}`
      : `Class Timetable - ${metadata.department} Sem ${metadata.semester} Sec ${metadata.section}`;

    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text(title, pageWidth / 2, 20, { align: 'center' });

    // Academic year
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(`Academic Year: ${metadata.academicYear}`, pageWidth / 2, 30, { align: 'center' });

    // Prepare headers including breaks with proper period numbers - show all periods 1-7 plus breaks
    const headers = ['Day'];
    processedData.timeSlots.forEach(slot => {
      headers.push(slot.label);
    });

    console.log('🏗️ PDF Table Headers:', headers);
    console.log('🏗️ Headers length:', headers.length);

    console.log('🏗️ Starting table data preparation...');

    // Prepare table data with exact webapp formatting and lab spanning
    const tableData: any[][] = [];

    try {
      for (const day of processedData.days) {
        const row = [day];
        let skipNextCells = 0; // Track how many cells to skip due to colSpan

        for (const [timeSlotIndex, timeSlot] of processedData.timeSlots.entries()) {
        // If we're in a skipped cell due to previous colSpan, skip this iteration
        if (skipNextCells > 0) {
          skipNextCells--;
          continue;
        }

        if (timeSlot.isBreak) {
          // Break columns
          row.push('');
        } else {
          // Check if this is a lab span continuation (should be empty)
          const spanInfo = processedData.labSpans?.[day]?.[timeSlot.time];
          // Reduced verbosity - only log important span info
          if (spanInfo && spanInfo.colSpan > 1) {
            console.log(`🔗 Lab Span: ${day} ${timeSlot.time} -> colSpan=${spanInfo.colSpan}, isStart=${spanInfo.isStart}`);
          }

          if (spanInfo && !spanInfo.isStart && spanInfo.colSpan === 0) {
            // This is a continuation of a lab span, add empty cell
            row.push('');
            continue;
          }

          // Regular period columns
          const slots = processedData.timetableGrid[day][timeSlot.time] || [];

          let cellContent = '';
          if (slots.length > 0) {
            const formattedSlots = await Promise.all(slots.map(async slot => {
              const formatted = await this.formatSlotForPDF(slot, metadata.view, metadata.selectedFaculty);
              console.log(`📄 PDF Cell Content: ${day} ${timeSlot.time} -> "${formatted}" (${slot.subject_type})`);
              return formatted;
            }));
            cellContent = formattedSlots.join('\n');
          } else {
            // Reduced verbosity for empty cells
          }

          // Create cell object with potential column span
          const cellData = {
            content: cellContent,
            colSpan: spanInfo?.colSpan || 1,
            styles: {}
          };

          // Add special styling for lab cells
          if (spanInfo && spanInfo.colSpan > 1) {
            cellData.styles = {
              fillColor: [240, 248, 255], // Light blue background for labs
              fontStyle: 'bold'
            };
            console.log(`🎨 Lab Cell Styling: ${day} ${timeSlot.time} -> colSpan=${cellData.colSpan}, content="${cellContent}"`);

            // Calculate how many subsequent period cells to skip (excluding breaks)
            let periodsToSkip = spanInfo.colSpan - 1;
            let cellsToSkip = 0;

            for (let i = timeSlotIndex + 1; i < processedData.timeSlots.length && periodsToSkip > 0; i++) {
              cellsToSkip++;
              if (!processedData.timeSlots[i].isBreak) {
                periodsToSkip--;
              }
            }

            skipNextCells = cellsToSkip;
            console.log(`🔄 Will skip next ${skipNextCells} cells due to colSpan=${spanInfo.colSpan}`);
          }

          // Only log important cell data
          if (cellData.colSpan > 1 || cellContent) {
            console.log(`📄 Cell: ${day} ${timeSlot.time} -> "${cellContent}" (span=${cellData.colSpan})`);
          }
          row.push(cellData);
        }
      }

      console.log(`🏗️ PDF Row for ${day} (${row.length} cells):`, row.map((cell, i) => {
        if (i === 0) return `[${i}] ${cell} (DAY)`;
        const timeSlotIndex = i - 1;
        if (timeSlotIndex >= processedData.timeSlots.length) {
          return `[${i}] ${typeof cell === 'object' ? `"${cell.content}" (span=${cell.colSpan})` : `"${cell}"`} (EXTRA!)`;
        }
        const timeSlot = processedData.timeSlots[timeSlotIndex];
        const cellType = timeSlot.isBreak ? 'BREAK' : 'PERIOD';
        const cellContent = typeof cell === 'object' ? `"${cell.content}" (span=${cell.colSpan})` : `"${cell}"`;
        return `[${i}] ${cellContent} (${cellType})`;
      }));

      console.log(`✅ ${day} completed: ${row.length} cells`);

      tableData.push(row);
    }

    console.log('✅ Table data preparation completed successfully');

    } catch (error) {
      console.error('❌ Error during table data preparation:', error);
      throw error;
    }

    console.log('🏗️ Final table data summary:');
    console.log('  Headers:', headers.length, 'columns');
    console.log('  Data rows:', tableData.length, 'rows');
    tableData.forEach((row, i) => {
      console.log(`  Row ${i}: ${row.length} cells`);
    });

    // Calculate column widths
    const totalColumns = headers.length;
    const dayColumnWidth = 25;
    const remainingWidth = pageWidth - 40 - dayColumnWidth; // 40 for margins
    const regularColumnWidth = remainingWidth / (totalColumns - 1);
    const breakColumnWidth = 15; // Narrower for break columns

    // Create column styles
    const columnStyles: any = {
      0: { cellWidth: dayColumnWidth, fontStyle: 'bold', halign: 'center' }
    };

    // Set column widths for periods and breaks - for all columns we have
    for (let i = 1; i < headers.length; i++) {
      const timeSlotIndex = i - 1;
      if (timeSlotIndex < processedData.timeSlots.length) {
        const timeSlot = processedData.timeSlots[timeSlotIndex];
        columnStyles[i] = {
          cellWidth: timeSlot.isBreak ? breakColumnWidth : regularColumnWidth,
          halign: 'center',
          valign: 'middle',
          fontSize: timeSlot.isBreak ? 8 : 9
        };
      }
    }

    console.log('🏗️ Column styles defined for columns:', Object.keys(columnStyles));
    console.log('🏗️ Total column styles:', Object.keys(columnStyles).length);

    console.log('📊 Creating PDF table...');

    // Create the table with webapp-like styling and lab spanning
    try {
      autoTable(doc, {
      head: [headers],
      body: tableData,
      startY: 45,
      styles: {
        fontSize: 8,
        cellPadding: 3,
        lineColor: [0, 0, 0],
        lineWidth: 0.5,
        textColor: [0, 0, 0],
        halign: 'center',
        valign: 'middle'
      },
      headStyles: {
        fillColor: [52, 152, 219], // Blue header like webapp
        textColor: [255, 255, 255],
        fontSize: 9,
        fontStyle: 'bold',
        halign: 'center',
        valign: 'middle'
      },
      // Custom header styling for break columns
      didParseCell: function(data) {
        if (data.section === 'head') {
          const timeSlot = processedData.timeSlots[data.column.index - 1];
          if (timeSlot && timeSlot.isBreak) {
            if (timeSlot.label === 'TEA BREAK') {
              data.cell.styles.fillColor = [23, 162, 184]; // Blue background for tea break header
              data.cell.styles.textColor = [255, 255, 255]; // White text
            } else if (timeSlot.label === 'LUNCH BREAK') {
              data.cell.styles.fillColor = [255, 193, 7]; // Yellow background for lunch break header
              data.cell.styles.textColor = [33, 37, 41]; // Dark text
            }
          }
        } else {
          // Body cell styling
          const timeSlot = processedData.timeSlots[data.column.index - 1];
          if (timeSlot && timeSlot.isBreak) {
            if (timeSlot.label === 'TEA BREAK') {
              data.cell.styles.fillColor = [209, 236, 241]; // Light blue background for tea break cells
              data.cell.styles.textColor = [12, 84, 96]; // Dark blue text
            } else if (timeSlot.label === 'LUNCH BREAK') {
              data.cell.styles.fillColor = [255, 243, 205]; // Light yellow background for lunch break cells
              data.cell.styles.textColor = [133, 100, 4]; // Dark yellow text
            }
            data.cell.styles.fontStyle = 'italic';
          }

          // Handle lab cell styling
          if (data.cell.raw && typeof data.cell.raw === 'object' && data.cell.raw.styles) {
            Object.assign(data.cell.styles, data.cell.raw.styles);
          }
        }
      },
      columnStyles: columnStyles,
      alternateRowStyles: {
        fillColor: [248, 249, 250] // Light gray for alternate rows
      },
      margin: { left: 20, right: 20 },
      tableLineColor: [0, 0, 0],
      tableLineWidth: 0.5,
      // Handle column spanning for labs
      willDrawCell: function(data) {
        if (data.cell.raw && typeof data.cell.raw === 'object' && data.cell.raw.colSpan > 1) {
          // This is handled by autoTable automatically when colSpan is set in cell data
          return true;
        }
      }
    });

    console.log('✅ autoTable created successfully');

    } catch (error) {
      console.error('❌ Error creating autoTable:', error);
      throw error;
    }

    // Add footer with generation timestamp
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 20, pageHeight - 10);

    // Save the PDF
    const fileName = metadata.view === 'faculty'
      ? `${metadata.facultyName?.replace(/\s+/g, '_')}_Timetable_${metadata.academicYear}.pdf`
      : `${metadata.department}_Sem${metadata.semester}_Sec${metadata.section}_${metadata.academicYear}.pdf`;

    console.log('💾 Attempting to save PDF:', fileName);

    try {
      doc.save(fileName);
      console.log('🎉 PDF DOWNLOAD COMPLETED:', fileName);
    } catch (error) {
      console.error('❌ PDF SAVE ERROR:', error);
      throw error;
    }
  }

  /**
   * Export timetable as Excel - Exact visual replica of webapp
   */
  static async exportToExcel(exportData: ExportData): Promise<void> {
    const { metadata } = exportData;
    const processedData = await this.processDataForExport(exportData);

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Prepare data for Excel
    const excelData: any[][] = [];

    // Add title rows
    excelData.push([metadata.view === 'faculty'
      ? `Faculty Timetable - ${metadata.facultyName}`
      : `Class Timetable - ${metadata.department} Sem ${metadata.semester} Sec ${metadata.section}`]);
    excelData.push([`Academic Year: ${metadata.academicYear}`]);
    excelData.push([]); // Empty row

    // Add headers including breaks
    const headers = ['Day'];
    processedData.timeSlots.forEach(slot => {
      headers.push(slot.label);
    });
    excelData.push(headers);

    // Add timetable data with exact webapp formatting and lab spanning
    for (const day of processedData.days) {
      const row = [day];

      for (const timeSlot of processedData.timeSlots) {
        if (timeSlot.isBreak) {
          // Break columns - empty or with break label
          row.push('');
        } else {
          // Check if this is a lab span continuation (should be empty)
          const spanInfo = processedData.labSpans?.[day]?.[timeSlot.time];
          if (spanInfo && !spanInfo.isStart && spanInfo.colSpan === 0) {
            // This is a continuation of a lab span, add empty cell
            // The merge will handle the spanning
            row.push('');
            return;
          }

          // Regular period columns
          const slots = processedData.timetableGrid[day][timeSlot.time] || [];

          let cellContent = '';
          if (slots.length > 0) {
            const formattedSlots = await Promise.all(slots.map(async slot => {
              return await this.formatSlotForExcel(slot, metadata.view, metadata.selectedFaculty);
            }));
            cellContent = formattedSlots.join(', ');
          }
          row.push(cellContent);
        }
      }

      excelData.push(row);
    }

    // Create worksheet
    const worksheet = XLSX.utils.aoa_to_sheet(excelData);

    // Set column widths
    const colWidths = [{ wch: 12 }]; // Day column
    for (let i = 1; i < headers.length; i++) {
      const timeSlot = processedData.timeSlots[i - 1];
      if (timeSlot.isBreak) {
        colWidths.push({ wch: 8 }); // Narrower for break columns
      } else {
        colWidths.push({ wch: 25 }); // Wider for period columns
      }
    }
    worksheet['!cols'] = colWidths;

    // Handle cell merging for lab spans
    const merges: any[] = [];
    const headerRowIndex = 3; // 0-based index (title, academic year, empty, then headers)

    processedData.days.forEach((day, dayIndex) => {
      const rowIndex = headerRowIndex + 1 + dayIndex;
      let colIndex = 1; // Start after day column

      processedData.timeSlots.forEach(timeSlot => {
        if (timeSlot.isBreak) {
          // Break column - just increment
          colIndex++;
        } else {
          const spanInfo = processedData.labSpans?.[day]?.[timeSlot.time];
          if (spanInfo && spanInfo.isStart && spanInfo.colSpan > 1) {
            // Calculate how many actual columns this lab spans (excluding breaks)
            let actualSpan = 0;
            let tempColIndex = colIndex;

            // Count the actual period columns this lab spans
            for (let i = 0; i < spanInfo.colSpan; i++) {
              const timeSlotIndex = processedData.timeSlots.findIndex(ts => ts.time === timeSlot.time) + i;
              if (timeSlotIndex < processedData.timeSlots.length) {
                const currentTimeSlot = processedData.timeSlots[timeSlotIndex];
                if (!currentTimeSlot.isBreak) {
                  actualSpan++;
                }
              }
            }

            if (actualSpan > 1) {
              // Create merge range for lab span
              const startCol = colIndex;
              const endCol = colIndex + actualSpan - 1;
              merges.push({
                s: { r: rowIndex, c: startCol },
                e: { r: rowIndex, c: endCol }
              });
            }
          }
          colIndex++;
        }
      });
    });

    if (merges.length > 0) {
      worksheet['!merges'] = merges;
    }

    // Style the header row with break-specific colors
    for (let col = 0; col < headers.length; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col });
      if (!worksheet[cellAddress]) worksheet[cellAddress] = { t: 's', v: headers[col] };

      const timeSlot = col > 0 ? processedData.timeSlots[col - 1] : null;
      let headerStyle = {
        font: { bold: true, color: { rgb: 'FFFFFF' } },
        fill: { fgColor: { rgb: '3498DB' } }, // Default blue
        alignment: { horizontal: 'center', vertical: 'center' },
        border: {
          top: { style: 'thin', color: { rgb: '000000' } },
          bottom: { style: 'thin', color: { rgb: '000000' } },
          left: { style: 'thin', color: { rgb: '000000' } },
          right: { style: 'thin', color: { rgb: '000000' } }
        }
      };

      // Apply break-specific colors
      if (timeSlot && timeSlot.isBreak) {
        if (timeSlot.label === 'TEA BREAK') {
          headerStyle.fill = { fgColor: { rgb: '17A2B8' } }; // Blue for tea break
          headerStyle.font = { bold: true, color: { rgb: 'FFFFFF' } };
        } else if (timeSlot.label === 'LUNCH BREAK') {
          headerStyle.fill = { fgColor: { rgb: 'FFC107' } }; // Yellow for lunch break
          headerStyle.font = { bold: true, color: { rgb: '212529' } }; // Dark text
        }
      }

      worksheet[cellAddress].s = headerStyle;
    }

    // Style break columns
    for (let row = headerRowIndex + 1; row < excelData.length; row++) {
      for (let col = 1; col < headers.length; col++) {
        const timeSlot = processedData.timeSlots[col - 1];
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });

        if (!worksheet[cellAddress]) worksheet[cellAddress] = { t: 's', v: '' };

        if (timeSlot.isBreak) {
          // Style break columns with specific colors
          let breakStyle = {
            alignment: { horizontal: 'center', vertical: 'center' },
            border: {
              top: { style: 'thin', color: { rgb: '000000' } },
              bottom: { style: 'thin', color: { rgb: '000000' } },
              left: { style: 'thin', color: { rgb: '000000' } },
              right: { style: 'thin', color: { rgb: '000000' } }
            }
          };

          if (timeSlot.label === 'TEA BREAK') {
            breakStyle.fill = { fgColor: { rgb: 'D1ECF1' } }; // Light blue for tea break cells
            breakStyle.font = { italic: true, color: { rgb: '0C5460' } }; // Dark blue text
          } else if (timeSlot.label === 'LUNCH BREAK') {
            breakStyle.fill = { fgColor: { rgb: 'FFF3CD' } }; // Light yellow for lunch break cells
            breakStyle.font = { italic: true, color: { rgb: '856404' } }; // Dark yellow text
          } else {
            // Default break styling
            breakStyle.fill = { fgColor: { rgb: 'E9ECEF' } };
            breakStyle.font = { italic: true, color: { rgb: '6C757D' } };
          }

          worksheet[cellAddress].s = breakStyle;
        } else {
          // Style regular columns
          worksheet[cellAddress].s = {
            alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
            border: {
              top: { style: 'thin', color: { rgb: '000000' } },
              bottom: { style: 'thin', color: { rgb: '000000' } },
              left: { style: 'thin', color: { rgb: '000000' } },
              right: { style: 'thin', color: { rgb: '000000' } }
            }
          };
        }
      }
    }

    // Add worksheet to workbook
    const sheetName = metadata.view === 'faculty' ? 'Faculty Timetable' : 'Class Timetable';
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    // Save the Excel file
    const fileName = metadata.view === 'faculty'
      ? `${metadata.facultyName?.replace(/\s+/g, '_')}_Timetable_${metadata.academicYear}.xlsx`
      : `${metadata.department}_Sem${metadata.semester}_Sec${metadata.section}_${metadata.academicYear}.xlsx`;

    XLSX.writeFile(workbook, fileName);
  }
}
