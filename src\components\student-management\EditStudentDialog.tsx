import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { ClassStudent, ClassStudentService } from '@/services/ClassStudentService';
import { Loader2, User, Mail, Phone } from 'lucide-react';

interface EditStudentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (updatedStudent: ClassStudent) => void;
  student: ClassStudent | null;
  facultyId: string;
}

const EditStudentDialog: React.FC<EditStudentDialogProps> = ({
  isOpen,
  onClose,
  onSuccess,
  student,
  facultyId
}) => {
  const [formData, setFormData] = useState({
    student_name: '',
    email: '',
    student_mobile: '',
    father_mobile: '',
    mother_mobile: ''
  });
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  // Initialize form data when student changes
  useEffect(() => {
    if (student) {
      setFormData({
        student_name: student.student_name || '',
        email: student.email || '',
        student_mobile: student.student_mobile || '',
        father_mobile: student.father_mobile || '',
        mother_mobile: student.mother_mobile || ''
      });
    }
  }, [student]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    if (!student) return;

    // Validate required fields
    if (!formData.student_name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Student name is required.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);
      
      const result = await ClassStudentService.updateStudent(
        student.id,
        formData,
        facultyId
      );

      if (result.success && result.student) {
        toast({
          title: 'Success',
          description: 'Student information updated successfully.',
        });
        onSuccess(result.student);
        onClose();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to update student information.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating student:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (!saving) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Edit Student Information
          </DialogTitle>
          <DialogDescription>
            Update student details for {student?.usn}. All changes will be saved to the class records.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Student Name */}
          <div className="space-y-2">
            <Label htmlFor="student_name" className="text-sm font-medium">
              Student Name *
            </Label>
            <Input
              id="student_name"
              value={formData.student_name}
              onChange={(e) => handleInputChange('student_name', e.target.value)}
              placeholder="Enter student full name"
              disabled={saving}
            />
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium flex items-center gap-1">
              <Mail className="h-3 w-3" />
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              disabled={saving}
            />
          </div>

          {/* Contact Numbers */}
          <div className="space-y-4">
            <Label className="text-sm font-medium flex items-center gap-1">
              <Phone className="h-3 w-3" />
              Contact Numbers
            </Label>
            
            <div className="grid grid-cols-1 gap-3">
              <div className="space-y-1">
                <Label htmlFor="student_mobile" className="text-xs text-muted-foreground">
                  Student Mobile
                </Label>
                <Input
                  id="student_mobile"
                  value={formData.student_mobile}
                  onChange={(e) => handleInputChange('student_mobile', e.target.value)}
                  placeholder="Student's mobile number"
                  disabled={saving}
                />
              </div>
              
              <div className="space-y-1">
                <Label htmlFor="father_mobile" className="text-xs text-muted-foreground">
                  Father's Mobile
                </Label>
                <Input
                  id="father_mobile"
                  value={formData.father_mobile}
                  onChange={(e) => handleInputChange('father_mobile', e.target.value)}
                  placeholder="Father's mobile number"
                  disabled={saving}
                />
              </div>
              
              <div className="space-y-1">
                <Label htmlFor="mother_mobile" className="text-xs text-muted-foreground">
                  Mother's Mobile
                </Label>
                <Input
                  id="mother_mobile"
                  value={formData.mother_mobile}
                  onChange={(e) => handleInputChange('mother_mobile', e.target.value)}
                  placeholder="Mother's mobile number"
                  disabled={saving}
                />
              </div>
            </div>
          </div>

          {/* USN Display (Read-only) */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-muted-foreground">
              USN (Cannot be changed)
            </Label>
            <Input
              value={student?.usn || ''}
              disabled
              className="bg-muted"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={saving}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditStudentDialog;
