import { supabase } from '@/integrations/supabase/client';

export interface AttendanceSheetRecord {
  student_id: string;
  usn: string;
  student_name: string;
  attendance_data: { [date: string]: 'present' | 'absent' };
  sequential_numbers: { [date: string]: number | 'A' };
  total_classes: number;
  classes_attended: number;
  attendance_percentage: number;
  // Substitute assignment information
  substitute_sessions?: { [sessionKey: string]: {
    original_subject_code: string;
    substitution_notes: string;
    leave_request_id: string;
  }};
}

export interface AttendanceSheetData {
  subject_code: string;
  subject_name: string;
  subject_type: string;
  batch_name?: string;
  semester: string;
  section: string;
  department: string;
  class_dates: string[];
  students: AttendanceSheetRecord[];
  total_classes_conducted: number;
  last_updated: string;
}

export interface FacultySubjectSheet {
  subject_code: string;
  subject_name: string;
  subject_type: string;
  batch_name?: string;
  semester: string;
  section: string;
  department: string;
}

export class FacultyAttendanceSheetService {
  /**
   * Map department names for database queries
   */
  private static mapDepartmentName(userDepartment: string): string {
    const departmentMap: Record<string, string> = {
      'Computer Science and Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Electrical and Electronics Engineering': 'eee'
    };
    
    return departmentMap[userDepartment] || userDepartment.toLowerCase();
  }

  /**
   * Reverse map department names for student queries
   */
  private static reverseMapDepartmentName(shortDepartment: string): string {
    const reverseDepartmentMap: Record<string, string> = {
      'cse': 'Computer Science and Engineering',
      'ise': 'Information Science and Engineering',
      'ece': 'Electronics and Communication Engineering',
      'mech': 'Mechanical Engineering',
      'civil': 'Civil Engineering',
      'eee': 'Electrical and Electronics Engineering'
    };
    
    return reverseDepartmentMap[shortDepartment] || shortDepartment;
  }

  /**
   * Get all subjects taught by a faculty member for attendance sheets
   * ENHANCED: Shows only theory subjects and lab subjects where faculty is PRIMARY faculty
   */
  static async getFacultySubjectsForSheets(
    facultyId: string,
    userDepartment: string
  ): Promise<FacultySubjectSheet[]> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      console.log(`📋 ATTENDANCE SHEET: Fetching subjects for faculty ${facultyId} in department ${mappedDepartment}`);

      // ENHANCED: Only fetch subjects where faculty is PRIMARY faculty
      // This excludes lab classes where they are secondary faculty
      const { data: subjects, error } = await supabase
        .from('timetable_slots')
        .select(`
          subject_code,
          subject_name,
          semester,
          section,
          department,
          subject_type,
          batch_name,
          faculty_id,
          faculty2_id
        `)
        .eq('faculty_id', facultyId)  // CRITICAL: Only primary faculty assignments
        .eq('department', mappedDepartment);

      if (error) throw error;

      console.log(`📋 ATTENDANCE SHEET: Found ${subjects?.length || 0} primary faculty assignments`);

      // Log the filtering logic for transparency
      const theoryCount = subjects?.filter(s => s.subject_type === 'theory' || s.subject_type === 'elective').length || 0;
      const labCount = subjects?.filter(s => s.subject_type === 'lab' || s.subject_type === 'laboratory').length || 0;

      console.log(`📋 ATTENDANCE SHEET: ${theoryCount} theory subjects, ${labCount} lab subjects (primary faculty only)`);

      // Remove duplicates based on subject_code, semester, section, subject_type, and batch_name
      const uniqueSubjects = subjects?.reduce((acc, current) => {
        const key = `${current.subject_code}-${current.semester}-${current.section}-${current.subject_type}-${current.batch_name || 'no-batch'}`;
        if (!acc.find(item =>
          `${item.subject_code}-${item.semester}-${item.section}-${item.subject_type}-${item.batch_name || 'no-batch'}` === key
        )) {
          // Only include the essential fields for the attendance sheet
          acc.push({
            subject_code: current.subject_code,
            subject_name: current.subject_name,
            semester: current.semester,
            section: current.section,
            department: current.department,
            subject_type: current.subject_type,
            batch_name: current.batch_name
          });
        }
        return acc;
      }, [] as FacultySubjectSheet[]) || [];

      console.log(`📋 ATTENDANCE SHEET: Returning ${uniqueSubjects.length} unique subjects after deduplication`);

      return uniqueSubjects;
    } catch (error) {
      console.error('Error fetching faculty subjects for sheets:', error);
      throw error;
    }
  }

  /**
   * Generate attendance sheet data for a specific subject
   */
  static async generateAttendanceSheet(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    subjectType: string,
    batchName?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<AttendanceSheetData> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      // ENHANCED: Get subject details - only for primary faculty assignments
      let subjectQuery = supabase
        .from('timetable_slots')
        .select('subject_name, faculty_id, faculty2_id')
        .eq('faculty_id', facultyId)  // CRITICAL: Only primary faculty assignments
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('subject_type', subjectType);

      if (batchName) {
        subjectQuery = subjectQuery.eq('batch_name', batchName);
      } else if (subjectType === 'theory') {
        subjectQuery = subjectQuery.is('batch_name', null);
      }

      const { data: subjectInfo, error: subjectError } = await subjectQuery.limit(1).single();

      if (subjectError) {
        console.error('📋 ATTENDANCE SHEET ERROR: Subject not found or faculty not primary:', subjectError);
        throw new Error(`Subject not found or you are not the primary faculty for this subject. Attendance sheets are only available for subjects where you are the primary faculty.`);
      }

      console.log(`📋 ATTENDANCE SHEET: Confirmed primary faculty assignment for ${subjectCode}`);

      // Get time slots for this subject type and batch - only primary faculty assignments
      let timetableQuery = supabase
        .from('timetable_slots')
        .select('time_slot')
        .eq('faculty_id', facultyId)  // Only primary faculty assignments
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('subject_type', subjectType);

      if (batchName) {
        timetableQuery = timetableQuery.eq('batch_name', batchName);
      } else if (subjectType === 'theory') {
        timetableQuery = timetableQuery.is('batch_name', null);
      }

      const { data: timetableSlots, error: timetableError } = await timetableQuery;
      if (timetableError) throw timetableError;

      const timeSlots = timetableSlots?.map(slot => slot.time_slot) || [];
      
      if (timeSlots.length === 0) {
        return {
          subject_code: subjectCode,
          subject_name: subjectInfo?.subject_name || subjectCode,
          subject_type: subjectType,
          batch_name: batchName,
          semester,
          section,
          department: mappedDepartment,
          class_dates: [],
          students: [],
          total_classes_conducted: 0,
          last_updated: new Date().toISOString()
        };
      }

      // CRITICAL FIX: Use usn_attendance table with proper subject identifier filtering
      const actualSubjectType = (subjectType === 'laboratory' || subjectType === 'lab') ? 'lab' : 'theory';

      // Create subject identifier for proper filtering
      let subjectIdentifier;
      if (actualSubjectType === 'lab') {
        subjectIdentifier = batchName ? `${subjectCode}_LAB_${batchName}` : `${subjectCode}_LAB`;
      } else {
        subjectIdentifier = `${subjectCode}_THEORY`;
      }

      console.log(`📋 ATTENDANCE SHEET: Using subject identifier: ${subjectIdentifier} for ${subjectType} subject${batchName ? ` with batch ${batchName}` : ''}`);
      console.log(`📋 ATTENDANCE SHEET: Parameters - Subject: ${subjectCode}, Semester: ${semester}, Section: ${section}, Type: ${subjectType}`);

      // Handle department mapping inconsistencies
      const departmentVariants = [
        mappedDepartment,
        'cse',
        'CSE',
        userDepartment,
        userDepartment.toLowerCase()
      ];

      let attendanceRecords: any[] = [];
      let allRegularRecords: any[] = [];
      let allSubstituteRecords: any[] = [];

      // CRITICAL FIX: Query both regular and substitute attendance records from ALL department variants
      for (const deptVariant of departmentVariants) {
        console.log(`📋 Trying department variant: "${deptVariant}"`);
        console.log(`📋 DEBUGGING: Faculty ID being searched: ${facultyId}`);
        console.log(`📋 DEBUGGING: Subject identifier: ${subjectIdentifier}`);
        console.log(`📋 DEBUGGING: Date filters: ${dateFrom} to ${dateTo}`);

        // Query 1: Regular attendance records where faculty is primary instructor
        let regularAttendanceQuery = supabase
          .from('usn_attendance')
          .select('*')
          .eq('subject_identifier', subjectIdentifier)
          .eq('faculty_id', facultyId)
          .eq('semester', semester)
          .eq('section', section)
          .eq('department', deptVariant)
          .order('attendance_date', { ascending: true });

        // Apply date filters to regular query
        if (dateFrom) {
          regularAttendanceQuery = regularAttendanceQuery.gte('attendance_date', dateFrom);
        }
        if (dateTo) {
          regularAttendanceQuery = regularAttendanceQuery.lte('attendance_date', dateTo);
        }

        const { data: regularRecords, error: regularError } = await regularAttendanceQuery;

        console.log(`📋 REGULAR QUERY RESULT for ${deptVariant}:`, {
          query_params: {
            subject_identifier: subjectIdentifier,
            faculty_id: facultyId,
            semester,
            section,
            department: deptVariant,
            date_from: dateFrom,
            date_to: dateTo
          },
          records_found: regularRecords?.length || 0,
          error: regularError,
          sample_records: regularRecords?.slice(0, 3).map(r => ({
            attendance_date: r.attendance_date,
            time_slot: r.time_slot,
            subject_code: r.subject_code,
            subject_identifier: r.subject_identifier
          }))
        });

        // Collect regular records from this department variant
        if (!regularError && regularRecords && regularRecords.length > 0) {
          console.log(`✅ Found ${regularRecords.length} regular attendance records with department: "${deptVariant}"`);
          allRegularRecords.push(...regularRecords);
        }

        // Query 2: Substitute attendance records where this faculty was acting as substitute
        // We need to find records where the subject_identifier contains the substitute faculty's subject
        // and check if this faculty was assigned as substitute for those dates
        let substituteRecords: any[] = [];

        try {
          console.log(`🔍 DEBUGGING: Checking for substitute assignments for faculty ${facultyId}`);

          // Get substitute assignments from leave_requests where this faculty is the substitute
          const { data: leaveRequests, error: leaveError } = await supabase
            .from('leave_requests')
            .select(`
              id,
              faculty_id,
              start_date,
              end_date,
              affected_classes,
              status
            `)
            .eq('status', 'approved');

          if (!leaveError && leaveRequests) {
            console.log(`📋 DEBUGGING: Found ${leaveRequests.length} approved leave requests to check`);
            console.log(`📋 DEBUGGING: Sample leave request data:`, leaveRequests.slice(0, 2));

            for (const leaveRequest of leaveRequests) {
              console.log(`📋 DEBUGGING: Processing leave request ${leaveRequest.id}:`, {
                faculty_id: leaveRequest.faculty_id,
                start_date: leaveRequest.start_date,
                end_date: leaveRequest.end_date,
                affected_classes_type: typeof leaveRequest.affected_classes,
                affected_classes_length: Array.isArray(leaveRequest.affected_classes) ? leaveRequest.affected_classes.length : 'not array',
                affected_classes_sample: leaveRequest.affected_classes
              });

              if (leaveRequest.affected_classes && Array.isArray(leaveRequest.affected_classes)) {
                for (const affectedClass of leaveRequest.affected_classes) {
                  console.log(`📋 DEBUGGING: Checking affected class:`, {
                    class_data: affectedClass,
                    has_substitute_faculty_id: !!affectedClass.substitute_faculty_id,
                    substitute_faculty_id: affectedClass.substitute_faculty_id,
                    target_faculty_id: facultyId,
                    is_match: affectedClass.substitute_faculty_id === facultyId
                  });

                  // Check if this faculty is assigned as substitute for this class
                  if (affectedClass.substitute_faculty_id === facultyId) {
                    console.log(`🔍 FOUND SUBSTITUTE ASSIGNMENT for class:`, {
                      class_id: affectedClass.class_id,
                      substitute_faculty: facultyId,
                      date_range: `${leaveRequest.start_date} to ${leaveRequest.end_date}`,
                      affected_class_full: affectedClass
                    });

                    // CRITICAL FIX: Query attendance records for this substitute assignment
                    // Since affected_classes only has class_id, we need to query by faculty_id and date range
                    // The attendance records will be saved under the substitute faculty's subject identifier
                    let substituteFacultySubjectQuery = supabase
                      .from('usn_attendance')
                      .select('*')
                      .eq('faculty_id', facultyId)
                      .eq('semester', semester)
                      .eq('section', section)
                      .eq('department', deptVariant)
                      .gte('attendance_date', leaveRequest.start_date)
                      .lte('attendance_date', leaveRequest.end_date)
                      .order('attendance_date', { ascending: true });

                    // Apply additional date filters if specified
                    if (dateFrom && dateFrom > leaveRequest.start_date) {
                      substituteFacultySubjectQuery = substituteFacultySubjectQuery.gte('attendance_date', dateFrom);
                    }
                    if (dateTo && dateTo < leaveRequest.end_date) {
                      substituteFacultySubjectQuery = substituteFacultySubjectQuery.lte('attendance_date', dateTo);
                    }

                    const { data: subRecords, error: subError } = await substituteFacultySubjectQuery;

                    console.log(`🔍 SUBSTITUTE QUERY RESULT:`, {
                      query_params: {
                        faculty_id: facultyId,
                        semester,
                        section,
                        department: deptVariant,
                        date_range: `${leaveRequest.start_date} to ${leaveRequest.end_date}`
                      },
                      records_found: subRecords?.length || 0,
                      error: subError,
                      sample_record: subRecords?.[0]
                    });

                    if (!subError && subRecords && subRecords.length > 0) {
                      console.log(`✅ Found ${subRecords.length} substitute attendance records`);

                      // Mark these records as substitute assignments and add metadata
                      const markedSubRecords = subRecords.map(record => ({
                        ...record,
                        is_substitute_assignment: true,
                        original_faculty_id: leaveRequest.faculty_id,
                        original_subject_code: record.subject_code, // Use the subject code from the attendance record
                        leave_request_id: leaveRequest.id,
                        substitution_notes: affectedClass.notes || `Substitute assignment on ${record.attendance_date}`
                      }));

                      allSubstituteRecords.push(...markedSubRecords);
                    } else if (subError) {
                      console.warn(`⚠️ Error querying substitute attendance:`, subError);
                    } else {
                      console.log(`📋 No substitute attendance records found for this assignment`);
                    }
                  }
                }
              }
            }
          }
        } catch (substituteError) {
          console.warn('⚠️ Error checking substitute assignments:', substituteError);
        }
      }

      // CRITICAL FIX: Combine ALL records from ALL department variants
      attendanceRecords = [
        ...allRegularRecords,
        ...allSubstituteRecords
      ];

      // Remove duplicates based on unique combination of date, time_slot, and student_usn
      const uniqueRecords = attendanceRecords.filter((record, index, self) => {
        const recordKey = `${record.attendance_date}-${record.time_slot}-${record.student_usn}`;
        return index === self.findIndex(r => `${r.attendance_date}-${r.time_slot}-${r.student_usn}` === recordKey);
      });

      attendanceRecords = uniqueRecords;

      console.log(`✅ FINAL RESULT: Found total ${attendanceRecords.length} unique attendance records (${allRegularRecords.length} regular + ${allSubstituteRecords.length} substitute) across all department variants`);
      console.log(`📋 UNIQUE CLASS SESSIONS:`, [...new Set(attendanceRecords.map(r => `${r.attendance_date} ${r.time_slot}`))]);

      if (attendanceRecords.length === 0) {
        console.log(`❌ No attendance records found for any department variant`);
      }

      console.log(`📋 Final attendance records found: ${attendanceRecords.length}`);

      // CRITICAL FIX: Get students from attendance records if available, otherwise use student tables
      let studentsData: { id: string; usn: string; student_name: string }[] = [];

      if (attendanceRecords.length > 0) {
        // Extract unique students from attendance records
        const uniqueStudents = new Map<string, { id: string; usn: string; student_name: string }>();

        attendanceRecords.forEach(record => {
          if (!uniqueStudents.has(record.student_usn)) {
            uniqueStudents.set(record.student_usn, {
              id: record.student_usn, // Use USN as ID for consistency
              usn: record.student_usn,
              student_name: record.student_name
            });
          }
        });

        studentsData = Array.from(uniqueStudents.values()).sort((a, b) => a.usn.localeCompare(b.usn));
        console.log(`📋 Students extracted from attendance records: ${studentsData.length}`);
      } else {
        // Fallback: Use dynamic student loading service to get appropriate students
        try {
          const { FacultyStudentListService } = await import('./FacultyStudentListService');
          const students = await FacultyStudentListService.getStudentsForFacultySubject(
            facultyId,
            userDepartment,
            subjectCode,
            semester,
            section,
            subjectType,
            new Date().toLocaleDateString('en-US', { weekday: 'long' }), // Current day for lab batch detection
            timeSlots[0] || 'MANUAL' // Use first available time slot or default
          );

          // Transform to match expected format
          studentsData = students.map(student => ({
            id: student.id,
            usn: student.usn,
            student_name: student.student_name
          }));
          console.log(`📋 Students loaded from student tables: ${studentsData.length}`);
        } catch (error) {
          console.error('📋 Error loading students from tables:', error);
          studentsData = [];
        }
      }

      // Process attendance data
      // CRITICAL FIX: Use date-time_slot combinations to handle multiple classes per day
      console.log(`📋 RAW ATTENDANCE RECORDS:`, attendanceRecords?.slice(0, 3).map(r => ({
        attendance_date: r.attendance_date,
        time_slot: r.time_slot,
        student_usn: r.student_usn,
        is_substitute: r.is_substitute_assignment || false
      })));

      const uniqueClassSessions = [...new Set(attendanceRecords?.map(record => {
        const sessionKey = `${record.attendance_date}-${record.time_slot || 'unknown'}`;
        console.log(`📋 SESSION KEY GENERATION: ${record.attendance_date} + ${record.time_slot || 'unknown'} = ${sessionKey} ${record.is_substitute_assignment ? '(SUBSTITUTE)' : ''}`);
        return sessionKey;
      }) || [])].sort();
      const classDates = uniqueClassSessions; // Use class sessions instead of just dates

      console.log(`📋 ATTENDANCE SHEET: Found ${uniqueClassSessions.length} unique class sessions:`, uniqueClassSessions);

      // Log substitute assignments separately
      const substituteRecords = attendanceRecords?.filter(r => r.is_substitute_assignment) || [];
      if (substituteRecords.length > 0) {
        console.log(`📋 SUBSTITUTE ASSIGNMENTS: Found ${substituteRecords.length} substitute attendance records:`,
          substituteRecords.map(r => ({
            date: r.attendance_date,
            time_slot: r.time_slot,
            original_subject: r.original_subject_code,
            notes: r.substitution_notes
          }))
        );
      }

      // Also log unique dates for comparison
      const uniqueDates = [...new Set(attendanceRecords?.map(record => record.attendance_date) || [])].sort();
      console.log(`📋 ATTENDANCE SHEET: ${uniqueDates.length} unique dates vs ${uniqueClassSessions.length} unique class sessions`);

      const attendanceSheetData = this.processAttendanceSheetDataFromUSN(attendanceRecords || [], studentsData, classDates);

      return {
        subject_code: subjectCode,
        subject_name: subjectInfo?.subject_name || subjectCode,
        subject_type: subjectType,
        batch_name: batchName,
        semester,
        section,
        department: mappedDepartment,
        class_dates: classDates,
        students: attendanceSheetData,
        total_classes_conducted: uniqueClassSessions.length,
        last_updated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating attendance sheet:', error);
      throw error;
    }
  }

  /**
   * Process attendance data into sheet format (UPDATED for usn_attendance table with substitute support)
   */
  private static processAttendanceSheetDataFromUSN(
    attendanceRecords: any[],
    students: { id: string; usn: string; student_name: string }[],
    classSessions: string[] // Now receives session keys instead of just dates
  ): AttendanceSheetRecord[] {
    console.log(`📋 Processing attendance sheet data for ${students.length} students and ${classSessions.length} class sessions`);

    // Group attendance records by student USN (since usn_attendance table uses USN)
    const studentAttendanceMap = new Map<string, any[]>();
    attendanceRecords.forEach(record => {
      const studentKey = record.student_usn; // Use USN from usn_attendance table
      if (!studentAttendanceMap.has(studentKey)) {
        studentAttendanceMap.set(studentKey, []);
      }
      studentAttendanceMap.get(studentKey)!.push(record);
    });

    console.log(`📋 Attendance records grouped by USN: ${studentAttendanceMap.size} students have records`);

    // Create a map to track which sessions are substitute assignments
    const substituteSessionMap = new Map<string, any>();
    attendanceRecords.forEach(record => {
      if (record.is_substitute_assignment) {
        const sessionKey = `${record.attendance_date}-${record.time_slot || 'unknown'}`;
        substituteSessionMap.set(sessionKey, {
          original_subject_code: record.original_subject_code,
          substitution_notes: record.substitution_notes,
          leave_request_id: record.leave_request_id
        });
      }
    });

    return students.map(student => {
      const studentRecords = studentAttendanceMap.get(student.usn) || []; // Match by USN
      const attendanceData: { [sessionKey: string]: 'present' | 'absent' } = {};
      const sequentialNumbers: { [sessionKey: string]: number | 'A' } = {};

      let presentCount = 0;

      // Process each class session (date-time_slot combination)
      classSessions.forEach(sessionKey => {
        // CRITICAL FIX: Match by session key (date-time_slot combination)
        const record = studentRecords.find(r => {
          const recordSessionKey = `${r.attendance_date}-${r.time_slot || 'unknown'}`;
          const isMatch = recordSessionKey === sessionKey;
          if (isMatch && r.is_substitute_assignment) {
            console.log(`🔍 SUBSTITUTE MATCH: Found substitute record for "${sessionKey}" - Original subject: ${r.original_subject_code}`);
          }
          return isMatch;
        });

        if (record) {
          attendanceData[sessionKey] = record.status;
          if (record.status === 'present') {
            presentCount++;
            sequentialNumbers[sessionKey] = presentCount;
          } else {
            sequentialNumbers[sessionKey] = 'A';
          }
        } else {
          // If no record found for this session, student was not marked (treat as absent for display)
          // But don't include in attendance_data to distinguish from explicitly marked absent
        }
      });

      const totalClasses = classSessions.length;
      const classesAttended = Object.values(attendanceData).filter(status => status === 'present').length;
      const attendancePercentage = totalClasses > 0 ? Math.round((classesAttended / totalClasses) * 100) : 0;

      return {
        student_id: student.id,
        usn: student.usn,
        student_name: student.student_name,
        attendance_data: attendanceData,
        sequential_numbers: sequentialNumbers,
        total_classes: totalClasses,
        classes_attended: classesAttended,
        attendance_percentage: attendancePercentage,
        // Add substitute session information for UI display
        substitute_sessions: Object.fromEntries(
          Array.from(substituteSessionMap.entries()).filter(([sessionKey]) =>
            attendanceData.hasOwnProperty(sessionKey)
          )
        )
      };
    }).sort((a, b) => a.usn.localeCompare(b.usn)); // Sort by USN
  }

  /**
   * Process attendance data into sheet format with sequential numbering (LEGACY for old attendance table)
   */
  private static processAttendanceSheetData(
    attendanceRecords: any[],
    students: any[],
    classDates: string[]
  ): AttendanceSheetRecord[] {
    // Group attendance by student
    const studentAttendanceMap = new Map<string, any[]>();
    attendanceRecords.forEach(record => {
      if (!studentAttendanceMap.has(record.student_id)) {
        studentAttendanceMap.set(record.student_id, []);
      }
      studentAttendanceMap.get(record.student_id)!.push(record);
    });

    return students.map(student => {
      const studentRecords = studentAttendanceMap.get(student.id) || [];
      const attendanceData: { [date: string]: 'present' | 'absent' } = {};
      const sequentialNumbers: { [date: string]: number | 'A' } = {};
      
      let presentCount = 0;
      
      // Process each class date
      classDates.forEach(date => {
        const record = studentRecords.find(r => r.attendance_date === date);
        if (record) {
          attendanceData[date] = record.status;
          if (record.status === 'present') {
            presentCount++;
            sequentialNumbers[date] = presentCount;
          } else {
            sequentialNumbers[date] = 'A';
          }
        }
      });

      const totalClasses = studentRecords.length;
      const classesAttended = studentRecords.filter(r => r.status === 'present').length;
      const attendancePercentage = totalClasses > 0 ? (classesAttended / totalClasses) * 100 : 0;

      return {
        student_id: student.id,
        usn: student.usn,
        student_name: student.student_name,
        attendance_data: attendanceData,
        sequential_numbers: sequentialNumbers,
        total_classes: totalClasses,
        classes_attended: classesAttended,
        attendance_percentage: Math.round(attendancePercentage * 100) / 100
      };
    }).sort((a, b) => a.usn.localeCompare(b.usn)); // Sort by USN
  }

  /**
   * Export attendance sheet data to CSV format (with substitute assignment indicators)
   */
  static exportToCSV(attendanceSheet: AttendanceSheetData): string {
    // CRITICAL FIX: Use formatted session headers for CSV export with substitute indicators
    const sessionHeaders = attendanceSheet.class_dates.map(sessionKey => {
      const baseHeader = this.formatSessionHeader(sessionKey);

      // Check if any student has substitute session data for this session
      const hasSubstituteData = attendanceSheet.students.some(student =>
        student.substitute_sessions && student.substitute_sessions[sessionKey]
      );

      return hasSubstituteData ? `${baseHeader} (SUB)` : baseHeader;
    });

    const headers = ['USN', 'Student Name', ...sessionHeaders, 'Total Classes', 'Classes Attended', 'Attendance %'];

    const csvData = attendanceSheet.students.map(student => [
      student.usn,
      student.student_name,
      ...attendanceSheet.class_dates.map(sessionKey => {
        const value = student.sequential_numbers[sessionKey];
        const isSubstitute = student.substitute_sessions && student.substitute_sessions[sessionKey];

        if (value === 'A') {
          return isSubstitute ? 'A(SUB)' : 'A';
        } else if (value) {
          return isSubstitute ? `${value}(SUB)` : value;
        } else {
          return '-';
        }
      }),
      student.total_classes,
      student.classes_attended,
      `${student.attendance_percentage}%`
    ]);

    return [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
  }

  /**
   * Format session key into readable column header
   */
  static formatSessionHeader(sessionKey: string): string {
    console.log(`🎯 SESSION DEBUG: Input sessionKey: "${sessionKey}"`);

    if (sessionKey.includes('-unknown')) {
      // Handle cases where time_slot is unknown
      const date = sessionKey.replace('-unknown', '');
      console.log(`🎯 SESSION DEBUG: Unknown time slot, extracted date: "${date}"`);
      const formatted = this.formatDate(date);
      console.log(`🎯 SESSION DEBUG: Final formatted (unknown): "${formatted}"`);
      return formatted;
    }

    // CRITICAL FIX: Handle session keys with multiple hyphens (date format + time slot format)
    // Expected format: "2025-04-07-11:30-12:25" or "2025-04-07-unknown"
    const lastHyphenIndex = sessionKey.lastIndexOf('-');

    if (lastHyphenIndex === -1) {
      // No hyphen found, treat entire string as date
      console.log(`🎯 SESSION DEBUG: No hyphen found, treating as date: "${sessionKey}"`);
      const formatted = this.formatDate(sessionKey);
      console.log(`🎯 SESSION DEBUG: Final formatted (no hyphen): "${formatted}"`);
      return formatted;
    }

    // Split at the last hyphen to separate date from time slot
    const beforeLastHyphen = sessionKey.substring(0, lastHyphenIndex);
    const afterLastHyphen = sessionKey.substring(lastHyphenIndex + 1);

    console.log(`🎯 SESSION DEBUG: Before last hyphen: "${beforeLastHyphen}", After last hyphen: "${afterLastHyphen}"`);

    // Check if the part after last hyphen looks like a time (contains colon) or is a time component
    if (afterLastHyphen.includes(':') || afterLastHyphen.match(/^\d{2}$/)) {
      // This looks like a time component, need to find the actual date-time boundary
      // Look for pattern: YYYY-MM-DD-HH:MM-HH:MM or YYYY-MM-DD-unknown
      const datePattern = /^(\d{4}-\d{2}-\d{2})-(.+)$/;
      const match = sessionKey.match(datePattern);

      if (match) {
        const date = match[1]; // YYYY-MM-DD
        const timeSlot = match[2]; // HH:MM-HH:MM or unknown

        console.log(`🎯 SESSION DEBUG: Regex match - date: "${date}", timeSlot: "${timeSlot}"`);

        const formattedDate = this.formatDate(date);
        console.log(`🎯 SESSION DEBUG: Formatted date: "${formattedDate}"`);

        if (timeSlot && timeSlot !== 'unknown') {
          const final = `${formattedDate} (${timeSlot})`;
          console.log(`🎯 SESSION DEBUG: Final formatted (with time): "${final}"`);
          return final;
        }

        console.log(`🎯 SESSION DEBUG: Final formatted (date only): "${formattedDate}"`);
        return formattedDate;
      }
    }

    // Fallback: treat the part before last hyphen as date
    const formattedDate = this.formatDate(beforeLastHyphen);
    console.log(`🎯 SESSION DEBUG: Fallback - formatted date: "${formattedDate}"`);

    if (afterLastHyphen && afterLastHyphen !== 'unknown') {
      const final = `${formattedDate} (${afterLastHyphen})`;
      console.log(`🎯 SESSION DEBUG: Final formatted (fallback with time): "${final}"`);
      return final;
    }

    console.log(`🎯 SESSION DEBUG: Final formatted (fallback date only): "${formattedDate}"`);
    return formattedDate;
  }

  /**
   * Format date for display
   */
  private static formatDate(dateString: string): string {
    try {
      console.log(`🗓️ DATE DEBUG: Input dateString: "${dateString}"`);

      // CRITICAL FIX: Handle date parsing to avoid timezone issues
      // If the date is in YYYY-MM-DD format, parse it as local date
      if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day); // month is 0-indexed
        console.log(`🗓️ DATE DEBUG: Parsed as local date: ${date.toDateString()}`);

        const formatted = date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric'
        });
        console.log(`🗓️ DATE DEBUG: Formatted result: "${formatted}"`);
        return formatted;
      } else {
        // For other formats, use the original parsing
        const date = new Date(dateString);
        console.log(`🗓️ DATE DEBUG: Parsed with Date constructor: ${date.toDateString()}`);

        const formatted = date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric'
        });
        console.log(`🗓️ DATE DEBUG: Formatted result: "${formatted}"`);
        return formatted;
      }
    } catch (error) {
      console.error(`🗓️ DATE DEBUG: Error parsing date "${dateString}":`, error);
      return dateString; // Return original string if parsing fails
    }
  }

  /**
   * Generate filename for export
   */
  static generateExportFilename(
    attendanceSheet: AttendanceSheetData,
    dateFrom?: string,
    dateTo?: string,
    format: 'csv' | 'pdf' = 'csv'
  ): string {
    let filename = `${attendanceSheet.subject_code}_attendance_sheet`;

    if (attendanceSheet.batch_name) {
      filename += `_batch_${attendanceSheet.batch_name}`;
    }

    if (dateFrom || dateTo) {
      filename += '_filtered';
    }

    filename += `.${format}`;
    return filename;
  }
}
