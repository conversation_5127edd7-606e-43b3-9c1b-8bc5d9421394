import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Users, Upload, Building2, AlertCircle, GraduationCap, Shield, List, Beaker } from 'lucide-react';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { supabase } from '@/integrations/supabase/client';
import ClassStudentBulkUpload from './ClassStudentBulkUpload';
import StudentAuthBulkUpload from './StudentAuthBulkUpload';
import ClassStudentList from './ClassStudentList';
import LabBatchList from './LabBatchList';
import { useAuth } from '@/contexts/AuthContext';

const StudentManagement: React.FC = () => {
  const [showClassBulkUpload, setShowClassBulkUpload] = useState(false);
  const [showAuthBulkUpload, setShowAuthBulkUpload] = useState(false);
  const [isClassTeacher, setIsClassTeacher] = useState(false);
  const [classTeacherAssignment, setClassTeacherAssignment] = useState<{
    department: string;
    semester: string;
    section: string;
  } | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const { toast } = useToast();
  const { user } = useAuth();

  // Function to refresh student list
  const handleRefreshStudentList = () => {
    setRefreshKey(prev => prev + 1);
  };

  // Get user's department information
  const { department, departmentName, fullName, loading: departmentLoading, error: departmentError } = useUserDepartment();

  // Check if user is a class teacher and get their assignment
  useEffect(() => {
    async function checkClassTeacherStatus() {
      if (!user?.id) return;

      try {
        const { data: assignment, error } = await supabase
          .from('class_teachers')
          .select('department, semester, section')
          .eq('faculty_id', user.id)
          .eq('academic_year', '2024-2025')
          .single();

        if (!error && assignment) {
          setIsClassTeacher(true);
          setClassTeacherAssignment(assignment);
        } else {
          setIsClassTeacher(false);
          setClassTeacherAssignment(null);
        }
      } catch (error) {
        console.error('Error checking class teacher status:', error);
        setIsClassTeacher(false);
        setClassTeacherAssignment(null);
      }
    }

    checkClassTeacherStatus();
  }, [user?.id]);



  // Show loading state while fetching department info
  if (departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading department information...</span>
        </div>
      </div>
    );
  }

  // Show error if department info couldn't be loaded
  if (departmentError || !department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {departmentError || 'Unable to load your department information. Please contact administrator to set your department.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Users className="h-6 w-6 text-primary" />
        <h1 className="text-3xl font-bold">Student Management</h1>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Managing:</strong> {departmentName} Department
          {fullName && <span className="ml-4"><strong>User:</strong> {fullName}</span>}
          {isClassTeacher && classTeacherAssignment && (
            <span className="ml-4">
              <strong>Class Teacher:</strong> {classTeacherAssignment.semester}{classTeacherAssignment.section}
            </span>
          )}
        </AlertDescription>
      </Alert>

      {/* Class Teacher Alert */}
      {isClassTeacher && classTeacherAssignment && (
        <Alert className="border-green-200 bg-green-50">
          <GraduationCap className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Class Teacher Mode:</strong> You can only manage students for your assigned class:
            {classTeacherAssignment.semester}{classTeacherAssignment.section} - {classTeacherAssignment.department.toUpperCase()}
          </AlertDescription>
        </Alert>
      )}

      {/* Class Teacher Primary View - Tabbed Interface */}
      {isClassTeacher ? (
        <div className="space-y-6">
          {/* Quick Actions for Class Teachers */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Quick Actions
                  </CardTitle>
                  <CardDescription>
                    Manage your class data and student records
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowClassBulkUpload(true)}
                    className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Students
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAuthBulkUpload(true)}
                    className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Create Auth Accounts
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Tabbed Interface for Class Teachers */}
          <Tabs defaultValue="class-students" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="class-students" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Class Students
              </TabsTrigger>
              <TabsTrigger value="lab-batches" className="flex items-center gap-2">
                <Beaker className="h-4 w-4" />
                Lab Batches
              </TabsTrigger>
            </TabsList>

            <TabsContent value="class-students" className="space-y-6">
              <ClassStudentList
                key={refreshKey}
                facultyId={user?.id || ''}
                onRefresh={handleRefreshStudentList}
              />
            </TabsContent>

            <TabsContent value="lab-batches" className="space-y-6">
              <LabBatchList
                key={refreshKey}
                facultyId={user?.id || ''}
                onRefresh={handleRefreshStudentList}
              />
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        /* Non-Class Teacher View - Upload Interface */
        <Tabs defaultValue="upload" className="space-y-6">
          <TabsList className="grid w-full grid-cols-1">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Upload Students
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6">
            <Card>
              <CardContent className="py-8">
                <Alert>
                  <Users className="h-4 w-4" />
                  <AlertDescription>
                    You are not assigned as a class teacher. Only class teachers can view and manage student lists. You can still upload student data using the upload functionality below.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Student Management
                    </CardTitle>
                    <CardDescription>
                      Upload student data and create authentication accounts for {departmentName} Department.
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowClassBulkUpload(true)}
                      className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Class Students
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAuthBulkUpload(true)}
                      className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                    >
                      <Shield className="h-4 w-4 mr-2" />
                      Create Auth Accounts
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Upload Class Students */}
                  <div className="p-6 border rounded-lg bg-green-50 border-green-200">
                    <div className="flex items-center gap-3 mb-3">
                      <Upload className="h-8 w-8 text-green-600" />
                      <div>
                        <h3 className="font-semibold text-green-800">Step 1: Upload Class Students</h3>
                        <p className="text-sm text-green-700">Upload student data with USN, names, and phone numbers</p>
                      </div>
                    </div>
                    <ul className="text-sm text-green-700 space-y-1 mb-4">
                      <li>• Select department, semester, section</li>
                      <li>• Download Excel template</li>
                      <li>• Fill with student information</li>
                      <li>• Upload to create class records</li>
                    </ul>
                    <Button
                      onClick={() => setShowClassBulkUpload(true)}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Class Students
                    </Button>
                  </div>

                  {/* Create Auth Accounts */}
                  <div className="p-6 border rounded-lg bg-blue-50 border-blue-200">
                    <div className="flex items-center gap-3 mb-3">
                      <Shield className="h-8 w-8 text-blue-600" />
                      <div>
                        <h3 className="font-semibold text-blue-800">Step 2: Create Auth Accounts</h3>
                        <p className="text-sm text-blue-700">Enable student login with default passwords</p>
                      </div>
                    </div>
                    <ul className="text-sm text-blue-700 space-y-1 mb-4">
                      <li>• Select the same class from Step 1</li>
                      <li>• System creates login accounts</li>
                      <li>• Default password: Password@123</li>
                      <li>• Students can now access portal</li>
                    </ul>
                    <Button
                      onClick={() => setShowAuthBulkUpload(true)}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                    >
                      <Shield className="h-4 w-4 mr-2" />
                      Create Auth Accounts
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Class Student Bulk Upload Dialog (for class_students table) */}
      {showClassBulkUpload && (
        <ClassStudentBulkUpload
          isOpen={showClassBulkUpload}
          onClose={() => setShowClassBulkUpload(false)}
          onSuccess={() => {
            setShowClassBulkUpload(false);
            handleRefreshStudentList(); // Refresh student list after upload
          }}
          facultyId={user?.id}
        />
      )}

      {/* Authentication Bulk Upload Dialog */}
      {showAuthBulkUpload && (
        <StudentAuthBulkUpload
          isOpen={showAuthBulkUpload}
          onClose={() => setShowAuthBulkUpload(false)}
          onSuccess={() => {
            setShowAuthBulkUpload(false);
            // Auth upload doesn't affect class_students table, so no refresh needed
          }}
          facultyId={user?.id}
        />
      )}
    </div>
  );
};

export default StudentManagement;
