import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { 
  SubstituteNotificationService, 
  SubstituteNotification 
} from '@/services/SubstituteNotificationService';

export function useSubstituteNotifications() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<SubstituteNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Handle new notification
  const handleNewNotification = useCallback((notification: SubstituteNotification) => {
    console.log('🔔 New substitute notification received:', notification);
    
    setNotifications(prev => [notification, ...prev]);
    setUnreadCount(prev => prev + 1);
    
    // Show toast notification
    SubstituteNotificationService.sendToastNotification(notification, toast);
  }, [toast]);

  // Load initial notifications
  const loadNotifications = useCallback(async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      const unreadNotifications = await SubstituteNotificationService.getUnreadNotifications(user.id);
      setNotifications(unreadNotifications);
      setUnreadCount(unreadNotifications.length);
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Check for new assignments (polling fallback)
  const checkForNewAssignments = useCallback(async () => {
    if (!user?.id) return;

    try {
      const newAssignments = await SubstituteNotificationService.checkForNewAssignments(user.id);
      
      if (newAssignments.length > 0) {
        console.log('📋 Found new substitute assignments:', newAssignments);
        
        // Add new assignments to notifications
        setNotifications(prev => {
          const existingIds = new Set(prev.map(n => n.leave_request_id));
          const newNotifications = newAssignments.filter(
            n => !existingIds.has(n.leave_request_id)
          );
          
          if (newNotifications.length > 0) {
            setUnreadCount(prev => prev + newNotifications.length);
            
            // Show toast for the first new notification
            if (newNotifications[0]) {
              SubstituteNotificationService.sendToastNotification(newNotifications[0], toast);
            }
          }
          
          return [...newNotifications, ...prev];
        });
      }
    } catch (error) {
      console.error('Error checking for new assignments:', error);
    }
  }, [user?.id, toast]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await SubstituteNotificationService.markNotificationAsRead(notificationId);
      
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, read_at: new Date().toISOString() }
            : n
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const unreadNotifications = notifications.filter(n => !n.read_at);
      
      await Promise.all(
        unreadNotifications.map(n => 
          SubstituteNotificationService.markNotificationAsRead(n.id)
        )
      );
      
      setNotifications(prev => 
        prev.map(n => ({ ...n, read_at: new Date().toISOString() }))
      );
      
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, [notifications]);

  // Subscribe to real-time notifications
  useEffect(() => {
    if (!user?.id) return;

    console.log('🔔 Setting up substitute notification subscription for faculty:', user.id);

    // Subscribe to real-time updates
    const unsubscribe = SubstituteNotificationService.subscribeToSubstituteAssignments(
      user.id,
      handleNewNotification
    );

    // Load initial notifications
    loadNotifications();

    // Set up polling as fallback (check every 5 minutes)
    const pollInterval = setInterval(checkForNewAssignments, 5 * 60 * 1000);

    // Cleanup
    return () => {
      console.log('🔕 Cleaning up substitute notification subscription');
      unsubscribe();
      clearInterval(pollInterval);
    };
  }, [user?.id, handleNewNotification, loadNotifications, checkForNewAssignments]);

  // Refresh notifications manually
  const refresh = useCallback(async () => {
    await loadNotifications();
    await checkForNewAssignments();
  }, [loadNotifications, checkForNewAssignments]);

  return {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    refresh,
    checkForNewAssignments
  };
}
