/**
 * Comprehensive Gemini AI Service for Quiz Management
 * Handles module detection, content analysis, and question generation
 */

import { DifficultyLevel } from '@/types/quiz-system';

const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const GEMINI_BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models';

// Available Gemini models in order of preference
const GEMINI_MODELS = [
  'gemini-1.5-flash',
  'gemini-1.5-pro',
  'gemini-pro'
];

interface DetectedModule {
  module_number: number;
  module_title: string;
  module_description: string;
  key_topics: string[];
  estimated_duration_hours: number;
  difficulty_level: DifficultyLevel;
  content_preview: string;
}

interface ModuleDetectionResult {
  total_modules: number;
  detected_modules: DetectedModule[];
  syllabus_overview: string;
  subject_complexity: DifficultyLevel;
  recommended_question_distribution: Record<string, number>;
  ai_confidence_score: number;
}

interface ChapterMapping {
  module_name: string;
  chapter_references: string[];  // e.g., ["Chapter 1 (1.1-1.4)", "Chapter 2 (2.1-2.5)"]
  textbook_sections: string[];   // e.g., ["1.1", "1.2", "1.3", "1.4", "2.1", "2.2", "2.3", "2.4", "2.5"]
  content_scope: string;         // Description of content boundaries
}

interface ContentWeighting {
  textbook_based: number;      // 80-95%, default 80% (MANDATORY MINIMUM)
  syllabus_based: number;      // 5-15%, default 10% (REDUCED)
  cross_referenced: number;    // 2-8%, default 5% (REDUCED)
  ai_synthesis: number;        // 2-5%, default 5% (REDUCED)
}

interface QuestionGenerationRequest {
  module_content: string;
  textbook_content?: string;
  module_title: string;
  subject_name: string;
  difficulty_level: DifficultyLevel;
  question_count: number;
  focus_areas?: string[];
  content_weighting?: ContentWeighting;
  chapter_mapping?: ChapterMapping;  // Chapter-specific boundaries for textbook questions
}

interface GeneratedQuestion {
  question_text: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  correct_answer: 'A' | 'B' | 'C' | 'D';
  explanation: string;
  difficulty_level: DifficultyLevel;
  topic_tags: string[];
  source_reference: string;
  source_type: 'syllabus_based' | 'textbook_based' | 'cross_referenced' | 'ai_synthesis';
  chapter_reference?: string;  // e.g., "Chapter 1 (1.1-1.4)" for textbook questions
  section_numbers?: string[];  // e.g., ["1.1", "1.2"] for specific sections
  ai_confidence: number;
}

interface QuestionGenerationResult {
  questions: GeneratedQuestion[];
  generation_metadata: {
    total_generated: number;
    average_confidence: number;
    processing_time_ms: number;
    model_used: string;
    content_analysis: {
      key_concepts_identified: string[];
      content_complexity: DifficultyLevel;
      coverage_percentage: number;
    };
  };
}

export class ComprehensiveGeminiService {

  /**
   * Extract chapter mappings from syllabus content
   */
  static extractChapterMappings(syllabusContent: string): ChapterMapping[] {
    const chapterMappings: ChapterMapping[] = [];

    // Enhanced regex patterns to detect chapter references
    const modulePatterns = [
      // Pattern: "Module 1: Title ... Textbook 1: Chapter 1 - 1.1 to 1.4, Chapter 2 - 2.1 to 2.5"
      /Module\s+(\d+):\s*([^:]+?)(?:.*?Textbook\s*\d*:\s*(.+?)(?=Module|\n\n|$))/gis,
      // Pattern: "MODULE-1 ... Textbook 1: Chapter 1 - 1.1 to 1.4, Chapter 2 - 2.1 to 2.5"
      /MODULE[-\s]*(\d+)\s*([^:]+?)(?:.*?Textbook\s*\d*:\s*(.+?)(?=MODULE|$))/gis,
      // Pattern: Direct chapter references in module content
      /Module\s+(\d+):\s*([^:]+?)(?:.*?(Chapter\s+\d+[^:]*(?:\d+\.\d+[^:]*)+))/gis
    ];

    for (const pattern of modulePatterns) {
      let match;
      while ((match = pattern.exec(syllabusContent)) !== null) {
        const moduleNumber = parseInt(match[1]);
        const moduleTitle = match[2].trim();
        const chapterText = match[3] || '';

        if (chapterText) {
          const chapterReferences = this.parseChapterReferences(chapterText);
          const textbookSections = this.extractSectionNumbers(chapterText);

          chapterMappings.push({
            module_name: `Module ${moduleNumber}: ${moduleTitle}`,
            chapter_references: chapterReferences,
            textbook_sections: textbookSections,
            content_scope: `Covers ${chapterReferences.join(', ')} with sections ${textbookSections.join(', ')}`
          });
        }
      }
    }

    return chapterMappings;
  }

  /**
   * Parse chapter references from text
   */
  private static parseChapterReferences(chapterText: string): string[] {
    const references: string[] = [];

    // Pattern: "Chapter 1 - 1.1 to 1.4"
    const chapterPattern = /Chapter\s+(\d+)(?:\s*[-:]\s*(\d+\.\d+)\s*to\s*(\d+\.\d+))?/gi;
    let match;

    while ((match = chapterPattern.exec(chapterText)) !== null) {
      const chapterNum = match[1];
      const startSection = match[2];
      const endSection = match[3];

      if (startSection && endSection) {
        references.push(`Chapter ${chapterNum} (${startSection}-${endSection})`);
      } else {
        references.push(`Chapter ${chapterNum}`);
      }
    }

    return references;
  }

  /**
   * Extract section numbers from chapter text
   */
  private static extractSectionNumbers(chapterText: string): string[] {
    const sections: string[] = [];

    // Pattern: "1.1 to 1.4" or "2.1 to 2.5"
    const sectionPattern = /(\d+\.\d+)\s*to\s*(\d+\.\d+)/gi;
    let match;

    while ((match = sectionPattern.exec(chapterText)) !== null) {
      const startSection = match[1];
      const endSection = match[2];

      // Generate all sections in range
      const [startChapter, startNum] = startSection.split('.').map(Number);
      const [endChapter, endNum] = endSection.split('.').map(Number);

      if (startChapter === endChapter) {
        for (let i = startNum; i <= endNum; i++) {
          sections.push(`${startChapter}.${i}`);
        }
      }
    }

    return sections;
  }

  /**
   * Detect and extract modules from syllabus content
   */
  static async detectModulesFromSyllabus(
    syllabusContent: string,
    subjectName: string
  ): Promise<ModuleDetectionResult> {
    if (!GEMINI_API_KEY) {
      throw new Error('Gemini API key not configured');
    }

    console.log('🔍 Starting intelligent module detection...');
    console.log('📊 Content length:', syllabusContent.length);

    const prompt = `
You are an expert educational content analyzer. Analyze the following syllabus for "${subjectName}" and intelligently detect and extract distinct modules/units.

SYLLABUS CONTENT:
${syllabusContent}

Your task is to:
1. Identify all distinct modules/units/chapters in the syllabus
2. Extract the title, description, and key topics for each module
3. Estimate the complexity and duration for each module
4. Provide a comprehensive analysis of the entire syllabus

Return ONLY a JSON object with this exact structure:
{
  "total_modules": 5,
  "detected_modules": [
    {
      "module_number": 1,
      "module_title": "Introduction to Data Structures",
      "module_description": "Fundamental concepts of data organization and storage",
      "key_topics": ["Arrays", "Linked Lists", "Basic Operations"],
      "estimated_duration_hours": 12,
      "difficulty_level": "easy",
      "content_preview": "This module covers the basic concepts..."
    }
  ],
  "syllabus_overview": "This course covers fundamental concepts...",
  "subject_complexity": "medium",
  "recommended_question_distribution": {
    "easy": 6,
    "medium": 10,
    "hard": 4
  },
  "ai_confidence_score": 0.95
}

IMPORTANT REQUIREMENTS:
- Detect ALL modules present in the syllabus
- Each module should have a clear, descriptive title
- Key topics should be specific and actionable
- Difficulty levels: "easy", "medium", "hard"
- Ensure module numbers are sequential starting from 1
- Content preview should be 2-3 sentences summarizing the module
- Confidence score should reflect how clearly modules were identified (0.0 to 1.0)
`;

    const startTime = Date.now();
    let lastError: Error | null = null;

    // Try multiple Gemini models
    for (const model of GEMINI_MODELS) {
      try {
        console.log(`🚀 Trying Gemini model: ${model}`);

        const response = await fetch(`${GEMINI_BASE_URL}/${model}:generateContent?key=${GEMINI_API_KEY}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig: {
              temperature: 0.3,
              maxOutputTokens: 4000,
              topP: 0.8,
              topK: 40
            }
          })
        });

        if (response.ok) {
          const data = await response.json();
          const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

          if (!generatedText) {
            throw new Error('No content generated by Gemini');
          }

          console.log('📝 Generated response preview:', generatedText.substring(0, 200) + '...');

          // Extract JSON from response
          const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
            throw new Error('No valid JSON found in Gemini response');
          }

          const result = JSON.parse(jsonMatch[0]) as ModuleDetectionResult;
          const processingTime = Date.now() - startTime;

          console.log('✅ Module detection successful:', {
            totalModules: result.total_modules,
            confidence: result.ai_confidence_score,
            processingTime: `${processingTime}ms`
          });

          return result;

        } else {
          const errorText = await response.text();
          console.error(`❌ Model ${model} failed:`, response.status, errorText);
          lastError = new Error(`Gemini API error: ${response.status} - ${errorText}`);
        }

      } catch (error) {
        console.error(`❌ Model ${model} failed:`, error);
        lastError = error as Error;
      }
    }

    throw lastError || new Error('All Gemini models failed for module detection');
  }

  /**
   * Generate quiz questions for a specific module
   */
  static async generateModuleQuestions(
    request: QuestionGenerationRequest
  ): Promise<QuestionGenerationResult> {
    if (!GEMINI_API_KEY) {
      throw new Error('Gemini API key not configured');
    }

    console.log('🎯 Generating questions for module:', request.module_title);
    console.log('📊 Request details:', {
      questionCount: request.question_count,
      difficulty: request.difficulty_level,
      hasTextbook: !!request.textbook_content
    });

    const prompt = this.buildQuestionGenerationPrompt(request);
    const startTime = Date.now();
    let lastError: Error | null = null;

    // Try multiple Gemini models
    for (const model of GEMINI_MODELS) {
      try {
        console.log(`🚀 Trying Gemini model: ${model}`);

        const response = await fetch(`${GEMINI_BASE_URL}/${model}:generateContent?key=${GEMINI_API_KEY}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig: {
              temperature: 0.4,
              maxOutputTokens: 6000,
              topP: 0.9,
              topK: 40
            }
          })
        });

        if (response.ok) {
          const data = await response.json();
          const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

          if (!generatedText) {
            throw new Error('No content generated by Gemini');
          }

          console.log('📝 Generated questions preview:', generatedText.substring(0, 300) + '...');

          // Extract JSON from response
          const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
            throw new Error('No valid JSON found in Gemini response');
          }

          const result = JSON.parse(jsonMatch[0]);
          const processingTime = Date.now() - startTime;

          // Validate and format the result
          const formattedResult: QuestionGenerationResult = {
            questions: this.validateAndFormatQuestions(result.questions),
            generation_metadata: {
              total_generated: result.questions?.length || 0,
              average_confidence: this.calculateAverageConfidence(result.questions),
              processing_time_ms: processingTime,
              model_used: model,
              content_analysis: result.content_analysis || {
                key_concepts_identified: [],
                content_complexity: request.difficulty_level,
                coverage_percentage: 85
              }
            }
          };

          console.log('✅ Question generation successful:', {
            questionsGenerated: formattedResult.questions.length,
            averageConfidence: formattedResult.generation_metadata.average_confidence,
            processingTime: `${processingTime}ms`
          });

          return formattedResult;

        } else {
          const errorText = await response.text();
          console.error(`❌ Model ${model} failed:`, response.status, errorText);
          lastError = new Error(`Gemini API error: ${response.status} - ${errorText}`);
        }

      } catch (error) {
        console.error(`❌ Model ${model} failed:`, error);
        lastError = error as Error;
      }
    }

    throw lastError || new Error('All Gemini models failed for question generation');
  }

  /**
   * Build comprehensive prompt for question generation
   */
  private static buildQuestionGenerationPrompt(request: QuestionGenerationRequest): string {
    const weighting = request.content_weighting || {
      textbook_based: 80,      // MANDATORY MINIMUM 80%
      syllabus_based: 10,      // REDUCED to 10%
      cross_referenced: 5,     // REDUCED to 5%
      ai_synthesis: 5          // REDUCED to 5%
    };

    const textbookQuestions = Math.round((request.question_count * weighting.textbook_based) / 100);
    const syllabusQuestions = Math.round((request.question_count * weighting.syllabus_based) / 100);
    const crossRefQuestions = Math.round((request.question_count * weighting.cross_referenced) / 100);
    const aiSynthesisQuestions = Math.round((request.question_count * weighting.ai_synthesis) / 100);

    // Chapter-specific instructions
    const chapterInstructions = request.chapter_mapping ? `
CHAPTER-SPECIFIC REQUIREMENTS FOR TEXTBOOK QUESTIONS:
Module: ${request.chapter_mapping.module_name}
Required Chapters: ${request.chapter_mapping.chapter_references.join(', ')}
Specific Sections: ${request.chapter_mapping.textbook_sections.join(', ')}
Content Scope: ${request.chapter_mapping.content_scope}

CRITICAL: All ${textbookQuestions} textbook-based questions MUST be sourced ONLY from the specified chapters and sections above.
Do NOT generate questions from any other chapters or sections of the textbook.
Each textbook question must reference content within the defined chapter boundaries.
` : '';

    return `
You are an expert quiz creator for "${request.subject_name}". Generate ${request.question_count} high-quality multiple choice questions specifically for the module: "${request.module_title}".

MODULE CONTENT:
${request.module_content}

${request.textbook_content ? `
RELATED TEXTBOOK CONTENT:
${request.textbook_content}
` : ''}

${chapterInstructions}

CONTENT SOURCE DISTRIBUTION REQUIREMENTS (TEXTBOOK-FOCUSED):
You must distribute the ${request.question_count} questions according to these specific weightings:

1. TEXTBOOK-BASED QUESTIONS (${textbookQuestions} questions - ${weighting.textbook_based}%) - PRIMARY FOCUS:
   - Questions sourced EXCLUSIVELY from the RELATED TEXTBOOK CONTENT above
   ${request.chapter_mapping ? `- MUST be from specified chapters: ${request.chapter_mapping.chapter_references.join(', ')}
   - MUST reference content within sections: ${request.chapter_mapping.textbook_sections.join(', ')}
   - Include chapter/section numbers in question metadata for verification` : ''}
   - Include detailed explanations, examples, and technical depth from textbook
   - Test practical applications and detailed understanding from specific textbook sections
   - Each question must clearly reference textbook content within the defined boundaries

2. SYLLABUS-BASED QUESTIONS (${syllabusQuestions} questions - ${weighting.syllabus_based}%) - REDUCED:
   - Questions derived from the MODULE CONTENT above
   - Focus on learning objectives, module descriptions, and key topics
   - Test understanding of concepts as outlined in the syllabus

3. CROSS-REFERENCED QUESTIONS (${crossRefQuestions} questions - ${weighting.cross_referenced}%) - REDUCED:
   - Questions that synthesize content from BOTH syllabus and textbook materials
   - Combine concepts from multiple sources within the specified chapter boundaries
   - Test ability to connect and integrate knowledge

4. AI SYNTHESIS QUESTIONS (${aiSynthesisQuestions} questions - ${weighting.ai_synthesis}%) - MINIMAL:
   - Questions using your domain knowledge to fill gaps or provide additional context
   - Ensure they still relate to the module topic and chapter scope
   - Add value beyond the provided materials while staying within content boundaries

REQUIREMENTS:
- Generate exactly ${request.question_count} multiple choice questions
- Each question must have exactly 4 options (A, B, C, D)
- Difficulty level: ${request.difficulty_level}
- Questions should focus specifically on "${request.module_title}"
- Include clear explanations for correct answers
- Ensure questions test understanding, not just memorization
- Mix conceptual and application-based questions
- STRICTLY follow the content distribution specified above

Return ONLY a JSON object with this exact structure:
{
  "questions": [
    {
      "question_text": "What is the primary purpose of data structures?",
      "option_a": "To store data efficiently",
      "option_b": "To process data quickly",
      "option_c": "To organize and manage data effectively",
      "option_d": "To display data beautifully",
      "correct_answer": "C",
      "explanation": "Data structures are primarily designed to organize and manage data effectively, enabling efficient storage, retrieval, and manipulation.",
      "difficulty_level": "medium",
      "topic_tags": ["data structures", "fundamentals"],
      "source_reference": "Module content on data structure basics",
      "source_type": "syllabus_based",
      "chapter_reference": "Chapter 1 (1.1-1.4)",
      "section_numbers": ["1.1", "1.2"],
      "ai_confidence": 0.92
    }
  ],
  "content_analysis": {
    "key_concepts_identified": ["data structures", "algorithms", "efficiency"],
    "content_complexity": "medium",
    "coverage_percentage": 90,
    "source_distribution": {
      "syllabus_based": ${syllabusQuestions},
      "textbook_based": ${textbookQuestions},
      "cross_referenced": ${crossRefQuestions},
      "ai_synthesis": ${aiSynthesisQuestions}
    }
  }
}

CRITICAL REQUIREMENTS:
- correct_answer must be exactly "A", "B", "C", or "D"
- source_type must be exactly "syllabus_based", "textbook_based", "cross_referenced", or "ai_synthesis"
- For textbook_based questions: chapter_reference and section_numbers are MANDATORY
- chapter_reference must match the specified chapter boundaries exactly
- section_numbers must be within the allowed textbook sections
- All questions must be directly related to the provided module content
- Each question should be clear, unambiguous, and grammatically correct
- Options should be plausible but only one clearly correct
- ai_confidence should be between 0.0 and 1.0
- MUST generate the exact number of questions specified for each source type
- TEXTBOOK QUESTIONS MUST STAY WITHIN SPECIFIED CHAPTER BOUNDARIES
- Questions should be evenly distributed across the specified content sources
- Validate that textbook questions reference content only from allowed chapters/sections
`;
  }

  /**
   * Validate and format generated questions
   */
  private static validateAndFormatQuestions(questions: any[]): GeneratedQuestion[] {
    if (!Array.isArray(questions)) {
      throw new Error('Questions must be an array');
    }

    return questions.map((q, index) => {
      // Validate required fields
      if (!q.question_text || !q.option_a || !q.option_b || !q.option_c || !q.option_d || !q.correct_answer) {
        throw new Error(`Question ${index + 1} is missing required fields`);
      }

      // Validate correct answer format
      if (!['A', 'B', 'C', 'D'].includes(q.correct_answer)) {
        throw new Error(`Question ${index + 1} has invalid correct_answer: ${q.correct_answer}`);
      }

      // Validate chapter references for textbook questions
      if (q.source_type === 'textbook_based') {
        if (!q.chapter_reference) {
          console.warn(`Question ${index + 1}: Textbook question missing chapter_reference`);
        }
        if (!q.section_numbers || !Array.isArray(q.section_numbers)) {
          console.warn(`Question ${index + 1}: Textbook question missing section_numbers`);
        }
      }

      return {
        question_text: q.question_text.trim(),
        option_a: q.option_a.trim(),
        option_b: q.option_b.trim(),
        option_c: q.option_c.trim(),
        option_d: q.option_d.trim(),
        correct_answer: q.correct_answer as 'A' | 'B' | 'C' | 'D',
        explanation: q.explanation?.trim() || '',
        difficulty_level: q.difficulty_level || 'medium',
        topic_tags: Array.isArray(q.topic_tags) ? q.topic_tags : [],
        source_reference: q.source_reference || 'Module content',
        source_type: q.source_type || 'syllabus_based',
        chapter_reference: q.chapter_reference || undefined,
        section_numbers: Array.isArray(q.section_numbers) ? q.section_numbers : undefined,
        ai_confidence: Math.min(Math.max(q.ai_confidence || 0.8, 0), 1)
      };
    });
  }

  /**
   * Calculate average confidence score
   */
  private static calculateAverageConfidence(questions: any[]): number {
    if (!questions || questions.length === 0) return 0;

    const total = questions.reduce((sum, q) => sum + (q.ai_confidence || 0.8), 0);
    return Math.round((total / questions.length) * 100) / 100;
  }
}
