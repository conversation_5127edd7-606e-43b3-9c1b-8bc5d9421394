import { supabase } from '@/integrations/supabase/client';

export interface StudentBasicInfo {
  id: string; // Add student ID
  usn: string;
  student_name: string;
  email?: string;
  student_mobile?: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
}

export interface AttendanceSummary {
  overall_percentage: number;
  total_classes: number;
  attended_classes: number;
  subject_wise_attendance: SubjectAttendance[];
  date_range: {
    from: string;
    to: string;
  };
}

export interface SubjectAttendance {
  subject_code: string;
  subject_name?: string;
  total_classes: number;
  attended_classes: number;
  percentage: number;
  recent_records: AttendanceRecord[];
}

export interface AttendanceRecord {
  date: string;
  status: 'present' | 'absent';
  time_slot?: string;
  period_number?: number;
}

export interface IAMarks {
  subject_code: string;
  subject_name?: string;
  ia1_marks?: number;
  ia1_date?: string;
  ia2_marks?: number;
  ia2_date?: string;
  ia3_marks?: number;
  ia3_date?: string;
  assignment_marks?: number;
  theory_marks?: number; // Changed from lab_marks to match database schema
  total_marks: number;
  average_marks: number;
  grade?: string;
}

export interface StudentProgressCard {
  basic_info: StudentBasicInfo;
  attendance: AttendanceSummary;
  ia_marks: IAMarks[];
  overall_performance: {
    attendance_status: 'good' | 'warning' | 'critical';
    ia_status: 'excellent' | 'good' | 'average' | 'poor';
    overall_grade: string;
  };
}

export class StudentProgressService {
  /**
   * Lab subject mappings for proper display names
   * Only used for lab subjects with batch suffixes (e.g., BCS402_A1)
   * Maps base subject codes to their lab names
   */
  private static readonly LAB_SUBJECT_MAPPINGS: Record<string, string> = {
    'BCS403': 'DBMS Lab',    // DBMS Lab
    'BCS402': 'MC Lab',      // MC Lab
    'BCS401': 'ADA Lab',     // ADA Lab
    'BCS404': 'CN Lab',      // CN Lab
    'BCS405': 'Software Engineering Lab',  // SE Lab
    'BCSL403': 'DBMS Lab',   // DBMS Lab (alternative code)
    'BCSL402': 'MC Lab',     // MC Lab (alternative code)
    'BCSL401': 'ADA Lab',    // ADA Lab (alternative code)
    'BCSL404': 'CN Lab',     // CN Lab (alternative code)
    'BCSL405': 'Software Engineering Lab',  // SE Lab (alternative code)
  };

  /**
   * Detect if a subject code has a batch suffix (e.g., "_A1", "_B2")
   * Returns the base subject code and batch identifier if found
   */
  private static detectBatchSuffix(subjectCode: string): { baseCode: string; batchSuffix?: string; isLabBatch: boolean } {
    if (!subjectCode) return { baseCode: subjectCode, isLabBatch: false };

    // Pattern for batch suffixes: _A1, _A2, _B1, _B2, etc.
    const batchPattern = /^(.+)_([A-Z]\d+)$/;
    const match = subjectCode.match(batchPattern);

    if (match) {
      const [, baseCode, batchSuffix] = match;
      return {
        baseCode,
        batchSuffix,
        isLabBatch: true
      };
    }

    return { baseCode: subjectCode, isLabBatch: false };
  }

  /**
   * Normalize subject codes to handle variations like BCS401_THEORY -> BCS401
   * Enhanced to also handle batch suffixes like BCS402_A1 -> BCS402
   * This ensures consistency between IA entry and progress display
   */
  private static normalizeSubjectCode(subjectCode: string): string {
    if (!subjectCode) return subjectCode;

    // First check for batch suffixes
    const { baseCode, isLabBatch } = this.detectBatchSuffix(subjectCode);
    if (isLabBatch) {
      return baseCode;
    }

    // Remove common suffixes used in attendance/IA systems
    return subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
  }

  /**
   * Get the appropriate subject name for a subject code, handling lab batches
   */
  private static getSubjectDisplayName(subjectCode: string, originalSubjectName?: string): string {
    const { baseCode, batchSuffix, isLabBatch } = this.detectBatchSuffix(subjectCode);

    // For lab batches (with batch suffixes like _A1, _B2), always use lab mappings
    if (isLabBatch) {
      const labName = this.LAB_SUBJECT_MAPPINGS[baseCode];
      if (labName) {
        return batchSuffix ? `${labName} (${batchSuffix})` : labName;
      }
    }

    // For subjects with explicit _LAB suffix, use lab mappings
    if (subjectCode.includes('_LAB')) {
      const normalizedCode = this.normalizeSubjectCode(subjectCode);
      const labName = this.LAB_SUBJECT_MAPPINGS[normalizedCode];
      if (labName) {
        return labName;
      }
    }

    // For BCSL prefixed subjects (clear lab indicators), use lab mappings only if unknown
    if (subjectCode.startsWith('BCSL') && (!originalSubjectName || originalSubjectName === 'Unknown Subject')) {
      const labName = this.LAB_SUBJECT_MAPPINGS[subjectCode];
      if (labName) {
        return labName;
      }
    }

    return originalSubjectName || 'Unknown Subject';
  }

  /**
   * Get all possible subject code variations for a base subject code
   * Enhanced to include batch suffix variations for lab subjects
   * Used when querying attendance data that might use suffixed codes
   */
  private static getSubjectCodeVariations(baseSubjectCode: string): string[] {
    const variations = [
      baseSubjectCode,
      `${baseSubjectCode}_THEORY`,
      `${baseSubjectCode}_LAB`,
      `${baseSubjectCode}_PRACTICAL`,
      `${baseSubjectCode}_TUTORIAL`
    ];

    // Add common batch suffix variations for lab subjects
    const batchSuffixes = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
    batchSuffixes.forEach(suffix => {
      variations.push(`${baseSubjectCode}_${suffix}`);
    });

    return variations;
  }
  /**
   * Filter out skill lab and tutorial subjects
   */
  private static filterSubjects(subjects: { subject_code: string; subject_name: string }[]): { subject_code: string; subject_name: string }[] {
    return subjects.filter(subject => {
      const subjectName = (subject.subject_name || '').toLowerCase();
      const subjectCode = (subject.subject_code || '').toLowerCase();

      // Filter out skill lab and tutorial subjects
      return !subjectName.includes('skill lab') &&
             !subjectName.includes('tutorial') &&
             !subjectCode.includes('skill') &&
             !subjectCode.includes('tutorial');
    });
  }

  /**
   * Get comprehensive progress card for a student
   */
  static async getStudentProgressCard(
    studentUsn: string,
    academicYear: string = '2025-2026'
  ): Promise<StudentProgressCard> {
    try {
      // Get basic student info
      const basicInfo = await this.getStudentBasicInfo(studentUsn);

      // Get attendance summary
      const attendance = await this.getStudentAttendanceSummary(
        studentUsn,
        basicInfo.department,
        basicInfo.semester,
        basicInfo.section,
        academicYear
      );

      // Get IA marks
      const iaMarks = await this.getStudentIAMarks(
        studentUsn,
        basicInfo.department,
        basicInfo.semester,
        basicInfo.section,
        academicYear
      );

      // Calculate overall performance
      const overallPerformance = this.calculateOverallPerformance(attendance, iaMarks);

      return {
        basic_info: basicInfo,
        attendance,
        ia_marks: iaMarks,
        overall_performance: overallPerformance
      };
    } catch (error) {
      console.error('Error getting student progress card:', error);
      throw error;
    }
  }

  /**
   * Get basic student information
   */
  private static async getStudentBasicInfo(studentUsn: string): Promise<StudentBasicInfo> {
    try {
      console.log('👤 Fetching basic info for student:', studentUsn);

      const { data, error } = await supabase
        .from('class_students')
        .select('id, usn, student_name, email, student_mobile, department, semester, section, academic_year')
        .eq('usn', studentUsn.toUpperCase())
        .single();

      if (error) {
        console.error('❌ Error fetching student basic info:', error.message);
        throw new Error(`Student with USN ${studentUsn} not found: ${error.message}`);
      }

      if (!data) {
        console.error('❌ No student data found for USN:', studentUsn);
        throw new Error(`Student with USN ${studentUsn} not found`);
      }

      console.log('✅ Found student:', data.student_name);

      return {
        id: data.id,
        usn: data.usn,
        student_name: data.student_name,
        email: data.email,
        student_mobile: data.student_mobile,
        department: data.department,
        semester: data.semester,
        section: data.section,
        academic_year: data.academic_year || '2024-25'
      };
    } catch (error) {
      console.error('Error getting student basic info:', error);
      throw error;
    }
  }

  /**
   * Get student attendance summary across all subjects
   */
  private static async getStudentAttendanceSummary(
    studentUsn: string,
    department: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<AttendanceSummary> {
    try {
      console.log('📊 Fetching attendance for student:', { studentUsn, department, semester, section });

      let allRecords: any[] = [];

      // First, get all subjects for this student's class to fetch attendance for each
      let studentSubjects: { subject_code: string; subject_name: string }[] = [];

      try {
        console.log('🔍 Querying timetable_slots with:', { department, semester, section });

        // Try case-insensitive department matching for attendance subjects
        let subjectsData = null;
        let subjectsError = null;

        // First try exact match
        const { data: exactData, error: exactError } = await supabase
          .from('timetable_slots')
          .select('subject_code, subject_name')
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section);

        if (!exactError && exactData && exactData.length > 0) {
          subjectsData = exactData;
          subjectsError = exactError;
        } else {
          // Try lowercase department
          const { data: lowerData, error: lowerError } = await supabase
            .from('timetable_slots')
            .select('subject_code, subject_name')
            .eq('department', department.toLowerCase())
            .eq('semester', semester)
            .eq('section', section);

          if (!lowerError && lowerData && lowerData.length > 0) {
            subjectsData = lowerData;
            subjectsError = lowerError;
          } else {
            // Try uppercase department
            const { data: upperData, error: upperError } = await supabase
              .from('timetable_slots')
              .select('subject_code, subject_name')
              .eq('department', department.toUpperCase())
              .eq('semester', semester)
              .eq('section', section);

            subjectsData = upperData;
            subjectsError = upperError;
          }
        }

        console.log('📊 Timetable query result:', {
          error: subjectsError?.message,
          dataLength: subjectsData?.length,
          sampleData: subjectsData?.slice(0, 3)
        });

        if (subjectsError) {
          console.log('❌ Error fetching subjects:', subjectsError.message);
        } else if (subjectsData && subjectsData.length > 0) {
          console.log(`✅ Found ${subjectsData.length} timetable entries for the class`);

          // Get unique subjects and filter out skill lab and tutorial
          const allSubjects = Array.from(
            new Map(subjectsData.map(s => [s.subject_code, s])).values()
          );

          studentSubjects = this.filterSubjects(allSubjects);

          console.log(`📚 Filtered subjects for student (${studentSubjects.length}/${allSubjects.length}): ${studentSubjects.map(s => s.subject_code).join(', ')}`);

          // Fetch attendance for each subject from actual attendance tables
          for (const subject of studentSubjects) {
            try {
              console.log(`🔍 Fetching attendance for subject: ${subject.subject_code}`);
              console.log(`🔍 Query parameters: student_usn=${studentUsn.toUpperCase()}, subject_identifier=${subject.subject_code}, department=${department.toLowerCase()}, semester=${semester}, section=${section}`);

              // Try usn_attendance table first
              let attendanceData: any[] = [];

              // Try multiple subject identifier variations
              const subjectVariations = [
                subject.subject_code,
                `${subject.subject_code}_THEORY`,
                `${subject.subject_code}_LAB`,
                `${subject.subject_code}_PRACTICAL`
              ];

              let usnAttendanceData: any[] = [];
              let usnAttendanceError: any = null;

              for (const subjectVariation of subjectVariations) {
                const { data, error } = await supabase
                  .from('usn_attendance')
                  .select('*')
                  .eq('student_usn', studentUsn.toUpperCase())
                  .eq('subject_identifier', subjectVariation)
                  .eq('department', department.toLowerCase())
                  .eq('semester', semester)
                  .eq('section', section);

                if (!error && data && data.length > 0) {
                  console.log(`✅ Found ${data.length} records with subject variation: ${subjectVariation}`);
                  usnAttendanceData = [...usnAttendanceData, ...data];
                }
              }

              if (!usnAttendanceError && usnAttendanceData && usnAttendanceData.length > 0) {
                console.log(`✅ Found ${usnAttendanceData.length} records in usn_attendance for ${subject.subject_code}`);
                attendanceData = usnAttendanceData.map(record => ({
                  ...record,
                  subject_code: subject.subject_code,
                  subject_name: subject.subject_name || 'Unknown Subject',
                  student_usn: studentUsn.toUpperCase(),
                  attendance_date: record.attendance_date || record.date,
                  status: record.status
                }));
                console.log(`📝 Subject-specific attendance data sample for ${subject.subject_code}:`, attendanceData[0]);
              } else {
                // Try student_attendance table as fallback (but this table likely doesn't exist)
                console.log(`📝 No attendance data found for ${subject.subject_code} in usn_attendance table`);
                // Skip student_attendance table query as it's causing 400 errors

                if (!studentAttendanceError && studentAttendanceData && studentAttendanceData.length > 0) {
                  console.log(`✅ Found ${studentAttendanceData.length} records in student_attendance for ${subject.subject_code}`);
                  attendanceData = studentAttendanceData.map(record => ({
                    ...record,
                    subject_code: subject.subject_code,
                    subject_name: subject.subject_name,
                    student_usn: studentUsn.toUpperCase(),
                    attendance_date: record.date,
                    status: record.status
                  }));
                } else {
                  console.log(`📝 No attendance data found for ${subject.subject_code} in either table`);
                }
              }

              if (attendanceData.length > 0) {
                allRecords = [...allRecords, ...attendanceData];
              }
            } catch (error) {
              console.log(`❌ Exception fetching attendance for ${subject.subject_code}:`, error);
            }
          }
        } else {
          console.log('❌ No subjects found in timetable_slots for this class');

          // Try alternative queries with different department formats
          const departmentVariations = [
            department,
            department.toUpperCase(),
            department.toLowerCase(),
            'Computer Science and Engineering',
            'CSE',
            'cse'
          ];

          for (const deptVariant of departmentVariations) {
            console.log(`🔍 Trying department variant: "${deptVariant}"`);

            const { data: altData, error: altError } = await supabase
              .from('timetable_slots')
              .select('subject_code, subject_name')
              .eq('department', deptVariant)
              .eq('semester', semester)
              .eq('section', section)
              .limit(20);

            if (!altError && altData && altData.length > 0) {
              console.log(`✅ Found ${altData.length} subjects with department variant "${deptVariant}"`);

              // Filter out skill lab and tutorial subjects
              const allAltSubjects = Array.from(
                new Map(altData.map(s => [s.subject_code, s])).values()
              );

              studentSubjects = this.filterSubjects(allAltSubjects);

              console.log(`📚 Filtered subjects found (${studentSubjects.length}/${allAltSubjects.length}): ${studentSubjects.map(s => s.subject_code).join(', ')}`);

              if (studentSubjects.length > 0) {
                break;
              }
            } else {
              console.log(`❌ No subjects found with department variant "${deptVariant}"`);
            }
          }

          // If still no subjects found, let's see what's in the timetable_slots table
          if (studentSubjects.length === 0) {
            const { data: sampleData, error: sampleError } = await supabase
              .from('timetable_slots')
              .select('department, semester, section, subject_code, subject_name')
              .limit(10);

            console.log('🔍 Sample timetable_slots data for debugging:', {
              error: sampleError?.message,
              sampleData: sampleData
            });
          }
        }
      } catch (error) {
        console.log('❌ Exception fetching subjects:', error);
      }

      // Fallback: Try direct attendance table access if no subject-specific data found
      if (allRecords.length === 0) {
        console.log('🔄 No subject-specific attendance found, trying direct table queries...');

        try {
          // Try usn_attendance table
          const { data: usnAttendanceData, error: usnError } = await supabase
            .from('usn_attendance')
            .select('*')
            .eq('student_usn', studentUsn.toUpperCase())
            .eq('department', department.toLowerCase())
            .eq('semester', semester)
            .eq('section', section);

          if (!usnError && usnAttendanceData && usnAttendanceData.length > 0) {
            console.log(`✅ Found ${usnAttendanceData.length} records in usn_attendance (direct query)`);

            // Transform and enrich attendance data with subject names
            const enrichedData = usnAttendanceData.map(record => {
              // Find subject name from studentSubjects with flexible matching
              let subjectInfo = studentSubjects.find(s => s.subject_code === record.subject_identifier);

              // If not found, try matching with normalized subject codes (handles batch suffixes and other variations)
              if (!subjectInfo) {
                const normalizedSubjectCode = this.normalizeSubjectCode(record.subject_identifier);
                subjectInfo = studentSubjects.find(s => s.subject_code === normalizedSubjectCode);
                console.log(`🔍 Trying normalized code match: ${record.subject_identifier} -> ${normalizedSubjectCode}, found:`, subjectInfo);
              }

              // If still not found, try matching without common suffixes (legacy support)
              if (!subjectInfo) {
                const baseSubjectCode = record.subject_identifier.replace(/_THEORY|_LAB|_PRACTICAL/g, '');
                subjectInfo = studentSubjects.find(s => s.subject_code === baseSubjectCode);
                console.log(`🔍 Trying legacy base code match: ${record.subject_identifier} -> ${baseSubjectCode}, found:`, subjectInfo);
              }

              console.log(`🔍 Enriching ${record.subject_identifier}: found subject info:`, subjectInfo);

              // Use the normalized subject code for consistency
              const finalSubjectCode = subjectInfo?.subject_code || this.normalizeSubjectCode(record.subject_identifier);

              // Get the appropriate display name, handling lab batches
              const displayName = this.getSubjectDisplayName(
                record.subject_identifier,
                subjectInfo?.subject_name || record.subject_name
              );

              return {
                ...record,
                subject_code: finalSubjectCode,
                subject_name: displayName,
                student_usn: record.student_usn,
                attendance_date: record.attendance_date,
                status: record.status
              };
            });

            console.log(`📝 Enriched attendance data sample:`, enrichedData[0]);
            allRecords = [...allRecords, ...enrichedData];
          } else {
            console.log('❌ No data in usn_attendance, trying student_attendance...');

            // Skip student_attendance table as it's causing 400 errors and likely doesn't exist
            console.log('📝 Skipping student_attendance table (not available in this database)');

            // Final check - if still no data, show what tables exist
            if (allRecords.length === 0) {
              console.log('❌ No attendance data found in any table');
              console.log('💡 To populate attendance data, you need records in either:');
              console.log('   • usn_attendance table with columns: student_usn, subject_code, department, semester, section, attendance_date, status');
              console.log('   • student_attendance table with columns: student_id, subject_id, date, status');
            }
          }
        } catch (error) {
          console.log('❌ Exception accessing attendance tables:', error);
        }
      }

      console.log(`📊 Processing ${allRecords.length} attendance records`);

      // Process attendance data
      let subjectWiseAttendance = this.processAttendanceRecords(allRecords);

      // If no attendance data found, create placeholder records for all student subjects
      if (subjectWiseAttendance.length === 0 && studentSubjects.length > 0) {
        console.log(`📚 Creating placeholder attendance records for ${studentSubjects.length} subjects`);
        subjectWiseAttendance = studentSubjects.map(subject => ({
          subject_code: subject.subject_code,
          subject_name: subject.subject_name,
          total_classes: 0,
          attended_classes: 0,
          percentage: 0,
          recent_records: []
        }));
      } else if (subjectWiseAttendance.length > 0 && studentSubjects.length > 0) {
        // Ensure all student subjects are represented, even if they have no attendance data
        const existingSubjectCodes = new Set(subjectWiseAttendance.map(s => s.subject_code));
        const missingSubjects = studentSubjects.filter(s => !existingSubjectCodes.has(s.subject_code));

        if (missingSubjects.length > 0) {
          console.log(`📚 Adding ${missingSubjects.length} missing subjects with no attendance data`);
          const placeholderSubjects = missingSubjects.map(subject => ({
            subject_code: subject.subject_code,
            subject_name: subject.subject_name,
            total_classes: 0,
            attended_classes: 0,
            percentage: 0,
            recent_records: []
          }));
          subjectWiseAttendance = [...subjectWiseAttendance, ...placeholderSubjects];
        }
      }

      // Calculate overall statistics
      const totalClasses = subjectWiseAttendance.reduce((sum, subject) => sum + subject.total_classes, 0);
      const attendedClasses = subjectWiseAttendance.reduce((sum, subject) => sum + subject.attended_classes, 0);
      const overallPercentage = totalClasses > 0 ? (attendedClasses / totalClasses) * 100 : 0;

      // Get date range
      const dates = allRecords.map(record => record.attendance_date || record.date).filter(Boolean);
      const dateRange = {
        from: dates.length > 0 ? Math.min(...dates.map(d => new Date(d).getTime())) : new Date().getTime(),
        to: dates.length > 0 ? Math.max(...dates.map(d => new Date(d).getTime())) : new Date().getTime()
      };

      console.log(`📊 Attendance summary: ${attendedClasses}/${totalClasses} classes (${overallPercentage.toFixed(1)}%)`);

      return {
        overall_percentage: Math.round(overallPercentage * 100) / 100,
        total_classes: totalClasses,
        attended_classes: attendedClasses,
        subject_wise_attendance: subjectWiseAttendance,
        date_range: {
          from: new Date(dateRange.from).toISOString().split('T')[0],
          to: new Date(dateRange.to).toISOString().split('T')[0]
        }
      };
    } catch (error) {
      console.error('Error getting student attendance summary:', error);
      // Return empty summary if no data found
      return {
        overall_percentage: 0,
        total_classes: 0,
        attended_classes: 0,
        subject_wise_attendance: [],
        date_range: {
          from: new Date().toISOString().split('T')[0],
          to: new Date().toISOString().split('T')[0]
        }
      };
    }
  }

  /**
   * Process attendance records to get subject-wise summary
   */
  private static processAttendanceRecords(records: any[]): SubjectAttendance[] {
    const subjectMap = new Map<string, {
      total: number;
      attended: number;
      records: AttendanceRecord[];
      subject_name?: string;
    }>();

    records.forEach(record => {
      const subjectCode = record.subject_code;
      if (!subjectCode) return;

      if (!subjectMap.has(subjectCode)) {
        subjectMap.set(subjectCode, {
          total: 0,
          attended: 0,
          records: [],
          subject_name: record.subject_name
        });
      }

      const subject = subjectMap.get(subjectCode)!;
      subject.total++;

      if (record.status === 'present') {
        subject.attended++;
      }

      subject.records.push({
        date: record.attendance_date || record.date,
        status: record.status,
        time_slot: record.time_slot,
        period_number: record.period_number
      });
    });

    return Array.from(subjectMap.entries()).map(([subjectCode, data]) => ({
      subject_code: subjectCode,
      subject_name: data.subject_name,
      total_classes: data.total,
      attended_classes: data.attended,
      percentage: data.total > 0 ? Math.round((data.attended / data.total) * 10000) / 100 : 0,
      recent_records: data.records.slice(-10) // Last 10 records
    }));
  }

  /**
   * Get student IA marks across all subjects
   */
  private static async getStudentIAMarks(
    studentUsn: string,
    department: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<IAMarks[]> {
    try {
      console.log('📚 Fetching IA marks for student:', { studentUsn, department, semester, section, academicYear });

      // Check if internal_assessments table has data
      console.log('🔍 Checking internal_assessments table for IA marks...');

      // Get student ID(s) from class_students - handle multiple records
      const { data: studentRecords, error: studentError } = await supabase
        .from('class_students')
        .select('id, usn, student_name, department, semester, section, academic_year')
        .eq('usn', studentUsn.toUpperCase());

      if (studentError) {
        console.log('❌ Error finding student in class_students:', studentError.message);
        return [];
      }

      if (!studentRecords || studentRecords.length === 0) {
        console.log('❌ Student not found in class_students table');
        return [];
      }

      console.log(`✅ Found ${studentRecords.length} student record(s) for USN ${studentUsn}`);
      studentRecords.forEach((record, index) => {
        console.log(`📋 Student Record ${index + 1}: ID=${record.id}, Dept=${record.department}, Sem=${record.semester}, Sec=${record.section}, AY=${record.academic_year}`);
      });

      // Use the most recent student record or the one that matches the query parameters
      let selectedStudent = studentRecords[0]; // Default to first record

      // Try to find a student record that matches the department/semester/section being queried
      const matchingStudent = studentRecords.find(record =>
        record.department.toLowerCase() === department.toLowerCase() &&
        record.semester === semester &&
        record.section === section
      );

      if (matchingStudent) {
        selectedStudent = matchingStudent;
        console.log(`✅ Using matching student record: ID=${selectedStudent.id}`);
      } else {
        console.log(`⚠️ No exact match found, using first record: ID=${selectedStudent.id}`);
      }

      const studentData = { id: selectedStudent.id };

      console.log('✅ Found student ID:', studentData.id);
      console.log(`🔍 IA Query parameters: student_id=${studentData.id}, department=${department.toLowerCase()}, semester=${semester}, academic_year=${academicYear}`);
      console.log(`🔍 Original department parameter: "${department}" -> lowercase: "${department.toLowerCase()}"`);

      // Enhanced IA marks fetching with real-time synchronization
      // Read from BOTH IA tables to ensure complete data coverage
      let data: any[] = [];
      let individualIAData: any[] = [];

      // First try with all filters (use lowercase department)
      // Try multiple academic year formats to handle variations
      const currentYear = new Date().getFullYear();
      const academicYearVariations = [
        academicYear,           // e.g., "2024-2025" (student's academic year)
        `${currentYear}-${currentYear + 1}`, // e.g., "2025-2026" (Sequential IA format - current year)
        "2025-2026",           // Explicit current academic year (where IA data is stored)
        "2024-25",             // Previous short format
        "2024-2025",           // Previous long format
        `${currentYear-1}-${currentYear}`,   // e.g., "2024-2025" (previous year)
        // Handle the specific mismatch: if student is in 2024-2025, IA might be in 2025-2026
        academicYear === "2024-2025" ? "2025-2026" : academicYear,
        academicYear === "2024-25" ? "2025-26" : academicYear,
        // Convert between formats
        academicYear.includes('-') ? academicYear.split('-')[0] + '-' + (parseInt(academicYear.split('-')[0]) + 1) : academicYear, // Convert short to long format
        academicYear.includes('-') && academicYear.split('-')[1].length === 4 ? academicYear.split('-')[0] + '-' + academicYear.split('-')[1].slice(-2) : academicYear, // Convert long to short format
      ];

      // Remove duplicates
      const uniqueYearVariations = [...new Set(academicYearVariations)];
      console.log(`🔍 Trying academic year variations: ${uniqueYearVariations.join(', ')}`);

      let iaData1 = null;
      let iaError1 = null;

      for (const yearVariant of uniqueYearVariations) {
        const { data: tempData, error: tempError } = await supabase
          .from('internal_assessments')
          .select('*')
          .eq('student_id', studentData.id)
          .eq('department', department.toLowerCase())
          .eq('semester', semester)
          .eq('academic_year', yearVariant)
          .order('updated_at', { ascending: false });

        if (!tempError && tempData && tempData.length > 0) {
          iaData1 = tempData;
          iaError1 = tempError;
          console.log(`✅ Found IA records with academic year variant: "${yearVariant}"`);
          console.log(`📊 IA records found: ${tempData.length}`);
          console.log(`📋 Subject codes: ${tempData.map(r => r.subject_code).join(', ')}`);
          break;
        } else if (tempError) {
          console.log(`❌ Error with academic year variant "${yearVariant}":`, tempError.message);
        } else {
          console.log(`📝 No records found with academic year variant: "${yearVariant}"`);
        }
      }

      if (!iaError1 && iaData1 && iaData1.length > 0) {
        console.log(`✅ Found ${iaData1.length} IA records from internal_assessments table`);
        data = iaData1;
      } else {
        console.log('❌ No IA records with full filters, trying with student_id only...');

        // Try with just student_id for broader search - check ALL student IDs for this USN
        let allIAData: any[] = [];

        for (const studentRecord of studentRecords) {
          const { data: iaData2, error: iaError2 } = await supabase
            .from('internal_assessments')
            .select('*')
            .eq('student_id', studentRecord.id)
            .order('updated_at', { ascending: false });

          if (!iaError2 && iaData2 && iaData2.length > 0) {
            console.log(`✅ Found ${iaData2.length} IA records for student ID: ${studentRecord.id}`);
            allIAData = [...allIAData, ...iaData2];
          } else {
            console.log(`📝 No IA records found for student ID: ${studentRecord.id}`);
          }
        }

        if (allIAData.length > 0) {
          console.log(`✅ Found ${allIAData.length} total IA records across all student IDs`);
          // Filter by department and semester in code with flexible matching
          data = allIAData.filter(record => {
            const recordDept = (record.department || '').toLowerCase();
            const targetDept = department.toLowerCase();
            const deptMatch = recordDept === targetDept ||
                             recordDept === department ||
                             recordDept === department.toUpperCase();
            const semesterMatch = record.semester === semester;

            console.log(`🔍 Filtering record: ${record.subject_code} - dept: "${recordDept}" vs "${targetDept}" (${deptMatch}), semester: ${record.semester} vs ${semester} (${semesterMatch})`);

            return deptMatch && semesterMatch;
          });
          console.log(`✅ After filtering: ${data.length} matching records`);
        } else {
          console.log('❌ No IA records found for this student');

          // Debug: Check what's in the internal_assessments table
          const { data: sampleIA, error: sampleError } = await supabase
            .from('internal_assessments')
            .select('student_id, department, semester, academic_year, subject_code, updated_at, ia1_marks, ia2_marks, ia3_marks')
            .order('updated_at', { ascending: false })
            .limit(10);

          console.log('🔍 Sample internal_assessments data:', {
            error: sampleError?.message,
            sampleData: sampleIA,
            lookingFor: { student_id: studentData.id, department: department.toLowerCase(), semester, academicYear }
          });

          // Additional debug: Check for this specific student
          const { data: studentSpecificIA, error: studentSpecificError } = await supabase
            .from('internal_assessments')
            .select('*')
            .eq('student_id', studentData.id)
            .limit(5);

          console.log('🔍 Records for this specific student:', {
            error: studentSpecificError?.message,
            count: studentSpecificIA?.length || 0,
            sampleRecord: studentSpecificIA?.[0]
          });

          // Check with different department variations
          const departmentVariations = ['cse', 'CSE', 'Cse'];
          for (const deptVariation of departmentVariations) {
            const { data: deptData, error: deptError } = await supabase
              .from('internal_assessments')
              .select('student_id, department, semester, subject_code')
              .eq('student_id', studentData.id)
              .eq('department', deptVariation)
              .limit(3);

            if (!deptError && deptData && deptData.length > 0) {
              console.log(`✅ Found ${deptData.length} records with department: "${deptVariation}"`);
              console.log('📝 Sample record:', deptData[0]);
            } else {
              console.log(`❌ No records found with department: "${deptVariation}"`);
            }
          }

          console.log('💡 To populate IA marks data, you need records in internal_assessments table with columns:');
          console.log('   • student_id (UUID from class_students table)');
          console.log('   • subject_code (e.g., BCS401, BCS402, etc.)');
          console.log('   • department (lowercase, e.g., "cse")');
          console.log('   • semester, academic_year');
          console.log('   • ia1_marks, ia2_marks, ia3_marks, assignment_marks, lab_marks');
        }
      }

      // Note: individual_ia_records table doesn't exist in this database
      // All IA data is stored in internal_assessments table

      console.log(`✅ Final IA records count: ${data?.length || 0}`);

      // Get all subjects for this student's class to ensure we show all subjects
      let allStudentSubjects: { subject_code: string; subject_name: string }[] = [];
      try {
        console.log('🔍 Fetching subjects for IA processing with:', { department, semester, section });

        // Try case-insensitive department matching
        let subjectsData = null;
        let subjectsError = null;

        // First try exact match
        const { data: exactData, error: exactError } = await supabase
          .from('timetable_slots')
          .select('subject_code, subject_name')
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section);

        if (!exactError && exactData && exactData.length > 0) {
          subjectsData = exactData;
          subjectsError = exactError;
        } else {
          // Try lowercase department
          const { data: lowerData, error: lowerError } = await supabase
            .from('timetable_slots')
            .select('subject_code, subject_name')
            .eq('department', department.toLowerCase())
            .eq('semester', semester)
            .eq('section', section);

          if (!lowerError && lowerData && lowerData.length > 0) {
            subjectsData = lowerData;
            subjectsError = lowerError;
          } else {
            // Try uppercase department
            const { data: upperData, error: upperError } = await supabase
              .from('timetable_slots')
              .select('subject_code, subject_name')
              .eq('department', department.toUpperCase())
              .eq('semester', semester)
              .eq('section', section);

            subjectsData = upperData;
            subjectsError = upperError;
          }
        }

        console.log('📊 IA subjects query result:', {
          error: subjectsError?.message,
          dataLength: subjectsData?.length,
          sampleData: subjectsData?.slice(0, 3)
        });

        if (!subjectsError && subjectsData && subjectsData.length > 0) {
          const allIASubjects = Array.from(
            new Map(subjectsData.map(s => [s.subject_code, s])).values()
          );

          // Filter out skill lab and tutorial subjects
          allStudentSubjects = this.filterSubjects(allIASubjects);

          console.log(`📚 Found ${allStudentSubjects.length}/${allIASubjects.length} subjects for IA processing (filtered): ${allStudentSubjects.map(s => s.subject_code).join(', ')}`);
        } else {
          console.log('❌ No subjects found for IA processing');
        }
      } catch (error) {
        console.log('❌ Error fetching subjects for IA processing:', error);
      }

      // Process existing IA records with enhanced subject name enrichment
      const iaRecords = (data || []).map(record => {
        // Normalize subject code to handle variations
        const normalizedSubjectCode = this.normalizeSubjectCode(record.subject_code);

        // Find subject name from timetable subjects
        const subjectInfo = allStudentSubjects.find(s => s.subject_code === normalizedSubjectCode);

        // Get the appropriate display name, handling lab batches
        const displayName = this.getSubjectDisplayName(
          record.subject_code,
          subjectInfo?.subject_name || record.subject_name
        );

        // Calculate total and average marks (only count non-null values)
        const ia1 = record.ia1_marks !== null && record.ia1_marks !== undefined ? Number(record.ia1_marks) : 0;
        const ia2 = record.ia2_marks !== null && record.ia2_marks !== undefined ? Number(record.ia2_marks) : 0;
        const ia3 = record.ia3_marks !== null && record.ia3_marks !== undefined ? Number(record.ia3_marks) : 0;
        const assignment = record.assignment_marks !== null && record.assignment_marks !== undefined ? Number(record.assignment_marks) : 0;
        const theory = record.theory_marks !== null && record.theory_marks !== undefined ? Number(record.theory_marks) : 0;

        const totalMarks = ia1 + ia2 + ia3 + assignment + theory;
        const iaCount = [record.ia1_marks, record.ia2_marks, record.ia3_marks].filter(mark => mark !== null && mark !== undefined).length;
        const averageMarks = iaCount > 0 ? (ia1 + ia2 + ia3) / iaCount : 0;

        console.log(`📝 Processing IA record: ${record.subject_code} -> ${normalizedSubjectCode}, subject_name: ${displayName}`);

        return {
          subject_code: normalizedSubjectCode, // Use normalized code for consistency
          subject_name: displayName,
          ia1_marks: record.ia1_marks !== null && record.ia1_marks !== undefined && !isNaN(Number(record.ia1_marks)) ? Number(record.ia1_marks) : undefined,
          ia1_date: record.ia1_date,
          ia2_marks: record.ia2_marks !== null && record.ia2_marks !== undefined && !isNaN(Number(record.ia2_marks)) ? Number(record.ia2_marks) : undefined,
          ia2_date: record.ia2_date,
          ia3_marks: record.ia3_marks !== null && record.ia3_marks !== undefined && !isNaN(Number(record.ia3_marks)) ? Number(record.ia3_marks) : undefined,
          ia3_date: record.ia3_date,
          assignment_marks: record.assignment_marks !== null && record.assignment_marks !== undefined && !isNaN(Number(record.assignment_marks)) ? Number(record.assignment_marks) : undefined,
          theory_marks: record.theory_marks !== null && record.theory_marks !== undefined && !isNaN(Number(record.theory_marks)) ? Number(record.theory_marks) : undefined,
          total_marks: totalMarks,
          average_marks: Math.round(averageMarks * 100) / 100,
          grade: this.calculateGrade(averageMarks),
          last_updated: record.updated_at // Track when marks were last updated
        };
      });

      // Add placeholder records for subjects without IA data
      if (allStudentSubjects.length > 0) {
        const existingIASubjects = new Set(iaRecords.map(ia => ia.subject_code));
        const missingIASubjects = allStudentSubjects.filter(s => !existingIASubjects.has(s.subject_code));

        if (missingIASubjects.length > 0) {
          console.log(`📚 Adding ${missingIASubjects.length} subjects with no IA data`);
          const placeholderIARecords = missingIASubjects.map(subject => {
            // Get the appropriate display name, handling lab subjects
            const displayName = this.getSubjectDisplayName(subject.subject_code, subject.subject_name);

            return {
              subject_code: subject.subject_code,
              subject_name: displayName,
              ia1_marks: undefined,
              ia1_date: undefined,
              ia2_marks: undefined,
              ia2_date: undefined,
              ia3_marks: undefined,
              ia3_date: undefined,
              assignment_marks: undefined,
              theory_marks: undefined,
              total_marks: 0,
              average_marks: 0,
              grade: undefined
            };
          });

          return [...iaRecords, ...placeholderIARecords];
        }
      }

      return iaRecords;
    } catch (error) {
      console.error('Error getting student IA marks:', error);
      return [];
    }
  }

  /**
   * Calculate overall performance indicators
   */
  private static calculateOverallPerformance(
    attendance: AttendanceSummary,
    iaMarks: IAMarks[]
  ): StudentProgressCard['overall_performance'] {
    // Attendance status
    let attendanceStatus: 'good' | 'warning' | 'critical' = 'good';
    if (attendance.overall_percentage < 50) {
      attendanceStatus = 'critical';
    } else if (attendance.overall_percentage < 75) {
      attendanceStatus = 'warning';
    }

    // IA status
    const averageIA = iaMarks.length > 0
      ? iaMarks.reduce((sum, ia) => sum + ia.average_marks, 0) / iaMarks.length
      : 0;

    let iaStatus: 'excellent' | 'good' | 'average' | 'poor' = 'poor';
    if (averageIA >= 20) {
      iaStatus = 'excellent';
    } else if (averageIA >= 15) {
      iaStatus = 'good';
    } else if (averageIA >= 10) {
      iaStatus = 'average';
    }

    // Overall grade
    const overallGrade = this.calculateGrade((attendance.overall_percentage / 4) + (averageIA * 3));

    return {
      attendance_status: attendanceStatus,
      ia_status: iaStatus,
      overall_grade: overallGrade
    };
  }

  /**
   * Calculate grade based on marks
   */
  private static calculateGrade(marks: number): string {
    if (marks >= 90) return 'A+';
    if (marks >= 80) return 'A';
    if (marks >= 70) return 'B+';
    if (marks >= 60) return 'B';
    if (marks >= 50) return 'C';
    if (marks >= 40) return 'D';
    return 'F';
  }

  /**
   * Refresh student progress data after IA marks update
   * This method can be called after faculty saves IA marks to ensure real-time synchronization
   */
  static async refreshStudentProgress(studentUsn: string): Promise<StudentProgressCard | null> {
    try {
      console.log(`🔄 Refreshing student progress data for USN: ${studentUsn}`);

      // Get fresh student data
      const progressData = await this.getStudentProgressCard(studentUsn);

      if (progressData) {
        console.log(`✅ Successfully refreshed progress data for ${studentUsn}`);
        console.log(`📊 Updated IA marks count: ${progressData.ia_marks.length}`);
        console.log(`📊 Updated attendance: ${progressData.attendance.overall_percentage}%`);
      }

      return progressData;
    } catch (error) {
      console.error(`❌ Error refreshing student progress for ${studentUsn}:`, error);
      return null;
    }
  }

  /**
   * Validate IA marks data consistency between entry and display systems
   * This method helps ensure data integrity across both systems
   */
  static async validateIAMarksConsistency(
    studentId: string,
    subjectCode: string,
    academicYear: string
  ): Promise<{
    isConsistent: boolean;
    entrySystemData?: any;
    displaySystemData?: any;
    issues: string[];
  }> {
    try {
      console.log(`🔍 Validating IA marks consistency for student ${studentId}, subject ${subjectCode}`);

      const issues: string[] = [];

      // Get data from internal_assessments table (entry system)
      const { data: entryData, error: entryError } = await supabase
        .from('internal_assessments')
        .select('*')
        .eq('student_id', studentId)
        .eq('subject_code', subjectCode)
        .eq('academic_year', academicYear)
        .single();

      if (entryError && entryError.code !== 'PGRST116') {
        issues.push(`Error fetching entry system data: ${entryError.message}`);
      }

      // Normalize subject code for comparison
      const normalizedSubjectCode = this.normalizeSubjectCode(subjectCode);

      if (normalizedSubjectCode !== subjectCode) {
        console.log(`📝 Subject code normalized: ${subjectCode} -> ${normalizedSubjectCode}`);
      }

      const result = {
        isConsistent: issues.length === 0,
        entrySystemData: entryData,
        displaySystemData: entryData, // Same data source after integration
        issues
      };

      console.log(`✅ Validation complete. Consistent: ${result.isConsistent}`);
      return result;

    } catch (error) {
      console.error('❌ Error validating IA marks consistency:', error);
      return {
        isConsistent: false,
        issues: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }

  /**
   * Consolidate individual IA records from individual_ia_records table into standard format
   * This handles the Sequential IA system data
   */
  private static consolidateIndividualIARecords(individualRecords: any[]): any[] {
    console.log('🔄 Consolidating individual IA records...');

    // Group records by subject_code
    const subjectGroups = new Map<string, any[]>();

    individualRecords.forEach(record => {
      const key = record.subject_code;
      if (!subjectGroups.has(key)) {
        subjectGroups.set(key, []);
      }
      subjectGroups.get(key)!.push(record);
    });

    const consolidatedRecords: any[] = [];

    subjectGroups.forEach((records, subjectCode) => {
      console.log(`📝 Consolidating ${records.length} individual records for ${subjectCode}`);

      // Create a consolidated record for this subject
      const consolidatedRecord = {
        id: records[0].id, // Use first record's ID
        student_id: records[0].student_id,
        subject_code: subjectCode,
        department: records[0].department,
        semester: records[0].semester,
        section: records[0].section,
        academic_year: records[0].academic_year,
        faculty_id: records[0].faculty_id,
        created_at: records[0].created_at,
        updated_at: Math.max(...records.map(r => new Date(r.updated_at || r.created_at).getTime())),
        // Initialize all marks as null
        ia1_marks: null,
        ia2_marks: null,
        ia3_marks: null,
        assignment_marks: null,
        lab_marks: null
      };

      // Populate marks from individual records based on ia_phase
      records.forEach(record => {
        switch (record.ia_phase) {
          case 'IA1':
            consolidatedRecord.ia1_marks = record.marks;
            break;
          case 'IA2':
            consolidatedRecord.ia2_marks = record.marks;
            break;
          case 'IA3':
            consolidatedRecord.ia3_marks = record.marks;
            break;
          case 'ASSIGNMENT':
            consolidatedRecord.assignment_marks = record.marks;
            break;
          case 'LAB_INTERNAL':
            consolidatedRecord.lab_marks = record.marks;
            break;
        }
      });

      consolidatedRecords.push(consolidatedRecord);

      console.log(`✅ Consolidated ${subjectCode}: IA1=${consolidatedRecord.ia1_marks}, IA2=${consolidatedRecord.ia2_marks}, IA3=${consolidatedRecord.ia3_marks}, Assignment=${consolidatedRecord.assignment_marks}`);
    });

    return consolidatedRecords;
  }

  /**
   * Merge IA records from different sources, prioritizing the most recent data
   */
  private static mergeIARecords(standardRecords: any[], consolidatedRecords: any[]): any[] {
    console.log(`🔄 Merging ${standardRecords.length} standard records with ${consolidatedRecords.length} consolidated records`);

    const mergedMap = new Map<string, any>();

    // Add standard records first
    standardRecords.forEach(record => {
      const key = `${record.subject_code}_${record.academic_year}`;
      mergedMap.set(key, { ...record, source: 'internal_assessments' });
    });

    // Add/override with consolidated records if they're more recent or if standard record doesn't exist
    consolidatedRecords.forEach(record => {
      const key = `${record.subject_code}_${record.academic_year}`;
      const existing = mergedMap.get(key);

      if (!existing) {
        // No existing record, add the consolidated one
        mergedMap.set(key, { ...record, source: 'individual_ia_records' });
        console.log(`✅ Added new record from individual_ia_records: ${record.subject_code}`);
      } else {
        // Compare timestamps and use the more recent one
        const existingTime = new Date(existing.updated_at || existing.created_at).getTime();
        const newTime = new Date(record.updated_at || record.created_at).getTime();

        if (newTime > existingTime) {
          mergedMap.set(key, { ...record, source: 'individual_ia_records' });
          console.log(`✅ Updated record with more recent data from individual_ia_records: ${record.subject_code}`);
        } else {
          console.log(`✅ Kept existing record from internal_assessments: ${record.subject_code}`);
        }
      }
    });

    const mergedRecords = Array.from(mergedMap.values());
    console.log(`✅ Final merged records: ${mergedRecords.length} total`);

    return mergedRecords;
  }
}
