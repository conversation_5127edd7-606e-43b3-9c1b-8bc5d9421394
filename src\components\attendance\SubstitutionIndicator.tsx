import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { 
  UserCheck, 
  AlertTriangle, 
  Info, 
  Calendar,
  Clock,
  BookOpen,
  Users,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { AttendanceSubstitutionService, AttendanceSubstitutionInfo } from '@/services/AttendanceSubstitutionService';

interface SubstitutionIndicatorProps {
  facultyId: string;
  date: string;
  timeSlot: string;
  subjectCode: string;
  semester: string;
  section: string;
  department: string;
  className?: string;
}

export default function SubstitutionIndicator({
  facultyId,
  date,
  timeSlot,
  subjectCode,
  semester,
  section,
  department,
  className = ''
}: SubstitutionIndicatorProps) {
  const [substitutionInfo, setSubstitutionInfo] = useState<AttendanceSubstitutionInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    checkSubstitutionStatus();
  }, [facultyId, date, timeSlot, subjectCode, semester, section, department]);

  const checkSubstitutionStatus = async () => {
    try {
      setLoading(true);
      
      // Check if current faculty is a substitute
      const currentFacultySubstitution = await AttendanceSubstitutionService.checkSubstitutionStatus(
        facultyId,
        date,
        timeSlot,
        subjectCode,
        semester,
        section,
        department
      );

      // Also check if there's any substitution for this class
      const classSubstitution = await AttendanceSubstitutionService.getAttendanceSubstitutionInfo(
        date,
        timeSlot,
        subjectCode,
        semester,
        section,
        department
      );

      // Use the most relevant substitution info
      const relevantInfo = currentFacultySubstitution.is_substitute ? currentFacultySubstitution : classSubstitution;
      setSubstitutionInfo(relevantInfo);

    } catch (error) {
      console.error('Error checking substitution status:', error);
      setSubstitutionInfo(null);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>
        <span className="text-sm text-muted-foreground">Checking substitution status...</span>
      </div>
    );
  }

  if (!substitutionInfo || !substitutionInfo.is_substitute) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Main Substitution Alert */}
      <Alert className="border-blue-200 bg-blue-50">
        <UserCheck className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                Substitute Class
              </Badge>
              <span className="font-medium">
                You are substituting for {substitutionInfo.original_faculty_name}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setExpanded(!expanded)}
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-100"
            >
              {expanded ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-1" />
                  Less Info
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  More Info
                </>
              )}
            </Button>
          </div>
        </AlertDescription>
      </Alert>

      {/* Expanded Details */}
      {expanded && (
        <Card className="border-blue-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Info className="h-4 w-4 text-blue-600" />
              Substitution Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <UserCheck className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium">Original Faculty:</span>
                  <span>{substitutionInfo.original_faculty_name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium">Date:</span>
                  <span>{formatDate(date)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium">Time:</span>
                  <span>{timeSlot}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium">Subject:</span>
                  <span>{subjectCode}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium">Class:</span>
                  <span>{semester}-{section}</span>
                </div>
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium">Status:</span>
                  <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                    Active Substitution
                  </Badge>
                </div>
              </div>
            </div>

            {substitutionInfo.substitution_notes && (
              <div className="mt-3 p-3 bg-muted rounded-md">
                <div className="text-sm font-medium text-muted-foreground mb-1">
                  Instructions from {substitutionInfo.original_faculty_name}:
                </div>
                <div className="text-sm">
                  {substitutionInfo.substitution_notes}
                </div>
              </div>
            )}

            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <div className="font-medium">Important:</div>
                  <div>
                    This attendance will be recorded under your name as the substitute faculty. 
                    The original faculty ({substitutionInfo.original_faculty_name}) is on approved leave.
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Compact version for use in attendance lists
export function CompactSubstitutionIndicator({
  substitutionInfo,
  className = ''
}: {
  substitutionInfo: AttendanceSubstitutionInfo | null;
  className?: string;
}) {
  if (!substitutionInfo || !substitutionInfo.is_substitute) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200 text-xs">
        <UserCheck className="h-3 w-3 mr-1" />
        Substitute
      </Badge>
      <span className="text-xs text-muted-foreground">
        for {substitutionInfo.original_faculty_name}
      </span>
    </div>
  );
}

// Hook for easy substitution status checking
export function useSubstitutionStatus(
  facultyId: string,
  date: string,
  timeSlot: string,
  subjectCode: string,
  semester: string,
  section: string,
  department: string
) {
  const [substitutionInfo, setSubstitutionInfo] = useState<AttendanceSubstitutionInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkStatus = async () => {
      try {
        setLoading(true);
        const info = await AttendanceSubstitutionService.checkSubstitutionStatus(
          facultyId,
          date,
          timeSlot,
          subjectCode,
          semester,
          section,
          department
        );
        setSubstitutionInfo(info);
      } catch (error) {
        console.error('Error checking substitution status:', error);
        setSubstitutionInfo(null);
      } finally {
        setLoading(false);
      }
    };

    if (facultyId && date && timeSlot && subjectCode && semester && section && department) {
      checkStatus();
    }
  }, [facultyId, date, timeSlot, subjectCode, semester, section, department]);

  return { substitutionInfo, loading };
}
