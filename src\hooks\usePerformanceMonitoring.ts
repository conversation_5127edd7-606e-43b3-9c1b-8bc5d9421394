import { useEffect, useCallback } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  componentName: string;
}

/**
 * Hook for monitoring component performance and lazy loading metrics
 */
export const usePerformanceMonitoring = (componentName: string) => {
  const startTime = performance.now();

  useEffect(() => {
    const endTime = performance.now();
    const loadTime = endTime - startTime;

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 Performance: ${componentName} loaded in ${loadTime.toFixed(2)}ms`);
    }

    // Report to analytics in production (if needed)
    if (process.env.NODE_ENV === 'production' && loadTime > 1000) {
      console.warn(`⚠️ Slow loading: ${componentName} took ${loadTime.toFixed(2)}ms`);
    }
  }, [componentName, startTime]);

  const measureRender = useCallback((renderStartTime: number) => {
    const renderEndTime = performance.now();
    const renderTime = renderEndTime - renderStartTime;

    if (process.env.NODE_ENV === 'development') {
      console.log(`🎨 Render: ${componentName} rendered in ${renderTime.toFixed(2)}ms`);
    }

    return renderTime;
  }, [componentName]);

  return { measureRender };
};

/**
 * Hook for preloading components based on user interaction
 */
export const useComponentPreloader = () => {
  const preloadComponent = useCallback(async (importFunction: () => Promise<any>) => {
    try {
      await importFunction();
      console.log('🔄 Component preloaded successfully');
    } catch (error) {
      console.error('❌ Failed to preload component:', error);
    }
  }, []);

  const preloadOnHover = useCallback((importFunction: () => Promise<any>) => {
    return {
      onMouseEnter: () => preloadComponent(importFunction),
      onFocus: () => preloadComponent(importFunction)
    };
  }, [preloadComponent]);

  return { preloadComponent, preloadOnHover };
};

export default usePerformanceMonitoring;
