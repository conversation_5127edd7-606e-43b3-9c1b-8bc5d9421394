// src/components/subjects/TimeRangeField.tsx
import React from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

interface Props {
  value: string;                // 'morning' | 'afternoon'
  onChange: (val: string) => void;
}

export const TimeRangeField: React.FC<Props> = ({ value, onChange }) => {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger>
        <SelectValue placeholder="Select time of day" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="morning">Morning (08:30–11:25)</SelectItem>
        <SelectItem value="afternoon">Afternoon (13:15–16:00)</SelectItem>
      </SelectContent>
    </Select>
  );
};
