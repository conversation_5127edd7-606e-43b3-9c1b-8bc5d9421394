import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import {
  FileText,
  Download,
  Eye,
  BarChart3,
  Users,
  TrendingUp,
  AlertCircle,
  Award,
  Target,
  BookOpen,
  Filter,
  RefreshCw
} from 'lucide-react';

import { FeedbackService } from '@/services/FeedbackService';
import { FeedbackSession } from '@/types/feedback-system';

interface HODFacultyReportsProps {
  department: string;
  departmentName: string;
}

interface FacultyReport {
  faculty_id: string;
  faculty_name: string;
  subjects: any[];
  total_responses: number;
  average_rating: number;
  overall_percentage: number;
}

interface DepartmentSummary {
  department: string;
  total_faculty: number;
  total_reports: number;
  average_rating: number;
  total_responses: number;
  top_performers: any[];
  improvement_areas: any[];
}

const HODFacultyReports: React.FC<HODFacultyReportsProps> = ({
  department,
  departmentName
}) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [sessions, setSessions] = useState<FeedbackSession[]>([]);
  const [facultyReports, setFacultyReports] = useState<FacultyReport[]>([]);
  const [departmentSummary, setDepartmentSummary] = useState<DepartmentSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedSession, setSelectedSession] = useState<string>("");
  const [selectedSemester, setSelectedSemester] = useState<string>("");
  const [selectedSection, setSelectedSection] = useState<string>("");
  const [consolidatedReport, setConsolidatedReport] = useState<any>(null);
  const [generatingReport, setGeneratingReport] = useState(false);
  const [selectedFacultyForReports, setSelectedFacultyForReports] = useState<string | null>(null);
  const [detailedIndividualReport, setDetailedIndividualReport] = useState<any>(null);

  const { toast } = useToast();

  useEffect(() => {
    loadReportsData();
  }, [department]);

  useEffect(() => {
    if (selectedSession || selectedSemester || selectedSection) {
      loadFilteredReports();
    }
  }, [selectedSession, selectedSemester, selectedSection]);

  const loadReportsData = async () => {
    try {
      setLoading(true);
      const data = await FeedbackService.getHODFacultyReports(department);
      
      setSessions(data.sessions || []);
      setFacultyReports(data.facultyReports || []);
      setDepartmentSummary(data.departmentSummary);

      if (data.sessions && data.sessions.length > 0) {
        setSelectedSession(data.sessions[0].id);
      }

    } catch (error) {
      console.error('Error loading reports data:', error);
      toast({
        title: "Error",
        description: "Failed to load faculty reports",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadFilteredReports = async () => {
    try {
      const data = await FeedbackService.getHODFacultyReports(
        department,
        selectedSession === "all" ? undefined : selectedSession || undefined,
        selectedSemester === "all" ? undefined : selectedSemester || undefined,
        selectedSection === "all" ? undefined : selectedSection || undefined
      );

      setFacultyReports(data.facultyReports || []);
      setDepartmentSummary(data.departmentSummary);

    } catch (error) {
      console.error('Error loading filtered reports:', error);
      toast({
        title: "Error",
        description: "Failed to load filtered reports",
        variant: "destructive",
      });
    }
  };

  const generateConsolidatedReport = async () => {
    if (!selectedSession || selectedSession === "all") {
      toast({
        title: "Session Required",
        description: "Please select a specific feedback session to generate consolidated report",
        variant: "destructive",
      });
      return;
    }

    try {
      setGeneratingReport(true);
      const report = await FeedbackService.generateDepartmentConsolidatedReport(
        department,
        selectedSession,
        'HOD'
      );

      setConsolidatedReport(report);
      setActiveTab("consolidated");

      toast({
        title: "Report Generated",
        description: "Consolidated department report has been generated successfully",
      });

    } catch (error) {
      console.error('Error generating consolidated report:', error);
      toast({
        title: "Error",
        description: "Failed to generate consolidated report",
        variant: "destructive",
      });
    } finally {
      setGeneratingReport(false);
    }
  };

  const generateIndividualReport = async (facultyId: string) => {
    if (!selectedSession || selectedSession === "all") {
      toast({
        title: "Session Required",
        description: "Please select a specific feedback session to generate individual report",
        variant: "destructive",
      });
      return;
    }

    try {
      const report = await FeedbackService.generateDetailedStudentFeedbackReport(
        department,
        selectedSession,
        facultyId
      );

      setDetailedIndividualReport(report);
      setSelectedFacultyForReports(facultyId);
      setActiveTab("individual-reports");

      toast({
        title: "Individual Report Generated",
        description: `Generated detailed student feedback report for the faculty`,
      });

    } catch (error) {
      console.error('Error generating individual report:', error);
      toast({
        title: "Error",
        description: "Failed to generate individual faculty report",
        variant: "destructive",
      });
    }
  };



  const getPerformanceBadge = (percentage: number) => {
    if (percentage >= 80) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (percentage >= 60) return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge className="bg-red-100 text-red-800">Needs Improvement</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="loading-spinner h-8 w-8 mx-auto"></div>
          <p className="text-muted-foreground">Loading faculty reports...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Faculty Feedback Reports</h2>
          <p className="text-muted-foreground">
            Comprehensive feedback analysis for {departmentName} faculty
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={generateConsolidatedReport}
            disabled={!selectedSession || selectedSession === "all" || generatingReport}
            className="gap-2"
          >
            {generatingReport ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <FileText className="h-4 w-4" />
            )}
            Generate Consolidated Report
          </Button>
          <Button onClick={loadReportsData} variant="outline" className="gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Feedback Session</label>
              <Select value={selectedSession} onValueChange={setSelectedSession}>
                <SelectTrigger>
                  <SelectValue placeholder="Select session" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sessions</SelectItem>
                  {sessions.map((session) => (
                    <SelectItem key={session.id} value={session.id}>
                      {session.session_name} ({session.academic_year})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Semester</label>
              <Select value={selectedSemester} onValueChange={setSelectedSemester}>
                <SelectTrigger>
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Semesters</SelectItem>
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((sem) => (
                    <SelectItem key={sem} value={sem.toString()}>
                      Semester {sem}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Section</label>
              <Select value={selectedSection} onValueChange={setSelectedSection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sections</SelectItem>
                  {['A', 'B', 'C', 'D'].map((section) => (
                    <SelectItem key={section} value={section}>
                      Section {section}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="faculty">Faculty Reports</TabsTrigger>
          <TabsTrigger value="individual-reports">Individual Reports</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="consolidated">Consolidated</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {departmentSummary && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Faculty</p>
                      <p className="text-2xl font-bold">{departmentSummary.total_faculty}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Reports</p>
                      <p className="text-2xl font-bold">{departmentSummary.total_reports}</p>
                    </div>
                    <FileText className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Responses</p>
                      <p className="text-2xl font-bold">{departmentSummary.total_responses}</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Avg Rating</p>
                      <p className="text-2xl font-bold">{departmentSummary.average_rating.toFixed(1)}%</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="faculty" className="space-y-6">
          {facultyReports.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Faculty Reports Available</h3>
                <p className="text-muted-foreground mb-4">
                  No feedback reports found for the selected filters.
                </p>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Ensure feedback sessions are completed and reports are generated.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {facultyReports.map((faculty) => (
                <Card key={faculty.faculty_id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-xl">{faculty.faculty_name}</CardTitle>
                        <CardDescription>
                          {faculty.subjects.length} subjects • {faculty.total_responses} total responses
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        {getPerformanceBadge(faculty.overall_percentage)}
                        <Badge variant="outline">
                          {faculty.overall_percentage.toFixed(1)}%
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Faculty Summary */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                        <div className="text-center">
                          <p className="text-2xl font-bold">{faculty.subjects.length}</p>
                          <p className="text-sm text-muted-foreground">Subjects</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold">{faculty.total_responses}</p>
                          <p className="text-sm text-muted-foreground">Responses</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold">{faculty.overall_percentage.toFixed(1)}%</p>
                          <p className="text-sm text-muted-foreground">Avg Rating</p>
                        </div>
                      </div>

                      {/* Subject Details */}
                      <div className="space-y-3">
                        <h4 className="font-medium flex items-center gap-2">
                          <BookOpen className="h-4 w-4" />
                          Subject-wise Performance
                        </h4>
                        <div className="space-y-2">
                          {faculty.subjects.map((subject, index) => (
                            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <p className="font-medium">{subject.subject_name}</p>
                                  <Badge variant="outline" className="text-xs">
                                    {subject.subject_code}
                                  </Badge>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  Semester {subject.semester} • Section {subject.section}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-medium">{subject.overall_percentage.toFixed(1)}%</p>
                                <p className="text-sm text-muted-foreground">
                                  {subject.total_responses} responses
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-4 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          className="gap-2"
                          onClick={() => generateIndividualReport(faculty.faculty_id)}
                          disabled={!selectedSession || selectedSession === "all"}
                        >
                          <Eye className="h-4 w-4" />
                          Generate Individual Report
                        </Button>
                        <Button variant="outline" size="sm" className="gap-2">
                          <Download className="h-4 w-4" />
                          Export PDF
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="individual-reports" className="space-y-6">
          {!detailedIndividualReport ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Individual Report Generated</h3>
                <p className="text-muted-foreground">
                  Click "Generate Individual Report" for any faculty to view detailed student feedback analysis.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-bold">Individual Faculty Report - {detailedIndividualReport.faculty.name}</h3>
                  <p className="text-muted-foreground">
                    Detailed student feedback analysis • Session: {detailedIndividualReport.session.name}
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    setDetailedIndividualReport(null);
                    setSelectedFacultyForReports(null);
                    setActiveTab("faculty");
                  }}
                >
                  Back to Faculty Reports
                </Button>
              </div>

              {/* Subject Reports */}
              {detailedIndividualReport.subject_reports.map((subjectReport: any, index: number) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-lg">
                          {subjectReport.subject.name} ({subjectReport.subject.code})
                        </CardTitle>
                        <CardDescription>
                          Semester {subjectReport.subject.semester} • Section {subjectReport.subject.section} • {subjectReport.total_students} students
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Student Feedback Table */}
                      <div>
                        <h4 className="font-medium mb-3">Student-wise Feedback Analysis</h4>
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse border border-gray-300">
                            <thead>
                              <tr className="bg-muted/50">
                                <th className="border border-gray-300 px-3 py-2 text-left font-medium">Masked USN</th>
                                {detailedIndividualReport.questions.map((question: any, qIndex: number) => (
                                  <th key={qIndex} className="border border-gray-300 px-2 py-2 text-center font-medium text-xs">
                                    Q{qIndex + 1}
                                  </th>
                                ))}
                                <th className="border border-gray-300 px-3 py-2 text-center font-medium">Total</th>
                                <th className="border border-gray-300 px-3 py-2 text-center font-medium">%</th>
                              </tr>
                            </thead>
                            <tbody>
                              {subjectReport.student_details.map((student: any, studentIndex: number) => (
                                <tr key={studentIndex} className={studentIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                                  <td className="border border-gray-300 px-3 py-2 font-medium">{student.masked_usn}</td>
                                  {detailedIndividualReport.questions.map((question: any, qIndex: number) => (
                                    <td key={qIndex} className="border border-gray-300 px-2 py-2 text-center">
                                      {student.question_ratings[`question_${question.id}`] || student.question_ratings[`question_${qIndex + 1}`] || '-'}
                                    </td>
                                  ))}
                                  <td className="border border-gray-300 px-3 py-2 text-center font-medium">
                                    {student.aggregate_score}
                                  </td>
                                  <td className="border border-gray-300 px-3 py-2 text-center">
                                    <span className={`font-medium ${
                                      student.percentage >= 80 ? 'text-green-600' :
                                      student.percentage >= 60 ? 'text-yellow-600' : 'text-red-600'
                                    }`}>
                                      {student.percentage.toFixed(1)}%
                                    </span>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      {/* Summary Statistics */}
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
                        <div className="text-center">
                          <p className="text-xl font-bold">{subjectReport.total_students}</p>
                          <p className="text-sm text-muted-foreground">Total Students</p>
                        </div>
                        <div className="text-center">
                          <p className="text-xl font-bold">
                            {(subjectReport.student_details.reduce((sum: number, s: any) => sum + s.aggregate_score, 0) / subjectReport.student_details.length).toFixed(1)}
                          </p>
                          <p className="text-sm text-muted-foreground">Avg Score</p>
                        </div>
                        <div className="text-center">
                          <p className="text-xl font-bold">
                            {Math.max(...subjectReport.student_details.map((s: any) => s.aggregate_score))}
                          </p>
                          <p className="text-sm text-muted-foreground">Highest Score</p>
                        </div>
                        <div className="text-center">
                          <p className="text-xl font-bold">
                            {(subjectReport.student_details.reduce((sum: number, s: any) => sum + s.percentage, 0) / subjectReport.student_details.length).toFixed(1)}%
                          </p>
                          <p className="text-sm text-muted-foreground">Avg Percentage</p>
                        </div>
                      </div>

                      {/* Export Options */}
                      <div className="flex gap-2 pt-4 border-t">
                        <Button variant="outline" size="sm" className="gap-2">
                          <Download className="h-4 w-4" />
                          Export PDF
                        </Button>
                        <Button variant="outline" size="sm" className="gap-2">
                          <Download className="h-4 w-4" />
                          Export Excel
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>



        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardContent className="p-8 text-center">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Analytics Coming Soon</h3>
              <p className="text-muted-foreground">Department analytics will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consolidated" className="space-y-6">
          {consolidatedReport ? (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Consolidated Department Report
                  </CardTitle>
                  <CardDescription>
                    Generated on {new Date(consolidatedReport.generated_at).toLocaleDateString()} at {new Date(consolidatedReport.generated_at).toLocaleTimeString()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Department Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <p className="text-2xl font-bold text-blue-600">
                          {consolidatedReport.department_metrics.total_faculty}
                        </p>
                        <p className="text-sm text-muted-foreground">Total Faculty</p>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <p className="text-2xl font-bold text-green-600">
                          {consolidatedReport.department_metrics.total_responses}
                        </p>
                        <p className="text-sm text-muted-foreground">Total Responses</p>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <p className="text-2xl font-bold text-purple-600">
                          {consolidatedReport.department_metrics.average_department_rating.toFixed(1)}
                        </p>
                        <p className="text-sm text-muted-foreground">Avg Rating</p>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <p className="text-2xl font-bold text-orange-600">
                          {consolidatedReport.faculty_summary.length}
                        </p>
                        <p className="text-sm text-muted-foreground">Active Faculty</p>
                      </div>
                    </div>

                    {/* Faculty Summary Cards */}
                    <div>
                      <h4 className="font-medium mb-4">Faculty Summary</h4>
                      <div className="space-y-4">
                        {consolidatedReport.faculty_summary.map((faculty: any, index: number) => (
                          <Card key={faculty.faculty_id}>
                            <CardHeader>
                              <div className="flex items-start justify-between">
                                <div className="space-y-1">
                                  <CardTitle className="text-lg">{faculty.faculty_name}</CardTitle>
                                  <CardDescription>
                                    Employee ID: {faculty.employee_id} • {faculty.subjects_taught.length} subjects • {faculty.total_responses} total responses
                                  </CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                  {getPerformanceBadge(faculty.average_rating)}
                                  <Badge variant="outline">
                                    {faculty.average_rating.toFixed(1)}/10
                                  </Badge>
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-4">
                                {/* Faculty Metrics */}
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
                                  <div className="text-center">
                                    <p className="text-xl font-bold">{faculty.subjects_taught.length}</p>
                                    <p className="text-sm text-muted-foreground">Subjects</p>
                                  </div>
                                  <div className="text-center">
                                    <p className="text-xl font-bold">{faculty.total_responses}</p>
                                    <p className="text-sm text-muted-foreground">Total Responses</p>
                                  </div>
                                  <div className="text-center">
                                    <p className="text-xl font-bold">{faculty.average_rating.toFixed(1)}</p>
                                    <p className="text-sm text-muted-foreground">Avg Rating</p>
                                  </div>
                                  <div className="text-center">
                                    <p className="text-xl font-bold">{((faculty.average_rating / 10) * 100).toFixed(1)}%</p>
                                    <p className="text-sm text-muted-foreground">Performance</p>
                                  </div>
                                </div>

                                {/* Subject Details */}
                                <div>
                                  <h5 className="font-medium mb-3 flex items-center gap-2">
                                    <BookOpen className="h-4 w-4" />
                                    Subject Details
                                  </h5>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    {faculty.subjects_taught.map((subject: any, subIndex: number) => (
                                      <div key={subIndex} className="p-3 border rounded-lg bg-white">
                                        <div className="flex items-start justify-between">
                                          <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                              <p className="font-medium text-sm">{subject.subject_name}</p>
                                              <Badge variant="outline" className="text-xs">
                                                {subject.subject_code}
                                              </Badge>
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                              Semester {subject.semester} • Section {subject.section} • {subject.subject_type}
                                            </p>
                                          </div>
                                          <div className="text-right">
                                            <p className="font-bold text-sm">{subject.response_count}</p>
                                            <p className="text-xs text-muted-foreground">responses</p>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex gap-2 pt-4 border-t">
                                  <Button
                                    variant="default"
                                    size="sm"
                                    className="gap-2"
                                    onClick={() => generateIndividualReport(faculty.faculty_id)}
                                  >
                                    <FileText className="h-4 w-4" />
                                    Generate Individual Report
                                  </Button>
                                  <Button variant="outline" size="sm" className="gap-2">
                                    <Download className="h-4 w-4" />
                                    Export PDF
                                  </Button>
                                  <Button variant="outline" size="sm" className="gap-2">
                                    <Eye className="h-4 w-4" />
                                    View Performance Trends
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    {/* Export Options */}
                    <div className="flex gap-2 pt-4 border-t">
                      <Button className="gap-2">
                        <Download className="h-4 w-4" />
                        Export PDF
                      </Button>
                      <Button variant="outline" className="gap-2">
                        <Download className="h-4 w-4" />
                        Export Excel
                      </Button>
                      <Button variant="outline" className="gap-2">
                        <FileText className="h-4 w-4" />
                        Print Report
                      </Button>
                      <Button
                        variant="outline"
                        className="gap-2"
                        onClick={() => setConsolidatedReport(null)}
                      >
                        <RefreshCw className="h-4 w-4" />
                        Generate New Report
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Generate Consolidated Report</h3>
                <p className="text-muted-foreground mb-4">
                  Select a feedback session and click "Generate Consolidated Report" to create a comprehensive department analysis.
                </p>
                <Button
                  onClick={generateConsolidatedReport}
                  disabled={!selectedSession || selectedSession === "all" || generatingReport}
                  className="gap-2"
                >
                  {generatingReport ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <FileText className="h-4 w-4" />
                  )}
                  Generate Report
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HODFacultyReports;
