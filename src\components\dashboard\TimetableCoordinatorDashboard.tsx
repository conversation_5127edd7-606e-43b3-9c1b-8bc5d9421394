import { useState, useEffect } from "react";
import {
  Calendar,
  FileText,
  Users,
  Book,
  Clock,
  AlertTriangle,
  Plus,
  Play,
  Eye,
  Settings,
  TrendingUp,
  BarChart3,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
  Sparkles,
  Zap,
  Shield
} from "lucide-react";
import DashboardCard from "@/components/dashboard/DashboardCard";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Bar<PERSON>hart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON>ianGrid, <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from "recharts";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { ModernLayout, ModernGrid, ModernCard, ModernButtonGroup } from "@/components/layout/ModernLayout";
import { ModernLoading } from "@/components/ui/modern-loading";

interface TimetableStats {
  totalDepartments: number;
  activeTimetables: number;
  subjectAllotments: number;
  facultyMembers: number;
  pendingConflicts: number;
  completedSchedules: number;
  labSlots: number;
  theorySlots: number;
  totalSlots: number;
  facultyUtilization: number;
  completionPercentage: number;
}

interface FacultyWorkload {
  name: string;
  department: string;
  totalSlots: number;
  labSlots: number;
  theorySlots: number;
  availability: number;
}

interface DepartmentCompletion {
  department: string;
  completed: number;
  total: number;
  percentage: number;
}

interface ConflictData {
  type: string;
  count: number;
  severity: 'high' | 'medium' | 'low';
}

interface ActivityLog {
  id: string;
  action: string;
  description?: string;
  created_at: string;
  user_id?: string;
}

interface RoomUtilization {
  room: string;
  department: string;
  totalSlots: number;
  utilizationPercentage: number;
}

interface TimeSlotAnalysis {
  timeSlot: string;
  totalBookings: number;
  departments: string[];
  utilizationRate: number;
}

const TimetableCoordinatorDashboard = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState<TimetableStats>({
    totalDepartments: 0,
    activeTimetables: 0,
    subjectAllotments: 0,
    facultyMembers: 0,
    pendingConflicts: 0,
    completedSchedules: 0,
    labSlots: 0,
    theorySlots: 0,
    totalSlots: 0,
    facultyUtilization: 0,
    completionPercentage: 0,
  });
  const [facultyWorkload, setFacultyWorkload] = useState<FacultyWorkload[]>([]);
  const [departmentCompletion, setDepartmentCompletion] = useState<DepartmentCompletion[]>([]);
  const [conflictData, setConflictData] = useState<ConflictData[]>([]);
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [roomUtilization, setRoomUtilization] = useState<RoomUtilization[]>([]);
  const [timeSlotAnalysis, setTimeSlotAnalysis] = useState<TimeSlotAnalysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    fetchTimetableStats();
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchTimetableStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchTimetableStats = async () => {
    try {
      setLoading(true);

      // Fetch comprehensive statistics with better error handling
      const [
        { data: employees, error: employeesError },
        { data: mappings, error: mappingsError },
        { data: facultyTimetables, error: facultyTimetablesError },
        { data: labSlots, error: labSlotsError },
        { data: theorySlots, error: theorySlotsError },
        { data: conflicts, error: conflictsError },
        { data: auditLogs, error: auditError }
      ] = await Promise.all([
        supabase.from('employee_details').select('id, full_name, department, designation, vacant_by_day, vacant_count_by_day').not('department', 'is', null),
        supabase.from('simplified_subject_faculty_mappings').select('id, department, semester, section, subject_type, faculty_1_id, faculty_2_id, hours_per_week'),
        supabase.from('faculty_timetables').select('id, faculty_id, subject_type, department, day, time_slot'),
        supabase.from('lab_time_slots').select('id, faculty_id, department, day, time_slot'),
        supabase.from('timetable_slots').select('id, faculty_id, department, subject_type, day, time_slot').eq('subject_type', 'theory'),
        supabase.from('timetable_conflicts').select('id, conflict_type, severity, status, created_at'),
        supabase.from('timetable_audit_log').select('id, action, created_at').order('created_at', { ascending: false }).limit(10)
      ]);

      // Log any errors but continue with available data
      if (employeesError) console.warn('Error fetching employees:', employeesError);
      if (mappingsError) console.warn('Error fetching mappings:', mappingsError);
      if (facultyTimetablesError) console.warn('Error fetching faculty timetables:', facultyTimetablesError);
      if (labSlotsError) console.warn('Error fetching lab slots:', labSlotsError);
      if (theorySlotsError) console.warn('Error fetching theory slots:', theorySlotsError);
      if (conflictsError) console.warn('Error fetching conflicts:', conflictsError);
      if (auditError) console.warn('Error fetching audit logs:', auditError);

      // Calculate departments from employees
      const uniqueDepartments = new Set(employees?.map(e => e.department).filter(Boolean) || []);

      // Calculate departments with active timetables
      const activeTimetableDepartments = new Set([
        ...(facultyTimetables?.map(t => t.department).filter(Boolean) || []),
        ...(labSlots?.map(l => l.department).filter(Boolean) || []),
        ...(theorySlots?.map(t => t.department).filter(Boolean) || [])
      ]);

      // Calculate slot statistics
      const totalLabSlots = (labSlots?.length || 0) + (facultyTimetables?.filter(ft => ft.subject_type === 'laboratory').length || 0);
      const totalTheorySlots = (theorySlots?.length || 0) + (facultyTimetables?.filter(ft => ft.subject_type === 'theory').length || 0);
      const totalSlots = totalLabSlots + totalTheorySlots;

      // Calculate faculty utilization (faculty with assigned slots)
      const facultyWithSlots = new Set([
        ...(facultyTimetables?.map(ft => ft.faculty_id) || []),
        ...(labSlots?.map(ls => ls.faculty_id) || []),
        ...(theorySlots?.map(ts => ts.faculty_id) || [])
      ]);
      const facultyUtilization = employees?.length ? Math.round((facultyWithSlots.size / employees.length) * 100) : 0;

      // Calculate completion percentage based on mappings vs scheduled slots
      const totalMappings = mappings?.length || 0;
      const scheduledMappings = totalSlots;
      const completionPercentage = totalMappings > 0 ? Math.round((scheduledMappings / (totalMappings * 5)) * 100) : 0; // Assuming 5 slots per week per mapping

      // Calculate pending conflicts
      const pendingConflicts = conflicts?.filter(c => c.status === 'pending' || c.status === 'active').length || 0;

      const calculatedStats = {
        totalDepartments: uniqueDepartments.size || 0,
        activeTimetables: activeTimetableDepartments.size || 0,
        subjectAllotments: totalMappings,
        facultyMembers: employees?.length || 0,
        pendingConflicts: pendingConflicts,
        completedSchedules: activeTimetableDepartments.size || 0,
        labSlots: totalLabSlots,
        theorySlots: totalTheorySlots,
        totalSlots: totalSlots,
        facultyUtilization: facultyUtilization,
        completionPercentage: Math.min(completionPercentage, 100),
      };

      setStats(calculatedStats);
      await Promise.all([
        fetchFacultyWorkload(),
        fetchDepartmentCompletion(),
        fetchConflictData(),
        fetchActivityLogs(),
        fetchRoomUtilization(),
        fetchTimeSlotAnalysis()
      ]);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching timetable stats:', error);
      // Set reasonable default values on error
      setStats({
        totalDepartments: 0,
        activeTimetables: 0,
        subjectAllotments: 0,
        facultyMembers: 0,
        pendingConflicts: 0,
        completedSchedules: 0,
        labSlots: 0,
        theorySlots: 0,
        totalSlots: 0,
        facultyUtilization: 0,
        completionPercentage: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchFacultyWorkload = async () => {
    try {
      // Fetch faculty data with better error handling
      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, department, vacant_by_day, vacant_count_by_day')
        .not('department', 'is', null);

      if (facultyError) {
        console.warn('Error fetching faculty data:', facultyError);
        return;
      }

      // Fetch all timetable data for workload calculation
      const [
        { data: facultyTimetableData, error: ftError },
        { data: labSlotData, error: labError },
        { data: theorySlotData, error: theoryError }
      ] = await Promise.all([
        supabase.from('faculty_timetables').select('faculty_id, subject_type, day, time_slot'),
        supabase.from('lab_time_slots').select('faculty_id, day, time_slot'),
        supabase.from('timetable_slots').select('faculty_id, subject_type, day, time_slot').eq('subject_type', 'theory')
      ]);

      if (ftError) console.warn('Error fetching faculty timetables:', ftError);
      if (labError) console.warn('Error fetching lab slots:', labError);
      if (theoryError) console.warn('Error fetching theory slots:', theoryError);

      const workloadMap = new Map();

      facultyData?.forEach(faculty => {
        // Count slots from all sources
        const facultyTimetableSlots = facultyTimetableData?.filter(slot => slot.faculty_id === faculty.id) || [];
        const facultyLabSlots = labSlotData?.filter(slot => slot.faculty_id === faculty.id) || [];
        const facultyTheorySlots = theorySlotData?.filter(slot => slot.faculty_id === faculty.id) || [];

        // Calculate lab and theory slots
        const labSlots = facultyLabSlots.length + facultyTimetableSlots.filter(slot => slot.subject_type === 'laboratory').length;
        const theorySlots = facultyTheorySlots.length + facultyTimetableSlots.filter(slot => slot.subject_type === 'theory').length;
        const totalSlots = labSlots + theorySlots;

        // Calculate availability from vacant_by_day (total available slots per week)
        let totalAvailableSlots = 35; // Default: 7 days × 5 slots per day
        if (faculty.vacant_by_day && typeof faculty.vacant_by_day === 'object') {
          totalAvailableSlots = Object.values(faculty.vacant_by_day).reduce((sum: number, count: unknown) => {
            const numCount = typeof count === 'number' ? count : 0;
            return sum + numCount;
          }, 0);
        }

        // Calculate availability percentage (remaining slots / total possible slots)
        const remainingSlots = Math.max(0, totalAvailableSlots - totalSlots);
        const availabilityPercentage = totalAvailableSlots > 0 ? Math.round((remainingSlots / totalAvailableSlots) * 100) : 0;

        workloadMap.set(faculty.id, {
          name: faculty.full_name,
          department: faculty.department,
          totalSlots,
          labSlots,
          theorySlots,
          availability: availabilityPercentage
        });
      });

      // Sort by total slots (highest workload first) and take top 10
      const sortedWorkload = Array.from(workloadMap.values())
        .sort((a, b) => b.totalSlots - a.totalSlots)
        .slice(0, 10);

      setFacultyWorkload(sortedWorkload);
    } catch (error) {
      console.error('Error fetching faculty workload:', error);
      // Set empty array on error - will be handled by UI
      setFacultyWorkload([]);
    }
  };

  const fetchDepartmentCompletion = async () => {
    try {
      // Fetch department data and mappings
      const [
        { data: departments, error: deptError },
        { data: mappings, error: mappingsError },
        { data: facultyTimetables, error: ftError },
        { data: labSlots, error: labError },
        { data: theorySlots, error: theoryError }
      ] = await Promise.all([
        supabase.from('employee_details').select('department').not('department', 'is', null),
        supabase.from('simplified_subject_faculty_mappings').select('department, hours_per_week'),
        supabase.from('faculty_timetables').select('department'),
        supabase.from('lab_time_slots').select('department'),
        supabase.from('timetable_slots').select('department')
      ]);

      if (deptError) console.warn('Error fetching departments:', deptError);
      if (mappingsError) console.warn('Error fetching mappings:', mappingsError);
      if (ftError) console.warn('Error fetching faculty timetables:', ftError);
      if (labError) console.warn('Error fetching lab slots:', labError);
      if (theoryError) console.warn('Error fetching theory slots:', theoryError);

      const deptMap = new Map();
      const uniqueDepts = [...new Set(departments?.map(d => d.department).filter(Boolean))];

      uniqueDepts.forEach(dept => {
        // Calculate expected slots based on subject mappings
        const deptMappings = mappings?.filter(m => m.department === dept) || [];
        const expectedSlots = deptMappings.reduce((total, mapping) => {
          // Estimate 5 slots per week per mapping (could be adjusted based on hours_per_week)
          const slotsPerWeek = mapping.hours_per_week || 5;
          return total + slotsPerWeek;
        }, 0);

        // Calculate actual scheduled slots
        const deptFacultyTimetables = facultyTimetables?.filter(t => t.department === dept) || [];
        const deptLabSlots = labSlots?.filter(l => l.department === dept) || [];
        const deptTheorySlots = theorySlots?.filter(t => t.department === dept) || [];

        const scheduledSlots = deptFacultyTimetables.length + deptLabSlots.length + deptTheorySlots.length;

        // Calculate completion percentage
        const percentage = expectedSlots > 0 ? Math.round((scheduledSlots / expectedSlots) * 100) : 0;

        deptMap.set(dept, {
          department: dept,
          completed: scheduledSlots,
          total: expectedSlots,
          percentage: Math.min(percentage, 100)
        });
      });

      const completionData = Array.from(deptMap.values())
        .filter(dept => dept.total > 0) // Only include departments with mappings
        .sort((a, b) => b.percentage - a.percentage); // Sort by completion percentage

      setDepartmentCompletion(completionData);
    } catch (error) {
      console.error('Error fetching department completion:', error);
      // Set empty array on error
      setDepartmentCompletion([]);
    }
  };

  const fetchConflictData = async () => {
    try {
      // Fetch actual conflict data from the database
      const { data: conflicts, error: conflictsError } = await supabase
        .from('timetable_conflicts')
        .select('id, conflict_type, severity, status, created_at, description')
        .in('status', ['pending', 'active']);

      if (conflictsError) {
        console.warn('Error fetching conflicts:', conflictsError);
        setConflictData([]);
        return;
      }

      // Group conflicts by type and severity
      const conflictMap = new Map();

      conflicts?.forEach(conflict => {
        const key = conflict.conflict_type || 'Unknown Conflict';
        const existing = conflictMap.get(key) || { type: key, count: 0, severity: 'low' };

        existing.count += 1;

        // Set highest severity for this conflict type
        if (conflict.severity === 'high' || existing.severity !== 'high') {
          if (conflict.severity === 'high') existing.severity = 'high';
          else if (conflict.severity === 'medium' && existing.severity === 'low') existing.severity = 'medium';
        }

        conflictMap.set(key, existing);
      });

      // Convert to array and sort by severity and count
      const conflictArray = Array.from(conflictMap.values()).sort((a, b) => {
        const severityOrder = { high: 3, medium: 2, low: 1 };
        const severityDiff = (severityOrder[b.severity as keyof typeof severityOrder] || 0) - (severityOrder[a.severity as keyof typeof severityOrder] || 0);
        if (severityDiff !== 0) return severityDiff;
        return b.count - a.count;
      });

      setConflictData(conflictArray);
    } catch (error) {
      console.error('Error fetching conflict data:', error);
      setConflictData([]);
    }
  };

  const fetchActivityLogs = async () => {
    try {
      const { data: logs, error } = await supabase
        .from('timetable_audit_log')
        .select('id, action, description, created_at, user_id')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        console.warn('Error fetching activity logs:', error);
        setActivityLogs([]);
        return;
      }

      setActivityLogs(logs || []);
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      setActivityLogs([]);
    }
  };

  const fetchRoomUtilization = async () => {
    try {
      // Fetch room usage data from timetable slots
      const [
        { data: facultyTimetables, error: ftError },
        { data: labSlots, error: labError },
        { data: theorySlots, error: theoryError }
      ] = await Promise.all([
        supabase.from('faculty_timetables').select('room_number, department'),
        supabase.from('lab_time_slots').select('room_number, department'),
        supabase.from('timetable_slots').select('room_number, department')
      ]);

      if (ftError) console.warn('Error fetching faculty timetables for rooms:', ftError);
      if (labError) console.warn('Error fetching lab slots for rooms:', labError);
      if (theoryError) console.warn('Error fetching theory slots for rooms:', theoryError);

      const roomMap = new Map();

      // Process all room assignments
      const allRoomData = [
        ...(facultyTimetables || []),
        ...(labSlots || []),
        ...(theorySlots || [])
      ];

      allRoomData.forEach(slot => {
        if (slot.room_number) {
          const key = slot.room_number;
          const existing = roomMap.get(key) || {
            room: key,
            department: slot.department || 'Unknown',
            totalSlots: 0,
            utilizationPercentage: 0
          };
          existing.totalSlots += 1;
          roomMap.set(key, existing);
        }
      });

      // Calculate utilization percentage (assuming 35 slots per week max per room)
      const roomUtilizationData = Array.from(roomMap.values()).map(room => ({
        ...room,
        utilizationPercentage: Math.round((room.totalSlots / 35) * 100)
      })).sort((a, b) => b.utilizationPercentage - a.utilizationPercentage);

      setRoomUtilization(roomUtilizationData.slice(0, 10)); // Top 10 most utilized rooms
    } catch (error) {
      console.error('Error fetching room utilization:', error);
      setRoomUtilization([]);
    }
  };

  const fetchTimeSlotAnalysis = async () => {
    try {
      // Fetch time slot usage data
      const [
        { data: facultyTimetables, error: ftError },
        { data: labSlots, error: labError },
        { data: theorySlots, error: theoryError }
      ] = await Promise.all([
        supabase.from('faculty_timetables').select('time_slot, department'),
        supabase.from('lab_time_slots').select('time_slot, department'),
        supabase.from('timetable_slots').select('time_slot, department')
      ]);

      if (ftError) console.warn('Error fetching faculty timetables for time analysis:', ftError);
      if (labError) console.warn('Error fetching lab slots for time analysis:', labError);
      if (theoryError) console.warn('Error fetching theory slots for time analysis:', theoryError);

      const timeSlotMap = new Map();

      // Process all time slot assignments
      const allTimeData = [
        ...(facultyTimetables || []),
        ...(labSlots || []),
        ...(theorySlots || [])
      ];

      allTimeData.forEach(slot => {
        if (slot.time_slot) {
          const key = slot.time_slot;
          const existing = timeSlotMap.get(key) || {
            timeSlot: key,
            totalBookings: 0,
            departments: new Set(),
            utilizationRate: 0
          };
          existing.totalBookings += 1;
          if (slot.department) existing.departments.add(slot.department);
          timeSlotMap.set(key, existing);
        }
      });

      // Convert to array and calculate utilization rate
      const timeSlotData = Array.from(timeSlotMap.values()).map(slot => ({
        timeSlot: slot.timeSlot,
        totalBookings: slot.totalBookings,
        departments: Array.from(slot.departments) as string[],
        utilizationRate: Math.round((slot.totalBookings / 20) * 100) // Assuming 20 max bookings per time slot
      })).sort((a, b) => b.utilizationRate - a.utilizationRate);

      setTimeSlotAnalysis(timeSlotData);
    } catch (error) {
      console.error('Error fetching time slot analysis:', error);
      setTimeSlotAnalysis([]);
    }
  };

  // Quick action handlers
  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'add-mapping':
        navigate('/subjects');
        break;
      case 'generate-timetable':
        navigate('/timetable');
        break;
      case 'view-faculty':
        navigate('/timetable');
        break;
      case 'settings':
        navigate('/faculty-availability-reset');
        break;
      default:
        break;
    }
  };

  // Chart colors
  const CHART_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

  if (loading) {
    return (
      <ModernLayout
        title="Timetable Coordinator Dashboard"
        description="Loading comprehensive timetable management and analytics..."
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <ModernLoading size="lg" text="Loading dashboard data..." />
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout
      title="Timetable Coordinator Dashboard"
      description={
        <div className="flex items-center gap-4">
          <span>Comprehensive timetable management and analytics</span>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span className="text-xs text-muted-foreground">Live</span>
          </div>
          <span className="text-xs text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </span>
        </div>
      }
      actions={
        <ModernButtonGroup>
          <Button
            onClick={() => handleQuickAction('add-mapping')}
            className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Subject
          </Button>
          <Button
            onClick={() => handleQuickAction('generate-timetable')}
            variant="success"
          >
            <Play className="w-4 h-4 mr-2" />
            Generate
          </Button>
          <Button
            onClick={() => handleQuickAction('view-faculty')}
            variant="outline"
          >
            <Eye className="w-4 h-4 mr-2" />
            View Faculty
          </Button>
          <Button
            onClick={() => handleQuickAction('settings')}
            variant="outline"
            size="icon"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </ModernButtonGroup>
      }
    >

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Activity
          </TabsTrigger>
          <TabsTrigger value="conflicts" className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4" />
            Conflicts
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-8 animate-fade-in">
          {/* Key Metrics Cards */}
          <ModernGrid cols={4} gap="lg">
            <DashboardCard
              title="Total Departments"
              value={stats.totalDepartments}
              icon={Book}
              description="Active academic departments"
              colorClass="text-primary bg-primary/10"
              trend={{ value: 12, isPositive: true }}
              interactive
            />
            <DashboardCard
              title="Faculty Members"
              value={stats.facultyMembers}
              icon={Users}
              description="Active teaching personnel"
              colorClass="text-success bg-success/10"
              trend={{ value: 8, isPositive: true }}
              interactive
            />
            <DashboardCard
              title="Subject Allotments"
              value={stats.subjectAllotments}
              icon={FileText}
              description="Total subject mappings"
              colorClass="text-warning bg-warning/10"
              trend={{ value: 15, isPositive: true }}
              interactive
            />
            <DashboardCard
              title="Total Slots"
              value={stats.totalSlots}
              icon={Calendar}
              description="Lab + Theory slots scheduled"
              colorClass="text-info bg-info/10"
              trend={{ value: 22, isPositive: true }}
              interactive
            />
          </ModernGrid>

          {/* Enhanced Metrics with Progress */}
          <ModernGrid cols={3} gap="lg" className="animate-slide-up" style={{ animationDelay: '0.1s' }}>
            <ModernCard
              title="Faculty Utilization"
              description="Faculty members with assigned slots"
              elevated
              interactive
            >
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="text-3xl font-bold text-success">
                    {`${stats.facultyUtilization}%`}
                  </div>
                  <div className="p-2 bg-success/10 rounded-lg">
                    <CheckCircle className="w-6 h-6 text-success" />
                  </div>
                </div>
                <Progress value={stats.facultyUtilization} className="h-3" />
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Optimal utilization range: 70-85%</span>
                </div>
              </div>
            </ModernCard>

            <ModernCard
              title="Timetable Completion"
              description="Overall scheduling progress"
              elevated
              interactive
            >
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="text-3xl font-bold text-primary">
                    {`${stats.completionPercentage}%`}
                  </div>
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <BarChart3 className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <Progress value={stats.completionPercentage} className="h-3" />
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Target completion: 95%</span>
                </div>
              </div>
            </ModernCard>

            <ModernCard
              title="Pending Conflicts"
              description="Requires immediate attention"
              elevated
              interactive
            >
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="text-3xl font-bold text-destructive">
                    {stats.pendingConflicts}
                  </div>
                  <div className="p-2 bg-destructive/10 rounded-lg">
                    <AlertCircle className="w-6 h-6 text-destructive" />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Badge variant="destructive" className="text-xs">High: 2</Badge>
                  <Badge variant="warning" className="text-xs">Medium: 1</Badge>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="w-2 h-2 bg-destructive rounded-full"></div>
                  <span>Auto-resolution available</span>
                </div>
              </div>
            </ModernCard>
          </ModernGrid>

          {/* Slot Distribution and System Status */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="p-6 bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-950 dark:to-gray-950">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="w-5 h-5 text-indigo-600" />
                  Slot Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-primary rounded-full"></div>
                      <span className="text-sm font-medium">Theory Slots</span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{stats.theorySlots}</div>
                      <div className="text-xs text-muted-foreground">
                        {stats.totalSlots > 0 ? Math.round((stats.theorySlots / stats.totalSlots) * 100) : 0}%
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-accent rounded-full"></div>
                      <span className="text-sm font-medium">Lab Slots</span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{stats.labSlots}</div>
                      <div className="text-xs text-muted-foreground">
                        {stats.totalSlots > 0 ? Math.round((stats.labSlots / stats.totalSlots) * 100) : 0}%
                      </div>
                    </div>
                  </div>
                  <div className="pt-2 border-t">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Total Scheduled</span>
                      <span className="font-bold text-lg">{stats.totalSlots}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950 dark:to-teal-950">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5 text-emerald-600" />
                  System Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Timetable Engine</span>
                      <Badge variant="success" className="text-xs">Operational</Badge>
                    </div>
                    <Progress value={100} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Conflict Detection</span>
                      <Badge variant="success" className="text-xs">Active</Badge>
                    </div>
                    <Progress value={95} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Data Sync</span>
                      <Badge variant="warning" className="text-xs">Syncing</Badge>
                    </div>
                    <Progress value={75} className="h-2" />
                  </div>
                  <div className="pt-2 border-t">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-muted-foreground">Overall Health</span>
                      <span className="font-medium text-green-600">Excellent</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Faculty Workload Chart */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-blue-600" />
                  Faculty Workload Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    totalSlots: {
                      label: "Total Slots",
                      color: "hsl(var(--chart-1))",
                    },
                    labSlots: {
                      label: "Lab Slots",
                      color: "hsl(var(--chart-2))",
                    },
                    theorySlots: {
                      label: "Theory Slots",
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <BarChart data={facultyWorkload}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="totalSlots" fill={CHART_COLORS[0]} name="Total Slots" />
                    <Bar dataKey="labSlots" fill={CHART_COLORS[1]} name="Lab Slots" />
                    <Bar dataKey="theorySlots" fill={CHART_COLORS[2]} name="Theory Slots" />
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="w-5 h-5 text-purple-600" />
                  Department Completion Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    percentage: {
                      label: "Completion %",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <PieChart>
                    <Pie
                      data={departmentCompletion}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="percentage"
                      label={({ department, percentage }) => `${department}: ${percentage}%`}
                    >
                      {departmentCompletion.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                      ))}
                    </Pie>
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </PieChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          {/* Faculty Availability Overview */}
          <Card className="p-6">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5 text-green-600" />
                Faculty Availability Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {facultyWorkload.slice(0, 6).map((faculty, index) => (
                  <div key={index} className="p-4 border rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-sm">{faculty.name}</h4>
                        <p className="text-xs text-muted-foreground">{faculty.department}</p>
                      </div>
                      <Badge
                        variant={faculty.availability > 70 ? "success" : faculty.availability > 40 ? "warning" : "destructive"}
                        className="text-xs"
                      >
                        {faculty.availability}%
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span>Availability</span>
                        <span>{faculty.availability}%</span>
                      </div>
                      <Progress value={faculty.availability} className="h-2" />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Lab: {faculty.labSlots}</span>
                        <span>Theory: {faculty.theorySlots}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Conflict Analysis */}
          <Card className="p-6">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                Conflict Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                {conflictData.map((conflict, index) => (
                  <div key={index} className={`p-4 border rounded-lg ${
                    conflict.severity === 'high' ? 'bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800' :
                    conflict.severity === 'medium' ? 'bg-amber-50 border-amber-200 dark:bg-amber-950 dark:border-amber-800' :
                    'bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800'
                  }`}>
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium text-sm">{conflict.type}</h4>
                      <Badge
                        variant={conflict.severity === 'high' ? 'destructive' : conflict.severity === 'medium' ? 'warning' : 'secondary'}
                        className="text-xs"
                      >
                        {conflict.severity.toUpperCase()}
                      </Badge>
                    </div>
                    <div className="text-2xl font-bold mb-1">{conflict.count}</div>
                    <p className="text-xs text-muted-foreground">
                      {conflict.severity === 'high' ? 'Immediate action required' :
                       conflict.severity === 'medium' ? 'Should be resolved soon' :
                       'Monitor and resolve when possible'}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Room Utilization Analysis */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <Book className="w-5 h-5 text-orange-600" />
                  Room Utilization Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                {roomUtilization.length > 0 ? (
                  <div className="space-y-3">
                    {roomUtilization.slice(0, 8).map((room, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-lg">
                        <div>
                          <h4 className="font-medium text-sm">Room {room.room}</h4>
                          <p className="text-xs text-muted-foreground">{room.department} • {room.totalSlots} slots</p>
                        </div>
                        <div className="text-right">
                          <Badge
                            variant={room.utilizationPercentage > 80 ? "destructive" : room.utilizationPercentage > 60 ? "warning" : "success"}
                            className="text-xs"
                          >
                            {room.utilizationPercentage}%
                          </Badge>
                          <Progress value={room.utilizationPercentage} className="w-20 h-2 mt-1" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Book className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">No room utilization data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5 text-indigo-600" />
                  Time Slot Efficiency
                </CardTitle>
              </CardHeader>
              <CardContent>
                {timeSlotAnalysis.length > 0 ? (
                  <div className="space-y-3">
                    {timeSlotAnalysis.slice(0, 8).map((slot, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-lg">
                        <div>
                          <h4 className="font-medium text-sm">{slot.timeSlot}</h4>
                          <p className="text-xs text-muted-foreground">
                            {slot.totalBookings} bookings • {slot.departments.length} dept{slot.departments.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge
                            variant={slot.utilizationRate > 80 ? "destructive" : slot.utilizationRate > 60 ? "warning" : "success"}
                            className="text-xs"
                          >
                            {slot.utilizationRate}%
                          </Badge>
                          <Progress value={slot.utilizationRate} className="w-20 h-2 mt-1" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">No time slot data available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5 text-blue-600" />
                  Recent System Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activityLogs.length > 0 ? (
                    activityLogs.slice(0, 10).map((log, index) => {
                      // Determine color based on action type
                      const getActionColor = (action: string) => {
                        if (action.toLowerCase().includes('generate') || action.toLowerCase().includes('create')) return 'bg-green-600 dark:bg-green-400';
                        if (action.toLowerCase().includes('update') || action.toLowerCase().includes('modify')) return 'bg-primary';
                        if (action.toLowerCase().includes('delete') || action.toLowerCase().includes('remove')) return 'bg-red-600 dark:bg-red-400';
                        if (action.toLowerCase().includes('conflict') || action.toLowerCase().includes('resolve')) return 'bg-yellow-600 dark:bg-yellow-400';
                        if (action.toLowerCase().includes('mapping') || action.toLowerCase().includes('assign')) return 'bg-secondary';
                        return 'bg-teal-600 dark:bg-teal-400';
                      };

                      // Format time ago
                      const formatTimeAgo = (dateString: string) => {
                        const date = new Date(dateString);
                        const now = new Date();
                        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

                        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
                        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
                        return `${Math.floor(diffInMinutes / 1440)}d ago`;
                      };

                      return (
                        <div key={log.id} className={`flex items-start gap-3 ${index < activityLogs.length - 1 ? 'pb-3 border-b' : ''}`}>
                          <div className={`w-2 h-2 ${getActionColor(log.action)} rounded-full mt-2`}></div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium text-sm">{log.action}</p>
                                {log.description && (
                                  <p className="text-xs text-muted-foreground">{log.description}</p>
                                )}
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {formatTimeAgo(log.created_at)}
                              </span>
                            </div>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center py-8">
                      <Activity className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-sm text-muted-foreground">No recent activity found</p>
                      <p className="text-xs text-muted-foreground mt-1">Activity will appear here as you use the system</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">Active Timetables</p>
                      <p className="text-xs text-muted-foreground">Currently scheduled</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{stats.activeTimetables}</div>
                      <div className="text-xs text-green-600 dark:text-green-400">Departments active</div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">Faculty Utilization</p>
                      <p className="text-xs text-muted-foreground">With assigned slots</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{stats.facultyUtilization}%</div>
                      <div className="text-xs text-primary/80">{stats.facultyMembers} total faculty</div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">Completion Rate</p>
                      <p className="text-xs text-muted-foreground">Overall progress</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-purple-600">{stats.completionPercentage}%</div>
                      <div className="text-xs text-secondary/80">{stats.totalSlots} slots scheduled</div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-amber-50 dark:bg-amber-950 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">Pending Conflicts</p>
                      <p className="text-xs text-muted-foreground">Requiring attention</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-amber-600">{stats.pendingConflicts}</div>
                      <div className="text-xs text-yellow-600 dark:text-yellow-400">{stats.pendingConflicts === 0 ? 'All clear' : 'Needs review'}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="conflicts" className="space-y-6">
          {/* Conflict Summary */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="p-4 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950 dark:to-pink-950 border-red-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 dark:bg-red-900 rounded-full">
                  <XCircle className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-red-700 dark:text-red-300">Critical Issues</p>
                  <p className="text-2xl font-bold text-red-800 dark:text-red-200">2</p>
                </div>
              </div>
            </Card>
            <Card className="p-4 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950 dark:to-orange-950 border-amber-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-amber-100 dark:bg-amber-900 rounded-full">
                  <AlertCircle className="w-5 h-5 text-amber-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-amber-700 dark:text-amber-300">Warnings</p>
                  <p className="text-2xl font-bold text-amber-800 dark:text-amber-200">1</p>
                </div>
              </div>
            </Card>
            <Card className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 border-green-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-full">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-green-700 dark:text-green-300">Resolved Today</p>
                  <p className="text-2xl font-bold text-green-800 dark:text-green-200">5</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Detailed Conflicts */}
          <Card className="p-6">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                Active Conflicts Requiring Attention
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Alert className="border-red-200 bg-red-50 dark:bg-red-950">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800 dark:text-red-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">Faculty Double Booking - Critical</p>
                        <p className="text-sm mt-1">Dr. Johnson is scheduled for Machine Learning (6A) and Data Structures (5B) at the same time slot (10:30-11:25 AM, Monday)</p>
                        <p className="text-xs mt-2 text-red-600">Detected: 2 hours ago • Auto-resolution failed</p>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Button size="sm" variant="outline" className="text-red-600 border-red-300">
                          Resolve
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-600 border-red-300">
                          Details
                        </Button>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>

                <Alert className="border-red-200 bg-red-50 dark:bg-red-950">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800 dark:text-red-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">Room Assignment Conflict - Critical</p>
                        <p className="text-sm mt-1">Room 301 is assigned to both Computer Networks Lab (CSE-6A) and Software Engineering (CSE-6B) on Tuesday 2:15-4:00 PM</p>
                        <p className="text-xs mt-2 text-red-600">Detected: 4 hours ago • Requires manual intervention</p>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Button size="sm" variant="outline" className="text-red-600 border-red-300">
                          Resolve
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-600 border-red-300">
                          Details
                        </Button>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>

                <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-950">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800 dark:text-amber-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">Faculty Workload Warning - Medium</p>
                        <p className="text-sm mt-1">Dr. Williams has 18 slots this week, exceeding the recommended 15-slot limit</p>
                        <p className="text-xs mt-2 text-amber-600">Detected: 1 day ago • Consider redistributing load</p>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Button size="sm" variant="outline" className="text-amber-600 border-amber-300">
                          Review
                        </Button>
                        <Button size="sm" variant="outline" className="text-amber-600 border-amber-300">
                          Redistribute
                        </Button>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>

          {/* Conflict Resolution Tools */}
          <Card className="p-6">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5 text-blue-600" />
                Conflict Resolution Tools
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2 border-blue-200 hover:bg-blue-50"
                  onClick={() => handleQuickAction('generate-timetable')}
                >
                  <Play className="w-6 h-6 text-blue-600" />
                  <span className="text-sm font-medium">Auto-Resolve</span>
                  <span className="text-xs text-muted-foreground">Run conflict resolution</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2 border-purple-200 hover:bg-purple-50"
                  onClick={() => handleQuickAction('view-faculty')}
                >
                  <Eye className="w-6 h-6 text-purple-600" />
                  <span className="text-sm font-medium">View Details</span>
                  <span className="text-xs text-muted-foreground">Analyze conflicts</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2 border-green-200 hover:bg-green-50"
                  onClick={() => handleQuickAction('add-mapping')}
                >
                  <Plus className="w-6 h-6 text-green-600" />
                  <span className="text-sm font-medium">Manual Fix</span>
                  <span className="text-xs text-muted-foreground">Edit mappings</span>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2 border-amber-200 hover:bg-amber-50"
                  onClick={() => handleQuickAction('settings')}
                >
                  <Settings className="w-6 h-6 text-amber-600" />
                  <span className="text-sm font-medium">Settings</span>
                  <span className="text-xs text-muted-foreground">Configure rules</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </ModernLayout>
  );
};

export default TimetableCoordinatorDashboard;
