import { supabase } from "@/integrations/supabase/client";

async function executeQuery(query: string): Promise<void> {
  console.log(`Executing: ${query.substring(0, 100)}...`);
  
  try {
    const result = await supabase.rpc('execute_sql', {
      sql_query: query
    });
    
    if (result.error) {
      throw result.error;
    }
    
    console.log('✅ Query executed successfully');
  } catch (error) {
    console.error('❌ Query failed:', error);
    throw error;
  }
}

async function installFeedbackSystem() {
  try {
    console.log("🚀 Installing Student Faculty Feedback System...");
    
    // Step 1: Create feedback_questions table
    console.log("📋 Step 1: Creating feedback_questions table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS feedback_questions (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          question_number INTEGER NOT NULL UNIQUE,
          question_text TEXT NOT NULL,
          category TEXT NOT NULL,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Step 2: Insert the 10 standardized questions
    console.log("📝 Step 2: Inserting standardized feedback questions...");
    await executeQuery(`
      INSERT INTO feedback_questions (question_number, question_text, category) VALUES
      (1, 'How clearly did the faculty explain technical concepts and subject matter?', 'Teaching Clarity'),
      (2, 'How well did the faculty demonstrate mastery and depth of the subject?', 'Subject Knowledge'),
      (3, 'How effectively did the faculty communicate ideas and respond to students during the class?', 'Communication'),
      (4, 'How supportive was the faculty in encouraging students to ask questions and clear doubts?', 'Student Engagement'),
      (5, 'How effectively did the faculty use real-life examples, analogies, or practical applications related to engineering?', 'Practical Application'),
      (6, 'How well did the faculty manage the classroom, maintain discipline, and ensure a positive learning environment?', 'Classroom Management'),
      (7, 'How regular and punctual was the faculty, and how well was the class time utilized?', 'Time Management'),
      (8, 'How fair and transparent was the faculty in evaluating assignments, internals, or lab work?', 'Evaluation Fairness'),
      (9, 'How well did the faculty motivate students and provide academic or career-related guidance?', 'Motivation & Guidance'),
      (10, 'How effectively did the faculty promote critical thinking, technical problem-solving, or innovation during the course?', 'Analytical Thinking')
      ON CONFLICT (question_number) DO NOTHING;
    `);

    // Step 3: Create feedback_sessions table
    console.log("📅 Step 3: Creating feedback_sessions table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS feedback_sessions (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          session_name TEXT NOT NULL,
          academic_year TEXT NOT NULL,
          department TEXT NOT NULL,
          semester TEXT,
          section TEXT,
          start_date TIMESTAMP WITH TIME ZONE NOT NULL,
          end_date TIMESTAMP WITH TIME ZONE NOT NULL,
          is_active BOOLEAN DEFAULT true,
          created_by UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Step 4: Create student_feedback_responses table
    console.log("👨‍🎓 Step 4: Creating student_feedback_responses table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS student_feedback_responses (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          session_id UUID NOT NULL REFERENCES feedback_sessions(id) ON DELETE CASCADE,
          
          -- Student identification (references existing student data)
          student_usn TEXT NOT NULL, -- References class_students.usn
          student_name TEXT NOT NULL,
          department TEXT NOT NULL,
          semester TEXT NOT NULL,
          section TEXT NOT NULL,
          
          -- Faculty and subject details
          faculty_id UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
          subject_code TEXT NOT NULL,
          subject_name TEXT NOT NULL,
          
          -- Response data (JSON array of question responses)
          responses JSONB NOT NULL,
          
          -- Submission tracking
          is_submitted BOOLEAN DEFAULT false,
          submitted_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          
          -- Ensure one response per student per faculty per session
          UNIQUE(session_id, student_usn, faculty_id, subject_code)
      );
    `);

    // Step 5: Create feedback_reports table
    console.log("📊 Step 5: Creating feedback_reports table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS feedback_reports (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          session_id UUID NOT NULL REFERENCES feedback_sessions(id) ON DELETE CASCADE,
          
          -- Faculty and subject details
          faculty_id UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
          faculty_name TEXT NOT NULL,
          subject_code TEXT NOT NULL,
          subject_name TEXT NOT NULL,
          department TEXT NOT NULL,
          semester TEXT NOT NULL,
          section TEXT NOT NULL,
          
          -- Aggregated data
          total_responses INTEGER NOT NULL DEFAULT 0,
          average_ratings JSONB NOT NULL, -- Array of question averages
          overall_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
          anonymized_responses JSONB NOT NULL, -- Array of anonymized student responses
          
          -- Generation tracking
          generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          generated_by UUID REFERENCES employee_details(id),
          
          -- Ensure one report per faculty per subject per session
          UNIQUE(session_id, faculty_id, subject_code)
      );
    `);

    console.log("🔗 Step 6: Creating indexes for performance...");
    
    // Performance indexes
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_feedback_sessions_department_active 
      ON feedback_sessions(department, is_active, start_date, end_date);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_student_responses_session_student 
      ON student_feedback_responses(session_id, student_usn, is_submitted);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_student_responses_faculty_subject 
      ON student_feedback_responses(faculty_id, subject_code, department, semester);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_feedback_reports_session_department 
      ON feedback_reports(session_id, department, semester, section);
    `);

    console.log("🔒 Step 7: Setting up Row Level Security (RLS)...");
    
    // Enable RLS on all tables
    await executeQuery(`
      ALTER TABLE feedback_questions ENABLE ROW LEVEL SECURITY;
      ALTER TABLE feedback_sessions ENABLE ROW LEVEL SECURITY;
      ALTER TABLE student_feedback_responses ENABLE ROW LEVEL SECURITY;
      ALTER TABLE feedback_reports ENABLE ROW LEVEL SECURITY;
    `);

    console.log("✅ Student Faculty Feedback System installation completed!");
    console.log("");
    console.log("📊 Summary of new tables created:");
    console.log("  • feedback_questions - 10 standardized evaluation questions");
    console.log("  • feedback_sessions - HOD-triggered feedback collection periods");
    console.log("  • student_feedback_responses - Individual student feedback submissions");
    console.log("  • feedback_reports - Aggregated reports with anonymized data");
    console.log("");
    console.log("🔗 All tables reference existing schema:");
    console.log("  • employee_details (faculty_id, created_by)");
    console.log("  • class_students (student_usn)");
    console.log("");
    console.log("🔒 Row Level Security enabled for data protection");
    console.log("📈 Performance indexes created for optimal query performance");

  } catch (error) {
    console.error("❌ Installation failed:", error);
    throw error;
  }
}

// Export for use in other scripts or direct execution
export { installFeedbackSystem };

// Allow direct execution
if (require.main === module) {
  installFeedbackSystem()
    .then(() => {
      console.log("🎉 Installation completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Installation failed:", error);
      process.exit(1);
    });
}
