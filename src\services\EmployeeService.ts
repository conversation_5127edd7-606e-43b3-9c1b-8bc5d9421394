
import { supabase } from "@/integrations/supabase/client";
import * as XLSX from 'xlsx';

export interface Employee {
  id: string; // Changed from optional to required to match UserService.Employee
  full_name: string;
  email: string;
  department?: string;
  designation?: string;
  phone?: string;
  created_at?: string;
  updated_at?: string;
}

export class EmployeeService {
  static async getEmployees(): Promise<Employee[]> {
    const { data, error } = await supabase
      .from('employee_details')
      .select('*')
      .order('full_name', { ascending: true });
      
    if (error) throw error;
    return data || [];
  }
  
  static async addEmployee(employee: Partial<Employee>): Promise<Employee> {
    const { data, error } = await supabase
      .from('employee_details')
      .insert({
        full_name: employee.full_name,
        email: employee.email,
        department: employee.department,
        designation: employee.designation,
        phone: employee.phone,
      })
      .select();
    
    if (error) throw error;
    return data[0];
  }
  
  static async addMultipleEmployees(employees: Partial<Employee>[]): Promise<{ success: Employee[]; failed: Partial<Employee>[] }> {
    const successfulEntries: Employee[] = [];
    const failedEntries: Partial<Employee>[] = [];

    for (const employee of employees) {
      try {
        const addedEmployee = await this.addEmployee(employee);
        successfulEntries.push(addedEmployee);
      } catch (error) {
        console.error(`Error adding employee ${employee.full_name}:`, error);
        failedEntries.push(employee);
      }
    }

    return { success: successfulEntries, failed: failedEntries };
  }
  
  static async updateEmployee(employee: Employee): Promise<Employee> {
    const { data, error } = await supabase
      .from('employee_details')
      .update({
        full_name: employee.full_name,
        email: employee.email,
        department: employee.department,
        designation: employee.designation,
        phone: employee.phone,
      })
      .eq('id', employee.id!)
      .select();
    
    if (error) throw error;
    return data[0];
  }
  
  static async deleteEmployee(id: string): Promise<void> {
    const { error } = await supabase
      .from('employee_details')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
  
  static parseExcelFile(file: File): Promise<Partial<Employee>[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = e.target?.result;
          const workbook = XLSX.read(data, { type: 'binary' });
          const sheet = workbook.Sheets[workbook.SheetNames[0]];
          const rows = XLSX.utils.sheet_to_json<any>(sheet, { defval: null });
          
          // Map Excel columns to our Employee interface
          const employees = rows.map(row => {
            return {
              full_name: row['Full Name'] || '',
              designation: row['Designation'] || '',
              department: row['Department'] || '',
              email: row['Email Id'] || '',
              phone: row['Phone'] || '',
            };
          });
          
          resolve(employees);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = (error) => {
        reject(error);
      };
      
      reader.readAsBinaryString(file);
    });
  }
}
