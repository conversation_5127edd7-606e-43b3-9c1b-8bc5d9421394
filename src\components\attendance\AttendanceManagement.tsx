import React, { memo } from 'react';
import { Building2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useAuth } from '@/contexts/AuthContext';
import ManualAttendance from './ManualAttendance';

// CRITICAL FIX: Memoize the component to prevent unnecessary re-renders
const AttendanceManagement: React.FC = memo(() => {
  const { user } = useAuth();

  // Get user's department information
  const { department, departmentName, fullName, loading: departmentLoading, error: departmentError } = useUserDepartment();

  // Show loading state while fetching department info
  if (departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading department information...</span>
        </div>
      </div>
    );
  }

  // Show error if department info couldn't be loaded
  if (departmentError || !department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {departmentError || 'Unable to load your department information. Please contact administrator to set your department.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Building2 className="h-6 w-6 text-primary" />
        <h1 className="text-3xl font-bold">Attendance Management</h1>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Faculty:</strong> {fullName} | <strong>Department:</strong> {departmentName}
        </AlertDescription>
      </Alert>

      <ManualAttendance />
    </div>
  );
});

// CRITICAL FIX: Add display name for debugging
AttendanceManagement.displayName = 'AttendanceManagement';

export default AttendanceManagement;
