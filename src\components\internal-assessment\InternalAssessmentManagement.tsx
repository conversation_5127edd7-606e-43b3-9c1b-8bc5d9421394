import React, { useState, useEffect, memo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { InternalAssessmentService, IASession } from '@/services/InternalAssessmentService';
import { ClipboardCheck, Building2, AlertCircle, Save, Users, BookOpen, RefreshCw, Eye } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useAuth } from '@/contexts/AuthContext';
import { Input } from '@/components/ui/input';
import { FacultyAssignmentService } from '@/services/FacultyAssignmentService';
import { FacultyStudentListService } from '@/services/FacultyStudentListService';

// Enhanced interfaces for auto-populated functionality
interface FacultySubject {
  subject_code: string;
  subject_name: string;
  subject_type: 'theory' | 'laboratory' | 'elective';
  semester: string;
  section: string;
  batch_name?: string;
  display_name: string;
  key: string;
}

interface EnhancedIASession extends IASession {
  subject_type: 'theory' | 'laboratory' | 'elective';
  batch_name?: string;
  max_ia_marks: number;
  max_assignment_marks: number;
  max_theory_marks?: number; // Changed from max_lab_marks to match database schema
}

// CRITICAL FIX: Memoize the component to prevent unnecessary re-renders
const InternalAssessmentManagement: React.FC = memo(() => {
  const [selectedSubjectKey, setSelectedSubjectKey] = useState<string>('');
  const [iaSession, setIaSession] = useState<EnhancedIASession | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [facultySubjects, setFacultySubjects] = useState<FacultySubject[]>([]);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Get user's department information
  const { department, departmentName, fullName, loading: departmentLoading, error: departmentError } = useUserDepartment();

  // Auto-load faculty subjects when component mounts
  useEffect(() => {
    if (department && user?.id && !departmentLoading) {
      loadFacultySubjects();
    }
  }, [department, user?.id, departmentLoading]);

  // Enhanced auto-population of faculty subjects
  const loadFacultySubjects = useCallback(async () => {
    if (!user?.id || !department) return;

    try {
      setAssignmentsLoading(true);
      console.log('🔄 Loading faculty subjects for IA management...');

      // Get faculty assignments from the same source as analytics dashboard
      const assignments = await FacultyAssignmentService.getFacultyAssignments(user.id, department);

      console.log('📊 Faculty assignments loaded:', {
        totalAssignments: assignments.assignments.length,
        subjects: assignments.subjects.length
      });

      // Transform assignments to faculty subjects with enhanced display format
      const transformedSubjects: FacultySubject[] = assignments.assignments.map(assignment => {
        const displayName = `${assignment.subject_code} - ${assignment.subject_name} (Sem ${assignment.semester} - Sec ${assignment.section})`;
        const key = `${assignment.subject_code}-${assignment.semester}-${assignment.section}-${assignment.subject_type}`;

        return {
          subject_code: assignment.subject_code,
          subject_name: assignment.subject_name,
          subject_type: assignment.subject_type as 'theory' | 'laboratory' | 'elective',
          semester: assignment.semester,
          section: assignment.section,
          display_name: displayName,
          key: key
        };
      });

      // Sort subjects by semester, then section, then subject code
      transformedSubjects.sort((a, b) => {
        if (a.semester !== b.semester) return parseInt(a.semester) - parseInt(b.semester);
        if (a.section !== b.section) return a.section.localeCompare(b.section);
        return a.subject_code.localeCompare(b.subject_code);
      });

      setFacultySubjects(transformedSubjects);

      console.log('✅ Faculty subjects loaded for IA:', transformedSubjects.length);

      if (transformedSubjects.length === 0) {
        toast({
          title: 'No Subjects Found',
          description: 'No teaching assignments found for your account. Please contact administrator.',
          variant: 'default',
        });
      }
    } catch (error) {
      console.error('Error loading faculty subjects:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your teaching assignments.',
        variant: 'destructive',
      });
    } finally {
      setAssignmentsLoading(false);
    }
  }, [user?.id, department, toast]);

  // Enhanced IA session loading with auto-populated student lists
  const loadIASession = useCallback(async () => {
    if (!user?.id || !department || !selectedSubjectKey) {
      toast({
        title: 'Missing Information',
        description: 'Please select a subject.',
        variant: 'default',
      });
      return;
    }

    // Find the selected subject
    const selectedSubject = facultySubjects.find(s => s.key === selectedSubjectKey);
    if (!selectedSubject) {
      toast({
        title: 'Subject Not Found',
        description: 'Selected subject not found in your assignments.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);
      console.log('🔄 Loading IA session for:', selectedSubject);

      // Get current academic year
      const currentYear = new Date().getFullYear();
      const academicYear = `${currentYear}-${currentYear + 1}`;

      console.log('📋 IA Session Parameters:', {
        facultyId: user.id,
        department,
        subjectCode: selectedSubject.subject_code,
        semester: selectedSubject.semester,
        section: selectedSubject.section,
        academicYear,
        subjectType: selectedSubject.subject_type
      });

      // Use dynamic loading to get appropriate students based on subject type and teaching assignments
      const session = await InternalAssessmentService.getStudentsForIAWithDynamicLoading(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear
      );

      console.log('📊 IA Session Result:', {
        studentsCount: session.students.length,
        subjectName: session.subject_name,
        sampleStudent: session.students[0]
      });

      // Create enhanced IA session with subject type and corrected marks configuration
      const enhancedSession: EnhancedIASession = {
        ...session,
        subject_type: selectedSubject.subject_type,
        batch_name: selectedSubject.batch_name,
        max_ia_marks: 25, // IA marks out of 25 each
        max_assignment_marks: 10, // Assignment marks out of 10
        max_theory_marks: 20 // Theory marks out of 20 for all subjects
      };

      setIaSession(enhancedSession);

      // Show info about the student list source and subject type
      if (session.students.length > 0) {
        let sourceInfo = `Successfully loaded ${session.students.length} students for ${selectedSubject.subject_type} subject "${selectedSubject.subject_code}" (Sem ${selectedSubject.semester} - Sec ${selectedSubject.section})`;

        console.log('✅', sourceInfo);

        toast({
          title: 'Students Loaded Successfully',
          description: sourceInfo,
        });
      } else {
        console.error('❌ No students loaded for IA session');

        // Trigger debugging to help identify the issue
        await FacultyStudentListService.debugStudentTables();

        toast({
          title: 'No Students Found',
          description: `No students found for ${selectedSubject.subject_type} subject "${selectedSubject.subject_code}" (Sem ${selectedSubject.semester} - Sec ${selectedSubject.section}).

The system checked both class_students and batch_students tables but found no students for this semester-section combination.

Please contact administrator to upload students for Semester ${selectedSubject.semester}, Section ${selectedSubject.section} in the class_students table.`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('❌ Error loading IA session:', error);

      let errorMessage = 'Failed to load IA session.';

      if (error instanceof Error) {
        if (error.message.includes('access')) {
          errorMessage = `You do not have access to teach this subject. Please contact administrator to verify your teaching assignments.`;
        } else if (error.message.includes('students')) {
          errorMessage = `Unable to load student list. Please verify that students have been uploaded for this class.`;
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: 'Failed to Load Students',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, department, selectedSubjectKey, facultySubjects, toast]);

  // Enhanced marks change handler with validation
  const handleMarksChange = (studentId: string, field: string, value: string) => {
    if (!iaSession) return;

    const numValue = value === '' ? undefined : parseFloat(value);

    // Validate marks based on field and subject type with corrected maximum values
    if (numValue !== undefined) {
      let maxValue = 0;
      switch (field) {
        case 'ia1_marks':
        case 'ia2_marks':
        case 'ia3_marks':
          maxValue = iaSession.max_ia_marks; // 25 marks each
          break;
        case 'assignment_marks':
          maxValue = iaSession.max_assignment_marks; // 10 marks
          break;
        case 'lab_marks':
          maxValue = iaSession.max_lab_marks || 20; // 20 marks for lab subjects
          break;
        default:
          maxValue = 100;
      }

      if (numValue < 0 || numValue > maxValue) {
        toast({
          title: 'Invalid Marks',
          description: `Marks must be between 0 and ${maxValue}`,
          variant: 'destructive',
        });
        return;
      }
    }

    setIaSession({
      ...iaSession,
      students: iaSession.students.map(student =>
        student.id === studentId
          ? { ...student, [field]: numValue }
          : student
      )
    });
  };

  // Enhanced save marks handler with lab marks support
  const handleSaveMarks = async () => {
    if (!iaSession || !user?.id || !department) {
      toast({
        title: 'Error',
        description: 'Missing required information for saving marks.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);

      // Filter students with any marks entered (including lab marks for lab subjects)
      const iaData = iaSession.students
        .filter(student => {
          const hasBasicMarks = student.ia1_marks !== undefined ||
                               student.ia2_marks !== undefined ||
                               student.ia3_marks !== undefined ||
                               student.assignment_marks !== undefined;

          const hasLabMarks = iaSession.subject_type === 'laboratory' &&
                             (student as any).lab_marks !== undefined;

          return hasBasicMarks || hasLabMarks;
        })
        .map(student => ({
          student_id: student.id,
          subject_code: iaSession.subject_code,
          faculty_id: user.id,
          department, // This will be mapped to short code in the service
          semester: iaSession.semester,
          section: iaSession.section,
          academic_year: iaSession.academic_year,
          ia1_marks: student.ia1_marks,
          ia2_marks: student.ia2_marks,
          ia3_marks: student.ia3_marks,
          assignment_marks: student.assignment_marks,
          // Add theory marks for all subjects
          theory_marks: (student as any).theory_marks
        }));

      if (iaData.length === 0) {
        toast({
          title: 'No Changes',
          description: 'Please enter marks for at least one student.',
          variant: 'default',
        });
        return;
      }

      console.log('💾 Saving IA marks:', {
        subjectType: iaSession.subject_type,
        studentsCount: iaData.length,
        hasLabMarks: iaSession.subject_type === 'laboratory'
      });

      await InternalAssessmentService.saveIAMarks(iaData, user.id);

      toast({
        title: 'Success',
        description: `IA marks saved for ${iaData.length} students in ${iaSession.subject_type} subject.`,
      });

      // Reload the session to show updated data
      await loadIASession();
    } catch (error) {
      console.error('Error saving IA marks:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save IA marks.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Show loading state while fetching department info
  if (departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading department information...</span>
        </div>
      </div>
    );
  }

  // Show error if department info couldn't be loaded
  if (departmentError || !department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {departmentError || 'Unable to load your department information. Please contact administrator to set your department.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <ClipboardCheck className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold">Internal Assessment Management</h1>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadFacultySubjects}
              disabled={assignmentsLoading}
            >
              {assignmentsLoading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh Subjects
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => FacultyStudentListService.debugStudentTables()}
            >
              <Eye className="h-4 w-4 mr-2" />
              Debug Tables
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <strong>Faculty:</strong> {fullName} | <strong>Department:</strong> {departmentName}
            </div>
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <BookOpen className="h-4 w-4" />
                <span>{facultySubjects.length} Subjects</span>
              </div>
              {iaSession && (
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{iaSession.students.length} Students</span>
                </div>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Enhanced Auto-populated Subject Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Select Subject for Internal Assessment
          </CardTitle>
          <CardDescription>
            All your teaching assignments are auto-populated. Select a subject to load students and enter IA marks.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {assignmentsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 mx-auto mb-4 animate-spin text-blue-600" />
                <p className="text-muted-foreground">Loading your teaching assignments...</p>
              </div>
            </div>
          ) : facultySubjects.length === 0 ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No teaching assignments found. Please contact administrator to verify your subject assignments.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Your Teaching Subjects ({facultySubjects.length} subjects found)
                </label>
                <Select value={selectedSubjectKey} onValueChange={setSelectedSubjectKey}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a subject to enter IA marks" />
                  </SelectTrigger>
                  <SelectContent>
                    {facultySubjects.map((subject) => (
                      <SelectItem key={subject.key} value={subject.key}>
                        <div className="flex items-center gap-2">
                          <Badge variant={
                            subject.subject_type === 'theory' ? 'default' :
                            subject.subject_type === 'laboratory' ? 'secondary' : 'outline'
                          }>
                            {subject.subject_type.toUpperCase()}
                          </Badge>
                          <span>{subject.display_name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedSubjectKey && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-blue-800">Selected Subject</h4>
                      <p className="text-sm text-blue-600">
                        {facultySubjects.find(s => s.key === selectedSubjectKey)?.display_name}
                      </p>
                    </div>
                    <Button
                      onClick={loadIASession}
                      disabled={loading}
                      className="min-w-[140px]"
                    >
                      {loading ? (
                        <div className="flex items-center gap-2">
                          <RefreshCw className="h-4 w-4 animate-spin" />
                          Loading...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Load Students
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced IA Entry Form with Dynamic Columns */}
      {iaSession && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <CardTitle>Internal Assessment: {iaSession.subject_name}</CardTitle>
                  <Badge variant={
                    iaSession.subject_type === 'theory' ? 'default' :
                    iaSession.subject_type === 'laboratory' ? 'secondary' : 'outline'
                  }>
                    {iaSession.subject_type.toUpperCase()}
                  </Badge>
                </div>
                <CardDescription>
                  Semester {iaSession.semester} Section {iaSession.section} • Academic Year {iaSession.academic_year}
                  {iaSession.batch_name && ` • Batch ${iaSession.batch_name}`}
                </CardDescription>
              </div>
              <Button
                onClick={handleSaveMarks}
                disabled={saving}
                className="min-w-[120px]"
              >
                {saving ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                    Saving...
                  </div>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Marks
                  </>
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Enhanced marks information based on subject type */}
            <div className="text-sm text-gray-600 mb-4 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><strong>Marks Configuration:</strong></p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>IA1, IA2, IA3: Out of {iaSession.max_ia_marks} each</li>
                    <li>Assignment: Out of {iaSession.max_assignment_marks}</li>
                    <li>Theory: Out of {iaSession.max_theory_marks}</li>
                  </ul>
                </div>
                <div>
                  <p><strong>Subject Information:</strong></p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Type: {iaSession.subject_type.charAt(0).toUpperCase() + iaSession.subject_type.slice(1)}</li>
                    <li>Students: {iaSession.students.length}</li>
                    {iaSession.batch_name && <li>Batch: {iaSession.batch_name}</li>}
                  </ul>
                </div>
              </div>
            </div>

            {/* Enhanced marks entry table with dynamic columns */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 p-2 text-left">USN</th>
                    <th className="border border-gray-300 p-2 text-left">Student Name</th>
                    <th className="border border-gray-300 p-2 text-center">IA1 ({iaSession.max_ia_marks})</th>
                    <th className="border border-gray-300 p-2 text-center">IA2 ({iaSession.max_ia_marks})</th>
                    <th className="border border-gray-300 p-2 text-center">IA3 ({iaSession.max_ia_marks})</th>
                    <th className="border border-gray-300 p-2 text-center">Assignment ({iaSession.max_assignment_marks})</th>
                    {/* Dynamic Lab Internal column for laboratory subjects */}
                    {iaSession.subject_type === 'laboratory' && iaSession.max_lab_marks && (
                      <th className="border border-gray-300 p-2 text-center bg-blue-50">
                        Lab Internal ({iaSession.max_lab_marks})
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody>
                  {/* Sort students by USN in ascending order */}
                  {iaSession.students
                    .sort((a, b) => a.roll_number.localeCompare(b.roll_number))
                    .map((student) => (
                    <tr key={student.id}>
                      <td className="border border-gray-300 p-2 font-medium">{student.roll_number}</td>
                      <td className="border border-gray-300 p-2">{student.student_name}</td>
                      <td className="border border-gray-300 p-2">
                        <Input
                          type="number"
                          min="0"
                          max={iaSession.max_ia_marks}
                          step="0.5"
                          value={student.ia1_marks || ''}
                          onChange={(e) => handleMarksChange(student.id, 'ia1_marks', e.target.value)}
                          className="w-20 text-center"
                          placeholder="0"
                        />
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Input
                          type="number"
                          min="0"
                          max={iaSession.max_ia_marks}
                          step="0.5"
                          value={student.ia2_marks || ''}
                          onChange={(e) => handleMarksChange(student.id, 'ia2_marks', e.target.value)}
                          className="w-20 text-center"
                          placeholder="0"
                        />
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Input
                          type="number"
                          min="0"
                          max={iaSession.max_ia_marks}
                          step="0.5"
                          value={student.ia3_marks || ''}
                          onChange={(e) => handleMarksChange(student.id, 'ia3_marks', e.target.value)}
                          className="w-20 text-center"
                          placeholder="0"
                        />
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Input
                          type="number"
                          min="0"
                          max={iaSession.max_assignment_marks}
                          step="0.5"
                          value={student.assignment_marks || ''}
                          onChange={(e) => handleMarksChange(student.id, 'assignment_marks', e.target.value)}
                          className="w-20 text-center"
                          placeholder="0"
                        />
                      </td>
                      {/* Dynamic Lab Internal column for laboratory subjects */}
                      <td className="border border-gray-300 p-2 bg-blue-50">
                        <Input
                          type="number"
                          min="0"
                          max={iaSession.max_theory_marks}
                          step="0.5"
                          value={(student as any).theory_marks || ''}
                          onChange={(e) => handleMarksChange(student.id, 'theory_marks', e.target.value)}
                          className="w-20 text-center"
                          placeholder="0"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
});

// CRITICAL FIX: Add display name for debugging
InternalAssessmentManagement.displayName = 'InternalAssessmentManagement';

export default InternalAssessmentManagement;
