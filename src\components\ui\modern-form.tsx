import React from 'react';
import { cn } from '@/lib/utils';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ModernLoading } from './modern-loading';

interface ModernFormProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  onSubmit?: (e: React.FormEvent) => void;
  loading?: boolean;
  actions?: React.ReactNode;
  card?: boolean;
}

/**
 * Modern Form Component
 * Enhanced form wrapper with professional styling and layout
 */
export const ModernForm: React.FC<ModernFormProps> = ({
  children,
  className,
  title,
  description,
  onSubmit,
  loading = false,
  actions,
  card = true
}) => {
  const formContent = (
    <form onSubmit={onSubmit} className={cn("space-y-6", className)}>
      {(title || description) && (
        <div className="space-y-2">
          {title && (
            <h2 className="text-xl font-semibold tracking-tight">
              {title}
            </h2>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}
      
      <div className="space-y-4">
        {children}
      </div>

      {actions && (
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
          {actions}
        </div>
      )}

      {loading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center rounded-lg">
          <ModernLoading size="md" text="Processing..." />
        </div>
      )}
    </form>
  );

  if (card) {
    return (
      <Card className="relative">
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit} className={cn("space-y-6", className)}>
            <div className="space-y-4">
              {children}
            </div>

            {actions && (
              <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
                {actions}
              </div>
            )}

            {loading && (
              <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center rounded-lg">
                <ModernLoading size="md" text="Processing..." />
              </div>
            )}
          </form>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="relative">
      {formContent}
    </div>
  );
};

interface ModernFieldGroupProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  columns?: 1 | 2 | 3;
}

/**
 * Modern Field Group Component
 * Groups related form fields with optional title and responsive layout
 */
export const ModernFieldGroup: React.FC<ModernFieldGroupProps> = ({
  children,
  className,
  title,
  description,
  columns = 1
}) => {
  const getGridClass = () => {
    switch (columns) {
      case 2:
        return 'grid grid-cols-1 sm:grid-cols-2 gap-4';
      case 3:
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';
      default:
        return 'space-y-4';
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}
      <div className={getGridClass()}>
        {children}
      </div>
    </div>
  );
};

interface ModernFormActionsProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right';
  loading?: boolean;
  submitText?: string;
  cancelText?: string;
  onCancel?: () => void;
  submitDisabled?: boolean;
}

/**
 * Modern Form Actions Component
 * Standardized form action buttons with loading states
 */
export const ModernFormActions: React.FC<ModernFormActionsProps> = ({
  children,
  className,
  align = 'right',
  loading = false,
  submitText = 'Submit',
  cancelText = 'Cancel',
  onCancel,
  submitDisabled = false
}) => {
  const getAlignClass = () => {
    switch (align) {
      case 'left':
        return 'justify-start';
      case 'center':
        return 'justify-center';
      default:
        return 'justify-end';
    }
  };

  if (children) {
    return (
      <div className={cn(
        'flex flex-col sm:flex-row gap-3',
        getAlignClass(),
        className
      )}>
        {children}
      </div>
    );
  }

  return (
    <div className={cn(
      'flex flex-col sm:flex-row gap-3',
      getAlignClass(),
      className
    )}>
      {onCancel && (
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          {cancelText}
        </Button>
      )}
      <Button
        type="submit"
        disabled={loading || submitDisabled}
        className="min-w-24"
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="loading-spinner h-4 w-4" />
            Processing...
          </div>
        ) : (
          submitText
        )}
      </Button>
    </div>
  );
};

interface ModernFieldProps {
  children: React.ReactNode;
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  className?: string;
}

/**
 * Modern Field Component
 * Enhanced field wrapper with label, description, and error handling
 */
export const ModernField: React.FC<ModernFieldProps> = ({
  children,
  label,
  description,
  error,
  required = false,
  className
}) => {
  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      {description && (
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      )}
      {children}
      {error && (
        <p className="text-xs text-destructive">
          {error}
        </p>
      )}
    </div>
  );
};

export default ModernForm;
