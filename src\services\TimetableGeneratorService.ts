// TimetableGeneratorService.ts
import { TimetableService, TimeStructure, TimetableSlot } from "./TimetableService";
import { MappingType } from "@/stores/SubjectMappingStore";
import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from 'uuid';

interface TimetableGeneratorConfig {
  academicYear: string;
  department: string;
  semester: string;
  section: string;
  constraints?: {
    noConsecutiveClasses?: boolean;
    maxOneClassPerDay?: boolean;
    preferredTimeSlots?: Record<string, string[]>;
    respectLabSlots?: boolean;
  };
}

interface Violation {
  type: string;
  description: string;
}

interface ValidationResult {
  isValid: boolean;
  violations: string[];
}

export class TimetableGeneratorService {
  private timeStructure: TimeStructure | null = null;
  private timeSlots: string[] = [];
  private workingDays: string[] = [];

  constructor() {
    // Initialize any necessary properties
  }

  // Main method to generate a comprehensive timetable with intelligent conflict resolution
  async generateTimetable(
    subjectMappings: MappingType[],
    config: TimetableGeneratorConfig
  ): Promise<TimetableSlot[]> {
    console.log("=== STARTING COMPREHENSIVE TIMETABLE GENERATION ===");
    console.log("Generating timetable with", subjectMappings.length, "subject mappings");

    // 1) Load time structure
    this.timeStructure = await TimetableService.fetchTimeStructure({
      academicYear: config.academicYear,
      department: config.department
    });

    if (!this.timeStructure) {
      throw new Error("Time structure not defined for this department and academic year");
    }

    // 2) Build your day/time slots
    this.timeSlots = this.generateTimeSlots(this.timeStructure);
    this.workingDays = this.timeStructure.working_days;
    console.log("Working with", this.timeSlots.length, "time slots across", this.workingDays.length, "days");

    // 3) Split out lab vs theory mappings
    const labMappings = subjectMappings.filter(m =>
      m.subject.type === 'lab' || m.subject.type === 'laboratory'
    );
    const theoryMappings = subjectMappings.filter(m =>
      m.subject.type === 'theory'
    );
    console.log("Found", labMappings.length, "lab mappings and", theoryMappings.length, "theory mappings");

    // 4) Calculate total theory hours required (should be 20 for complete allocation)
    const totalTheoryHours = theoryMappings.reduce((sum, mapping) => sum + (mapping.hours_per_week || 0), 0);
    console.log(`Total theory hours required: ${totalTheoryHours} (target: 20 slots)`);

    // 5) PHASE 1: Allocate FIXED lab slots (these are IMMOVABLE and take top priority)
    console.log("=== PHASE 1: Allocating FIXED lab slots (IMMOVABLE) ===");
    const timetableSlots: TimetableSlot[] = [];

    for (const labMapping of labMappings) {
      const labSlots = await this.allocateLabSlots(labMapping, config);
      // Mark lab slots as FIXED and IMMOVABLE
      labSlots.forEach(slot => {
        slot.is_fixed = true;
        slot.priority = 'HIGH';
        slot.movable = false;
      });
      timetableSlots.push(...labSlots);
      console.log(`✅ Allocated ${labSlots.length} FIXED lab slots for ${labMapping.subject.code}`);
    }

    // 6) Update faculty availability based on fixed lab allocations
    await this.updateFacultyAvailabilityAfterLabAllocation(timetableSlots, config);

    // 7) PHASE 2: Allocate FLEXIBLE theory slots with intelligent conflict resolution
    console.log("=== PHASE 2: Allocating FLEXIBLE theory slots with conflict resolution ===");
    const theorySlots = await this.allocateAllTheorySlots(theoryMappings, config, timetableSlots);
    timetableSlots.push(...theorySlots);

    // 8) Validate that all required theory hours are allocated
    const allocatedTheoryHours = theorySlots.length;
    console.log(`Theory allocation result: ${allocatedTheoryHours}/${totalTheoryHours} slots`);

    if (allocatedTheoryHours < totalTheoryHours) {
      console.warn(`⚠️  CRITICAL: Only allocated ${allocatedTheoryHours}/${totalTheoryHours} theory slots`);
      // Apply fallback allocation strategy
      const additionalSlots = await this.applyFallbackTheoryAllocation(
        theoryMappings, config, timetableSlots, totalTheoryHours - allocatedTheoryHours
      );
      timetableSlots.push(...additionalSlots);
      console.log(`✅ Fallback allocation added ${additionalSlots.length} additional theory slots`);
    }

    // 9) Final validation and conflict resolution
    await this.performFinalValidationAndConflictResolution(timetableSlots, config);

    // 10) Persist everything
    const savedCount = await this.saveTimetable(timetableSlots);
    console.log(`💾 Saved ${savedCount} of ${timetableSlots.length} timetable slots`);

    const finalTheoryCount = timetableSlots.filter(s => s.subject_type === 'theory').length;
    const finalLabCount = timetableSlots.filter(s => s.subject_type === 'lab').length;
    console.log(`🎯 FINAL ALLOCATION: ${finalTheoryCount} theory + ${finalLabCount} lab slots`);
    console.log("=== TIMETABLE GENERATION COMPLETED ===");

    return timetableSlots;
  }

  private generateTimeSlots(structure: TimeStructure): string[] {
    const slots: string[] = [];
    // first half
    let current = structure.first_half_start_time;
    for (let i = 0; i < structure.periods_in_first_half; i++) {
      if (current === structure.tea_break_start_time) {
        current = structure.tea_break_end_time;
      }
      const end = this.addMinutesToTime(current, structure.theory_class_duration);
      slots.push(`${current}-${end}`);
      current = end;
    }
    // second half
    current = structure.second_half_start_time;
    for (let i = 0; i < structure.periods_in_second_half; i++) {
      const end = this.addMinutesToTime(current, structure.theory_class_duration);
      slots.push(`${current}-${end}`);
      current = end;
    }
    return slots;
  }

  private addMinutesToTime(time: string, minutes: number): string {
    const [h, m] = time.split(':').map(Number);
    let total = h * 60 + m + minutes;
    const nh = Math.floor(total / 60);
    const nm = total % 60;
    return `${String(nh).padStart(2,'0')}:${String(nm).padStart(2,'0')}`;
  }

  // **UPDATED** lab allocation with logging & improved session-count logic
  private async allocateLabSlots(
    labMapping: MappingType,
    config: TimetableGeneratorConfig
  ): Promise<TimetableSlot[]> {
    const slots: TimetableSlot[] = [];
    if (labMapping.labSlots && labMapping.labSlots.length > 0) {
      // predefined from DB
      console.log(`Using ${labMapping.labSlots.length} predefined slots for ${labMapping.subject.code}`);
      for (const ls of labMapping.labSlots) {
        const ts = this.findTimeSlotForLabSession(ls.day, ls.timeOfDay);
        if (!ts) continue;
        const slot: TimetableSlot = {
          id: uuidv4(),
          academic_year: config.academicYear,
          department: config.department,
          semester: config.semester,
          section: config.section,
          day: ls.day,
          time_slot: ts,
          subject_id: labMapping.subject.id,
          subject_code: labMapping.subject.code,
          subject_name: labMapping.subject.name,
          subject_type: 'lab',
          faculty_id: labMapping.faculty.id,
          faculty_name: labMapping.faculty.name,
          room_number: labMapping.classroom,
          subject_short_id: labMapping.subject.shortId,
          batch_name: ls.batch,
          is_lab_start: true,
          is_processed: false,
          col_span: 2,
          faculty2_id: labMapping.faculty2Id,
          faculty2_name: labMapping.faculty2Name,
          is_hidden: false,
          is_full_session_lab: true
        };
        console.log(`→ inserting lab slot: ${slot.subject_code} | ${slot.day} | ${slot.time_slot}`);
        slots.push(slot);
      }
    } else {
      // automatic allocation
      console.log(`Auto-allocating lab slots for ${labMapping.subject.code}`);
      if (!this.timeStructure) throw new Error("No time structure");
      // determine credit‐based duration
      const labDur = (labMapping.subject.type === 'laboratory')
        ? this.timeStructure.three_credits_lab_duration
        : this.timeStructure.two_credits_lab_duration;
      // how many sessions?
      const sessions = labMapping.slotsPerWeek && labMapping.slotsPerWeek > 0
        ? labMapping.slotsPerWeek
        : Math.ceil((labMapping.subject.hoursPerWeek || labMapping.hours_per_week) / labDur);
      for (let i = 0; i < sessions; i++) {
        const alloc = this.findAvailableLabSlot(slots);
        if (!alloc) continue;
        const slot: TimetableSlot = {
          id: uuidv4(),
          academic_year: config.academicYear,
          department: config.department,
          semester: config.semester,
          section: config.section,
          day: alloc.day,
          time_slot: alloc.timeSlot,
          subject_id: labMapping.subject.id,
          subject_code: labMapping.subject.code,
          subject_name: labMapping.subject.name,
          subject_type: 'lab',
          faculty_id: labMapping.faculty.id,
          faculty_name: labMapping.faculty.name,
          room_number: labMapping.classroom,
          subject_short_id: labMapping.subject.shortId,
          batch_name: `Batch ${i+1}`,
          is_lab_start: true,
          is_processed: false,
          col_span: 2,
          faculty2_id: labMapping.faculty2Id,
          faculty2_name: labMapping.faculty2Name,
          is_hidden: false,
          is_full_session_lab: true
        };
        console.log(`→ inserting lab slot: ${slot.subject_code} | ${slot.day} | ${slot.time_slot}`);
        slots.push(slot);
      }
    }
    return slots;
  }

  private findTimeSlotForLabSession(day: string, timeOfDay: string): string {
    if (!this.timeStructure) return '';
    const firstCount = this.timeStructure.periods_in_first_half;
    const arr = timeOfDay.toLowerCase() === 'morning'
      ? this.timeSlots.slice(0, firstCount)
      : this.timeSlots.slice(firstCount);
    return arr[0] || '';
  }

  private findAvailableLabSlot(existing: TimetableSlot[]): { day: string; timeSlot: string } | null {
    for (const d of this.workingDays) {
      for (const t of this.timeSlots) {
        if (!existing.some(s => s.day===d && s.time_slot===t)) {
          return { day: d, timeSlot: t };
        }
      }
    }
    return null;
  }

  /**
   * Update faculty availability after lab allocation to prevent conflicts
   */
  private async updateFacultyAvailabilityAfterLabAllocation(
    labSlots: TimetableSlot[],
    config: TimetableGeneratorConfig
  ): Promise<void> {
    console.log("Updating faculty availability after lab allocation...");

    // Group lab slots by faculty
    const facultyLabSlots: Record<string, TimetableSlot[]> = {};

    labSlots.forEach(slot => {
      if (slot.faculty_id) {
        if (!facultyLabSlots[slot.faculty_id]) {
          facultyLabSlots[slot.faculty_id] = [];
        }
        facultyLabSlots[slot.faculty_id].push(slot);
      }

      if (slot.faculty2_id) {
        if (!facultyLabSlots[slot.faculty2_id]) {
          facultyLabSlots[slot.faculty2_id] = [];
        }
        facultyLabSlots[slot.faculty2_id].push(slot);
      }
    });

    // Update availability for each faculty
    for (const [facultyId, slots] of Object.entries(facultyLabSlots)) {
      console.log(`Updating availability for faculty ${facultyId} with ${slots.length} lab slots`);

      // Get current faculty data
      const { data: faculty } = await supabase
        .from('employee_details')
        .select('vacant_by_day, vacant_count_by_day')
        .eq('id', facultyId)
        .single();

      if (faculty) {
        const vacant_by_day = { ...faculty.vacant_by_day };
        const vacant_count_by_day = { ...faculty.vacant_count_by_day };

        // Remove lab time slots from availability
        slots.forEach(slot => {
          const day = slot.day;
          if (vacant_count_by_day[day]) {
            // Remove the specific time slot
            vacant_count_by_day[day] = vacant_count_by_day[day].filter(
              (timeSlot: string) => timeSlot !== slot.time_slot
            );

            // Update day count
            vacant_by_day[day] = vacant_count_by_day[day].length;
          }
        });

        // Update in database
        await supabase
          .from('employee_details')
          .update({
            vacant_by_day,
            vacant_count_by_day
          })
          .eq('id', facultyId);

        console.log(`✅ Updated availability for faculty ${facultyId}`);
      }
    }
  }

  /**
   * Allocate all theory slots with intelligent conflict resolution
   */
  private async allocateAllTheorySlots(
    theoryMappings: MappingType[],
    config: TimetableGeneratorConfig,
    existingSlots: TimetableSlot[]
  ): Promise<TimetableSlot[]> {
    console.log("Starting intelligent theory slot allocation...");

    const theorySlots: TimetableSlot[] = [];
    const occupiedSlots = this.buildOccupiedSlotsMap(existingSlots);

    // Sort theory mappings by hours (descending) to prioritize subjects with more requirements
    const sortedMappings = [...theoryMappings].sort((a, b) =>
      (b.hours_per_week || 0) - (a.hours_per_week || 0)
    );

    for (const mapping of sortedMappings) {
      console.log(`Allocating ${mapping.hours_per_week} slots for ${mapping.subject.code}`);

      const allocatedSlots = await this.allocateTheorySlots(mapping, config, [...theorySlots, ...existingSlots]);
      theorySlots.push(...allocatedSlots);

      // Update occupied slots map
      allocatedSlots.forEach(slot => {
        if (!occupiedSlots[slot.day]) {
          occupiedSlots[slot.day] = {};
        }
        if (!occupiedSlots[slot.day][slot.faculty_id]) {
          occupiedSlots[slot.day][slot.faculty_id] = new Set();
        }
        occupiedSlots[slot.day][slot.faculty_id].add(slot.time_slot);
      });
    }

    return theorySlots;
  }

  /**
   * Apply fallback allocation strategy for missing theory slots
   */
  private async applyFallbackTheoryAllocation(
    theoryMappings: MappingType[],
    config: TimetableGeneratorConfig,
    existingSlots: TimetableSlot[],
    missingSlots: number
  ): Promise<TimetableSlot[]> {
    console.log(`Applying fallback allocation for ${missingSlots} missing theory slots...`);

    const fallbackSlots: TimetableSlot[] = [];
    const occupiedSlots = this.buildOccupiedSlotsMap(existingSlots);

    // Find subjects that still need slots
    const subjectSlotCounts: Record<string, number> = {};
    existingSlots.filter(s => s.subject_type === 'theory').forEach(slot => {
      subjectSlotCounts[slot.subject_code] = (subjectSlotCounts[slot.subject_code] || 0) + 1;
    });

    const subjectsNeedingSlots = theoryMappings.filter(mapping => {
      const allocated = subjectSlotCounts[mapping.subject.code] || 0;
      return allocated < (mapping.hours_per_week || 0);
    });

    // Try to allocate remaining slots using any available time slots
    for (const mapping of subjectsNeedingSlots) {
      const needed = (mapping.hours_per_week || 0) - (subjectSlotCounts[mapping.subject.code] || 0);

      for (let i = 0; i < needed && fallbackSlots.length < missingSlots; i++) {
        const availableSlot = await this.findAnyAvailableSlot(mapping.faculty.id, occupiedSlots);

        if (availableSlot) {
          // Create fallback allocation slot
          const slot: TimetableSlot = {
            id: uuidv4(),
            academic_year: config.academicYear,
            department: config.department,
            semester: config.semester,
            section: config.section,
            day: availableSlot.day,
            time_slot: availableSlot.timeSlot,
            subject_id: mapping.subject.id,
            subject_code: mapping.subject.code,
            subject_name: mapping.subject.name,
            subject_type: 'theory',
            faculty_id: mapping.faculty.id,
            faculty_name: mapping.faculty.name,
            subject_short_id: mapping.subject.shortId
          };

          fallbackSlots.push(slot);

          // Update occupied slots
          if (!occupiedSlots[availableSlot.day]) {
            occupiedSlots[availableSlot.day] = {};
          }
          if (!occupiedSlots[availableSlot.day][mapping.faculty.id]) {
            occupiedSlots[availableSlot.day][mapping.faculty.id] = new Set();
          }
          occupiedSlots[availableSlot.day][mapping.faculty.id].add(availableSlot.timeSlot);

          console.log(`✅ Fallback allocated ${mapping.subject.code} on ${availableSlot.day} at ${availableSlot.timeSlot}`);
        }
      }
    }

    return fallbackSlots;
  }

  /**
   * Perform final validation and conflict resolution
   */
  private async performFinalValidationAndConflictResolution(
    timetableSlots: TimetableSlot[],
    config: TimetableGeneratorConfig
  ): Promise<void> {
    console.log("Performing final validation and conflict resolution...");

    // Check for faculty double-booking conflicts
    const facultyConflicts = this.detectFacultyConflicts(timetableSlots);

    if (facultyConflicts.length > 0) {
      console.warn(`Found ${facultyConflicts.length} faculty conflicts, attempting resolution...`);

      for (const conflict of facultyConflicts) {
        await this.resolveIndividualConflict(conflict, timetableSlots, config);
      }
    }

    // Validate that lab slots remain fixed
    const labSlots = timetableSlots.filter(s => s.subject_type === 'lab');
    console.log(`✅ Validated ${labSlots.length} lab slots remain FIXED and IMMOVABLE`);

    // Final theory slot count validation
    const theorySlots = timetableSlots.filter(s => s.subject_type === 'theory');
    console.log(`✅ Final theory slot count: ${theorySlots.length}`);
  }

  /**
   * Build occupied slots map for conflict detection
   */
  private buildOccupiedSlotsMap(slots: TimetableSlot[]): Record<string, Record<string, Set<string>>> {
    const occupiedSlots: Record<string, Record<string, Set<string>>> = {};

    slots.forEach(slot => {
      if (!occupiedSlots[slot.day]) {
        occupiedSlots[slot.day] = {};
      }
      if (!occupiedSlots[slot.day][slot.faculty_id]) {
        occupiedSlots[slot.day][slot.faculty_id] = new Set();
      }
      occupiedSlots[slot.day][slot.faculty_id].add(slot.time_slot);

      // Also track faculty2 for lab slots
      if (slot.faculty2_id) {
        if (!occupiedSlots[slot.day][slot.faculty2_id]) {
          occupiedSlots[slot.day][slot.faculty2_id] = new Set();
        }
        occupiedSlots[slot.day][slot.faculty2_id].add(slot.time_slot);
      }
    });

    return occupiedSlots;
  }

  /**
   * Find any available slot for a faculty member - CRITICAL FIX: Now respects faculty availability
   */
  private async findAnyAvailableSlot(
    facultyId: string,
    occupiedSlots: Record<string, Record<string, Set<string>>>
  ): Promise<{ day: string; timeSlot: string } | null> {

    // CRITICAL FIX: Get faculty availability from database
    const { data: facultyData } = await supabase
      .from("employee_details")
      .select("vacant_count_by_day, full_name")
      .eq("id", facultyId)
      .single();

    if (!facultyData?.vacant_count_by_day) {
      console.warn(`No availability data found for faculty ${facultyId}`);
      return null;
    }

    console.log(`Checking availability for faculty ${facultyData.full_name} (${facultyId})`);

    for (const day of this.workingDays) {
      // CRITICAL FIX: Only check slots where faculty is actually available
      const availableSlots = facultyData.vacant_count_by_day[day] || [];

      for (const timeSlot of availableSlots) {
        // Check if this slot is not occupied in the current timetable
        if (!occupiedSlots[day]?.[facultyId]?.has(timeSlot)) {
          console.log(`Found available slot for ${facultyData.full_name}: ${day} at ${timeSlot}`);
          return { day, timeSlot };
        } else {
          console.log(`Slot ${day} ${timeSlot} is occupied for ${facultyData.full_name}`);
        }
      }
    }

    console.warn(`No available slots found for faculty ${facultyData.full_name} (${facultyId})`);
    return null;
  }

  /**
   * Detect faculty conflicts in the timetable
   */
  private detectFacultyConflicts(slots: TimetableSlot[]): any[] {
    const conflicts: any[] = [];
    const facultySlots: Record<string, Record<string, TimetableSlot[]>> = {};

    // Group slots by faculty and day
    slots.forEach(slot => {
      [slot.faculty_id, slot.faculty2_id].filter(Boolean).forEach(facultyId => {
        if (!facultySlots[facultyId!]) {
          facultySlots[facultyId!] = {};
        }
        if (!facultySlots[facultyId!][slot.day]) {
          facultySlots[facultyId!][slot.day] = [];
        }
        facultySlots[facultyId!][slot.day].push(slot);
      });
    });

    // Check for time conflicts
    Object.entries(facultySlots).forEach(([facultyId, daySlots]) => {
      Object.entries(daySlots).forEach(([day, daySlotList]) => {
        const timeSlotMap: Record<string, TimetableSlot[]> = {};

        daySlotList.forEach(slot => {
          if (!timeSlotMap[slot.time_slot]) {
            timeSlotMap[slot.time_slot] = [];
          }
          timeSlotMap[slot.time_slot].push(slot);
        });

        // Find conflicts (multiple slots at same time)
        Object.entries(timeSlotMap).forEach(([timeSlot, conflictingSlots]) => {
          if (conflictingSlots.length > 1) {
            conflicts.push({
              facultyId,
              day,
              timeSlot,
              conflictingSlots
            });
          }
        });
      });
    });

    return conflicts;
  }

  /**
   * Resolve individual conflict through intelligent slot swapping
   */
  private async resolveIndividualConflict(
    conflict: any,
    timetableSlots: TimetableSlot[],
    config: TimetableGeneratorConfig
  ): Promise<void> {
    console.log(`Resolving conflict for faculty ${conflict.facultyId} on ${conflict.day} at ${conflict.timeSlot}`);

    // Prioritize lab slots (they are fixed and cannot be moved)
    const labSlots = conflict.conflictingSlots.filter((s: TimetableSlot) => s.subject_type === 'lab');
    const theorySlots = conflict.conflictingSlots.filter((s: TimetableSlot) => s.subject_type === 'theory');

    if (labSlots.length > 0 && theorySlots.length > 0) {
      // Lab slots are fixed, move theory slots
      for (const theorySlot of theorySlots) {
        const newSlot = await this.findAlternativeSlotForTheory(theorySlot, timetableSlots);
        if (newSlot) {
          // Update the theory slot
          theorySlot.day = newSlot.day;
          theorySlot.time_slot = newSlot.timeSlot;
          console.log(`✅ Moved theory slot ${theorySlot.subject_code} to ${newSlot.day} at ${newSlot.timeSlot}`);
        } else {
          console.warn(`⚠️  Could not find alternative slot for theory ${theorySlot.subject_code}`);
        }
      }
    } else if (theorySlots.length > 1) {
      // Multiple theory slots conflict, move all but the first
      for (let i = 1; i < theorySlots.length; i++) {
        const theorySlot = theorySlots[i];
        const newSlot = await this.findAlternativeSlotForTheory(theorySlot, timetableSlots);
        if (newSlot) {
          theorySlot.day = newSlot.day;
          theorySlot.time_slot = newSlot.timeSlot;
          console.log(`✅ Moved conflicting theory slot ${theorySlot.subject_code} to ${newSlot.day} at ${newSlot.timeSlot}`);
        }
      }
    }
  }

  /**
   * Find alternative slot for a theory subject - CRITICAL FIX: Now respects faculty availability
   */
  private async findAlternativeSlotForTheory(
    theorySlot: TimetableSlot,
    allSlots: TimetableSlot[]
  ): Promise<{ day: string; timeSlot: string } | null> {

    const occupiedSlots = this.buildOccupiedSlotsMap(allSlots.filter(s => s.id !== theorySlot.id));

    // CRITICAL FIX: Get faculty availability from database
    const { data: facultyData } = await supabase
      .from("employee_details")
      .select("vacant_count_by_day, full_name")
      .eq("id", theorySlot.faculty_id)
      .single();

    if (!facultyData?.vacant_count_by_day) {
      console.warn(`No availability data found for faculty ${theorySlot.faculty_id}`);
      return null;
    }

    // Try to find a slot where the faculty is actually available
    for (const day of this.workingDays) {
      // CRITICAL FIX: Only check slots where faculty is actually available
      const availableSlots = facultyData.vacant_count_by_day[day] || [];

      for (const timeSlot of availableSlots) {
        if (!occupiedSlots[day]?.[theorySlot.faculty_id]?.has(timeSlot)) {
          console.log(`Found alternative slot for ${theorySlot.subject_code}: ${day} at ${timeSlot}`);
          return { day, timeSlot };
        }
      }
    }

    console.warn(`No alternative slots found for ${theorySlot.subject_code} (faculty: ${facultyData.full_name})`);
    return null;
  }

  /**
   * Allocate theory slots for a specific mapping (placeholder - needs full implementation)
   */
  private async allocateTheorySlots(
    mapping: MappingType,
    config: TimetableGeneratorConfig,
    existingSlots: TimetableSlot[]
  ): Promise<TimetableSlot[]> {
    // This is a simplified implementation - in practice, this would use
    // the existing TheorySlotGenerator service
    const slots: TimetableSlot[] = [];
    const occupiedSlots = this.buildOccupiedSlotsMap(existingSlots);
    const hoursNeeded = mapping.hours_per_week || 0;

    for (let i = 0; i < hoursNeeded; i++) {
      const availableSlot = await this.findAnyAvailableSlot(mapping.faculty.id, occupiedSlots);

      if (availableSlot) {
        const slot: TimetableSlot = {
          id: uuidv4(),
          academic_year: config.academicYear,
          department: config.department,
          semester: config.semester,
          section: config.section,
          day: availableSlot.day,
          time_slot: availableSlot.timeSlot,
          subject_id: mapping.subject.id,
          subject_code: mapping.subject.code,
          subject_name: mapping.subject.name,
          subject_type: 'theory',
          faculty_id: mapping.faculty.id,
          faculty_name: mapping.faculty.name,
          subject_short_id: mapping.subject.shortId
        };

        slots.push(slot);

        // Update occupied slots
        if (!occupiedSlots[availableSlot.day]) {
          occupiedSlots[availableSlot.day] = {};
        }
        if (!occupiedSlots[availableSlot.day][mapping.faculty.id]) {
          occupiedSlots[availableSlot.day][mapping.faculty.id] = new Set();
        }
        occupiedSlots[availableSlot.day][mapping.faculty.id].add(availableSlot.timeSlot);
      }
    }

    return slots;
  }

  // **IN SAVE: add logging so you can confirm every slot is written**
  async saveTimetable(slots: TimetableSlot[]): Promise<number> {
    let successCount = 0;
    if (slots.length > 0) {
      const s0 = slots[0];
      await supabase
        .from('timetable_slots')
        .delete()
        .eq('academic_year', s0.academic_year)
        .eq('department', s0.department)
        .eq('semester', s0.semester)
        .eq('section', s0.section);
    }
    for (const slot of slots) {
      console.log(`=> saving to DB: ${slot.subject_code} | ${slot.day} | ${slot.time_slot}`);
      const { error } = await supabase
        .from('timetable_slots')
        .upsert({
          id: slot.id || uuidv4(),
          academic_year: slot.academic_year,
          department: slot.department,
          semester: slot.semester,
          section: slot.section,
          day: slot.day,
          time_slot: slot.time_slot,
          subject_id: slot.subject_id,
          subject_code: slot.subject_code,
          subject_name: slot.subject_name,
          subject_type: slot.subject_type,
          faculty_id: slot.faculty_id,
          faculty_name: slot.faculty_name,
          room_number: slot.room_number,
          is_lab_start: slot.is_lab_start,
          is_processed: slot.is_processed,
          col_span: slot.col_span || 1,
          faculty2_id: slot.faculty2_id || null,
          faculty2_name: slot.faculty2_name || null,
          is_hidden: slot.is_hidden,
          is_full_session_lab: slot.is_full_session_lab,
          subject_short_id: slot.subject_short_id,
          batch_name: slot.batch_name
        });
      if (!error) {
        successCount++;
      } else {
        console.error("Error saving slot:", error);
      }
    }
    return successCount;
  }
}
