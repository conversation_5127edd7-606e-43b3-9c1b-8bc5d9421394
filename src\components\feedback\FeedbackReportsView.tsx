import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import {
  FileText,
  Download,
  Eye,
  BarChart3,
  Users,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

import { FeedbackService } from '@/services/FeedbackService';
import { FeedbackReport } from '@/types/feedback-system';

interface FeedbackReportsViewProps {
  department: string;
}

const FeedbackReportsView: React.FC<FeedbackReportsViewProps> = ({
  department
}) => {
  const [reports, setReports] = useState<FeedbackReport[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadReports();
  }, [department]);

  const loadReports = async () => {
    try {
      setLoading(true);
      // TODO: Implement getReportsByDepartment in FeedbackService
      // const reportsData = await FeedbackService.getReportsByDepartment(department);
      // setReports(reportsData);
      setReports([]); // Placeholder
    } catch (error) {
      console.error('Error loading reports:', error);
      toast({
        title: "Error",
        description: "Failed to load feedback reports",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async (sessionId: string, facultyId: string, subjectCode: string) => {
    try {
      // TODO: Implement report generation
      toast({
        title: "Report Generated",
        description: "Feedback report has been generated successfully",
      });
      loadReports();
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        title: "Error",
        description: "Failed to generate report",
        variant: "destructive",
      });
    }
  };

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-100';
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[300px]">
        <div className="text-center space-y-4">
          <div className="loading-spinner h-8 w-8 mx-auto"></div>
          <p className="text-muted-foreground">Loading reports...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Feedback Reports</h2>
          <p className="text-muted-foreground">
            View and download faculty feedback reports
          </p>
        </div>
        <Button onClick={loadReports} variant="outline">
          Refresh Reports
        </Button>
      </div>

      {/* Reports List */}
      {reports.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Reports Available</h3>
            <p className="text-muted-foreground mb-4">
              Reports will be generated automatically once feedback sessions are completed.
            </p>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Create and activate feedback sessions to start collecting student feedback.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {reports.map((report) => (
            <Card key={report.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">
                      {report.faculty_name}
                    </CardTitle>
                    <CardDescription>
                      {report.subject_name} ({report.subject_code}) • {report.semester} Semester, Section {report.section}
                    </CardDescription>
                  </div>
                  <Badge 
                    className={getPerformanceColor(report.overall_percentage)}
                  >
                    {report.overall_percentage.toFixed(1)}%
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {report.total_responses} responses
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Overall: {report.overall_percentage.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        Generated: {new Date(report.generated_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="gap-2">
                      <Eye className="h-4 w-4" />
                      View Details
                    </Button>
                    <Button variant="outline" size="sm" className="gap-2">
                      <Download className="h-4 w-4" />
                      Download PDF
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default FeedbackReportsView;
