import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserRole } from '@/hooks/useUserRole';
import { 
  LeaveNotificationService, 
  LeaveNotification, 
  NotificationStats 
} from '@/services/LeaveNotificationService';

interface UseLeaveNotificationsReturn {
  notifications: LeaveNotification[];
  unreadCount: number;
  stats: NotificationStats;
  loading: boolean;
  error: string | null;
  refreshNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  dismissNotification: (notificationId: string) => Promise<void>;
  getNotificationMessage: (notification: LeaveNotification) => string;
  isUrgent: (notification: LeaveNotification) => boolean;
}

/**
 * Custom hook for managing leave notifications for HODs
 */
export const useLeaveNotifications = (): UseLeaveNotificationsReturn => {
  const { user } = useAuth();
  const { isHOD, isPrincipal } = useUserRole();
  
  const [notifications, setNotifications] = useState<LeaveNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [stats, setStats] = useState<NotificationStats>({
    total_notifications: 0,
    unread_notifications: 0,
    new_requests: 0,
    urgent_requests: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Real-time subscription channel
  const [channel, setChannel] = useState<any>(null);

  /**
   * Load notifications and stats
   */
  const loadNotifications = useCallback(async () => {
    if (!user?.id || (!isHOD && !isPrincipal)) {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      
      // Load notifications and stats in parallel
      const [notificationsData, unreadCountData, statsData] = await Promise.all([
        LeaveNotificationService.getNotificationsForHOD(user.id, {
          is_dismissed: false,
          limit: 50
        }),
        LeaveNotificationService.getUnreadNotificationCount(user.id),
        LeaveNotificationService.getNotificationStats(user.id)
      ]);

      setNotifications(notificationsData);
      setUnreadCount(unreadCountData);
      setStats(statsData);
    } catch (err) {
      console.error('Error loading notifications:', err);

      // Handle case where leave_notifications table doesn't exist yet
      if (err && typeof err === 'object' && 'code' in err && err.code === '42P01') {
        console.warn('Leave notifications table does not exist. Please run the database setup script.');
        setError('Notification system not yet configured. Please contact administrator.');
        // Set empty state instead of error for better UX
        setNotifications([]);
        setUnreadCount(0);
        setStats({
          total_notifications: 0,
          unread_notifications: 0,
          new_requests: 0,
          urgent_requests: 0
        });
        return;
      }

      setError(err instanceof Error ? err.message : 'Failed to load notifications');
    } finally {
      setLoading(false);
    }
  }, [user?.id, isHOD, isPrincipal]);

  /**
   * Set up real-time subscription
   */
  const setupRealtimeSubscription = useCallback(() => {
    if (!user?.id || (!isHOD && !isPrincipal)) return;

    const newChannel = LeaveNotificationService.subscribeToNotifications(
      user.id,
      (payload) => {
        console.log('Real-time notification update:', payload);
        
        // Refresh notifications when changes occur
        loadNotifications();
      }
    );

    setChannel(newChannel);

    return () => {
      if (newChannel) {
        LeaveNotificationService.unsubscribeFromNotifications(newChannel);
      }
    };
  }, [user?.id, isHOD, isPrincipal, loadNotifications]);

  /**
   * Mark notification as read
   */
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await LeaveNotificationService.markNotificationAsRead(notificationId);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: true, read_at: new Date().toISOString() }
            : notification
        )
      );
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
      setStats(prev => ({
        ...prev,
        unread_notifications: Math.max(0, prev.unread_notifications - 1)
      }));
    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError(err instanceof Error ? err.message : 'Failed to mark notification as read');
    }
  }, []);

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = useCallback(async () => {
    if (!user?.id) return;

    try {
      await LeaveNotificationService.markAllNotificationsAsRead(user.id);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({
          ...notification,
          is_read: true,
          read_at: new Date().toISOString()
        }))
      );
      
      setUnreadCount(0);
      setStats(prev => ({
        ...prev,
        unread_notifications: 0
      }));
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      setError(err instanceof Error ? err.message : 'Failed to mark all notifications as read');
    }
  }, [user?.id]);

  /**
   * Dismiss notification
   */
  const dismissNotification = useCallback(async (notificationId: string) => {
    try {
      await LeaveNotificationService.dismissNotification(notificationId);
      
      // Remove from local state
      setNotifications(prev => 
        prev.filter(notification => notification.id !== notificationId)
      );
      
      // Update counts if it was unread
      const notification = notifications.find(n => n.id === notificationId);
      if (notification && !notification.is_read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
        setStats(prev => ({
          ...prev,
          unread_notifications: Math.max(0, prev.unread_notifications - 1)
        }));
      }
    } catch (err) {
      console.error('Error dismissing notification:', err);
      setError(err instanceof Error ? err.message : 'Failed to dismiss notification');
    }
  }, [notifications]);

  /**
   * Get formatted notification message
   */
  const getNotificationMessage = useCallback((notification: LeaveNotification): string => {
    return LeaveNotificationService.formatNotificationMessage(notification);
  }, []);

  /**
   * Check if notification is urgent
   */
  const isUrgent = useCallback((notification: LeaveNotification): boolean => {
    return LeaveNotificationService.isNotificationUrgent(notification);
  }, []);

  /**
   * Refresh notifications manually
   */
  const refreshNotifications = useCallback(async () => {
    setLoading(true);
    await loadNotifications();
  }, [loadNotifications]);

  // Initial load
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // Set up real-time subscription
  useEffect(() => {
    const cleanup = setupRealtimeSubscription();
    return cleanup;
  }, [setupRealtimeSubscription]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (channel) {
        LeaveNotificationService.unsubscribeFromNotifications(channel);
      }
    };
  }, [channel]);

  // Auto-refresh every 30 seconds for backup
  useEffect(() => {
    if (!user?.id || (!isHOD && !isPrincipal)) return;

    const interval = setInterval(() => {
      loadNotifications();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [user?.id, isHOD, isPrincipal, loadNotifications]);

  return {
    notifications,
    unreadCount,
    stats,
    loading,
    error,
    refreshNotifications,
    markAsRead,
    markAllAsRead,
    dismissNotification,
    getNotificationMessage,
    isUrgent
  };
};
