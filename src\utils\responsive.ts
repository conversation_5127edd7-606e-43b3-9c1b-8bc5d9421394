/**
 * Responsive Design Utilities
 * Helper functions and constants for responsive design implementation
 */

// Breakpoint constants matching Tailwind CSS
export const BREAKPOINTS = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1400,
  '3xl': 1600,
  '4xl': 1920,
  '5xl': 2560
} as const;

// Common screen resolutions for testing
export const SCREEN_RESOLUTIONS = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1366, height: 768 },
  desktop: { width: 1920, height: 1080 },
  ultrawide: { width: 2560, height: 1440 },
  '4k': { width: 3840, height: 2160 }
} as const;

/**
 * Get current screen size category
 */
export const getScreenSize = (): keyof typeof BREAKPOINTS => {
  if (typeof window === 'undefined') return 'lg';
  
  const width = window.innerWidth;
  
  if (width >= BREAKPOINTS['5xl']) return '5xl';
  if (width >= BREAKPOINTS['4xl']) return '4xl';
  if (width >= BREAKPOINTS['3xl']) return '3xl';
  if (width >= BREAKPOINTS['2xl']) return '2xl';
  if (width >= BREAKPOINTS.xl) return 'xl';
  if (width >= BREAKPOINTS.lg) return 'lg';
  if (width >= BREAKPOINTS.md) return 'md';
  if (width >= BREAKPOINTS.sm) return 'sm';
  if (width >= BREAKPOINTS.xs) return 'xs';
  
  return 'xs';
};

/**
 * Check if screen is at least a certain size
 */
export const isScreenAtLeast = (breakpoint: keyof typeof BREAKPOINTS): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= BREAKPOINTS[breakpoint];
};

/**
 * Check if screen is mobile size
 */
export const isMobile = (): boolean => {
  return !isScreenAtLeast('md');
};

/**
 * Check if screen is tablet size
 */
export const isTablet = (): boolean => {
  return isScreenAtLeast('md') && !isScreenAtLeast('lg');
};

/**
 * Check if screen is desktop size
 */
export const isDesktop = (): boolean => {
  return isScreenAtLeast('lg');
};

/**
 * Get responsive grid columns based on screen size
 */
export const getResponsiveColumns = (
  maxColumns: number,
  breakpoints?: Partial<Record<keyof typeof BREAKPOINTS, number>>
): number => {
  const screenSize = getScreenSize();
  
  // Default responsive behavior
  const defaultBreakpoints = {
    xs: 1,
    sm: Math.min(2, maxColumns),
    md: Math.min(3, maxColumns),
    lg: Math.min(4, maxColumns),
    xl: maxColumns,
    '2xl': maxColumns,
    '3xl': maxColumns,
    '4xl': maxColumns,
    '5xl': maxColumns
  };
  
  const finalBreakpoints = { ...defaultBreakpoints, ...breakpoints };
  return finalBreakpoints[screenSize] || maxColumns;
};

/**
 * Get responsive font size based on screen size
 */
export const getResponsiveFontSize = (
  baseSize: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'
): string => {
  const screenSize = getScreenSize();
  
  const fontSizeMap = {
    xs: {
      xs: 'text-xs',
      sm: 'text-xs',
      md: 'text-xs',
      lg: 'text-sm',
      xl: 'text-sm',
      '2xl': 'text-sm',
      '3xl': 'text-sm',
      '4xl': 'text-sm',
      '5xl': 'text-sm'
    },
    sm: {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-sm',
      lg: 'text-sm',
      xl: 'text-base',
      '2xl': 'text-base',
      '3xl': 'text-base',
      '4xl': 'text-base',
      '5xl': 'text-base'
    },
    base: {
      xs: 'text-sm',
      sm: 'text-base',
      md: 'text-base',
      lg: 'text-base',
      xl: 'text-lg',
      '2xl': 'text-lg',
      '3xl': 'text-lg',
      '4xl': 'text-lg',
      '5xl': 'text-lg'
    },
    lg: {
      xs: 'text-base',
      sm: 'text-lg',
      md: 'text-lg',
      lg: 'text-xl',
      xl: 'text-xl',
      '2xl': 'text-2xl',
      '3xl': 'text-2xl',
      '4xl': 'text-2xl',
      '5xl': 'text-2xl'
    },
    xl: {
      xs: 'text-lg',
      sm: 'text-xl',
      md: 'text-xl',
      lg: 'text-2xl',
      xl: 'text-2xl',
      '2xl': 'text-3xl',
      '3xl': 'text-3xl',
      '4xl': 'text-3xl',
      '5xl': 'text-3xl'
    },
    '2xl': {
      xs: 'text-xl',
      sm: 'text-2xl',
      md: 'text-2xl',
      lg: 'text-3xl',
      xl: 'text-3xl',
      '2xl': 'text-4xl',
      '3xl': 'text-4xl',
      '4xl': 'text-4xl',
      '5xl': 'text-4xl'
    },
    '3xl': {
      xs: 'text-2xl',
      sm: 'text-3xl',
      md: 'text-3xl',
      lg: 'text-4xl',
      xl: 'text-4xl',
      '2xl': 'text-5xl',
      '3xl': 'text-5xl',
      '4xl': 'text-5xl',
      '5xl': 'text-5xl'
    },
    '4xl': {
      xs: 'text-3xl',
      sm: 'text-4xl',
      md: 'text-4xl',
      lg: 'text-5xl',
      xl: 'text-5xl',
      '2xl': 'text-6xl',
      '3xl': 'text-6xl',
      '4xl': 'text-6xl',
      '5xl': 'text-6xl'
    }
  };
  
  return fontSizeMap[baseSize][screenSize];
};

/**
 * Get responsive spacing based on screen size
 */
export const getResponsiveSpacing = (
  baseSpacing: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
): string => {
  const screenSize = getScreenSize();
  
  const spacingMap = {
    xs: {
      xs: 'space-y-2',
      sm: 'space-y-2',
      md: 'space-y-3',
      lg: 'space-y-3',
      xl: 'space-y-4',
      '2xl': 'space-y-4',
      '3xl': 'space-y-4',
      '4xl': 'space-y-4',
      '5xl': 'space-y-4'
    },
    sm: {
      xs: 'space-y-3',
      sm: 'space-y-4',
      md: 'space-y-4',
      lg: 'space-y-5',
      xl: 'space-y-5',
      '2xl': 'space-y-6',
      '3xl': 'space-y-6',
      '4xl': 'space-y-6',
      '5xl': 'space-y-6'
    },
    md: {
      xs: 'space-y-4',
      sm: 'space-y-5',
      md: 'space-y-6',
      lg: 'space-y-6',
      xl: 'space-y-7',
      '2xl': 'space-y-8',
      '3xl': 'space-y-8',
      '4xl': 'space-y-8',
      '5xl': 'space-y-8'
    },
    lg: {
      xs: 'space-y-5',
      sm: 'space-y-6',
      md: 'space-y-7',
      lg: 'space-y-8',
      xl: 'space-y-9',
      '2xl': 'space-y-10',
      '3xl': 'space-y-10',
      '4xl': 'space-y-10',
      '5xl': 'space-y-10'
    },
    xl: {
      xs: 'space-y-6',
      sm: 'space-y-7',
      md: 'space-y-8',
      lg: 'space-y-10',
      xl: 'space-y-12',
      '2xl': 'space-y-14',
      '3xl': 'space-y-16',
      '4xl': 'space-y-16',
      '5xl': 'space-y-16'
    }
  };
  
  return spacingMap[baseSpacing][screenSize];
};

/**
 * Hook for responsive design
 */
export const useResponsive = () => {
  const [screenSize, setScreenSize] = React.useState<keyof typeof BREAKPOINTS>('lg');
  
  React.useEffect(() => {
    const handleResize = () => {
      setScreenSize(getScreenSize());
    };
    
    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return {
    screenSize,
    isMobile: !isScreenAtLeast('md'),
    isTablet: isScreenAtLeast('md') && !isScreenAtLeast('lg'),
    isDesktop: isScreenAtLeast('lg'),
    isLargeScreen: isScreenAtLeast('xl'),
    isUltrawide: isScreenAtLeast('3xl'),
    getColumns: (maxColumns: number) => getResponsiveColumns(maxColumns),
    getFontSize: (baseSize: Parameters<typeof getResponsiveFontSize>[0]) => 
      getResponsiveFontSize(baseSize),
    getSpacing: (baseSpacing: Parameters<typeof getResponsiveSpacing>[0]) => 
      getResponsiveSpacing(baseSpacing)
  };
};

// React import for the hook
import React from 'react';
