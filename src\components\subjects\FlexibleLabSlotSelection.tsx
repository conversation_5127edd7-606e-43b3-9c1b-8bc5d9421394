import React, { useState, useEffect, useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Clock, AlertTriangle, CheckCircle } from "lucide-react";
import { TimeSlotValidationService } from "@/services/TimeSlotValidationService";

interface FlexibleLabSlotSelectionProps {
  form: UseFormReturn<any>;
  slotNumber: number;
  disabled?: boolean;
  hoursPerSlot: number;
  academicYear?: string;
  department?: string;
  semester?: string;
}

export default function FlexibleLabSlotSelection({
  form,
  slotNumber,
  disabled = false,
  hoursPerSlot,
}: FlexibleLabSlotSelectionProps) {
  const dayField = `day${slotNumber}` as const;
  const startTimeField = `startTime${slotNumber}` as const;
  const endTimeField = `endTime${slotNumber}` as const;
  const batchField = `batch${slotNumber}` as const;

  // Watch start and end times for validation
  const startTime = form.watch(startTimeField);
  const endTime = form.watch(endTimeField);

  // Generate available start times using the validation service
  const availableStartTimes = useMemo(() => {
    return TimeSlotValidationService.getAvailableStartTimes(hoursPerSlot);
  }, [hoursPerSlot]);

  // Auto-calculate end time when start time changes
  useEffect(() => {
    if (startTime && hoursPerSlot) {
      const calculatedEndTime = TimeSlotValidationService.calculateEndTime(startTime, hoursPerSlot);
      if (calculatedEndTime) {
        form.setValue(endTimeField, calculatedEndTime);
      }
    }
  }, [startTime, hoursPerSlot, form, endTimeField]);

  // Validate time selection using the service
  const validation = useMemo(() => {
    return TimeSlotValidationService.validateTimeSelection(startTime, endTime, hoursPerSlot);
  }, [startTime, endTime, hoursPerSlot]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4" />
        <h4 className="text-sm font-medium">Lab Slot {slotNumber}</h4>
        <Badge variant="outline" className="text-xs">
          {hoursPerSlot} hours
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <FormField
          control={form.control}
          name={dayField}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Day</FormLabel>
              <Select
                value={field.value}
                onValueChange={field.onChange}
                disabled={disabled}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Monday">Monday</SelectItem>
                  <SelectItem value="Tuesday">Tuesday</SelectItem>
                  <SelectItem value="Wednesday">Wednesday</SelectItem>
                  <SelectItem value="Thursday">Thursday</SelectItem>
                  <SelectItem value="Friday">Friday</SelectItem>
                  <SelectItem value="Saturday">Saturday</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={startTimeField}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Start Time</FormLabel>
              <Select
                value={field.value}
                onValueChange={field.onChange}
                disabled={disabled}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select start time" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {availableStartTimes.map((time) => (
                    <SelectItem key={time.value} value={time.value}>
                      {time.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={endTimeField}
          render={({ field }) => (
            <FormItem>
              <FormLabel>End Time</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Auto-calculated"
                  disabled={true}
                  className="bg-gray-50"
                />
              </FormControl>
              <FormDescription>
                Automatically calculated based on duration
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={batchField}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Batch</FormLabel>
              <FormControl>
                <Input
                  placeholder="e.g., A, B1, CSE-A"
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormDescription>Student batch for this lab</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Validation feedback */}
      {(startTime || endTime) && (
        <Alert className={`${
          validation.type === 'success' ? 'border-green-200 bg-green-50' :
          validation.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
          'border-red-200 bg-red-50'
        }`}>
          <div className="flex items-center gap-2">
            {validation.type === 'success' && <CheckCircle className="h-4 w-4 text-green-600" />}
            {validation.type === 'warning' && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
            {validation.type === 'error' && <AlertTriangle className="h-4 w-4 text-red-600" />}
            <AlertDescription className={`${
              validation.type === 'success' ? 'text-green-800' :
              validation.type === 'warning' ? 'text-yellow-800' :
              'text-red-800'
            }`}>
              {validation.message}
              {validation.periods && (
                <span className="ml-2 font-medium">({validation.periods})</span>
              )}
            </AlertDescription>
          </div>
        </Alert>
      )}
    </div>
  );
}
