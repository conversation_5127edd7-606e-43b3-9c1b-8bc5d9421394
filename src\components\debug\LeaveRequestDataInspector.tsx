import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Database, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Eye,
  Wrench
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { LeaveRequest } from '@/services/LeaveManagementService';

export default function LeaveRequestDataInspector() {
  const [leaveRequestId, setLeaveRequestId] = useState('');
  const [leaveRequest, setLeaveRequest] = useState<LeaveRequest | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fixing, setFixing] = useState(false);
  const [allRequests, setAllRequests] = useState<any[]>([]);
  const [loadingRequests, setLoadingRequests] = useState(false);

  const loadAllRequests = async () => {
    try {
      setLoadingRequests(true);
      setError(null);

      // Fetch recent leave requests with basic info
      const { data, error: fetchError } = await supabase
        .from('leave_requests')
        .select(`
          id,
          faculty_id,
          leave_type,
          start_date,
          end_date,
          status,
          total_classes_affected,
          substitution_status,
          applied_date
        `)
        .order('applied_date', { ascending: false })
        .limit(20);

      if (fetchError) {
        throw new Error(`Database error: ${fetchError.message}`);
      }

      // Get faculty names
      if (data && data.length > 0) {
        const facultyIds = [...new Set(data.map(req => req.faculty_id))];
        const { data: facultyData, error: facultyError } = await supabase
          .from('employee_details')
          .select('id, full_name, department')
          .in('id', facultyIds);

        if (!facultyError && facultyData) {
          const facultyMap = new Map(facultyData.map(f => [f.id, f]));
          const enrichedRequests = data.map(req => ({
            ...req,
            faculty_name: facultyMap.get(req.faculty_id)?.full_name || 'Unknown',
            faculty_department: facultyMap.get(req.faculty_id)?.department || 'Unknown'
          }));
          setAllRequests(enrichedRequests);
        } else {
          setAllRequests(data);
        }
      } else {
        setAllRequests([]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load requests');
      setAllRequests([]);
    } finally {
      setLoadingRequests(false);
    }
  };

  const inspectLeaveRequest = async () => {
    if (!leaveRequestId.trim()) {
      setError('Please enter a leave request ID');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch the leave request directly from database
      const { data, error: fetchError } = await supabase
        .from('leave_requests')
        .select('*')
        .eq('id', leaveRequestId.trim())
        .single();

      if (fetchError) {
        throw new Error(`Database error: ${fetchError.message}`);
      }

      if (!data) {
        throw new Error('Leave request not found');
      }

      setLeaveRequest(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setLeaveRequest(null);
    } finally {
      setLoading(false);
    }
  };

  const fixLeaveRequestData = async () => {
    if (!leaveRequest) return;

    try {
      setFixing(true);
      setError(null);

      // Get the faculty ID to re-analyze class impact
      const facultyId = leaveRequest.faculty_id;
      const startDate = leaveRequest.start_date;
      const endDate = leaveRequest.end_date;

      // Generate date range
      const dates: string[] = [];
      const start = new Date(startDate);
      const end = new Date(endDate);
      for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
        dates.push(date.toISOString().split('T')[0]);
      }

      console.log('🔧 Fixing leave request data for:', { facultyId, dates });

      // Re-analyze class impact with proper data
      const affectedClasses = [];
      
      for (const dateStr of dates) {
        const date = new Date(dateStr + 'T00:00:00');
        const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });

        // Query timetable with all fields
        const { data: timetableSlots, error: timetableError } = await supabase
          .from('timetable_slots')
          .select(`
            id, day, time_slot, subject_code, subject_name, subject_type,
            semester, section, department, room_number, batch_name, academic_year,
            faculty_id
          `)
          .eq('faculty_id', facultyId)
          .eq('day', dayName)
          .order('time_slot');

        if (timetableError) {
          console.error('Timetable query error:', timetableError);
          continue;
        }

        if (timetableSlots && timetableSlots.length > 0) {
          for (const slot of timetableSlots) {
            const periodMatch = slot.time_slot?.match(/Period (\d+)/);
            const periodNumber = periodMatch ? parseInt(periodMatch[1]) : 0;

            // Check if this class already has a substitute assignment
            const existingClass = leaveRequest.affected_classes?.find(
              (cls: any) => cls.id === `${slot.id}-${dateStr}`
            );

            const affectedClass = {
              id: `${slot.id}-${dateStr}`,
              day: dayName,
              time_slot: slot.time_slot || 'N/A',
              period_number: periodNumber,
              subject_code: slot.subject_code || 'N/A',
              subject_name: slot.subject_name || 'Unknown Subject',
              subject_type: slot.subject_type || 'theory',
              semester: slot.semester || 'N/A',
              section: slot.section || 'N/A',
              room_number: slot.room_number || undefined,
              batch_name: slot.batch_name || undefined,
              // Preserve existing substitute assignments if any
              substitute_faculty_id: existingClass?.substitute_faculty_id || undefined,
              substitute_faculty_name: existingClass?.substitute_faculty_name || undefined,
              substitution_notes: existingClass?.substitution_notes || undefined,
              substitution_status: existingClass?.substitute_faculty_id ? 'assigned' : 'pending'
            };

            affectedClasses.push(affectedClass);
          }
        }
      }

      // Apply enhanced enrichment to the fixed data
      console.log('🔧 Applying enhanced data enrichment...');

      // Import the enrichment function
      const { LeaveManagementService } = await import('@/services/LeaveManagementService');

      // Use the public enrichment method
      const enrichedClasses = await LeaveManagementService.enrichAffectedClassesData(affectedClasses);

      // Update the leave request with enhanced data
      const { error: updateError } = await supabase
        .from('leave_requests')
        .update({
          affected_classes: enrichedClasses,
          total_classes_affected: enrichedClasses.length,
          updated_at: new Date().toISOString()
        })
        .eq('id', leaveRequest.id);

      if (updateError) {
        throw new Error(`Update error: ${updateError.message}`);
      }

      // Refresh the data
      await inspectLeaveRequest();
      
      console.log('✅ Leave request data fixed successfully');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fix data');
    } finally {
      setFixing(false);
    }
  };

  const getDataQualityScore = () => {
    if (!leaveRequest?.affected_classes || !Array.isArray(leaveRequest.affected_classes)) {
      return { score: 0, issues: ['No affected classes data'], recommendations: ['Use the fix tool to regenerate class data'] };
    }

    const classes = leaveRequest.affected_classes;
    const issues: string[] = [];
    const recommendations: string[] = [];
    let totalScore = 0;
    let maxPossibleScore = 0;

    classes.forEach((cls: any, index: number) => {
      let classScore = 0;
      const classMaxScore = 100;
      maxPossibleScore += classMaxScore;

      // Subject Code Assessment (25 points)
      if (!cls.subject_code || cls.subject_code === 'N/A' || cls.subject_code === 'UNKNOWN') {
        issues.push(`Class ${index + 1}: Missing or invalid subject code`);
      } else if (cls.subject_code === 'COURSE-TBD') {
        issues.push(`Class ${index + 1}: Subject code pending (${cls.subject_code})`);
        classScore += 10; // Partial credit for professional fallback
      } else {
        classScore += 25; // Full credit for valid subject code
      }

      // Subject Name Assessment (25 points)
      if (!cls.subject_name || cls.subject_name === 'Unknown Subject' || cls.subject_name === 'N/A') {
        issues.push(`Class ${index + 1}: Missing or invalid subject name`);
      } else if (cls.subject_name.includes('Course') && cls.subject_name.includes('TBD')) {
        issues.push(`Class ${index + 1}: Subject name is placeholder (${cls.subject_name})`);
        classScore += 15; // Partial credit for descriptive fallback
      } else {
        classScore += 25; // Full credit for proper subject name
      }

      // Schedule Information Assessment (25 points)
      if (!cls.day || cls.day === 'Unknown' || cls.day === 'N/A') {
        issues.push(`Class ${index + 1}: Missing day information`);
      } else if (cls.day === 'Schedule TBD') {
        issues.push(`Class ${index + 1}: Day schedule pending`);
        classScore += 10; // Partial credit for professional fallback
      } else {
        classScore += 12.5; // Half credit for day
      }

      if (!cls.time_slot || cls.time_slot === 'Unknown' || cls.time_slot === 'N/A') {
        issues.push(`Class ${index + 1}: Missing time slot information`);
      } else if (cls.time_slot === 'Time TBD') {
        issues.push(`Class ${index + 1}: Time slot pending`);
        classScore += 5; // Partial credit for professional fallback
      } else {
        classScore += 12.5; // Half credit for time slot
      }

      // Class Details Assessment (25 points)
      if (!cls.semester || cls.semester === 'Unknown' || cls.semester === 'N/A') {
        issues.push(`Class ${index + 1}: Missing semester information`);
      } else if (cls.semester === 'TBD') {
        issues.push(`Class ${index + 1}: Semester information pending`);
        classScore += 5; // Partial credit for professional fallback
      } else {
        classScore += 12.5; // Half credit for semester
      }

      if (!cls.section || cls.section === 'Unknown' || cls.section === 'N/A') {
        issues.push(`Class ${index + 1}: Missing section information`);
      } else if (cls.section === 'TBD') {
        issues.push(`Class ${index + 1}: Section information pending`);
        classScore += 5; // Partial credit for professional fallback
      } else {
        classScore += 12.5; // Half credit for section
      }

      totalScore += classScore;
    });

    const finalScore = maxPossibleScore > 0 ? Math.round((totalScore / maxPossibleScore) * 100) : 0;

    // Generate recommendations based on issues
    if (issues.some(issue => issue.includes('Missing or invalid subject'))) {
      recommendations.push('Run the data fix tool to recover subject information from multiple database sources');
    }
    if (issues.some(issue => issue.includes('schedule pending') || issue.includes('Missing day') || issue.includes('Missing time'))) {
      recommendations.push('Verify timetable data is properly populated for the affected faculty');
    }
    if (issues.some(issue => issue.includes('semester') || issue.includes('section'))) {
      recommendations.push('Check class assignment data in the timetable system');
    }
    if (finalScore < 50) {
      recommendations.push('Consider regenerating the leave request with updated timetable data');
    }

    return {
      score: finalScore,
      issues,
      recommendations,
      classCount: classes.length,
      averageClassScore: classes.length > 0 ? Math.round(totalScore / classes.length) : 0
    };
  };

  const dataQuality = leaveRequest ? getDataQualityScore() : null;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5 text-blue-600" />
          Leave Request Data Inspector & Fixer
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Recent Requests List */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label>Recent Leave Requests</Label>
            <Button
              onClick={loadAllRequests}
              disabled={loadingRequests}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              {loadingRequests ? <RefreshCw className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
              Load Requests
            </Button>
          </div>

          {allRequests.length > 0 && (
            <div className="border rounded-lg max-h-64 overflow-y-auto">
              <div className="grid gap-1 p-2">
                {allRequests.map((request) => (
                  <div
                    key={request.id}
                    className="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer border"
                    onClick={() => setLeaveRequestId(request.id)}
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm truncate">
                          {request.faculty_name}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {request.leave_type}
                        </Badge>
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            request.status === 'approved' ? 'bg-green-100 text-green-800' :
                            'bg-red-100 text-red-800'
                          }`}
                        >
                          {request.status}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {request.start_date} to {request.end_date} • {request.total_classes_affected || 0} classes
                      </div>
                      <div className="text-xs text-gray-400 font-mono">
                        ID: {request.id}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        setLeaveRequestId(request.id);
                        inspectLeaveRequest();
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Manual Input Section */}
        <div className="space-y-3">
          <Label htmlFor="leave-request-id">Or Enter Leave Request ID Manually</Label>
          <div className="flex gap-2">
            <Input
              id="leave-request-id"
              placeholder="Enter leave request ID to inspect..."
              value={leaveRequestId}
              onChange={(e) => setLeaveRequestId(e.target.value)}
              className="flex-1 font-mono text-sm"
            />
            <Button
              onClick={inspectLeaveRequest}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
              Inspect
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        {/* Data Quality Assessment */}
        {leaveRequest && dataQuality && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold">Data Quality Assessment</h4>
              <Badge 
                variant="outline" 
                className={`${
                  dataQuality.score >= 80 ? 'bg-green-100 text-green-800 border-green-200' :
                  dataQuality.score >= 60 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                  'bg-red-100 text-red-800 border-red-200'
                }`}
              >
                {dataQuality.score}% Complete
              </Badge>
            </div>

            {dataQuality.issues.length > 0 && (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-red-700">Issues Found ({dataQuality.issues.length}):</Label>
                  <div className="mt-2 max-h-32 overflow-y-auto">
                    <ul className="text-sm space-y-1">
                      {dataQuality.issues.map((issue, index) => (
                        <li key={index} className="flex items-start gap-2 text-red-600">
                          <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                          <span>{issue}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {dataQuality.recommendations && dataQuality.recommendations.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium text-blue-700">Recommendations:</Label>
                    <ul className="text-sm space-y-1 mt-2">
                      {dataQuality.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-start gap-2 text-blue-600">
                          <CheckCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                          <span>{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span>Classes: {dataQuality.classCount}</span>
                  <span>Avg Score: {dataQuality.averageClassScore}%</span>
                </div>

                <Button
                  onClick={fixLeaveRequestData}
                  disabled={fixing}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 w-full"
                >
                  {fixing ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Wrench className="h-4 w-4" />}
                  Apply Comprehensive Data Fix
                </Button>
              </div>
            )}

            {dataQuality.issues.length === 0 && (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">No data quality issues found</span>
              </div>
            )}
          </div>
        )}

        {/* Raw Data Display */}
        {leaveRequest && (
          <div className="space-y-4">
            <h4 className="font-semibold">Raw Data Structure</h4>
            <div className="bg-gray-50 p-4 rounded-lg overflow-auto max-h-96">
              <pre className="text-xs">
                {JSON.stringify(leaveRequest, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
