import { useState, useEffect, useCallback } from 'react';
import { FacultyStudentListService, type StudentInfo } from '@/services/FacultyStudentListService';
import { useToast } from '@/hooks/use-toast';

interface UseFacultyStudentsProps {
  facultyId: string;
  userDepartment: string;
  subjectCode: string;
  semester: string;
  section: string;
  subjectType: string;
  day?: string;
  timeSlot?: string;
  enabled?: boolean;
}

interface UseFacultyStudentsReturn {
  students: StudentInfo[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  validateAccess: () => Promise<boolean>;
}

/**
 * Hook to dynamically load students for faculty based on subject type and teaching assignments
 */
export const useFacultyStudents = ({
  facultyId,
  userDepartment,
  subjectCode,
  semester,
  section,
  subjectType,
  day,
  timeSlot,
  enabled = true
}: UseFacultyStudentsProps): UseFacultyStudentsReturn => {
  const [students, setStudents] = useState<StudentInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const loadStudents = useCallback(async () => {
    if (!enabled || !facultyId || !userDepartment || !subjectCode || !semester || !section || !subjectType) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Validate faculty access first
      const hasAccess = await FacultyStudentListService.validateFacultySubjectAccess(
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        subjectType
      );

      if (!hasAccess) {
        throw new Error('You do not have access to this subject');
      }

      // Load appropriate students based on subject type
      const studentList = await FacultyStudentListService.getStudentsForFacultySubject(
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        subjectType,
        day,
        timeSlot
      );

      setStudents(studentList);

      // Show info about student list type
      if (studentList.length > 0) {
        const sourceType = studentList[0].source_type;
        let message = '';
        
        switch (sourceType) {
          case 'class':
            message = `Loaded ${studentList.length} students from class list`;
            break;
          case 'batch':
            const batchName = studentList[0].batch_name;
            message = `Loaded ${studentList.length} students from batch ${batchName}`;
            break;
          case 'elective':
            message = `Loaded ${studentList.length} students enrolled in elective`;
            break;
        }

        console.log(message, { subjectCode, subjectType, studentCount: studentList.length });
      } else {
        console.warn('No students found for this subject assignment');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load students';
      setError(errorMessage);
      console.error('Error loading faculty students:', err);
      
      toast({
        title: 'Error Loading Students',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [facultyId, userDepartment, subjectCode, semester, section, subjectType, day, timeSlot, enabled, toast]);

  const validateAccess = useCallback(async (): Promise<boolean> => {
    if (!facultyId || !userDepartment || !subjectCode || !semester || !section || !subjectType) {
      return false;
    }

    try {
      return await FacultyStudentListService.validateFacultySubjectAccess(
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        subjectType
      );
    } catch (err) {
      console.error('Error validating faculty access:', err);
      return false;
    }
  }, [facultyId, userDepartment, subjectCode, semester, section, subjectType]);

  // Load students when dependencies change
  useEffect(() => {
    loadStudents();
  }, [loadStudents]);

  return {
    students,
    loading,
    error,
    refetch: loadStudents,
    validateAccess
  };
};

/**
 * Hook to get current day's lab batch for a faculty member
 */
export const useCurrentLabBatch = (
  facultyId: string,
  userDepartment: string,
  subjectCode: string,
  semester: string,
  section: string
) => {
  const [batchName, setBatchName] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const getCurrentBatch = useCallback(async () => {
    if (!facultyId || !userDepartment || !subjectCode || !semester || !section) {
      return;
    }

    try {
      setLoading(true);
      const currentDay = new Date().toLocaleDateString('en-US', { weekday: 'long' });
      
      const batch = await FacultyStudentListService.getCurrentDayLabBatch(
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        currentDay
      );

      setBatchName(batch);
    } catch (err) {
      console.error('Error getting current lab batch:', err);
      setBatchName(null);
    } finally {
      setLoading(false);
    }
  }, [facultyId, userDepartment, subjectCode, semester, section]);

  useEffect(() => {
    getCurrentBatch();
  }, [getCurrentBatch]);

  return { batchName, loading, refetch: getCurrentBatch };
};

/**
 * Hook to get all teaching assignments for a faculty member
 */
export const useFacultyTeachingAssignments = (facultyId: string, userDepartment: string) => {
  const [assignments, setAssignments] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAssignments = useCallback(async () => {
    if (!facultyId || !userDepartment) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const teachingAssignments = await FacultyStudentListService.getFacultyTeachingAssignments(
        facultyId,
        userDepartment
      );

      setAssignments(teachingAssignments);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load teaching assignments';
      setError(errorMessage);
      console.error('Error loading teaching assignments:', err);
    } finally {
      setLoading(false);
    }
  }, [facultyId, userDepartment]);

  useEffect(() => {
    loadAssignments();
  }, [loadAssignments]);

  return { assignments, loading, error, refetch: loadAssignments };
};
