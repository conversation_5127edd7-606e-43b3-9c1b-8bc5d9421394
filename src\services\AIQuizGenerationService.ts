/**
 * AI Quiz Generation Service
 * 
 * This service handles AI-powered question generation using OpenAI's GPT-4 API.
 * It processes course materials and generates contextual quiz questions.
 */

import { 
  AIQuestionGenerationRequest, 
  AIQuestionGenerationResponse, 
  AIGeneratedQuestion,
  QuestionType,
  DifficultyLevel 
} from '@/types/quiz-system';

// OpenAI API configuration
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

export class AIQuizGenerationService {
  
  /**
   * Generate quiz questions from course content using AI
   */
  static async generateQuestions(request: AIQuestionGenerationRequest): Promise<AIQuestionGenerationResponse> {
    const startTime = Date.now();
    
    try {
      if (!OPENAI_API_KEY) {
        throw new Error('OpenAI API key not configured');
      }

      console.log('Generating questions with OpenAI...');
      console.log('API Key present:', OPENAI_API_KEY ? 'Yes' : 'No');

      const prompt = this.buildPrompt(request);

      const requestBody = {
        model: 'gpt-3.5-turbo', // Changed from gpt-4 to gpt-3.5-turbo
        messages: [
          {
            role: 'system',
            content: 'You are an expert educational content creator specializing in generating high-quality quiz questions from academic materials. You create questions that test understanding, application, and analysis of the subject matter. Always return valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      };

      console.log('Making OpenAI API request for question generation...');

      const response = await fetch(OPENAI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify(requestBody)
      });

      console.log('OpenAI API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('OpenAI API error response:', errorText);
        throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      const aiResponse = JSON.parse(data.choices[0].message.content);
      
      const processingTime = Date.now() - startTime;
      
      return {
        questions: this.validateAndFormatQuestions(aiResponse.questions),
        processing_time_ms: processingTime,
        confidence_score: aiResponse.confidence_score || 0.85,
        source_analysis: {
          key_topics: aiResponse.key_topics || [],
          content_complexity: aiResponse.content_complexity || request.difficulty_level,
          suggested_question_distribution: aiResponse.question_distribution || {}
        }
      };

    } catch (error) {
      console.error('AI question generation failed:', error);
      console.log('Using fallback question generation...');

      // Fallback: Generate sample questions without AI
      const fallbackQuestions = this.generateFallbackQuestions(request);

      return {
        questions: fallbackQuestions,
        processing_time_ms: Date.now() - startTime,
        confidence_score: 0.6, // Lower confidence for fallback
        source_analysis: {
          key_topics: ['General Topics'],
          content_complexity: request.difficulty_level,
          suggested_question_distribution: {
            multiple_choice: Math.floor(request.question_count * 0.7),
            true_false: Math.floor(request.question_count * 0.2),
            short_answer: Math.floor(request.question_count * 0.1)
          }
        }
      };
    }
  }

  /**
   * Generate fallback questions when AI is not available
   */
  private static generateFallbackQuestions(request: AIQuestionGenerationRequest): AIGeneratedQuestion[] {
    const questions: AIGeneratedQuestion[] = [];
    const questionCount = Math.min(request.question_count, 10); // Limit fallback questions

    for (let i = 0; i < questionCount; i++) {
      const questionType = request.question_types[i % request.question_types.length];

      let question: AIGeneratedQuestion;

      switch (questionType) {
        case 'multiple_choice':
          question = {
            question_text: `Sample multiple choice question ${i + 1} for ${request.subject_name}`,
            question_type: 'multiple_choice',
            options: {
              A: 'Option A',
              B: 'Option B',
              C: 'Option C',
              D: 'Option D'
            },
            correct_answer: 'A',
            difficulty_level: request.difficulty_level,
            topic_tags: [request.subject_name],
            ai_confidence_score: 0.6,
            source_material_reference: 'Fallback generation'
          };
          break;

        case 'true_false':
          question = {
            question_text: `Sample true/false question ${i + 1} for ${request.subject_name}`,
            question_type: 'true_false',
            correct_answer: i % 2 === 0 ? 'true' : 'false',
            difficulty_level: request.difficulty_level,
            topic_tags: [request.subject_name],
            ai_confidence_score: 0.6,
            source_material_reference: 'Fallback generation'
          };
          break;

        default:
          question = {
            question_text: `Sample short answer question ${i + 1} for ${request.subject_name}`,
            question_type: 'short_answer',
            correct_answer: 'Sample answer',
            difficulty_level: request.difficulty_level,
            topic_tags: [request.subject_name],
            ai_confidence_score: 0.6,
            source_material_reference: 'Fallback generation'
          };
      }

      questions.push(question);
    }

    return questions;
  }

  /**
   * Build the AI prompt for question generation
   */
  private static buildPrompt(request: AIQuestionGenerationRequest): string {
    const questionTypeInstructions = this.getQuestionTypeInstructions(request.question_types);
    const difficultyInstructions = this.getDifficultyInstructions(request.difficulty_level);
    
    return `
Generate ${request.question_count} high-quality quiz questions from the following course content for the subject "${request.subject_name}".

CONTENT TO ANALYZE:
${request.extracted_text}

REQUIREMENTS:
- Difficulty Level: ${request.difficulty_level} ${difficultyInstructions}
- Question Types: ${request.question_types.join(', ')} ${questionTypeInstructions}
- Subject: ${request.subject_name}
- Focus Topics: ${request.topic_focus?.join(', ') || 'All relevant topics from the content'}

RESPONSE FORMAT:
Return a JSON object with the following structure:
{
  "questions": [
    {
      "question_text": "Clear, well-formed question text",
      "question_type": "multiple_choice|true_false|short_answer|essay",
      "options": {"A": "Option 1", "B": "Option 2", "C": "Option 3", "D": "Option 4"}, // Only for multiple_choice
      "correct_answer": "A|B|C|D|true|false|expected answer text",
      "difficulty_level": "easy|medium|hard",
      "topic_tags": ["topic1", "topic2"],
      "ai_confidence_score": 0.95,
      "source_material_reference": "Brief reference to source content"
    }
  ],
  "confidence_score": 0.90,
  "key_topics": ["topic1", "topic2", "topic3"],
  "content_complexity": "medium",
  "question_distribution": {
    "multiple_choice": 15,
    "true_false": 3,
    "short_answer": 2
  }
}

QUALITY GUIDELINES:
1. Questions should test understanding, not just memorization
2. Multiple choice options should be plausible and well-distributed
3. Avoid ambiguous wording or trick questions
4. Ensure questions are directly related to the provided content
5. Include a mix of factual recall and conceptual understanding
6. For technical subjects, include application-based questions
7. Tag questions with relevant topics for categorization

Generate exactly ${request.question_count} questions following these guidelines.
`;
  }

  /**
   * Get instructions for specific question types
   */
  private static getQuestionTypeInstructions(questionTypes: QuestionType[]): string {
    const instructions: Record<QuestionType, string> = {
      multiple_choice: 'Create 4 options (A, B, C, D) with one clearly correct answer and plausible distractors.',
      true_false: 'Create clear statements that are definitively true or false based on the content.',
      short_answer: 'Create questions requiring 1-3 sentence answers demonstrating understanding.',
      essay: 'Create questions requiring detailed explanations or analysis (100-200 words).'
    };

    return questionTypes.map(type => `${type}: ${instructions[type]}`).join(' ');
  }

  /**
   * Get instructions for difficulty levels
   */
  private static getDifficultyInstructions(difficulty: DifficultyLevel): string {
    const instructions: Record<DifficultyLevel, string> = {
      easy: 'Focus on basic recall and simple understanding of key concepts.',
      medium: 'Include application of concepts and moderate analysis.',
      hard: 'Require synthesis, evaluation, and complex problem-solving.',
      mixed: 'Include a balanced mix of easy (30%), medium (50%), and hard (20%) questions.'
    };

    return instructions[difficulty];
  }

  /**
   * Validate and format AI-generated questions
   */
  private static validateAndFormatQuestions(questions: any[]): AIGeneratedQuestion[] {
    return questions.map((q, index) => {
      // Validate required fields
      if (!q.question_text || !q.question_type || !q.correct_answer) {
        throw new Error(`Invalid question at index ${index}: missing required fields`);
      }

      // Validate question type
      if (!['multiple_choice', 'true_false', 'short_answer', 'essay'].includes(q.question_type)) {
        throw new Error(`Invalid question type at index ${index}: ${q.question_type}`);
      }

      // Validate multiple choice questions have options
      if (q.question_type === 'multiple_choice' && (!q.options || Object.keys(q.options).length < 2)) {
        throw new Error(`Multiple choice question at index ${index} missing valid options`);
      }

      // Validate true/false questions
      if (q.question_type === 'true_false' && !['true', 'false'].includes(q.correct_answer.toLowerCase())) {
        throw new Error(`True/false question at index ${index} has invalid correct answer`);
      }

      return {
        question_text: q.question_text.trim(),
        question_type: q.question_type,
        options: q.question_type === 'multiple_choice' ? q.options : undefined,
        correct_answer: q.correct_answer,
        difficulty_level: q.difficulty_level || 'medium',
        topic_tags: Array.isArray(q.topic_tags) ? q.topic_tags : [],
        ai_confidence_score: Math.min(Math.max(q.ai_confidence_score || 0.8, 0), 1),
        source_material_reference: q.source_material_reference || ''
      };
    });
  }

  /**
   * Extract text from uploaded files
   */
  static async extractTextFromFile(file: File): Promise<string> {
    try {
      if (file.type === 'application/pdf') {
        return await this.extractTextFromPDF(file);
      } else if (file.type.includes('text')) {
        return await this.extractTextFromTextFile(file);
      } else if (file.type.includes('document') || file.type.includes('word')) {
        // For Word documents, you might need additional libraries
        throw new Error('Word document processing not yet implemented. Please convert to PDF or text format.');
      } else {
        throw new Error(`Unsupported file type: ${file.type}`);
      }
    } catch (error) {
      console.error('Text extraction failed:', error);
      throw new Error(`Failed to extract text from file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract text from PDF files
   */
  private static async extractTextFromPDF(file: File): Promise<string> {
    // For now, return a placeholder message
    // In production, implement with pdf-parse or similar library
    return `[PDF Content Placeholder - File: ${file.name}]

This is a placeholder for PDF text extraction. In a production environment,
you would implement this using libraries like:
- pdf-parse (Node.js)
- PDF.js (Browser)
- pdfplumber (Python backend)

The extracted text would contain the actual content from the PDF file
which would then be processed by the AI to generate relevant quiz questions.`;
  }

  /**
   * Extract text from text files
   */
  private static async extractTextFromTextFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        resolve(text);
      };
      reader.onerror = () => reject(new Error('Failed to read text file'));
      reader.readAsText(file);
    });
  }

  /**
   * Analyze content complexity and suggest question distribution
   */
  static async analyzeContentComplexity(text: string, subjectName: string): Promise<{
    complexity: DifficultyLevel;
    keyTopics: string[];
    suggestedDistribution: Record<QuestionType, number>;
  }> {
    try {
      // Check API key
      if (!OPENAI_API_KEY) {
        console.warn('OpenAI API key not configured, using default analysis');
        throw new Error('OpenAI API key not configured');
      }

      console.log('Analyzing content complexity with OpenAI...');
      console.log('API Key present:', OPENAI_API_KEY ? 'Yes' : 'No');
      console.log('API Key length:', OPENAI_API_KEY?.length || 0);

      const prompt = `
Analyze the following educational content for the subject "${subjectName}" and provide:

CONTENT:
${text.substring(0, 2000)}...

Return a JSON object with:
{
  "complexity": "easy|medium|hard",
  "keyTopics": ["topic1", "topic2", "topic3"],
  "suggestedDistribution": {
    "multiple_choice": 15,
    "true_false": 3,
    "short_answer": 2,
    "essay": 0
  }
}

Base the complexity on:
- Vocabulary level and technical terms
- Concept depth and abstraction
- Prerequisites required
- Application complexity

Suggest question distribution based on content type and complexity.
`;

      const requestBody = {
        model: 'gpt-3.5-turbo', // Changed from gpt-4 to gpt-3.5-turbo
        messages: [
          {
            role: 'system',
            content: 'You are an educational content analyst. Analyze content complexity and suggest appropriate question types. Return only valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000
      };

      console.log('Making OpenAI API request...');
      console.log('Request URL:', OPENAI_API_URL);
      console.log('Request model:', requestBody.model);

      const response = await fetch(OPENAI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify(requestBody)
      });

      console.log('OpenAI API response status:', response.status);
      console.log('OpenAI API response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('OpenAI API error response:', errorText);
        throw new Error(`Analysis failed: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      const analysis = JSON.parse(data.choices[0].message.content);

      return {
        complexity: analysis.complexity || 'medium',
        keyTopics: analysis.keyTopics || [],
        suggestedDistribution: analysis.suggestedDistribution || {
          multiple_choice: 15,
          true_false: 3,
          short_answer: 2,
          essay: 0
        }
      };

    } catch (error) {
      console.error('Content analysis failed:', error);
      console.log('Using fallback content analysis...');

      // Fallback: Basic text analysis without AI
      const fallbackAnalysis = this.performBasicContentAnalysis(text, subjectName);

      return {
        complexity: fallbackAnalysis.complexity,
        keyTopics: fallbackAnalysis.keyTopics,
        suggestedDistribution: {
          multiple_choice: 15,
          true_false: 3,
          short_answer: 2,
          essay: 0
        }
      };
    }
  }

  /**
   * Perform basic content analysis without AI
   */
  private static performBasicContentAnalysis(text: string, subjectName: string): {
    complexity: DifficultyLevel;
    keyTopics: string[];
  } {
    const words = text.toLowerCase().split(/\s+/);
    const wordCount = words.length;

    // Basic complexity assessment
    let complexity: DifficultyLevel = 'medium';

    // Count technical terms and complex words
    const technicalTerms = words.filter(word =>
      word.length > 8 ||
      /^(algorithm|implementation|architecture|methodology|analysis|synthesis|evaluation)/.test(word)
    ).length;

    const complexityRatio = technicalTerms / wordCount;

    if (complexityRatio > 0.15) {
      complexity = 'hard';
    } else if (complexityRatio < 0.05) {
      complexity = 'easy';
    }

    // Extract potential topics (simple keyword extraction)
    const commonWords = new Set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'a', 'an']);

    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      if (word.length > 3 && !commonWords.has(word)) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
      }
    });

    const keyTopics = Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([word]) => word.charAt(0).toUpperCase() + word.slice(1));

    return { complexity, keyTopics };
  }
}
