import { supabase } from '@/integrations/supabase/client';

/**
 * AI-Powered Quiz Management System Database Schema
 * 
 * This script creates new tables for the AI quiz system without modifying existing schema.
 * All tables reference existing tables (employee_details, class_students, subjects, etc.)
 * 
 * Features:
 * - Course material upload and AI processing
 * - AI-generated quiz questions with faculty review
 * - Quiz scheduling and student access control
 * - Automated grading and analytics
 * - Integration with existing faculty-student relationships
 */

async function executeQuery(query: string) {
  const { data, error } = await supabase.rpc('execute_sql', {
    sql_query: query
  });
  
  if (error) {
    console.error('❌ Query failed:', error);
    throw error;
  }
  
  return data;
}

async function installAIQuizSystem() {
  try {
    console.log("🚀 Installing AI-Powered Quiz Management System...");
    console.log("📋 Creating new tables without modifying existing schema...");
    
    // Step 1: Course Materials Storage
    console.log("📚 Step 1: Creating course_materials table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS course_materials (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          faculty_id UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
          subject_code TEXT NOT NULL,
          subject_name TEXT NOT NULL,
          department TEXT NOT NULL,
          semester TEXT NOT NULL,
          section TEXT,
          academic_year TEXT NOT NULL,
          
          -- File information
          file_name TEXT NOT NULL,
          file_path TEXT NOT NULL,
          file_type TEXT NOT NULL CHECK (file_type IN ('pdf', 'doc', 'docx', 'txt')),
          file_size_bytes BIGINT NOT NULL,
          
          -- AI processing status
          processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
          extracted_text TEXT,
          ai_analysis JSONB,
          processing_error TEXT,
          
          -- Metadata
          upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          processed_date TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Step 2: Quiz Templates
    console.log("📝 Step 2: Creating quiz_templates table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS quiz_templates (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          faculty_id UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
          
          -- Subject and class information (references existing mappings)
          subject_code TEXT NOT NULL,
          subject_name TEXT NOT NULL,
          department TEXT NOT NULL,
          semester TEXT NOT NULL,
          section TEXT,
          academic_year TEXT NOT NULL,
          
          -- Quiz details
          title TEXT NOT NULL,
          description TEXT,
          instructions TEXT,
          duration_minutes INTEGER DEFAULT 60,
          total_marks INTEGER DEFAULT 20,
          passing_marks INTEGER DEFAULT 12,
          
          -- AI generation settings
          source_material_ids UUID[] DEFAULT '{}',
          ai_generation_prompt TEXT,
          difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard', 'mixed')),
          question_types TEXT[] DEFAULT '{"multiple_choice"}',
          
          -- Status and workflow
          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'ai_generated', 'reviewed', 'published', 'archived')),
          is_ai_generated BOOLEAN DEFAULT false,
          review_notes TEXT,
          
          -- Metadata
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          published_at TIMESTAMP WITH TIME ZONE
      );
    `);

    // Step 3: Quiz Questions
    console.log("❓ Step 3: Creating quiz_questions table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS quiz_questions (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          quiz_template_id UUID NOT NULL REFERENCES quiz_templates(id) ON DELETE CASCADE,
          
          -- Question content
          question_text TEXT NOT NULL,
          question_type TEXT DEFAULT 'multiple_choice' CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'essay')),
          
          -- Multiple choice options (stored as JSON)
          options JSONB, -- {"A": "Option 1", "B": "Option 2", "C": "Option 3", "D": "Option 4"}
          correct_answer TEXT NOT NULL, -- For MCQ: "A", for T/F: "true"/"false", for others: expected answer
          
          -- Question metadata
          marks INTEGER DEFAULT 1,
          difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
          topic_tags TEXT[] DEFAULT '{}',
          
          -- AI generation info
          is_ai_generated BOOLEAN DEFAULT false,
          ai_confidence_score DECIMAL(3,2), -- 0.00 to 1.00
          source_material_reference TEXT,
          
          -- Faculty review
          is_reviewed BOOLEAN DEFAULT false,
          review_status TEXT DEFAULT 'pending' CHECK (review_status IN ('pending', 'approved', 'rejected', 'modified')),
          faculty_modifications TEXT,
          
          -- Ordering and display
          question_order INTEGER NOT NULL,
          
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Step 4: Quiz Schedules
    console.log("📅 Step 4: Creating quiz_schedules table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS quiz_schedules (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          quiz_template_id UUID NOT NULL REFERENCES quiz_templates(id) ON DELETE CASCADE,
          faculty_id UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
          
          -- Scheduling details
          title TEXT NOT NULL,
          start_time TIMESTAMP WITH TIME ZONE NOT NULL,
          end_time TIMESTAMP WITH TIME ZONE NOT NULL,
          duration_minutes INTEGER NOT NULL,
          
          -- Access control (references existing student data)
          target_department TEXT NOT NULL,
          target_semester TEXT NOT NULL,
          target_section TEXT,
          academic_year TEXT NOT NULL,
          
          -- Quiz settings
          max_attempts INTEGER DEFAULT 1,
          show_results_immediately BOOLEAN DEFAULT true,
          randomize_questions BOOLEAN DEFAULT true,
          randomize_options BOOLEAN DEFAULT true,
          
          -- Status
          status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'active', 'completed', 'cancelled')),
          
          -- Notifications
          notification_sent BOOLEAN DEFAULT false,
          reminder_sent BOOLEAN DEFAULT false,
          
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Step 5: Student Quiz Attempts
    console.log("👨‍🎓 Step 5: Creating student_quiz_attempts table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS student_quiz_attempts (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          quiz_schedule_id UUID NOT NULL REFERENCES quiz_schedules(id) ON DELETE CASCADE,
          
          -- Student identification (references existing student data)
          student_usn TEXT NOT NULL, -- References class_students.usn
          student_name TEXT NOT NULL,
          department TEXT NOT NULL,
          semester TEXT NOT NULL,
          section TEXT NOT NULL,
          
          -- Attempt details
          attempt_number INTEGER DEFAULT 1,
          start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          end_time TIMESTAMP WITH TIME ZONE,
          submission_time TIMESTAMP WITH TIME ZONE,
          
          -- Scoring
          total_questions INTEGER NOT NULL,
          attempted_questions INTEGER DEFAULT 0,
          correct_answers INTEGER DEFAULT 0,
          score DECIMAL(5,2) DEFAULT 0.00,
          percentage DECIMAL(5,2) DEFAULT 0.00,
          
          -- Status
          status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'auto_submitted', 'abandoned')),
          
          -- Time tracking
          time_spent_minutes INTEGER DEFAULT 0,
          
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Step 6: Student Answers
    console.log("✍️ Step 6: Creating student_quiz_answers table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS student_quiz_answers (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          attempt_id UUID NOT NULL REFERENCES student_quiz_attempts(id) ON DELETE CASCADE,
          question_id UUID NOT NULL REFERENCES quiz_questions(id) ON DELETE CASCADE,
          
          -- Answer details
          student_answer TEXT,
          is_correct BOOLEAN DEFAULT false,
          marks_awarded DECIMAL(4,2) DEFAULT 0.00,
          
          -- Timing
          time_spent_seconds INTEGER DEFAULT 0,
          answered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    console.log("🔗 Step 7: Creating indexes for performance...");
    
    // Performance indexes
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_course_materials_faculty_subject 
      ON course_materials(faculty_id, subject_code, department, semester);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_quiz_templates_faculty_subject 
      ON quiz_templates(faculty_id, subject_code, department, semester);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_quiz_schedules_target_class 
      ON quiz_schedules(target_department, target_semester, target_section, academic_year);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_student_attempts_usn_quiz 
      ON student_quiz_attempts(student_usn, quiz_schedule_id);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_quiz_answers_attempt_question 
      ON student_quiz_answers(attempt_id, question_id);
    `);

    console.log("🔒 Step 8: Setting up Row Level Security (RLS)...");
    
    // Enable RLS on all tables
    const tables = [
      'course_materials',
      'quiz_templates', 
      'quiz_questions',
      'quiz_schedules',
      'student_quiz_attempts',
      'student_quiz_answers'
    ];
    
    for (const table of tables) {
      await executeQuery(`ALTER TABLE ${table} ENABLE ROW LEVEL SECURITY;`);
    }

    console.log("✅ AI-Powered Quiz Management System installation completed!");
    console.log("");
    console.log("📊 Summary of new tables created:");
    console.log("  • course_materials - Store and process course content");
    console.log("  • quiz_templates - Quiz definitions and AI settings");
    console.log("  • quiz_questions - Individual questions with AI metadata");
    console.log("  • quiz_schedules - Quiz timing and access control");
    console.log("  • student_quiz_attempts - Student quiz sessions");
    console.log("  • student_quiz_answers - Individual answer records");
    console.log("");
    console.log("🔗 All tables reference existing schema:");
    console.log("  • employee_details (faculty_id)");
    console.log("  • class_students (student_usn)");
    console.log("  • Existing subject and department data");
    console.log("");
    console.log("🎉 Ready to integrate with Faculty Dashboard!");

  } catch (error) {
    console.error('❌ Installation failed:', error);
    throw error;
  }
}

// Export the installation function
export { installAIQuizSystem };

// Auto-run the installation
installAIQuizSystem()
  .then(() => {
    console.log('✅ Installation completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Installation failed:', error);
    process.exit(1);
  });
