const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';
const supabase = createClient(supabaseUrl, supabaseKey);

async function executeQuery(query) {
  console.log('Executing query:', query.substring(0, 100) + '...');
  
  try {
    const { data, error } = await supabase.rpc('execute_sql', {
      sql_query: query
    });
    
    if (error) {
      console.error('Query error:', error);
      throw error;
    }
    
    console.log('✅ Query executed successfully');
    return data;
  } catch (error) {
    console.error('❌ Query execution failed:', error);
    throw error;
  }
}

async function installStudentProctoringSystem() {
  try {
    console.log("🚀 Installing Student Proctoring System...");
    
    // Step 1: Create faculty_student_proctoring table
    console.log("📋 Step 1: Creating faculty_student_proctoring table...");
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS faculty_student_proctoring (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          faculty_id UUID NOT NULL REFERENCES employee_details(id) ON DELETE CASCADE,
          student_usn TEXT NOT NULL, -- References class_students.usn
          student_name TEXT NOT NULL,
          department TEXT NOT NULL,
          semester TEXT NOT NULL,
          section TEXT NOT NULL,
          academic_year TEXT NOT NULL,
          assigned_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          assigned_by UUID REFERENCES employee_details(id),
          is_active BOOLEAN DEFAULT true,
          notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          
          -- Ensure one active proctoring relationship per student
          UNIQUE(student_usn, academic_year, is_active)
      );
    `);

    // Step 2: Create indexes for performance
    console.log("🔗 Step 2: Creating indexes for performance...");
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_faculty_student_proctoring_faculty 
      ON faculty_student_proctoring(faculty_id, is_active, academic_year);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_faculty_student_proctoring_student 
      ON faculty_student_proctoring(student_usn, is_active);
    `);
    
    await executeQuery(`
      CREATE INDEX IF NOT EXISTS idx_faculty_student_proctoring_department 
      ON faculty_student_proctoring(department, semester, section, academic_year);
    `);

    // Step 3: Create function to get unassigned students
    console.log("🔍 Step 3: Creating function to get unassigned students...");
    await executeQuery(`
      CREATE OR REPLACE FUNCTION get_unassigned_students(
        p_department TEXT DEFAULT NULL,
        p_semester TEXT DEFAULT NULL,
        p_section TEXT DEFAULT NULL,
        p_academic_year TEXT DEFAULT '2024-25'
      )
      RETURNS TABLE (
        id TEXT,
        usn TEXT,
        student_name TEXT,
        email TEXT,
        department TEXT,
        semester TEXT,
        section TEXT,
        academic_year TEXT
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          cs.id::TEXT,
          cs.usn,
          cs.student_name,
          cs.email,
          cs.department,
          cs.semester,
          cs.section,
          cs.academic_year
        FROM class_students cs
        WHERE cs.academic_year = p_academic_year
          AND (p_department IS NULL OR cs.department = p_department)
          AND (p_semester IS NULL OR cs.semester = p_semester)
          AND (p_section IS NULL OR cs.section = p_section)
          AND NOT EXISTS (
            SELECT 1 FROM faculty_student_proctoring fsp
            WHERE fsp.student_usn = cs.usn
              AND fsp.academic_year = cs.academic_year
              AND fsp.is_active = true
          )
        ORDER BY cs.semester, cs.section, cs.student_name;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `);

    // Step 4: Create function to get faculty's proctor students
    console.log("👥 Step 4: Creating function to get faculty's proctor students...");
    await executeQuery(`
      CREATE OR REPLACE FUNCTION get_faculty_proctor_students(
        p_faculty_id UUID,
        p_academic_year TEXT DEFAULT '2024-25'
      )
      RETURNS TABLE (
        id UUID,
        student_usn TEXT,
        student_name TEXT,
        department TEXT,
        semester TEXT,
        section TEXT,
        academic_year TEXT,
        assigned_date TIMESTAMP WITH TIME ZONE,
        notes TEXT,
        student_email TEXT,
        student_mobile TEXT
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          fsp.id,
          fsp.student_usn,
          fsp.student_name,
          fsp.department,
          fsp.semester,
          fsp.section,
          fsp.academic_year,
          fsp.assigned_date,
          fsp.notes,
          cs.email as student_email,
          cs.student_mobile
        FROM faculty_student_proctoring fsp
        LEFT JOIN class_students cs ON cs.usn = fsp.student_usn
        WHERE fsp.faculty_id = p_faculty_id
          AND fsp.academic_year = p_academic_year
          AND fsp.is_active = true
        ORDER BY fsp.semester, fsp.section, fsp.student_name;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `);

    console.log("✅ Student Proctoring System installation completed successfully!");
    
  } catch (error) {
    console.error("❌ Installation failed:", error);
    throw error;
  }
}

// Execute the installation
installStudentProctoringSystem()
  .then(() => {
    console.log("🎉 Student Proctoring System is ready!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Installation failed:", error);
    process.exit(1);
  });
