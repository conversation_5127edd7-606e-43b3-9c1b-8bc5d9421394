import { supabase } from "@/integrations/supabase/client";
import * as XLSX from 'xlsx';

export interface Student {
  id: string;
  roll_number: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  parent_name?: string;
  father_phone?: string;
  mother_phone?: string;
  student_phone?: string;
  parent_email?: string;
  student_email?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

export interface StudentFormData {
  roll_number: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  parent_name?: string;
  father_phone?: string;
  mother_phone?: string;
  student_phone?: string;
  parent_email?: string;
  student_email?: string;
}

export interface LabBatch {
  id: string;
  batch_name: string;
  department: string;
  semester: string;
  section: string;
  subject_code: string;
  academic_year: string;
  created_at: string;
  students?: Student[];
}

export interface ElectiveSelection {
  id: string;
  student_id: string;
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  academic_year: string;
  selected_at: string;
  student?: Student;
}

export class StudentService {
  /**
   * Map department names from class_teachers table to class_students table format
   */
  private static mapDepartmentName(fullDepartmentName: string): string[] {
    const departmentMap: Record<string, string[]> = {
      'Computer Science and Engineering': ['CSE', 'cse', 'Computer Science and Engineering'],
      'Information Science and Engineering': ['ISE', 'ise', 'Information Science and Engineering'],
      'Electronics and Communication Engineering': ['ECE', 'ece', 'Electronics and Communication Engineering'],
      'Mechanical Engineering': ['MECH', 'mech', 'Mechanical Engineering'],
      'Civil Engineering': ['CIVIL', 'civil', 'Civil Engineering'],
      'Electrical and Electronics Engineering': ['EEE', 'eee', 'Electrical and Electronics Engineering']
    };

    return departmentMap[fullDepartmentName] || [fullDepartmentName, fullDepartmentName.toLowerCase(), fullDepartmentName.toUpperCase()];
  }

  /**
   * Get students for class teacher (restricted to their assigned class)
   */
  static async getStudentsForClassTeacher(
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<Student[]> {
    try {
      // First get the class teacher assignment
      const { data: assignment, error: assignmentError } = await supabase
        .from('class_teachers')
        .select('department, semester, section')
        .eq('faculty_id', facultyId)
        .eq('academic_year', academicYear)
        .single();

      if (assignmentError || !assignment) {
        throw new Error('No class teacher assignment found');
      }

      // Get students for the assigned class
      const { data, error } = await supabase
        .from('students')
        .select('*')
        .eq('department', assignment.department)
        .eq('semester', assignment.semester)
        .eq('section', assignment.section)
        .eq('is_active', true)
        .order('roll_number');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching students for class teacher:', error);
      throw error;
    }
  }

  /**
   * Get all students for a specific department, semester, and section
   */
  static async getStudents(
    userDepartment: string,
    semester?: string,
    section?: string
  ): Promise<Student[]> {
    try {
      let query = supabase
        .from('students')
        .select('*')
        .eq('department', userDepartment)
        .eq('is_active', true)
        .order('roll_number');

      if (semester) {
        query = query.eq('semester', semester);
      }

      if (section) {
        query = query.eq('section', section);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching students:', error);
      throw error;
    }
  }

  /**
   * Create a new student with manual roll number
   */
  static async createStudent(
    studentData: StudentFormData,
    createdBy: string
  ): Promise<Student> {
    try {
      // Validate required fields
      if (!studentData.roll_number) {
        throw new Error('Roll number is required');
      }

      // Validate roll number format (1KS25CS001)
      const rollNumberPattern = /^1KS[0-9]{2}[A-Z]{2,4}[0-9]{3}$/;
      if (!rollNumberPattern.test(studentData.roll_number)) {
        throw new Error('Roll number must follow format: 1KS25CS001 (1KS + year + department + 3-digit number)');
      }

      // Validate roll number uniqueness
      const isUnique = await this.isRollNumberUnique(studentData.roll_number);
      if (!isUnique) {
        throw new Error('Roll number already exists');
      }

      // Generate default password (same as roll number)
      const defaultPassword = studentData.roll_number;

      const { data, error } = await supabase
        .from('students')
        .insert({
          ...studentData,
          password_hash: defaultPassword, // In production, this should be properly hashed
          created_by: createdBy,
          first_login: true
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating student:', error);
      throw error;
    }
  }

  /**
   * Create student for class teacher (with validation)
   */
  static async createStudentForClassTeacher(
    studentData: StudentFormData,
    facultyId: string,
    createdBy: string,
    academicYear: string = '2024-2025'
  ): Promise<Student> {
    try {
      // Validate class teacher assignment
      const { data: assignment, error: assignmentError } = await supabase
        .from('class_teachers')
        .select('department, semester, section')
        .eq('faculty_id', facultyId)
        .eq('academic_year', academicYear)
        .single();

      if (assignmentError || !assignment) {
        throw new Error('You are not assigned as a class teacher');
      }

      // Validate that student data matches class teacher's assignment
      if (
        studentData.department !== assignment.department ||
        studentData.semester !== assignment.semester ||
        studentData.section !== assignment.section
      ) {
        throw new Error('You can only add students to your assigned class');
      }

      return await this.createStudent(studentData, createdBy);
    } catch (error) {
      console.error('Error creating student for class teacher:', error);
      throw error;
    }
  }

  /**
   * Update student information
   */
  static async updateStudent(
    studentId: string,
    studentData: Partial<StudentFormData>
  ): Promise<Student> {
    try {
      const { data, error } = await supabase
        .from('students')
        .update(studentData)
        .eq('id', studentId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating student:', error);
      throw error;
    }
  }

  /**
   * Delete (deactivate) a student
   */
  static async deleteStudent(studentId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('students')
        .update({ is_active: false })
        .eq('id', studentId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting student:', error);
      throw error;
    }
  }

  /**
   * Bulk upload students from Excel/CSV data
   */
  static async bulkUploadStudents(
    studentsData: StudentFormData[],
    createdBy: string
  ): Promise<{ success: Student[], errors: { row: number, error: string }[] }> {
    const success: Student[] = [];
    const errors: { row: number, error: string }[] = [];

    for (let i = 0; i < studentsData.length; i++) {
      try {
        const student = await this.createStudent(studentsData[i], createdBy);
        success.push(student);
      } catch (error) {
        errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { success, errors };
  }

  /**
   * Bulk upload students for class teacher (with validation)
   */
  static async bulkUploadStudentsForClassTeacher(
    studentsData: StudentFormData[],
    facultyId: string,
    createdBy: string,
    academicYear: string = '2024-2025'
  ): Promise<{ success: Student[], errors: { row: number, error: string }[] }> {
    const success: Student[] = [];
    const errors: { row: number, error: string }[] = [];

    // Validate class teacher assignment first
    try {
      const { data: assignment, error: assignmentError } = await supabase
        .from('class_teachers')
        .select('department, semester, section')
        .eq('faculty_id', facultyId)
        .eq('academic_year', academicYear)
        .single();

      if (assignmentError || !assignment) {
        throw new Error('You are not assigned as a class teacher');
      }

      // Process each student
      for (let i = 0; i < studentsData.length; i++) {
        try {
          const studentData = studentsData[i];

          // Validate that student data matches class teacher's assignment
          if (
            studentData.department !== assignment.department ||
            studentData.semester !== assignment.semester ||
            studentData.section !== assignment.section
          ) {
            throw new Error('Student data does not match your assigned class');
          }

          const student = await this.createStudent(studentData, createdBy);
          success.push(student);
        } catch (error) {
          errors.push({
            row: i + 1,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    } catch (error) {
      // If class teacher validation fails, add error for all rows
      for (let i = 0; i < studentsData.length; i++) {
        errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Class teacher validation failed'
        });
      }
    }

    return { success, errors };
  }

  /**
   * Bulk create student authentication accounts from class_students data
   */
  static async bulkCreateStudentAuthAccounts(
    department: string,
    semester: string,
    section: string,
    academicYear: string,
    createdBy: string
  ): Promise<{ success: any[], errors: { row: number, error: string }[] }> {
    try {
      // Import here to avoid circular dependency
      const { SimpleStudentAuthService } = await import('./SimpleStudentAuthService');

      // Get students from class_students table
      const { data: classStudents, error: fetchError } = await supabase
        .from('class_students')
        .select('usn, student_name, email')
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .eq('academic_year', academicYear);

      if (fetchError) {
        throw new Error(`Failed to fetch class students: ${fetchError.message}`);
      }

      if (!classStudents || classStudents.length === 0) {
        throw new Error('No students found in the specified class. Please upload class students first.');
      }

      console.log(`📋 Found ${classStudents.length} students to create auth accounts for`);

      // Prepare data for bulk registration
      const studentsForAuth = classStudents.map(student => ({
        usn: student.usn,
        email: student.email || `${student.usn.toLowerCase()}@student.college.edu`,
        student_name: student.student_name
      }));

      // Bulk register students
      const results = await SimpleStudentAuthService.bulkRegisterStudents(studentsForAuth, createdBy);

      console.log(`✅ Auth account creation completed: ${results.success.length} success, ${results.errors.length} errors`);

      return results;
    } catch (error) {
      console.error('Error creating student auth accounts:', error);
      throw error;
    }
  }

  /**
   * Parse Excel file for student data with flexible column detection
   * Supports SLNO, USN, NAME pattern detection as specified in requirements
   */
  static parseExcelFile(file: File): Promise<StudentFormData[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          const students: StudentFormData[] = jsonData.map((row: any) => ({
            roll_number: row['Roll Number'] || '',
            student_name: row['Student Name'] || '',
            department: row['Department'] || '',
            semester: row['Semester'] || '',
            section: row['Section'] || '',
            parent_name: row['Parent Name'] || '',
            father_phone: row['Father Phone'] || '',
            mother_phone: row['Mother Phone'] || '',
            student_phone: row['Student Phone'] || '',
            parent_email: row['Parent Email'] || '',
            student_email: row['Student Email'] || ''
          }));

          resolve(students);
        } catch (error) {
          reject(new Error('Failed to parse Excel file'));
        }
      };

      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * Parse Excel file for Class Teacher Student Management
   * Uses SLNO, USN, NAME, EMAIL, STUDENT MOBILE, FATHER MOBILE, MOTHER MOBILE pattern detection as specified in requirements
   */
  static parseClassStudentExcel(file: File): Promise<{ slno: string; usn: string; name: string; email?: string; student_mobile?: string; father_mobile?: string; mother_mobile?: string }[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];

          // Get all data including headers
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          console.log('Raw Excel data:', jsonData);

          // Find the header row with SLNO, USN, NAME, EMAIL, STUDENT MOBILE, FATHER MOBILE, MOTHER MOBILE pattern
          let headerRowIndex = -1;
          let slnoCol = -1, usnCol = -1, nameCol = -1, emailCol = -1, studentMobileCol = -1, fatherMobileCol = -1, motherMobileCol = -1;

          for (let i = 0; i < jsonData.length; i++) {
            const row = jsonData[i] as any[];
            if (!row || row.length === 0) continue;

            // Look for required columns (case insensitive)
            for (let j = 0; j < row.length; j++) {
              const cellValue = String(row[j] || '').toLowerCase().trim();

              if (cellValue.includes('sl') && (cellValue.includes('no') || cellValue.includes('num'))) {
                slnoCol = j;
              } else if (cellValue.includes('usn') || cellValue.includes('roll')) {
                usnCol = j;
              } else if (cellValue.includes('name') && !cellValue.includes('father') && !cellValue.includes('mother') && !cellValue.includes('parent')) {
                nameCol = j;
              } else if (cellValue.includes('email') && !cellValue.includes('father') && !cellValue.includes('mother') && !cellValue.includes('parent')) {
                emailCol = j;
              } else if (cellValue.includes('student') && cellValue.includes('mobile')) {
                studentMobileCol = j;
              } else if (cellValue.includes('father') && cellValue.includes('mobile')) {
                fatherMobileCol = j;
              } else if (cellValue.includes('mother') && cellValue.includes('mobile')) {
                motherMobileCol = j;
              }
            }

            // If we found the required columns (SLNO, USN, NAME are mandatory), this is our header row
            if (slnoCol !== -1 && usnCol !== -1 && nameCol !== -1) {
              headerRowIndex = i;
              break;
            }

            // Reset for next row
            slnoCol = -1; usnCol = -1; nameCol = -1; emailCol = -1; studentMobileCol = -1; fatherMobileCol = -1; motherMobileCol = -1;
          }

          if (headerRowIndex === -1) {
            throw new Error('Could not find required SLNO, USN, NAME columns in the Excel file');
          }

          console.log(`Found header at row ${headerRowIndex + 1}, columns: SLNO=${slnoCol}, USN=${usnCol}, NAME=${nameCol}, EMAIL=${emailCol}, STUDENT_MOBILE=${studentMobileCol}, FATHER_MOBILE=${fatherMobileCol}, MOTHER_MOBILE=${motherMobileCol}`);

          // Parse data rows starting after header
          const students: { slno: string; usn: string; name: string; email?: string; student_mobile?: string; father_mobile?: string; mother_mobile?: string }[] = [];

          for (let i = headerRowIndex + 1; i < jsonData.length; i++) {
            const row = jsonData[i] as any[];
            if (!row || row.length === 0) continue;

            const slno = String(row[slnoCol] || '').trim();
            const usn = String(row[usnCol] || '').trim();
            const name = String(row[nameCol] || '').trim();
            const email = emailCol !== -1 ? String(row[emailCol] || '').trim() : undefined;
            const student_mobile = studentMobileCol !== -1 ? String(row[studentMobileCol] || '').trim() : undefined;
            const father_mobile = fatherMobileCol !== -1 ? String(row[fatherMobileCol] || '').trim() : undefined;
            const mother_mobile = motherMobileCol !== -1 ? String(row[motherMobileCol] || '').trim() : undefined;

            // Skip empty rows
            if (!slno && !usn && !name) continue;

            // Validate USN format (basic validation)
            if (usn && name) {
              const studentData: any = { slno, usn, name };

              // Only add optional fields if they have values
              if (email) studentData.email = email;
              if (student_mobile) studentData.student_mobile = student_mobile;
              if (father_mobile) studentData.father_mobile = father_mobile;
              if (mother_mobile) studentData.mother_mobile = mother_mobile;

              students.push(studentData);
            }
          }

          console.log(`Parsed ${students.length} students from Excel`);
          resolve(students);

        } catch (error) {
          console.error('Error parsing Excel file:', error);
          reject(new Error('Failed to parse Excel file: ' + (error instanceof Error ? error.message : 'Unknown error')));
        }
      };

      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * Convert department full name to short code
   */
  private static getDepartmentCode(department: string): string {
    const departmentMap: { [key: string]: string } = {
      'Computer Science and Engineering': 'cse',
      'Electronics and Communication Engineering': 'ece',
      'Electrical and Electronics Engineering': 'eee',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Information Science and Engineering': 'ise',
      'Biotechnology': 'biotech',
      'Chemical Engineering': 'chemical'
    };

    return departmentMap[department] || department.toLowerCase();
  }

  /**
   * Get lab batches for a department/semester/section
   * Dynamically detects actual batch names from the database
   */
  static async getLabBatches(department: string, semester: string, section: string): Promise<string[]> {
    try {
      console.log('🔍 Fetching lab batches for:', { department, semester, section });

      // Convert department to short code for database queries
      const deptCode = this.getDepartmentCode(department);
      console.log('🔍 Converted department:', department, '→', deptCode);

      // Known batch configurations for specific classes
      const knownBatches: { [key: string]: string[] } = {
        'cse-4-A': ['A1', 'A2'],
        'cse-4-B': ['B1', 'B2'],
        'cse-4-C': ['C1', 'C2'],
        'ece-4-A': ['A1', 'A2'],
        'ece-4-B': ['B1', 'B2'],
        'mech-4-A': ['A1', 'A2'],
        'eee-4-A': ['A1', 'A2']
      };

      const classKey = `${deptCode}-${semester}-${section}`;
      if (knownBatches[classKey]) {
        console.log('🔍 Returning known batches for', classKey, ':', knownBatches[classKey]);
        return knownBatches[classKey];
      }

      // Try to get from timetable_slots using department code
      const { data: timetableSlots, error: timetableSlotsError } = await supabase
        .from('timetable_slots')
        .select('batch_name')
        .eq('department', deptCode)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['lab', 'laboratory'])
        .not('batch_name', 'is', null);

      console.log('🔍 Timetable slots query result:', { data: timetableSlots, error: timetableSlotsError });

      if (!timetableSlotsError && timetableSlots && timetableSlots.length > 0) {
        const uniqueBatches = [...new Set(timetableSlots.map(slot => slot.batch_name).filter(Boolean))];
        console.log('🔍 Found batches from timetable_slots:', uniqueBatches);
        if (uniqueBatches.length > 0) {
          return uniqueBatches.sort();
        }
      }

      // Try lab_time_slots approach using department code
      console.log('🔍 Trying lab_time_slots approach...');

      const { data: labSubjects, error: labSubjectsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('id, subject_code, subject_name')
        .eq('department', deptCode)
        .eq('semester', semester)
        .eq('section', section)
        .in('subject_type', ['lab', 'laboratory'])
        .not('subject_code', 'like', 'DELETED_%');

      console.log('🔍 Lab subjects query result:', { data: labSubjects, error: labSubjectsError });

      if (!labSubjectsError && labSubjects && labSubjects.length > 0) {
        const subjectIds = labSubjects.map(s => s.id);

        const { data: labTimeSlots, error: labTimeSlotsError } = await supabase
          .from('lab_time_slots')
          .select('batch_name')
          .in('simplified_mapping_id', subjectIds);

        console.log('🔍 Lab time slots query result:', { data: labTimeSlots, error: labTimeSlotsError });

        if (!labTimeSlotsError && labTimeSlots && labTimeSlots.length > 0) {
          const uniqueBatches = [...new Set(labTimeSlots.map(slot => slot.batch_name).filter(Boolean))];
          console.log('🔍 Found batches from lab_time_slots:', uniqueBatches);
          if (uniqueBatches.length > 0) {
            return uniqueBatches.sort();
          }
        }

        // If we found lab subjects but no batches, return default
        console.log('🔍 Found lab subjects but no batch configuration, returning defaults');
        return ['Batch A', 'Batch B', 'Batch C'];
      }

      console.log('🔍 No lab subjects found for this semester-section');
      return [];

    } catch (error) {
      console.error('🔍 Error in getLabBatches:', error);
      return [];
    }
  }

  /**
   * Get elective subjects for a department/semester/section
   */
  static async getElectiveSubjects(department: string, semester: string, section: string): Promise<{ id: string; code: string; name: string }[]> {
    try {
      const { data: electives, error } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('subject_id, subject_code, subject_name')
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .eq('subject_type', 'elective')
        .not('subject_code', 'like', 'DELETED_%');

      if (error) throw error;

      return (electives || []).map(e => ({
        id: e.subject_id,
        code: e.subject_code,
        name: e.subject_name
      }));
    } catch (error) {
      console.error('Error fetching elective subjects:', error);
      throw error;
    }
  }

  /**
   * Upload class students for a class teacher
   */
  static async uploadClassStudents(
    students: { slno: string; usn: string; name: string; email?: string; student_mobile?: string; father_mobile?: string; mother_mobile?: string }[],
    classTeacherAssignment: { department: string; semester: string; section: string; academic_year: string }
  ): Promise<{ success: number; errors: { row: number; error: string }[] }> {
    try {
      console.log('🔄 Uploading class students to database...', {
        count: students.length,
        assignment: classTeacherAssignment
      });

      const errors: { row: number; error: string }[] = [];
      let successCount = 0;

      // Get current user ID for uploaded_by field
      const { data: { user } } = await supabase.auth.getUser();
      const uploadedBy = user?.id;

      // Use department name as-is from class teacher assignment (no conversion needed)
      // The class_teachers table stores full department names, so we need to match exactly
      const department = classTeacherAssignment.department;

      // Verify the user is a class teacher for this class and use their faculty ID
      const { data: classTeacher, error: classTeacherError } = await supabase
        .from('class_teachers')
        .select('faculty_id')
        .eq('faculty_id', uploadedBy)
        .eq('department', department)
        .eq('semester', classTeacherAssignment.semester)
        .eq('section', classTeacherAssignment.section)
        .eq('academic_year', classTeacherAssignment.academic_year)
        .single();

      if (classTeacherError) {
        console.error('Error verifying class teacher assignment:', classTeacherError);
        throw new Error('You are not authorized to upload students for this class');
      }

      // Use the faculty ID as the class teacher ID (references employee_details table)
      const classTeacherId = classTeacher?.faculty_id;

      console.log('🔍 Class teacher details:', {
        department: department,
        classTeacherId: classTeacherId,
        uploadedBy: uploadedBy
      });

      // First, delete existing class students for this class to avoid duplicates
      const { error: deleteError } = await supabase
        .from('class_students')
        .delete()
        .eq('department', department)
        .eq('semester', classTeacherAssignment.semester)
        .eq('section', classTeacherAssignment.section)
        .eq('academic_year', classTeacherAssignment.academic_year);

      if (deleteError) {
        console.error('Error deleting existing class students:', deleteError);
      }

      // Prepare students for insertion
      const studentsToInsert = students.map((student) => ({
        usn: student.usn.trim(),
        student_name: student.name.trim(),
        email: student.email?.trim() || null,
        student_mobile: student.student_mobile?.trim() || null,
        father_mobile: student.father_mobile?.trim() || null,
        mother_mobile: student.mother_mobile?.trim() || null,
        department: department,
        semester: classTeacherAssignment.semester,
        section: classTeacherAssignment.section,
        academic_year: classTeacherAssignment.academic_year,
        class_teacher_id: classTeacherId,
        uploaded_by: uploadedBy
      }));

      console.log('🔍 Sample student data to insert:', {
        sampleStudent: studentsToInsert[0],
        totalCount: studentsToInsert.length
      });

      // Insert students in batches to handle large uploads
      const batchSize = 100;
      for (let i = 0; i < studentsToInsert.length; i += batchSize) {
        const batch = studentsToInsert.slice(i, i + batchSize);

        console.log(`🔄 Inserting batch ${Math.floor(i/batchSize) + 1}:`, {
          batchSize: batch.length,
          sampleData: batch[0]
        });

        const { data, error } = await supabase
          .from('class_students')
          .insert(batch)
          .select('id');

        console.log(`📊 Batch ${Math.floor(i/batchSize) + 1} result:`, {
          data: data,
          error: error,
          insertedCount: data?.length || 0
        });

        if (error) {
          console.error('❌ Error inserting batch:', error);
          // Add errors for this batch
          for (let j = 0; j < batch.length; j++) {
            errors.push({
              row: i + j + 1,
              error: error.message
            });
          }
        } else {
          successCount += batch.length;
          console.log(`✅ Inserted batch ${Math.floor(i/batchSize) + 1}: ${batch.length} students`);
        }
      }

      console.log('🎉 Class students upload completed:', {
        success: successCount,
        errors: errors.length
      });

      return { success: successCount, errors };
    } catch (error) {
      console.error('Error uploading class students:', error);
      throw error;
    }
  }

  /**
   * Upload lab batch students - Direct Excel upload to batch_students table
   * Similar to class_students upload pattern
   */
  static async uploadLabBatchStudents(
    students: { slno: string; usn: string; name: string; email?: string; student_mobile?: string; father_mobile?: string; mother_mobile?: string }[],
    batchName: string,
    classTeacherAssignment: { department: string; semester: string; section: string; academic_year: string }
  ): Promise<{ success: number; errors: { row: number; error: string }[] }> {
    try {
      console.log('🔄 Uploading lab batch students to batch_students table...', {
        batchName,
        count: students.length,
        assignment: classTeacherAssignment
      });

      const errors: { row: number; error: string }[] = [];
      let successCount = 0;

      // Get current user ID for uploaded_by field
      const { data: { user } } = await supabase.auth.getUser();
      const uploadedBy = user?.id;

      // Use department name as-is from class teacher assignment for storage
      const department = classTeacherAssignment.department;

      // Verify the user is a class teacher for this class and get their faculty ID
      const { data: classTeacher, error: classTeacherError } = await supabase
        .from('class_teachers')
        .select('faculty_id')
        .eq('faculty_id', uploadedBy)
        .eq('department', department)
        .eq('semester', classTeacherAssignment.semester)
        .eq('section', classTeacherAssignment.section)
        .eq('academic_year', classTeacherAssignment.academic_year)
        .single();

      if (classTeacherError) {
        console.error('Error verifying class teacher assignment:', classTeacherError);
        throw new Error('You are not authorized to upload lab batch students for this class');
      }

      // Use the faculty ID as the class teacher ID (references employee_details table)
      const classTeacherId = classTeacher?.faculty_id;

      console.log('🔍 Lab batch upload - Class teacher verification:', {
        department: department,
        classTeacherId: classTeacherId,
        uploadedBy: uploadedBy
      });

      // Step 1: Remove existing batch students for this batch to avoid duplicates
      const { error: deleteError } = await supabase
        .from('batch_students')
        .delete()
        .eq('batch_name', batchName)
        .eq('department', department)
        .eq('semester', classTeacherAssignment.semester)
        .eq('section', classTeacherAssignment.section)
        .eq('academic_year', classTeacherAssignment.academic_year);

      if (deleteError) {
        console.error('Error deleting existing batch students:', deleteError);
      }

      // Step 2: Process each student and insert directly into batch_students table
      for (let i = 0; i < students.length; i++) {
        const student = students[i];

        try {
          // Insert student directly into batch_students table (similar to class_students)
          const { error: insertError } = await supabase
            .from('batch_students')
            .insert({
              usn: student.usn.trim(),
              student_name: student.name.trim(),
              batch_name: batchName,
              academic_year: classTeacherAssignment.academic_year,
              department: department,
              semester: classTeacherAssignment.semester,
              section: classTeacherAssignment.section,
              class_teacher_id: classTeacherId,
              uploaded_by: uploadedBy
            });

          if (insertError) {
            errors.push({
              row: i + 1,
              error: `Failed to upload student ${student.usn}: ${insertError.message}`
            });
          } else {
            successCount++;
            console.log(`✅ Uploaded batch student: ${student.usn} to batch ${batchName}`);
          }

        } catch (studentError) {
          errors.push({
            row: i + 1,
            error: `Error processing student ${student.usn}: ${studentError instanceof Error ? studentError.message : 'Unknown error'}`
          });
        }
      }

      console.log('🎉 Lab batch students upload completed:', {
        batchName,
        success: successCount,
        errors: errors.length
      });

      return { success: successCount, errors };
    } catch (error) {
      console.error('Error uploading lab batch students:', error);
      throw error;
    }
  }

  /**
   * Upload elective students
   */
  static async uploadElectiveStudents(
    students: { slno: string; usn: string; name: string; email?: string; student_mobile?: string; father_mobile?: string; mother_mobile?: string }[],
    electiveSubjectId: string,
    classTeacherAssignment: { department: string; semester: string; section: string; academic_year: string }
  ): Promise<{ success: number; errors: { row: number; error: string }[] }> {
    try {
      console.log('🔄 Uploading elective students to database...', {
        electiveSubjectId,
        count: students.length,
        assignment: classTeacherAssignment
      });

      const errors: { row: number; error: string }[] = [];
      let successCount = 0;

      // Use department name as-is from class teacher assignment for storage
      const department = classTeacherAssignment.department;

      // Convert to department code for querying tables that use codes
      const deptCodeForQuery = this.getDepartmentCode(department);

      // Step 1: Get elective subject details
      const { data: electiveSubject, error: subjectError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('subject_code, subject_name')
        .eq('id', electiveSubjectId)
        .single();

      if (subjectError || !electiveSubject) {
        throw new Error(`Failed to fetch elective subject: ${subjectError?.message || 'Subject not found'}`);
      }

      console.log('📚 Elective subject:', electiveSubject);

      // Step 2: Remove existing elective selections for this subject to avoid duplicates
      const { error: deleteError } = await supabase
        .from('elective_selections')
        .delete()
        .eq('subject_code', electiveSubject.subject_code)
        .eq('department', department)
        .eq('semester', classTeacherAssignment.semester)
        .eq('academic_year', classTeacherAssignment.academic_year);

      if (deleteError) {
        console.error('Error deleting existing elective selections:', deleteError);
      }

      // Step 3: Process each student
      for (let i = 0; i < students.length; i++) {
        const student = students[i];

        try {
          // Check if student already exists in students table (use department code)
          let studentId: string;
          const { data: existingStudent } = await supabase
            .from('students')
            .select('id')
            .eq('roll_number', student.usn)
            .eq('department', deptCodeForQuery)
            .eq('semester', classTeacherAssignment.semester)
            .eq('section', classTeacherAssignment.section)
            .single();

          if (existingStudent) {
            studentId = existingStudent.id;
            console.log(`👤 Using existing student: ${student.usn}`);
          } else {
            // Create new student record (use department code for students table)
            const { data: newStudent, error: studentError } = await supabase
              .from('students')
              .insert({
                roll_number: student.usn.trim(),
                name: student.name.trim(),
                department: deptCodeForQuery,
                semester: classTeacherAssignment.semester,
                section: classTeacherAssignment.section,
                enrollment_year: classTeacherAssignment.academic_year.split('-')[0]
              })
              .select('id')
              .single();

            if (studentError) {
              errors.push({
                row: i + 1,
                error: `Failed to create student ${student.usn}: ${studentError.message}`
              });
              continue;
            }

            studentId = newStudent.id;
            console.log(`✅ Created student: ${student.usn} (${studentId})`);
          }

          // Step 4: Create elective selection
          const { error: selectionError } = await supabase
            .from('elective_selections')
            .insert({
              student_id: studentId,
              subject_code: electiveSubject.subject_code,
              subject_name: electiveSubject.subject_name,
              department: department,
              semester: classTeacherAssignment.semester,
              academic_year: classTeacherAssignment.academic_year
            });

          if (selectionError) {
            errors.push({
              row: i + 1,
              error: `Failed to create elective selection for ${student.usn}: ${selectionError.message}`
            });
          } else {
            successCount++;
            console.log(`✅ Created elective selection for: ${student.usn}`);
          }

        } catch (studentError) {
          errors.push({
            row: i + 1,
            error: `Error processing student ${student.usn}: ${studentError instanceof Error ? studentError.message : 'Unknown error'}`
          });
        }
      }

      console.log('🎉 Elective students upload completed:', {
        electiveSubject: electiveSubject.subject_code,
        success: successCount,
        errors: errors.length
      });

      return { success: successCount, errors };
    } catch (error) {
      console.error('Error uploading elective students:', error);
      throw error;
    }
  }

  /**
   * Create a lab batch
   */
  static async createLabBatch(
    batchData: {
      batch_name: string;
      department: string;
      semester: string;
      section: string;
      subject_code: string;
      academic_year: string;
    },
    createdBy: string
  ): Promise<LabBatch> {
    try {
      const { data, error } = await supabase
        .from('lab_batches')
        .insert({
          ...batchData,
          created_by: createdBy
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating lab batch:', error);
      throw error;
    }
  }

  /**
   * Assign students to a lab batch
   */
  static async assignStudentsToLabBatch(
    labBatchId: string,
    studentIds: string[],
    classTeacherId?: string
  ): Promise<void> {
    try {
      // Get current user ID for uploaded_by field
      const { data: { user } } = await supabase.auth.getUser();
      const uploadedBy = user?.id;

      // First, remove existing assignments for this batch
      await supabase
        .from('lab_batch_students')
        .delete()
        .eq('lab_batch_id', labBatchId);

      // Then, add new assignments with tracking fields
      const assignments = studentIds.map(studentId => ({
        lab_batch_id: labBatchId,
        student_id: studentId,
        class_teacher_id: classTeacherId,
        uploaded_by: uploadedBy,
        uploaded_at: new Date().toISOString()
      }));

      const { error } = await supabase
        .from('lab_batch_students')
        .insert(assignments);

      if (error) throw error;
    } catch (error) {
      console.error('Error assigning students to lab batch:', error);
      throw error;
    }
  }

  /**
   * Get elective selections for a department/semester
   */
  static async getElectiveSelections(
    userDepartment: string,
    semester: string,
    academicYear: string
  ): Promise<ElectiveSelection[]> {
    try {
      const { data, error } = await supabase
        .from('elective_selections')
        .select(`
          *,
          student:students(*)
        `)
        .eq('department', userDepartment)
        .eq('semester', semester)
        .eq('academic_year', academicYear)
        .order('subject_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching elective selections:', error);
      throw error;
    }
  }

  /**
   * Create elective selection for a student
   */
  static async createElectiveSelection(
    selectionData: {
      student_id: string;
      subject_code: string;
      subject_name: string;
      department: string;
      semester: string;
      academic_year: string;
    }
  ): Promise<ElectiveSelection> {
    try {
      const { data, error } = await supabase
        .from('elective_selections')
        .insert(selectionData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating elective selection:', error);
      throw error;
    }
  }

  /**
   * Bulk upload elective selections
   */
  static async bulkUploadElectiveSelections(
    selectionsData: {
      student_id: string;
      subject_code: string;
      subject_name: string;
      department: string;
      semester: string;
      academic_year: string;
    }[]
  ): Promise<{ success: ElectiveSelection[], errors: { row: number, error: string }[] }> {
    const success: ElectiveSelection[] = [];
    const errors: { row: number, error: string }[] = [];

    for (let i = 0; i < selectionsData.length; i++) {
      try {
        const selection = await this.createElectiveSelection(selectionsData[i]);
        success.push(selection);
      } catch (error) {
        errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { success, errors };
  }

  /**
   * Get uploaded class students from class_students table
   */
  static async getUploadedClassStudents(
    department: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<{
    classInfo: {
      department: string;
      semester: string;
      section: string;
      academicYear: string;
    };
    students: {
      id: string;
      usn: string;
      studentName: string;
      email: string | null;
      studentMobile: string | null;
      fatherMobile: string | null;
      motherMobile: string | null;
      classTeacherId: string | null;
      uploadedBy: string | null;
      uploadedAt: string | null;
    }[];
    totalStudents: number;
  }> {
    try {
      console.log('🔍 Retrieving uploaded class students from class_students table:', {
        department,
        semester,
        section,
        academicYear
      });

      // Get possible department name variations
      const departmentVariations = this.mapDepartmentName(department);
      console.log('🔍 StudentService: Department variations to try:', departmentVariations);

      let classStudents: any[] = [];
      let studentsError = null;

      // Try each department variation until we find students
      for (const deptVariation of departmentVariations) {
        console.log(`🔍 StudentService: Trying department: "${deptVariation}"`);

        const { data, error } = await supabase
          .from('class_students')
          .select(`
            id,
            usn,
            student_name,
            email,
            student_mobile,
            father_mobile,
            mother_mobile,
            department,
            semester,
            section,
            academic_year,
            class_teacher_id,
            uploaded_by,
            created_at,
            updated_at
          `)
          .eq('department', deptVariation)
          .eq('semester', semester)
          .eq('section', section)
          .eq('academic_year', academicYear)
          .order('usn');

        if (error) {
          console.warn(`⚠️ StudentService: Error with department "${deptVariation}":`, error);
          studentsError = error;
          continue;
        }

        if (data && data.length > 0) {
          console.log(`✅ StudentService: Found ${data.length} students with department: "${deptVariation}"`);
          classStudents = data;
          break;
        } else {
          console.log(`❌ StudentService: No students found with department: "${deptVariation}"`);
        }
      }

      if (classStudents.length === 0 && studentsError) {
        console.error('❌ StudentService: No students found with any department variation. Last error:', studentsError);
        throw new Error(`Failed to fetch class students: ${studentsError.message}`);
      }

      const students = (classStudents || []).map(student => ({
        id: student.id,
        usn: student.usn,
        studentName: student.student_name,
        email: student.email,
        studentMobile: student.student_mobile,
        fatherMobile: student.father_mobile,
        motherMobile: student.mother_mobile,
        classTeacherId: student.class_teacher_id,
        uploadedBy: student.uploaded_by,
        uploadedAt: student.created_at
      }));

      console.log('✅ Retrieved uploaded class students:', {
        totalStudents: students.length
      });

      return {
        classInfo: { department, semester, section, academicYear },
        students,
        totalStudents: students.length
      };

    } catch (error) {
      console.error('Error retrieving uploaded class students:', error);
      throw error;
    }
  }

  /**
   * Delete uploaded class students for a specific class
   */
  static async deleteUploadedClassStudents(
    department: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<void> {
    try {
      console.log('🗑️ Deleting uploaded class students:', {
        department,
        semester,
        section,
        academicYear
      });

      // Get possible department name variations
      const departmentVariations = this.mapDepartmentName(department);
      let deleteError = null;

      // Try each department variation
      for (const deptVariation of departmentVariations) {
        const { error } = await supabase
          .from('class_students')
          .delete()
          .eq('department', deptVariation)
          .eq('semester', semester)
          .eq('section', section)
          .eq('academic_year', academicYear);

        if (error) {
          console.warn(`⚠️ Delete error for department "${deptVariation}":`, error);
          deleteError = error;
          continue;
        } else {
          // Successfully deleted, exit loop
          deleteError = null;
          break;
        }
      }

      if (deleteError) {
        throw new Error(`Failed to delete class students: ${deleteError.message}`);
      }

      console.log('✅ Successfully deleted uploaded class students');
    } catch (error) {
      console.error('Error deleting uploaded class students:', error);
      throw error;
    }
  }

  /**
   * Get lab batch students by batch name, semester, and section
   * Retrieves from batch_students table (similar to class_students)
   */
  static async getLabBatchStudents(
    batchName: string,
    department: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<{
    batchInfo: {
      batchName: string;
      department: string;
      semester: string;
      section: string;
      academicYear: string;
    };
    students: {
      usn: string;
      studentName: string;
      uploadedBy: string | null;
      uploadedAt: string | null;
    }[];
    totalStudents: number;
  }> {
    try {
      console.log('🔍 Retrieving batch students from batch_students table:', {
        batchName,
        department,
        semester,
        section,
        academicYear
      });

      // Get possible department name variations
      const departmentVariations = this.mapDepartmentName(department);
      console.log('🔍 StudentService Batch: Department variations to try:', departmentVariations);

      let batchStudents: any[] = [];
      let studentsError = null;

      // Try each department variation until we find students
      for (const deptVariation of departmentVariations) {
        console.log(`🔍 StudentService Batch: Trying department: "${deptVariation}"`);

        const { data, error } = await supabase
          .from('batch_students')
          .select(`
            id,
            usn,
            student_name,
            batch_name,
            department,
            semester,
            section,
            academic_year,
            class_teacher_id,
            uploaded_by,
            created_at,
            updated_at
          `)
          .eq('batch_name', batchName)
          .eq('department', deptVariation)
          .eq('semester', semester)
          .eq('section', section)
          .eq('academic_year', academicYear)
          .order('usn');

        if (error) {
          console.warn(`⚠️ StudentService Batch: Error with department "${deptVariation}":`, error);
          studentsError = error;
          continue;
        }

        if (data && data.length > 0) {
          console.log(`✅ StudentService Batch: Found ${data.length} students with department: "${deptVariation}"`);
          batchStudents = data;
          break;
        } else {
          console.log(`❌ StudentService Batch: No students found with department: "${deptVariation}"`);
        }
      }

      if (batchStudents.length === 0 && studentsError) {
        console.error('❌ StudentService Batch: No students found with any department variation. Last error:', studentsError);
        throw new Error(`Failed to fetch batch students: ${studentsError.message}`);
      }

      const students = (batchStudents || []).map(student => ({
        usn: student.usn,
        studentName: student.student_name,
        uploadedBy: student.uploaded_by,
        uploadedAt: student.created_at
      }));

      console.log('✅ Retrieved batch students:', {
        batchName,
        totalStudents: students.length
      });

      return {
        batchInfo: { batchName, department, semester, section, academicYear },
        students,
        totalStudents: students.length
      };

    } catch (error) {
      console.error('Error retrieving batch students:', error);
      throw error;
    }
  }

  /**
   * Delete uploaded batch students for a specific batch
   */
  static async deleteUploadedBatchStudents(
    batchName: string,
    department: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<void> {
    try {
      console.log('🗑️ Deleting uploaded batch students:', {
        batchName,
        department,
        semester,
        section,
        academicYear
      });

      const { error } = await supabase
        .from('batch_students')
        .delete()
        .eq('batch_name', batchName)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .eq('academic_year', academicYear);

      if (error) {
        throw new Error(`Failed to delete batch students: ${error.message}`);
      }

      console.log('✅ Successfully deleted uploaded batch students for batch:', batchName);
    } catch (error) {
      console.error('Error deleting uploaded batch students:', error);
      throw error;
    }
  }

  /**
   * Get all lab batches for a department/semester/section
   * Uses batch_students table for summary
   */
  static async getLabBatchesByClass(
    department: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<{
    batchName: string;
    studentCount: number;
  }[]> {
    try {
      console.log('🔍 Fetching lab batches by class:', {
        department,
        semester,
        section,
        academicYear
      });

      // Get possible department name variations
      const departmentVariations = this.mapDepartmentName(department);
      console.log('🔍 StudentService LabBatches: Department variations to try:', departmentVariations);

      let allBatchSummary: any[] = [];
      let lastError = null;

      // Try each department variation until we find data
      for (const deptVariation of departmentVariations) {
        console.log(`🔍 StudentService LabBatches: Trying department: "${deptVariation}"`);

        const { data: batchSummary, error } = await supabase
          .from('batch_students')
          .select('batch_name')
          .eq('department', deptVariation)
          .eq('semester', semester)
          .eq('section', section)
          .eq('academic_year', academicYear);

        if (error) {
          console.warn(`⚠️ StudentService LabBatches: Error with department "${deptVariation}":`, error);
          lastError = error;
          continue;
        }

        if (batchSummary && batchSummary.length > 0) {
          console.log(`✅ StudentService LabBatches: Found ${batchSummary.length} batch records with department "${deptVariation}"`);
          allBatchSummary = batchSummary;
          break;
        }
      }

      if (allBatchSummary.length === 0) {
        console.log('🔍 StudentService LabBatches: No batch data found for any department variation');
        return [];
      }

      // Group by batch name and count students
      const batchMap = new Map();

      allBatchSummary.forEach(record => {
        const batchName = record.batch_name;

        if (!batchMap.has(batchName)) {
          batchMap.set(batchName, {
            batchName,
            studentCount: 0
          });
        }

        batchMap.get(batchName).studentCount++;
      });

      const result = Array.from(batchMap.values()).sort((a, b) => a.batchName.localeCompare(b.batchName));
      console.log('✅ StudentService LabBatches: Returning batch summary:', result);
      return result;
    } catch (error) {
      console.error('Error fetching lab batches by class:', error);
      throw error;
    }
  }

  /**
   * Check if roll number is unique
   */
  static async isRollNumberUnique(rollNumber: string, excludeId?: string): Promise<boolean> {
    try {
      let query = supabase
        .from('students')
        .select('id')
        .eq('roll_number', rollNumber)
        .eq('is_active', true);

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;

      if (error) throw error;
      return !data || data.length === 0;
    } catch (error) {
      console.error('Error checking roll number uniqueness:', error);
      throw error;
    }
  }
}
