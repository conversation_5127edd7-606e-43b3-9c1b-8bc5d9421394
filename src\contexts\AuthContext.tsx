
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { User, Session } from '@supabase/supabase-js';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate } from "react-router-dom";

type AuthContextType = {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, userData: { full_name?: string }) => Promise<void>;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // Check if logout was just performed
    const logoutPerformed = localStorage.getItem('logout_performed');
    if (logoutPerformed) {
      // Clear the logout flag and ensure clean state
      localStorage.removeItem('logout_performed');
      setSession(null);
      setUser(null);
      setLoading(false);
      return;
    }

    // Set up auth state listener first
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state change:', event);

        // If this is a sign out event, ensure clean state
        if (event === 'SIGNED_OUT') {
          setSession(null);
          setUser(null);
          setLoading(false);
          return;
        }

        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    // Then check for existing session only if no logout was performed
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithPassword({ 
        email, 
        password 
      });
      
      if (error) {
        toast({
          title: "Sign in failed",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      
      toast({
        title: "Signed in successfully",
        description: "Welcome back!",
      });
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, userData: { full_name?: string }) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      });

      if (error) {
        toast({
          title: "Sign up failed",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }

      // If user was created successfully, ensure employee_details record exists
      if (data.user) {
        try {
          // Check if employee_details already exists
          const { data: existingEmployee } = await supabase
            .from('employee_details')
            .select('id')
            .eq('id', data.user.id)
            .single();

          if (!existingEmployee) {
            // Create employee_details record
            const { error: employeeError } = await supabase
              .from('employee_details')
              .insert({
                id: data.user.id,
                full_name: userData.full_name || 'New User',
                email: email,
                department: 'General',
                designation: 'Faculty',
                roles: ['faculty'],
                vacant_by_day: {},
                vacant_count_by_day: {}
              });

            if (employeeError) {
              console.error('Error creating employee_details:', employeeError);
              // Don't throw error here, just log it
            }
          }
        } catch (employeeCreationError) {
          console.error('Error in employee_details creation:', employeeCreationError);
          // Don't throw error here, just log it
        }
      }

      toast({
        title: "Account created successfully",
        description: "Please check your email for verification.",
      });
    } catch (error) {
      console.error("Error signing up:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);

      // COMPREHENSIVE LOGOUT: Clear local state immediately
      setUser(null);
      setSession(null);

      // Clear all possible auth storage
      localStorage.removeItem('student_session');
      localStorage.removeItem('auth_session');
      localStorage.removeItem('user_session');
      localStorage.removeItem('supabase.auth.token');
      localStorage.removeItem('sb-milmyotuougemocvieof-auth-token');
      localStorage.removeItem('sb-xjltzryjhdcvzbglcqvo-auth-token');
      sessionStorage.clear();

      // Set logout flag to prevent auto-login
      localStorage.setItem('logout_performed', 'true');

      // PERFORMANCE FIX: Show success toast immediately
      toast({
        title: "Signed out successfully",
        description: "You have been logged out successfully.",
      });

      // PERFORMANCE FIX: Start Supabase signOut in background (non-blocking)
      supabase.auth.signOut().catch((error) => {
        console.error("Background Supabase signOut error:", error);
        // Error is logged but doesn't block the logout process
      });

      // PERFORMANCE FIX: Immediate redirect for better user experience
      window.location.href = '/';
    } catch (error) {
      console.error("Error signing out:", error);

      // Even if there's an error, still redirect for security
      window.location.href = '/';
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
