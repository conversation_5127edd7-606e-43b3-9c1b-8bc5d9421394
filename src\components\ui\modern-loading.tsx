import React from 'react';
import { cn } from '@/lib/utils';

interface ModernLoadingProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton';
  className?: string;
  text?: string;
  fullScreen?: boolean;
}

/**
 * Modern Loading Component
 * Professional loading indicators with multiple variants
 */
export const ModernLoading: React.FC<ModernLoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  className,
  text,
  fullScreen = false
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'md':
        return 'h-8 w-8';
      case 'lg':
        return 'h-12 w-12';
      case 'xl':
        return 'h-16 w-16';
      default:
        return 'h-8 w-8';
    }
  };

  const renderSpinner = () => (
    <div className={cn(
      'loading-spinner',
      getSizeClasses(),
      className
    )} />
  );

  const renderDots = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'rounded-full bg-primary animate-bounce',
            size === 'sm' ? 'h-2 w-2' : size === 'lg' ? 'h-4 w-4' : 'h-3 w-3',
            className
          )}
          style={{
            animationDelay: `${i * 0.1}s`,
            animationDuration: '0.6s'
          }}
        />
      ))}
    </div>
  );

  const renderPulse = () => (
    <div className={cn(
      'rounded-full bg-primary animate-pulse',
      getSizeClasses(),
      className
    )} />
  );

  const renderSkeleton = () => (
    <div className={cn(
      'loading-skeleton',
      getSizeClasses(),
      className
    )} />
  );

  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'skeleton':
        return renderSkeleton();
      default:
        return renderSpinner();
    }
  };

  const content = (
    <div className="flex flex-col items-center space-y-4 animate-fade-in">
      {renderLoader()}
      {text && (
        <p className={cn(
          'text-muted-foreground animate-pulse',
          size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'
        )}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        {content}
      </div>
    );
  }

  return content;
};

interface ModernSkeletonProps {
  className?: string;
  lines?: number;
  avatar?: boolean;
  button?: boolean;
}

/**
 * Modern Skeleton Component
 * Professional skeleton loading for content placeholders
 */
export const ModernSkeleton: React.FC<ModernSkeletonProps> = ({
  className,
  lines = 3,
  avatar = false,
  button = false
}) => {
  return (
    <div className={cn('animate-pulse space-y-3', className)}>
      {avatar && (
        <div className="flex items-center space-x-4">
          <div className="rounded-full bg-muted h-12 w-12" />
          <div className="space-y-2 flex-1">
            <div className="h-4 bg-muted rounded w-1/4" />
            <div className="h-3 bg-muted rounded w-1/3" />
          </div>
        </div>
      )}
      
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, i) => (
          <div
            key={i}
            className={cn(
              'h-4 bg-muted rounded',
              i === lines - 1 ? 'w-3/4' : 'w-full'
            )}
          />
        ))}
      </div>

      {button && (
        <div className="flex space-x-2 pt-2">
          <div className="h-9 bg-muted rounded w-20" />
          <div className="h-9 bg-muted rounded w-16" />
        </div>
      )}
    </div>
  );
};

interface ModernProgressProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
  showValue?: boolean;
  className?: string;
}

/**
 * Modern Progress Component
 * Professional progress indicators
 */
export const ModernProgress: React.FC<ModernProgressProps> = ({
  value,
  max = 100,
  size = 'md',
  variant = 'default',
  showValue = false,
  className
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  const getHeightClass = () => {
    switch (size) {
      case 'sm':
        return 'h-1';
      case 'md':
        return 'h-2';
      case 'lg':
        return 'h-3';
      default:
        return 'h-2';
    }
  };

  const getColorClass = () => {
    switch (variant) {
      case 'success':
        return 'bg-success';
      case 'warning':
        return 'bg-warning';
      case 'error':
        return 'bg-destructive';
      default:
        return 'bg-primary';
    }
  };

  return (
    <div className={cn('w-full', className)}>
      <div className={cn(
        'w-full bg-muted rounded-full overflow-hidden',
        getHeightClass()
      )}>
        <div
          className={cn(
            'h-full transition-all duration-500 ease-out rounded-full',
            getColorClass()
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      {showValue && (
        <div className="flex justify-between text-xs text-muted-foreground mt-1">
          <span>{value}</span>
          <span>{max}</span>
        </div>
      )}
    </div>
  );
};

interface ModernSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Simple Modern Spinner Component
 * Lightweight spinner for inline use
 */
export const ModernSpinner: React.FC<ModernSpinnerProps> = ({
  size = 'md',
  className
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'md':
        return 'h-6 w-6';
      case 'lg':
        return 'h-8 w-8';
      default:
        return 'h-6 w-6';
    }
  };

  return (
    <div className={cn(
      'loading-spinner',
      getSizeClass(),
      className
    )} />
  );
};

export default ModernLoading;
