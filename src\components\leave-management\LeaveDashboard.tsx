import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  TrendingUp,
  Users,
  FileText,
  BarChart3
} from 'lucide-react';
import { 
  LeaveManagementService, 
  LeaveBalance, 
  LeaveRequest, 
  LeaveStats,
  LeavePolicy 
} from '@/services/LeaveManagementService';

interface LeaveDashboardProps {
  refreshTrigger?: number;
}

export default function LeaveDashboard({ refreshTrigger }: LeaveDashboardProps) {
  const { user } = useAuth();
  const { department, departmentName } = useUserDepartment();

  // State management
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [recentRequests, setRecentRequests] = useState<LeaveRequest[]>([]);
  const [upcomingLeaves, setUpcomingLeaves] = useState<LeaveRequest[]>([]);
  const [leaveStats, setLeaveStats] = useState<LeaveStats | null>(null);
  const [leavePolicies, setLeavePolicies] = useState<LeavePolicy[]>([]);
  const [loading, setLoading] = useState(true);

  // Load dashboard data
  useEffect(() => {
    if (user?.id && department) {
      loadDashboardData();
    }
  }, [user?.id, department, refreshTrigger]);

  const loadDashboardData = async () => {
    if (!user?.id || !department) return;

    try {
      setLoading(true);

      // Initialize leave balances first
      await LeaveManagementService.initializeLeaveBalances(user.id, department);

      // Load all dashboard data
      const [balances, requests, upcoming, stats, policies] = await Promise.all([
        LeaveManagementService.getLeaveBalances(user.id),
        LeaveManagementService.getLeaveRequests(user.id),
        LeaveManagementService.getUpcomingLeaves(user.id),
        LeaveManagementService.getLeaveStats(user.id),
        LeaveManagementService.getLeavePolicies()
      ]);

      setLeaveBalances(balances);
      setRecentRequests(requests.slice(0, 5)); // Show only recent 5
      setUpcomingLeaves(upcoming);
      setLeaveStats(stats);
      setLeavePolicies(policies);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getLeaveTypeName = (leaveType: string): string => {
    const policy = leavePolicies.find(p => p.leave_type === leaveType);
    return policy?.leave_name || leaveType.replace('_', ' ').toUpperCase();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Leave Statistics Overview */}
      {leaveStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Requests</p>
                  <p className="text-2xl font-bold">{leaveStats.total_requests}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Pending</p>
                  <p className="text-2xl font-bold">{leaveStats.pending_requests}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Approved</p>
                  <p className="text-2xl font-bold">{leaveStats.approved_requests}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Calendar className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Days Taken</p>
                  <p className="text-2xl font-bold">{leaveStats.total_days_taken}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Leave Balance Cards */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Leave Balance Overview
          </CardTitle>
          <CardDescription>
            Your current leave balance for the academic year
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {leaveBalances.map((balance) => (
              <div key={balance.leave_type} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-sm">{getLeaveTypeName(balance.leave_type)}</h4>
                  <Badge variant="outline" className="text-xs">
                    {balance.remaining_days} Available
                  </Badge>
                </div>
                
                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${balance.total_allocated > 0 ? (balance.used_days / balance.total_allocated) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
                
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Used: {balance.used_days}</span>
                  <span>Total: {balance.total_allocated}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Leave Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Recent Leave Requests
          </CardTitle>
          <CardDescription>
            Your latest leave applications and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentRequests.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No leave requests found</p>
              <p className="text-sm">Submit your first leave request to see it here</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(request.status)}
                    <div>
                      <p className="font-medium text-sm">{getLeaveTypeName(request.leave_type)}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(request.start_date)} - {formatDate(request.end_date)} ({request.total_days} days)
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(request.status)}>
                      {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(request.applied_date)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upcoming Approved Leaves */}
      {upcomingLeaves.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Upcoming Approved Leaves
            </CardTitle>
            <CardDescription>
              Your scheduled leaves that have been approved
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingLeaves.map((leave) => (
                <div key={leave.id} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="font-medium text-sm">{getLeaveTypeName(leave.leave_type)}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(leave.start_date)} - {formatDate(leave.end_date)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-green-600">{leave.total_days} days</p>
                    <p className="text-xs text-muted-foreground">Approved</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions Notice */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Leave Policy Reminder:</strong> Please submit leave requests at least 3 days in advance for casual leave 
          and 7 days in advance for planned leave. Emergency leaves can be applied retrospectively with proper documentation.
        </AlertDescription>
      </Alert>
    </div>
  );
}
