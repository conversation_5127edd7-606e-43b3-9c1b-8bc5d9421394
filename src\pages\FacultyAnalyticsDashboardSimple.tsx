import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useAttendanceRealtime } from '@/hooks/useAttendanceRealtime';
import {
  FacultyAnalyticsService,
  type DashboardData
} from '@/services/FacultyAnalyticsService';
import {
  TrendingUp,
  TrendingDown,
  Minus,
  BarChart3,
  <PERSON>,
  Calendar,
  Clock,
  Al<PERSON><PERSON>riangle,
  RefreshCw,
  Filter,
  Target
} from 'lucide-react';

const FacultyAnalyticsDashboardSimple: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedTab, setSelectedTab] = useState('overview');
  const [filters, setFilters] = useState({
    semester: 'all',
    section: 'all',
    subject_code: '',
    date_from: '',
    date_to: '',
    attendance_threshold: 75
  });

  const { user } = useAuth();
  const { department } = useUserDepartment();
  const { toast } = useToast();

  // Real-time updates
  useAttendanceRealtime({
    facultyId: user?.id || '',
    subjectCode: filters.subject_code || '',
    semester: filters.semester || '',
    section: filters.section || '',
    subjectType: '',
    onAttendanceUpdate: () => {
      if (autoRefresh) {
        loadDashboardData();
        toast({
          title: 'Dashboard Updated',
          description: 'Analytics refreshed with latest attendance data.',
        });
      }
    },
    enabled: autoRefresh
  });

  // Load dashboard data on component mount and filter changes
  useEffect(() => {
    if (user?.id && department) {
      loadDashboardData();
    }
  }, [user?.id, department, filters]);

  const loadDashboardData = async () => {
    if (!user?.id || !department) return;

    try {
      setLoading(true);

      // Convert "all" values to empty strings for the service
      const serviceFilters = {
        ...filters,
        semester: filters.semester === 'all' ? '' : filters.semester,
        section: filters.section === 'all' ? '' : filters.section,
      };

      const data = await FacultyAnalyticsService.generateDashboardData(
        user.id,
        department,
        serviceFilters
      );
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load analytics dashboard.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'stable') => {
    switch (direction) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRiskBadgeVariant = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      default:
        return 'default';
    }
  };

  if (!dashboardData) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className={`h-8 w-8 mx-auto mb-4 ${loading ? 'animate-spin' : ''}`} />
            <p className="text-muted-foreground">
              {loading ? 'Loading analytics dashboard...' : 'No data available'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Faculty Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Comprehensive insights into attendance patterns and student performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={loadDashboardData}
            disabled={loading}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Analytics Filters
          </CardTitle>
          <CardDescription>
            Customize your analytics view with filters and date ranges
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div>
              <Label htmlFor="semester">Semester</Label>
              <Select
                value={filters.semester}
                onValueChange={(value) => setFilters(prev => ({ ...prev, semester: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Semesters" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Semesters</SelectItem>
                  {[1, 2, 3, 4, 5, 6, 7, 8].map(sem => (
                    <SelectItem key={sem} value={sem.toString()}>Semester {sem}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="section">Section</Label>
              <Select
                value={filters.section}
                onValueChange={(value) => setFilters(prev => ({ ...prev, section: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Sections" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sections</SelectItem>
                  {['A', 'B', 'C', 'D'].map(sec => (
                    <SelectItem key={sec} value={sec}>Section {sec}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="subject_code">Subject Code</Label>
              <Input
                id="subject_code"
                placeholder="e.g., BCS403"
                value={filters.subject_code}
                onChange={(e) => setFilters(prev => ({ ...prev, subject_code: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="date_from">From Date</Label>
              <Input
                id="date_from"
                type="date"
                value={filters.date_from}
                onChange={(e) => setFilters(prev => ({ ...prev, date_from: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="date_to">To Date</Label>
              <Input
                id="date_to"
                type="date"
                value={filters.date_to}
                onChange={(e) => setFilters(prev => ({ ...prev, date_to: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="threshold">Attendance Threshold (%)</Label>
              <Input
                id="threshold"
                type="number"
                min="0"
                max="100"
                value={filters.attendance_threshold}
                onChange={(e) => setFilters(prev => ({ ...prev, attendance_threshold: parseInt(e.target.value) || 75 }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Subjects</p>
                <p className="text-2xl font-bold">{dashboardData.summary.total_subjects}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Students</p>
                <p className="text-2xl font-bold">{dashboardData.summary.total_students}</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Classes Conducted</p>
                <p className="text-2xl font-bold">{dashboardData.summary.total_classes}</p>
              </div>
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Average Attendance</p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">{dashboardData.summary.average_attendance}%</p>
                  {getTrendIcon(dashboardData.summary.trend_direction)}
                  <span className="text-sm text-muted-foreground">
                    {dashboardData.summary.trend_percentage}%
                  </span>
                </div>
              </div>
              <Target className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* At-Risk Students Alert */}
      {dashboardData.predictive_insights.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              Students Requiring Attention
            </CardTitle>
            <CardDescription className="text-orange-700">
              {dashboardData.predictive_insights.length} students below attendance threshold
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dashboardData.predictive_insights.slice(0, 6).map((insight) => (
                <div key={insight.student_usn} className="p-4 bg-white rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">{insight.student_name}</h4>
                    <Badge variant="destructive">{insight.current_percentage}%</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">USN: {insight.student_usn}</p>
                  <div className="space-y-1">
                    {insight.risk_factors.slice(0, 2).map((factor, index) => (
                      <p key={index} className="text-xs text-orange-700">• {factor}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FacultyAnalyticsDashboardSimple;
