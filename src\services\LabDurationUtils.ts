/**
 * LabDurationUtils - Utility service for handling lab duration detection and time slot parsing
 *
 * This service centralizes the logic for:
 * - Detecting 3-hour morning labs that span across tea breaks
 * - Parsing lab time slots that contain full duration ranges vs individual periods
 * - Extracting start times from lab time slots for period matching
 */

export interface TimeSlot {
  time: string;
  periodNumber: number;
  isPeriod: boolean;
  isBreak?: boolean;
  breakType?: string;
}

export interface LabDurationInfo {
  totalPeriods: number;
  startPeriod: number;
  is3HourMorningLab: boolean;
  spansTeaBreak: boolean;
  visualColSpan: number;
  startTime: string;
  endTime: string;
}

export class LabDurationUtils {
  /**
   * Extract the start time from a lab's time_slot field
   * Handles both full duration ranges (e.g., "08:30-11:30") and individual periods (e.g., "08:30-09:25")
   */
  static extractStartTime(timeSlot: string): string {
    if (!timeSlot || typeof timeSlot !== 'string') {
      return '';
    }

    const parts = timeSlot.split('-');
    return parts[0] || '';
  }

  /**
   * Extract the end time from a lab's time_slot field
   */
  static extractEndTime(timeSlot: string): string {
    if (!timeSlot || typeof timeSlot !== 'string') {
      return '';
    }

    const parts = timeSlot.split('-');
    return parts[1] || '';
  }

  /**
   * Calculate the duration in periods from a time slot range
   * Assumes 55-minute periods with breaks
   */
  static calculatePeriodsFromTimeSlot(timeSlot: string): number {
    if (!timeSlot || typeof timeSlot !== 'string') {
      return 3; // Default fallback
    }

    const parts = timeSlot.split('-');
    if (parts.length !== 2) {
      return 3; // Default fallback
    }

    const startTime = parts[0];
    const endTime = parts[1];

    // Calculate duration in minutes
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    const durationMinutes = endMinutes - startMinutes;

    // Convert to periods (assuming 55-minute periods)
    return Math.ceil(durationMinutes / 55);
  }

  /**
   * Convert time string to minutes since midnight
   */
  static timeToMinutes(time: string): number {
    if (!time || typeof time !== 'string') {
      return 0;
    }

    const [hours, minutes] = time.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) {
      return 0;
    }

    return hours * 60 + minutes;
  }

  /**
   * Check if a lab is a 3-hour morning lab based on duration and start period
   * CRITICAL FIX: Also check for 4-period labs starting at period 1 (these are 3-hour labs with incorrect col_span)
   */
  static is3HourMorningLab(totalPeriods: number, startPeriod: number): boolean {
    // Original logic: 3 periods starting at period 1
    if (totalPeriods === 3 && startPeriod === 1) {
      return true;
    }

    // CRITICAL FIX: Handle incorrectly calculated 4-period morning labs
    // These are actually 3-hour labs but have col_span=4 due to calculation errors
    if (totalPeriods === 4 && startPeriod === 1) {
      console.log(`🔧 FIXING: Detected 4-period morning lab, treating as 3-hour morning lab`);
      return true;
    }

    return false;
  }

  /**
   * Check if a lab spans across the tea break
   */
  static spansTeaBreak(
    startPeriod: number,
    totalPeriods: number,
    teaBreakPeriod: number = 2.5
  ): boolean {
    const endPeriod = startPeriod + totalPeriods - 1;
    return startPeriod <= 2 && endPeriod >= 3;
  }

  /**
   * Find the matching time slot index for a lab's start time
   */
  static findMatchingTimeSlotIndex(
    labTimeSlot: string,
    timeSlots: TimeSlot[]
  ): number {
    const labStartTime = this.extractStartTime(labTimeSlot);

    if (!labStartTime) {
      return -1;
    }

    return timeSlots.findIndex(slot => {
      if (!slot.isPeriod) return false;
      const slotStartTime = this.extractStartTime(slot.time);
      return slotStartTime === labStartTime;
    });
  }

  /**
   * Get comprehensive lab duration information
   */
  static getLabDurationInfo(
    labTimeSlot: string,
    colSpan: number | null | undefined,
    timeSlots: TimeSlot[]
  ): LabDurationInfo {
    const startTime = this.extractStartTime(labTimeSlot);
    const endTime = this.extractEndTime(labTimeSlot);

    // Find the matching time slot index
    const matchingIndex = this.findMatchingTimeSlotIndex(labTimeSlot, timeSlots);
    const startPeriod = matchingIndex >= 0 ? timeSlots[matchingIndex].periodNumber : 1;

    // Calculate total periods
    let totalPeriods = colSpan;
    if (!totalPeriods) {
      totalPeriods = this.calculatePeriodsFromTimeSlot(labTimeSlot);
    }
    if (!totalPeriods) {
      totalPeriods = 3; // Default fallback
    }

    // Check if it's a 3-hour morning lab
    const is3HourMorningLab = this.is3HourMorningLab(totalPeriods, startPeriod);

    // Check if it spans tea break
    const spansTeaBreak = this.spansTeaBreak(startPeriod, totalPeriods);

    // Calculate visual colspan (for 3-hour morning labs, limit to 2 periods visually)
    const visualColSpan = is3HourMorningLab ? 2 : totalPeriods;

    return {
      totalPeriods,
      startPeriod,
      is3HourMorningLab,
      spansTeaBreak,
      visualColSpan,
      startTime,
      endTime
    };
  }

  /**
   * Check if a time slot represents a morning session
   */
  static isMorningTimeSlot(timeSlot: string): boolean {
    const startTime = this.extractStartTime(timeSlot);
    if (!startTime) return false;

    const startMinutes = this.timeToMinutes(startTime);
    // Consider anything before 12:00 PM as morning
    return startMinutes < 12 * 60;
  }

  /**
   * Check if a time slot represents an afternoon session
   */
  static isAfternoonTimeSlot(timeSlot: string): boolean {
    const startTime = this.extractStartTime(timeSlot);
    if (!startTime) return false;

    const startMinutes = this.timeToMinutes(startTime);
    // Consider anything at or after 1:00 PM as afternoon
    return startMinutes >= 13 * 60;
  }

  /**
   * Validate if a lab time slot format is correct
   */
  static isValidTimeSlotFormat(timeSlot: string): boolean {
    if (!timeSlot || typeof timeSlot !== 'string') {
      return false;
    }

    const parts = timeSlot.split('-');
    if (parts.length !== 2) {
      return false;
    }

    const timeRegex = /^([01][0-9]|2[0-3]):([0-5][0-9])$/;
    return timeRegex.test(parts[0]) && timeRegex.test(parts[1]);
  }

  /**
   * Get the period number for a specific time slot
   */
  static getPeriodNumber(timeSlot: string, timeSlots: TimeSlot[]): number {
    const matchingIndex = this.findMatchingTimeSlotIndex(timeSlot, timeSlots);
    return matchingIndex >= 0 ? timeSlots[matchingIndex].periodNumber : 0;
  }
}
