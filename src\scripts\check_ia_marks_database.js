/**
 * <PERSON><PERSON><PERSON> to check what IA marks actually exist in the internal_assessments table
 * This will help us understand if the issue is missing data or integration problems
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://milmyotuougemocvieof.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1MjE5NzQsImV4cCI6MjA1MDA5Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test configuration
const TEST_CONFIG = {
  studentUsn: '1KS23CS001',
  studentId: '4b4b0737-262f-4bc5-bc11-a17b29b4ee54' // From previous logs
};

async function checkIAMarksDatabase() {
  console.log('🔍 Checking IA Marks in Database\n');
  console.log('Test Configuration:', TEST_CONFIG);
  console.log('='.repeat(60));

  try {
    // Step 1: Verify student exists
    console.log('\n📋 Step 1: Verifying Student Exists');
    const { data: studentData, error: studentError } = await supabase
      .from('class_students')
      .select('id, usn, student_name, department, semester, section, academic_year')
      .eq('usn', TEST_CONFIG.studentUsn.toUpperCase())
      .single();

    if (studentError || !studentData) {
      console.log('❌ Student not found:', studentError?.message);
      return;
    }

    console.log('✅ Student verified:', {
      id: studentData.id,
      name: studentData.student_name,
      department: studentData.department,
      semester: studentData.semester,
      section: studentData.section,
      academic_year: studentData.academic_year
    });

    // Step 2: Check ALL IA records for this student
    console.log('\n📊 Step 2: Checking ALL IA Records for Student');
    const { data: allIARecords, error: allIAError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', studentData.id)
      .order('updated_at', { ascending: false });

    if (allIAError) {
      console.log('❌ Error fetching IA records:', allIAError.message);
      return;
    }

    console.log(`📈 Total IA records found: ${allIARecords?.length || 0}`);

    if (!allIARecords || allIARecords.length === 0) {
      console.log('❌ NO IA MARKS FOUND IN DATABASE!');
      console.log('🔧 This explains why marks are not showing in Student Progress Card');
      console.log('💡 Faculty needs to enter IA marks first');
      return;
    }

    // Step 3: Analyze the IA records
    console.log('\n📋 Step 3: Analyzing IA Records');
    console.log('Subject-wise IA marks:');
    console.log('-'.repeat(80));
    console.log('Subject Code | IA1 | IA2 | IA3 | Assignment | Lab | Academic Year | Department');
    console.log('-'.repeat(80));

    allIARecords.forEach(record => {
      const ia1 = record.ia1_marks || '-';
      const ia2 = record.ia2_marks || '-';
      const ia3 = record.ia3_marks || '-';
      const assignment = record.assignment_marks || '-';
      const lab = record.lab_marks || '-';
      
      console.log(`${record.subject_code.padEnd(12)} | ${String(ia1).padEnd(3)} | ${String(ia2).padEnd(3)} | ${String(ia3).padEnd(3)} | ${String(assignment).padEnd(10)} | ${String(lab).padEnd(3)} | ${record.academic_year.padEnd(13)} | ${record.department}`);
    });

    // Step 4: Check specifically for BCS402
    console.log('\n🎯 Step 4: BCS402 (Microcontrollers) Specific Check');
    const bcs402Records = allIARecords.filter(r => r.subject_code === 'BCS402');
    
    if (bcs402Records.length === 0) {
      console.log('❌ NO BCS402 IA MARKS FOUND!');
      console.log('🔧 This confirms why BCS402 marks are not showing in Student Progress Card');
      console.log('💡 Faculty needs to enter BCS402 IA marks');
    } else {
      console.log(`✅ Found ${bcs402Records.length} BCS402 IA record(s):`);
      bcs402Records.forEach((record, index) => {
        console.log(`\n📝 BCS402 Record ${index + 1}:`);
        console.log(`  - IA1: ${record.ia1_marks || 'Not entered'}`);
        console.log(`  - IA2: ${record.ia2_marks || 'Not entered'}`);
        console.log(`  - IA3: ${record.ia3_marks || 'Not entered'}`);
        console.log(`  - Assignment: ${record.assignment_marks || 'Not entered'}`);
        console.log(`  - Lab: ${record.lab_marks || 'Not entered'}`);
        console.log(`  - Academic Year: ${record.academic_year}`);
        console.log(`  - Department: ${record.department}`);
        console.log(`  - Semester: ${record.semester}`);
        console.log(`  - Section: ${record.section}`);
        console.log(`  - Created: ${record.created_at}`);
        console.log(`  - Updated: ${record.updated_at}`);
      });
    }

    // Step 5: Check academic year variations
    console.log('\n📅 Step 5: Academic Year Analysis');
    const academicYears = [...new Set(allIARecords.map(r => r.academic_year))];
    console.log(`Academic years in database: ${academicYears.join(', ')}`);
    console.log(`Student's academic year: ${studentData.academic_year}`);
    
    if (academicYears.length > 1) {
      console.log('⚠️ Multiple academic years found - this might cause filtering issues');
    }

    // Step 6: Check department case sensitivity
    console.log('\n🏢 Step 6: Department Case Analysis');
    const departments = [...new Set(allIARecords.map(r => r.department))];
    console.log(`Departments in IA records: ${departments.join(', ')}`);
    console.log(`Student's department: ${studentData.department}`);
    
    const hasLowercase = departments.some(d => d === studentData.department.toLowerCase());
    const hasUppercase = departments.some(d => d === studentData.department.toUpperCase());
    const hasExact = departments.some(d => d === studentData.department);
    
    console.log(`Department matching: lowercase=${hasLowercase}, uppercase=${hasUppercase}, exact=${hasExact}`);

    // Step 7: Check individual_ia_records table
    console.log('\n📊 Step 7: Checking individual_ia_records Table (Sequential IA)');
    const { data: individualRecords, error: individualError } = await supabase
      .from('individual_ia_records')
      .select('*')
      .eq('student_id', studentData.id);

    if (individualError) {
      console.log('❌ Error checking individual_ia_records:', individualError.message);
    } else {
      console.log(`📈 Individual IA records found: ${individualRecords?.length || 0}`);
      
      if (individualRecords && individualRecords.length > 0) {
        console.log('\n📋 Individual IA Records (by subject and phase):');
        const subjectGroups = {};
        individualRecords.forEach(record => {
          if (!subjectGroups[record.subject_code]) {
            subjectGroups[record.subject_code] = {};
          }
          subjectGroups[record.subject_code][record.ia_phase] = record.marks;
        });

        Object.keys(subjectGroups).forEach(subjectCode => {
          const phases = subjectGroups[subjectCode];
          console.log(`  ${subjectCode}: IA1=${phases.IA1 || '-'}, IA2=${phases.IA2 || '-'}, IA3=${phases.IA3 || '-'}, Assignment=${phases.ASSIGNMENT || '-'}, Lab=${phases.LAB_INTERNAL || '-'}`);
        });

        // Check for BCS402 in individual records
        const bcs402Individual = individualRecords.filter(r => r.subject_code === 'BCS402');
        if (bcs402Individual.length > 0) {
          console.log(`\n✅ Found BCS402 in individual_ia_records: ${bcs402Individual.length} phase records`);
        }
      }
    }

    // Step 8: Summary and recommendations
    console.log('\n📋 Summary and Recommendations');
    console.log('='.repeat(60));
    
    if (allIARecords.length === 0) {
      console.log('❌ ROOT CAUSE: No IA marks found in database');
      console.log('🔧 SOLUTION: Faculty must enter IA marks using:');
      console.log('   - Internal Assessment Management interface, OR');
      console.log('   - Sequential IA Entry system');
    } else if (bcs402Records.length === 0) {
      console.log('❌ ROOT CAUSE: BCS402 IA marks specifically missing');
      console.log('🔧 SOLUTION: Faculty must enter BCS402 IA marks');
      console.log(`✅ Other subjects found: ${allIARecords.map(r => r.subject_code).join(', ')}`);
    } else {
      console.log('✅ IA marks exist in database');
      console.log('🔧 If marks not showing in Student Progress Card, check:');
      console.log('   - Academic year matching');
      console.log('   - Department case sensitivity');
      console.log('   - Browser cache/refresh');
    }

  } catch (error) {
    console.error('❌ Database check failed:', error);
  }
}

// Run the database check
checkIAMarksDatabase();
