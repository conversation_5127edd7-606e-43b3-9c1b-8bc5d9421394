import { supabase } from '../integrations/supabase/client';
import { TimetableService, TimeStructure, TimetableSlot } from './TimetableService';
import { ClassAvailabilityService } from './ClassAvailabilityService';

export interface TheoryMapping {
  id: string;
  subject_code: string;
  subject_name: string;
  subject_short_id?: string;
  faculty_id: string;
  faculty_name: string;
  weekly_hours: number;
}

export interface TheorySlotGenerationParams {
  academicYear: string;
  semester: string;
  section: string;
  department: string;
  tutorialHours?: string;
}

export interface ValidationRule {
  name: string;
  description: string;
  enabled: boolean;
}

export interface TimetableValidationResult {
  isValid: boolean;
  violations: ValidationViolation[];
  warnings: ValidationWarning[];
  optimizationSuggestions: OptimizationSuggestion[];
}

export interface ValidationViolation {
  id: string;
  type: 'conflict_prevention' | 'successive_period' | 'morning_period_limit' | 'gap_prevention';
  severity: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  affectedSlots: string[];
  facultyId?: string;
  facultyName?: string;
  subjectCode?: string;
  day?: string;
  timeSlot?: string;
  suggestedFix?: string;
}

export interface ValidationWarning {
  id: string;
  type: 'distribution' | 'optimization' | 'preference';
  description: string;
  affectedSlots: string[];
  suggestion?: string;
}

export interface OptimizationSuggestion {
  id: string;
  type: 'swap' | 'move' | 'redistribute';
  description: string;
  expectedBenefit: string;
  complexity: 'low' | 'medium' | 'high';
}



export class TheorySlotGenerator {
  private unallocatedSubjects: string[] = [];
  private partiallyAllocatedSubjects: Array<{
    subject_code: string;
    subject_name: string;
    expected_slots: number;
    allocated_slots: number;
    missing_slots: number;
  }> = [];
  private conflictLog: Array<{
    subject: string;
    conflicts: Array<{
      day: string;
      timeSlot: string;
      conflictType: 'class_occupied' | 'faculty_unavailable' | 'skill_lab_conflict' | 'cross_class_conflict';
      details: string;
    }>;
    facultyAvailability: Record<string, string[]>;
    suggestions: string[];
  }> = [];
  private allocationStats = {
    totalSubjects: 0,
    successfullyAllocated: 0,
    partiallyAllocated: 0,
    conflictTypes: {
      class_occupied: 0,
      faculty_unavailable: 0,
      skill_lab_conflict: 0,
      cross_class_conflict: 0
    }
  };

  // Enhanced validation rules
  private validationRules: ValidationRule[] = [
    {
      name: 'conflict_prevention',
      description: 'Prevent faculty scheduling conflicts (overlapping time slots)',
      enabled: true
    },
    {
      name: 'successive_period_avoidance',
      description: 'Avoid scheduling same subject in consecutive periods',
      enabled: true
    },
    {
      name: 'morning_period_distribution',
      description: 'Limit each subject to maximum 1 morning period per week',
      enabled: true
    },
    {
      name: 'gap_prevention',
      description: 'Prevent vacant/empty periods in student timetables',
      enabled: true
    }
  ];

  /**
   * Generate theory slots using intelligent allocation algorithm
   */
  async generateTheorySlots(
    theoryMappings: TheoryMapping[],
    params: TheorySlotGenerationParams
  ): Promise<TimetableSlot[]> {
    console.log('🎯 Starting intelligent theory slot generation...');

    // Clear previous state
    this.unallocatedSubjects = [];
    this.partiallyAllocatedSubjects = [];
    this.conflictLog = [];
    this.allocationStats = {
      totalSubjects: 0,
      successfullyAllocated: 0,
      partiallyAllocated: 0,
      conflictTypes: {
        class_occupied: 0,
        faculty_unavailable: 0,
        skill_lab_conflict: 0,
        cross_class_conflict: 0
      }
    };

    // Fetch time structure
    const timeStructure = await TimetableService.fetchTimeStructure({
      academicYear: params.academicYear,
      department: params.department
    });

    if (!timeStructure) {
      throw new Error('Time structure not found');
    }

    // Note: We don't need to fetch existing slots here as we'll check conflicts dynamically

    // 🔥 CRITICAL FIX: Get ALL skill lab sessions across ALL classes to check faculty conflicts
    console.log('🔍 Checking for skill lab conflicts across all classes...');
    const { data: allSkillLabSlots, error: skillLabError } = await supabase
      .from('timetable_slots')
      .select('*')
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .or('subject_code.eq.SKILL LAB,subject_type.eq.skill_lab');

    if (skillLabError) {
      console.error('Error fetching skill lab slots:', skillLabError);
    } else {
      console.log(`Found ${allSkillLabSlots?.length || 0} skill lab sessions across all classes`);
      if (allSkillLabSlots && allSkillLabSlots.length > 0) {
        allSkillLabSlots.forEach(slot => {
          console.log(`  Skill lab: ${slot.day} ${slot.time_slot} (${slot.semester}${slot.section}) - Faculty: ${slot.faculty_name}`);
        });
      }
    }

    // Initialize class availability tracking
    const classAvailability = await ClassAvailabilityService.initializeClassAvailability(
      params.academicYear,
      params.department,
      params.semester,
      params.section,
      timeStructure
    );

    // Get faculty IDs for availability checking
    const facultyIds = [...new Set(theoryMappings.map(m => m.faculty_id))];

    // Fetch faculty availability
    const { data: facultyData, error: facultyError } = await supabase
      .from('employee_details')
      .select('id, full_name, vacant_by_day, vacant_count_by_day')
      .in('id', facultyIds);

    if (facultyError) {
      console.error('Error fetching faculty availability:', facultyError);
      throw facultyError;
    }

    // Create faculty availability map
    const facultyAvailability: Record<string, {
      full_name: string;
      vacant_by_day: Record<string, number>;
      vacant_count_by_day: Record<string, string[]>;
    }> = {};

    facultyData?.forEach(faculty => {
      facultyAvailability[faculty.id] = {
        full_name: faculty.full_name,
        vacant_by_day: faculty.vacant_by_day || {},
        vacant_count_by_day: faculty.vacant_count_by_day || {}
      };
    });

    // Generate theory slots using intelligent algorithm with enhanced validation
    const generatedSlots = await this.intelligentTheoryAllocationWithValidation(
      theoryMappings,
      params,
      timeStructure,
      classAvailability,
      facultyAvailability,
      allSkillLabSlots || []
    );

    console.log(`✅ Generated ${generatedSlots.length} theory slots with enhanced validation`);

    // 🔍 Run comprehensive validation on generated timetable
    const validationResult = await this.validateGeneratedTimetable(generatedSlots, params, timeStructure);

    if (!validationResult.isValid) {
      console.warn('⚠️ Validation issues detected:', validationResult.violations);

      // Attempt automatic conflict resolution
      const resolvedSlots = await this.autoResolveValidationIssues(generatedSlots, validationResult, params, timeStructure);

      if (resolvedSlots.length > 0) {
        console.log(`✅ Auto-resolved ${resolvedSlots.length - generatedSlots.length} validation issues`);
        generatedSlots.splice(0, generatedSlots.length, ...resolvedSlots);
      }
    }

    // 📊 Generate comprehensive allocation report
    this.generateAllocationReport(theoryMappings);

    if (this.unallocatedSubjects.length > 0) {
      console.warn('⚠️ Some subjects could not be allocated:', this.unallocatedSubjects);
      this.logDetailedConflictAnalysis();
    }

    // Save generated slots to database
    if (generatedSlots.length > 0) {
      await this.saveTheorySlots(generatedSlots);

      // Update faculty availability in database after slot placement
      await this.updateFacultyAvailabilityInDatabase(facultyAvailability);
    }

    return generatedSlots;
  }

  /**
   * Enhanced intelligent theory slot allocation algorithm with comprehensive validation
   */
  private async intelligentTheoryAllocationWithValidation(
    theoryMappings: TheoryMapping[],
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure,
    classAvailability: { vacant_by_day: Record<string, number>; vacant_count_by_day: Record<string, string[]>; },
    facultyAvailability: Record<string, any>,
    allSkillLabSlots: any[]
  ): Promise<TimetableSlot[]> {
    const result: TimetableSlot[] = [];
    const days = timeStructure.working_days;

    // Initialize class availability for each day
    const avail: Record<string, string[]> = {};
    days.forEach(day => {
      avail[day] = ClassAvailabilityService.getAvailableSlots(classAvailability, day);
    });

    // 🔥 CRITICAL FIX: Exclude skill lab time slots from class availability
    // Skill labs for THIS class should block theory slot placement
    const currentClassSkillLabs = (allSkillLabSlots || []).filter(slot =>
      slot.semester === params.semester && slot.section === params.section
    );

    if (currentClassSkillLabs.length > 0) {
      console.log(`🚫 Excluding ${currentClassSkillLabs.length} skill lab time slots from class availability:`);
      currentClassSkillLabs.forEach(skillLab => {
        if (avail[skillLab.day]) {
          // Remove skill lab time slots from available slots
          avail[skillLab.day] = avail[skillLab.day].filter(timeSlot => {
            // Check if this time slot overlaps with the skill lab session
            const isOverlapping = this.timeSlotOverlaps(timeSlot, skillLab.time_slot);
            if (isOverlapping) {
              console.log(`  Excluding ${skillLab.day} ${timeSlot} (conflicts with skill lab ${skillLab.time_slot})`);
            }
            return !isOverlapping;
          });
        }
      });
    }

    // 🔥 CRITICAL FIX: Update faculty availability based on ALL skill lab commitments
    // Faculty assigned to skill labs in ANY class should have reduced availability
    if (allSkillLabSlots && allSkillLabSlots.length > 0) {
      console.log('🔄 Updating faculty availability based on skill lab commitments...');

      allSkillLabSlots.forEach(skillLab => {
        // Update availability for primary faculty
        if (skillLab.faculty_id && facultyAvailability[skillLab.faculty_id]) {
          this.reduceFacultyAvailabilityForSkillLab(
            skillLab.faculty_id,
            skillLab.day,
            skillLab.time_slot,
            facultyAvailability
          );
        }

        // Update availability for secondary faculty (if exists)
        if (skillLab.faculty2_id && facultyAvailability[skillLab.faculty2_id]) {
          this.reduceFacultyAvailabilityForSkillLab(
            skillLab.faculty2_id,
            skillLab.day,
            skillLab.time_slot,
            facultyAvailability
          );
        }
      });
    }

    // Sort subjects by weekly hours (descending) for priority allocation
    const sortedMappings = [...theoryMappings].sort((a, b) => b.weekly_hours - a.weekly_hours);

    // Initialize allocation statistics
    this.allocationStats.totalSubjects = sortedMappings.length;
    this.allocationStats.successfullyAllocated = 0;

    // Track class-level slot conflicts - initialize with existing theory slots
    const classOccupiedSlots = new Set<string>();

    // 🔥 CRITICAL: Clear existing theory slots before generation to avoid conflicts
    console.log('🧹 Clearing existing theory slots before generation...');
    const { error: deleteError } = await supabase
      .from('timetable_slots')
      .delete()
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .eq('semester', params.semester)
      .eq('section', params.section)
      .eq('subject_type', 'theory');

    if (deleteError) {
      console.error('Error clearing existing theory slots:', deleteError);
    } else {
      console.log('✅ Cleared existing theory slots');
    }

    // Get existing theory, tutorial, and elective slots for this class to avoid conflicts
    console.log('🔍 Fetching existing theory, tutorial, and elective slots to avoid conflicts...');
    const { data: existingTheorySlots, error: existingError } = await supabase
      .from('timetable_slots')
      .select('day, time_slot, subject_type')
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .eq('semester', params.semester)
      .eq('section', params.section)
      .or('subject_type.eq.theory,subject_type.eq.tutorial,subject_type.eq.elective');

    if (existingError) {
      console.error('Error fetching existing theory/tutorial/elective slots:', existingError);
    } else {
      console.log(`Found ${existingTheorySlots?.length || 0} existing theory/tutorial/elective slots`);
      existingTheorySlots?.forEach(slot => {
        const slotKey = `${slot.day}-${slot.time_slot}`;
        classOccupiedSlots.add(slotKey);
        console.log(`  Marking ${slotKey} as occupied by existing ${slot.subject_type} slot`);
      });
    }

    // Debug: Show class availability after all exclusions
    console.log('\n📋 Class availability after lab allocation:');
    days.forEach(day => {
      const availableSlots = avail[day] || [];
      console.log(`  ${day}: ${availableSlots.length} slots available - [${availableSlots.join(', ')}]`);
    });

    console.log('\n🚫 Currently occupied slots (theory/tutorial/elective):');
    if (classOccupiedSlots.size === 0) {
      console.log('  No existing slots found');
    } else {
      Array.from(classOccupiedSlots).forEach(slotKey => {
        console.log(`  ${slotKey}`);
      });
    }

    // 🔄 IMPROVED ALLOCATION STRATEGY: Round-robin allocation for fair distribution
    console.log('\n🎯 Using round-robin allocation strategy for fair slot distribution...');

    // Track allocation progress for each subject
    const allocationProgress: Record<string, {
      mapping: TheoryMapping;
      slotsPlaced: number;
      targetSlots: number;
      conflicts: Array<{
        day: string;
        timeSlot: string;
        conflictType: 'class_occupied' | 'faculty_unavailable' | 'skill_lab_conflict' | 'cross_class_conflict';
        details: string;
      }>;
    }> = {};

    // Initialize progress tracking
    sortedMappings.forEach(mapping => {
      allocationProgress[mapping.subject_code] = {
        mapping,
        slotsPlaced: 0,
        targetSlots: mapping.weekly_hours,
        conflicts: []
      };
    });

    // Round-robin allocation: place one slot per subject per round
    let totalSlotsNeeded = sortedMappings.reduce((sum, m) => sum + m.weekly_hours, 0);
    let totalSlotsPlaced = 0;
    let maxRounds = totalSlotsNeeded * 2; // Safety limit to prevent infinite loops
    let currentRound = 0;

    while (totalSlotsPlaced < totalSlotsNeeded && currentRound < maxRounds) {
      currentRound++;
      console.log(`\n🔄 ROUND ${currentRound}: Attempting to place one slot per subject...`);

      let slotsPlacedThisRound = 0;

      for (const mapping of sortedMappings) {
        const progress = allocationProgress[mapping.subject_code];

        // Skip if this subject already has all required slots
        if (progress.slotsPlaced >= progress.targetSlots) {
          continue;
        }

        console.log(`\n📚 ${mapping.subject_code}: Needs ${progress.targetSlots - progress.slotsPlaced} more slots`);

        // Try to place one slot for this subject
        let slotPlaced = false;

        for (const day of days) {
          if (slotPlaced) break;

          // Get available slots for this day
          const daySlots = avail[day];

          // 🧠 INTELLIGENT CONFLICT RESOLUTION: Check real-time faculty conflicts
          const facultyAvailableSlots = await this.getIntelligentFacultyAvailableSlots(
            mapping.faculty_id,
            day,
            daySlots
          );

          // Try to place a slot in available time slots
          for (const timeSlot of facultyAvailableSlots) {
            if (slotPlaced) break;

            // Check if class slot is free
            const slotKey = `${day}-${timeSlot}`;

            if (!classOccupiedSlots.has(slotKey)) {
              // 🔍 FINAL CONFLICT CHECK: Verify no cross-semester conflicts
              const hasConflict = await this.checkCrossSemesterConflict(
                mapping.faculty_id,
                day,
                timeSlot,
                params
              );

              if (!hasConflict) {
                // 🔍 ENHANCED VALIDATION: Check all validation rules before placing slot
                const validationPassed = await this.validateSlotPlacement(
                  mapping,
                  day,
                  timeSlot,
                  result,
                  timeStructure
                );

                if (validationPassed) {
                  // Create and place the slot
                  const theorySlot = this.createTimetableSlot(mapping, day, timeSlot, params);
                  result.push(theorySlot);

                  classOccupiedSlots.add(slotKey);
                  this.updateFacultyAvailability(mapping.faculty_id, day, timeSlot, facultyAvailability);

                  progress.slotsPlaced++;
                  totalSlotsPlaced++;
                  slotsPlacedThisRound++;
                  slotPlaced = true;

                  console.log(`     ✅ PLACED: ${mapping.subject_code} on ${day} at ${timeSlot} (${progress.slotsPlaced}/${progress.targetSlots})`);
                  break;
                } else {
                  console.log(`     🚫 VALIDATION FAILED: ${mapping.subject_code} cannot be placed on ${day} at ${timeSlot} due to validation rules`);
                }
              } else {
                console.log(`     🚫 CROSS-SEMESTER CONFLICT: ${mapping.subject_code} cannot be placed on ${day} at ${timeSlot} due to faculty conflict`);

                // Record the conflict for analysis
                progress.conflicts.push({
                  day,
                  timeSlot,
                  conflictType: 'cross_class_conflict',
                  details: `Faculty ${mapping.faculty_name} has conflicting commitment in another semester/section`
                });
                this.allocationStats.conflictTypes.cross_class_conflict++;
              }
            } else {
              console.log(`     🚫 CLASS OCCUPIED: ${mapping.subject_code} cannot be placed on ${day} at ${timeSlot} - slot already occupied in this class`);

              // Record the conflict for analysis
              progress.conflicts.push({
                day,
                timeSlot,
                conflictType: 'class_occupied',
                details: `Time slot already occupied by another subject in this class`
              });
              this.allocationStats.conflictTypes.class_occupied++;
            }
          }
        }

        // If no slot was placed for this subject, try intelligent slot expansion
        if (!slotPlaced) {
          console.log(`     ❌ No available slot found for ${mapping.subject_code} in this round`);

          // 🧠 INTELLIGENT SLOT EXPANSION: Try to find alternative slots
          const alternativeSlot = await this.findAlternativeSlotIntelligently(
            mapping,
            classOccupiedSlots,
            params,
            timeStructure
          );

          if (alternativeSlot) {
            const theorySlot = this.createTimetableSlot(mapping, alternativeSlot.day, alternativeSlot.timeSlot, params);
            result.push(theorySlot);

            classOccupiedSlots.add(`${alternativeSlot.day}-${alternativeSlot.timeSlot}`);
            this.updateFacultyAvailability(mapping.faculty_id, alternativeSlot.day, alternativeSlot.timeSlot, facultyAvailability);

            progress.slotsPlaced++;
            totalSlotsPlaced++;
            slotsPlacedThisRound++;

            console.log(`     🎯 INTELLIGENT PLACEMENT: ${mapping.subject_code} on ${alternativeSlot.day} at ${alternativeSlot.timeSlot} (${progress.slotsPlaced}/${progress.targetSlots})`);
          }
        }
      }

      console.log(`Round ${currentRound} completed: ${slotsPlacedThisRound} slots placed`);

      // If no slots were placed in this round, we're stuck
      if (slotsPlacedThisRound === 0) {
        console.warn(`⚠️ No progress made in round ${currentRound}. Stopping allocation.`);
        break;
      }
    }

    // Generate final allocation report
    console.log('\n📊 FINAL ALLOCATION RESULTS:');
    for (const mapping of sortedMappings) {
      const progress = allocationProgress[mapping.subject_code];

      if (progress.slotsPlaced < progress.targetSlots) {
        const missingSlots = progress.targetSlots - progress.slotsPlaced;

        if (progress.slotsPlaced === 0) {
          console.warn(`❌ COMPLETE FAILURE: ${mapping.subject_code} (${mapping.subject_name})`);
          console.warn(`   No slots allocated out of ${progress.targetSlots} required`);
          this.unallocatedSubjects.push(mapping.subject_code);
        } else {
          console.warn(`⚠️ PARTIAL ALLOCATION: ${mapping.subject_code} (${mapping.subject_name})`);
          console.warn(`   Only placed ${progress.slotsPlaced}/${progress.targetSlots} slots`);
          console.warn(`   Missing ${missingSlots} slots`);

          this.partiallyAllocatedSubjects.push({
            subject_code: mapping.subject_code,
            subject_name: mapping.subject_name,
            expected_slots: progress.targetSlots,
            allocated_slots: progress.slotsPlaced,
            missing_slots: missingSlots
          });
          this.allocationStats.partiallyAllocated++;
        }

        // Store detailed conflict information
        const facultyAvailabilitySnapshot: Record<string, string[]> = {};
        days.forEach(day => {
          facultyAvailabilitySnapshot[day] = facultyAvailability[mapping.faculty_id]?.vacant_count_by_day[day] || [];
        });

        this.conflictLog.push({
          subject: `${mapping.subject_code} (${mapping.subject_name})`,
          conflicts: progress.conflicts,
          facultyAvailability: facultyAvailabilitySnapshot,
          suggestions: this.generateSuggestions(mapping, progress.conflicts, facultyAvailabilitySnapshot, avail)
        });
      } else {
        this.allocationStats.successfullyAllocated++;
        console.log(`✅ SUCCESS: ${mapping.subject_code} fully allocated (${progress.slotsPlaced}/${progress.targetSlots} slots)`);
      }
    }

    return result;
  }

  /**
   * Create a timetable slot object
   */
  private createTimetableSlot(
    mapping: TheoryMapping,
    day: string,
    timeSlot: string,
    params: TheorySlotGenerationParams
  ): TimetableSlot {
    return {
      academic_year: params.academicYear,
      department: params.department,
      semester: params.semester,
      section: params.section,
      day,
      time_slot: timeSlot,
      subject_id: mapping.id,
      subject_code: mapping.subject_code,
      subject_name: mapping.subject_name,
      subject_short_id: mapping.subject_short_id,
      subject_type: 'theory',
      faculty_id: mapping.faculty_id,
      faculty_name: mapping.faculty_name,
      col_span: 1
    };
  }

  /**
   * Update faculty availability after slot placement
   */
  private updateFacultyAvailability(
    facultyId: string,
    day: string,
    timeSlot: string,
    facultyAvailability: Record<string, any>
  ): void {
    if (facultyAvailability[facultyId]?.vacant_count_by_day[day]) {
      const slots = facultyAvailability[facultyId].vacant_count_by_day[day];
      const index = slots.indexOf(timeSlot);
      if (index > -1) {
        slots.splice(index, 1);
      }

      // Update vacant_by_day count
      if (facultyAvailability[facultyId].vacant_by_day) {
        facultyAvailability[facultyId].vacant_by_day[day] = slots.length;
      }
    }
  }

  /**
   * Generate all time slots for the day
   */
  private getAllTimeSlots(timeStructure: TimeStructure): string[] {
    const slots: string[] = [];
    const duration = timeStructure.theory_class_duration;

    // Generate slots from first half start to tea break
    let currentTime = timeStructure.first_half_start_time;
    while (this.timeIsBefore(currentTime, timeStructure.tea_break_start_time)) {
      const endTime = this.addMinutes(currentTime, duration);
      slots.push(`${currentTime}-${endTime}`);
      currentTime = endTime;
    }

    // Generate slots from tea break end to lunch break
    currentTime = timeStructure.tea_break_end_time;
    while (this.timeIsBefore(currentTime, timeStructure.lunch_break_start_time)) {
      const endTime = this.addMinutes(currentTime, duration);
      slots.push(`${currentTime}-${endTime}`);
      currentTime = endTime;
    }

    // Generate slots from lunch break end to second half end
    currentTime = timeStructure.lunch_break_end_time;
    while (this.timeIsBefore(currentTime, timeStructure.second_half_end_time)) {
      const endTime = this.addMinutes(currentTime, duration);
      if (this.timeIsBefore(endTime, timeStructure.second_half_end_time) || endTime === timeStructure.second_half_end_time) {
        slots.push(`${currentTime}-${endTime}`);
      }
      currentTime = endTime;
    }

    return slots;
  }

  /**
   * Add minutes to time string
   */
  private addMinutes(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number);
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60);
    const newMins = totalMinutes % 60;
    return `${String(newHours).padStart(2, '0')}:${String(newMins).padStart(2, '0')}`;
  }

  /**
   * Compare two time strings
   */
  private timeIsBefore(time1: string, time2: string): boolean {
    const [h1, m1] = time1.split(':').map(Number);
    const [h2, m2] = time2.split(':').map(Number);
    return h1 < h2 || (h1 === h2 && m1 < m2);
  }

  /**
   * Save theory slots to database
   */
  private async saveTheorySlots(slots: TimetableSlot[]): Promise<void> {
    console.log(`💾 Saving ${slots.length} theory slots to database...`);

    const { error } = await supabase
      .from('timetable_slots')
      .insert(slots);

    if (error) {
      console.error('❌ Error saving theory slots:', error);
      throw error;
    }

    console.log(`✅ Successfully saved ${slots.length} theory slots to database`);
  }

  /**
   * Update faculty availability in database after slot placement
   */
  private async updateFacultyAvailabilityInDatabase(
    facultyAvailability: Record<string, {
      full_name: string;
      vacant_by_day: Record<string, number>;
      vacant_count_by_day: Record<string, string[]>;
    }>
  ): Promise<void> {
    console.log('💾 Updating faculty availability in database...');

    const updates = Object.entries(facultyAvailability).map(([facultyId, availability]) => ({
      id: facultyId,
      vacant_by_day: availability.vacant_by_day,
      vacant_count_by_day: availability.vacant_count_by_day
    }));

    for (const update of updates) {
      const { error } = await supabase
        .from('employee_details')
        .update({
          vacant_by_day: update.vacant_by_day,
          vacant_count_by_day: update.vacant_count_by_day
        })
        .eq('id', update.id);

      if (error) {
        console.error(`❌ Error updating faculty availability for ${update.id}:`, error);
      } else {
        console.log(`✅ Updated availability for faculty ${update.id}`);
      }
    }

    console.log('✅ Faculty availability update complete');
  }

  /**
   * Get list of subjects that could not be allocated
   */
  getUnallocatedSubjects(): string[] {
    return [...this.unallocatedSubjects];
  }

  /**
   * Get detailed allocation results for debugging
   */
  getAllocationResults() {
    return {
      unallocatedSubjects: [...this.unallocatedSubjects],
      partiallyAllocatedSubjects: [...this.partiallyAllocatedSubjects],
      allocationStats: { ...this.allocationStats },
      conflictLog: [...this.conflictLog]
    };
  }

  /**
   * Check if two time slots overlap
   */
  private timeSlotOverlaps(timeSlot1: string, timeSlot2: string): boolean {
    const [start1, end1] = timeSlot1.split('-');
    const [start2, end2] = timeSlot2.split('-');

    const start1Minutes = this.timeToMinutes(start1);
    const end1Minutes = this.timeToMinutes(end1);
    const start2Minutes = this.timeToMinutes(start2);
    const end2Minutes = this.timeToMinutes(end2);

    // Check if there's any overlap
    return start1Minutes < end2Minutes && start2Minutes < end1Minutes;
  }

  /**
   * Convert time string to minutes
   */
  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Reduce faculty availability for skill lab commitments
   */
  private reduceFacultyAvailabilityForSkillLab(
    facultyId: string,
    day: string,
    skillLabTimeSlot: string,
    facultyAvailability: Record<string, any>
  ): void {
    if (!facultyAvailability[facultyId]) return;

    const faculty = facultyAvailability[facultyId];
    const daySlots = faculty.vacant_count_by_day[day] || [];

    console.log(`🔄 Reducing availability for faculty ${faculty.full_name} on ${day} due to skill lab ${skillLabTimeSlot}`);

    // Remove all time slots that overlap with the skill lab session
    const updatedSlots = daySlots.filter((timeSlot: string) => {
      const overlaps = this.timeSlotOverlaps(timeSlot, skillLabTimeSlot);
      if (overlaps) {
        console.log(`  Removing ${timeSlot} (overlaps with skill lab ${skillLabTimeSlot})`);
      }
      return !overlaps;
    });

    // Update faculty availability
    faculty.vacant_count_by_day[day] = updatedSlots;
    faculty.vacant_by_day[day] = updatedSlots.length;

    console.log(`  Updated availability: ${updatedSlots.length} slots remaining on ${day}`);
  }

  /**
   * 🧠 INTELLIGENT FACULTY AVAILABILITY: Get real-time available slots for faculty
   * This replaces the stale vacant_count_by_day data with live conflict checking
   */
  private async getIntelligentFacultyAvailableSlots(
    facultyId: string,
    day: string,
    classAvailableSlots: string[]
  ): Promise<string[]> {
    // Get faculty's base availability from database
    const { data: facultyData } = await supabase
      .from('employee_details')
      .select('vacant_count_by_day, full_name')
      .eq('id', facultyId)
      .single();

    if (!facultyData?.vacant_count_by_day) {
      console.warn(`No availability data for faculty ${facultyId}`);
      return [];
    }

    const baseFacultySlots = facultyData.vacant_count_by_day[day] || [];

    // Filter by class availability first
    const potentialSlots = classAvailableSlots.filter(slot =>
      baseFacultySlots.includes(slot)
    );

    console.log(`🔍 Faculty ${facultyData.full_name} on ${day}: ${potentialSlots.length} potential slots from base availability`);

    return potentialSlots;
  }

  /**
   * 🧠 INTELLIGENT ALTERNATIVE SLOT FINDER: Find slots beyond basic availability
   * This method uses advanced logic to find slots when normal allocation fails
   */
  private async findAlternativeSlotIntelligently(
    mapping: any,
    classOccupiedSlots: Set<string>,
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure
  ): Promise<{ day: string; timeSlot: string } | null> {
    console.log(`🔍 Searching for alternative slots for ${mapping.subject_code} (Faculty: ${mapping.faculty_name})`);

    // 🚨 CRITICAL: Get REAL-TIME class conflicts including labs, skill labs, electives, tutorials
    const realTimeClassConflicts = await this.getRealTimeClassConflicts(params);
    console.log(`🔍 Found ${realTimeClassConflicts.size} real-time class conflicts`);

    // 🔍 DEBUG: Show all detected conflicts
    if (realTimeClassConflicts.size > 0) {
      console.log(`🔍 DETECTED CLASS CONFLICTS:`);
      Array.from(realTimeClassConflicts).forEach(conflict => {
        console.log(`   🚫 ${conflict}`);
      });
    } else {
      console.log(`⚠️ WARNING: No class conflicts detected - this might be wrong!`);
    }

    // Get all possible time slots
    const allTimeSlots = this.getAllTimeSlots(timeStructure);
    const workingDays = timeStructure.working_days;

    // 🧠 INTELLIGENT CLASS-FIRST STRATEGY: Find ALL vacant class slots first
    console.log(`🎯 INTELLIGENT APPROACH: Finding all vacant class slots first...`);

    const vacantClassSlots: Array<{day: string, timeSlot: string}> = [];

    for (const day of workingDays) {
      for (const timeSlot of allTimeSlots) {
        const slotKey = `${day}-${timeSlot}`;

        // Check if class is completely free at this time
        const isClassFree = !classOccupiedSlots.has(slotKey) && !realTimeClassConflicts.has(slotKey);

        if (isClassFree) {
          vacantClassSlots.push({ day, timeSlot });
          console.log(`     ✅ VACANT CLASS SLOT: ${slotKey}`);
        } else {
          if (classOccupiedSlots.has(slotKey)) {
            console.log(`     ❌ SKIP ${slotKey}: Already occupied by theory/tutorial/elective`);
          }
          if (realTimeClassConflicts.has(slotKey)) {
            console.log(`     ❌ SKIP ${slotKey}: Class has lab/skill lab conflict`);
          }
        }
      }
    }

    console.log(`🔍 Found ${vacantClassSlots.length} vacant class slots`);

    // Now check faculty availability for these vacant class slots
    for (const vacantSlot of vacantClassSlots) {
      const { day, timeSlot } = vacantSlot;

      console.log(`🔍 Checking faculty availability for vacant slot: ${day} at ${timeSlot}`);

      // Check cross-semester faculty conflicts
      const hasConflict = await this.checkCrossSemesterConflict(
        mapping.faculty_id,
        day,
        timeSlot,
        params
      );

      if (!hasConflict) {
        console.log(`🎯 PERFECT MATCH: ${day} at ${timeSlot} - Class is free AND faculty is available!`);
        return { day, timeSlot };
      } else {
        console.log(`     ❌ Faculty conflict: ${mapping.faculty_name} not available on ${day} at ${timeSlot}`);
      }
    }

    console.log(`❌ No slots found where both class is free AND faculty is available`);

    // If no perfect match, show summary
    console.log(`📊 SUMMARY for ${mapping.subject_code}:`);
    console.log(`   Total vacant class slots: ${vacantClassSlots.length}`);
    console.log(`   Faculty conflicts prevented placement in all vacant slots`);

    if (vacantClassSlots.length > 0) {
      console.log(`   Suggestion: Consider reassigning faculty or adjusting faculty schedules`);
    } else {
      console.log(`   Issue: No vacant class slots available - class is completely full`);
    }

    // Strategy 2: Check if faculty has any completely free days (with proper class conflict checking)
    console.log(`🔍 Checking for completely free days for faculty ${mapping.faculty_name}`);

    for (const day of workingDays) {
      // Check if faculty has NO commitments on this day
      const { data: dayCommitments } = await supabase
        .from('timetable_slots')
        .select('time_slot')
        .eq('academic_year', params.academicYear)
        .eq('department', params.department)
        .or(`faculty_id.eq.${mapping.faculty_id},faculty2_id.eq.${mapping.faculty_id}`)
        .eq('day', day);

      if (!dayCommitments || dayCommitments.length === 0) {
        // Faculty is completely free on this day, find first available class slot
        for (const timeSlot of allTimeSlots) {
          const slotKey = `${day}-${timeSlot}`;

          // 🚨 PROPER CHECKS: Ensure no class conflicts
          if (!classOccupiedSlots.has(slotKey) && !realTimeClassConflicts.has(slotKey)) {
            console.log(`🎯 FOUND FREE DAY SLOT: ${day} at ${timeSlot} for ${mapping.subject_code}`);
            return { day, timeSlot };
          }
        }
      }
    }

    // Strategy 3: Intelligent slot swapping - find if we can swap with existing slots
    console.log(`🔄 Attempting intelligent slot swapping for ${mapping.subject_code}`);

    const swapOpportunity = await this.findSlotSwapOpportunity(
      mapping,
      classOccupiedSlots,
      params,
      timeStructure
    );

    if (swapOpportunity) {
      console.log(`🎯 FOUND SWAP OPPORTUNITY: Move existing slot to make room for ${mapping.subject_code}`);
      return swapOpportunity;
    }

    console.log(`❌ No alternative slots found for ${mapping.subject_code}`);
    return null;
  }

  /**
   * 🚨 GET REAL-TIME CLASS CONFLICTS: Check for labs, skill labs, electives, tutorials
   * This is CRITICAL - ensures theory slots don't overlap with existing class commitments
   */
  private async getRealTimeClassConflicts(params: TheorySlotGenerationParams): Promise<Set<string>> {
    const conflicts = new Set<string>();

    console.log(`🔍 DEBUGGING: Searching for class conflicts with params:`);
    console.log(`   Academic Year: ${params.academicYear}`);
    console.log(`   Department: ${params.department}`);
    console.log(`   Semester: ${params.semester}`);
    console.log(`   Section: ${params.section}`);

    // Get ALL existing slots for this class (labs, skill labs, electives, tutorials)
    const { data: existingSlots, error } = await supabase
      .from('timetable_slots')
      .select('day, time_slot, subject_type, subject_code, col_span, id')
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .eq('semester', params.semester)
      .eq('section', params.section)
      .in('subject_type', ['lab', 'laboratory', 'skill_lab', 'elective', 'tutorial']);

    if (error) {
      console.error('❌ Error fetching real-time class conflicts:', error);
      return conflicts;
    }

    console.log(`🔍 Query returned ${existingSlots?.length || 0} slots`);

    // Get time structure to understand period layout for 3-hour morning lab detection
    const { data: timeStructure } = await supabase
      .from('time_structure')
      .select('*')
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .single();

    if (existingSlots && existingSlots.length > 0) {
      console.log(`🔍 Processing ${existingSlots.length} existing class commitments:`);

      existingSlots.forEach(slot => {
        const slotKey = `${slot.day}-${slot.time_slot}`;
        conflicts.add(slotKey);
        console.log(`   🚫 CONFLICT: ${slotKey} - ${slot.subject_type.toUpperCase()} (${slot.subject_code}) [ID: ${slot.id}]`);

        // 🚨 CRITICAL FIX: For multi-period slots (labs), break them down into individual periods
        if (slot.col_span && slot.col_span > 1) {
          console.log(`   📏 Multi-period slot detected: col_span = ${slot.col_span}`);

          // Break down the multi-period slot into individual theory-sized periods
          const individualPeriods = this.breakDownMultiPeriodSlot(slot.day, slot.time_slot, slot.col_span);
          individualPeriods.forEach(period => {
            conflicts.add(period);
            console.log(`   🚫 INDIVIDUAL PERIOD CONFLICT: ${period} (from multi-period ${slotKey})`);
          });
        }

        // 🚨 CRITICAL FIX: Handle 3-hour morning lab continuation periods
        // Check if this is a 3-hour morning lab that creates a continuation in period 3
        if ((slot.subject_type === 'lab' || slot.subject_type === 'laboratory') &&
            slot.col_span && slot.col_span >= 3) {

          const labContinuationPeriods = this.detect3HourMorningLabContinuation(
            slot.day,
            slot.time_slot,
            slot.col_span,
            timeStructure
          );

          labContinuationPeriods.forEach(continuationPeriod => {
            conflicts.add(continuationPeriod);
            console.log(`   🚫 LAB CONTINUATION CONFLICT: ${continuationPeriod} (3-hour morning lab continuation)`);
          });
        }
      });
    } else {
      console.log(`⚠️ WARNING: No existing slots found for ${params.semester}${params.section}`);
      console.log(`   This could mean:`);
      console.log(`   1. No labs/skill labs/electives/tutorials have been generated yet`);
      console.log(`   2. The query parameters are incorrect`);
      console.log(`   3. The data is stored with different field values`);
    }

    return conflicts;
  }

  /**
   * 🚨 DETECT 3-HOUR MORNING LAB CONTINUATION: Find period 3 continuation slots
   * This is CRITICAL - ensures theory slots don't overlap with lab continuation periods
   */
  private detect3HourMorningLabContinuation(
    day: string,
    timeSlot: string,
    colSpan: number,
    timeStructure: any
  ): string[] {
    const continuationPeriods: string[] = [];

    // Check if this is a 3-hour morning lab (starts at period 1 with 3+ periods)
    const startTime = this.extractStartTime(timeSlot);
    const is3HourMorningLab = this.is3HourMorningLabByTime(startTime, colSpan);

    if (is3HourMorningLab) {
      // For 3-hour morning labs, period 3 (10:35-11:30) is a continuation period
      const period3TimeSlot = this.getPeriod3TimeSlot(timeStructure);
      if (period3TimeSlot) {
        const continuationKey = `${day}-${period3TimeSlot}`;
        continuationPeriods.push(continuationKey);
      }
    }

    return continuationPeriods;
  }

  /**
   * Check if a lab is a 3-hour morning lab based on start time and duration
   */
  private is3HourMorningLabByTime(startTime: string, colSpan: number): boolean {
    // 3-hour morning labs start at 8:30 AM (period 1) and have 3+ periods
    return startTime === '08:30' && colSpan >= 3;
  }

  /**
   * Get the time slot for period 3 (after tea break)
   */
  private getPeriod3TimeSlot(timeStructure: any): string | null {
    if (!timeStructure) return null;

    // Period 3 typically starts at 10:35 and ends at 11:30
    // This is after the tea break (09:25-10:35)
    return '10:35-11:30';
  }

  /**
   * Extract start time from a time slot string
   */
  private extractStartTime(timeSlot: string): string {
    if (!timeSlot || typeof timeSlot !== 'string') return '';
    const parts = timeSlot.split('-');
    return parts.length > 0 ? parts[0] : '';
  }

  /**
   * 🚨 BREAK DOWN MULTI-PERIOD SLOTS: Convert lab slots into individual theory periods
   * This is CRITICAL - ensures theory slots don't overlap with any part of multi-period labs
   */
  private breakDownMultiPeriodSlot(day: string, timeSlot: string, colSpan: number): string[] {
    const periods: string[] = [];

    // Parse the time slot (e.g., "10:35-12:25")
    const [startTime, endTime] = timeSlot.split('-');

    console.log(`🔧 Breaking down ${day} ${timeSlot} (col_span: ${colSpan}) into individual periods`);

    // Calculate theory class duration (typically 55 minutes)
    const theoryDuration = 55; // minutes

    // Convert start time to minutes
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    let currentTimeMinutes = startHours * 60 + startMinutes;

    // Convert end time to minutes for boundary check
    const [endHours, endMinutes] = endTime.split(':').map(Number);
    const endTimeMinutes = endHours * 60 + endMinutes;

    // Generate individual theory periods that fall within the lab time
    while (currentTimeMinutes < endTimeMinutes) {
      const nextTimeMinutes = currentTimeMinutes + theoryDuration;

      // Convert back to time format
      const currentHours = Math.floor(currentTimeMinutes / 60);
      const currentMins = currentTimeMinutes % 60;
      const nextHours = Math.floor(nextTimeMinutes / 60);
      const nextMins = nextTimeMinutes % 60;

      const currentTimeStr = `${String(currentHours).padStart(2, '0')}:${String(currentMins).padStart(2, '0')}`;
      const nextTimeStr = `${String(nextHours).padStart(2, '0')}:${String(nextMins).padStart(2, '0')}`;

      const periodKey = `${day}-${currentTimeStr}-${nextTimeStr}`;
      periods.push(periodKey);

      console.log(`   📅 Individual period: ${periodKey}`);

      // Move to next period
      currentTimeMinutes = nextTimeMinutes;

      // Safety check to prevent infinite loop
      if (periods.length > 10) {
        console.warn(`⚠️ Breaking loop - too many periods generated for ${timeSlot}`);
        break;
      }
    }

    return periods;
  }

  /**
   * 🔄 INTELLIGENT SLOT SWAPPING: Find opportunities to swap existing slots
   * This is advanced conflict resolution - moving existing slots to make room
   */
  private async findSlotSwapOpportunity(
    mapping: any,
    classOccupiedSlots: Set<string>,
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure
  ): Promise<{ day: string; timeSlot: string } | null> {
    // For now, return null to avoid complexity
    // This can be enhanced later with actual swapping logic
    console.log(`🔄 Slot swapping not implemented yet for ${mapping.subject_code}`);
    return null;
  }

  /**
   * 🔍 CROSS-SEMESTER CONFLICT CHECK: Verify faculty is not already committed
   * This is the key intelligence - checking actual database slots, not stale availability data
   * 🚨 CRITICAL FIX: Properly distinguish between primary and secondary faculty roles
   */
  private async checkCrossSemesterConflict(
    facultyId: string,
    day: string,
    timeSlot: string,
    params: TheorySlotGenerationParams
  ): Promise<boolean> {
    console.log(`🔍 DETAILED CONFLICT CHECK for faculty ${facultyId} on ${day} at ${timeSlot}`);

    // 🚨 CRITICAL: Get faculty details first to verify the faculty name
    const { data: facultyDetails } = await supabase
      .from('employee_details')
      .select('full_name')
      .eq('id', facultyId)
      .single();

    const targetFacultyName = facultyDetails?.full_name || 'Unknown Faculty';
    console.log(`🔍 Target Faculty: ${targetFacultyName} (ID: ${facultyId})`);

    // Check for ANY existing commitment for this faculty at this time across ALL semesters/sections
    const { data: existingCommitments, error } = await supabase
      .from('timetable_slots')
      .select('semester, section, subject_code, subject_type, faculty_id, faculty2_id, faculty_name, faculty2_name, id')
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`)
      .eq('day', day)
      .eq('time_slot', timeSlot);

    if (error) {
      console.error('❌ Error checking cross-semester conflicts:', error);
      return false; // Assume no conflict if we can't check
    }

    if (existingCommitments && existingCommitments.length > 0) {
      console.log(`🔍 Found ${existingCommitments.length} potential conflicts for faculty ${facultyId}:`);

      // 🚨 CRITICAL ANALYSIS: Check each commitment to determine if it's a real conflict
      let hasRealConflict = false;

      existingCommitments.forEach(commitment => {
        const isPrimaryFaculty = commitment.faculty_id === facultyId;
        const isSecondaryFaculty = commitment.faculty2_id === facultyId;

        console.log(`   📋 ANALYZING: ${commitment.semester}${commitment.section}: ${commitment.subject_code} (${commitment.subject_type}) [Slot ID: ${commitment.id}]`);
        console.log(`      Primary Faculty: ${commitment.faculty_name} (ID: ${commitment.faculty_id})`);
        console.log(`      Secondary Faculty: ${commitment.faculty2_name || 'None'} (ID: ${commitment.faculty2_id || 'None'})`);
        console.log(`      Target Faculty Role: ${isPrimaryFaculty ? 'PRIMARY' : 'SECONDARY'}`);

        // 🚨 DATA INTEGRITY CHECK: Verify faculty name matches
        if (isPrimaryFaculty && commitment.faculty_name !== targetFacultyName) {
          console.log(`      ⚠️ DATA INTEGRITY ISSUE: Slot shows faculty name '${commitment.faculty_name}' but ID belongs to '${targetFacultyName}'`);
          console.log(`      🔧 RECOMMENDATION: This slot may have incorrect faculty assignment`);
        }

        if (isSecondaryFaculty && commitment.faculty2_name !== targetFacultyName) {
          console.log(`      ⚠️ DATA INTEGRITY ISSUE: Slot shows secondary faculty name '${commitment.faculty2_name}' but ID belongs to '${targetFacultyName}'`);
          console.log(`      🔧 RECOMMENDATION: This slot may have incorrect secondary faculty assignment`);
        }

        // 🧠 INTELLIGENT CONFLICT LOGIC WITH DATA VALIDATION:
        // Only consider it a real conflict if the faculty name actually matches
        const facultyNameMatches = (isPrimaryFaculty && commitment.faculty_name === targetFacultyName) ||
                                  (isSecondaryFaculty && commitment.faculty2_name === targetFacultyName);

        if (!facultyNameMatches) {
          console.log(`      ✅ NO CONFLICT: Faculty name mismatch indicates data integrity issue, not real conflict`);
          return; // Skip this commitment as it's likely a data error
        }

        // For THEORY slots we're trying to place:
        // - Primary faculty conflicts are ALWAYS blocking
        // - Secondary faculty conflicts are ONLY blocking if it's also a theory slot
        // - Lab secondary faculty roles should NOT block theory placement

        if (isPrimaryFaculty) {
          console.log(`      🚫 REAL CONFLICT: Faculty is PRIMARY instructor for ${commitment.subject_type}`);
          hasRealConflict = true;
        } else if (isSecondaryFaculty) {
          if (commitment.subject_type === 'theory') {
            console.log(`      🚫 REAL CONFLICT: Faculty is SECONDARY instructor for theory class`);
            hasRealConflict = true;
          } else {
            console.log(`      ✅ NO CONFLICT: Faculty is only SECONDARY for ${commitment.subject_type}, not blocking theory placement`);
          }
        }
      });

      if (hasRealConflict) {
        console.log(`🚫 FINAL RESULT: Real conflict detected for faculty ${facultyId}`);
        return true;
      } else {
        console.log(`✅ FINAL RESULT: No real conflicts - faculty ${facultyId} can be assigned`);
        return false;
      }
    }

    console.log(`✅ No existing commitments found for faculty ${facultyId} on ${day} at ${timeSlot}`);
    return false; // No conflict
  }

  /**
   * Generate suggestions for resolving allocation conflicts
   */
  private generateSuggestions(
    mapping: any,
    conflicts: Array<{
      day: string;
      timeSlot: string;
      conflictType: 'class_occupied' | 'faculty_unavailable' | 'skill_lab_conflict' | 'cross_class_conflict';
      details: string;
    }>,
    facultyAvailability: Record<string, string[]>,
    classAvailability: Record<string, string[]>
  ): string[] {
    const suggestions: string[] = [];

    // Analyze conflict patterns
    const conflictTypes = new Set(conflicts.map(c => c.conflictType));
    const conflictDays = new Set(conflicts.map(c => c.day));

    // Suggestion 1: Alternative time slots
    const allAvailableDays = Object.keys(classAvailability).filter(day =>
      classAvailability[day].length > 0 && facultyAvailability[day]?.length > 0
    );

    if (allAvailableDays.length > 0) {
      suggestions.push(`Try alternative days: ${allAvailableDays.join(', ')} have both class and faculty availability`);
    }

    // Suggestion 2: Faculty-specific recommendations
    if (conflictTypes.has('faculty_unavailable')) {
      const totalFacultySlots = Object.values(facultyAvailability).flat().length;
      if (totalFacultySlots < mapping.weekly_hours) {
        suggestions.push(`Faculty ${mapping.faculty_name} has only ${totalFacultySlots} available slots but needs ${mapping.weekly_hours}. Consider assigning different faculty or reducing weekly hours.`);
      } else {
        suggestions.push(`Faculty ${mapping.faculty_name} has sufficient total slots (${totalFacultySlots}) but they don't align with class availability. Consider rescheduling other commitments.`);
      }
    }

    // Suggestion 3: Skill lab conflict resolution
    if (conflictTypes.has('skill_lab_conflict')) {
      const blockedDays = Array.from(conflictDays);
      suggestions.push(`Days ${blockedDays.join(', ')} are blocked by skill lab sessions. Focus allocation on other days or reschedule skill labs.`);
    }

    // Suggestion 4: Class scheduling optimization
    if (conflictTypes.has('class_occupied')) {
      suggestions.push(`Multiple subjects competing for same time slots. Consider distributing subjects more evenly across available periods.`);
    }

    // Suggestion 5: Cross-class conflict resolution
    if (conflictTypes.has('cross_class_conflict')) {
      suggestions.push(`Faculty availability doesn't match class needs. Review faculty assignments across all classes to optimize scheduling.`);
    }

    return suggestions;
  }

  /**
   * Generate comprehensive allocation report with detailed slot tracking
   */
  private generateAllocationReport(theoryMappings: any[]): void {
    console.log('\n📊 ===== THEORY SLOT ALLOCATION REPORT =====');
    console.log(`Total subjects processed: ${this.allocationStats.totalSubjects}`);
    console.log(`Successfully allocated: ${this.allocationStats.successfullyAllocated}`);
    console.log(`Partially allocated: ${this.allocationStats.partiallyAllocated}`);
    console.log(`Completely failed: ${this.unallocatedSubjects.length}`);
    console.log(`Success rate: ${((this.allocationStats.successfullyAllocated / this.allocationStats.totalSubjects) * 100).toFixed(1)}%`);

    console.log('\n📈 Conflict Type Breakdown:');
    console.log(`  Class occupied conflicts: ${this.allocationStats.conflictTypes.class_occupied}`);
    console.log(`  Faculty unavailable conflicts: ${this.allocationStats.conflictTypes.faculty_unavailable}`);
    console.log(`  Skill lab conflicts: ${this.allocationStats.conflictTypes.skill_lab_conflict}`);
    console.log(`  Cross-class conflicts: ${this.allocationStats.conflictTypes.cross_class_conflict}`);

    const totalConflicts = Object.values(this.allocationStats.conflictTypes).reduce((a, b) => a + b, 0);
    console.log(`  Total conflicts detected: ${totalConflicts}`);

    console.log('\n📋 Subject Details:');
    let totalExpectedSlots = 0;
    let totalAllocatedSlots = 0;

    theoryMappings.forEach(mapping => {
      const isCompleteFailure = this.unallocatedSubjects.includes(mapping.subject_code);
      const partialAllocation = this.partiallyAllocatedSubjects.find(p => p.subject_code === mapping.subject_code);

      let status = '✅ SUCCESS';
      let allocatedSlots = mapping.weekly_hours;

      if (isCompleteFailure) {
        status = '❌ COMPLETE FAILURE';
        allocatedSlots = 0;
      } else if (partialAllocation) {
        status = '⚠️ PARTIAL';
        allocatedSlots = partialAllocation.allocated_slots;
      }

      const expectedSlots = mapping.weekly_hours;
      totalExpectedSlots += expectedSlots;
      totalAllocatedSlots += allocatedSlots;

      console.log(`  ${status} ${mapping.subject_code} (${mapping.subject_name})`);
      console.log(`    Expected slots: ${expectedSlots}, Allocated: ${allocatedSlots}, Faculty: ${mapping.faculty_name}`);

      if (isCompleteFailure) {
        console.log(`    🚨 MISSING ALL ${expectedSlots} SLOTS FOR ${mapping.subject_code}`);
      } else if (partialAllocation) {
        console.log(`    ⚠️  MISSING ${partialAllocation.missing_slots} SLOTS FOR ${mapping.subject_code}`);
      }
    });

    console.log(`\n📊 SLOT SUMMARY:`);
    console.log(`  Total expected slots: ${totalExpectedSlots}`);
    console.log(`  Total allocated slots: ${totalAllocatedSlots}`);
    console.log(`  Missing slots: ${totalExpectedSlots - totalAllocatedSlots}`);

    if (this.partiallyAllocatedSubjects.length > 0) {
      console.log(`\n⚠️ PARTIALLY ALLOCATED SUBJECTS:`);
      this.partiallyAllocatedSubjects.forEach(partial => {
        console.log(`  ${partial.subject_code}: ${partial.allocated_slots}/${partial.expected_slots} slots (missing ${partial.missing_slots})`);
      });
    }

    if (totalExpectedSlots !== totalAllocatedSlots) {
      console.log(`\n🚨 ALLOCATION MISMATCH DETECTED!`);
      console.log(`  ${totalExpectedSlots - totalAllocatedSlots} theory slots are missing from the timetable`);
      console.log(`  This will result in empty slots in the timetable grid`);

      if (this.unallocatedSubjects.length > 0) {
        console.log(`\n❌ COMPLETELY UNALLOCATED SUBJECTS:`);
        this.unallocatedSubjects.forEach(subjectCode => {
          const mapping = theoryMappings.find(m => m.subject_code === subjectCode);
          if (mapping) {
            console.log(`  ${subjectCode}: 0/${mapping.weekly_hours} slots (${mapping.subject_name})`);
          }
        });
      }
    }

    console.log('============================================\n');
  }

  /**
   * Log detailed conflict analysis for unallocated subjects
   */
  private logDetailedConflictAnalysis(): void {
    console.log('\n🔍 ===== DETAILED CONFLICT ANALYSIS =====');

    this.conflictLog.forEach((entry, index) => {
      console.log(`\n${index + 1}. SUBJECT: ${entry.subject}`);
      console.log('   CONFLICTS DETECTED:');

      entry.conflicts.forEach((conflict, conflictIndex) => {
        console.log(`     ${conflictIndex + 1}. ${conflict.conflictType.toUpperCase()}: ${conflict.details}`);
      });

      console.log('   FACULTY AVAILABILITY:');
      Object.entries(entry.facultyAvailability).forEach(([day, slots]) => {
        console.log(`     ${day}: [${slots.join(', ')}] (${slots.length} slots)`);
      });

      console.log('   SUGGESTED SOLUTIONS:');
      entry.suggestions.forEach((suggestion, suggestionIndex) => {
        console.log(`     ${suggestionIndex + 1}. ${suggestion}`);
      });
    });

    console.log('\n🎯 OVERALL RECOMMENDATIONS:');

    if (this.allocationStats.conflictTypes.skill_lab_conflict > 0) {
      console.log('   • Review skill lab scheduling to free up more class time slots');
    }

    if (this.allocationStats.conflictTypes.faculty_unavailable > 0) {
      console.log('   • Consider redistributing faculty assignments or increasing faculty availability');
    }

    if (this.allocationStats.conflictTypes.class_occupied > 0) {
      console.log('   • Optimize subject distribution across available time periods');
    }

    if (this.allocationStats.conflictTypes.cross_class_conflict > 0) {
      console.log('   • Review faculty commitments across all classes for better alignment');
    }

    console.log('==========================================\n');
  }

  /**
   * 🔍 COMPREHENSIVE TIMETABLE VALIDATION
   * Validates generated timetable against all optimization rules
   */
  private async validateGeneratedTimetable(
    generatedSlots: TimetableSlot[],
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure
  ): Promise<TimetableValidationResult> {
    console.log('🔍 Running comprehensive timetable validation...');

    const result: TimetableValidationResult = {
      isValid: true,
      violations: [],
      warnings: [],
      optimizationSuggestions: []
    };

    // 1. Conflict Prevention Validation
    await this.validateConflictPrevention(generatedSlots, params, result);

    // 2. Successive Period Avoidance Validation
    await this.validateSuccessivePeriodAvoidance(generatedSlots, timeStructure, result);

    // 3. Morning Period Distribution Validation
    await this.validateMorningPeriodDistribution(generatedSlots, timeStructure, result);

    // 4. Gap Prevention Validation
    await this.validateGapPrevention(generatedSlots, params, timeStructure, result);

    // Determine overall validity
    result.isValid = result.violations.filter(v => v.severity === 'critical' || v.severity === 'high').length === 0;

    console.log(`✅ Validation complete: ${result.isValid ? 'PASSED' : 'FAILED'}`);
    console.log(`   Violations: ${result.violations.length}, Warnings: ${result.warnings.length}`);

    return result;
  }

  /**
   * 🚫 CONFLICT PREVENTION VALIDATION
   * Ensures no faculty scheduling conflicts exist
   */
  private async validateConflictPrevention(
    generatedSlots: TimetableSlot[],
    params: TheorySlotGenerationParams,
    result: TimetableValidationResult
  ): Promise<void> {
    if (!this.validationRules.find(r => r.name === 'conflict_prevention')?.enabled) return;

    console.log('🔍 Validating conflict prevention...');

    // Group slots by faculty
    const facultySlots = new Map<string, TimetableSlot[]>();
    generatedSlots.forEach(slot => {
      if (!facultySlots.has(slot.faculty_id)) {
        facultySlots.set(slot.faculty_id, []);
      }
      facultySlots.get(slot.faculty_id)!.push(slot);
    });

    // Check for overlapping time slots for each faculty
    for (const [facultyId, slots] of facultySlots) {
      for (let i = 0; i < slots.length; i++) {
        for (let j = i + 1; j < slots.length; j++) {
          const slot1 = slots[i];
          const slot2 = slots[j];

          if (slot1.day === slot2.day && slot1.time_slot === slot2.time_slot) {
            result.violations.push({
              id: `conflict_${facultyId}_${slot1.day}_${slot1.time_slot}`,
              type: 'conflict_prevention',
              severity: 'critical',
              description: `Faculty ${slot1.faculty_name} has overlapping classes: ${slot1.subject_code} and ${slot2.subject_code} on ${slot1.day} at ${slot1.time_slot}`,
              affectedSlots: [slot1.id, slot2.id],
              facultyId,
              facultyName: slot1.faculty_name,
              day: slot1.day,
              timeSlot: slot1.time_slot,
              suggestedFix: 'Move one of the conflicting classes to a different time slot'
            });
          }
        }
      }
    }
  }

  /**
   * 🔄 SUCCESSIVE PERIOD AVOIDANCE VALIDATION
   * Ensures no subject is scheduled in consecutive periods
   */
  private async validateSuccessivePeriodAvoidance(
    generatedSlots: TimetableSlot[],
    timeStructure: TimeStructure,
    result: TimetableValidationResult
  ): Promise<void> {
    if (!this.validationRules.find(r => r.name === 'successive_period_avoidance')?.enabled) return;

    console.log('🔍 Validating successive period avoidance...');

    // Get time slot order
    const timeSlots = this.getAllTimeSlots(timeStructure);
    const timeSlotOrder = new Map<string, number>();
    timeSlots.forEach((slot, index) => {
      timeSlotOrder.set(slot, index);
    });

    // Group slots by subject and day
    const subjectDaySlots = new Map<string, Map<string, TimetableSlot[]>>();
    generatedSlots.forEach(slot => {
      const key = slot.subject_code;
      if (!subjectDaySlots.has(key)) {
        subjectDaySlots.set(key, new Map());
      }
      if (!subjectDaySlots.get(key)!.has(slot.day)) {
        subjectDaySlots.get(key)!.set(slot.day, []);
      }
      subjectDaySlots.get(key)!.get(slot.day)!.push(slot);
    });

    // Check for consecutive periods
    for (const [subjectCode, daySlots] of subjectDaySlots) {
      for (const [day, slots] of daySlots) {
        if (slots.length > 1) {
          // Sort slots by time order
          const sortedSlots = slots.sort((a, b) =>
            (timeSlotOrder.get(a.time_slot) || 0) - (timeSlotOrder.get(b.time_slot) || 0)
          );

          for (let i = 0; i < sortedSlots.length - 1; i++) {
            const currentOrder = timeSlotOrder.get(sortedSlots[i].time_slot) || 0;
            const nextOrder = timeSlotOrder.get(sortedSlots[i + 1].time_slot) || 0;

            if (nextOrder - currentOrder === 1) {
              result.violations.push({
                id: `successive_${subjectCode}_${day}_${sortedSlots[i].time_slot}`,
                type: 'successive_period',
                severity: 'medium',
                description: `Subject ${subjectCode} has consecutive periods on ${day}: ${sortedSlots[i].time_slot} and ${sortedSlots[i + 1].time_slot}`,
                affectedSlots: [sortedSlots[i].id, sortedSlots[i + 1].id],
                subjectCode,
                day,
                suggestedFix: 'Redistribute periods with adequate spacing between them'
              });
            }
          }
        }
      }
    }
  }

  /**
   * 🌅 MORNING PERIOD DISTRIBUTION VALIDATION
   * Ensures each subject has maximum 1 morning period per week
   */
  private async validateMorningPeriodDistribution(
    generatedSlots: TimetableSlot[],
    timeStructure: TimeStructure,
    result: TimetableValidationResult
  ): Promise<void> {
    if (!this.validationRules.find(r => r.name === 'morning_period_distribution')?.enabled) return;

    console.log('🔍 Validating morning period distribution...');

    // Define morning period (typically Period 1: 08:30-09:25)
    const morningPeriod = '08:30-09:25'; // This should be configurable based on time structure

    // Group slots by subject
    const subjectSlots = new Map<string, TimetableSlot[]>();
    generatedSlots.forEach(slot => {
      if (!subjectSlots.has(slot.subject_code)) {
        subjectSlots.set(slot.subject_code, []);
      }
      subjectSlots.get(slot.subject_code)!.push(slot);
    });

    // Check morning period distribution
    for (const [subjectCode, slots] of subjectSlots) {
      const morningSlots = slots.filter(slot => slot.time_slot === morningPeriod);

      if (morningSlots.length > 1) {
        result.violations.push({
          id: `morning_limit_${subjectCode}`,
          type: 'morning_period_limit',
          severity: 'medium',
          description: `Subject ${subjectCode} has ${morningSlots.length} morning periods (limit: 1 per week)`,
          affectedSlots: morningSlots.map(slot => slot.id),
          subjectCode,
          suggestedFix: 'Redistribute some morning periods to other time slots'
        });
      }
    }
  }

  /**
   * 🕳️ GAP PREVENTION VALIDATION
   * Ensures no vacant periods in student timetables
   */
  private async validateGapPrevention(
    generatedSlots: TimetableSlot[],
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure,
    result: TimetableValidationResult
  ): Promise<void> {
    if (!this.validationRules.find(r => r.name === 'gap_prevention')?.enabled) return;

    console.log('🔍 Validating gap prevention...');

    // Get all existing slots for this class (including labs, electives, etc.)
    const { data: allClassSlots } = await supabase
      .from('timetable_slots')
      .select('day, time_slot, subject_type')
      .eq('academic_year', params.academicYear)
      .eq('department', params.department)
      .eq('semester', params.semester)
      .eq('section', params.section);

    if (!allClassSlots) return;

    // Combine with generated theory slots
    const allSlots = [...allClassSlots, ...generatedSlots.map(slot => ({
      day: slot.day,
      time_slot: slot.time_slot,
      subject_type: slot.subject_type
    }))];

    // Get time slot order
    const timeSlots = this.getAllTimeSlots(timeStructure);
    const timeSlotOrder = new Map<string, number>();
    timeSlots.forEach((slot, index) => {
      timeSlotOrder.set(slot, index);
    });

    // Check for gaps in each day
    const days = timeStructure.working_days;
    for (const day of days) {
      const daySlots = allSlots
        .filter(slot => slot.day === day)
        .map(slot => slot.time_slot)
        .sort((a, b) => (timeSlotOrder.get(a) || 0) - (timeSlotOrder.get(b) || 0));

      if (daySlots.length > 1) {
        // Check for gaps between first and last period
        const firstPeriodIndex = timeSlotOrder.get(daySlots[0]) || 0;
        const lastPeriodIndex = timeSlotOrder.get(daySlots[daySlots.length - 1]) || 0;

        for (let i = firstPeriodIndex; i <= lastPeriodIndex; i++) {
          const timeSlot = timeSlots[i];
          if (!daySlots.includes(timeSlot)) {
            result.warnings.push({
              id: `gap_${day}_${timeSlot}`,
              type: 'distribution',
              description: `Vacant period detected on ${day} at ${timeSlot}`,
              affectedSlots: [],
              suggestion: 'Consider filling this gap with a suitable subject or activity'
            });
          }
        }
      }
    }
  }

  /**
   * 🔧 AUTO-RESOLVE VALIDATION ISSUES
   * Attempts to automatically fix validation violations
   */
  private async autoResolveValidationIssues(
    generatedSlots: TimetableSlot[],
    validationResult: TimetableValidationResult,
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure
  ): Promise<TimetableSlot[]> {
    console.log('🔧 Attempting auto-resolution of validation issues...');

    let resolvedSlots = [...generatedSlots];
    let resolutionCount = 0;

    // Resolve critical conflicts first
    const criticalViolations = validationResult.violations.filter(v => v.severity === 'critical');

    for (const violation of criticalViolations) {
      if (violation.type === 'conflict_prevention') {
        const resolved = await this.resolveConflictViolation(violation, resolvedSlots, params, timeStructure);
        if (resolved) {
          resolvedSlots = resolved;
          resolutionCount++;
        }
      }
    }

    // Resolve successive period violations
    const successiveViolations = validationResult.violations.filter(v => v.type === 'successive_period');

    for (const violation of successiveViolations) {
      const resolved = await this.resolveSuccessivePeriodViolation(violation, resolvedSlots, params, timeStructure);
      if (resolved) {
        resolvedSlots = resolved;
        resolutionCount++;
      }
    }

    console.log(`✅ Auto-resolved ${resolutionCount} validation issues`);
    return resolvedSlots;
  }

  /**
   * 🚫 RESOLVE CONFLICT VIOLATION
   * Attempts to resolve faculty scheduling conflicts
   */
  private async resolveConflictViolation(
    violation: ValidationViolation,
    slots: TimetableSlot[],
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure
  ): Promise<TimetableSlot[] | null> {
    console.log(`🔧 Resolving conflict violation: ${violation.description}`);

    // Find alternative time slots for one of the conflicting classes
    const conflictingSlots = slots.filter(slot => violation.affectedSlots.includes(slot.id));

    if (conflictingSlots.length !== 2) return null;

    // Try to move the second slot to an available time
    const slotToMove = conflictingSlots[1];
    const alternativeSlot = await this.findAlternativeSlotIntelligently(
      {
        subject_code: slotToMove.subject_code,
        subject_name: slotToMove.subject_name,
        faculty_id: slotToMove.faculty_id,
        faculty_name: slotToMove.faculty_name,
        weekly_hours: 1
      },
      new Set(slots.map(s => `${s.day}-${s.time_slot}`)),
      params,
      timeStructure
    );

    if (alternativeSlot) {
      // Update the slot with new time
      const updatedSlots = slots.map(slot =>
        slot.id === slotToMove.id
          ? { ...slot, day: alternativeSlot.day, time_slot: alternativeSlot.timeSlot }
          : slot
      );

      console.log(`✅ Moved ${slotToMove.subject_code} from ${slotToMove.day} ${slotToMove.time_slot} to ${alternativeSlot.day} ${alternativeSlot.timeSlot}`);
      return updatedSlots;
    }

    return null;
  }

  /**
   * 🔄 RESOLVE SUCCESSIVE PERIOD VIOLATION
   * Attempts to resolve consecutive period scheduling
   */
  private async resolveSuccessivePeriodViolation(
    violation: ValidationViolation,
    slots: TimetableSlot[],
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure
  ): Promise<TimetableSlot[] | null> {
    console.log(`🔧 Resolving successive period violation: ${violation.description}`);

    // Find alternative time slot for one of the consecutive periods
    const consecutiveSlots = slots.filter(slot => violation.affectedSlots.includes(slot.id));

    if (consecutiveSlots.length !== 2) return null;

    // Try to move the second slot to a non-consecutive time
    const slotToMove = consecutiveSlots[1];
    const alternativeSlot = await this.findNonConsecutiveSlot(
      slotToMove,
      consecutiveSlots[0],
      slots,
      params,
      timeStructure
    );

    if (alternativeSlot) {
      const updatedSlots = slots.map(slot =>
        slot.id === slotToMove.id
          ? { ...slot, day: alternativeSlot.day, time_slot: alternativeSlot.timeSlot }
          : slot
      );

      console.log(`✅ Moved ${slotToMove.subject_code} to avoid consecutive periods`);
      return updatedSlots;
    }

    return null;
  }

  /**
   * 🔍 FIND NON-CONSECUTIVE SLOT
   * Finds a time slot that doesn't create consecutive periods
   */
  private async findNonConsecutiveSlot(
    slotToMove: TimetableSlot,
    fixedSlot: TimetableSlot,
    allSlots: TimetableSlot[],
    params: TheorySlotGenerationParams,
    timeStructure: TimeStructure
  ): Promise<{ day: string; timeSlot: string } | null> {
    const timeSlots = this.getAllTimeSlots(timeStructure);
    const timeSlotOrder = new Map<string, number>();
    timeSlots.forEach((slot, index) => {
      timeSlotOrder.set(slot, index);
    });

    const occupiedSlots = new Set(allSlots.map(s => `${s.day}-${s.time_slot}`));
    const fixedSlotOrder = timeSlotOrder.get(fixedSlot.time_slot) || 0;

    // Try all available slots
    for (const day of timeStructure.working_days) {
      for (const timeSlot of timeSlots) {
        const slotKey = `${day}-${timeSlot}`;

        if (!occupiedSlots.has(slotKey)) {
          // Check if this slot is non-consecutive with the fixed slot
          if (day !== fixedSlot.day) {
            // Different day is always non-consecutive
            const hasConflict = await this.checkCrossSemesterConflict(
              slotToMove.faculty_id,
              day,
              timeSlot,
              params
            );

            if (!hasConflict) {
              return { day, timeSlot };
            }
          } else {
            // Same day - check if periods are not consecutive
            const currentSlotOrder = timeSlotOrder.get(timeSlot) || 0;
            const orderDiff = Math.abs(currentSlotOrder - fixedSlotOrder);

            if (orderDiff > 1) {
              const hasConflict = await this.checkCrossSemesterConflict(
                slotToMove.faculty_id,
                day,
                timeSlot,
                params
              );

              if (!hasConflict) {
                return { day, timeSlot };
              }
            }
          }
        }
      }
    }

    return null;
  }

  /**
   * 🔍 VALIDATE SLOT PLACEMENT
   * Validates a potential slot placement against all validation rules
   */
  private async validateSlotPlacement(
    mapping: TheoryMapping,
    day: string,
    timeSlot: string,
    existingSlots: TimetableSlot[],
    timeStructure: TimeStructure
  ): Promise<boolean> {
    // 1. Check successive period avoidance
    if (this.validationRules.find(r => r.name === 'successive_period_avoidance')?.enabled) {
      if (!this.checkSuccessivePeriodRule(mapping.subject_code, day, timeSlot, existingSlots, timeStructure)) {
        console.log(`     🚫 SUCCESSIVE PERIOD VIOLATION: ${mapping.subject_code} would create consecutive periods`);
        return false;
      }
    }

    // 2. Check morning period distribution
    if (this.validationRules.find(r => r.name === 'morning_period_distribution')?.enabled) {
      if (!this.checkMorningPeriodRule(mapping.subject_code, timeSlot, existingSlots)) {
        console.log(`     🚫 MORNING PERIOD VIOLATION: ${mapping.subject_code} already has morning period this week`);
        return false;
      }
    }

    // 3. Additional validation rules can be added here

    return true;
  }

  /**
   * 🔄 CHECK SUCCESSIVE PERIOD RULE
   * Ensures no consecutive periods for the same subject
   */
  private checkSuccessivePeriodRule(
    subjectCode: string,
    day: string,
    timeSlot: string,
    existingSlots: TimetableSlot[],
    timeStructure: TimeStructure
  ): boolean {
    const timeSlots = this.getAllTimeSlots(timeStructure);
    const timeSlotOrder = new Map<string, number>();
    timeSlots.forEach((slot, index) => {
      timeSlotOrder.set(slot, index);
    });

    const currentOrder = timeSlotOrder.get(timeSlot) || 0;

    // Check existing slots for the same subject on the same day
    const sameDaySlots = existingSlots.filter(slot =>
      slot.subject_code === subjectCode && slot.day === day
    );

    for (const existingSlot of sameDaySlots) {
      const existingOrder = timeSlotOrder.get(existingSlot.time_slot) || 0;
      const orderDiff = Math.abs(currentOrder - existingOrder);

      if (orderDiff === 1) {
        // Consecutive periods detected
        return false;
      }
    }

    return true;
  }

  /**
   * 🌅 CHECK MORNING PERIOD RULE
   * Ensures maximum 1 morning period per subject per week
   */
  private checkMorningPeriodRule(
    subjectCode: string,
    timeSlot: string,
    existingSlots: TimetableSlot[]
  ): boolean {
    const morningPeriod = '08:30-09:25'; // This should be configurable

    // If this is not a morning period, allow it
    if (timeSlot !== morningPeriod) {
      return true;
    }

    // Check if subject already has a morning period this week
    const existingMorningSlots = existingSlots.filter(slot =>
      slot.subject_code === subjectCode && slot.time_slot === morningPeriod
    );

    return existingMorningSlots.length === 0;
  }
}
