import { useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface UseAttendanceRealtimeProps {
  facultyId: string;
  subjectCode: string;
  semester: string;
  section: string;
  subjectType: string;
  batchName?: string;
  onAttendanceUpdate: () => void;
  enabled: boolean;
}

/**
 * Hook to listen for real-time attendance updates
 * Automatically refreshes attendance sheet when new attendance is marked
 */
export const useAttendanceRealtime = ({
  facultyId,
  subjectCode,
  semester,
  section,
  subjectType,
  batchName,
  onAttendanceUpdate,
  enabled
}: UseAttendanceRealtimeProps) => {
  const subscriptionRef = useRef<any>(null);

  useEffect(() => {
    if (!enabled || !facultyId || !subjectCode) {
      return;
    }

    // Clean up existing subscription
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
    }

    // Create new subscription for attendance changes
    const subscription = supabase
      .channel(`attendance_updates_${facultyId}_${subjectCode}_${semester}_${section}_${subjectType}_${batchName || 'no-batch'}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'attendance',
          filter: `faculty_id=eq.${facultyId}`
        },
        (payload) => {
          console.log('Real-time attendance update received:', payload);
          
          // Check if the update is for the current subject/section/semester
          const record = payload.new || payload.old;
          if (
            record &&
            record.subject_code === subjectCode &&
            record.semester === semester &&
            record.section === section
          ) {
            // Trigger refresh with a small delay to ensure database consistency
            setTimeout(() => {
              onAttendanceUpdate();
            }, 1000);
          }
        }
      )
      .subscribe((status) => {
        console.log('Attendance realtime subscription status:', status);
      });

    subscriptionRef.current = subscription;

    // Cleanup function
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
    };
  }, [facultyId, subjectCode, semester, section, subjectType, batchName, onAttendanceUpdate, enabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, []);
};
