import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { 
  SemesterLabConfigurationService, 
  SemesterLabConfiguration,
  LabTimeSlotDefinition 
} from "@/services/SemesterLabConfigurationService";
import { Clock, Settings, Calendar, AlertCircle } from "lucide-react";

const formSchema = z.object({
  academicYear: z.string().min(1, "Academic year is required"),
  department: z.string().min(1, "Department is required"),
  semester: z.string().min(1, "Semester is required"),
  defaultLabDuration: z.coerce.number().min(2).max(3),
  labDurationType: z.enum(['two_hour', 'three_hour']),
  enableMorningSession: z.boolean(),
  enableMidSession: z.boolean(),
  enableAfternoonSession: z.boolean(),
  enableEarlyAfternoonSession: z.boolean(),
  skillLabRequired: z.boolean(),
  skillLabPlacementPreference: z.enum(['vacant_day', 'least_labs_day', 'specific_day']),
  skillLabPreferredDay: z.string().optional(),
  skillLabDuration: z.coerce.number().min(2).max(3),
  maxLabsPerDay: z.coerce.number().min(1).max(4),
  allowConsecutiveLabs: z.boolean(),
  preferLabDistribution: z.boolean(),
});

type FormValues = z.infer<typeof formSchema>;

interface SemesterLabConfigurationFormProps {
  onSave?: (config: SemesterLabConfiguration) => void;
  initialData?: {
    academicYear: string;
    department: string;
    semester?: string;
  };
}

export const SemesterLabConfigurationForm: React.FC<SemesterLabConfigurationFormProps> = ({
  onSave,
  initialData
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<LabTimeSlotDefinition[]>([]);
  const [existingConfig, setExistingConfig] = useState<SemesterLabConfiguration | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      academicYear: initialData?.academicYear || "2024-2025",
      department: initialData?.department || "cse",
      semester: initialData?.semester || "",
      defaultLabDuration: 3,
      labDurationType: 'three_hour',
      enableMorningSession: true,
      enableMidSession: false,
      enableAfternoonSession: true,
      enableEarlyAfternoonSession: false,
      skillLabRequired: true,
      skillLabPlacementPreference: 'vacant_day',
      skillLabPreferredDay: '',
      skillLabDuration: 3,
      maxLabsPerDay: 1,
      allowConsecutiveLabs: false,
      preferLabDistribution: true,
    },
  });

  const watchedValues = form.watch();

  // Update available time slots when lab duration changes
  useEffect(() => {
    const mockConfig: SemesterLabConfiguration = {
      ...SemesterLabConfigurationService.getDefaultConfiguration(
        watchedValues.academicYear,
        watchedValues.department,
        watchedValues.semester
      ),
      default_lab_duration: watchedValues.defaultLabDuration,
      lab_duration_type: watchedValues.labDurationType,
      enable_morning_session: watchedValues.enableMorningSession,
      enable_mid_session: watchedValues.enableMidSession,
      enable_afternoon_session: watchedValues.enableAfternoonSession,
      enable_early_afternoon_session: watchedValues.enableEarlyAfternoonSession,
    };

    const slots = SemesterLabConfigurationService.getAvailableTimeSlots(mockConfig);
    setAvailableTimeSlots(slots);
  }, [
    watchedValues.defaultLabDuration,
    watchedValues.labDurationType,
    watchedValues.enableMorningSession,
    watchedValues.enableMidSession,
    watchedValues.enableAfternoonSession,
    watchedValues.enableEarlyAfternoonSession,
    watchedValues.academicYear,
    watchedValues.department,
    watchedValues.semester
  ]);

  // Load existing configuration when semester changes
  useEffect(() => {
    if (watchedValues.academicYear && watchedValues.department && watchedValues.semester) {
      loadExistingConfiguration();
    }
  }, [watchedValues.academicYear, watchedValues.department, watchedValues.semester]);

  const loadExistingConfiguration = async () => {
    try {
      const config = await SemesterLabConfigurationService.getLabConfiguration(
        watchedValues.academicYear,
        watchedValues.department,
        watchedValues.semester
      );

      if (config && config.id !== `default-${watchedValues.academicYear}-${watchedValues.department}-${watchedValues.semester}`) {
        setExistingConfig(config);
        
        // Update form with existing values
        form.reset({
          academicYear: config.academic_year,
          department: config.department,
          semester: config.semester,
          defaultLabDuration: config.default_lab_duration,
          labDurationType: config.lab_duration_type as 'two_hour' | 'three_hour',
          enableMorningSession: config.enable_morning_session || false,
          enableMidSession: config.enable_mid_session || false,
          enableAfternoonSession: config.enable_afternoon_session || false,
          enableEarlyAfternoonSession: config.enable_early_afternoon_session || false,
          skillLabRequired: config.skill_lab_required || false,
          skillLabPlacementPreference: config.skill_lab_placement_preference as any || 'vacant_day',
          skillLabPreferredDay: config.skill_lab_preferred_day || '',
          skillLabDuration: config.skill_lab_duration || 3,
          maxLabsPerDay: config.max_labs_per_day || 1,
          allowConsecutiveLabs: config.allow_consecutive_labs || false,
          preferLabDistribution: config.prefer_lab_distribution || false,
        });
      } else {
        setExistingConfig(null);
      }
    } catch (error) {
      console.error('Error loading configuration:', error);
    }
  };

  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);
    try {
      const configData = {
        academic_year: values.academicYear,
        department: values.department,
        semester: values.semester,
        default_lab_duration: values.defaultLabDuration,
        lab_duration_type: values.labDurationType,
        enable_morning_session: values.enableMorningSession,
        enable_mid_session: values.enableMidSession,
        enable_afternoon_session: values.enableAfternoonSession,
        enable_early_afternoon_session: values.enableEarlyAfternoonSession,
        skill_lab_required: values.skillLabRequired,
        skill_lab_placement_preference: values.skillLabPlacementPreference,
        skill_lab_preferred_day: values.skillLabPreferredDay || null,
        skill_lab_duration: values.skillLabDuration,
        max_labs_per_day: values.maxLabsPerDay,
        allow_consecutive_labs: values.allowConsecutiveLabs,
        prefer_lab_distribution: values.preferLabDistribution,
      };

      const savedConfig = await SemesterLabConfigurationService.saveLabConfiguration(configData);
      
      toast({
        title: "Configuration Saved",
        description: `Lab configuration for ${values.semester} semester has been saved successfully.`,
      });

      if (onSave) {
        onSave(savedConfig);
      }

      setExistingConfig(savedConfig);
    } catch (error) {
      console.error('Error saving configuration:', error);
      toast({
        title: "Error",
        description: "Failed to save lab configuration. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Semester Lab Configuration
        </CardTitle>
        <CardDescription>
          Configure lab duration and scheduling preferences for each semester
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="academicYear"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Academic Year</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select academic year" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="2023-2024">2023-2024</SelectItem>
                        <SelectItem value="2024-2025">2024-2025</SelectItem>
                        <SelectItem value="2025-2026">2025-2026</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="cse">Computer Science</SelectItem>
                        <SelectItem value="ece">Electronics & Communication</SelectItem>
                        <SelectItem value="mech">Mechanical</SelectItem>
                        <SelectItem value="eee">Electrical & Electronics</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="semester"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Semester</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select semester" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {[1, 2, 3, 4, 5, 6, 7, 8].map((sem) => (
                          <SelectItem key={sem} value={sem.toString()}>
                            {sem} Semester
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {existingConfig && (
              <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-800">
                  Existing configuration found. Modify the settings below to update.
                </span>
              </div>
            )}

            <Separator />

            {/* Lab Duration Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Lab Duration Settings
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="defaultLabDuration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Default Lab Duration (Hours)</FormLabel>
                      <Select 
                        value={field.value.toString()} 
                        onValueChange={(value) => {
                          const duration = parseInt(value);
                          field.onChange(duration);
                          form.setValue('labDurationType', duration === 2 ? 'two_hour' : 'three_hour');
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="2">2 Hours</SelectItem>
                          <SelectItem value="3">3 Hours</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose between 2-hour or 3-hour lab sessions
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxLabsPerDay"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maximum Labs Per Day</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          max={4}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Maximum number of lab sessions allowed per day
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Available Time Slots Preview */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Available Time Slots
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableTimeSlots.map((slot) => (
                  <div key={slot.id} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{slot.name}</h4>
                      <Badge variant={slot.enabled ? "default" : "secondary"}>
                        {slot.duration}h
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{slot.timeRange}</p>
                    <p className="text-xs text-muted-foreground/80">{slot.periods}</p>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Configuration"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
