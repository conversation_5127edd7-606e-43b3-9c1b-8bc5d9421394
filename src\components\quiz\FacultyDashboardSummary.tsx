import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BookOpen, 
  Users, 
  GraduationCap, 
  FileText, 
  Brain,
  CheckCircle,
  Clock,
  Target
} from 'lucide-react';
import { useFacultyContext } from '@/hooks/useFacultyContext';

const FacultyDashboardSummary: React.FC = () => {
  const { context, dashboardStats, loading, error } = useFacultyContext();

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-32 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load faculty context: {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!context || !dashboardStats) {
    return (
      <Alert>
        <AlertDescription>
          No teaching assignments found. Please contact administration if this is incorrect.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Faculty Overview Card */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <GraduationCap className="h-5 w-5" />
            Faculty Teaching Overview
          </CardTitle>
          <CardDescription className="text-blue-600">
            Welcome back, <strong>{context.faculty_name}</strong> | Department: <strong>{context.department}</strong>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-700">{dashboardStats.total_subjects}</div>
              <div className="text-sm text-blue-600">Subjects</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-700">{dashboardStats.total_classes}</div>
              <div className="text-sm text-blue-600">Classes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-700">{dashboardStats.total_students}</div>
              <div className="text-sm text-blue-600">Students</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-700">{dashboardStats.total_quizzes}</div>
              <div className="text-sm text-blue-600">Quizzes Created</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Course Materials</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.total_materials}</div>
            <p className="text-xs text-muted-foreground">
              {dashboardStats.processed_materials} processed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Quizzes</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.active_quizzes}</div>
            <p className="text-xs text-muted-foreground">
              {dashboardStats.total_quizzes - dashboardStats.active_quizzes} in draft
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Status</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardStats.total_materials > 0 
                ? Math.round((dashboardStats.processed_materials / dashboardStats.total_materials) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Materials processed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quiz Coverage</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardStats.total_subjects > 0 
                ? Math.round((dashboardStats.total_quizzes / dashboardStats.total_subjects) * 100) / 100
                : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg quizzes per subject
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Subject Assignments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Your Teaching Assignments
          </CardTitle>
          <CardDescription>
            Subjects and classes assigned to you for the current academic year
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(context.subjects_summary).map(([subjectCode, subjectInfo]) => (
              <div key={subjectCode} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-semibold text-lg">{subjectCode}</h4>
                    <p className="text-muted-foreground">{subjectInfo.subject_name}</p>
                  </div>
                  <Badge variant="outline">
                    {subjectInfo.classes.length} {subjectInfo.classes.length === 1 ? 'Class' : 'Classes'}
                  </Badge>
                </div>
                
                <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                  {subjectInfo.classes.map((classInfo, index) => {
                    const assignment = context.assignments.find(a => 
                      a.subject_code === subjectCode &&
                      a.department === classInfo.department &&
                      a.semester === classInfo.semester &&
                      a.section === classInfo.section
                    );

                    return (
                      <div key={index} className="p-3 bg-gray-50 rounded border">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">
                              {classInfo.department} - Sem {classInfo.semester}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Section {classInfo.section} | {classInfo.academic_year}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              <span className="text-sm font-medium">
                                {assignment?.total_students || 0}
                              </span>
                            </div>
                            {assignment?.faculty_role === 'faculty_1' ? (
                              <Badge variant="default" className="text-xs">Primary</Badge>
                            ) : (
                              <Badge variant="secondary" className="text-xs">Secondary</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Get started with AI-powered quiz management for your subjects
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-3">
            <div className="p-3 border rounded-lg text-center hover:bg-gray-50 cursor-pointer">
              <FileText className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <p className="font-medium">Upload Materials</p>
              <p className="text-xs text-muted-foreground">Add syllabus and textbooks</p>
            </div>
            
            <div className="p-3 border rounded-lg text-center hover:bg-gray-50 cursor-pointer">
              <Brain className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <p className="font-medium">Generate Quiz</p>
              <p className="text-xs text-muted-foreground">AI-powered question creation</p>
            </div>
            
            <div className="p-3 border rounded-lg text-center hover:bg-gray-50 cursor-pointer">
              <Target className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <p className="font-medium">Schedule Quiz</p>
              <p className="text-xs text-muted-foreground">Set up student assessments</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FacultyDashboardSummary;
