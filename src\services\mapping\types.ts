
// src/services/mapping/types.ts
export interface MappingRow {
  academic_year:   string;
  department:      string;
  semester:        string;
  section:         string;
  subject_code:    string;
  subject_name:    string;
  subject_type:    "theory" | "laboratory" | "elective";
  faculty1:        string;
  faculty2?:       string;
  hours_per_week:  number;
  classroom:       string;
  slots_per_week?: number;
}

export interface LabSlotRow {
  parentRowIndex: number;
  slot_order:     number;
  batch_name:     string;
  day:            string;
  time_of_day:    string;
}

export interface ManualMappingInput {
  academic_year:  string;
  department:     string;
  semester:       string;
  section:        string;
  subject_id:     string;
  subject_code:   string;
  subject_name:   string;
  subject_type:   "theory" | "laboratory" | "elective";
  faculty_1_id:   string;
  faculty_2_id?:  string | null;
  hours_per_week: number;
  classroom:      string;
  slots_per_week?: number; // Add this field
}

export interface LabSlotInput {
  batch_name:    string;
  day:           string;
  time_of_day:   string;
}

export interface ImportResult {
  imported: number;
  skipped: number;
  reasons?: string[];
}
