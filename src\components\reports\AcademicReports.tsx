import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { BarChart3, Building2, AlertCircle, Download, Calendar } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useAuth } from '@/contexts/AuthContext';
import { Input } from '@/components/ui/input';
import { defaultFilterOptions } from '@/components/subjects/AllotmentFilterOptions';

const AcademicReports: React.FC = () => {
  const [academicYear, setAcademicYear] = useState<string>('');
  const [semester, setSemester] = useState<string>('');
  const [section, setSection] = useState<string>('');
  const [reportType, setReportType] = useState<string>('');
  const [fromDate, setFromDate] = useState<string>('');
  const [toDate, setToDate] = useState<string>('');
  const [attendanceFilter, setAttendanceFilter] = useState<string>('all');
  const [iaFilter, setIaFilter] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Get user's department information
  const { department, departmentName, fullName, loading: departmentLoading, error: departmentError } = useUserDepartment();

  // Get filter options
  const filterOptions = defaultFilterOptions;

  const generateReport = async () => {
    if (!reportType || !semester || !section) {
      toast({
        title: 'Missing Information',
        description: 'Please select report type, semester, and section.',
        variant: 'default',
      });
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement report generation based on reportType
      toast({
        title: 'Feature Coming Soon',
        description: 'Academic reports functionality will be implemented in the next phase.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate report.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while fetching department info
  if (departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading department information...</span>
        </div>
      </div>
    );
  }

  // Show error if department info couldn't be loaded
  if (departmentError || !department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {departmentError || 'Unable to load your department information. Please contact administrator to set your department.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <BarChart3 className="h-6 w-6 text-primary" />
        <h1 className="text-3xl font-bold">Academic Reports</h1>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Class Teacher:</strong> {fullName} | <strong>Department:</strong> {departmentName}
        </AlertDescription>
      </Alert>

      {/* Report Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Generate Academic Reports</CardTitle>
          <CardDescription>
            Generate comprehensive attendance and IA reports for your assigned class.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Academic Year</label>
              <Select value={academicYear} onValueChange={setAcademicYear}>
                <SelectTrigger>
                  <SelectValue placeholder="Select academic year" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.yearsList.map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Semester</label>
              <Select value={semester} onValueChange={setSemester}>
                <SelectTrigger>
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.semsList.map((sem) => (
                    <SelectItem key={sem} value={sem}>
                      Semester {sem}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Section</label>
              <Select value={section} onValueChange={setSection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.sectionsList.map((sec) => (
                    <SelectItem key={sec} value={sec}>
                      Section {sec}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Report Type</label>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger>
                <SelectValue placeholder="Select report type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="attendance">Attendance Report</SelectItem>
                <SelectItem value="ia">Internal Assessment Report</SelectItem>
                <SelectItem value="consolidated">Consolidated Academic Report</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {reportType === 'attendance' && (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
              <h4 className="font-medium">Attendance Report Options</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">From Date</label>
                  <Input
                    type="date"
                    value={fromDate}
                    onChange={(e) => setFromDate(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">To Date</label>
                  <Input
                    type="date"
                    value={toDate}
                    onChange={(e) => setToDate(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Attendance Filter</label>
                <Select value={attendanceFilter} onValueChange={setAttendanceFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Students</SelectItem>
                    <SelectItem value="low">Low Attendance (&lt;50%)</SelectItem>
                    <SelectItem value="medium">Medium Attendance (50-75%)</SelectItem>
                    <SelectItem value="high">High Attendance (&gt;75%)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {reportType === 'ia' && (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
              <h4 className="font-medium">IA Report Options</h4>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Score Filter</label>
                <Select value={iaFilter} onValueChange={setIaFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Students</SelectItem>
                    <SelectItem value="low">Low Scores (&lt;15)</SelectItem>
                    <SelectItem value="medium">Medium Scores (15-25)</SelectItem>
                    <SelectItem value="high">High Scores (&gt;25)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <div className="flex gap-2">
            <Button
              onClick={generateReport}
              disabled={loading || !reportType || !semester || !section}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                  Generating...
                </div>
              ) : (
                <>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Generate Report
                </>
              )}
            </Button>

            <Button variant="outline" disabled>
              <Download className="h-4 w-4 mr-2" />
              Export PDF
            </Button>

            <Button variant="outline" disabled>
              <Download className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Coming Soon Notice */}
      <Card>
        <CardContent className="py-8">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Academic Reports Coming Soon</h3>
            <p className="text-gray-600 mb-4">
              Comprehensive academic reporting features including attendance analysis, IA performance tracking, 
              and consolidated academic reports will be available in the next release.
            </p>
            <div className="flex flex-wrap justify-center gap-2">
              <Badge variant="outline">Attendance Analytics</Badge>
              <Badge variant="outline">IA Performance Tracking</Badge>
              <Badge variant="outline">Student Progress Reports</Badge>
              <Badge variant="outline">Parent Communication</Badge>
              <Badge variant="outline">Export Capabilities</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AcademicReports;
