import { useState, useEffect } from 'react';
import { AttendanceAccessControlService, AttendanceAccessResult } from '@/services/AttendanceAccessControlService';

export interface UseAttendanceAccessControlProps {
  facultyId: string;
  classInfo: {
    subject_code: string;
    subject_type: string;
    semester: string;
    section: string;
    department: string;
    batch_name?: string;
    time_slot?: string;
  };
  date: string;
  enabled?: boolean;
}

export interface UseAttendanceAccessControlResult {
  accessResult: AttendanceAccessResult | null;
  loading: boolean;
  error: string | null;
  recheckAccess: () => Promise<void>;
}

/**
 * Hook to check attendance access control for substitute faculty assignments
 */
export function useAttendanceAccessControl({
  facultyId,
  classInfo,
  date,
  enabled = true
}: UseAttendanceAccessControlProps): UseAttendanceAccessControlResult {
  const [accessResult, setAccessResult] = useState<AttendanceAccessResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkAccess = async () => {
    if (!enabled || !facultyId || !classInfo || !date) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('🔐 HOOK: Checking attendance access control');
      const result = await AttendanceAccessControlService.checkAttendanceAccess(
        facultyId,
        classInfo,
        date
      );

      setAccessResult(result);
      console.log('🔐 HOOK: Access control result:', result);

    } catch (err) {
      console.error('🔐 HOOK: Error checking access control:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      // Fallback to allow access in case of error
      setAccessResult({
        canAccess: true,
        reason: 'error_fallback'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAccess();
  }, [facultyId, classInfo.subject_code, classInfo.semester, classInfo.section, 
      classInfo.time_slot, date, enabled]);

  return {
    accessResult,
    loading,
    error,
    recheckAccess: checkAccess
  };
}

/**
 * Hook to get substitute assignments for a faculty member
 */
export function useSubstituteAssignments(facultyId: string, date: string) {
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssignments = async () => {
      if (!facultyId || !date) return;

      try {
        setLoading(true);
        setError(null);

        const result = await AttendanceAccessControlService.getSubstituteAssignmentsForFaculty(
          facultyId,
          date
        );

        setAssignments(result);
      } catch (err) {
        console.error('Error fetching substitute assignments:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchAssignments();
  }, [facultyId, date]);

  return {
    assignments,
    loading,
    error
  };
}
