
import React from "react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";
import { MappingFormData } from "@/hooks/useSubjectMappingForm";

interface BasicInfoFieldsProps {
  form: UseFormReturn<MappingFormData>;
  disabled?: boolean;
}

export default function BasicInfoFields({ form, disabled = false }: BasicInfoFieldsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="hoursPerWeek"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Hours Per Week</FormLabel>
            <FormControl>
              <Input
                type="number"
                min={1}
                max={15}
                disabled={disabled}
                {...field}
              />
            </FormControl>
            <FormDescription>
              Total hours per week for this subject
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="classroom"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Classroom</FormLabel>
            <FormControl>
              <Input
                placeholder="e.g., 301, Lab 2"
                disabled={disabled}
                {...field}
              />
            </FormControl>
            <FormDescription>
              Classroom or lab where the subject is taught
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
