
import React, { useState, useEffect, useRef } from "react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Check, ChevronsUpDown, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useDebounce } from "@/hooks/use-debounce";

export interface Faculty {
  id: string;
  name: string;
}

interface FacultyAutocompleteProps {
  faculties: Faculty[];
  selectedFaculty: Faculty | null;
  onSelect: (faculty: Faculty) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function FacultyAutocomplete({
  faculties,
  selectedFaculty,
  onSelect,
  placeholder = "Select faculty...",
  className,
  disabled = false,
}: FacultyAutocompleteProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 300);
  const popoverRef = useRef<HTMLDivElement>(null);

  // Ensure faculties is always an array
  const safeFaculties = Array.isArray(faculties) ? faculties : [];

  // Filter faculties based on search - handle null/undefined safely
  const filteredFaculties = safeFaculties.filter((faculty) =>
    faculty?.name?.toLowerCase().includes(debouncedSearch.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          disabled={disabled}
        >
          {selectedFaculty?.name ? selectedFaculty.name : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[280px]" ref={popoverRef} align="start" side="bottom">
        <Command shouldFilter={false}>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <CommandInput 
              placeholder="Search faculties..." 
              value={search}
              onValueChange={setSearch}
              className="h-9"
            />
          </div>
          <CommandList className="max-h-[300px] overflow-y-auto">
            <CommandEmpty>No faculty found.</CommandEmpty>
            <CommandGroup>
              {filteredFaculties.map((faculty) => (
                <CommandItem
                  key={faculty.id}
                  value={faculty.name}
                  onSelect={() => {
                    onSelect(faculty);
                    setOpen(false);
                    setSearch("");
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedFaculty?.id === faculty.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {faculty.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
