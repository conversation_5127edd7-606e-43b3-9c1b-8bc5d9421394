
import React, { useState, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  SemesterLabConfigurationService,
  LabTimeSlotDefinition
} from "@/services/SemesterLabConfigurationService";

interface LabSlotSelectionProps {
  form: UseFormReturn<any>;
  slotNumber: number;
  disabled?: boolean;
  hoursPerSlot: number;
  academicYear?: string;
  department?: string;
  semester?: string;
}

export default function LabSlotSelection({
  form,
  slotNumber,
  disabled = false,
  hoursPerSlot,
  academicYear = "2024-2025",
  department = "cse",
  semester = "4",
}: LabSlotSelectionProps) {
  const dayField = `day${slotNumber}` as const;
  const timeField = `timeOfDay${slotNumber}` as const;
  const batchField = `batch${slotNumber}` as const;

  const [availableTimeSlots, setAvailableTimeSlots] = useState<LabTimeSlotDefinition[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load available time slots based on semester configuration
  useEffect(() => {
    const loadTimeSlots = async () => {
      if (!academicYear || !department || !semester) {
        return;
      }

      setIsLoading(true);
      try {
        const config = await SemesterLabConfigurationService.getLabConfiguration(
          academicYear,
          department,
          semester
        );

        if (config) {
          const slots = SemesterLabConfigurationService.getAvailableTimeSlots(config);
          setAvailableTimeSlots(slots);
        }
      } catch (error) {
        console.error('Error loading time slots:', error);
        // Fallback to default slots for backward compatibility
        setAvailableTimeSlots([
          {
            id: 'morning_3h',
            name: 'Morning Session',
            timeRange: '08:30-11:30',
            periods: 'Periods 1-3',
            duration: 3,
            enabled: true
          },
          {
            id: 'afternoon_3h',
            name: 'Afternoon Session',
            timeRange: '13:15-16:00',
            periods: 'Periods 5-7',
            duration: 3,
            enabled: true
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    loadTimeSlots();
  }, [academicYear, department, semester]);

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium">Lab Slot {slotNumber}</h4>
      <p className="text-xs text-muted-foreground">
        This slot will use {hoursPerSlot} consecutive {hoursPerSlot === 1 ? "period" : "periods"}
        ({hoursPerSlot * 50} minutes)
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name={dayField}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Day</FormLabel>
              <Select
                value={field.value}
                onValueChange={field.onChange}
                disabled={disabled}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Monday">Monday</SelectItem>
                  <SelectItem value="Tuesday">Tuesday</SelectItem>
                  <SelectItem value="Wednesday">Wednesday</SelectItem>
                  <SelectItem value="Thursday">Thursday</SelectItem>
                  <SelectItem value="Friday">Friday</SelectItem>
                  <SelectItem value="Saturday">Saturday</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={timeField}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Time of Day</FormLabel>
              <Select
                value={field.value}
                onValueChange={field.onChange}
                disabled={disabled || isLoading}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={isLoading ? "Loading..." : "Select time"} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {availableTimeSlots.map((slot) => (
                    <SelectItem key={slot.id} value={slot.name}>
                      <div className="flex items-center justify-between w-full">
                        <span>{slot.name}</span>
                        <div className="flex items-center gap-2 ml-2">
                          <Badge variant="outline" className="text-xs">
                            {slot.duration}h
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {slot.timeRange}
                          </span>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                  {availableTimeSlots.length === 0 && !isLoading && (
                    <SelectItem value="" disabled>
                      No time slots available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <FormDescription>
                {availableTimeSlots.find(slot => slot.name === field.value)?.periods ||
                 `${hoursPerSlot} consecutive periods`}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={batchField}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Batch</FormLabel>
              <FormControl>
                <Input
                  placeholder="e.g., A, B1, CSE-A"
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormDescription>Student batch for this lab</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
