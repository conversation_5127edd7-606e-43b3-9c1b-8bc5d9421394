import React, { useState } from 'react';
import { Edit, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getDisplaySubjectCode } from '@/utils/subjectUtils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface TimetableCellContentProps {
  slot: any;
  isLab: boolean;
  onUpdate: (updatedSlot: any) => void;
  onDelete: (slotId: string) => void;
  view?: "class" | "faculty";
  selectedFaculty?: string;
  className?: string;
}

export const TimetableCellContent: React.FC<TimetableCellContentProps> = ({
  slot,
  isLab,
  onUpdate,
  onDelete,
  view = "class",
  selectedFaculty,
  className = "",
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedSlot, setEditedSlot] = useState(slot);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    e.dataTransfer.setData('text/plain', JSON.stringify(slot));
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleEdit = () => {
    setEditedSlot({ ...slot });
    setIsEditing(true);
  };

  const handleSave = () => {
    onUpdate(editedSlot);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedSlot(slot);
    setIsEditing(false);
  };

  const handleDelete = () => {
    onDelete(slot.id);
    setIsDeleting(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditedSlot(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Helper function to get display content based on view type
  const getDisplayContent = () => {
    // Check if this is a continued lab card
    if (slot.isContinuedLab || slot.showContinuedBadge) {
      const semesterSection = view === "faculty" && slot.semester && slot.section ? `${slot.semester}${slot.section}` : '';
      return {
        mainLine: getDisplaySubjectCode(slot.subject_code, slot.subject_short_id),
        subtitle: view === "faculty" ? semesterSection : (slot.batch_name ? `(${slot.batch_name})` : ''),
        isCompact: true,
        showContinuedBadge: true,
        excludeFaculty: true
      };
    }

    // Check if custom displayText is provided
    if (slot.displayText) {
      const semesterSection = view === "faculty" && slot.semester && slot.section ? `${slot.semester}${slot.section}` : '';
      return {
        mainLine: slot.displayText,
        subtitle: view === "faculty" ? semesterSection : (slot.batch_name ? `(${slot.batch_name})` : ''),
        isCompact: true
      };
    }

    const subjectShortId = getDisplaySubjectCode(slot.subject_code, slot.subject_short_id);

    if (isLab) {
      // Lab slots
      if (view === "faculty") {
        const semesterSection = slot.semester && slot.section ? `${slot.semester}${slot.section}` : '';
        const batchInfo = slot.batch_name ? ` (${slot.batch_name})` : '';
        return {
          mainLine: subjectShortId,
          subtitle: semesterSection + batchInfo,
          isCompact: true,
          showFacultyRole: true
        };
      } else {
        return {
          mainLine: subjectShortId,
          subtitle: slot.batch_name ? `(${slot.batch_name})` : '',
          isCompact: true
        };
      }
    } else {
      // Theory slots
      if (view === "faculty") {
        const semesterSection = slot.semester && slot.section ? `${slot.semester}${slot.section}` : '';
        const subjectType = slot.subject_type ? ` (${slot.subject_type.toUpperCase()})` : '';
        return {
          mainLine: subjectShortId,
          subtitle: semesterSection + subjectType,
          isCompact: true,
          showFacultyRole: true
        };
      } else {
        return {
          mainLine: subjectShortId,
          subtitle: '',
          isCompact: true
        };
      }
    }
  };

  const displayContent = getDisplayContent();

  return (
    <>
      <div
        className={`relative group p-2 min-h-[60px] flex flex-col justify-center text-center cursor-move hover:bg-gray-50 transition-colors duration-200 ${className}`}
        draggable
        onDragStart={handleDragStart}
        tabIndex={0}
      >
        {/* Main content */}
        <div className="flex flex-col items-center justify-center space-y-1">
          {/* Show "Continued" badge for continued lab cards */}
          {displayContent.showContinuedBadge && (
            <span className="text-xs font-medium text-orange-800 bg-orange-200 px-2 py-0.5 rounded-full border border-orange-300">
              Continued
            </span>
          )}

          {/* Faculty role indicator for faculty view */}
          {view === "faculty" && displayContent.showFacultyRole && selectedFaculty && (
            <div className="flex items-center space-x-1">
              {slot.faculty_id === selectedFaculty && (
                <span className="text-xs font-medium text-green-700 bg-green-100 px-1.5 py-0.5 rounded-full border border-green-300">
                  Primary
                </span>
              )}
              {slot.faculty2_id === selectedFaculty && (
                <span className="text-xs font-medium text-blue-700 bg-blue-100 px-1.5 py-0.5 rounded-full border border-blue-300">
                  Secondary
                </span>
              )}
            </div>
          )}

          <div className="font-semibold text-sm leading-tight break-words max-w-full">
            {displayContent.mainLine}
          </div>

          {displayContent.subtitle && (
            <div className={`text-xs font-medium px-2 py-0.5 rounded-full ${
              view === "faculty"
                ? 'text-purple-700 bg-purple-100 border border-purple-300'
                : 'text-blue-600 bg-blue-100'
            }`}>
              {displayContent.subtitle}
            </div>
          )}
        </div>

        {/* Action buttons - positioned in top-right corner */}
        <div className="absolute top-1 right-1 flex opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
          <button
            onClick={handleEdit}
            className="text-white hover:text-blue-200 p-1 bg-blue-600/80 hover:bg-blue-700/90 transition-all duration-200 backdrop-blur-sm shadow-sm rounded-l-sm"
            title="Edit slot"
          >
            <Edit className="h-3 w-3" />
          </button>
          <button
            onClick={() => setIsDeleting(true)}
            className="text-white hover:text-red-200 p-1 bg-red-600/80 hover:bg-red-700/90 transition-all duration-200 backdrop-blur-sm shadow-sm rounded-r-sm"
            title="Delete slot"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Timetable Slot</DialogTitle>
            <DialogDescription>Update details for this timeslot.</DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="faculty_name" className="text-right">
                Faculty
              </Label>
              <Input
                id="faculty_name"
                name="faculty_name"
                value={editedSlot.faculty_name}
                onChange={handleChange}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="faculty2_name" className="text-right">
                Second Faculty
              </Label>
              <Input
                id="faculty2_name"
                name="faculty2_name"
                value={editedSlot.faculty2_name || ''}
                onChange={handleChange}
                className="col-span-3"
                placeholder="Optional"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="room_number" className="text-right">
                Room
              </Label>
              <Input
                id="room_number"
                name="room_number"
                value={editedSlot.room_number || ''}
                onChange={handleChange}
                className="col-span-3"
              />
            </div>

            {isLab && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="batch_name" className="text-right">
                  Batch
                </Label>
                <Input
                  id="batch_name"
                  name="batch_name"
                  value={editedSlot.batch_name || ''}
                  onChange={handleChange}
                  className="col-span-3"
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="submit" onClick={handleSave}>
              Save changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleting} onOpenChange={setIsDeleting}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Timetable Slot</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this slot? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
