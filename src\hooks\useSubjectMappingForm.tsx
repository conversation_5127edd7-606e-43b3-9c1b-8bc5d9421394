
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useToast } from "@/hooks/use-toast";
import { MappingService } from "@/services/mapping";
import { MappingType, Faculty } from "@/stores/SubjectMappingStore";
import { SubjectType } from "@/components/subjects/useSubjectLoader";
import { TimeSlotValidationService } from "@/services/TimeSlotValidationService";

const schema = z.object({
  faculty: z.object({
    id: z.string(),
    name: z.string(),
  }),
  faculty2Id: z.string().optional(),
  subject: z.object({
    id: z.string(),
    code: z.string(),
    name: z.string(),
    type: z.union([
      z.literal("theory"),
      z.literal("lab"),
      z.literal("elective"),
    ]),
  }),
  hoursPerWeek: z.coerce
    .number()
    .min(1, "At least 1 hour per week required")
    .max(15, "Maximum 15 hours per week"),
  classroom: z.string().min(1, "Classroom is required"),
  // Only required for lab subjects
  slotsPerWeek: z.coerce
    .number()
    .min(1, "At least 1 slot per week required")
    .max(4, "Maximum 4 slots per week")
    .optional(),
  // Lab slot fields - supporting both old and new formats for backward compatibility
  day1: z.string().optional(),
  timeOfDay1: z.string().optional(), // Legacy field
  startTime1: z.string().optional(), // New flexible time field
  endTime1: z.string().optional(),   // New flexible time field
  batch1: z.string().optional(),
  day2: z.string().optional(),
  timeOfDay2: z.string().optional(), // Legacy field
  startTime2: z.string().optional(), // New flexible time field
  endTime2: z.string().optional(),   // New flexible time field
  batch2: z.string().optional(),
  day3: z.string().optional(),
  timeOfDay3: z.string().optional(), // Legacy field
  startTime3: z.string().optional(), // New flexible time field
  endTime3: z.string().optional(),   // New flexible time field
  batch3: z.string().optional(),
  day4: z.string().optional(),
  timeOfDay4: z.string().optional(), // Legacy field
  startTime4: z.string().optional(), // New flexible time field
  endTime4: z.string().optional(),   // New flexible time field
  batch4: z.string().optional(),
}).superRefine((data, ctx) => {
  // Make secondary faculty required only for lab subjects
  if (data.subject?.type === "lab" && (!data.faculty2Id || data.faculty2Id === "")) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Secondary faculty is required for lab subjects",
      path: ["faculty2Id"],
    });
  }
});

export type MappingFormData = z.infer<typeof schema>;

export interface UseSubjectMappingFormProps {
  filtersValid: boolean;
  refetchMappings: () => Promise<void>;
  year: string;
  dept: string;
  sem: string;
  section: string;
  closeDrawer: () => void;
  facultyList: Faculty[];
}

export const useSubjectMappingForm = ({
  filtersValid,
  year,
  dept,
  sem,
  section,
  closeDrawer,
  refetchMappings,
  facultyList,
}: UseSubjectMappingFormProps) => {
  const { toast } = useToast();
  const [saving, setSaving] = useState(false);

  const form = useForm<MappingFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      hoursPerWeek: 3,
      classroom: "",
      faculty2Id: "",
    },
    mode: "onChange" // Validate on change to provide immediate feedback
  });

  // Watch subject type to conditionally show lab fields and set secondary faculty requirement
  const watchSubject = form.watch("subject");
  const isLabSubject = watchSubject?.type === "lab";
  const watchHoursPerWeek = form.watch("hoursPerWeek") || 3;
  const watchSlotsPerWeek = form.watch("slotsPerWeek") || 1;

  // Calculate hours per slot
  const hoursPerSlot = isLabSubject && watchSlotsPerWeek
    ? Math.ceil(watchHoursPerWeek / watchSlotsPerWeek)
    : 1;

  // Update validation when subject type changes
  useEffect(() => {
    if (isLabSubject) {
      form.trigger("faculty2Id");
    }
  }, [isLabSubject, form]);

  const onSubmit = async (data: MappingFormData) => {
    if (!filtersValid) return;

    try {
      setSaving(true);

      // Ensure facultyList is an array
      const safeFacultyList = Array.isArray(facultyList) ? facultyList : [];

      // Initialize lab slots as an empty array explicitly to avoid undefined issues
      let labSlots: Array<{day: string; timeOfDay: string; batch: string}> = [];

      if (isLabSubject && data.slotsPerWeek) {
        // Helper function to get time value (supports both old and new formats)
        const getTimeValue = (slotNumber: number) => {
          const timeOfDay = data[`timeOfDay${slotNumber}` as keyof MappingFormData] as string;
          const startTime = data[`startTime${slotNumber}` as keyof MappingFormData] as string;
          const endTime = data[`endTime${slotNumber}` as keyof MappingFormData] as string;

          // If using new flexible time selection (startTime/endTime)
          if (startTime && endTime) {
            // Use the validation service to ensure proper formatting
            return TimeSlotValidationService.formatTimeRange(startTime, endTime);
          }

          // Otherwise use legacy timeOfDay
          return timeOfDay;
        };

        // Add first lab slot if day and time are selected
        const day1 = data.day1;
        const time1 = getTimeValue(1);
        if (day1 && time1) {
          labSlots.push({
            day: day1,
            timeOfDay: time1,
            batch: data.batch1 || "A",
          });
        }

        // Add second lab slot if day and time are selected and slots > 1
        if (data.slotsPerWeek >= 2) {
          const day2 = data.day2;
          const time2 = getTimeValue(2);
          if (day2 && time2) {
            labSlots.push({
              day: day2,
              timeOfDay: time2,
              batch: data.batch2 || "B",
            });
          }
        }

        // Add third lab slot if day and time are selected and slots > 2
        if (data.slotsPerWeek >= 3) {
          const day3 = data.day3;
          const time3 = getTimeValue(3);
          if (day3 && time3) {
            labSlots.push({
              day: day3,
              timeOfDay: time3,
              batch: data.batch3 || "C",
            });
          }
        }

        // Add fourth lab slot if day and time are selected and slots > 3
        if (data.slotsPerWeek >= 4) {
          const day4 = data.day4;
          const time4 = getTimeValue(4);
          if (day4 && time4) {
            labSlots.push({
              day: day4,
              timeOfDay: time4,
              batch: data.batch4 || "D",
            });
          }
        }
      }

      // Find secondary faculty name
      const faculty2 = data.faculty2Id
        ? safeFacultyList.find((f) => f.id === data.faculty2Id)
        : undefined;

      // Construct the payload with safe values
      const payload: MappingType = {
        id: "",
        academicYear: year,
        department: dept,
        semester: sem,
        section: section,
        subject: {
          id: data.subject.id,
          code: data.subject.code,
          name: data.subject.name,
          type: data.subject.type,
        },
        faculty: {
          id: data.faculty.id,
          name: data.faculty.name,
        },
        faculty2Id: data.faculty2Id,  // Store the secondary faculty ID
        faculty2Name: faculty2?.name,
        hoursPerWeek: data.hoursPerWeek,
        classroom: data.classroom,
        slotsPerWeek: isLabSubject ? data.slotsPerWeek : undefined,
        labSlots: labSlots, // Always pass a defined array
      };

      // Save the mapping with lab slots
      await MappingService.saveMapping(payload);

      toast({
        title: "Mapping created",
        description: `${data.subject.code} allocated to ${data.faculty.name}${faculty2?.name ? ` and ${faculty2.name}` : ''}`,
      });

      // Close the dialog and refetch mappings
      closeDrawer();
      refetchMappings();
    } catch (error) {
      console.error("Error saving mapping:", error);

      // Check for the specific PostgreSQL error code 42803 (aggregate function calls cannot be nested)
      const errorObj = error as any;
      const isAggregateError = errorObj && errorObj.code === '42803';

      if (isAggregateError) {
        // For this specific error, our backend has fallback mechanisms
        // The mapping might have been created despite the error
        toast({
          title: "Mapping may have been created",
          description: "The system encountered an issue but the mapping may have been created. Please check the list.",
          variant: "default",
        });

        // Try to refresh the mappings list to see if the mapping was created
        try {
          await refetchMappings();
          // Close the drawer after refreshing
          closeDrawer();
        } catch (refreshError) {
          console.error("Error refreshing mappings after aggregate error:", refreshError);
        }
      } else {
        // For other errors, show a standard error message
        toast({
          title: "Error saving mapping",
          description: "An error occurred while saving the mapping",
          variant: "destructive",
        });
      }
    } finally {
      setSaving(false);
    }
  };

  return {
    form,
    saving,
    isLabSubject,
    hoursPerSlot,
    onSubmit
  };
};

export { schema };