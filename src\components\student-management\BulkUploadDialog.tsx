import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, AlertCircle } from 'lucide-react';

interface BulkUploadDialogProps {
  onUpload: (data: any[]) => void;
  onCancel: () => void;
}

const BulkUploadDialog: React.FC<BulkUploadDialogProps> = ({ onUpload, onCancel }) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type === 'text/csv' || selectedFile.name.endsWith('.csv')) {
        setFile(selectedFile);
        setError(null);
      } else {
        setError('Please select a CSV file');
        setFile(null);
      }
    }
  };

  const parseCSV = (csvText: string): any[] => {
    const lines = csvText.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      throw new Error('CSV file must have at least a header row and one data row');
    }

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length !== headers.length) {
        throw new Error(`Row ${i + 1} has ${values.length} columns, expected ${headers.length}`);
      }

      const row: any = {};
      headers.forEach((header, index) => {
        // Map CSV headers to our field names
        const fieldMap: Record<string, string> = {
          'roll number': 'roll_number',
          'roll_number': 'roll_number',
          'student name': 'student_name',
          'student_name': 'student_name',
          'name': 'student_name',
          'parent name': 'parent_name',
          'parent_name': 'parent_name',
          'father phone': 'father_phone',
          'father_phone': 'father_phone',
          'mother phone': 'mother_phone',
          'mother_phone': 'mother_phone',
          'student phone': 'student_phone',
          'student_phone': 'student_phone',
          'parent email': 'parent_email',
          'parent_email': 'parent_email',
          'student email': 'student_email',
          'student_email': 'student_email'
        };

        const fieldName = fieldMap[header] || header;
        row[fieldName] = values[index];
      });

      // Validate required fields
      if (!row.roll_number || !row.student_name) {
        throw new Error(`Row ${i + 1}: Roll number and student name are required`);
      }

      data.push(row);
    }

    return data;
  };

  const handleUpload = async () => {
    if (!file) {
      setError('Please select a file');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const text = await file.text();
      const data = parseCSV(text);
      onUpload(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to parse CSV file');
    } finally {
      setUploading(false);
    }
  };

  const downloadTemplate = () => {
    const csvContent = [
      'roll_number,student_name,parent_name,father_phone,mother_phone,student_phone,parent_email,student_email',
      'CSE001,John Doe,Mr. Doe,9876543210,9876543211,9876543212,<EMAIL>,<EMAIL>',
      'CSE002,Jane Smith,Mrs. Smith,9876543213,9876543214,9876543215,<EMAIL>,<EMAIL>'
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'student_upload_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Bulk Upload Students</DialogTitle>
          <DialogDescription>
            Upload a CSV file to add multiple students at once.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="csv-file">CSV File</Label>
            <Input
              id="csv-file"
              type="file"
              accept=".csv"
              onChange={handleFileChange}
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Alert>
            <Download className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <span>Need a template?</span>
                <Button
                  variant="link"
                  size="sm"
                  onClick={downloadTemplate}
                  className="p-0 h-auto"
                >
                  Download Template
                </Button>
              </div>
            </AlertDescription>
          </Alert>

          <div className="text-sm text-gray-600">
            <p><strong>CSV Format:</strong></p>
            <ul className="list-disc list-inside space-y-1 mt-2">
              <li>Required: roll_number, student_name</li>
              <li>Optional: parent_name, father_phone, mother_phone, student_phone, parent_email, student_email</li>
              <li>First row should contain column headers</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!file || uploading}
          >
            {uploading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                Uploading...
              </div>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload Students
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BulkUploadDialog;
