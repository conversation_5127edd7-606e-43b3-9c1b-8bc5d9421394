import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, Users, Clock, BookOpen } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

export interface ElectiveConfiguration {
  hasElectives: boolean;
  groupingType: 'same_group' | 'different_groups';
  synchronizationMode: 'synchronized' | 'independent';
}

interface ElectiveConfigurationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (config: ElectiveConfiguration) => void;
  academicYear: string;
  department: string;
  semester: string;
  section: string;
}

interface ElectiveSubject {
  subject_code: string;
  subject_name: string;
  faculty_name: string;
  hours_per_week: number;
}

export const ElectiveConfigurationModal: React.FC<ElectiveConfigurationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  academicYear,
  department,
  semester,
  section
}) => {
  const [hasElectives, setHasElectives] = useState<string>('no');
  const [groupingType, setGroupingType] = useState<'same_group' | 'different_groups'>('same_group');
  const [synchronizationMode, setSynchronizationMode] = useState<'synchronized' | 'independent'>('synchronized');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [electiveSubjects, setElectiveSubjects] = useState<ElectiveSubject[]>([]);
  const [loadingElectives, setLoadingElectives] = useState(false);

  // Load elective subjects when modal opens
  useEffect(() => {
    if (isOpen) {
      loadElectiveSubjects();
    }
  }, [isOpen, academicYear, department, semester, section]);

  const loadElectiveSubjects = async () => {
    setLoadingElectives(true);
    setError('');

    try {
      console.log('🔍 Loading elective subjects for:', {
        academicYear,
        department,
        semester,
        section
      });

      // First, let's check what data exists in the table
      const { data: allData, error: allError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('academic_year, department, semester, section, subject_type, subject_code')
        .eq('semester', semester)
        .eq('subject_type', 'elective');

      console.log('🔍 All elective data in semester:', allData);

      // Also check what parameters we're using vs what's in the database
      if (allData && allData.length > 0) {
        console.log('📊 Parameter comparison:');
        console.log('  Our academicYear:', academicYear, 'vs DB:', [...new Set(allData.map(d => d.academic_year))]);
        console.log('  Our department:', department, 'vs DB:', [...new Set(allData.map(d => d.department))]);
        console.log('  Our section:', section, 'vs DB:', [...new Set(allData.map(d => d.section))]);
      }

      // Now try the specific query
      const { data, error } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          subject_code,
          subject_name,
          hours_per_week,
          faculty_1_id,
          subjects(subject_short_id)
        `)
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .eq('subject_type', 'elective');

      if (error) {
        console.error('❌ Database error:', error);
        throw error;
      }

      // Now get faculty names for each elective
      const electives = [];
      if (data && data.length > 0) {
        for (const item of data) {
          let facultyName = 'Unknown Faculty';

          if (item.faculty_1_id) {
            const { data: facultyData, error: facultyError } = await supabase
              .from('employee_details')
              .select('full_name')
              .eq('id', item.faculty_1_id)
              .single();

            if (!facultyError && facultyData) {
              facultyName = facultyData.full_name;
            }
          }

          electives.push({
            subject_code: item.subject_code,
            subject_name: item.subject_name,
            subject_short_id: item.subjects?.subject_short_id,
            faculty_name: facultyName,
            hours_per_week: item.hours_per_week
          });
        }
      }

      console.log('✅ Processed electives:', electives);
      setElectiveSubjects(electives);

      // Auto-set hasElectives based on data
      setHasElectives(electives.length > 0 ? 'yes' : 'no');

      if (electives.length === 0) {
        console.log('⚠️ No elective subjects found for this semester-section');
      }
    } catch (error) {
      console.error('❌ Error loading elective subjects:', error);
      setError(`Failed to load elective subjects: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoadingElectives(false);
    }
  };

  const handleConfirm = async () => {
    setError('');
    setIsLoading(true);

    try {
      const config: ElectiveConfiguration = {
        hasElectives: hasElectives === 'yes',
        groupingType,
        synchronizationMode
      };

      // Validate configuration
      if (config.hasElectives && electiveSubjects.length === 0) {
        setError('No elective subjects found for this semester-section');
        setIsLoading(false);
        return;
      }

      onConfirm(config);
    } catch (error) {
      console.error('Error confirming elective configuration:', error);
      setError('Failed to process elective configuration');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setHasElectives('no');
    setGroupingType('same_group');
    setSynchronizationMode('synchronized');
    setError('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[520px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            <span>Elective Configuration</span>
          </DialogTitle>
          <DialogDescription className="text-sm text-gray-600">
            Configure elective subjects for Semester {semester}, Section {section}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-5">
          {/* Loading State */}
          {loadingElectives && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <span className="ml-2 text-sm text-gray-600">Loading elective subjects...</span>
            </div>
          )}

          {/* Elective Subjects Display */}
          {!loadingElectives && electiveSubjects.length > 0 && (
            <div className="bg-primary/5 rounded-lg p-4 border border-primary/20">
              <h4 className="text-sm font-medium text-primary mb-3">Found Elective Subjects:</h4>
              <div className="space-y-2">
                {electiveSubjects.map((elective, index) => (
                  <div key={index} className="bg-card rounded p-3 border border-border">
                    <div className="font-medium text-sm text-card-foreground">{elective.subject_code}</div>
                    <div className="text-xs text-muted-foreground">{elective.subject_name}</div>
                    <div className="text-xs text-primary mt-1">
                      Faculty: {elective.faculty_name} • {elective.hours_per_week} hours/week
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Elective Question */}
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <Label className="text-sm font-medium text-gray-900">Does this semester have elective subjects?</Label>
              <RadioGroup
                value={hasElectives}
                onValueChange={setHasElectives}
                className="flex space-x-4"
                disabled={loadingElectives}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="electives-yes" />
                  <Label htmlFor="electives-yes" className="text-sm cursor-pointer">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="electives-no" />
                  <Label htmlFor="electives-no" className="text-sm cursor-pointer">No</Label>
                </div>
              </RadioGroup>
            </div>
          </div>

          {/* Elective Configuration (shown only if Yes) */}
          {hasElectives === 'yes' && !loadingElectives && (
            <>
              {/* Grouping Type */}
              <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                <Label className="text-sm font-medium text-green-900 mb-3 block">Elective Grouping Strategy</Label>
                <RadioGroup
                  value={groupingType}
                  onValueChange={(value: 'same_group' | 'different_groups') => setGroupingType(value)}
                  className="space-y-3"
                >
                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="same_group" id="same-group" className="mt-1" />
                    <div className="flex-1">
                      <Label htmlFor="same-group" className="text-sm font-medium cursor-pointer">Same Group (Mutually Exclusive)</Label>
                      <p className="text-xs text-green-700 mt-1">
                        Both electives placed in the same time slot. Students choose one elective.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="different_groups" id="different-groups" className="mt-1" />
                    <div className="flex-1">
                      <Label htmlFor="different-groups" className="text-sm font-medium cursor-pointer">Different Groups (Independent)</Label>
                      <p className="text-xs text-green-700 mt-1">
                        Each elective gets separate time slots. Students can take both electives.
                      </p>
                    </div>
                  </div>
                </RadioGroup>
              </div>

              {/* Synchronization Mode */}
              <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <Label className="text-sm font-medium text-purple-900 mb-3 block flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Cross-Section Synchronization</span>
                </Label>
                <RadioGroup
                  value={synchronizationMode}
                  onValueChange={(value: 'synchronized' | 'independent') => setSynchronizationMode(value)}
                  className="space-y-3"
                >
                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="synchronized" id="synchronized" className="mt-1" />
                    <div className="flex-1">
                      <Label htmlFor="synchronized" className="text-sm font-medium cursor-pointer">Synchronized Mode</Label>
                      <p className="text-xs text-purple-700 mt-1">
                        Same faculty teaches at identical time slots across all sections.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="independent" id="independent" className="mt-1" />
                    <div className="flex-1">
                      <Label htmlFor="independent" className="text-sm font-medium cursor-pointer">Independent Mode</Label>
                      <p className="text-xs text-purple-700 mt-1">
                        Section-specific scheduling without cross-section constraints.
                      </p>
                    </div>
                  </div>
                </RadioGroup>
              </div>

              {/* Configuration Summary */}
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  <strong>Configuration Summary:</strong><br />
                  • Grouping: {groupingType === 'same_group' ? 'Same time slot (mutually exclusive)' : 'Separate time slots (independent)'}<br />
                  • Synchronization: {synchronizationMode === 'synchronized' ? 'Synchronized across sections' : 'Independent per section'}<br />
                  • Electives found: {electiveSubjects.length} subjects
                </AlertDescription>
              </Alert>
            </>
          )}

          {/* No Electives Message */}
          {hasElectives === 'no' && !loadingElectives && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No elective configuration needed. Theory slots will be generated using standard algorithm.
              </AlertDescription>
            </Alert>
          )}

          {/* Error message */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded border border-red-200">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{error}</span>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="pt-4 border-t">
          <div className="flex space-x-3 w-full">
            <Button variant="outline" onClick={handleCancel} disabled={isLoading} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleConfirm} disabled={isLoading || loadingElectives} className="flex-1">
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Continue with Theory Generation
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
