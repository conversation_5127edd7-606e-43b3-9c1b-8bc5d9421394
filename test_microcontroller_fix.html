<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test MicroController IA Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: #fff3cd; }
    </style>
</head>
<body>
    <h1>🧪 Test MicroController IA Fix for Student 1KS23CS001</h1>
    <div id="results"></div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        const supabaseUrl = 'https://milmyotuougemocvieof.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NjI0NzQsImV4cCI6MjA1MDUzODQ3NH0.Ej5Ej6bJYJJhUvSJKJhUvSJKJhUvSJKJhUvSJKJhUvS';

        const supabase = createClient(supabaseUrl, supabaseKey);
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        async function testMicroControllerFix() {
            addResult('🔄 Testing MicroController IA Fix for Student 1KS23CS001...', 'info');

            try {
                const testUSN = '1KS23CS001';
                addResult(`📋 Testing with student USN: ${testUSN}`, 'info');

                // Step 1: Get student info
                const { data: studentRecords, error: studentError } = await supabase
                    .from('class_students')
                    .select('id, usn, student_name, department, semester, section, academic_year')
                    .eq('usn', testUSN.toUpperCase());

                if (studentError || !studentRecords || studentRecords.length === 0) {
                    addResult(`❌ Student not found: ${studentError?.message}`, 'error');
                    return;
                }

                const selectedStudent = studentRecords[0];
                addResult(`✅ Student found: ${selectedStudent.student_name}`, 'success');

                // Step 2: Test IA marks fetch (simulating StudentProgressService logic)
                addResult('🔍 Testing IA marks fetch with academic year variations...', 'info');

                const academicYearVariations = [
                    selectedStudent.academic_year, // "2024-2025"
                    "2025-2026", // Current year where IA data is stored
                    "2024-25",
                    "2025-26"
                ];

                let iaData = null;
                let foundWithYear = null;

                for (const yearVariant of academicYearVariations) {
                    const { data: tempData, error: tempError } = await supabase
                        .from('internal_assessments')
                        .select('*')
                        .eq('student_id', selectedStudent.id)
                        .eq('department', selectedStudent.department.toLowerCase())
                        .eq('semester', selectedStudent.semester)
                        .eq('academic_year', yearVariant);

                    if (!tempError && tempData && tempData.length > 0) {
                        iaData = tempData;
                        foundWithYear = yearVariant;
                        addResult(`✅ Found ${tempData.length} IA records with academic year: ${yearVariant}`, 'success');
                        break;
                    }
                }

                if (!iaData || iaData.length === 0) {
                    // Try fallback approach
                    addResult('🔍 No records with strict filters, trying fallback approach...', 'info');
                    
                    const { data: fallbackData, error: fallbackError } = await supabase
                        .from('internal_assessments')
                        .select('*')
                        .eq('student_id', selectedStudent.id);

                    if (!fallbackError && fallbackData && fallbackData.length > 0) {
                        iaData = fallbackData.filter(record => {
                            const recordDept = (record.department || '').toLowerCase();
                            const targetDept = selectedStudent.department.toLowerCase();
                            const deptMatch = recordDept === targetDept;
                            const semesterMatch = record.semester === selectedStudent.semester;
                            return deptMatch && semesterMatch;
                        });
                        
                        if (iaData.length > 0) {
                            addResult(`✅ Found ${iaData.length} IA records using fallback approach`, 'success');
                        }
                    }
                }

                if (!iaData || iaData.length === 0) {
                    addResult('❌ No IA records found', 'error');
                    return;
                }

                // Step 3: Check specifically for BCS402 (MicroController)
                const bcs402Data = iaData.find(record => record.subject_code === 'BCS402');
                if (bcs402Data) {
                    addResult('✅ BCS402 (MicroController) IA marks found!', 'success');
                    addResult(`📊 BCS402 IA1 Marks: ${bcs402Data.ia1_marks}/25`, 'success');
                } else {
                    addResult('❌ BCS402 (MicroController) IA marks NOT found', 'error');
                }

                // Step 4: Get faculty information
                const facultyIds = [...new Set(iaData.map(record => record.faculty_id))];
                const { data: facultyData, error: facultyError } = await supabase
                    .from('employee_details')
                    .select('id, full_name')
                    .in('id', facultyIds);

                const facultyMap = new Map();
                if (!facultyError && facultyData) {
                    facultyData.forEach(faculty => {
                        facultyMap.set(faculty.id, faculty.full_name);
                    });
                }

                // Step 5: Display results in a table
                addResult('📊 Complete IA Marks Data:', 'info');

                let tableHTML = `
                    <table>
                        <thead>
                            <tr>
                                <th>Subject Code</th>
                                <th>Subject Name</th>
                                <th>Faculty</th>
                                <th>IA1 (/25)</th>
                                <th>IA2 (/25)</th>
                                <th>IA3 (/25)</th>
                                <th>Assignment (/10)</th>
                                <th>Theory (/20)</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                iaData.forEach(record => {
                    const facultyName = facultyMap.get(record.faculty_id) || 'Unknown';
                    const isBCS402 = record.subject_code === 'BCS402';
                    const rowClass = isBCS402 ? 'highlight' : '';
                    const subjectName = record.subject_code === 'BCS402' ? 'MicroController' : 
                                      record.subject_code === 'BCS401' ? 'Analysis & Design of Algorithms' : 
                                      'Unknown Subject';
                    
                    tableHTML += `
                        <tr class="${rowClass}">
                            <td><strong>${record.subject_code}</strong></td>
                            <td>${subjectName}</td>
                            <td>${facultyName}</td>
                            <td>${record.ia1_marks || '-'}</td>
                            <td>${record.ia2_marks || '-'}</td>
                            <td>${record.ia3_marks || '-'}</td>
                            <td>${record.assignment_marks || '-'}</td>
                            <td>${record.theory_marks || '-'}</td>
                            <td>${isBCS402 ? '<strong>✅ FIXED!</strong>' : '✅ OK'}</td>
                        </tr>
                    `;
                });

                tableHTML += '</tbody></table>';
                addResult(tableHTML, 'success');

                // Step 6: Verify the fix
                const hasBCS402 = iaData.some(record => record.subject_code === 'BCS402');
                if (hasBCS402) {
                    addResult('🎉 SUCCESS: MicroController (BCS402) IA marks are now available!', 'success');
                    addResult('✅ The Student Progress Card should now display BCS402 IA marks correctly.', 'success');
                    addResult('✅ Faculty Sanjoy Das\'s IA marks for MicroController subject are now visible.', 'success');
                } else {
                    addResult('❌ FAILED: BCS402 IA marks are still missing', 'error');
                }

            } catch (error) {
                addResult(`❌ Test failed: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }

        // Run the test when page loads
        testMicroControllerFix();
    </script>
</body>
</html>
