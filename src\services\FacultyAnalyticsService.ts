import { supabase } from '@/integrations/supabase/client';

export interface AttendanceTrendData {
  date: string;
  attendance_percentage: number;
  total_students: number;
  present_count: number;
  subject_code: string;
  time_slot: string;
}

export interface StudentPerformanceData {
  student_usn: string;
  student_name: string;
  subjects: {
    subject_code: string;
    subject_type: string;
    attendance_percentage: number;
    total_classes: number;
    attended_classes: number;
    trend: 'improving' | 'declining' | 'stable';
    risk_level: 'low' | 'medium' | 'high';
  }[];
  overall_percentage: number;
  risk_level: 'low' | 'medium' | 'high';
}

export interface ClassSessionAnalytics {
  time_slot: string;
  day_of_week: string;
  average_attendance: number;
  total_sessions: number;
  subject_type: string;
  attendance_trend: number; // positive/negative trend
}

export interface SubjectAnalysis {
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: string;
  section: string;
  attendance_percentage: number;
  total_students: number;
  total_classes: number;
  trend_direction: 'up' | 'down' | 'stable';
  trend_percentage: number;
}

export interface PredictiveInsight {
  student_usn: string;
  student_name: string;
  current_percentage: number;
  predicted_percentage: number;
  risk_factors: string[];
  recommended_actions: string[];
  subjects_at_risk: string[];
}

export interface DashboardData {
  summary: {
    total_subjects: number;
    total_students: number;
    total_classes: number;
    average_attendance: number;
    trend_direction: 'up' | 'down' | 'stable';
    trend_percentage: number;
  };
  attendance_trends: AttendanceTrendData[];
  student_performance: StudentPerformanceData[];
  session_analytics: ClassSessionAnalytics[];
  subject_analysis: SubjectAnalysis[];
  predictive_insights: PredictiveInsight[];
  last_updated: string;
}

export class FacultyAnalyticsService {
  /**
   * Generate comprehensive analytics dashboard data for faculty
   */
  static async generateDashboardData(
    facultyId: string,
    department: string,
    filters: {
      semester?: string;
      section?: string;
      subject_code?: string;
      date_from?: string;
      date_to?: string;
      attendance_threshold?: number;
    } = {}
  ): Promise<DashboardData> {
    try {
      console.log('📊 Generating faculty analytics dashboard...');

      // Get base attendance data
      const attendanceData = await this.getAttendanceData(facultyId, department, filters);
      
      if (!attendanceData || attendanceData.length === 0) {
        return this.getEmptyDashboard();
      }

      // Generate all analytics components in parallel
      const [
        summary,
        attendanceTrends,
        studentPerformance,
        sessionAnalytics,
        subjectAnalysis,
        predictiveInsights
      ] = await Promise.all([
        this.generateSummaryStats(attendanceData, filters),
        this.generateAttendanceTrends(attendanceData, filters),
        this.generateStudentPerformance(attendanceData, filters),
        this.generateSessionAnalytics(attendanceData, filters),
        this.generateSubjectAnalysis(attendanceData, filters),
        this.generatePredictiveInsights(attendanceData, filters)
      ]);

      return {
        summary,
        attendance_trends: attendanceTrends,
        student_performance: studentPerformance,
        session_analytics: sessionAnalytics,
        subject_analysis: subjectAnalysis,
        predictive_insights: predictiveInsights,
        last_updated: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error generating dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get base attendance data with proper filtering including substitute classes
   */
  private static async getAttendanceData(
    facultyId: string,
    department: string,
    filters: any
  ): Promise<any[]> {
    const departmentVariants = [department, 'cse', 'CSE', department.toLowerCase()];

    console.log('📊 ANALYTICS: Fetching attendance data for faculty:', facultyId);
    console.log('📊 ANALYTICS: Department variants:', departmentVariants);

    let allAttendanceData: any[] = [];

    for (const deptVariant of departmentVariants) {
      // Get regular classes where faculty is the primary faculty
      let regularQuery = supabase
        .from('usn_attendance')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('department', deptVariant);

      // Get substitute classes where faculty marked attendance as substitute
      let substituteQuery = supabase
        .from('usn_attendance')
        .select('*')
        .eq('marked_by', facultyId)
        .eq('department', deptVariant)
        .neq('faculty_id', facultyId); // Different from faculty_id means it's a substitute

      // Apply filters to both queries
      if (filters.semester) {
        regularQuery = regularQuery.eq('semester', filters.semester);
        substituteQuery = substituteQuery.eq('semester', filters.semester);
      }
      if (filters.section) {
        regularQuery = regularQuery.eq('section', filters.section);
        substituteQuery = substituteQuery.eq('section', filters.section);
      }
      if (filters.date_from) {
        regularQuery = regularQuery.gte('attendance_date', filters.date_from);
        substituteQuery = substituteQuery.gte('attendance_date', filters.date_from);
      }
      if (filters.date_to) {
        regularQuery = regularQuery.lte('attendance_date', filters.date_to);
        substituteQuery = substituteQuery.lte('attendance_date', filters.date_to);
      }

      // Execute both queries
      const [regularResult, substituteResult] = await Promise.all([
        regularQuery.order('attendance_date', { ascending: true }),
        substituteQuery.order('attendance_date', { ascending: true })
      ]);

      if (regularResult.error) {
        console.error('Error fetching regular attendance data:', regularResult.error);
      }

      if (substituteResult.error) {
        console.error('Error fetching substitute attendance data:', substituteResult.error);
      }

      // Combine regular and substitute data
      const regularData = regularResult.data || [];
      const substituteData = substituteResult.data || [];

      console.log(`📊 ANALYTICS: Found ${regularData.length} regular attendance records for dept: ${deptVariant}`);
      console.log(`📊 ANALYTICS: Found ${substituteData.length} substitute attendance records for dept: ${deptVariant}`);

      // Mark substitute data for identification
      const markedSubstituteData = substituteData.map(record => ({
        ...record,
        is_substitute_marking: true
      }));

      const combinedData = [...regularData, ...markedSubstituteData];

      if (combinedData.length > 0) {
        // Filter by subject_code if provided
        if (filters.subject_code) {
          const filteredData = combinedData.filter(record =>
            record.subject_identifier && record.subject_identifier.includes(filters.subject_code)
          );
          allAttendanceData.push(...filteredData);
        } else {
          allAttendanceData.push(...combinedData);
        }
      }
    }

    // Remove duplicates based on unique combination of student, subject, date, time_slot
    const uniqueData = allAttendanceData.filter((record, index, self) =>
      index === self.findIndex(r =>
        r.student_usn === record.student_usn &&
        r.subject_code === record.subject_code &&
        r.attendance_date === record.attendance_date &&
        r.time_slot === record.time_slot
      )
    );

    console.log(`📊 ANALYTICS: Total unique attendance records: ${uniqueData.length}`);
    return uniqueData;
  }

  /**
   * Generate summary statistics with trend analysis
   */
  private static async generateSummaryStats(
    attendanceData: any[],
    filters: any
  ): Promise<DashboardData['summary']> {
    const uniqueSubjects = [...new Set(attendanceData.map(r => r.subject_identifier))];
    const uniqueStudents = [...new Set(attendanceData.map(r => r.student_usn))];
    const uniqueClasses = [...new Set(attendanceData.map(r => `${r.attendance_date}-${r.time_slot}`))];
    
    const totalPresent = attendanceData.filter(r => r.status === 'present').length;
    const averageAttendance = attendanceData.length > 0 ? (totalPresent / attendanceData.length) * 100 : 0;

    // Calculate trend (compare last 30 days vs previous 30 days)
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const recentData = attendanceData.filter(r => new Date(r.attendance_date) >= thirtyDaysAgo);
    const previousData = attendanceData.filter(r => 
      new Date(r.attendance_date) >= sixtyDaysAgo && new Date(r.attendance_date) < thirtyDaysAgo
    );

    const recentAttendance = recentData.length > 0 ? 
      (recentData.filter(r => r.status === 'present').length / recentData.length) * 100 : 0;
    const previousAttendance = previousData.length > 0 ? 
      (previousData.filter(r => r.status === 'present').length / previousData.length) * 100 : 0;

    const trendPercentage = recentAttendance - previousAttendance;
    const trendDirection = Math.abs(trendPercentage) < 1 ? 'stable' : 
      trendPercentage > 0 ? 'up' : 'down';

    return {
      total_subjects: uniqueSubjects.length,
      total_students: uniqueStudents.length,
      total_classes: uniqueClasses.length,
      average_attendance: Math.round(averageAttendance * 100) / 100,
      trend_direction: trendDirection,
      trend_percentage: Math.round(Math.abs(trendPercentage) * 100) / 100
    };
  }

  /**
   * Generate attendance trend data for charts
   */
  private static async generateAttendanceTrends(
    attendanceData: any[],
    filters: any
  ): Promise<AttendanceTrendData[]> {
    // Group by date and subject
    const trendMap = new Map<string, {
      date: string;
      subject_code: string;
      time_slot: string;
      total: number;
      present: number;
    }>();

    attendanceData.forEach(record => {
      const key = `${record.attendance_date}-${record.subject_identifier}-${record.time_slot}`;
      
      if (!trendMap.has(key)) {
        trendMap.set(key, {
          date: record.attendance_date,
          subject_code: record.subject_code,
          time_slot: record.time_slot || 'Unknown',
          total: 0,
          present: 0
        });
      }

      const trend = trendMap.get(key)!;
      trend.total++;
      if (record.status === 'present') {
        trend.present++;
      }
    });

    return Array.from(trendMap.values()).map(trend => ({
      date: trend.date,
      attendance_percentage: trend.total > 0 ? Math.round((trend.present / trend.total) * 100 * 100) / 100 : 0,
      total_students: trend.total,
      present_count: trend.present,
      subject_code: trend.subject_code,
      time_slot: trend.time_slot
    })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  /**
   * Generate student performance analysis
   */
  private static async generateStudentPerformance(
    attendanceData: any[],
    filters: any
  ): Promise<StudentPerformanceData[]> {
    const studentMap = new Map<string, {
      student_usn: string;
      student_name: string;
      subjects: Map<string, {
        subject_code: string;
        subject_type: string;
        total: number;
        present: number;
        dates: string[];
      }>;
    }>();

    // Group data by student and subject
    attendanceData.forEach(record => {
      if (!studentMap.has(record.student_usn)) {
        studentMap.set(record.student_usn, {
          student_usn: record.student_usn,
          student_name: record.student_name,
          subjects: new Map()
        });
      }

      const student = studentMap.get(record.student_usn)!;
      const subjectKey = record.subject_identifier;

      if (!student.subjects.has(subjectKey)) {
        student.subjects.set(subjectKey, {
          subject_code: record.subject_code,
          subject_type: record.subject_type || 'theory',
          total: 0,
          present: 0,
          dates: []
        });
      }

      const subject = student.subjects.get(subjectKey)!;
      subject.total++;
      subject.dates.push(record.attendance_date);
      if (record.status === 'present') {
        subject.present++;
      }
    });

    // Convert to final format with analysis
    return Array.from(studentMap.values()).map(student => {
      const subjects = Array.from(student.subjects.values()).map(subject => {
        const attendancePercentage = subject.total > 0 ? (subject.present / subject.total) * 100 : 0;
        
        // Calculate trend (simple: compare first half vs second half of dates)
        const sortedDates = subject.dates.sort();
        const midPoint = Math.floor(sortedDates.length / 2);
        const firstHalf = sortedDates.slice(0, midPoint);
        const secondHalf = sortedDates.slice(midPoint);
        
        const firstHalfAttendance = firstHalf.length > 0 ? 
          attendanceData.filter(r => 
            r.student_usn === student.student_usn && 
            r.subject_identifier === subject.subject_code + '_' + subject.subject_type &&
            firstHalf.includes(r.attendance_date) && 
            r.status === 'present'
          ).length / firstHalf.length * 100 : 0;

        const secondHalfAttendance = secondHalf.length > 0 ? 
          attendanceData.filter(r => 
            r.student_usn === student.student_usn && 
            r.subject_identifier === subject.subject_code + '_' + subject.subject_type &&
            secondHalf.includes(r.attendance_date) && 
            r.status === 'present'
          ).length / secondHalf.length * 100 : 0;

        const trendDiff = secondHalfAttendance - firstHalfAttendance;
        const trend = Math.abs(trendDiff) < 5 ? 'stable' : trendDiff > 0 ? 'improving' : 'declining';
        
        const riskLevel = attendancePercentage < 50 ? 'high' : 
          attendancePercentage < 75 ? 'medium' : 'low';

        return {
          subject_code: subject.subject_code,
          subject_type: subject.subject_type,
          attendance_percentage: Math.round(attendancePercentage * 100) / 100,
          total_classes: subject.total,
          attended_classes: subject.present,
          trend,
          risk_level: riskLevel as 'low' | 'medium' | 'high'
        };
      });

      const overallPercentage = subjects.length > 0 ? 
        subjects.reduce((sum, s) => sum + s.attendance_percentage, 0) / subjects.length : 0;
      
      const overallRiskLevel = overallPercentage < 50 ? 'high' : 
        overallPercentage < 75 ? 'medium' : 'low';

      return {
        student_usn: student.student_usn,
        student_name: student.student_name,
        subjects,
        overall_percentage: Math.round(overallPercentage * 100) / 100,
        risk_level: overallRiskLevel as 'low' | 'medium' | 'high'
      };
    }).sort((a, b) => a.student_usn.localeCompare(b.student_usn));
  }

  /**
   * Return empty dashboard for no data scenarios
   */
  private static getEmptyDashboard(): DashboardData {
    return {
      summary: {
        total_subjects: 0,
        total_students: 0,
        total_classes: 0,
        average_attendance: 0,
        trend_direction: 'stable',
        trend_percentage: 0
      },
      attendance_trends: [],
      student_performance: [],
      session_analytics: [],
      subject_analysis: [],
      predictive_insights: [],
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Generate session analytics (time slots, days, subject types)
   */
  private static async generateSessionAnalytics(attendanceData: any[], filters: any): Promise<ClassSessionAnalytics[]> {
    const sessionMap = new Map<string, {
      time_slot: string;
      day_of_week: string;
      subject_type: string;
      total: number;
      present: number;
      dates: string[];
    }>();

    attendanceData.forEach(record => {
      const date = new Date(record.attendance_date);
      const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
      const key = `${record.time_slot}-${dayOfWeek}-${record.subject_type}`;

      if (!sessionMap.has(key)) {
        sessionMap.set(key, {
          time_slot: record.time_slot || 'Unknown',
          day_of_week: dayOfWeek,
          subject_type: record.subject_type || 'theory',
          total: 0,
          present: 0,
          dates: []
        });
      }

      const session = sessionMap.get(key)!;
      session.total++;
      session.dates.push(record.attendance_date);
      if (record.status === 'present') {
        session.present++;
      }
    });

    return Array.from(sessionMap.values()).map(session => {
      const averageAttendance = session.total > 0 ? (session.present / session.total) * 100 : 0;

      // Calculate trend (compare first half vs second half of dates)
      const sortedDates = [...new Set(session.dates)].sort();
      const midPoint = Math.floor(sortedDates.length / 2);
      const firstHalf = sortedDates.slice(0, midPoint);
      const secondHalf = sortedDates.slice(midPoint);

      const firstHalfData = attendanceData.filter(r =>
        r.time_slot === session.time_slot &&
        firstHalf.includes(r.attendance_date)
      );
      const secondHalfData = attendanceData.filter(r =>
        r.time_slot === session.time_slot &&
        secondHalf.includes(r.attendance_date)
      );

      const firstHalfAttendance = firstHalfData.length > 0 ?
        (firstHalfData.filter(r => r.status === 'present').length / firstHalfData.length) * 100 : 0;
      const secondHalfAttendance = secondHalfData.length > 0 ?
        (secondHalfData.filter(r => r.status === 'present').length / secondHalfData.length) * 100 : 0;

      const attendanceTrend = secondHalfAttendance - firstHalfAttendance;

      return {
        time_slot: session.time_slot,
        day_of_week: session.day_of_week,
        average_attendance: Math.round(averageAttendance * 100) / 100,
        total_sessions: sortedDates.length,
        subject_type: session.subject_type,
        attendance_trend: Math.round(attendanceTrend * 100) / 100
      };
    }).sort((a, b) => a.time_slot.localeCompare(b.time_slot));
  }

  /**
   * Generate subject-specific analysis for faculty's assigned subjects
   */
  private static async generateSubjectAnalysis(
    attendanceData: any[],
    filters: any
  ): Promise<SubjectAnalysis[]> {
    const subjectMap = new Map<string, {
      subject_code: string;
      subject_name: string;
      subject_type: string;
      semester: string;
      section: string;
      total: number;
      present: number;
      dates: string[];
    }>();

    // Group data by subject-semester-section combination
    attendanceData.forEach(record => {
      const key = `${record.subject_code}-${record.semester}-${record.section}`;

      if (!subjectMap.has(key)) {
        subjectMap.set(key, {
          subject_code: record.subject_code,
          subject_name: record.subject_name || record.subject_code,
          subject_type: record.subject_type || 'theory',
          semester: record.semester.toString(),
          section: record.section,
          total: 0,
          present: 0,
          dates: []
        });
      }

      const subject = subjectMap.get(key)!;
      subject.total++;
      subject.dates.push(record.attendance_date);
      if (record.status === 'present') {
        subject.present++;
      }
    });

    return Array.from(subjectMap.values()).map(subject => {
      const attendancePercentage = subject.total > 0 ? (subject.present / subject.total) * 100 : 0;

      // Calculate trend (compare first half vs second half of dates)
      const sortedDates = [...new Set(subject.dates)].sort();
      const midPoint = Math.floor(sortedDates.length / 2);
      const firstHalf = sortedDates.slice(0, midPoint);
      const secondHalf = sortedDates.slice(midPoint);

      const firstHalfData = attendanceData.filter(r =>
        r.subject_code === subject.subject_code &&
        r.semester.toString() === subject.semester &&
        r.section === subject.section &&
        firstHalf.includes(r.attendance_date)
      );
      const secondHalfData = attendanceData.filter(r =>
        r.subject_code === subject.subject_code &&
        r.semester.toString() === subject.semester &&
        r.section === subject.section &&
        secondHalf.includes(r.attendance_date)
      );

      const firstHalfAttendance = firstHalfData.length > 0 ?
        (firstHalfData.filter(r => r.status === 'present').length / firstHalfData.length) * 100 : 0;
      const secondHalfAttendance = secondHalfData.length > 0 ?
        (secondHalfData.filter(r => r.status === 'present').length / secondHalfData.length) * 100 : 0;

      const trendDiff = secondHalfAttendance - firstHalfAttendance;
      const trendDirection = Math.abs(trendDiff) < 2 ? 'stable' : trendDiff > 0 ? 'up' : 'down';

      const uniqueStudents = new Set(attendanceData.filter(r =>
        r.subject_code === subject.subject_code &&
        r.semester.toString() === subject.semester &&
        r.section === subject.section
      ).map(r => r.student_usn)).size;

      const uniqueClasses = new Set(attendanceData.filter(r =>
        r.subject_code === subject.subject_code &&
        r.semester.toString() === subject.semester &&
        r.section === subject.section
      ).map(r => `${r.attendance_date}-${r.time_slot}`)).size;

      return {
        subject_code: subject.subject_code,
        subject_name: subject.subject_name,
        subject_type: subject.subject_type,
        semester: subject.semester,
        section: subject.section,
        attendance_percentage: Math.round(attendancePercentage * 100) / 100,
        total_students: uniqueStudents,
        total_classes: uniqueClasses,
        trend_direction: trendDirection as 'up' | 'down' | 'stable',
        trend_percentage: Math.round(Math.abs(trendDiff) * 100) / 100
      };
    }).sort((a, b) => {
      // Sort by semester, then section, then subject code
      if (a.semester !== b.semester) {
        return parseInt(a.semester) - parseInt(b.semester);
      }
      if (a.section !== b.section) {
        return a.section.localeCompare(b.section);
      }
      return a.subject_code.localeCompare(b.subject_code);
    });
  }

  /**
   * Generate predictive insights for at-risk students
   */
  private static async generatePredictiveInsights(
    attendanceData: any[],
    filters: any
  ): Promise<PredictiveInsight[]> {
    const threshold = filters.attendance_threshold || 75;
    const insights: PredictiveInsight[] = [];

    // Group by student
    const studentGroups = attendanceData.reduce((acc, record) => {
      if (!acc[record.student_usn]) {
        acc[record.student_usn] = {
          student_name: record.student_name,
          records: []
        };
      }
      acc[record.student_usn].records.push(record);
      return acc;
    }, {} as Record<string, { student_name: string; records: any[] }>);

    Object.entries(studentGroups).forEach(([usn, student]) => {
      const totalClasses = student.records.length;
      const presentClasses = student.records.filter(r => r.status === 'present').length;
      const currentPercentage = totalClasses > 0 ? (presentClasses / totalClasses) * 100 : 0;

      if (currentPercentage < threshold) {
        // Calculate trend for prediction
        const sortedRecords = student.records.sort((a, b) =>
          new Date(a.attendance_date).getTime() - new Date(b.attendance_date).getTime()
        );

        const recentRecords = sortedRecords.slice(-10); // Last 10 classes
        const recentPresent = recentRecords.filter(r => r.status === 'present').length;
        const recentPercentage = recentRecords.length > 0 ? (recentPresent / recentRecords.length) * 100 : 0;

        // Simple prediction: if recent trend continues
        const trendDiff = recentPercentage - currentPercentage;
        const predictedPercentage = Math.max(0, Math.min(100, currentPercentage + (trendDiff * 2)));

        // Identify risk factors
        const riskFactors: string[] = [];
        const subjectGroups = student.records.reduce((acc, record) => {
          if (!acc[record.subject_identifier]) acc[record.subject_identifier] = [];
          acc[record.subject_identifier].push(record);
          return acc;
        }, {} as Record<string, any[]>);

        const subjectsAtRisk: string[] = [];
        Object.entries(subjectGroups).forEach(([subjectId, records]) => {
          const subjectPresent = records.filter(r => r.status === 'present').length;
          const subjectPercentage = (subjectPresent / records.length) * 100;
          if (subjectPercentage < threshold) {
            subjectsAtRisk.push(records[0].subject_code);
          }
        });

        if (currentPercentage < 50) riskFactors.push('Critical attendance level');
        if (recentPercentage < currentPercentage) riskFactors.push('Declining recent attendance');
        if (subjectsAtRisk.length > 1) riskFactors.push('Multiple subjects affected');

        // Generate recommendations
        const recommendedActions: string[] = [];
        if (currentPercentage < 50) {
          recommendedActions.push('Immediate intervention required');
          recommendedActions.push('Schedule one-on-one meeting');
        }
        if (recentPercentage < 60) {
          recommendedActions.push('Monitor daily attendance');
        }
        if (subjectsAtRisk.length > 0) {
          recommendedActions.push('Focus on specific subjects');
        }
        recommendedActions.push('Contact student support services');

        insights.push({
          student_usn: usn,
          student_name: student.student_name,
          current_percentage: Math.round(currentPercentage * 100) / 100,
          predicted_percentage: Math.round(predictedPercentage * 100) / 100,
          risk_factors: riskFactors,
          recommended_actions: recommendedActions,
          subjects_at_risk: subjectsAtRisk
        });
      }
    });

    return insights.sort((a, b) => a.current_percentage - b.current_percentage);
  }
}
