import { supabase } from "@/integrations/supabase/client";

// Types for Leave Notification System
export interface LeaveNotification {
  id: string;
  leave_request_id: string;
  hod_id: string;
  department: string;
  notification_type: 'new_request' | 'request_updated' | 'request_cancelled';
  is_read: boolean;
  is_dismissed: boolean;
  faculty_name: string;
  leave_type: string;
  start_date: string;
  end_date: string;
  total_days: number;
  created_at: string;
  read_at?: string;
  dismissed_at?: string;
}

export interface NotificationStats {
  total_notifications: number;
  unread_notifications: number;
  new_requests: number;
  urgent_requests: number;
}

/**
 * Service to handle leave request notifications for HODs
 */
export class LeaveNotificationService {
  
  /**
   * Get all notifications for a HOD
   */
  static async getNotificationsForHOD(
    hodId: string,
    filters?: {
      is_read?: boolean;
      is_dismissed?: boolean;
      notification_type?: string;
      limit?: number;
    }
  ): Promise<LeaveNotification[]> {
    try {
      let query = supabase
        .from('leave_notifications')
        .select('*')
        .eq('hod_id', hodId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters?.is_read !== undefined) {
        query = query.eq('is_read', filters.is_read);
      }
      if (filters?.is_dismissed !== undefined) {
        query = query.eq('is_dismissed', filters.is_dismissed);
      }
      if (filters?.notification_type) {
        query = query.eq('notification_type', filters.notification_type);
      }
      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching notifications for HOD:', error);

      // Handle case where table doesn't exist
      if (error && typeof error === 'object' && 'code' in error && error.code === '42P01') {
        console.warn('Leave notifications table does not exist. Returning empty array.');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get unread notification count for HOD
   */
  static async getUnreadNotificationCount(hodId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('leave_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('hod_id', hodId)
        .eq('is_read', false)
        .eq('is_dismissed', false);

      if (error) throw error;
      return count || 0;
    } catch (error) {
      console.error('Error fetching unread notification count:', error);

      // Handle case where table doesn't exist
      if (error && typeof error === 'object' && 'code' in error && error.code === '42P01') {
        console.warn('Leave notifications table does not exist. Returning 0 count.');
        return 0;
      }

      return 0;
    }
  }

  /**
   * Get notification statistics for HOD dashboard
   */
  static async getNotificationStats(hodId: string): Promise<NotificationStats> {
    try {
      const [totalResult, unreadResult, newRequestsResult] = await Promise.all([
        // Total notifications
        supabase
          .from('leave_notifications')
          .select('*', { count: 'exact', head: true })
          .eq('hod_id', hodId),
        
        // Unread notifications
        supabase
          .from('leave_notifications')
          .select('*', { count: 'exact', head: true })
          .eq('hod_id', hodId)
          .eq('is_read', false)
          .eq('is_dismissed', false),
        
        // New requests (last 24 hours)
        supabase
          .from('leave_notifications')
          .select('*', { count: 'exact', head: true })
          .eq('hod_id', hodId)
          .eq('notification_type', 'new_request')
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      ]);

      // Calculate urgent requests (requests with start date within 7 days)
      const urgentDate = new Date();
      urgentDate.setDate(urgentDate.getDate() + 7);
      
      const { count: urgentCount } = await supabase
        .from('leave_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('hod_id', hodId)
        .eq('is_dismissed', false)
        .lte('start_date', urgentDate.toISOString().split('T')[0]);

      return {
        total_notifications: totalResult.count || 0,
        unread_notifications: unreadResult.count || 0,
        new_requests: newRequestsResult.count || 0,
        urgent_requests: urgentCount || 0
      };
    } catch (error) {
      console.error('Error fetching notification stats:', error);
      return {
        total_notifications: 0,
        unread_notifications: 0,
        new_requests: 0,
        urgent_requests: 0
      };
    }
  }

  /**
   * Mark notification as read
   */
  static async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('leave_notifications')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', notificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark multiple notifications as read
   */
  static async markMultipleNotificationsAsRead(notificationIds: string[]): Promise<void> {
    try {
      const { error } = await supabase
        .from('leave_notifications')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .in('id', notificationIds);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking multiple notifications as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a HOD
   */
  static async markAllNotificationsAsRead(hodId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('leave_notifications')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('hod_id', hodId)
        .eq('is_read', false);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Dismiss notification
   */
  static async dismissNotification(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('leave_notifications')
        .update({
          is_dismissed: true,
          dismissed_at: new Date().toISOString()
        })
        .eq('id', notificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error dismissing notification:', error);
      throw error;
    }
  }

  /**
   * Subscribe to real-time notification updates
   */
  static subscribeToNotifications(
    hodId: string,
    onNotificationUpdate: (payload: any) => void
  ) {
    const channel = supabase
      .channel('leave_notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'leave_notifications',
          filter: `hod_id=eq.${hodId}`
        },
        onNotificationUpdate
      )
      .subscribe();

    return channel;
  }

  /**
   * Unsubscribe from real-time notifications
   */
  static unsubscribeFromNotifications(channel: any) {
    if (channel) {
      supabase.removeChannel(channel);
    }
  }

  /**
   * Get notification details with leave request information
   */
  static async getNotificationWithLeaveDetails(notificationId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('leave_notifications')
        .select(`
          *,
          leave_requests (
            id,
            faculty_id,
            leave_type,
            start_date,
            end_date,
            total_days,
            reason,
            status,
            applied_date,
            academic_year
          )
        `)
        .eq('id', notificationId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching notification with leave details:', error);
      throw error;
    }
  }

  /**
   * Format notification for display
   */
  static formatNotificationMessage(notification: LeaveNotification): string {
    const startDate = new Date(notification.start_date).toLocaleDateString();
    const endDate = new Date(notification.end_date).toLocaleDateString();
    
    switch (notification.notification_type) {
      case 'new_request':
        return `${notification.faculty_name} has requested ${notification.total_days} day(s) of ${notification.leave_type.replace('_', ' ')} from ${startDate} to ${endDate}`;
      case 'request_updated':
        return `${notification.faculty_name} has updated their leave request for ${notification.leave_type.replace('_', ' ')}`;
      case 'request_cancelled':
        return `${notification.faculty_name} has cancelled their leave request for ${notification.leave_type.replace('_', ' ')}`;
      default:
        return `New notification from ${notification.faculty_name}`;
    }
  }

  /**
   * Check if notification is urgent (leave starts within 3 days)
   */
  static isNotificationUrgent(notification: LeaveNotification): boolean {
    const startDate = new Date(notification.start_date);
    const today = new Date();
    const diffTime = startDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays <= 3 && diffDays >= 0;
  }
}
