/**
 * Add Sample IA Marks Data for Student Progress Testing
 * This script adds sample IA marks for student 1KS23CS001
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://milmyotuougemocvieof.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';
const supabase = createClient(supabaseUrl, supabaseKey);

async function addSampleIAMarks() {
  console.log('🎯 Adding sample IA marks for student 1KS23CS001...');

  const studentId = '4b4b0737-262f-4bc5-bc11-a17b29b4ee54'; // From your logs
  
  const sampleIAMarks = [
    {
      student_id: studentId,
      subject_code: 'BCS401',
      department: 'cse',
      semester: '4',
      section: 'A',
      academic_year: '2024-25',
      ia1_marks: 22,
      ia2_marks: 20,
      ia3_marks: 24,
      assignment_marks: 8,
      lab_marks: null
    },
    {
      student_id: studentId,
      subject_code: 'BCS402',
      department: 'cse',
      semester: '4',
      section: 'A',
      academic_year: '2024-25',
      ia1_marks: 18,
      ia2_marks: 21,
      ia3_marks: 19,
      assignment_marks: 7,
      lab_marks: null
    },
    {
      student_id: studentId,
      subject_code: 'BCS403',
      department: 'cse',
      semester: '4',
      section: 'A',
      academic_year: '2024-25',
      ia1_marks: 25,
      ia2_marks: 23,
      ia3_marks: 22,
      assignment_marks: 9,
      lab_marks: null
    },
    {
      student_id: studentId,
      subject_code: 'BCSL404',
      department: 'cse',
      semester: '4',
      section: 'A',
      academic_year: '2024-25',
      ia1_marks: 20,
      ia2_marks: 22,
      ia3_marks: 21,
      assignment_marks: 8,
      lab_marks: 18
    },
    {
      student_id: studentId,
      subject_code: 'BCS405A',
      department: 'cse',
      semester: '4',
      section: 'A',
      academic_year: '2024-25',
      ia1_marks: 16,
      ia2_marks: 19,
      ia3_marks: 17,
      assignment_marks: 6,
      lab_marks: null
    },
    {
      student_id: studentId,
      subject_code: 'BBOK407',
      department: 'cse',
      semester: '4',
      section: 'A',
      academic_year: '2024-25',
      ia1_marks: 21,
      ia2_marks: 20,
      ia3_marks: 23,
      assignment_marks: 9,
      lab_marks: null
    },
    {
      student_id: studentId,
      subject_code: 'BCS456C',
      department: 'cse',
      semester: '4',
      section: 'A',
      academic_year: '2024-25',
      ia1_marks: 19,
      ia2_marks: 18,
      ia3_marks: 20,
      assignment_marks: 7,
      lab_marks: null
    },
    {
      student_id: studentId,
      subject_code: 'BUHK408',
      department: 'cse',
      semester: '4',
      section: 'A',
      academic_year: '2024-25',
      ia1_marks: 24,
      ia2_marks: 25,
      ia3_marks: 23,
      assignment_marks: 10,
      lab_marks: null
    }
  ];

  try {
    // Insert the sample data
    const { data, error } = await supabase
      .from('internal_assessments')
      .insert(sampleIAMarks)
      .select();

    if (error) {
      console.error('❌ Error inserting sample IA marks:', error);
      return;
    }

    console.log(`✅ Successfully inserted ${data.length} IA mark records`);

    // Verify the data
    const { data: verifyData, error: verifyError } = await supabase
      .from('internal_assessments')
      .select('subject_code, ia1_marks, ia2_marks, ia3_marks, assignment_marks, lab_marks')
      .eq('student_id', studentId)
      .order('subject_code');

    if (verifyError) {
      console.error('❌ Error verifying data:', verifyError);
      return;
    }

    console.log('\n📊 Inserted IA marks data:');
    console.table(verifyData);

    console.log('\n🎉 Sample IA marks data has been successfully added!');
    console.log('💡 Now refresh the Student Progress card to see the IA marks.');

  } catch (error) {
    console.error('❌ Exception:', error);
  }
}

// Run the script
addSampleIAMarks();
