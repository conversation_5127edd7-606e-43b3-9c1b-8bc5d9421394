/**
 * Comprehensive test script to verify IA marks integration fix
 * Tests the complete data flow from faculty entry to student progress display
 */

import { StudentProgressService } from '../services/StudentProgressService.js';

// Test configuration
const TEST_CONFIG = {
  studentUsn: '1KS23CS001', // Student with missing BCS402 IA marks
  academicYear: '2024-25'
};

async function testIAIntegrationFix() {
  console.log('🧪 Testing IA Marks Integration Fix\n');
  console.log('Test Configuration:', TEST_CONFIG);
  console.log('='.repeat(60));

  try {
    console.log('\n📋 Step 1: Loading Student Progress Card');
    const progressData = await StudentProgressService.getStudentProgressCard(
      TEST_CONFIG.studentUsn,
      TEST_CONFIG.academicYear
    );

    if (!progressData) {
      console.log('❌ No progress data returned');
      return;
    }

    console.log('\n✅ Student Progress Data Loaded:');
    console.log(`Student: ${progressData.basic_info.student_name}`);
    console.log(`Department: ${progressData.basic_info.department}`);
    console.log(`Semester: ${progressData.basic_info.semester}`);
    console.log(`Section: ${progressData.basic_info.section}`);
    console.log(`Academic Year: ${progressData.basic_info.academic_year}`);

    console.log('\n📊 IA Marks Analysis:');
    console.log(`Total subjects with IA marks: ${progressData.ia_marks.length}`);

    if (progressData.ia_marks.length === 0) {
      console.log('❌ No IA marks found - integration still not working');
      return;
    }

    // Check for specific subjects
    const criticalSubjects = ['BCS401', 'BCS402', 'BCS403'];

    console.log('\n🔍 Critical Subjects Analysis:');
    criticalSubjects.forEach(subjectCode => {
      const record = progressData.ia_marks.find(r => r.subject_code === subjectCode);

      if (record) {
        const hasMarks = record.ia1_marks || record.ia2_marks || record.ia3_marks || record.assignment_marks;
        const status = hasMarks ? '✅' : '⚠️';
        console.log(`${status} ${subjectCode}: "${record.subject_name}"`);
        console.log(`    IA1: ${record.ia1_marks || 'N/A'}, IA2: ${record.ia2_marks || 'N/A'}, IA3: ${record.ia3_marks || 'N/A'}, Assignment: ${record.assignment_marks || 'N/A'}`);

        if (record.subject_name === 'Unknown Subject') {
          console.log(`    ⚠️ Subject name issue detected for ${subjectCode}`);
        }
      } else {
        console.log(`❌ ${subjectCode}: Not found in IA marks`);
      }
    });

    // Check for BCS402 specifically (the reported issue)
    console.log('\n🎯 BCS402 (Microcontrollers) Specific Check:');
    const bcs402Record = progressData.ia_marks.find(r => r.subject_code === 'BCS402');

    if (bcs402Record) {
      const hasMarks = bcs402Record.ia1_marks || bcs402Record.ia2_marks || bcs402Record.ia3_marks || bcs402Record.assignment_marks;

      if (hasMarks) {
        console.log('✅ BCS402 IA marks are now showing up!');
        console.log(`   Subject Name: "${bcs402Record.subject_name}"`);
        console.log(`   IA1: ${bcs402Record.ia1_marks}`);
        console.log(`   IA2: ${bcs402Record.ia2_marks}`);
        console.log(`   IA3: ${bcs402Record.ia3_marks}`);
        console.log(`   Assignment: ${bcs402Record.assignment_marks}`);
        console.log(`   Total: ${bcs402Record.total_marks}`);
        console.log(`   Grade: ${bcs402Record.grade}`);
      } else {
        console.log('⚠️ BCS402 found but no marks present');
      }
    } else {
      console.log('❌ BCS402 still not found in IA marks');
    }

    // Subject name verification
    console.log('\n📚 Subject Name Verification:');
    const unknownSubjects = progressData.ia_marks.filter(r => r.subject_name === 'Unknown Subject');

    if (unknownSubjects.length === 0) {
      console.log('✅ All subjects have proper names from timetable data');
    } else {
      console.log(`⚠️ ${unknownSubjects.length} subjects still showing as "Unknown Subject":`);
      unknownSubjects.forEach(subject => {
        console.log(`   - ${subject.subject_code}`);
      });
    }

    // Data source analysis
    console.log('\n🔍 Data Source Analysis:');
    progressData.ia_marks.forEach(record => {
      const source = record.source || 'unknown';
      console.log(`${record.subject_code}: ${source}`);
    });

    // Overall integration status
    console.log('\n📈 Integration Status Summary:');
    const totalSubjects = progressData.ia_marks.length;
    const subjectsWithMarks = progressData.ia_marks.filter(r =>
      r.ia1_marks || r.ia2_marks || r.ia3_marks || r.assignment_marks
    ).length;
    const subjectsWithProperNames = progressData.ia_marks.filter(r =>
      r.subject_name !== 'Unknown Subject'
    ).length;

    console.log(`📊 Subjects with IA marks: ${subjectsWithMarks}/${totalSubjects}`);
    console.log(`📊 Subjects with proper names: ${subjectsWithProperNames}/${totalSubjects}`);

    if (subjectsWithMarks > 0) {
      console.log('✅ IA marks integration is working!');
    } else {
      console.log('❌ IA marks integration still has issues');
    }

    if (subjectsWithProperNames === totalSubjects) {
      console.log('✅ Subject name lookup is working perfectly!');
    } else {
      console.log('⚠️ Some subject names still need fixing');
    }

    // Attendance verification
    console.log('\n📊 Attendance Data Verification:');
    console.log(`Total subjects with attendance: ${progressData.attendance.length}`);

    const bcs402Attendance = progressData.attendance.find(r => r.subject_code === 'BCS402');
    if (bcs402Attendance) {
      console.log(`✅ BCS402 Attendance: "${bcs402Attendance.subject_name}" - ${bcs402Attendance.percentage}%`);
    } else {
      console.log('❌ BCS402 attendance data not found');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testIAIntegrationFix();
