
import React, { useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";

interface FileDropzoneProps {
  onFileDrop: (file: File) => void;
  isUploading: boolean;
}

export default function FileDropzone({ onFileDrop, isUploading }: FileDropzoneProps) {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;
    onFileDrop(acceptedFiles[0]);
  }, [onFileDrop]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    disabled: isUploading,
    maxSize: 5 * 1024 * 1024 // 5MB
  });

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
        isDragActive ? "bg-muted border-primary" : "border-border hover:border-muted-foreground cursor-pointer"
      }`}
    >
      <input {...getInputProps()} />
      <div className="flex flex-col items-center">
        <Upload className="h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-sm font-medium mb-1">
          {isDragActive ? "Drop file here..." : "Drag and drop your file here"}
        </p>
        <p className="text-xs text-muted-foreground mb-4">
          or browse to upload (Excel/CSV format)
        </p>
        <Button variant="secondary">
          Select File
        </Button>
      </div>
    </div>
  );
}
