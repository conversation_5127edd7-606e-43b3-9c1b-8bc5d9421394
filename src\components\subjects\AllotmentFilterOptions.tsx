
import React from "react";

interface AllotmentFilterOptionsProps {
  yearsList: string[];
  deptsList: { id: string; name: string }[];
  semsList: string[];
  sectionsList: string[];
}

export const AllotmentFilterOptions: React.FC<AllotmentFilterOptionsProps> = ({
  yearsList,
  deptsList,
  semsList,
  sectionsList
}) => {
  return (
    <React.Fragment>
      {/* Component doesn't render UI but provides filter options */}
    </React.Fragment>
  );
};

export const defaultFilterOptions = {
  yearsList: ["2024-2025", "2023-2024", "2022-2023"], // Current academic year first
  deptsList: [
    { id: "cse", name: "Computer Science" },
    { id: "ece", name: "Electronics & Communication" },
    { id: "mech", name: "Mechanical Engineering" },
    { id: "eee", name: "Electrical & Electronics" },
  ],
  semsList: ["1", "2", "3", "4", "5", "6", "7", "8"],
  sectionsList: ["A", "B", "C"]
};
