import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Settings, TestTube, CheckCircle, AlertCircle } from "lucide-react";
import { setupSemesterConfigurations, testSemesterConfiguration } from "@/utils/setupSemesterConfigurations";

export const SetupLabConfigurationsButton: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [setupComplete, setSetupComplete] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);

  const handleSetup = async () => {
    setIsLoading(true);
    try {
      const result = await setupSemesterConfigurations();
      
      if (result.success) {
        setSetupComplete(true);
        setTestResults(result);
        toast({
          title: "Setup Complete",
          description: "Semester lab configurations have been created successfully.",
        });
      } else {
        toast({
          title: "Setup Failed",
          description: "Failed to create semester configurations. Check console for details.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Setup error:', error);
      toast({
        title: "Error",
        description: "An error occurred during setup.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTest = async () => {
    setIsLoading(true);
    try {
      // Test multiple semesters
      const tests = await Promise.all([
        testSemesterConfiguration("2024-2025", "cse", "2"),
        testSemesterConfiguration("2024-2025", "cse", "4"),
        testSemesterConfiguration("2024-2025", "cse", "6"),
      ]);

      console.log('Test results:', tests);
      
      toast({
        title: "Test Complete",
        description: "Check console for detailed test results.",
      });
    } catch (error) {
      console.error('Test error:', error);
      toast({
        title: "Test Failed",
        description: "Failed to test configurations.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Lab Duration System Setup
        </CardTitle>
        <CardDescription>
          Set up semester-specific lab duration configurations for testing the flexible lab system
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-4">
          <Button 
            onClick={handleSetup} 
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            {isLoading ? "Setting up..." : "Setup Configurations"}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleTest} 
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <TestTube className="h-4 w-4" />
            {isLoading ? "Testing..." : "Test Configurations"}
          </Button>
        </div>

        {setupComplete && (
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">Setup completed successfully!</span>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Configurations Created:</h4>
              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center justify-between p-2 border rounded">
                  <span>2nd Semester</span>
                  <Badge variant="secondary">2-hour labs</Badge>
                </div>
                <div className="flex items-center justify-between p-2 border rounded">
                  <span>4th Semester</span>
                  <Badge variant="default">3-hour labs</Badge>
                </div>
                <div className="flex items-center justify-between p-2 border rounded">
                  <span>6th Semester</span>
                  <Badge variant="secondary">2-hour labs</Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            Testing Instructions:
          </h4>
          <ol className="text-sm text-blue-800 space-y-1">
            <li>1. Click "Setup Configurations" to create test data</li>
            <li>2. Go to Subject Allotment</li>
            <li>3. Select 2nd or 6th semester (2-hour labs)</li>
            <li>4. Add a lab subject and check Time of Day dropdown</li>
            <li>5. Should show: Morning Session (2h), Mid Session (2h), Early Afternoon Session (2h)</li>
            <li>6. Select 4th semester (3-hour labs)</li>
            <li>7. Should show: Morning Session (3h), Afternoon Session (3h)</li>
          </ol>
        </div>

        <div className="p-3 bg-gray-50 rounded text-sm">
          <strong>Console Commands:</strong><br/>
          You can also run these in the browser console:<br/>
          <code>quickSetup()</code> - Quick setup<br/>
          <code>testSemesterConfiguration("2024-2025", "cse", "6")</code> - Test specific semester
        </div>
      </CardContent>
    </Card>
  );
};
