import { supabase } from "@/integrations/supabase/client";

export interface ClassStudent {
  id: string;
  usn: string;
  student_name: string;
  email?: string;
  student_mobile?: string;
  father_mobile?: string;
  mother_mobile?: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  class_teacher_id?: string;
  uploaded_by?: string;
  created_at: string;
  updated_at: string;
}

export interface ClassTeacherAssignment {
  department: string;
  semester: string;
  section: string;
  academic_year: string;
}

export class ClassStudentService {
  /**
   * Map department names from class_teachers table to class_students table format
   */
  private static mapDepartmentName(fullDepartmentName: string): string[] {
    const departmentMap: Record<string, string[]> = {
      'Computer Science and Engineering': ['CSE', 'cse', 'Computer Science and Engineering'],
      'Information Science and Engineering': ['ISE', 'ise', 'Information Science and Engineering'],
      'Electronics and Communication Engineering': ['ECE', 'ece', 'Electronics and Communication Engineering'],
      'Mechanical Engineering': ['MECH', 'mech', 'Mechanical Engineering'],
      'Civil Engineering': ['CIVIL', 'civil', 'Civil Engineering'],
      'Electrical and Electronics Engineering': ['EEE', 'eee', 'Electrical and Electronics Engineering']
    };

    return departmentMap[fullDepartmentName] || [fullDepartmentName, fullDepartmentName.toLowerCase(), fullDepartmentName.toUpperCase()];
  }

  /**
   * Get class teacher assignment for a faculty member
   */
  static async getClassTeacherAssignment(
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<ClassTeacherAssignment | null> {
    try {
      const { data, error } = await supabase
        .from('class_teachers')
        .select('department, semester, section, academic_year')
        .eq('faculty_id', facultyId)
        .eq('academic_year', academicYear)
        .single();

      if (error || !data) {
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching class teacher assignment:', error);
      return null;
    }
  }

  /**
   * Get students for class teacher's assigned class
   */
  static async getStudentsForClassTeacher(
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<ClassStudent[]> {
    try {
      // First get the class teacher assignment
      const assignment = await this.getClassTeacherAssignment(facultyId, academicYear);

      if (!assignment) {
        throw new Error('No class teacher assignment found');
      }

      console.log('🔍 ClassStudentService: Assignment found:', assignment);

      // Get possible department name variations
      const departmentVariations = this.mapDepartmentName(assignment.department);
      console.log('🔍 ClassStudentService: Department variations to try:', departmentVariations);

      let students: ClassStudent[] = [];
      let lastError = null;

      // Try each department variation until we find students
      for (const deptVariation of departmentVariations) {
        console.log(`🔍 ClassStudentService: Trying department: "${deptVariation}"`);

        const { data, error } = await supabase
          .from('class_students')
          .select(`
            id,
            usn,
            student_name,
            email,
            student_mobile,
            father_mobile,
            mother_mobile,
            department,
            semester,
            section,
            academic_year,
            class_teacher_id,
            uploaded_by,
            created_at,
            updated_at
          `)
          .eq('department', deptVariation)
          .eq('semester', assignment.semester)
          .eq('section', assignment.section)
          .eq('academic_year', assignment.academic_year)
          .order('usn');

        if (error) {
          console.warn(`⚠️ ClassStudentService: Error with department "${deptVariation}":`, error);
          lastError = error;
          continue;
        }

        if (data && data.length > 0) {
          console.log(`✅ ClassStudentService: Found ${data.length} students with department: "${deptVariation}"`);
          students = data;
          break;
        } else {
          console.log(`❌ ClassStudentService: No students found with department: "${deptVariation}"`);
        }
      }

      if (students.length === 0 && lastError) {
        console.error('❌ ClassStudentService: No students found with any department variation. Last error:', lastError);
        throw lastError;
      }

      console.log(`✅ ClassStudentService: Returning ${students.length} students`);
      return students;
    } catch (error) {
      console.error('Error fetching students for class teacher:', error);
      throw error;
    }
  }

  /**
   * Delete a single student
   */
  static async deleteStudent(
    studentId: string,
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Verify class teacher assignment
      const assignment = await this.getClassTeacherAssignment(facultyId, academicYear);

      if (!assignment) {
        throw new Error('You are not assigned as a class teacher');
      }

      // Get the student to verify they belong to the class teacher's class
      const { data: student, error: fetchError } = await supabase
        .from('class_students')
        .select('id, usn, student_name, department, semester, section')
        .eq('id', studentId)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      // Verify student belongs to the class teacher's assigned class
      const departmentVariations = this.mapDepartmentName(assignment.department);
      const isValidDepartment = departmentVariations.includes(student.department);

      if (!student ||
          !isValidDepartment ||
          student.semester !== assignment.semester ||
          student.section !== assignment.section) {
        throw new Error('You can only delete students from your assigned class');
      }

      // Perform the deletion
      const { error: deleteError } = await supabase
        .from('class_students')
        .delete()
        .eq('id', studentId);

      if (deleteError) {
        throw deleteError;
      }

      return { success: true };
    } catch (error) {
      console.error('Error deleting student:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Update student information
   */
  static async updateStudent(
    studentId: string,
    studentData: Partial<ClassStudent>,
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<{ success: boolean; student?: ClassStudent; error?: string }> {
    try {
      // Verify class teacher assignment
      const assignment = await this.getClassTeacherAssignment(facultyId, academicYear);

      if (!assignment) {
        throw new Error('You are not assigned as a class teacher');
      }

      // Get the student to verify they belong to the class teacher's class
      const { data: existingStudent, error: fetchError } = await supabase
        .from('class_students')
        .select('id, usn, student_name, department, semester, section')
        .eq('id', studentId)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      // Verify student belongs to the class teacher's assigned class
      const departmentVariations = this.mapDepartmentName(assignment.department);
      const isValidDepartment = departmentVariations.includes(existingStudent.department);

      if (!existingStudent ||
          !isValidDepartment ||
          existingStudent.semester !== assignment.semester ||
          existingStudent.section !== assignment.section) {
        throw new Error('You can only edit students from your assigned class');
      }

      // Update the student
      const { data: updatedStudent, error: updateError } = await supabase
        .from('class_students')
        .update({
          student_name: studentData.student_name,
          email: studentData.email,
          student_mobile: studentData.student_mobile,
          father_mobile: studentData.father_mobile,
          mother_mobile: studentData.mother_mobile,
          updated_at: new Date().toISOString()
        })
        .eq('id', studentId)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      return { success: true, student: updatedStudent };
    } catch (error) {
      console.error('Error updating student:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Delete selected students (bulk delete)
   */
  static async deleteStudents(
    studentIds: string[],
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<{ success: number; errors: string[] }> {
    try {
      // Verify class teacher assignment
      const assignment = await this.getClassTeacherAssignment(facultyId, academicYear);
      
      if (!assignment) {
        throw new Error('You are not assigned as a class teacher');
      }

      // Get the students to verify they belong to the class teacher's class
      const { data: studentsToDelete, error: fetchError } = await supabase
        .from('class_students')
        .select('id, usn, student_name, department, semester, section')
        .in('id', studentIds);

      if (fetchError) {
        throw fetchError;
      }

      // Verify all students belong to the class teacher's assigned class
      const departmentVariations = this.mapDepartmentName(assignment.department);
      const invalidStudents = studentsToDelete?.filter(student =>
        !departmentVariations.includes(student.department) ||
        student.semester !== assignment.semester ||
        student.section !== assignment.section
      ) || [];

      if (invalidStudents.length > 0) {
        throw new Error('You can only delete students from your assigned class');
      }

      // Perform the deletion
      const { error: deleteError } = await supabase
        .from('class_students')
        .delete()
        .in('id', studentIds);

      if (deleteError) {
        throw deleteError;
      }

      return {
        success: studentIds.length,
        errors: []
      };
    } catch (error) {
      console.error('Error deleting students:', error);
      return {
        success: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Delete all students from class teacher's assigned class
   */
  static async deleteAllStudents(
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<{ success: number; errors: string[] }> {
    try {
      // Verify class teacher assignment
      const assignment = await this.getClassTeacherAssignment(facultyId, academicYear);
      
      if (!assignment) {
        throw new Error('You are not assigned as a class teacher');
      }

      // Get possible department name variations
      const departmentVariations = this.mapDepartmentName(assignment.department);

      let totalCount = 0;
      let deleteError = null;

      // Try each department variation
      for (const deptVariation of departmentVariations) {
        // Get count of students to be deleted
        const { count, error: countError } = await supabase
          .from('class_students')
          .select('*', { count: 'exact', head: true })
          .eq('department', deptVariation)
          .eq('semester', assignment.semester)
          .eq('section', assignment.section)
          .eq('academic_year', assignment.academic_year);

        if (countError) {
          console.warn(`⚠️ Count error for department "${deptVariation}":`, countError);
          continue;
        }

        if (count && count > 0) {
          totalCount = count;

          // Delete all students from the class
          const { error: delError } = await supabase
            .from('class_students')
            .delete()
            .eq('department', deptVariation)
            .eq('semester', assignment.semester)
            .eq('section', assignment.section)
            .eq('academic_year', assignment.academic_year);

          if (delError) {
            deleteError = delError;
          } else {
            break; // Successfully deleted, exit loop
          }
        }
      }

      if (deleteError) {
        throw deleteError;
      }

      if (deleteError) {
        throw deleteError;
      }

      return {
        success: totalCount,
        errors: []
      };
    } catch (error) {
      console.error('Error deleting all students:', error);
      return {
        success: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Get student count for class teacher's assigned class
   */
  static async getStudentCount(
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<number> {
    try {
      const assignment = await this.getClassTeacherAssignment(facultyId, academicYear);
      
      if (!assignment) {
        return 0;
      }

      // Get possible department name variations
      const departmentVariations = this.mapDepartmentName(assignment.department);

      // Try each department variation until we find students
      for (const deptVariation of departmentVariations) {
        const { count, error } = await supabase
          .from('class_students')
          .select('*', { count: 'exact', head: true })
          .eq('department', deptVariation)
          .eq('semester', assignment.semester)
          .eq('section', assignment.section)
          .eq('academic_year', assignment.academic_year);

        if (error) {
          console.warn(`⚠️ Count error for department "${deptVariation}":`, error);
          continue;
        }

        if (count && count > 0) {
          return count;
        }
      }

      return 0;
    } catch (error) {
      console.error('Error getting student count:', error);
      return 0;
    }
  }
}
