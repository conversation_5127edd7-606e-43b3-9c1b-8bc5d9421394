
import CollegeAdminDashboard from "@/components/dashboard/CollegeAdminDashboard";
import TimetableCoordinatorDashboard from "@/components/dashboard/TimetableCoordinatorDashboard";
import { useUserRole } from "@/hooks/useUserRole";
import { Navigate } from "react-router-dom";

const Dashboard = () => {
  const { userRoles, loading, isCollegeAdmin, isTimetableCoordinator, isFaculty, isHOD, isPrincipal } = useUserRole();

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4 animate-fade-in">
          <div className="loading-spinner h-12 w-12"></div>
          <p className="text-muted-foreground text-sm animate-pulse">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Render role-specific dashboard based on priority
  // Priority: college_admin > timetable_coordinator > principal > hod > faculty
  if (isCollegeAdmin) {
    return <CollegeAdminDashboard />;
  }

  if (isTimetableCoordinator) {
    return <TimetableCoordinatorDashboard />;
  }

  // Principal users get their own dashboard (or redirect to appropriate page)
  if (isPrincipal) {
    return <Navigate to="/hod-leave-approvals" replace />;
  }

  // HOD users get redirected to leave approvals (their main function)
  if (isHOD) {
    return <Navigate to="/leave-approvals" replace />;
  }

  // Faculty users are redirected to Analytics Dashboard
  if (isFaculty) {
    return <Navigate to="/faculty-analytics" replace />;
  }

  // Default dashboard for unknown roles
  return (
    <div className="container-modern space-responsive animate-fade-in">
      <div className="page-header">
        <h2 className="page-title">Dashboard</h2>
        <p className="page-description">
          Welcome to the Timetable Management System
        </p>
      </div>
      <div className="card-modern p-6 border-warning/20 bg-warning/5">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-warning/20 flex items-center justify-center">
            <svg className="w-5 h-5 text-warning" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="flex-1 space-y-2">
            <h3 className="text-lg font-semibold text-warning-foreground">Role Not Assigned</h3>
            <p className="text-warning-foreground/80">
              Your account doesn't have a valid role assigned. Please contact your administrator to assign you a proper role.
            </p>
            <p className="text-warning-foreground/70 text-sm">
              Current roles: <span className="font-medium">{userRoles.length > 0 ? userRoles.join(', ') : 'None'}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
