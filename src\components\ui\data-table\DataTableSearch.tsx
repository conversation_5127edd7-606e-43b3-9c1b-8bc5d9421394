
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface DataTableSearchProps {
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  setCurrentPage: (page: number) => void;
}

export function DataTableSearch({
  searchQuery,
  setSearchQuery,
  setCurrentPage,
}: DataTableSearchProps) {
  return (
    <div className="relative flex-1">
      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <Input
        placeholder="Search..."
        className="pl-8"
        value={searchQuery}
        onChange={(e) => {
          setSearchQuery(e.target.value);
          setCurrentPage(1);
        }}
      />
    </div>
  );
}
