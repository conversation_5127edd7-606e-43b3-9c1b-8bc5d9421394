
// src/components/subjects/SubjectAllotment.tsx
import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import SubjectFilters from "./SubjectFilters";
import ManualEntryForm from "./ManualEntryForm";
import MappingsTable from "./MappingsTable";
import { defaultFilterOptions } from "./AllotmentFilterOptions";
import { useFacultyLoader } from "./useFacultyLoader";
import { useSubjectLoader } from "./useSubjectLoader";
import { useMappingsManager } from "./useMappingsManager";
import { useRefreshMappings } from "@/hooks/useRefreshMappings";
import { useClientSideDelete } from "@/hooks/useClientSideDelete";
import { MappingService } from "@/services/mapping";
import PublishButton from "./allotment/PublishButton";

export type MappingRow = {
  academic_year: string;
  department: string;
  semester: string;
  section: string;
  subject_code: string;
  subject_name: string;
  subject_type: "theory" | "laboratory" | "elective";
  faculty1: string;
  faculty2?: string;
  hours_per_week: number;
  classroom: string;
  slots_per_week?: number;
};

// Helper function to get stored filter value
const getStoredFilter = (key: string, defaultValue: string) => {
  const stored = localStorage.getItem(`subjects-allotment-filter-${key}`);
  return stored || defaultValue;
};

export default function SubjectAllotment() {
  // 1️⃣ Filters with localStorage persistence
  const [year, setYear] = useState(() => getStoredFilter("year", ""));
  const [dept, setDept] = useState(() => getStoredFilter("dept", ""));
  const [sem, setSem] = useState(() => getStoredFilter("sem", ""));
  const [section, setSection] = useState(() => getStoredFilter("section", ""));
  const filtersValid = Boolean(year && dept && sem && section);

  // Save filters to localStorage when they change
  useEffect(() => {
    localStorage.setItem("subjects-allotment-filter-year", year);
    localStorage.setItem("subjects-allotment-filter-dept", dept);
    localStorage.setItem("subjects-allotment-filter-sem", sem);
    localStorage.setItem("subjects-allotment-filter-section", section);
  }, [year, dept, sem, section]);

  // Get filter options
  const filterOptions = defaultFilterOptions;

  // 2️⃣ Load faculty - ensure we have a valid array even if the hook returns undefined
  const facultyList = useFacultyLoader() || [];

  // 3️⃣ Load subjects when filters change - ensure we have a valid array even if the hook returns undefined
  const subjects = useSubjectLoader({
    year,
    dept,
    sem,
    section,
    filtersValid
  }) || [];

  // 4️⃣ Manage mappings - ensure we have a valid array even if the hook returns undefined
  const { mappings: serverMappings = [], loadMappings } = useMappingsManager({
    year,
    dept,
    sem,
    section,
    filtersValid,
    facultyList
  });

  // Use the refreshMappings hook
  const refreshMappings = useRefreshMappings({
    loadMappings,
    filtersValid,
    year,
    dept,
    sem,
    section
  });

  // Use client-side delete hook to handle cases where server-side deletion fails
  const { items: mappings, handleDelete } = useClientSideDelete(
    serverMappings,
    MappingService.deleteSubjectMapping
  );

  // Form open state
  const [formOpen, setFormOpen] = useState(false);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <SubjectFilters
        year={year}
        dept={dept}
        sem={sem}
        section={section}
        setYear={setYear}
        setDept={setDept}
        setSem={setSem}
        setSection={setSection}
        yearsList={filterOptions.yearsList}
        deptsList={filterOptions.deptsList}
        semsList={filterOptions.semsList}
        sectionsList={filterOptions.sectionsList}
      />

      {!filtersValid ? (
        <Card><CardContent className="text-center text-muted-foreground p-4">
          Please complete all filters above to proceed.
        </CardContent></Card>
      ) : (
        <>
          {/* Action Buttons */}
          <div className="flex flex-col md:flex-row justify-between gap-4">
            {/* Add Subject Button */}
            <Button
              onClick={() => setFormOpen(true)}
              className="md:w-auto"
            >
              Add Subject Mapping
            </Button>

            {/* Publish Button */}
            <PublishButton mappings={mappings} filters={{ year, dept, sem, section }} />
          </div>

          {/* Manual Entry Pane */}
          <ManualEntryForm
            subjects={subjects}
            facultyList={facultyList}
            onMappingSaved={refreshMappings}
            filtersValid={filtersValid}
            year={year}
            dept={dept}
            sem={sem}
            section={section}
            open={formOpen}
            closeDrawer={() => setFormOpen(false)}
            refetchMappings={refreshMappings}
          />
        </>
      )}

      {/* Current Mappings */}
      <MappingsTable
        mappings={mappings || []}
        onMappingDeleted={refreshMappings}
        onDelete={handleDelete}
      />
    </div>
  );
}
