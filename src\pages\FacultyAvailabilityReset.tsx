import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { FacultyAvailabilityService } from "@/services/FacultyAvailabilityService";
import { Loader2, Info, Search, X, ChevronLeft, ChevronRight, ChevronsUpDown, RefreshCw } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { RealtimeChannel } from "@supabase/supabase-js";

interface FacultyAvailability {
  id: string;
  full_name: string;
  vacant_by_day: Record<string, number> | null;
  vacant_count_by_day: Record<string, string[]> | null;
  isUpdated?: boolean; // Flag to indicate if this faculty's data was just updated
  lastUpdated?: number; // Timestamp of the last update
}

// Component to visualize availability as bars
const AvailabilityBars: React.FC<{
  day: string;
  availableSlots: number;
  maxSlots: number;
}> = ({ day, availableSlots, maxSlots }) => {
  // Determine color based on availability
  const getColor = (available: number, max: number) => {
    if (available === 0) return "bg-red-500 dark:bg-red-600";
    if (available === max) return "bg-green-500 dark:bg-green-600";
    return "bg-yellow-500 dark:bg-yellow-600";
  };

  // Create array of bars
  const bars = Array.from({ length: maxSlots }, (_, i) => {
    const isFilled = i < availableSlots;
    return (
      <div
        key={`${day}-bar-${i}`}
        className={cn(
          "w-3 h-5 rounded-sm mx-0.5",
          isFilled ? getColor(availableSlots, maxSlots) : "bg-muted"
        )}
        title={`${isFilled ? "Available" : "Unavailable"} slot ${i + 1}`}
      />
    );
  });

  return (
    <div className="flex items-center">
      <div className="w-20 font-medium text-sm">{day}:</div>
      <div className="flex">{bars}</div>
      <div className="ml-2 text-xs text-gray-500">
        {availableSlots}/{maxSlots}
      </div>
    </div>
  );
};

// Component to display a faculty member's availability
const FacultyAvailabilityCard: React.FC<{
  faculty: FacultyAvailability;
}> = ({ faculty }) => {
  const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];

  // All days have 7 slots
  const getMaxSlots = (day: string) => 7;

  // Get available slots for a day
  const getAvailableSlots = (day: string) => {
    if (!faculty.vacant_by_day) return 0;
    return faculty.vacant_by_day[day] || 0;
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">{faculty.full_name}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {days.map(day => (
            <AvailabilityBars
              key={`${faculty.id}-${day}`}
              day={day}
              availableSlots={getAvailableSlots(day)}
              maxSlots={getMaxSlots(day)}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const FacultyAvailabilityReset: React.FC = () => {
  const [facultyList, setFacultyList] = useState<FacultyAvailability[]>([]);
  const [filteredFacultyList, setFilteredFacultyList] = useState<FacultyAvailability[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isResetting, setIsResetting] = useState(false);
  const [isResettingLabs, setIsResettingLabs] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmLabResetDialogOpen, setConfirmLabResetDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [realtimeStatus, setRealtimeStatus] = useState<"connected" | "disconnected" | "error">("disconnected");
  const [lastUpdateTime, setLastUpdateTime] = useState<string | null>(null);
  const subscriptionRef = useRef<RealtimeChannel | null>(null);
  const itemsPerPage = 10;
  const { user } = useAuth();
  const navigate = useNavigate();

  // Check if user has permission to access admin settings
  useEffect(() => {
    async function checkUserRole() {
      if (!user?.id) return;

      const { data, error } = await supabase
        .from("employee_details")
        .select("roles")
        .eq("id", user.id)
        .single();

      if (error || !data || !data.roles || !data.roles.includes("timetable_coordinator")) {
        toast({
          description: "You don't have permission to view this page.",
          variant: "destructive",
        });
        navigate("/dashboard");
      }
    }

    checkUserRole();
  }, [user, navigate]);

  // Function to load faculty list with availability data
  const loadFacultyAvailability = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from("employee_details")
        .select("id, full_name, vacant_by_day, vacant_count_by_day")
        .filter("roles", "cs", "{faculty}")
        .order("full_name");

      if (error) {
        console.error("Error loading faculty availability:", error);
        toast({
          title: "Error",
          description: "Failed to load faculty availability data.",
          variant: "destructive",
        });
        return;
      }

      // Add timestamp to each faculty record
      const facultyWithTimestamp = (data || []).map(faculty => ({
        ...faculty,
        lastUpdated: Date.now()
      }));

      console.log("Loaded faculty data:", facultyWithTimestamp);

      // Preserve expanded items when updating the list
      setFacultyList(facultyWithTimestamp);

      // Update filtered list while preserving search
      if (searchQuery.trim() === "") {
        setFilteredFacultyList(facultyWithTimestamp);
      } else {
        const filtered = facultyWithTimestamp.filter(faculty =>
          faculty.full_name.toLowerCase().includes(searchQuery.toLowerCase())
        );
        setFilteredFacultyList(filtered);
      }
    } catch (error) {
      console.error("Exception loading faculty availability:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred while loading faculty data.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load faculty list and set up subscription on component mount
  useEffect(() => {
    // Initial data load
    loadFacultyAvailability().then(() => {
      // Set up real-time subscription after initial data load
      setupRealtimeSubscription();
    });

    // Cleanup subscription on component unmount
    return () => {
      if (subscriptionRef.current) {
        console.log("Cleaning up subscription");
        subscriptionRef.current.unsubscribe();
        setRealtimeStatus("disconnected");
      }
    };
  }, []);

  // Set up real-time subscription to employee_details table
  const setupRealtimeSubscription = () => {
    try {
      // Unsubscribe from any existing subscription
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }

      // Create a new subscription
      const subscription = supabase
        .channel('faculty-availability-changes')
        // Listen for UPDATE events
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'employee_details',
            // No filter - we'll filter faculty members in our handler
          },
          (payload) => {
            console.log('UPDATE received!', payload);

            // Check if this is a faculty member by examining the roles array
            const roles = payload.new.roles;
            if (Array.isArray(roles) && roles.includes('faculty')) {
              handleFacultyUpdate(payload.new);

              // Update last update time
              const now = new Date();
              setLastUpdateTime(now.toLocaleTimeString());
            }
          }
        )
        // Listen for INSERT events (new faculty members)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'employee_details',
          },
          (payload) => {
            console.log('INSERT received!', payload);

            // Check if this is a faculty member
            const roles = payload.new.roles;
            if (Array.isArray(roles) && roles.includes('faculty')) {
              // Reload the entire faculty list to include the new member
              loadFacultyAvailability();

              // Update last update time
              const now = new Date();
              setLastUpdateTime(now.toLocaleTimeString());
            }
          }
        )
        // Listen for DELETE events (removed faculty members)
        .on(
          'postgres_changes',
          {
            event: 'DELETE',
            schema: 'public',
            table: 'employee_details',
          },
          (payload) => {
            console.log('DELETE received!', payload);

            // Since we don't have the full record in the payload.old for DELETE events,
            // we'll just reload the entire faculty list
            loadFacultyAvailability();

            // Update last update time
            const now = new Date();
            setLastUpdateTime(now.toLocaleTimeString());
          }
        )
        .subscribe((status) => {
          console.log('Subscription status:', status);
          if (status === 'SUBSCRIBED') {
            setRealtimeStatus('connected');
            console.log('Successfully subscribed to real-time updates');

            // Force a refresh of faculty data when subscription is established
            loadFacultyAvailability();
          } else if (status === 'CHANNEL_ERROR') {
            setRealtimeStatus('error');
            console.error('Error subscribing to real-time updates');
          } else {
            setRealtimeStatus('disconnected');
          }
        });

      // Store the subscription reference
      subscriptionRef.current = subscription;
    } catch (error) {
      console.error('Error setting up real-time subscription:', error);
      setRealtimeStatus('error');
    }
  };

  // Handle updates to faculty availability
  const handleFacultyUpdate = (updatedFaculty: any) => {
    // Only process if it's a faculty member with availability data
    if (!updatedFaculty || !updatedFaculty.id) {
      console.log("Invalid faculty update received:", updatedFaculty);
      return;
    }

    console.log("Processing faculty update for:", updatedFaculty.full_name, updatedFaculty.id);

    // Check if this update actually changes availability data
    const hasAvailabilityChanged = (prevFaculty: FacultyAvailability) => {
      // Check if vacant_by_day has changed
      if (JSON.stringify(prevFaculty.vacant_by_day) !== JSON.stringify(updatedFaculty.vacant_by_day)) {
        console.log("vacant_by_day changed for", updatedFaculty.full_name);
        return true;
      }

      // Check if vacant_count_by_day has changed
      if (JSON.stringify(prevFaculty.vacant_count_by_day) !== JSON.stringify(updatedFaculty.vacant_count_by_day)) {
        console.log("vacant_count_by_day changed for", updatedFaculty.full_name);
        return true;
      }

      return false;
    };

    setFacultyList(prevList => {
      // Find the faculty member in the current list
      const index = prevList.findIndex(f => f.id === updatedFaculty.id);

      if (index === -1) {
        console.log("Faculty not found in current list, will reload all data");
        // If faculty not found, trigger a full reload
        loadFacultyAvailability();
        return prevList;
      }

      // Check if availability data has actually changed
      if (!hasAvailabilityChanged(prevList[index])) {
        console.log("No availability changes detected for", updatedFaculty.full_name);
        return prevList; // No changes needed
      }

      console.log("Updating faculty in list:", updatedFaculty.full_name);

      // Create a new list with the updated faculty
      const newList = [...prevList];
      newList[index] = {
        ...updatedFaculty,
        isUpdated: true, // Mark as updated for highlighting
        lastUpdated: Date.now()
      };

      // After 3 seconds, remove the highlight
      setTimeout(() => {
        setFacultyList(currentList => {
          const updatedIndex = currentList.findIndex(f => f.id === updatedFaculty.id);
          if (updatedIndex === -1) return currentList;

          const updatedList = [...currentList];
          updatedList[updatedIndex] = {
            ...updatedList[updatedIndex],
            isUpdated: false
          };

          return updatedList;
        });
      }, 3000);

      return newList;
    });
  };

  // Filter faculty list based on search query or when faculty list changes
  useEffect(() => {
    console.log("Faculty list or search query changed, updating filtered list");

    if (searchQuery.trim() === "") {
      setFilteredFacultyList(facultyList);
    } else {
      const filtered = facultyList.filter(faculty =>
        faculty.full_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredFacultyList(filtered);
    }

    // Don't reset page when faculty list changes due to real-time updates
    // Only reset when search query changes
    if (searchQuery !== prevSearchRef.current) {
      setCurrentPage(1);
      prevSearchRef.current = searchQuery;
    }
  }, [searchQuery, facultyList]);

  // Keep track of previous search query to avoid resetting page on real-time updates
  const prevSearchRef = useRef(searchQuery);

  // Update filtered list when faculty list changes
  useEffect(() => {
    // This ensures the filtered list is always in sync with the main list
    // even when individual faculty members are updated
    const updateFilteredList = () => {
      if (searchQuery.trim() === "") {
        setFilteredFacultyList(facultyList);
      } else {
        const filtered = facultyList.filter(faculty =>
          faculty.full_name.toLowerCase().includes(searchQuery.toLowerCase())
        );
        setFilteredFacultyList(filtered);
      }
    };

    updateFilteredList();
  }, [facultyList]);

  // Reset faculty availability
  const resetFacultyAvailability = async () => {
    try {
      setIsResetting(true);

      // Use the FacultyAvailabilityService to reset all faculty availability
      await FacultyAvailabilityService.resetFacultyAvailabilityToDefaults();

      // Reload faculty list to show updated data
      // Note: In a production environment with proper real-time setup,
      // this reload might not be necessary as the subscription would
      // catch the updates, but we'll keep it for reliability
      await loadFacultyAvailability();

      toast({
        title: "Success",
        description: `Faculty availability has been reset for all faculty members.`,
        duration: 5000,
      });

      // Update last update time
      const now = new Date();
      setLastUpdateTime(now.toLocaleTimeString());
    } catch (error) {
      console.error("Error resetting faculty availability:", error);
      toast({
        title: "Error",
        description: "Failed to reset faculty availability.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsResetting(false);
      setConfirmDialogOpen(false);
    }
  };

  // Reset faculty availability based on lab assignments
  const resetFacultyAvailabilityFromLabs = async () => {
    try {
      setIsResettingLabs(true);

      // Use the FacultyAvailabilityService to recalculate faculty availability based on lab assignments
      await FacultyAvailabilityService.resetFacultyAvailabilityFromLabs();

      // Reload faculty list to show updated data
      await loadFacultyAvailability();

      toast({
        title: "Success",
        description: `Faculty availability has been recalculated based on lab assignments.`,
        duration: 5000,
      });

      // Update last update time
      const now = new Date();
      setLastUpdateTime(now.toLocaleTimeString());
    } catch (error) {
      console.error("Error recalculating faculty availability from labs:", error);
      toast({
        title: "Error",
        description: "Failed to recalculate faculty availability from lab assignments.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsResettingLabs(false);
      setConfirmLabResetDialogOpen(false);
    }
  };

  // Pagination helpers
  const totalPages = Math.ceil(filteredFacultyList.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, filteredFacultyList.length);
  const currentItems = filteredFacultyList.slice(startIndex, endIndex);

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Show a subset of pages with ellipsis
      if (currentPage <= 3) {
        // Near the start
        for (let i = 1; i <= 3; i++) {
          pageNumbers.push(i);
        }
        pageNumbers.push('ellipsis');
        pageNumbers.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Near the end
        pageNumbers.push(1);
        pageNumbers.push('ellipsis');
        for (let i = totalPages - 2; i <= totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        // In the middle
        pageNumbers.push(1);
        pageNumbers.push('ellipsis');
        pageNumbers.push(currentPage - 1);
        pageNumbers.push(currentPage);
        pageNumbers.push(currentPage + 1);
        pageNumbers.push('ellipsis');
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers;
  };

  // Handle accordion state
  const toggleAccordion = (itemId: string) => {
    setExpandedItems(prev => {
      if (prev.includes(itemId)) {
        return prev.filter(id => id !== itemId);
      } else {
        return [...prev, itemId];
      }
    });
  };

  // Get summary of faculty availability
  const getAvailabilitySummary = (faculty: FacultyAvailability) => {
    if (!faculty.vacant_by_day) return "No availability data";

    const totalSlots = Object.values(faculty.vacant_by_day).reduce((sum, count) => sum + count, 0);
    const maxPossibleSlots = 35; // 7 slots × 5 days
    const availabilityPercentage = Math.round((totalSlots / maxPossibleSlots) * 100);

    let status = "Unknown";
    let color = "bg-gray-500";

    if (availabilityPercentage >= 90) {
      status = "Fully Available";
      color = "bg-green-500";
    } else if (availabilityPercentage >= 50) {
      status = "Partially Available";
      color = "bg-yellow-500";
    } else {
      status = "Limited Availability";
      color = "bg-red-500";
    }

    return (
      <div className="flex items-center gap-2">
        <div className={`w-3 h-3 rounded-full ${color}`}></div>
        <span>{status}</span>
        <span className="text-muted-foreground ml-auto">{availabilityPercentage}% available</span>
      </div>
    );
  };


  return (
    <div className="container py-10 max-w-7xl mx-auto">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">Faculty Availability Reset</h1>
          <div className="flex items-center gap-2 mt-1">
            <p className="text-muted-foreground">
              Manage and reset faculty availability settings
            </p>

            {/* Real-time status indicator */}
            <div className="flex items-center gap-1 text-xs ml-2">
              <div
                className={cn(
                  "w-2 h-2 rounded-full",
                  realtimeStatus === "connected" ? "bg-green-500" :
                  realtimeStatus === "error" ? "bg-red-500" : "bg-gray-400"
                )}
              />
              <span className="text-muted-foreground">
                {realtimeStatus === "connected" ? "Live updates" :
                 realtimeStatus === "error" ? "Update error" : "Connecting..."}
              </span>

              {lastUpdateTime && (
                <span className="text-muted-foreground ml-2">
                  Last update: {lastUpdateTime}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={setupRealtimeSubscription}
            disabled={realtimeStatus === "connected"}
            title="Reconnect to real-time updates"
          >
            <RefreshCw className={cn(
              "h-4 w-4",
              realtimeStatus === "connected" ? "text-green-500" :
              realtimeStatus === "error" ? "text-red-500" : "text-gray-400"
            )} />
          </Button>

          <Button
            onClick={() => setConfirmLabResetDialogOpen(true)}
            disabled={isLoading || isResetting || isResettingLabs}
            variant="secondary"
          >
            {isResettingLabs ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Recalculating...
              </>
            ) : (
              "Reset Lab Slots"
            )}
          </Button>

          <Button
            onClick={() => setConfirmDialogOpen(true)}
            disabled={isLoading || isResetting || isResettingLabs}
          >
            {isResetting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Resetting...
              </>
            ) : (
              "Reset Faculty Availability"
            )}
          </Button>
        </div>
      </div>

      <Alert className="mb-6">
        <Info className="h-4 w-4" />
        <AlertTitle>Important Information</AlertTitle>
        <AlertDescription>
          <p className="mb-2">
            Use <strong>Reset Lab Slots</strong> to recalculate faculty availability based on their current lab assignments.
            This ensures both Faculty 1 and Faculty 2 have their availability correctly updated.
          </p>
          <p>
            Use <strong>Reset Faculty Availability</strong> to clear all faculty lab slot assignments and restore their availability to the default values.
            This action will affect timetable generation and should be used with caution.
          </p>
        </AlertDescription>
      </Alert>

      {/* Legend Card */}
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle>Availability Legend</CardTitle>
          <CardDescription>
            Visual representation of faculty availability
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-6">
            <div className="flex items-center">
              <div className="w-5 h-5 bg-green-500 rounded-sm mr-2"></div>
              <span className="text-sm">Full Availability</span>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 bg-yellow-500 rounded-sm mr-2"></div>
              <span className="text-sm">Partial Availability</span>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 bg-red-500 rounded-sm mr-2"></div>
              <span className="text-sm">No Availability</span>
            </div>
            <div className="flex items-center">
              <div className="w-5 h-5 bg-gray-200 rounded-sm mr-2"></div>
              <span className="text-sm">Unavailable Slot</span>
            </div>
          </div>
          <div className="mt-3 text-sm text-muted-foreground">
            <p>Each bar represents one time slot. All days have a maximum of 7 slots.</p>
          </div>
        </CardContent>
      </Card>

      {/* Faculty Availability List */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <CardTitle>Faculty Availability Status</CardTitle>
              <CardDescription>
                Current availability settings for all faculty members
              </CardDescription>
            </div>

            {/* Search Bar */}
            <div className="relative w-full md:w-64">
              <Input
                placeholder="Search faculty..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-8"
              />
              {searchQuery ? (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  <X className="h-4 w-4" />
                </button>
              ) : (
                <Search className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div>
              {filteredFacultyList.length === 0 ? (
                <div className="text-center py-8">
                  {searchQuery ? (
                    <div className="space-y-2">
                      <p>No faculty members found matching "{searchQuery}"</p>
                      <Button variant="outline" size="sm" onClick={() => setSearchQuery("")}>
                        Clear Search
                      </Button>
                    </div>
                  ) : (
                    <p>No faculty members found</p>
                  )}
                </div>
              ) : (
                <>
                  <div className="text-sm text-muted-foreground mb-4">
                    Showing {startIndex + 1}-{endIndex} of {filteredFacultyList.length} faculty members
                  </div>

                  <Accordion type="multiple" value={expandedItems} className="mb-6">
                    {currentItems.map((faculty) => (
                      <AccordionItem
                        key={faculty.id}
                        value={faculty.id}
                        className={cn(
                          "border rounded-md mb-2 overflow-hidden transition-colors duration-500",
                          faculty.isUpdated && "bg-yellow-50 border-yellow-300"
                        )}
                      >
                        <AccordionTrigger
                          onClick={() => toggleAccordion(faculty.id)}
                          className="px-4 py-3 hover:no-underline"
                        >
                          <div className="flex flex-col sm:flex-row sm:items-center justify-between w-full text-left gap-2">
                            <span className="font-medium">{faculty.full_name}</span>
                            <div className="ml-auto">
                              {getAvailabilitySummary(faculty)}
                            </div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="px-4 pb-4 pt-0">
                          <div className="space-y-2 mt-2">
                            {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"].map(day => (
                              <AvailabilityBars
                                key={`${faculty.id}-${day}`}
                                day={day}
                                availableSlots={faculty.vacant_by_day?.[day] || 0}
                                maxSlots={7}
                              />
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <Pagination className="mt-6">
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                            className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>

                        {getPageNumbers().map((page, index) => (
                          typeof page === 'string' ? (
                            <PaginationItem key={`ellipsis-${index}`}>
                              <PaginationEllipsis />
                            </PaginationItem>
                          ) : (
                            <PaginationItem key={`page-${page}`}>
                              <PaginationLink
                                onClick={() => setCurrentPage(page)}
                                isActive={currentPage === page}
                                className="cursor-pointer"
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          )
                        ))}

                        <PaginationItem>
                          <PaginationNext
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                            className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  )}
                </>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Confirmation Dialog for Full Reset */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Faculty Availability</DialogTitle>
            <DialogDescription>
              This will reset availability for all faculty members to the default values.
              This action cannot be undone and will affect timetable generation.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm font-medium mb-2">New availability values will be:</p>
            <ul className="text-sm space-y-1 list-disc pl-5">
              <li>Monday: 7 slots</li>
              <li>Tuesday: 7 slots</li>
              <li>Wednesday: 7 slots</li>
              <li>Thursday: 7 slots</li>
              <li>Friday: 7 slots</li>
            </ul>
            <p className="text-sm text-muted-foreground mt-4">
              Note: All days will have all 7 time slots available.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={resetFacultyAvailability}
              disabled={isResetting}
            >
              {isResetting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Resetting...
                </>
              ) : (
                "Reset Availability"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog for Lab Slots Reset */}
      <Dialog open={confirmLabResetDialogOpen} onOpenChange={setConfirmLabResetDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reset Lab Slots</DialogTitle>
            <DialogDescription>
              This will recalculate faculty availability based on their current lab assignments.
              All faculty members will first be reset to default availability, then lab slots will be applied.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm font-medium mb-2">This process will:</p>
            <ul className="text-sm space-y-1 list-disc pl-5">
              <li>Reset all faculty to full availability (7 slots per day)</li>
              <li>Find all laboratory subject assignments</li>
              <li>Update availability for both Faculty 1 and Faculty 2</li>
              <li>Reduce availability based on lab time slots (morning or afternoon)</li>
            </ul>
            <p className="text-sm text-muted-foreground mt-4">
              Note: This ensures faculty availability accurately reflects their lab commitments.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmLabResetDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="secondary"
              onClick={resetFacultyAvailabilityFromLabs}
              disabled={isResettingLabs}
            >
              {isResettingLabs ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Recalculating...
                </>
              ) : (
                "Recalculate Lab Availability"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FacultyAvailabilityReset;
