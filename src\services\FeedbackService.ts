import { supabase } from "@/integrations/supabase/client";
import {
  FeedbackQuestion,
  FeedbackSession,
  StudentFeedbackResponse,
  FeedbackReport,
  CreateFeedbackSessionRequest,
  SubmitFeedbackRequest,
  HODFeedbackDashboard,
  StudentFeedbackDashboard,
  ClassTeacherFeedbackTracking,
  FeedbackQuestionResponse,
  AnonymizedResponse,
  FeedbackQuestionAverage
} from "@/types/feedback-system";

export class FeedbackService {

  // ==================== FEEDBACK QUESTIONS ====================

  static async getFeedbackQuestions(): Promise<FeedbackQuestion[]> {
    const { data, error } = await supabase
      .from('feedback_questions')
      .select('*')
      .eq('is_active', true)
      .order('question_number', { ascending: true });

    if (error) throw error;
    return data || [];
  }

  // ==================== SESSION MANAGEMENT ====================

  static async createFeedbackSession(
    request: CreateFeedbackSessionRequest,
    createdBy: string
  ): Promise<FeedbackSession> {
    const { data, error } = await supabase
      .from('feedback_sessions')
      .insert({
        session_name: request.session_name,
        academic_year: request.academic_year,
        department: request.department,
        semester: request.semester || null,
        section: request.section || null,
        start_date: request.start_date,
        end_date: request.end_date,
        created_by: createdBy
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async getActiveFeedbackSessions(department: string): Promise<FeedbackSession[]> {
    const now = new Date().toISOString();
    const { data, error } = await supabase
      .from('feedback_sessions')
      .select('*')
      .eq('department', department)
      .eq('is_active', true)
      .lte('start_date', now) // Session has started
      .gte('end_date', now)   // Session hasn't ended yet
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getCompletedFeedbackSessions(department: string): Promise<FeedbackSession[]> {
    const { data, error } = await supabase
      .from('feedback_sessions')
      .select('*')
      .eq('department', department)
      .lt('end_date', new Date().toISOString())
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getClassTeacherFeedbackSessions(
    department: string,
    semester?: string,
    section?: string
  ): Promise<FeedbackSession[]> {
    const now = new Date().toISOString();

    // Get all active sessions for the department
    const { data: allSessions, error } = await supabase
      .from('feedback_sessions')
      .select('*')
      .eq('department', department)
      .eq('is_active', true)
      .lte('start_date', now) // Session has started
      .gte('end_date', now)   // Session hasn't ended yet
      .order('created_at', { ascending: false });

    if (error) throw error;

    // If no specific class assigned, return all department sessions
    if (!semester || !section) {
      return allSessions || [];
    }

    // Filter sessions that apply to this class teacher's assigned class
    const sessions = allSessions?.filter(session => {
      // If session targets all semesters/sections, include it
      if (!session.semester && !session.section) return true;

      // If session targets specific semester but all sections
      if (session.semester && !session.section) {
        return session.semester === semester;
      }

      // If session targets all semesters but specific section
      if (!session.semester && session.section) {
        return session.section === section;
      }

      // If session targets specific semester and section
      if (session.semester && session.section) {
        return session.semester === semester && session.section === section;
      }

      return false;
    }) || [];

    return sessions;
  }

  static async deactivateFeedbackSession(sessionId: string): Promise<void> {
    const { error } = await supabase
      .from('feedback_sessions')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', sessionId);

    if (error) throw error;
  }

  // ==================== STUDENT FEEDBACK ====================

  // Helper method to map department names to short form (for mappings table)
  private static mapDepartmentName(department: string): string {
    const departmentMap: { [key: string]: string } = {
      // Full name to short form
      'Computer Science and Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',

      // Short form to short form (no change needed)
      'cse': 'cse',
      'ise': 'ise',
      'ece': 'ece',
      'mech': 'mech',
      'civil': 'civil',

      // Uppercase short forms
      'CSE': 'cse',
      'ISE': 'ise',
      'ECE': 'ece',
      'MECH': 'mech',
      'CIVIL': 'civil'
    };

    return departmentMap[department] || department.toLowerCase();
  }

  // Helper method to map department names to full form (for sessions table)
  private static mapDepartmentToFullName(department: string): string {
    const departmentMap: { [key: string]: string } = {
      // Short forms to full names
      'cse': 'Computer Science and Engineering',
      'ise': 'Information Science and Engineering',
      'ece': 'Electronics and Communication Engineering',
      'mech': 'Mechanical Engineering',
      'civil': 'Civil Engineering',

      // Uppercase short forms to full names
      'CSE': 'Computer Science and Engineering',
      'ISE': 'Information Science and Engineering',
      'ECE': 'Electronics and Communication Engineering',
      'MECH': 'Mechanical Engineering',
      'CIVIL': 'Civil Engineering',

      // Full names stay as full names
      'Computer Science and Engineering': 'Computer Science and Engineering',
      'Information Science and Engineering': 'Information Science and Engineering',
      'Electronics and Communication Engineering': 'Electronics and Communication Engineering',
      'Mechanical Engineering': 'Mechanical Engineering',
      'Civil Engineering': 'Civil Engineering'
    };

    return departmentMap[department] || department;
  }

  private static mapFullNameToDepartment(fullName: string): string {
    const departmentMap: { [key: string]: string } = {
      'Computer Science and Engineering': 'CSE',
      'Information Science and Engineering': 'ISE',
      'Electronics and Communication Engineering': 'ECE',
      'Mechanical Engineering': 'MECH',
      'Civil Engineering': 'CIVIL'
    };

    return departmentMap[fullName] || fullName;
  }

  static async getStudentPendingFeedback(
    studentUsn: string,
    department: string,
    semester: string,
    section: string
  ): Promise<any[]> {
    // Get active sessions for the student's class
    const now = new Date().toISOString();

    // Map department name for session query (sessions use full names)
    const sessionDepartment = this.mapDepartmentToFullName(department);

    // Get active sessions with date filters
    const { data: activeSessions, error: sessionError } = await supabase
      .from('feedback_sessions')
      .select('*')
      .eq('department', sessionDepartment)
      .eq('is_active', true)
      .lte('start_date', now) // Session has started
      .gte('end_date', now);   // Session hasn't ended yet

    if (sessionError) {
      throw sessionError;
    }

    if (!activeSessions || activeSessions.length === 0) {
      return []; // No active sessions
    }

    // Filter sessions that apply to this student's class
    const sessions = activeSessions?.filter(session => {
      // If session targets all semesters/sections (null values), include it
      if (!session.semester && !session.section) return true;

      // If session targets specific semester but all sections
      if (session.semester && !session.section) {
        return session.semester === semester;
      }

      // If session targets all semesters but specific section
      if (!session.semester && session.section) {
        return session.section === section;
      }

      // If session targets specific semester and section
      if (session.semester && session.section) {
        return session.semester === semester && session.section === section;
      }

      return false;
    }) || [];

    if (!sessions || sessions.length === 0) {
      return [];
    }

    // Get faculty-subject mappings for the student's class
    const mappedDepartment = this.mapDepartmentName(department);
    const semesterInt = parseInt(semester);

    const { data: subjects, error: subjectError } = await supabase
      .from('simplified_subject_faculty_mappings')
      .select(`
        subject_code,
        subject_name,
        subject_type,
        faculty_1_id,
        faculty_2_id,
        employee_details!faculty_1_id(id, full_name),
        employee_details_faculty_2:employee_details!faculty_2_id(id, full_name)
      `)
      .eq('department', mappedDepartment)
      .eq('semester', semesterInt)
      .eq('section', section);

    if (subjectError) {
      throw subjectError;
    }

    if (!subjects || subjects.length === 0) {
      return [];
    }

    // Get already submitted feedback
    const sessionIds = sessions.map(s => s.id);
    const { data: submitted, error: submittedError } = await supabase
      .from('student_feedback_responses')
      .select('session_id, faculty_id, subject_code')
      .eq('student_usn', studentUsn)
      .in('session_id', sessionIds)
      .eq('is_submitted', true);

    if (submittedError) throw submittedError;

    // Build pending feedback list (only for theory and elective subjects)
    const pendingFeedback = [];

    for (const session of sessions) {
      for (const subject of subjects || []) {
        // Skip lab subjects - only include theory and elective subjects
        if (subject.subject_type === 'laboratory' ||
            subject.subject_type === 'lab' ||
            subject.subject_type === 'skill_lab' ||
            subject.subject_code === 'SKILL LAB') {
          continue;
        }

        // Check faculty 1
        if (subject.faculty_1_id && subject.faculty_1_id !== '00000000-0000-0000-0000-000000000000') {
          const isSubmitted = submitted?.some(s =>
            s.session_id === session.id &&
            s.faculty_id === subject.faculty_1_id &&
            s.subject_code === subject.subject_code
          );

          if (!isSubmitted) {
            pendingFeedback.push({
              session_id: session.id,
              session_name: session.session_name,
              faculty_id: subject.faculty_1_id,
              faculty_name: subject.employee_details?.full_name || 'Unknown Faculty',
              subject_code: subject.subject_code,
              subject_name: subject.subject_name,
              subject_type: subject.subject_type,
              deadline: session.end_date
            });
          }
        }

        // Check faculty 2 if exists
        if (subject.faculty_2_id && subject.faculty_2_id !== '00000000-0000-0000-0000-000000000000') {
          const isSubmitted = submitted?.some(s =>
            s.session_id === session.id &&
            s.faculty_id === subject.faculty_2_id &&
            s.subject_code === subject.subject_code
          );

          if (!isSubmitted) {
            pendingFeedback.push({
              session_id: session.id,
              session_name: session.session_name,
              faculty_id: subject.faculty_2_id,
              faculty_name: subject.employee_details_faculty_2?.full_name || 'Unknown Faculty',
              subject_code: subject.subject_code,
              subject_name: subject.subject_name,
              subject_type: subject.subject_type,
              deadline: session.end_date
            });
          }
        }
      }
    }

    return pendingFeedback;
  }

  static async getStudentCompletedFeedback(
    studentUsn: string,
    department: string,
    semester: string,
    section: string
  ): Promise<any[]> {
    // Get active sessions for the student's class
    const now = new Date().toISOString();
    const sessionDepartment = this.mapDepartmentToFullName(department);

    // Get active sessions
    const { data: activeSessions, error: sessionError } = await supabase
      .from('feedback_sessions')
      .select('*')
      .eq('department', sessionDepartment)
      .eq('is_active', true)
      .lte('start_date', now)
      .gte('end_date', now);

    if (sessionError) throw sessionError;

    if (!activeSessions || activeSessions.length === 0) {
      return [];
    }

    // Filter sessions that apply to this student's class
    const sessions = activeSessions?.filter(session => {
      if (!session.semester && !session.section) return true;
      if (session.semester && !session.section) {
        return session.semester === semester;
      }
      if (!session.semester && session.section) {
        return session.section === section;
      }
      if (session.semester && session.section) {
        return session.semester === semester && session.section === section;
      }
      return false;
    }) || [];

    if (!sessions || sessions.length === 0) {
      return [];
    }

    // Get submitted feedback responses for this student
    const sessionIds = sessions.map(s => s.id);
    const { data: submittedResponses, error: submittedError } = await supabase
      .from('student_feedback_responses')
      .select(`
        session_id,
        faculty_id,
        subject_code,
        subject_name,
        submitted_at,
        created_at
      `)
      .eq('student_usn', studentUsn)
      .in('session_id', sessionIds)
      .eq('is_submitted', true);

    if (submittedError) throw submittedError;

    if (!submittedResponses || submittedResponses.length === 0) {
      return [];
    }

    // Get faculty names for the responses
    const facultyIds = [...new Set(submittedResponses.map(r => r.faculty_id))];
    const { data: facultyData, error: facultyError } = await supabase
      .from('employee_details')
      .select('id, full_name')
      .in('id', facultyIds);

    if (facultyError) throw facultyError;

    // Build completed feedback list
    const completedFeedback = submittedResponses.map(response => {
      const faculty = facultyData?.find(f => f.id === response.faculty_id);
      return {
        session_id: response.session_id,
        faculty_name: faculty?.full_name || 'Unknown Faculty',
        subject_code: response.subject_code,
        subject_name: response.subject_name,
        submitted_at: response.submitted_at || response.created_at
      };
    });

    return completedFeedback;
  }

  static async submitStudentFeedback(
    request: SubmitFeedbackRequest,
    studentUsn: string,
    studentName: string,
    department: string,
    semester: string,
    section: string
  ): Promise<void> {
    // Get subject name from mappings (take first row if duplicates exist)
    const mappedDepartment = this.mapDepartmentName(department);
    const { data: subjectDataArray, error: subjectError } = await supabase
      .from('simplified_subject_faculty_mappings')
      .select('subject_name')
      .eq('subject_code', request.subject_code)
      .eq('department', mappedDepartment)
      .eq('semester', parseInt(semester))
      .eq('section', section)
      .limit(1);

    const subjectData = subjectDataArray?.[0] || null;

    if (subjectError) throw subjectError;

    const { error } = await supabase
      .from('student_feedback_responses')
      .insert({
        session_id: request.session_id,
        student_usn: studentUsn,
        student_name: studentName,
        department: department,
        semester: semester,
        section: section,
        faculty_id: request.faculty_id,
        subject_code: request.subject_code,
        subject_name: subjectData?.subject_name || request.subject_code,
        responses: request.responses,
        is_submitted: true,
        submitted_at: new Date().toISOString()
      });

    if (error) throw error;
  }

  // ==================== REPORT GENERATION ====================

  static async generateFeedbackReport(
    sessionId: string,
    facultyId: string,
    subjectCode: string,
    generatedBy: string
  ): Promise<FeedbackReport> {
    // Get all responses for this faculty-subject combination
    const { data: responses, error: responseError } = await supabase
      .from('student_feedback_responses')
      .select('*')
      .eq('session_id', sessionId)
      .eq('faculty_id', facultyId)
      .eq('subject_code', subjectCode)
      .eq('is_submitted', true);

    if (responseError) throw responseError;

    if (!responses || responses.length === 0) {
      throw new Error('No feedback responses found for this faculty-subject combination');
    }

    // Get faculty and subject details
    const { data: facultyData, error: facultyError } = await supabase
      .from('employee_details')
      .select('full_name')
      .eq('id', facultyId)
      .single();

    if (facultyError) throw facultyError;

    // Calculate averages and generate report
    const questions = await this.getFeedbackQuestions();
    const averageRatings: FeedbackQuestionAverage[] = [];
    let totalMarks = 0;
    let possibleMarks = questions.length * 10 * responses.length;

    for (const question of questions) {
      const ratings = responses.map(r => {
        const response = (r.responses as FeedbackQuestionResponse[])
          .find(resp => resp.question_id === question.id);
        return response?.rating || 0;
      });

      const sum = ratings.reduce((acc, rating) => acc + rating, 0);
      const average = sum / ratings.length;
      const questionTotalMarks = sum;
      const questionPossibleMarks = ratings.length * 10;

      totalMarks += questionTotalMarks;

      averageRatings.push({
        question_id: question.id,
        question_number: question.question_number,
        question_text: question.question_text,
        average_rating: average,
        total_marks: questionTotalMarks,
        possible_marks: questionPossibleMarks,
        percentage: (questionTotalMarks / questionPossibleMarks) * 100
      });
    }

    const overallPercentage = (totalMarks / possibleMarks) * 100;

    // Create anonymized responses
    const anonymizedResponses: AnonymizedResponse[] = responses.map(response => ({
      masked_usn: this.maskUSN(response.student_usn),
      responses: response.responses as FeedbackQuestionResponse[],
      submitted_at: response.submitted_at || response.created_at
    }));

    // Randomize order to prevent identification
    anonymizedResponses.sort(() => Math.random() - 0.5);

    const reportData = {
      session_id: sessionId,
      faculty_id: facultyId,
      faculty_name: facultyData.full_name,
      subject_code: subjectCode,
      subject_name: responses[0].subject_name,
      department: responses[0].department,
      semester: responses[0].semester,
      section: responses[0].section,
      total_responses: responses.length,
      average_ratings: averageRatings,
      overall_percentage: overallPercentage,
      anonymized_responses: anonymizedResponses,
      generated_by: generatedBy
    };

    // Save report to database
    const { data: report, error: reportError } = await supabase
      .from('feedback_reports')
      .upsert(reportData, {
        onConflict: 'session_id,faculty_id,subject_code'
      })
      .select()
      .single();

    if (reportError) throw reportError;

    return report;
  }

  // ==================== HOD FACULTY REPORTS ====================

  static async getHODFacultyReports(
    department: string,
    sessionId?: string,
    semester?: string,
    section?: string
  ): Promise<any> {
    try {
      // Get sessions for the department
      let sessionsQuery = supabase
        .from('feedback_sessions')
        .select('*')
        .eq('department', this.mapDepartmentToFullName(department))
        .order('created_at', { ascending: false });

      if (sessionId) {
        sessionsQuery = sessionsQuery.eq('id', sessionId);
      }

      const { data: sessions, error: sessionError } = await sessionsQuery;
      if (sessionError) throw sessionError;

      if (!sessions || sessions.length === 0) {
        return {
          sessions: [],
          facultyReports: [],
          departmentSummary: null
        };
      }

      // Get all feedback reports for these sessions
      const sessionIds = sessions.map(s => s.id);
      let reportsQuery = supabase
        .from('feedback_reports')
        .select('*')
        .in('session_id', sessionIds);

      if (semester) {
        reportsQuery = reportsQuery.eq('semester', semester);
      }
      if (section) {
        reportsQuery = reportsQuery.eq('section', section);
      }

      const { data: reports, error: reportsError } = await reportsQuery;
      if (reportsError) throw reportsError;

      // Group reports by faculty
      const facultyReports = this.groupReportsByFaculty(reports || []);

      // Calculate department summary
      const departmentSummary = this.calculateDepartmentSummary(reports || [], department);

      return {
        sessions,
        facultyReports,
        departmentSummary,
        totalReports: reports?.length || 0
      };

    } catch (error) {
      console.error('Error getting HOD faculty reports:', error);
      throw error;
    }
  }

  static async generateDepartmentConsolidatedReport(
    department: string,
    sessionId: string,
    generatedBy: string
  ): Promise<any> {
    try {
      // Get all faculty in the department
      const { data: facultyList, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, username')
        .contains('roles', ['faculty'])
        .eq('department', department);

      if (facultyError) throw facultyError;

      // Get all subject mappings for the department (only theory and elective subjects)
      const mappedDepartment = this.mapDepartmentName(department);
      const { data: allSubjects, error: subjectsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          subject_code,
          subject_name,
          subject_type,
          faculty_1_id,
          faculty_2_id,
          semester,
          section,
          employee_details!faculty_1_id(id, full_name),
          employee_details_faculty_2:employee_details!faculty_2_id(id, full_name)
        `)
        .eq('department', mappedDepartment);

      if (subjectsError) throw subjectsError;

      // Filter out lab subjects - only include theory and elective subjects
      const subjects = allSubjects?.filter(subject =>
        subject.subject_type !== 'laboratory' &&
        subject.subject_type !== 'lab' &&
        subject.subject_type !== 'skill_lab' &&
        subject.subject_code !== 'SKILL LAB' &&
        !subject.subject_name?.toLowerCase().includes('lab')
      ) || [];

      console.log('🔍 Consolidated Report - All subjects:', allSubjects?.length);
      console.log('🔍 Consolidated Report - Filtered theory subjects:', subjects.length);
      console.log('🔍 Consolidated Report - Theory subjects:', subjects.map(s => `${s.subject_code} (${s.subject_type})`));

      // Get all responses for this session
      const { data: responses, error: responsesError } = await supabase
        .from('student_feedback_responses')
        .select('*')
        .eq('session_id', sessionId)
        .eq('is_submitted', true);

      if (responsesError) throw responsesError;

      // Build consolidated report
      const consolidatedReport = {
        session_id: sessionId,
        department: department,
        generated_by: generatedBy,
        generated_at: new Date().toISOString(),
        faculty_summary: [],
        department_metrics: {
          total_faculty: facultyList?.length || 0,
          total_responses: responses?.length || 0,
          average_department_rating: 0,
          top_performers: [],
          improvement_areas: []
        }
      };

      // Process each faculty member (only those who teach theory/elective subjects)
      for (const faculty of facultyList || []) {
        const facultySubjects = subjects?.filter(s =>
          s.faculty_1_id === faculty.id || s.faculty_2_id === faculty.id
        ) || [];

        const facultyResponses = responses?.filter(r => r.faculty_id === faculty.id) || [];

        // Only include faculty who teach theory/elective subjects (not lab-only faculty)
        if (facultySubjects.length > 0) {
          const facultySummary = {
            faculty_id: faculty.id,
            faculty_name: faculty.full_name,
            employee_id: faculty.username || faculty.id,
            subjects_taught: facultySubjects.map(s => ({
              subject_code: s.subject_code,
              subject_name: s.subject_name,
              subject_type: s.subject_type,
              semester: s.semester,
              section: s.section,
              response_count: facultyResponses.filter(r => r.subject_code === s.subject_code).length
            })),
            total_responses: facultyResponses.length,
            average_rating: this.calculateFacultyAverageRating(facultyResponses),
            performance_metrics: this.calculateFacultyPerformanceMetrics(facultyResponses)
          };

          consolidatedReport.faculty_summary.push(facultySummary);
        }
      }

      // Calculate department-wide metrics
      consolidatedReport.department_metrics = this.calculateDepartmentMetrics(
        consolidatedReport.faculty_summary,
        responses || []
      );

      return consolidatedReport;

    } catch (error) {
      console.error('Error generating consolidated report:', error);
      throw error;
    }
  }

  static async generateIndividualFacultySubjectReports(
    department: string,
    sessionId: string,
    facultyId?: string
  ): Promise<any[]> {
    try {
      // Get session details
      const { data: session, error: sessionError } = await supabase
        .from('feedback_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (sessionError) throw sessionError;

      // Get all feedback reports for this session and department
      let reportsQuery = supabase
        .from('feedback_reports')
        .select('*')
        .eq('session_id', sessionId);

      if (facultyId) {
        reportsQuery = reportsQuery.eq('faculty_id', facultyId);
      }

      const { data: reports, error: reportsError } = await reportsQuery;
      if (reportsError) throw reportsError;

      // Get faculty details
      const facultyIds = [...new Set(reports?.map(r => r.faculty_id) || [])];
      const { data: facultyList, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, username, department')
        .in('id', facultyIds)
        .eq('department', department);

      if (facultyError) throw facultyError;

      // Get subject mappings to get subject details
      const mappedDepartment = this.mapDepartmentName(department);
      const { data: subjects, error: subjectsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('*')
        .eq('department', mappedDepartment);

      if (subjectsError) throw subjectsError;

      // Build individual subject reports
      const individualReports = [];

      for (const report of reports || []) {
        const faculty = facultyList?.find(f => f.id === report.faculty_id);
        const subject = subjects?.find(s =>
          s.subject_code === report.subject_code &&
          (s.faculty_1_id === report.faculty_id || s.faculty_2_id === report.faculty_id)
        );

        if (faculty && subject) {
          // Get detailed responses for this faculty-subject combination
          const { data: responses, error: responsesError } = await supabase
            .from('student_feedback_responses')
            .select('*')
            .eq('session_id', sessionId)
            .eq('faculty_id', report.faculty_id)
            .eq('subject_code', report.subject_code)
            .eq('is_submitted', true);

          if (responsesError) throw responsesError;

          // Calculate detailed metrics
          const detailedMetrics = this.calculateDetailedSubjectMetrics(responses || []);

          individualReports.push({
            report_id: `${report.faculty_id}_${report.subject_code}_${sessionId}`,
            session_name: session.session_name,
            academic_year: session.academic_year,
            faculty: {
              id: faculty.id,
              name: faculty.full_name,
              username: faculty.username,
              department: faculty.department
            },
            subject: {
              code: subject.subject_code,
              name: subject.subject_name,
              type: subject.subject_type,
              semester: subject.semester,
              section: subject.section
            },
            feedback_summary: {
              total_responses: report.total_responses,
              overall_percentage: report.overall_percentage,
              average_ratings: report.average_ratings,
              question_wise_analysis: detailedMetrics.question_analysis,
              rating_distribution: detailedMetrics.rating_distribution,
              student_comments: detailedMetrics.comments,
              strengths: detailedMetrics.strengths,
              improvement_areas: detailedMetrics.improvement_areas
            },
            generated_at: new Date().toISOString()
          });
        }
      }

      return individualReports;

    } catch (error) {
      console.error('Error generating individual faculty subject reports:', error);
      throw error;
    }
  }

  private static calculateDetailedSubjectMetrics(responses: any[]): any {
    if (!responses || responses.length === 0) {
      return {
        question_analysis: [],
        rating_distribution: {},
        comments: [],
        strengths: [],
        improvement_areas: []
      };
    }

    const questionAnalysis = new Map();
    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0 };
    const comments = [];

    responses.forEach(response => {
      if (response.responses && Array.isArray(response.responses)) {
        response.responses.forEach((questionResponse: any) => {
          const questionId = questionResponse.question_id;
          const rating = questionResponse.rating || 0;

          // Track question-wise ratings
          if (!questionAnalysis.has(questionId)) {
            questionAnalysis.set(questionId, {
              question_id: questionId,
              question_text: questionResponse.question_text || `Question ${questionId}`,
              ratings: [],
              average: 0,
              total_responses: 0
            });
          }

          const questionData = questionAnalysis.get(questionId);
          questionData.ratings.push(rating);
          questionData.total_responses++;

          // Track overall rating distribution
          if (rating >= 1 && rating <= 10) {
            ratingDistribution[rating as keyof typeof ratingDistribution]++;
          }
        });
      }

      // Collect comments if any
      if (response.comments && response.comments.trim()) {
        comments.push({
          student_usn: this.maskUSN(response.student_usn),
          comment: response.comments,
          submitted_at: response.created_at
        });
      }
    });

    // Calculate averages and identify strengths/improvements
    const questionResults = Array.from(questionAnalysis.values()).map(q => {
      q.average = q.ratings.reduce((sum: number, rating: number) => sum + rating, 0) / q.ratings.length;
      return q;
    }).sort((a, b) => b.average - a.average);

    return {
      question_analysis: questionResults,
      rating_distribution: ratingDistribution,
      comments: comments.slice(0, 10), // Limit to 10 recent comments
      strengths: questionResults.slice(0, 3), // Top 3 questions
      improvement_areas: questionResults.slice(-3).reverse() // Bottom 3 questions
    };
  }

  static async generateDetailedFacultyReport(
    department: string,
    sessionId: string,
    facultyId: string
  ): Promise<any> {
    try {
      // Get session details
      const { data: session, error: sessionError } = await supabase
        .from('feedback_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (sessionError) throw sessionError;

      // Get faculty details
      const { data: faculty, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, username, department, designation')
        .eq('id', facultyId)
        .single();

      if (facultyError) throw facultyError;

      // Get all subjects taught by this faculty (theory and elective only)
      const mappedDepartment = this.mapDepartmentName(department);
      const { data: allSubjects, error: subjectsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          subject_code,
          subject_name,
          subject_type,
          faculty_1_id,
          faculty_2_id,
          semester,
          section
        `)
        .eq('department', mappedDepartment)
        .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`);

      if (subjectsError) throw subjectsError;

      // Filter to only theory and elective subjects
      const subjects = allSubjects?.filter(subject =>
        subject.subject_type !== 'laboratory' &&
        subject.subject_type !== 'lab' &&
        subject.subject_type !== 'skill_lab' &&
        subject.subject_code !== 'SKILL LAB' &&
        !subject.subject_name?.toLowerCase().includes('lab')
      ) || [];

      // Get all feedback responses for this faculty in this session
      const { data: responses, error: responsesError } = await supabase
        .from('student_feedback_responses')
        .select('*')
        .eq('session_id', sessionId)
        .eq('faculty_id', facultyId)
        .eq('is_submitted', true);

      if (responsesError) throw responsesError;

      // Get feedback questions for context
      const { data: questions, error: questionsError } = await supabase
        .from('feedback_questions')
        .select('*')
        .eq('is_active', true)
        .order('question_number');

      if (questionsError) {
        console.error('Error fetching feedback questions:', questionsError);
        throw questionsError;
      }

      console.log('🔍 Detailed Report - Faculty:', faculty.full_name);
      console.log('🔍 Detailed Report - Subjects found:', subjects.length);
      console.log('🔍 Detailed Report - Responses found:', responses?.length);
      console.log('🔍 Detailed Report - Questions found:', questions?.length);

      // Build detailed report
      const detailedReport = {
        faculty: {
          id: faculty.id,
          name: faculty.full_name,
          username: faculty.username,
          department: faculty.department,
          designation: faculty.designation
        },
        session: {
          id: session.id,
          name: session.session_name,
          academic_year: session.academic_year,
          start_date: session.start_date,
          end_date: session.end_date
        },
        overall_metrics: {
          total_subjects: subjects.length,
          total_responses: responses?.length || 0,
          overall_average_rating: 0,
          overall_percentage: 0
        },
        subject_wise_analysis: [],
        section_wise_performance: [],
        question_wise_analysis: [],
        student_feedback_details: [],
        generated_at: new Date().toISOString()
      };

      // Process each subject
      for (const subject of subjects) {
        const subjectResponses = responses?.filter(r => r.subject_code === subject.subject_code) || [];

        if (subjectResponses.length > 0) {
          const subjectAnalysis = this.analyzeSubjectFeedback(subject, subjectResponses, questions || []);
          detailedReport.subject_wise_analysis.push(subjectAnalysis);
        }
      }

      // Calculate overall metrics
      if (detailedReport.subject_wise_analysis.length > 0) {
        const totalRating = detailedReport.subject_wise_analysis.reduce((sum, subject) => sum + subject.average_rating, 0);
        detailedReport.overall_metrics.overall_average_rating = totalRating / detailedReport.subject_wise_analysis.length;
        detailedReport.overall_metrics.overall_percentage = (detailedReport.overall_metrics.overall_average_rating / 10) * 100;
      }

      // Generate section-wise performance
      detailedReport.section_wise_performance = this.calculateSectionWisePerformance(detailedReport.subject_wise_analysis);

      // Generate question-wise analysis across all subjects
      detailedReport.question_wise_analysis = this.calculateQuestionWiseAnalysis(responses || [], questions || []);

      // Generate student feedback details (privacy-protected)
      detailedReport.student_feedback_details = this.generateStudentFeedbackDetails(responses || [], questions || []);

      return detailedReport;

    } catch (error) {
      console.error('Error generating detailed faculty report:', error);
      throw error;
    }
  }

  private static analyzeSubjectFeedback(subject: any, responses: any[], questions: any[]): any {
    const questionAnalysis = new Map();
    let totalRating = 0;
    let totalQuestions = 0;

    responses.forEach(response => {
      if (response.responses && Array.isArray(response.responses)) {
        response.responses.forEach((questionResponse: any) => {
          const questionId = questionResponse.question_id;
          const rating = questionResponse.rating || 0;

          if (!questionAnalysis.has(questionId)) {
            const question = questions.find(q => q.id === questionId);
            questionAnalysis.set(questionId, {
              question_id: questionId,
              question_text: question?.question_text || `Question ${questionId}`,
              ratings: [],
              average: 0,
              student_responses: []
            });
          }

          const questionData = questionAnalysis.get(questionId);
          questionData.ratings.push(rating);
          questionData.student_responses.push({
            student_usn: this.maskUSN(response.student_usn),
            rating: rating,
            submitted_at: response.created_at
          });

          totalRating += rating;
          totalQuestions++;
        });
      }
    });

    // Calculate averages for each question
    const questionResults = Array.from(questionAnalysis.values()).map(q => {
      q.average = q.ratings.reduce((sum: number, rating: number) => sum + rating, 0) / q.ratings.length;
      return q;
    });

    return {
      subject: {
        code: subject.subject_code,
        name: subject.subject_name,
        type: subject.subject_type,
        semester: subject.semester,
        section: subject.section
      },
      response_count: responses.length,
      average_rating: totalQuestions > 0 ? totalRating / totalQuestions : 0,
      percentage: totalQuestions > 0 ? (totalRating / totalQuestions / 10) * 100 : 0,
      question_analysis: questionResults,
      student_comments: responses
        .filter(r => r.comments && r.comments.trim())
        .map(r => ({
          student_usn: this.maskUSN(r.student_usn),
          comment: r.comments,
          submitted_at: r.created_at
        }))
    };
  }

  private static calculateSectionWisePerformance(subjectAnalysis: any[]): any[] {
    const sectionMap = new Map();

    subjectAnalysis.forEach(subject => {
      const sectionKey = `${subject.subject.semester}-${subject.subject.section}`;

      if (!sectionMap.has(sectionKey)) {
        sectionMap.set(sectionKey, {
          semester: subject.subject.semester,
          section: subject.subject.section,
          subjects: [],
          total_responses: 0,
          average_rating: 0,
          average_percentage: 0
        });
      }

      const sectionData = sectionMap.get(sectionKey);
      sectionData.subjects.push(subject);
      sectionData.total_responses += subject.response_count;
    });

    // Calculate averages for each section
    return Array.from(sectionMap.values()).map(section => {
      if (section.subjects.length > 0) {
        section.average_rating = section.subjects.reduce((sum: number, subject: any) => sum + subject.average_rating, 0) / section.subjects.length;
        section.average_percentage = section.subjects.reduce((sum: number, subject: any) => sum + subject.percentage, 0) / section.subjects.length;
      }
      return section;
    });
  }

  private static calculateQuestionWiseAnalysis(responses: any[], questions: any[]): any[] {
    const questionAnalysis = new Map();

    responses.forEach(response => {
      if (response.responses && Array.isArray(response.responses)) {
        response.responses.forEach((questionResponse: any) => {
          const questionId = questionResponse.question_id;
          const rating = questionResponse.rating || 0;

          if (!questionAnalysis.has(questionId)) {
            const question = questions.find(q => q.id === questionId);
            questionAnalysis.set(questionId, {
              question_id: questionId,
              question_text: question?.question_text || `Question ${questionId}`,
              ratings: [],
              average: 0,
              total_responses: 0,
              rating_distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0 }
            });
          }

          const questionData = questionAnalysis.get(questionId);
          questionData.ratings.push(rating);
          questionData.total_responses++;
          if (rating >= 1 && rating <= 10) {
            questionData.rating_distribution[rating as keyof typeof questionData.rating_distribution]++;
          }
        });
      }
    });

    return Array.from(questionAnalysis.values()).map(q => {
      q.average = q.ratings.reduce((sum: number, rating: number) => sum + rating, 0) / q.ratings.length;
      return q;
    }).sort((a, b) => b.average - a.average);
  }

  private static generateStudentFeedbackDetails(responses: any[], questions: any[]): any[] {
    return responses.map(response => ({
      student_usn: this.maskUSN(response.student_usn),
      subject_code: response.subject_code,
      submitted_at: response.created_at,
      question_responses: response.responses?.map((qr: any) => {
        const question = questions.find(q => q.id === qr.question_id);
        return {
          question_id: qr.question_id,
          question_text: question?.question_text || `Question ${qr.question_id}`,
          rating: qr.rating
        };
      }) || [],
      comments: response.comments || '',
      overall_rating: response.responses?.reduce((sum: number, qr: any) => sum + (qr.rating || 0), 0) / (response.responses?.length || 1)
    })).sort((a, b) => new Date(b.submitted_at).getTime() - new Date(a.submitted_at).getTime());
  }

  static async generateDetailedStudentFeedbackReport(
    department: string,
    sessionId: string,
    facultyId: string
  ): Promise<any> {
    try {
      // Get session details
      const { data: session, error: sessionError } = await supabase
        .from('feedback_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (sessionError) throw sessionError;

      // Get faculty details
      const { data: faculty, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, username, department, designation')
        .eq('id', facultyId)
        .single();

      if (facultyError) throw facultyError;

      // Get all feedback responses for this faculty in this session
      const { data: responses, error: responsesError } = await supabase
        .from('student_feedback_responses')
        .select('*')
        .eq('session_id', sessionId)
        .eq('faculty_id', facultyId)
        .eq('is_submitted', true);

      if (responsesError) throw responsesError;

      // Get feedback questions
      const questions = await this.getFeedbackQuestions();

      // Group responses by subject
      const subjectGroups = new Map();
      responses?.forEach(response => {
        const key = `${response.subject_code}-${response.semester}-${response.section}`;
        if (!subjectGroups.has(key)) {
          subjectGroups.set(key, {
            subject_code: response.subject_code,
            subject_name: response.subject_name,
            semester: response.semester,
            section: response.section,
            responses: []
          });
        }
        subjectGroups.get(key).responses.push(response);
      });

      // Generate detailed student feedback for each subject
      const subjectReports = Array.from(subjectGroups.values()).map(subjectGroup => {
        const studentDetails = subjectGroup.responses.map((response: any) => {
          const questionRatings: { [key: string]: number } = {};
          let totalScore = 0;

          // Process each question response
          response.responses?.forEach((qr: any) => {
            questionRatings[`question_${qr.question_number || qr.question_id}`] = qr.rating || 0;
            totalScore += qr.rating || 0;
          });

          const maxPossibleScore = questions.length * 10;
          const percentage = maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;

          return {
            masked_usn: this.maskUSN(response.student_usn),
            question_ratings: questionRatings,
            aggregate_score: totalScore,
            percentage: Math.round(percentage * 100) / 100,
            submitted_at: response.submitted_at || response.created_at
          };
        });

        return {
          subject: {
            code: subjectGroup.subject_code,
            name: subjectGroup.subject_name,
            semester: subjectGroup.semester,
            section: subjectGroup.section
          },
          total_students: studentDetails.length,
          student_details: studentDetails.sort((a, b) => b.percentage - a.percentage) // Sort by percentage descending
        };
      });

      return {
        faculty: {
          id: faculty.id,
          name: faculty.full_name,
          username: faculty.username,
          department: faculty.department,
          designation: faculty.designation
        },
        session: {
          id: session.id,
          name: session.session_name,
          academic_year: session.academic_year
        },
        questions: questions,
        subject_reports: subjectReports,
        generated_at: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error generating detailed student feedback report:', error);
      throw error;
    }
  }

  private static groupReportsByFaculty(reports: any[]): any[] {
    const facultyMap = new Map();

    reports.forEach(report => {
      if (!facultyMap.has(report.faculty_id)) {
        facultyMap.set(report.faculty_id, {
          faculty_id: report.faculty_id,
          faculty_name: report.faculty_name,
          subjects: [],
          total_responses: 0,
          average_rating: 0,
          overall_percentage: 0
        });
      }

      const faculty = facultyMap.get(report.faculty_id);
      faculty.subjects.push({
        subject_code: report.subject_code,
        subject_name: report.subject_name,
        semester: report.semester,
        section: report.section,
        total_responses: report.total_responses,
        overall_percentage: report.overall_percentage,
        average_ratings: report.average_ratings
      });

      faculty.total_responses += report.total_responses;
    });

    // Calculate averages for each faculty
    facultyMap.forEach(faculty => {
      if (faculty.subjects.length > 0) {
        faculty.average_rating = faculty.subjects.reduce((sum: number, subject: any) =>
          sum + subject.overall_percentage, 0) / faculty.subjects.length;
        faculty.overall_percentage = faculty.average_rating;
      }
    });

    return Array.from(facultyMap.values());
  }

  private static calculateDepartmentSummary(reports: any[], department: string): any {
    if (!reports || reports.length === 0) {
      return {
        department,
        total_faculty: 0,
        total_reports: 0,
        average_rating: 0,
        total_responses: 0,
        top_performers: [],
        improvement_areas: []
      };
    }

    const facultyPerformance = new Map();
    let totalResponses = 0;
    let totalRating = 0;

    reports.forEach(report => {
      if (!facultyPerformance.has(report.faculty_id)) {
        facultyPerformance.set(report.faculty_id, {
          faculty_name: report.faculty_name,
          ratings: [],
          total_responses: 0
        });
      }

      const faculty = facultyPerformance.get(report.faculty_id);
      faculty.ratings.push(report.overall_percentage);
      faculty.total_responses += report.total_responses;
      totalResponses += report.total_responses;
      totalRating += report.overall_percentage;
    });

    // Calculate faculty averages and sort
    const facultyAverages = Array.from(facultyPerformance.entries()).map(([id, data]: [string, any]) => ({
      faculty_id: id,
      faculty_name: data.faculty_name,
      average_rating: data.ratings.reduce((sum: number, rating: number) => sum + rating, 0) / data.ratings.length,
      total_responses: data.total_responses
    })).sort((a, b) => b.average_rating - a.average_rating);

    return {
      department,
      total_faculty: facultyPerformance.size,
      total_reports: reports.length,
      average_rating: totalRating / reports.length,
      total_responses: totalResponses,
      top_performers: facultyAverages.slice(0, 3),
      improvement_areas: facultyAverages.slice(-3).reverse()
    };
  }

  private static calculateFacultyAverageRating(responses: any[]): number {
    if (!responses || responses.length === 0) return 0;

    let totalRating = 0;
    let totalQuestions = 0;

    responses.forEach(response => {
      if (response.responses && Array.isArray(response.responses)) {
        response.responses.forEach((questionResponse: any) => {
          totalRating += questionResponse.rating || 0;
          totalQuestions++;
        });
      }
    });

    return totalQuestions > 0 ? (totalRating / totalQuestions) : 0;
  }

  private static calculateFacultyPerformanceMetrics(responses: any[]): any {
    if (!responses || responses.length === 0) {
      return {
        total_responses: 0,
        average_rating: 0,
        rating_distribution: {},
        strengths: [],
        improvement_areas: []
      };
    }

    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0 };
    const questionRatings = new Map();

    responses.forEach(response => {
      if (response.responses && Array.isArray(response.responses)) {
        response.responses.forEach((questionResponse: any) => {
          const rating = questionResponse.rating || 0;
          ratingDistribution[rating as keyof typeof ratingDistribution]++;

          if (!questionRatings.has(questionResponse.question_id)) {
            questionRatings.set(questionResponse.question_id, []);
          }
          questionRatings.get(questionResponse.question_id).push(rating);
        });
      }
    });

    // Calculate question averages for strengths/improvements
    const questionAverages = Array.from(questionRatings.entries()).map(([questionId, ratings]) => ({
      question_id: questionId,
      average: ratings.reduce((sum: number, rating: number) => sum + rating, 0) / ratings.length
    })).sort((a, b) => b.average - a.average);

    return {
      total_responses: responses.length,
      average_rating: this.calculateFacultyAverageRating(responses),
      rating_distribution: ratingDistribution,
      strengths: questionAverages.slice(0, 3),
      improvement_areas: questionAverages.slice(-3).reverse()
    };
  }

  private static calculateDepartmentMetrics(facultySummary: any[], allResponses: any[]): any {
    if (!facultySummary || facultySummary.length === 0) {
      return {
        total_faculty: 0,
        total_responses: 0,
        average_department_rating: 0,
        top_performers: [],
        improvement_areas: []
      };
    }

    const totalResponses = allResponses.length;
    const averageRating = facultySummary.reduce((sum, faculty) => sum + faculty.average_rating, 0) / facultySummary.length;

    // Sort faculty by performance
    const sortedFaculty = [...facultySummary].sort((a, b) => b.average_rating - a.average_rating);

    return {
      total_faculty: facultySummary.length,
      total_responses: totalResponses,
      average_department_rating: averageRating,
      top_performers: sortedFaculty.slice(0, 5),
      improvement_areas: sortedFaculty.slice(-3).reverse()
    };
  }

  // ==================== CLASS TEACHER TRACKING ====================

  /**
   * Get feedback tracking data for a specific class teacher's assigned class
   */
  static async getClassTeacherFeedbackTrackingByFaculty(
    facultyId: string,
    academicYear: string = '2024-2025'
  ): Promise<any> {
    try {
      // First, get the class teacher's assignment
      const { ClassTeacherService } = await import('./ClassTeacherService');
      const assignment = await ClassTeacherService.getClassTeacherAssignment(facultyId, academicYear);

      if (!assignment) {
        console.log('🔍 No class teacher assignment found for faculty:', facultyId);
        return null;
      }

      console.log('🔍 Found class teacher assignment:', assignment);

      // Get tracking data for the specific class
      return await this.getClassTeacherFeedbackTracking(
        assignment.department,
        assignment.semester,
        assignment.section
      );
    } catch (error) {
      console.error('Error getting class teacher feedback tracking by faculty:', error);
      throw error;
    }
  }

  static async getClassTeacherFeedbackTracking(
    department: string,
    semester?: string,
    section?: string
  ): Promise<any> {
    console.log('🔍 Class Teacher Tracking - Filtering for specific class:', {
      department,
      semester,
      section
    });
    // Get active sessions for the department
    const sessionDepartment = this.mapDepartmentToFullName(department);
    const now = new Date().toISOString();

    console.log('🔍 Class Teacher Tracking - Input department:', department);
    console.log('🔍 Mapped to session department:', sessionDepartment);

    const { data: activeSessions, error: sessionError } = await supabase
      .from('feedback_sessions')
      .select('*')
      .eq('department', sessionDepartment)
      .eq('is_active', true)
      .lte('start_date', now)
      .gte('end_date', now)
      .order('created_at', { ascending: false });

    if (sessionError) throw sessionError;

    if (!activeSessions || activeSessions.length === 0) {
      console.log('⚠️ No active sessions found for department:', sessionDepartment);
      return null; // No active sessions
    }

    // Use the first active session
    const session = activeSessions[0];
    const mappedDepartment = this.mapDepartmentName(department);

    console.log('🔍 Active session found:', session.session_name);
    console.log('🔍 Mapped department for subjects:', mappedDepartment);

    // Get all students for the class (class_students uses short department names like 'CSE')
    // Map full department name to short name for class_students query
    const studentDepartment = this.mapFullNameToDepartment(department);
    console.log('🔍 Fetching students for department:', department, '→', studentDepartment);

    let studentsQuery = supabase
      .from('class_students')
      .select('usn, student_name, semester, section, department')
      .eq('department', studentDepartment); // Use mapped short department name (e.g., 'CSE')

    // IMPORTANT: For class teacher tracking, we must filter by specific semester and section
    if (semester && semester !== 'All') {
      studentsQuery = studentsQuery.eq('semester', semester);
      console.log('🔍 Filtering students by semester:', semester);
    }
    if (section && section !== 'All') {
      studentsQuery = studentsQuery.eq('section', section);
      console.log('🔍 Filtering students by section:', section);
    }

    // If no specific semester/section provided, this is an error for class teacher tracking
    if (!semester || !section || semester === 'All' || section === 'All') {
      console.warn('⚠️ Class teacher tracking requires specific semester and section');
    }

    const { data: students, error: studentsError } = await studentsQuery;
    if (studentsError) throw studentsError;

    console.log('🔍 Students found:', students?.length, students);

    // If no students found for this class, return empty tracking data
    if (!students || students.length === 0) {
      console.log('⚠️ No students found for class:', { department: studentDepartment, semester, section });
      return {
        session_id: session.id,
        session_name: session.session_name,
        department: session.department,
        semester: semester || 'All',
        section: section || 'All',
        total_students: 0,
        total_subjects: 0,
        submission_summary: [],
        student_submission_status: []
      };
    }

    // Get subject mappings for the specific class
    let subjectsQuery = supabase
      .from('simplified_subject_faculty_mappings')
      .select(`
        subject_code,
        subject_name,
        subject_type,
        faculty_1_id,
        faculty_2_id,
        semester,
        section,
        employee_details!faculty_1_id(id, full_name),
        employee_details_faculty_2:employee_details!faculty_2_id(id, full_name)
      `)
      .eq('department', mappedDepartment);

    // IMPORTANT: For class teacher tracking, filter by specific semester and section
    if (semester && semester !== 'All') {
      subjectsQuery = subjectsQuery.eq('semester', parseInt(semester));
      console.log('🔍 Filtering subjects by semester:', semester);
    }
    if (section && section !== 'All') {
      subjectsQuery = subjectsQuery.eq('section', section);
      console.log('🔍 Filtering subjects by section:', section);
    }

    const { data: subjects, error: subjectsError } = await subjectsQuery;
    if (subjectsError) throw subjectsError;

    console.log('🔍 Raw subjects found:', subjects?.length, subjects);

    // Filter out lab subjects - only track theory and elective subjects
    const theorySubjects = subjects?.filter(subject =>
      subject.subject_type !== 'laboratory' &&
      subject.subject_type !== 'lab' &&
      subject.subject_type !== 'skill_lab' &&
      subject.subject_code !== 'SKILL LAB'
    ) || [];

    console.log('🔍 Theory subjects after filtering:', theorySubjects.length, theorySubjects);

    // Get all feedback responses for this session, filtered by students in this class
    const studentUSNs = students?.map(s => s.usn) || [];
    console.log('🔍 Student USNs for filtering responses:', studentUSNs.length, studentUSNs);

    let responses = [];
    if (studentUSNs.length > 0) {
      const { data: responseData, error: responsesError } = await supabase
        .from('student_feedback_responses')
        .select('student_usn, faculty_id, subject_code, is_submitted')
        .eq('session_id', session.id)
        .eq('is_submitted', true)
        .in('student_usn', studentUSNs); // Only include responses from students in this class

      if (responsesError) throw responsesError;
      responses = responseData || [];
    }

    console.log('🔍 Filtered feedback responses:', responses?.length, responses);

    // Build subject submission summary - only for faculty teaching this specific class
    const subjectSummary = [];
    console.log('🔍 Building subject summary for', theorySubjects.length, 'theory subjects');

    for (const subject of theorySubjects) {
      console.log('🔍 Processing subject:', subject.subject_code, 'for semester', subject.semester, 'section', subject.section);

      // Check faculty 1
      if (subject.faculty_1_id && subject.faculty_1_id !== '00000000-0000-0000-0000-000000000000') {
        const facultyResponses = responses?.filter(r =>
          r.faculty_id === subject.faculty_1_id &&
          r.subject_code === subject.subject_code
        ) || [];

        const totalStudents = students?.length || 0;
        const submittedCount = facultyResponses.length;
        const completionPercentage = totalStudents > 0 ? (submittedCount / totalStudents) * 100 : 0;

        console.log(`🔍 Faculty 1 (${subject.employee_details?.full_name}): ${submittedCount}/${totalStudents} responses`);

        subjectSummary.push({
          faculty_id: subject.faculty_1_id,
          faculty_name: subject.employee_details?.full_name || 'Unknown Faculty',
          subject_code: subject.subject_code,
          subject_name: subject.subject_name,
          total_students: totalStudents,
          submitted_count: submittedCount,
          pending_count: totalStudents - submittedCount,
          completion_percentage: completionPercentage
        });
      }

      // Check faculty 2 if exists
      if (subject.faculty_2_id && subject.faculty_2_id !== '00000000-0000-0000-0000-000000000000') {
        const facultyResponses = responses?.filter(r =>
          r.faculty_id === subject.faculty_2_id &&
          r.subject_code === subject.subject_code
        ) || [];

        const totalStudents = students?.length || 0;
        const submittedCount = facultyResponses.length;
        const completionPercentage = totalStudents > 0 ? (submittedCount / totalStudents) * 100 : 0;

        console.log(`🔍 Faculty 2 (${subject.employee_details_faculty_2?.full_name}): ${submittedCount}/${totalStudents} responses`);

        subjectSummary.push({
          faculty_id: subject.faculty_2_id,
          faculty_name: subject.employee_details_faculty_2?.full_name || 'Unknown Faculty',
          subject_code: subject.subject_code,
          subject_name: subject.subject_name,
          total_students: totalStudents,
          submitted_count: submittedCount,
          pending_count: totalStudents - submittedCount,
          completion_percentage: completionPercentage
        });
      }
    }

    console.log('🔍 Final subject summary:', subjectSummary.length, 'entries');

    // Build student submission status
    const studentStatus = [];
    for (const student of students || []) {
      const studentResponses = responses?.filter(r => r.student_usn === student.usn) || [];
      const totalSubjects = subjectSummary.length;
      const completedSubjects = studentResponses.length;
      const pendingSubjects = [];

      // Find pending subjects for this student
      for (const subject of subjectSummary) {
        const hasSubmitted = studentResponses.some(r =>
          r.faculty_id === subject.faculty_id &&
          r.subject_code === subject.subject_code
        );
        if (!hasSubmitted) {
          pendingSubjects.push(`${subject.subject_code} (${subject.faculty_name})`);
        }
      }

      const completionPercentage = totalSubjects > 0 ? (completedSubjects / totalSubjects) * 100 : 0;

      studentStatus.push({
        student_usn: student.usn,
        student_name: student.student_name,
        total_subjects: totalSubjects,
        completed_subjects: completedSubjects,
        pending_subjects: pendingSubjects,
        completion_percentage: completionPercentage
      });
    }

    return {
      session_id: session.id,
      session_name: session.session_name,
      department: session.department,
      semester: session.semester || 'All',
      section: session.section || 'All',
      total_students: students?.length || 0,
      total_subjects: subjectSummary.length,
      submission_summary: subjectSummary,
      student_submission_status: studentStatus
    };
  }

  // ==================== UTILITY METHODS ====================

  private static maskUSN(usn: string): string {
    if (usn.length <= 5) return usn;
    return usn.substring(0, 5) + '****';
  }
}
