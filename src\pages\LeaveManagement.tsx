import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import {
  Calendar,
  FileText,
  BarChart3,
  Plus,
  Building2,
  AlertCircle,
  Clock,
  CheckCircle
} from 'lucide-react';
import LeaveDashboard from '@/components/leave-management/LeaveDashboard';
import LeaveRequestForm from '@/components/leave-management/LeaveRequestForm';
import EnhancedLeaveRequestForm from '@/components/leave-management/EnhancedLeaveRequestForm';
import LeaveRequestDataInspector from '@/components/debug/LeaveRequestDataInspector';
import TimetableDataInspector from '@/components/debug/TimetableDataInspector';

export default function LeaveManagement() {
  const { user } = useAuth();
  const {
    department,
    departmentName,
    fullName,
    loading: departmentLoading,
    error: departmentError
  } = useUserDepartment();

  // State management
  const [activeTab, setActiveTab] = useState('dashboard');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Handle successful form submission
  const handleFormSubmitSuccess = () => {
    // Refresh dashboard data
    setRefreshTrigger(prev => prev + 1);
    // Switch back to dashboard tab
    setActiveTab('dashboard');
  };

  // Show loading state while fetching department info
  if (departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading department information...</span>
        </div>
      </div>
    );
  }

  // Show error if department info couldn't be loaded
  if (departmentError || !department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {departmentError || 'Unable to load your department information. Please contact administrator.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Calendar className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold">Leave Management</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setRefreshTrigger(prev => prev + 1)}
          >
            <Clock className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <strong>Faculty:</strong> {fullName} | <strong>Department:</strong> {departmentName}
            </div>
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Academic Year: 2024-2025</span>
              </div>
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="apply" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Apply Leave
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Leave History
          </TabsTrigger>
          <TabsTrigger value="debug" className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            Debug
          </TabsTrigger>
        </TabsList>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold">Leave Dashboard</h2>
              <p className="text-muted-foreground">
                Overview of your leave balance, recent requests, and upcoming leaves
              </p>
            </div>
            <Button onClick={() => setActiveTab('apply')} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Apply for Leave
            </Button>
          </div>

          <LeaveDashboard refreshTrigger={refreshTrigger} />
        </TabsContent>

        {/* Apply Leave Tab */}
        <TabsContent value="apply" className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold">Apply for Leave</h2>
            <p className="text-muted-foreground">
              Submit a new leave request with automatic class impact analysis and substitute faculty assignment.
            </p>
          </div>

          <EnhancedLeaveRequestForm onSubmitSuccess={handleFormSubmitSuccess} />
        </TabsContent>

        {/* Leave History Tab */}
        <TabsContent value="history" className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold">Leave History</h2>
            <p className="text-muted-foreground">
              View all your past and current leave requests with detailed status information
            </p>
          </div>

          {/* Leave History Component - To be implemented */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Leave History & Reports
              </CardTitle>
              <CardDescription>
                Detailed view of all your leave requests with filtering and export options
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <FileText className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Leave History Coming Soon</h3>
                <p className="mb-4">
                  Detailed leave history with advanced filtering, search, and export functionality will be available soon.
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Filter by date range, leave type, and status</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Export leave reports to PDF/Excel</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Calendar view of leave history</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Leave pattern analysis and insights</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Debug Tab */}
        <TabsContent value="debug" className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold">Enhanced Leave Management Debug Tools</h2>
            <p className="text-muted-foreground">
              Debug tools to test class impact analysis, substitute attendance integration, and troubleshoot system issues
            </p>
          </div>

          <div className="grid gap-6">
            <TimetableDataInspector />
            <LeaveRequestDataInspector />
          </div>
        </TabsContent>
      </Tabs>

      {/* Quick Help Section */}
      <Card className="border-dashed">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <AlertCircle className="h-5 w-5 text-blue-600" />
            Quick Help & Guidelines
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-medium mb-2">Leave Application Guidelines:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Submit requests at least 3 days in advance for casual leave</li>
                <li>• Planned leaves require 7+ days advance notice</li>
                <li>• Emergency leaves can be applied retrospectively</li>
                <li>• Supporting documents may be required for certain leave types</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Leave Balance Information:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Casual Leave: 12 days per academic year</li>
                <li>• Sick Leave: 12 days per academic year</li>
                <li>• Earned Leave: 30 days per academic year</li>
                <li>• Balances reset at the start of each academic year</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
