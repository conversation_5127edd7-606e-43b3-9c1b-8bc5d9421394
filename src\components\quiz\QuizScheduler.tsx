import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users, Plus, Eye, Edit } from 'lucide-react';
import { QuizSchedule, QuizTemplate } from '@/types/quiz-system';

interface QuizSchedulerProps {
  onQuizScheduled: (schedule: QuizSchedule) => void;
  schedules: QuizSchedule[];
  templates: QuizTemplate[];
}

const QuizScheduler: React.FC<QuizSchedulerProps> = ({
  onQuizScheduled,
  schedules,
  templates
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'secondary';
      case 'active':
        return 'default';
      case 'completed':
        return 'outline';
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Quiz Scheduler</h3>
          <p className="text-sm text-muted-foreground">
            Schedule quizzes for your students with time controls
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Schedule Quiz
        </Button>
      </div>

      {/* Scheduled Quizzes */}
      <div className="space-y-4">
        {schedules.length > 0 ? (
          schedules.map((schedule) => (
            <Card key={schedule.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      {schedule.title}
                    </CardTitle>
                    <CardDescription>
                      {schedule.target_department} - Semester {schedule.target_semester}
                      {schedule.target_section && ` Section ${schedule.target_section}`}
                    </CardDescription>
                  </div>
                  <Badge variant={getStatusColor(schedule.status) as any}>
                    {schedule.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Start Time</p>
                      <p className="text-muted-foreground">
                        {new Date(schedule.start_time).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Duration</p>
                      <p className="text-muted-foreground">{schedule.duration_minutes} minutes</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Max Attempts</p>
                      <p className="text-muted-foreground">{schedule.max_attempts}</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-muted-foreground">No quizzes scheduled yet.</p>
              <p className="text-sm text-muted-foreground">
                Schedule your first quiz to make it available to students.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default QuizScheduler;
