import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import {
  Plus,
  FileText,
  Brain,
  Zap,
  Eye,
  Edit,
  CheckCircle,
  Clock,
  Target,
  BookOpen,
  GraduationCap,
  X,
  Trash2
} from 'lucide-react';

import { ComprehensiveQuizService, QuizTemplate, CourseMaterial, QuizQuestion, ContentWeighting } from '@/services/ComprehensiveQuizService';

interface ComprehensiveQuizCreatorProps {
  materials: CourseMaterial[];
  onTemplateCreated: (template: QuizTemplate) => void;
  templates: QuizTemplate[];
}

const ComprehensiveQuizCreator: React.FC<ComprehensiveQuizCreatorProps> = ({
  materials,
  onTemplateCreated,
  templates
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [creating, setCreating] = useState(false);
  const [generating, setGenerating] = useState<string | null>(null);
  const [availableModules, setAvailableModules] = useState<any[]>([]);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<QuizTemplate | null>(null);
  const [templateQuestions, setTemplateQuestions] = useState<QuizQuestion[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    target_module: '',
    duration_minutes: 60,
    total_marks: 20,
    difficulty_level: 'medium' as 'easy' | 'medium' | 'hard',
    source_material_ids: [] as string[],
    content_weighting: {
      textbook_based: 80,      // MANDATORY MINIMUM 80%
      syllabus_based: 10,      // REDUCED to 10%
      cross_referenced: 5,     // REDUCED to 5%
      ai_synthesis: 5          // REDUCED to 5%
    } as ContentWeighting
  });

  const { user } = useAuth();
  const { toast } = useToast();
  const { department, departmentName, loading: departmentLoading } = useUserDepartment();

  // Get unique subjects from materials
  const availableSubjects = React.useMemo(() => {
    const subjects = materials
      .filter(m => m.processing_status === 'completed')
      .reduce((acc, material) => {
        const key = `${material.subject_code}-${material.semester}-${material.section}`;
        if (!acc.find(s => s.key === key)) {
          acc.push({
            key,
            subject_code: material.subject_code,
            subject_name: material.subject_name,
            semester: material.semester,
            section: material.section,
            academic_year: material.academic_year
          });
        }
        return acc;
      }, [] as any[]);
    return subjects;
  }, [materials]);

  // Load modules when subject is selected
  useEffect(() => {
    const loadModules = async () => {
      if (!selectedSubject || !user?.id) return;

      const [subjectCode] = selectedSubject.split('-');
      try {
        const modules = await ComprehensiveQuizService.getDetectedModules(user.id, subjectCode);
        setAvailableModules(modules);
        console.log('📚 Loaded modules for subject:', subjectCode, modules);
      } catch (error) {
        console.error('Failed to load modules:', error);
        setAvailableModules([]);
      }
    };

    loadModules();
  }, [selectedSubject, user?.id]);

  const handleSubjectChange = (subjectKey: string) => {
    setSelectedSubject(subjectKey);
    setFormData(prev => ({ 
      ...prev, 
      target_module: '',
      source_material_ids: []
    }));
  };

  const handleMaterialSelection = (materialId: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        source_material_ids: [...prev.source_material_ids, materialId]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        source_material_ids: prev.source_material_ids.filter(id => id !== materialId)
      }));
    }
  };

  // Content weighting validation and adjustment
  const validateContentWeighting = (weighting: ContentWeighting): ContentWeighting => {
    // Apply bounds with TEXTBOOK-FOCUSED requirements
    const bounded = {
      textbook_based: Math.max(80, Math.min(95, weighting.textbook_based)),    // MANDATORY 80-95%
      syllabus_based: Math.max(5, Math.min(15, weighting.syllabus_based)),     // REDUCED 5-15%
      cross_referenced: Math.max(2, Math.min(8, weighting.cross_referenced)),  // REDUCED 2-8%
      ai_synthesis: Math.max(2, Math.min(5, weighting.ai_synthesis))           // REDUCED 2-5%
    };

    // Ensure total equals 100
    const total = bounded.textbook_based + bounded.syllabus_based + bounded.cross_referenced + bounded.ai_synthesis;
    if (total !== 100) {
      // Proportionally adjust to sum to 100, but maintain textbook minimum
      const factor = 100 / total;
      const adjusted = {
        textbook_based: Math.round(bounded.textbook_based * factor),
        syllabus_based: Math.round(bounded.syllabus_based * factor),
        cross_referenced: Math.round(bounded.cross_referenced * factor),
        ai_synthesis: Math.round(bounded.ai_synthesis * factor)
      };

      // Ensure textbook minimum is still maintained after adjustment
      if (adjusted.textbook_based < 80) {
        adjusted.textbook_based = 80;
        const remaining = 20;
        const otherTotal = adjusted.syllabus_based + adjusted.cross_referenced + adjusted.ai_synthesis;
        if (otherTotal > 0) {
          const otherFactor = remaining / otherTotal;
          adjusted.syllabus_based = Math.round(adjusted.syllabus_based * otherFactor);
          adjusted.cross_referenced = Math.round(adjusted.cross_referenced * otherFactor);
          adjusted.ai_synthesis = Math.round(adjusted.ai_synthesis * otherFactor);
        }
      }

      return adjusted;
    }

    return bounded;
  };

  const handleContentWeightingChange = (field: keyof ContentWeighting, value: number) => {
    setFormData(prev => {
      const newWeighting = { ...prev.content_weighting, [field]: value };

      // Auto-adjust other values to maintain 100% total
      const otherFields = Object.keys(newWeighting).filter(k => k !== field) as (keyof ContentWeighting)[];
      const otherTotal = otherFields.reduce((sum, key) => sum + newWeighting[key], 0);
      const remaining = 100 - value;

      if (remaining > 0 && otherTotal > 0) {
        const factor = remaining / otherTotal;
        otherFields.forEach(key => {
          newWeighting[key] = Math.round(newWeighting[key] * factor);
        });
      }

      const validatedWeighting = validateContentWeighting(newWeighting);

      return {
        ...prev,
        content_weighting: validatedWeighting
      };
    });
  };

  const handleCreateTemplate = async () => {
    if (!user?.id || !department) return;

    // Validate required fields
    const missingFields = [];
    if (!selectedSubject) missingFields.push('Subject');
    if (!formData.title) missingFields.push('Quiz Title');
    if (!formData.target_module) missingFields.push('Target Module');
    if (formData.source_material_ids.length === 0) missingFields.push('Source Materials');

    if (missingFields.length > 0) {
      toast({
        title: 'Missing Information',
        description: `Please select: ${missingFields.join(', ')}`,
        variant: 'destructive',
      });
      return;
    }

    try {
      setCreating(true);

      const selectedSubjectInfo = availableSubjects.find(s => s.key === selectedSubject);
      if (!selectedSubjectInfo) {
        throw new Error('Selected subject not found');
      }

      const templateData = {
        title: formData.title,
        description: formData.description,
        subject_code: selectedSubjectInfo.subject_code,
        subject_name: selectedSubjectInfo.subject_name,
        department: department,
        semester: selectedSubjectInfo.semester,
        section: selectedSubjectInfo.section,
        academic_year: selectedSubjectInfo.academic_year,
        target_module: formData.target_module,
        duration_minutes: formData.duration_minutes,
        total_marks: formData.total_marks,
        difficulty_level: formData.difficulty_level,
        source_material_ids: formData.source_material_ids,
        content_weighting: formData.content_weighting
      };

      const template = await ComprehensiveQuizService.createModuleQuizTemplate(user.id, templateData);

      onTemplateCreated(template);
      setShowCreateForm(false);

      toast({
        title: 'Template Created',
        description: `Quiz template "${formData.title}" created successfully for ${formData.target_module}`,
      });

      // Reset form
      setFormData({
        title: '',
        description: '',
        target_module: '',
        duration_minutes: 60,
        total_marks: 20,
        difficulty_level: 'medium',
        source_material_ids: [],
        content_weighting: {
          textbook_based: 80,      // MANDATORY MINIMUM 80%
          syllabus_based: 10,      // REDUCED to 10%
          cross_referenced: 5,     // REDUCED to 5%
          ai_synthesis: 5          // REDUCED to 5%
        }
      });
      setSelectedSubject('');

    } catch (error) {
      console.error('Template creation failed:', error);
      toast({
        title: 'Creation Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred.',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  const handleGenerateQuestions = async (templateId: string) => {
    try {
      setGenerating(templateId);

      const questions = await ComprehensiveQuizService.generateQuestionsForTemplate(templateId, 20);

      toast({
        title: 'Success',
        description: `AI generated ${questions.length} questions successfully using Gemini AI!`,
      });

      // Refresh template to show updated status
      const updatedTemplate = templates.find(t => t.id === templateId);
      if (updatedTemplate) {
        onTemplateCreated({ ...updatedTemplate, status: 'ai_generated' as any });
      }

      // Auto-load and show the generated questions
      await loadTemplateQuestions(templateId);
      setSelectedTemplate(updatedTemplate || null);

    } catch (error) {
      console.error('Question generation failed:', error);
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred.',
        variant: 'destructive',
      });
    } finally {
      setGenerating(null);
    }
  };

  const loadTemplateQuestions = async (templateId: string) => {
    try {
      setLoadingQuestions(true);
      const questions = await ComprehensiveQuizService.getTemplateQuestions(templateId);
      setTemplateQuestions(questions);
      console.log('📝 Loaded questions for template:', templateId, questions.length);
    } catch (error) {
      console.error('Failed to load questions:', error);
      toast({
        title: 'Loading Failed',
        description: 'Failed to load quiz questions.',
        variant: 'destructive',
      });
    } finally {
      setLoadingQuestions(false);
    }
  };

  const handleViewQuestions = async (template: QuizTemplate) => {
    setSelectedTemplate(template);
    await loadTemplateQuestions(template.id);
  };

  const handleDeleteTemplate = async (templateId: string, templateTitle: string) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete the quiz template "${templateTitle}"? This will also delete all generated questions. This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      await ComprehensiveQuizService.deleteQuizTemplate(templateId);

      // Remove from local state
      const updatedTemplates = templates.filter(t => t.id !== templateId);

      // If this was the selected template, clear the preview
      if (selectedTemplate?.id === templateId) {
        setSelectedTemplate(null);
        setTemplateQuestions([]);
      }

      toast({
        title: 'Template Deleted',
        description: `Quiz template "${templateTitle}" has been deleted successfully.`,
      });

      // Refresh the templates list
      window.location.reload(); // Simple refresh for now

    } catch (error) {
      console.error('Template deletion failed:', error);
      toast({
        title: 'Deletion Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred.',
        variant: 'destructive',
      });
    }
  };

  const getStatusBadge = (template: QuizTemplate) => {
    switch (template.status) {
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'ai_generated':
        return <Badge variant="secondary">AI Generated</Badge>;
      case 'reviewed':
        return <Badge variant="default">Reviewed</Badge>;
      case 'published':
        return <Badge variant="default">Published</Badge>;
      default:
        return <Badge variant="outline">{template.status}</Badge>;
    }
  };

  // Get materials for selected subject
  const subjectMaterials = React.useMemo(() => {
    if (!selectedSubject) return [];
    const [subjectCode] = selectedSubject.split('-');
    return materials.filter(m => 
      m.subject_code === subjectCode && m.processing_status === 'completed'
    );
  }, [selectedSubject, materials]);

  if (departmentLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        <span className="ml-2">Loading department information...</span>
      </div>
    );
  }

  if (!department) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-muted-foreground mb-2">Department information required</p>
          <p className="text-sm text-muted-foreground">
            Please contact your administrator to set up your department information.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">AI-Powered Quiz Templates</h3>
          <p className="text-sm text-muted-foreground">
            Create module-specific quizzes with intelligent AI question generation
            {departmentName && ` • ${departmentName}`}
          </p>
        </div>
        <Button 
          onClick={() => setShowCreateForm(true)} 
          disabled={!department || availableSubjects.length === 0}
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Quiz Template
        </Button>
      </div>

      {/* No subjects available */}
      {availableSubjects.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-muted-foreground mb-2">No course materials available</p>
            <p className="text-sm text-muted-foreground">
              Upload and process syllabus materials first to create quiz templates.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Create Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Create AI-Powered Quiz Template
            </CardTitle>
            <CardDescription>
              Select a module from your syllabus and create an intelligent quiz template
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Subject Selection */}
            <div className="space-y-2">
              <Label>Select Subject *</Label>
              <p className="text-xs text-muted-foreground">
                Choose from subjects where you have uploaded and processed course materials
              </p>
              <Select value={selectedSubject} onValueChange={handleSubjectChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a subject from your uploaded materials" />
                </SelectTrigger>
                <SelectContent>
                  {availableSubjects.map((subject) => (
                    <SelectItem key={subject.key} value={subject.key}>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {subject.subject_code} - {subject.subject_name}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Semester {subject.semester} • Section {subject.section}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {availableSubjects.length > 0 && (
                <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded border">
                  💡 <strong>Smart Subject Detection:</strong> Only showing subjects with processed materials
                </div>
              )}
            </div>

            {/* Module Selection */}
            {selectedSubject && (
              <div className="space-y-2">
                <Label>Target Module *</Label>
                <p className="text-xs text-muted-foreground">
                  AI-detected modules from your uploaded syllabus
                </p>
                {availableModules.length > 0 ? (
                  <Select 
                    value={formData.target_module} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, target_module: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a module for this quiz" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      {availableModules.map((module) => (
                        <SelectItem key={module.module_number} value={module.module_title}>
                          <div className="flex flex-col py-1">
                            <span className="font-medium">{module.module_title}</span>
                            <span className="text-xs text-muted-foreground">
                              {module.module_description}
                            </span>
                            <div className="flex gap-1 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {module.difficulty_level}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {module.estimated_duration_hours}h
                              </Badge>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                    <p className="text-sm text-yellow-700">
                      No modules detected for this subject. Make sure you have uploaded and processed a syllabus.
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Quiz Details */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Quiz Title *</Label>
                <Input
                  id="title"
                  placeholder="e.g., Module 1 Quiz - Introduction to Data Structures"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  placeholder="Brief description of the quiz content..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>

              {/* Quiz Settings */}
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration (minutes)</Label>
                  <Input
                    id="duration"
                    type="number"
                    value={formData.duration_minutes}
                    onChange={(e) => setFormData(prev => ({ ...prev, duration_minutes: parseInt(e.target.value) }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="total-marks">Total Marks</Label>
                  <Input
                    id="total-marks"
                    type="number"
                    value={formData.total_marks}
                    onChange={(e) => setFormData(prev => ({ ...prev, total_marks: parseInt(e.target.value) }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="difficulty">Difficulty Level</Label>
                  <Select 
                    value={formData.difficulty_level} 
                    onValueChange={(value: any) => setFormData(prev => ({ ...prev, difficulty_level: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Content Weighting Configuration */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-base font-medium">AI Question Generation Sources</Label>
                <p className="text-sm text-muted-foreground">
                  Configure how AI should distribute questions across different content sources. All percentages must sum to 100%.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg bg-gray-50">
                {/* Textbook-Based Questions - PRIMARY FOCUS */}
                <div className="space-y-2 col-span-full">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="textbook-weight" className="text-sm font-medium flex items-center gap-2">
                      📖 Textbook-Based Questions
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">PRIMARY FOCUS</span>
                    </Label>
                    <span className="text-sm font-bold text-green-600">
                      {formData.content_weighting.textbook_based}%
                    </span>
                  </div>
                  <Input
                    id="textbook-weight"
                    type="range"
                    min="80"
                    max="95"
                    value={formData.content_weighting.textbook_based}
                    onChange={(e) => handleContentWeightingChange('textbook_based', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    <strong>MANDATORY:</strong> Questions from specific textbook chapters and sections (80-95%).
                    This ensures questions are primarily sourced from your uploaded textbook materials.
                  </p>
                </div>

                {/* Syllabus-Based Questions - REDUCED */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="syllabus-weight" className="text-sm font-medium">
                      📚 Syllabus-Based Questions
                    </Label>
                    <span className="text-sm font-bold text-blue-600">
                      {formData.content_weighting.syllabus_based}%
                    </span>
                  </div>
                  <Input
                    id="syllabus-weight"
                    type="range"
                    min="5"
                    max="15"
                    value={formData.content_weighting.syllabus_based}
                    onChange={(e) => handleContentWeightingChange('syllabus_based', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Questions from module descriptions and learning objectives (5-15%)
                  </p>
                </div>

                {/* Cross-Referenced Questions - REDUCED */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="cross-ref-weight" className="text-sm font-medium">
                      🔗 Cross-Referenced Questions
                    </Label>
                    <span className="text-sm font-bold text-purple-600">
                      {formData.content_weighting.cross_referenced}%
                    </span>
                  </div>
                  <Input
                    id="cross-ref-weight"
                    type="range"
                    min="2"
                    max="8"
                    value={formData.content_weighting.cross_referenced}
                    onChange={(e) => handleContentWeightingChange('cross_referenced', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Questions that synthesize content from both syllabus and textbook materials (2-8%)
                  </p>
                </div>

                {/* AI Synthesis Questions - REDUCED */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="ai-synthesis-weight" className="text-sm font-medium">
                      🤖 AI Synthesis Questions
                    </Label>
                    <span className="text-sm font-bold text-orange-600">
                      {formData.content_weighting.ai_synthesis}%
                    </span>
                  </div>
                  <Input
                    id="ai-synthesis-weight"
                    type="range"
                    min="2"
                    max="5"
                    value={formData.content_weighting.ai_synthesis}
                    onChange={(e) => handleContentWeightingChange('ai_synthesis', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Questions using AI's domain knowledge to fill gaps or provide additional context (2-5%)
                  </p>
                </div>
              </div>

              {/* Total Validation */}
              <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded">
                <span className="text-sm font-medium text-blue-800">Total Distribution:</span>
                <span className={`text-sm font-bold ${
                  (formData.content_weighting.textbook_based +
                   formData.content_weighting.syllabus_based +
                   formData.content_weighting.cross_referenced +
                   formData.content_weighting.ai_synthesis) === 100
                    ? 'text-green-600' : 'text-red-600'
                }`}>
                  {formData.content_weighting.textbook_based +
                   formData.content_weighting.syllabus_based +
                   formData.content_weighting.cross_referenced +
                   formData.content_weighting.ai_synthesis}%
                </span>
              </div>

              {/* Textbook Focus Indicator */}
              <div className="p-3 bg-green-50 border border-green-200 rounded">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-green-600">✅</span>
                  <span className="text-sm font-medium text-green-800">Textbook-Focused Configuration</span>
                </div>
                <p className="text-xs text-green-700">
                  This configuration ensures {formData.content_weighting.textbook_based}% of questions come from specific textbook chapters,
                  providing precise content alignment with your uploaded materials.
                </p>
              </div>
            </div>

            {/* Source Materials */}
            {selectedSubject && subjectMaterials.length > 0 && (
              <div className="space-y-2">
                <Label>Source Materials *</Label>
                <p className="text-xs text-muted-foreground">
                  Select materials to use for AI question generation
                </p>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {subjectMaterials.map((material) => (
                    <div key={material.id} className="flex items-center space-x-2 p-2 border rounded">
                      <Checkbox
                        id={material.id}
                        checked={formData.source_material_ids.includes(material.id)}
                        onCheckedChange={(checked) =>
                          handleMaterialSelection(material.id, checked as boolean)
                        }
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          {material.content_type === 'syllabus' && <GraduationCap className="h-4 w-4" />}
                          {material.content_type === 'textbook' && <BookOpen className="h-4 w-4" />}
                          {material.content_type === 'reference' && <FileText className="h-4 w-4" />}
                          <span className="text-sm font-medium">{material.file_name}</span>
                          <Badge variant="outline" className="text-xs">
                            {material.content_type}
                          </Badge>
                        </div>
                        {material.content_type === 'syllabus' && material.ai_detected_modules && (
                          <p className="text-xs text-blue-600 mt-1">
                            🤖 {material.ai_detected_modules.length} modules detected
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button onClick={handleCreateTemplate} disabled={creating}>
                {creating ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Template
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Templates List */}
      <div className="space-y-4">
        {templates.length > 0 ? (
          templates.map((template) => (
            <Card key={template.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5" />
                      {template.title}
                    </CardTitle>
                    <CardDescription>
                      {template.subject_code} - {template.subject_name} | 
                      Module: {template.target_module} | 
                      {template.duration_minutes} min | {template.total_marks} marks
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(template)}
                    {template.is_ai_generated && (
                      <Badge variant="outline" className="text-purple-600">
                        <Brain className="h-3 w-3 mr-1" />
                        AI Generated
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    <p>Difficulty: {template.difficulty_level}</p>
                    <p>Created: {new Date(template.created_at).toLocaleDateString()}</p>
                  </div>
                  
                  <div className="flex gap-2">
                    {template.status === 'draft' && template.source_material_ids.length > 0 && (
                      <Button
                        size="sm"
                        onClick={() => handleGenerateQuestions(template.id)}
                        disabled={generating === template.id}
                      >
                        {generating === template.id ? (
                          <>
                            <Clock className="h-4 w-4 mr-1 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Zap className="h-4 w-4 mr-1" />
                            Generate Questions
                          </>
                        )}
                      </Button>
                    )}
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleViewQuestions(template)}
                      disabled={loadingQuestions}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      {loadingQuestions && selectedTemplate?.id === template.id ? 'Loading...' : 'View Questions'}
                    </Button>
                    
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteTemplate(template.id, template.title)}
                      className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>

                    {template.status === 'ai_generated' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setSelectedTemplate(template)}
                        className="text-purple-600 border-purple-200 hover:bg-purple-50"
                      >
                        <Brain className="h-4 w-4 mr-1" />
                        Regenerate
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-muted-foreground">No quiz templates created yet.</p>
              <p className="text-sm text-muted-foreground">
                Create your first AI-powered quiz template to get started.
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Question Preview Modal/Section */}
      {selectedTemplate && (
        <Card className="mt-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Quiz Preview: {selectedTemplate.title}
                </CardTitle>
                <CardDescription>
                  {selectedTemplate.subject_code} - {selectedTemplate.target_module} |
                  {templateQuestions.length} Questions | {selectedTemplate.total_marks} Marks
                </CardDescription>
              </div>
              <div className="flex gap-2">
                {selectedTemplate.status === 'ai_generated' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleGenerateQuestions(selectedTemplate.id)}
                    disabled={generating === selectedTemplate.id}
                    className="text-purple-600 border-purple-200 hover:bg-purple-50"
                  >
                    {generating === selectedTemplate.id ? (
                      <>
                        <Clock className="h-4 w-4 mr-1 animate-spin" />
                        Regenerating...
                      </>
                    ) : (
                      <>
                        <Brain className="h-4 w-4 mr-1" />
                        Regenerate Questions
                      </>
                    )}
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedTemplate(null);
                    setTemplateQuestions([]);
                  }}
                >
                  <X className="h-4 w-4 mr-1" />
                  Close Preview
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loadingQuestions ? (
              <div className="flex items-center justify-center py-8">
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                <span>Loading questions...</span>
              </div>
            ) : templateQuestions.length > 0 ? (
              <div className="space-y-6">
                {/* Quiz Header Info */}
                <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <p className="text-sm font-medium">Duration</p>
                    <p className="text-lg font-bold text-blue-600">{selectedTemplate.duration_minutes} min</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium">Questions</p>
                    <p className="text-lg font-bold text-green-600">{templateQuestions.length}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium">Total Marks</p>
                    <p className="text-lg font-bold text-purple-600">{selectedTemplate.total_marks}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium">Difficulty</p>
                    <p className="text-lg font-bold text-orange-600 capitalize">{selectedTemplate.difficulty_level}</p>
                  </div>
                </div>

                {/* Questions List */}
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {templateQuestions.map((question, index) => (
                    <div key={question.id} className="border rounded-lg p-4 bg-white">
                      {/* Question Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            Q{index + 1}
                          </Badge>
                          <Badge variant="outline" className="text-xs capitalize">
                            {question.difficulty_level}
                          </Badge>
                          {question.is_ai_generated && (
                            <Badge variant="outline" className="text-xs text-purple-600">
                              <Brain className="h-3 w-3 mr-1" />
                              AI Generated
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {question.marks} mark{question.marks !== 1 ? 's' : ''}
                        </div>
                      </div>

                      {/* Question Text */}
                      <div className="mb-4">
                        <p className="font-medium text-gray-900">{question.question_text}</p>
                      </div>

                      {/* Options */}
                      <div className="space-y-2 mb-4">
                        {question.options && typeof question.options === 'object' && (
                          Object.entries(question.options).map(([key, value]) => (
                            <div
                              key={key}
                              className={`p-2 rounded border ${
                                key === question.correct_answer
                                  ? 'bg-green-50 border-green-200 text-green-800'
                                  : 'bg-gray-50 border-gray-200'
                              }`}
                            >
                              <div className="flex items-center gap-2">
                                <span className="font-medium text-sm">
                                  {key}.
                                </span>
                                <span className="text-sm">{value as string}</span>
                                {key === question.correct_answer && (
                                  <CheckCircle className="h-4 w-4 text-green-600 ml-auto" />
                                )}
                              </div>
                            </div>
                          ))
                        )}
                      </div>

                      {/* Correct Answer & Explanation */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <span className="font-medium text-green-700">Correct Answer:</span>
                          <Badge variant="default" className="bg-green-600">
                            Option {question.correct_answer}
                          </Badge>
                        </div>

                        {question.explanation && (
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                            <p className="text-sm font-medium text-blue-800 mb-1">Explanation:</p>
                            <p className="text-sm text-blue-700">{question.explanation}</p>
                          </div>
                        )}

                        {/* AI Confidence & Tags */}
                        <div className="space-y-1 text-xs text-muted-foreground">
                          <div className="flex items-center gap-2">
                            {question.ai_confidence_score && (
                              <span>AI Confidence: {(question.ai_confidence_score * 100).toFixed(0)}%</span>
                            )}
                            <span className="text-blue-600">Source: {question.source_content_type}</span>
                          </div>

                          {question.ai_generation_prompt && (
                            <div className="text-green-600">
                              📖 {question.ai_generation_prompt}
                            </div>
                          )}

                          {question.topic_tags && question.topic_tags.length > 0 && (
                            <div className="flex gap-1">
                              {question.topic_tags.slice(0, 3).map((tag, i) => (
                                <Badge key={i} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Quiz Summary */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-3">Quiz Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p><strong>AI Generated:</strong> {templateQuestions.filter(q => q.is_ai_generated).length} questions</p>
                      <p><strong>Average Confidence:</strong> {
                        templateQuestions.length > 0
                          ? (templateQuestions.reduce((acc, q) => acc + (q.ai_confidence_score || 0), 0) / templateQuestions.length * 100).toFixed(0)
                          : 0
                      }%</p>
                    </div>
                    <div>
                      <p><strong>Difficulty Distribution:</strong></p>
                      <div className="flex gap-2 mt-1">
                        {['easy', 'medium', 'hard'].map(level => {
                          const count = templateQuestions.filter(q => q.difficulty_level === level).length;
                          return count > 0 ? (
                            <Badge key={level} variant="outline" className="text-xs">
                              {level}: {count}
                            </Badge>
                          ) : null;
                        })}
                      </div>
                    </div>
                    <div>
                      <p><strong>Content Source Distribution:</strong></p>
                      <div className="space-y-1 mt-1">
                        {['syllabus_based', 'textbook_based', 'cross_referenced', 'ai_synthesis'].map(sourceType => {
                          const count = templateQuestions.filter(q => q.source_content_type === sourceType).length;
                          const percentage = templateQuestions.length > 0 ? Math.round((count / templateQuestions.length) * 100) : 0;
                          const labels = {
                            syllabus_based: '📚 Syllabus',
                            textbook_based: '📖 Textbook',
                            cross_referenced: '🔗 Cross-Ref',
                            ai_synthesis: '🤖 AI Synthesis'
                          };
                          return count > 0 ? (
                            <div key={sourceType} className="flex justify-between text-xs">
                              <span>{labels[sourceType as keyof typeof labels]}:</span>
                              <span className="font-medium">{count} ({percentage}%)</span>
                            </div>
                          ) : null;
                        })}
                      </div>
                    </div>
                  </div>

                  {/* Content Weighting Configuration Display */}
                  {selectedTemplate.content_weighting && (
                    <div className="mt-4 pt-3 border-t">
                      <p className="font-medium mb-2">Configured Content Weighting:</p>
                      <div className="grid grid-cols-4 gap-2 text-xs">
                        <div className="text-center p-2 bg-blue-100 rounded">
                          <p className="font-medium">📚 Syllabus</p>
                          <p className="text-blue-700">{selectedTemplate.content_weighting.syllabus_based}%</p>
                        </div>
                        <div className="text-center p-2 bg-green-100 rounded">
                          <p className="font-medium">📖 Textbook</p>
                          <p className="text-green-700">{selectedTemplate.content_weighting.textbook_based}%</p>
                        </div>
                        <div className="text-center p-2 bg-purple-100 rounded">
                          <p className="font-medium">🔗 Cross-Ref</p>
                          <p className="text-purple-700">{selectedTemplate.content_weighting.cross_referenced}%</p>
                        </div>
                        <div className="text-center p-2 bg-orange-100 rounded">
                          <p className="font-medium">🤖 AI Synthesis</p>
                          <p className="text-orange-700">{selectedTemplate.content_weighting.ai_synthesis}%</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-muted-foreground">No questions found for this template.</p>
                <p className="text-sm text-muted-foreground">
                  Generate questions using AI to see them here.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ComprehensiveQuizCreator;
