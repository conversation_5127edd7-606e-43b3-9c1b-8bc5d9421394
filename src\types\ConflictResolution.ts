// ConflictResolution.ts - Type definitions for intelligent conflict resolution system
import { TimetableSlot } from "@/services/TimetableService";

/**
 * Enhanced conflict information for immediate resolution during theory slot generation
 */
export interface EnhancedConflictInfo {
  facultyId: string;
  day: string;
  timeSlot: string;
  conflictType: 'theory' | 'lab';
  otherSemester: string;
  otherSection: string;
  subjectCode?: string;
  theorySlot?: TimetableSlot;
  needsImmediateResolution?: boolean;
}

export interface ConflictInfo {
  id: string;
  type: 'faculty_double_booking' | 'consecutive_theory' | 'lab_theory_overlap' | 'faculty_overload';
  severity: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  affectedSlots: ConflictSlot[];
  facultyId: string;
  facultyName: string;
  day: string;
  timeSlot: string;
  academicYear: string;
  department: string;
}

export interface ConflictSlot {
  slotId: string;
  semester: string;
  section: string;
  subjectCode: string;
  subjectName: string;
  subjectType: 'theory' | 'lab' | 'tutorial';
  day: string;
  timeSlot: string;
  facultyId: string;
  facultyName: string;
  faculty2Id?: string;
  faculty2Name?: string;
}

export interface SwapCandidate {
  facultyId: string;
  facultyName: string;
  currentSlot: ConflictSlot;
  availabilityScore: number;
  swapFeasibility: number;
  alternativeSlots: AlternativeSlot[];
}

export interface AlternativeSlot {
  day: string;
  timeSlot: string;
  score: number;
  reason: string;
  conflicts: ConflictInfo[];
}

export interface SwapProposal {
  id: string;
  type: 'direct_swap' | 'cascading_swap' | 'relocation';
  primaryFaculty: {
    facultyId: string;
    facultyName: string;
    fromSlot: ConflictSlot;
    toSlot: AlternativeSlot;
  };
  secondaryFaculty?: {
    facultyId: string;
    facultyName: string;
    fromSlot: ConflictSlot;
    toSlot: AlternativeSlot;
  };
  cascadingSwaps: SwapProposal[];
  totalScore: number;
  estimatedSuccess: number;
  potentialConflicts: ConflictInfo[];
}

export interface FacultyAvailabilityProfile {
  facultyId: string;
  facultyName: string;
  totalAvailableSlots: number;
  availableSlotsByDay: Record<string, string[]>;
  occupiedSlotsByDay: Record<string, string[]>;
  flexibilityScore: number;
  workloadDistribution: Record<string, number>;
  preferredDays: string[];
  constrainedDays: string[];
}

export interface ConflictResolutionResult {
  success: boolean;
  resolvedConflicts: ConflictInfo[];
  unresolvedConflicts: ConflictInfo[];
  appliedSwaps: SwapProposal[];
  facultyAvailabilityUpdates: FacultyAvailabilityUpdate[];
  warnings: string[];
  executionTime: number;
}

export interface FacultyAvailabilityUpdate {
  facultyId: string;
  facultyName: string;
  previousAvailability: {
    vacant_by_day: Record<string, number>;
    vacant_count_by_day: Record<string, string[]>;
  };
  newAvailability: {
    vacant_by_day: Record<string, number>;
    vacant_count_by_day: Record<string, string[]>;
  };
  changedDays: string[];
}

export interface ConflictResolutionConfig {
  maxCascadingDepth: number;
  maxSwapAttempts: number;
  prioritizeHighAvailabilityFaculty: boolean;
  allowConsecutiveTheorySlots: boolean;
  preserveLabSlotPriority: boolean;
  enableCrossValidation: boolean;
  timeoutMs: number;
}

export interface ConflictDetectionParams {
  academicYear: string;
  department: string;
  semester?: string;
  section?: string;
  facultyId?: string;
  includeAllSemesters?: boolean;
}

export interface SlotSwapValidation {
  isValid: boolean;
  conflicts: ConflictInfo[];
  warnings: string[];
  facultyAvailabilityImpact: FacultyAvailabilityUpdate[];
  cascadingEffects: ConflictInfo[];
}

/**
 * Information about a rescheduled slot for audit purposes
 */
export interface RescheduledSlotInfo {
  originalSlot: TimetableSlot;
  newSlot: TimetableSlot;
  reason: string;
  conflictType: 'lab' | 'theory';
  facultyId: string;
  facultyName: string;
}

/**
 * Audit log entry for conflict resolution actions
 */
export interface ConflictResolutionAuditLog {
  id: string;
  action_type: 'theory_slot_rescheduled' | 'intra_class_swap' | 'conflict_resolved';
  slot_id: string;
  subject_code: string;
  faculty_id: string;
  original_day: string;
  original_time_slot: string;
  new_day: string;
  new_time_slot: string;
  reason: string;
  conflict_type: 'lab' | 'theory';
  academic_year: string;
  department: string;
  semester: string;
  section: string;
  created_at: string;
}

/**
 * Alternative slot option for rescheduling
 */
export interface AlternativeSlotOption {
  day: string;
  timeSlot: string;
  score: number;
  reason: string;
  facultyAvailable: boolean;
  classAvailable: boolean;
  conflicts: string[];
}
