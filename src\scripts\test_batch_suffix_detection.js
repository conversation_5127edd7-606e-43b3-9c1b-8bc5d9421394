/**
 * Test script for batch suffix detection and lab subject mapping
 * Tests the enhanced StudentProgressService functionality
 */

// Mock the StudentProgressService methods for testing
class TestStudentProgressService {
  static get LAB_SUBJECT_MAPPINGS() {
    return {
      'BCS403': 'DBMS Lab',    // DBMS Lab
      'BCS402': 'MC Lab',      // MC Lab
      'BCS401': 'ADA Lab',     // ADA Lab
      'BCS404': 'CN Lab',      // CN Lab
      'BCS405': 'Software Engineering Lab',  // SE Lab
      'BCSL403': 'DBMS Lab',   // DBMS Lab (alternative code)
      'BCSL402': 'MC Lab',     // MC Lab (alternative code)
      'BCSL401': 'ADA Lab',    // ADA Lab (alternative code)
      'BCSL404': 'CN Lab',     // CN Lab (alternative code)
      'BCSL405': 'Software Engineering Lab',  // SE Lab (alternative code)
    };
  }

  /**
   * Detect if a subject code has a batch suffix (e.g., "_A1", "_B2")
   * Returns the base subject code and batch identifier if found
   */
  static detectBatchSuffix(subjectCode) {
    if (!subjectCode) return { baseCode: subjectCode, isLabBatch: false };

    // Pattern for batch suffixes: _A1, _A2, _B1, _B2, etc.
    const batchPattern = /^(.+)_([A-Z]\d+)$/;
    const match = subjectCode.match(batchPattern);

    if (match) {
      const [, baseCode, batchSuffix] = match;
      return {
        baseCode,
        batchSuffix,
        isLabBatch: true
      };
    }

    return { baseCode: subjectCode, isLabBatch: false };
  }

  /**
   * Normalize subject codes to handle variations like BCS401_THEORY -> BCS401
   * Enhanced to also handle batch suffixes like BCS402_A1 -> BCS402
   */
  static normalizeSubjectCode(subjectCode) {
    if (!subjectCode) return subjectCode;

    // First check for batch suffixes
    const { baseCode, isLabBatch } = this.detectBatchSuffix(subjectCode);
    if (isLabBatch) {
      return baseCode;
    }

    // Remove common suffixes used in attendance/IA systems
    return subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
  }

  /**
   * Get the appropriate subject name for a subject code, handling lab batches
   */
  static getSubjectDisplayName(subjectCode, originalSubjectName) {
    const { baseCode, batchSuffix, isLabBatch } = this.detectBatchSuffix(subjectCode);

    // For lab batches (with batch suffixes like _A1, _B2), always use lab mappings
    if (isLabBatch) {
      const labName = this.LAB_SUBJECT_MAPPINGS[baseCode];
      if (labName) {
        return batchSuffix ? `${labName} (${batchSuffix})` : labName;
      }
    }

    // For subjects with explicit _LAB suffix, use lab mappings
    if (subjectCode.includes('_LAB')) {
      const normalizedCode = this.normalizeSubjectCode(subjectCode);
      const labName = this.LAB_SUBJECT_MAPPINGS[normalizedCode];
      if (labName) {
        return labName;
      }
    }

    // For regular subjects, only use lab mappings if:
    // 1. No original name provided OR original name is "Unknown Subject"
    // 2. AND the subject code suggests it's a lab (starts with BCSL or has lab indicators)
    if (!originalSubjectName || originalSubjectName === 'Unknown Subject') {
      const normalizedCode = this.normalizeSubjectCode(subjectCode);

      // Only apply lab mappings for codes that clearly indicate lab subjects
      const isLikelyLab = normalizedCode.startsWith('BCSL') ||
                         subjectCode.includes('LAB') ||
                         subjectCode.includes('_LAB') ||
                         isLabBatch;

      if (isLikelyLab) {
        const labName = this.LAB_SUBJECT_MAPPINGS[normalizedCode];
        if (labName) {
          return labName;
        }
      }
    }

    return originalSubjectName || 'Unknown Subject';
  }
}

// Test cases
function runTests() {
  console.log('🧪 Testing Batch Suffix Detection and Lab Subject Mapping\n');

  // Test 1: Batch suffix detection
  console.log('📋 Test 1: Batch Suffix Detection');
  const batchTests = [
    { input: 'BCS402_A1', expected: { baseCode: 'BCS402', batchSuffix: 'A1', isLabBatch: true } },
    { input: 'BCS402_A2', expected: { baseCode: 'BCS402', batchSuffix: 'A2', isLabBatch: true } },
    { input: 'BCS402_B1', expected: { baseCode: 'BCS402', batchSuffix: 'B1', isLabBatch: true } },
    { input: 'BCS402_B2', expected: { baseCode: 'BCS402', batchSuffix: 'B2', isLabBatch: true } },
    { input: 'BCS403_C1', expected: { baseCode: 'BCS403', batchSuffix: 'C1', isLabBatch: true } },
    { input: 'BCS401', expected: { baseCode: 'BCS401', isLabBatch: false } },
    { input: 'BCS401_THEORY', expected: { baseCode: 'BCS401_THEORY', isLabBatch: false } },
    { input: 'BCS401_LAB', expected: { baseCode: 'BCS401_LAB', isLabBatch: false } },
  ];

  batchTests.forEach(test => {
    const result = TestStudentProgressService.detectBatchSuffix(test.input);
    const passed = JSON.stringify(result) === JSON.stringify(test.expected);
    console.log(`  ${passed ? '✅' : '❌'} ${test.input} → ${JSON.stringify(result)}`);
    if (!passed) {
      console.log(`    Expected: ${JSON.stringify(test.expected)}`);
    }
  });

  // Test 2: Subject code normalization
  console.log('\n📋 Test 2: Subject Code Normalization');
  const normalizationTests = [
    { input: 'BCS402_A1', expected: 'BCS402' },
    { input: 'BCS402_A2', expected: 'BCS402' },
    { input: 'BCS402_B1', expected: 'BCS402' },
    { input: 'BCS403_C1', expected: 'BCS403' },
    { input: 'BCS401_THEORY', expected: 'BCS401' },
    { input: 'BCS401_LAB', expected: 'BCS401' },
    { input: 'BCS401', expected: 'BCS401' },
    { input: 'BCSL404_PRACTICAL', expected: 'BCSL404' },
  ];

  normalizationTests.forEach(test => {
    const result = TestStudentProgressService.normalizeSubjectCode(test.input);
    const passed = result === test.expected;
    console.log(`  ${passed ? '✅' : '❌'} ${test.input} → ${result} (expected: ${test.expected})`);
  });

  // Test 3: Subject display name mapping
  console.log('\n📋 Test 3: Subject Display Name Mapping');
  const displayNameTests = [
    { input: 'BCS402_A1', originalName: 'Unknown Subject', expected: 'MC Lab (A1)' },
    { input: 'BCS402_A2', originalName: 'Unknown Subject', expected: 'MC Lab (A2)' },
    { input: 'BCS402_B1', originalName: 'Unknown Subject', expected: 'MC Lab (B1)' },
    { input: 'BCS403_A1', originalName: 'Unknown Subject', expected: 'DBMS Lab (A1)' },
    { input: 'BCS401_B2', originalName: 'Unknown Subject', expected: 'ADA Lab (B2)' },
    { input: 'BCS404_A1', originalName: 'Unknown Subject', expected: 'CN Lab (A1)' },
    { input: 'BCS405_B1', originalName: 'Unknown Subject', expected: 'Software Engineering Lab (B1)' },
    { input: 'BCS402', originalName: 'Unknown Subject', expected: 'Unknown Subject' }, // Theory subject - no hardcoded fallback
    { input: 'BCS403', originalName: 'Unknown Subject', expected: 'Unknown Subject' }, // Theory subject - no hardcoded fallback
    { input: 'BCS405', originalName: 'Unknown Subject', expected: 'Unknown Subject' }, // Theory subject - no hardcoded fallback
    { input: 'BCS401', originalName: 'Algorithm Design and Analysis', expected: 'Algorithm Design and Analysis' },
    { input: 'BCS401', originalName: 'Unknown Subject', expected: 'Unknown Subject' }, // Theory subject should NOT map to lab
    { input: 'BCS401_LAB', originalName: 'Unknown Subject', expected: 'ADA Lab' }, // Explicit _LAB should map to lab
    { input: 'BCSL401', originalName: 'Unknown Subject', expected: 'ADA Lab' }, // BCSL prefix should map to lab
    { input: 'BCS999_A1', originalName: 'Unknown Subject', expected: 'Unknown Subject' }, // Unknown lab
  ];

  displayNameTests.forEach(test => {
    const result = TestStudentProgressService.getSubjectDisplayName(test.input, test.originalName);
    const passed = result === test.expected;
    console.log(`  ${passed ? '✅' : '❌'} ${test.input} (${test.originalName}) → ${result}`);
    if (!passed) {
      console.log(`    Expected: ${test.expected}`);
    }
  });

  console.log('\n🎉 Test completed! Check results above for any failures.');
}

// Run the tests
runTests();
