
import React from "react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from "react-hook-form";
import { Separator } from "@/components/ui/separator";
import LabSlotSelection from "@/components/subjects/LabSlotSelection";
import FlexibleLabSlotSelection from "@/components/subjects/FlexibleLabSlotSelection";
import { MappingFormData } from "@/hooks/useSubjectMappingForm";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ToggleLeft, ToggleRight } from "lucide-react";

interface LabInfoFieldsProps {
  form: UseFormReturn<MappingFormData>;
  hoursPerSlot: number;
  disabled?: boolean;
  isLabSubject: boolean;
  academicYear?: string;
  department?: string;
  semester?: string;
}

export default function LabInfoFields({
  form,
  hoursPerSlot,
  disabled = false,
  isLabSubject,
  academicYear = "2024-2025",
  department = "cse",
  semester = "4",
}: LabInfoFieldsProps) {
  const [useFlexibleTimeSelection, setUseFlexibleTimeSelection] = useState(true);

  if (!isLabSubject) {
    return null;
  }

  return (
    <>
      <Separator />
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Lab Scheduling</h3>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setUseFlexibleTimeSelection(!useFlexibleTimeSelection)}
            className="flex items-center gap-2"
          >
            {useFlexibleTimeSelection ? (
              <>
                <ToggleRight className="h-4 w-4" />
                Flexible Time Selection
              </>
            ) : (
              <>
                <ToggleLeft className="h-4 w-4" />
                Predefined Time Slots
              </>
            )}
          </Button>
        </div>

        <FormField
          control={form.control}
          name="slotsPerWeek"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Lab Slots Per Week</FormLabel>
              <Select
                value={String(field.value || 1)}
                onValueChange={(value) => field.onChange(Number(value))}
                disabled={disabled}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select number of slots" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="1">1 slot</SelectItem>
                  <SelectItem value="2">2 slots</SelectItem>
                  <SelectItem value="3">3 slots</SelectItem>
                  <SelectItem value="4">4 slots</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                Number of lab sessions per week ({hoursPerSlot} hours per slot)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {useFlexibleTimeSelection ? (
          <FlexibleLabSlotSelection
            form={form}
            slotNumber={1}
            disabled={disabled}
            hoursPerSlot={hoursPerSlot}
            academicYear={academicYear}
            department={department}
            semester={semester}
          />
        ) : (
          <LabSlotSelection
            form={form}
            slotNumber={1}
            disabled={disabled}
            hoursPerSlot={hoursPerSlot}
            academicYear={academicYear}
            department={department}
            semester={semester}
          />
        )}

        {form.watch("slotsPerWeek") >= 2 && (
          useFlexibleTimeSelection ? (
            <FlexibleLabSlotSelection
              form={form}
              slotNumber={2}
              disabled={disabled}
              hoursPerSlot={hoursPerSlot}
              academicYear={academicYear}
              department={department}
              semester={semester}
            />
          ) : (
            <LabSlotSelection
              form={form}
              slotNumber={2}
              disabled={disabled}
              hoursPerSlot={hoursPerSlot}
              academicYear={academicYear}
              department={department}
              semester={semester}
            />
          )
        )}

        {form.watch("slotsPerWeek") >= 3 && (
          useFlexibleTimeSelection ? (
            <FlexibleLabSlotSelection
              form={form}
              slotNumber={3}
              disabled={disabled}
              hoursPerSlot={hoursPerSlot}
              academicYear={academicYear}
              department={department}
              semester={semester}
            />
          ) : (
            <LabSlotSelection
              form={form}
              slotNumber={3}
              disabled={disabled}
              hoursPerSlot={hoursPerSlot}
              academicYear={academicYear}
              department={department}
              semester={semester}
            />
          )
        )}

        {form.watch("slotsPerWeek") >= 4 && (
          useFlexibleTimeSelection ? (
            <FlexibleLabSlotSelection
              form={form}
              slotNumber={4}
              disabled={disabled}
              hoursPerSlot={hoursPerSlot}
              academicYear={academicYear}
              department={department}
              semester={semester}
            />
          ) : (
            <LabSlotSelection
              form={form}
              slotNumber={4}
              disabled={disabled}
              hoursPerSlot={hoursPerSlot}
              academicYear={academicYear}
              department={department}
              semester={semester}
            />
          )
        )}
      </div>
    </>
  );
}
