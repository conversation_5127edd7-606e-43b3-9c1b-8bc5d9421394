// ConflictResolutionPanel.tsx - UI component for intelligent conflict resolution

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertTriangle, CheckCircle, Clock, Users, Shuffle } from 'lucide-react';
import { ConflictResolutionService } from '@/services/ConflictResolutionService';
import { ConflictDetectionParams } from '@/types/ConflictResolution';
import { toast } from '@/components/ui/use-toast';

interface ConflictResolutionPanelProps {
  academicYear: string;
  department: string;
  semester?: string;
  section?: string;
  onResolutionComplete?: () => void;
}

interface ResolutionResult {
  success: boolean;
  resolvedConflicts: number;
  unresolvedConflicts: number;
  appliedSwaps: number;
  facultyUpdates: number;
  warnings: string[];
  executionTime: number;
}

export const ConflictResolutionPanel: React.FC<ConflictResolutionPanelProps> = ({
  academicYear,
  department,
  semester,
  section,
  onResolutionComplete
}) => {
  const [isResolving, setIsResolving] = useState(false);
  const [lastResult, setLastResult] = useState<ResolutionResult | null>(null);

  const handleResolveConflicts = async () => {
    setIsResolving(true);
    setLastResult(null);

    try {
      const params: ConflictDetectionParams = {
        academicYear,
        department,
        semester,
        section,
        includeAllSemesters: !semester || !section
      };

      console.log('Starting intelligent conflict resolution with params:', params);

      const result = await ConflictResolutionService.resolveAllConflictsIntelligently(params);

      setLastResult(result);

      if (result.success) {
        toast({
          title: "Conflicts Resolved Successfully",
          description: `Resolved ${result.resolvedConflicts} conflicts with ${result.appliedSwaps} smart swaps in ${result.executionTime}ms`,
          variant: "default"
        });
      } else {
        toast({
          title: "Conflict Resolution Completed",
          description: `Resolved ${result.resolvedConflicts} conflicts, ${result.unresolvedConflicts} remain unresolved`,
          variant: result.unresolvedConflicts > 0 ? "destructive" : "default"
        });
      }

      if (onResolutionComplete) {
        onResolutionComplete();
      }
    } catch (error) {
      console.error('Error during conflict resolution:', error);
      toast({
        title: "Error",
        description: `Failed to resolve conflicts: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive"
      });
    } finally {
      setIsResolving(false);
    }
  };

  const getSeverityColor = (severity: 'critical' | 'high' | 'medium' | 'low') => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shuffle className="h-5 w-5" />
          Intelligent Conflict Resolution
        </CardTitle>
        <CardDescription>
          Automatically detect and resolve scheduling conflicts using smart slot swapping
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Button
            onClick={handleResolveConflicts}
            disabled={isResolving}
            className="flex items-center gap-2"
          >
            {isResolving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Resolving Conflicts...
              </>
            ) : (
              <>
                <Shuffle className="h-4 w-4" />
                Resolve Conflicts
              </>
            )}
          </Button>
          
          {semester && section && (
            <Badge variant="outline">
              {semester}-{section}
            </Badge>
          )}
        </div>

        {lastResult && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="text-sm font-medium text-green-900">Resolved</div>
                  <div className="text-lg font-bold text-green-700">{lastResult.resolvedConflicts}</div>
                </div>
              </div>

              <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <div>
                  <div className="text-sm font-medium text-red-900">Unresolved</div>
                  <div className="text-lg font-bold text-red-700">{lastResult.unresolvedConflicts}</div>
                </div>
              </div>

              <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
                <Shuffle className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-sm font-medium text-blue-900">Swaps Applied</div>
                  <div className="text-lg font-bold text-blue-700">{lastResult.appliedSwaps}</div>
                </div>
              </div>

              <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
                <Users className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="text-sm font-medium text-purple-900">Faculty Updates</div>
                  <div className="text-lg font-bold text-purple-700">{lastResult.facultyUpdates}</div>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              Execution time: {lastResult.executionTime}ms
            </div>

            {lastResult.warnings.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-1">Warnings:</div>
                  <ul className="list-disc list-inside space-y-1">
                    {lastResult.warnings.map((warning, index) => (
                      <li key={index} className="text-sm">{warning}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            <div className="text-sm text-gray-600">
              {lastResult.success ? (
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  All conflicts resolved successfully
                </div>
              ) : (
                <div className="flex items-center gap-2 text-amber-600">
                  <AlertTriangle className="h-4 w-4" />
                  Some conflicts remain unresolved
                </div>
              )}
            </div>
          </div>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>How it works:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>Detects faculty double-booking and consecutive theory slot violations</li>
            <li>Analyzes faculty availability patterns to find optimal swap candidates</li>
            <li>Prioritizes high-availability faculty for swapping to minimize disruption</li>
            <li>Updates faculty availability data automatically after successful swaps</li>
            <li>Prevents cascading conflicts through intelligent validation</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
