-- Add sample BCS402 (Microcontrollers) IA marks for student USN 1KS23CS001
-- This will demonstrate the complete IA integration workflow

-- Get student ID first (should be 4b4b0737-262f-4bc5-bc11-a17b29b4ee54)
-- Student USN: 1KS23CS001

-- Insert BCS402 IA marks
INSERT INTO internal_assessments (
    student_id,
    subject_code,
    department,
    semester,
    section,
    academic_year,
    ia1_marks,
    ia2_marks,
    ia3_marks,
    assignment_marks,
    theory_marks,
    faculty_id,
    created_at,
    updated_at
) VALUES (
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',  -- Student ID for USN 1KS23CS001
    'BCS402',                                 -- Subject: Microcontrollers
    'cse',                                    -- Department (lowercase to match existing)
    '4',                                      -- Semester
    'A',                                      -- Section
    '2025-2026',                             -- Academic year (to match existing)
    18,                                       -- IA1: 18/25
    21,                                       -- IA2: 21/25
    19,                                       -- IA3: 19/25
    7,                                        -- Assignment: 7/10
    NULL,                                     -- Theory marks (not used)
    NULL,                                     -- Faculty ID (can be NULL for testing)
    NOW(),                                    -- Created timestamp
    NOW()                                     -- Updated timestamp
);

-- Also complete the BCS401 marks (currently only has IA1=20)
UPDATE internal_assessments 
SET 
    ia2_marks = 22,        -- IA2: 22/25
    ia3_marks = 24,        -- IA3: 24/25
    assignment_marks = 8,  -- Assignment: 8/10
    updated_at = NOW()
WHERE 
    student_id = '4b4b0737-262f-4bc5-bc11-a17b29b4ee54'
    AND subject_code = 'BCS401'
    AND academic_year = '2025-2026';

-- Add a few more subjects for comprehensive testing
INSERT INTO internal_assessments (
    student_id,
    subject_code,
    department,
    semester,
    section,
    academic_year,
    ia1_marks,
    ia2_marks,
    ia3_marks,
    assignment_marks,
    theory_marks,
    faculty_id,
    created_at,
    updated_at
) VALUES 
-- BCS403 - DATABASE MANAGEMENT SYSTEMS
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BCS403',
    'cse',
    '4',
    'A',
    '2025-2026',
    25,  -- IA1: 25/25 (Full marks)
    23,  -- IA2: 23/25
    22,  -- IA3: 22/25
    9,   -- Assignment: 9/10
    NULL,
    NULL,
    NOW(),
    NOW()
),
-- BCS404 - COMPUTER NETWORKS
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BCS404',
    'cse',
    '4',
    'A',
    '2025-2026',
    20,  -- IA1: 20/25
    18,  -- IA2: 18/25
    21,  -- IA3: 21/25
    8,   -- Assignment: 8/10
    NULL,
    NULL,
    NOW(),
    NOW()
);

-- Verification query to check all inserted marks
SELECT 
    subject_code,
    ia1_marks,
    ia2_marks,
    ia3_marks,
    assignment_marks,
    (COALESCE(ia1_marks, 0) + COALESCE(ia2_marks, 0) + COALESCE(ia3_marks, 0) + COALESCE(assignment_marks, 0)) as total_marks,
    academic_year,
    department,
    created_at,
    updated_at
FROM internal_assessments 
WHERE student_id = '4b4b0737-262f-4bc5-bc11-a17b29b4ee54'
ORDER BY subject_code;
