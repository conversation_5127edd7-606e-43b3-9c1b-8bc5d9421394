import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  Calendar,
  Clock,
  BookOpen,
  Users,
  MapPin,
  UserCheck,
  AlertTriangle,
  CheckCircle,
  XCircle,
  FileText,
  Loader2,
  GraduationCap,
  FlaskConical,
  Monitor,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  TrendingUp,
  Shield,
  Target,
  User,
  Building2
} from 'lucide-react';
import { LeaveRequest, AffectedClass, LeaveManagementService } from '@/services/LeaveManagementService';

interface EnhancedLeaveApprovalDialogProps {
  isOpen: boolean;
  onClose: () => void;
  leaveRequest: LeaveRequest | null;
  onApprove: () => Promise<void>;
  onReject: (reason: string) => Promise<void>;
  processing: boolean;
}

export default function EnhancedLeaveApprovalDialog({
  isOpen,
  onClose,
  leaveRequest,
  onApprove,
  onReject,
  processing
}: EnhancedLeaveApprovalDialogProps) {
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectionForm, setShowRejectionForm] = useState(false);
  const [enrichedLeaveRequest, setEnrichedLeaveRequest] = useState<LeaveRequest | null>(null);
  const [enriching, setEnriching] = useState(false);

  // Enrich the leave request data when it changes
  useEffect(() => {
    const enrichLeaveRequestData = async () => {
      if (!leaveRequest || !leaveRequest.affected_classes) {
        setEnrichedLeaveRequest(leaveRequest);
        return;
      }

      try {
        setEnriching(true);
        console.log('🔄 Enriching leave request data for approval dialog');

        // Enrich the affected classes data with faculty ID for better lookup
        const enrichedClasses = await LeaveManagementService.enrichAffectedClassesData(
          leaveRequest.affected_classes,
          leaveRequest.faculty_id // Pass faculty ID for schedule lookup
        );

        // Create enriched leave request
        const enriched = {
          ...leaveRequest,
          affected_classes: enrichedClasses
        };

        setEnrichedLeaveRequest(enriched);
        console.log('✅ Leave request data enriched successfully');
      } catch (error) {
        console.error('❌ Error enriching leave request data:', error);
        // Fall back to original data if enrichment fails
        setEnrichedLeaveRequest(leaveRequest);
      } finally {
        setEnriching(false);
      }
    };

    enrichLeaveRequestData();
  }, [leaveRequest]);

  // Use enriched data if available, otherwise fall back to original
  const displayLeaveRequest = enrichedLeaveRequest || leaveRequest;

  if (!displayLeaveRequest) return null;

  // Calculate substitution statistics
  const getSubstitutionStats = () => {
    if (!displayLeaveRequest.affected_classes || !Array.isArray(displayLeaveRequest.affected_classes)) {
      return { assigned: 0, total: 0, percentage: 0 };
    }

    const total = displayLeaveRequest.affected_classes.length;
    const assigned = displayLeaveRequest.affected_classes.filter(cls => cls && cls.substitute_faculty_name).length;
    const percentage = total > 0 ? Math.round((assigned / total) * 100) : 0;

    return { assigned, total, percentage };
  };

  // Get class type icon
  const getClassTypeIcon = (type: string) => {
    const typeStr = type?.toLowerCase() || '';
    switch (typeStr) {
      case 'theory': return <GraduationCap className="h-4 w-4" />;
      case 'lab': return <FlaskConical className="h-4 w-4" />;
      case 'tutorial': return <Monitor className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  // Get substitution status - simplified version
  const getSubstitutionStatus = (affectedClass: AffectedClass) => {
    if (affectedClass.substitute_faculty_name) {
      return {
        color: 'text-green-600',
        bgColor: 'bg-green-100 border-green-200',
        icon: <CheckCircle className="h-4 w-4" />,
        text: 'Substitute Assigned',
        status: 'assigned'
      };
    } else {
      return {
        color: 'text-red-600',
        bgColor: 'bg-red-100 border-red-200',
        icon: <AlertCircle className="h-4 w-4" />,
        text: 'Substitute Required',
        status: 'unassigned'
      };
    }
  };

  // Group classes by type for summary
  const getClassSummary = () => {
    if (!displayLeaveRequest.affected_classes || !Array.isArray(displayLeaveRequest.affected_classes)) {
      return { theory: 0, lab: 0, tutorial: 0 };
    }

    return displayLeaveRequest.affected_classes.reduce((acc, cls) => {
      if (!cls) return acc; // Skip null/undefined classes
      const type = cls.subject_type?.toLowerCase() || '';
      if (type === 'theory') acc.theory++;
      else if (type === 'lab') acc.lab++;
      else if (type === 'tutorial') acc.tutorial++;
      return acc;
    }, { theory: 0, lab: 0, tutorial: 0 });
  };

  const handleApprove = async () => {
    try {
      await onApprove();
      onClose();
    } catch (error) {
      console.error('Error approving leave:', error);
    }
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      return;
    }
    
    try {
      await onReject(rejectionReason);
      setRejectionReason('');
      setShowRejectionForm(false);
      onClose();
    } catch (error) {
      console.error('Error rejecting leave:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSubjectTypeColor = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'theory':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'lab':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'tutorial':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSubstitutionStatusColor = (status: string) => {
    switch (status) {
      case 'assigned':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Loading overlay while enriching data */}
        {enriching && (
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-50 rounded-lg">
            <div className="flex items-center gap-2 text-blue-600">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Enriching class data...</span>
            </div>
          </div>
        )}
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Leave Request Review
          </DialogTitle>
          <DialogDescription className="space-y-1">
            <div>Review the leave request details and class substitution arrangements</div>
            <div className="text-xs font-mono text-gray-500 bg-gray-100 px-2 py-1 rounded">
              Request ID: {displayLeaveRequest.id}
              {enriching && <span className="ml-2 text-blue-600">• Enriching data...</span>}
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Faculty and Leave Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Leave Request Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Faculty Name</Label>
                  <div className="text-lg font-semibold">{displayLeaveRequest.employee_details?.full_name}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Department</Label>
                  <div>{displayLeaveRequest.employee_details?.department}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Leave Type</Label>
                  <Badge variant="outline">{displayLeaveRequest.leave_type}</Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Duration</Label>
                  <div className="font-semibold">{displayLeaveRequest.total_days} days</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Start Date</Label>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(displayLeaveRequest.start_date)}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">End Date</Label>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(displayLeaveRequest.end_date)}
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-muted-foreground">Reason</Label>
                <div className="mt-1 p-3 bg-muted rounded-md">
                  {displayLeaveRequest.reason}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-muted-foreground">Applied Date</Label>
                <div>{formatDate(displayLeaveRequest.applied_date || displayLeaveRequest.created_at)}</div>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Class Impact Analysis */}
          {displayLeaveRequest.total_classes_affected && displayLeaveRequest.total_classes_affected > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Detailed Class Impact & Substitution Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Enhanced Impact Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-orange-200 bg-orange-50">
                    <CardContent className="pt-4">
                      <div className="flex items-center gap-2">
                        <Target className="h-5 w-5 text-orange-600" />
                        <div>
                          <div className="text-2xl font-bold text-orange-600">
                            {displayLeaveRequest.total_classes_affected}
                          </div>
                          <div className="text-sm text-orange-700">Classes Affected</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className={`border-2 ${getSubstitutionStats().percentage === 100 ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <CardContent className="pt-4">
                      <div className="flex items-center gap-2">
                        <Shield className="h-5 w-5" />
                        <div>
                          <div className="text-2xl font-bold">
                            {getSubstitutionStats().assigned}/{getSubstitutionStats().total}
                          </div>
                          <div className="text-sm">Substitutes Assigned</div>
                        </div>
                      </div>
                      <Progress
                        value={getSubstitutionStats().percentage}
                        className="mt-2 h-2"
                      />
                      <div className="text-xs mt-1 text-center">
                        {getSubstitutionStats().percentage}% Coverage
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="pt-4">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5 text-blue-600" />
                        <div>
                          <div className="text-sm text-blue-700 font-medium">
                            {getSubstitutionStats().percentage === 100 ? 'Ready for Approval' :
                             getSubstitutionStats().percentage >= 80 ? 'Mostly Covered' :
                             getSubstitutionStats().percentage >= 50 ? 'Partially Covered' : 'Needs Attention'}
                          </div>
                          <div className="text-xs text-blue-600">
                            Substitution Status
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Class Type Breakdown */}
                <div className="grid grid-cols-3 gap-4">
                  {(() => {
                    const summary = getClassSummary();
                    return (
                      <>
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <GraduationCap className="h-4 w-4 text-blue-600" />
                            <span className="text-sm font-medium">Theory</span>
                          </div>
                          <div className="text-xl font-bold text-blue-600">{summary.theory}</div>
                        </div>
                        <div className="text-center p-3 bg-purple-50 rounded-lg">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <FlaskConical className="h-4 w-4 text-purple-600" />
                            <span className="text-sm font-medium">Lab</span>
                          </div>
                          <div className="text-xl font-bold text-purple-600">{summary.lab}</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <Monitor className="h-4 w-4 text-green-600" />
                            <span className="text-sm font-medium">Tutorial</span>
                          </div>
                          <div className="text-xl font-bold text-green-600">{summary.tutorial}</div>
                        </div>
                      </>
                    );
                  })()}
                </div>

                {/* Substitution Status */}
                <div className="flex items-center gap-2">
                  <Label>Substitution Status:</Label>
                  <Badge
                    variant="outline"
                    className={getSubstitutionStatusColor(displayLeaveRequest.substitution_status || 'pending')}
                  >
                    {displayLeaveRequest.substitution_status || 'pending'}
                  </Badge>
                </div>

                <Separator />

                {/* Enhanced Affected Classes List */}
                {displayLeaveRequest.affected_classes && Array.isArray(displayLeaveRequest.affected_classes) && displayLeaveRequest.affected_classes.length > 0 && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-lg">Detailed Class Schedule & Substitution Plan</h4>
                      <Badge variant="outline" className="text-sm">
                        {displayLeaveRequest.affected_classes.length} Classes
                      </Badge>
                    </div>

                    {displayLeaveRequest.affected_classes.filter(cls => cls != null).map((affectedClass: AffectedClass, index: number) => {
                      const substitutionStatus = getSubstitutionStatus(affectedClass);

                      return (
                        <Card
                          key={affectedClass.id || index}
                          className={`border-l-4 transition-all duration-200 ${
                            substitutionStatus.status === 'assigned'
                              ? 'border-l-green-500 hover:shadow-md'
                              : 'border-l-red-500 hover:shadow-md'
                          }`}
                        >
                          <CardContent className="pt-4">
                            {/* Class Header with Toggle */}
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-3">
                                {getClassTypeIcon(affectedClass.subject_type)}
                                <div className="flex-1">
                                  <div className="font-semibold text-lg">
                                    {/* Show real subject information instead of placeholders */}
                                    {affectedClass.subject_code && affectedClass.subject_code !== 'COURSE-TBD'
                                      ? `${affectedClass.subject_code} - ${affectedClass.subject_name}`
                                      : 'Subject Information Pending'
                                    }
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {/* Show real schedule information instead of placeholders */}
                                    {affectedClass.day && affectedClass.day !== 'Schedule TBD'
                                      ? `${affectedClass.day} • ${affectedClass.time_slot} • Semester ${affectedClass.semester}-${affectedClass.section}`
                                      : 'Schedule Information Pending'
                                    }
                                  </div>
                                  {affectedClass.room_number && affectedClass.room_number !== 'Room TBD' && (
                                    <div className="text-xs text-muted-foreground mt-1">
                                      📍 {affectedClass.room_number}
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <Badge
                                  variant="outline"
                                  className={getSubjectTypeColor(affectedClass.subject_type || 'theory')}
                                >
                                  {affectedClass.subject_type || 'Theory'}
                                </Badge>
                                <Badge
                                  variant="outline"
                                  className={substitutionStatus.bgColor}
                                >
                                  <div className="flex items-center gap-1">
                                    {substitutionStatus.icon}
                                    {substitutionStatus.text}
                                  </div>
                                </Badge>

                              </div>
                            </div>



                            {/* Substitute Assignment Status */}
                            <div className={`p-3 rounded-lg border ${substitutionStatus.bgColor}`}>
                              {affectedClass.substitute_faculty_name ? (
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2">
                                    <UserCheck className="h-5 w-5 text-green-600" />
                                    <span className="font-medium text-green-800">
                                      Substitute Assigned: {affectedClass.substitute_faculty_name}
                                    </span>
                                  </div>
                                  <div className="text-sm text-green-700">
                                    Will handle: {affectedClass.substitute_subject_info?.primary_subject
                                      ? `${affectedClass.substitute_subject_info.primary_subject.subject_code} - ${affectedClass.substitute_subject_info.primary_subject.subject_name}`
                                      : 'Subject assignment pending'
                                    }
                                  </div>
                                  {affectedClass.substitution_notes && (
                                    <div className="mt-2 text-sm text-green-700">
                                      Notes: {affectedClass.substitution_notes}
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5 text-red-600" />
                                    <span className="font-medium text-red-800">
                                      Substitute Required
                                    </span>
                                  </div>
                                  <div className="text-sm text-red-700">
                                    Need substitute for: {affectedClass.subject_code && affectedClass.subject_code !== 'COURSE-TBD'
                                      ? `${affectedClass.subject_code} - ${affectedClass.subject_name}`
                                      : 'Subject assignment pending'
                                    }
                                  </div>
                                  <div className="text-sm text-red-700">
                                    Schedule: {affectedClass.day && affectedClass.day !== 'Schedule TBD'
                                      ? `${affectedClass.day}, ${affectedClass.time_slot}`
                                      : 'Schedule information pending'
                                    }
                                  </div>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                )}

                {/* Decision Support Summary */}
                <Card className="border-blue-200 bg-blue-50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-blue-800">
                      <Target className="h-5 w-5" />
                      Decision Support Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Substitution Completeness */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-blue-700">Substitution Coverage</Label>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={getSubstitutionStats().percentage}
                            className="flex-1 h-3"
                          />
                          <span className="text-sm font-medium text-blue-700">
                            {getSubstitutionStats().percentage}%
                          </span>
                        </div>
                        <div className="text-xs text-blue-600">
                          {getSubstitutionStats().assigned} of {getSubstitutionStats().total} classes have substitute coverage
                        </div>
                      </div>

                      {/* Academic Impact */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-blue-700">Academic Impact</Label>
                        <div className="text-sm">
                          {displayLeaveRequest.total_classes_affected === 1 ? (
                            <span className="text-green-600">Low Impact - Single class affected</span>
                          ) : displayLeaveRequest.total_classes_affected <= 3 ? (
                            <span className="text-yellow-600">Moderate Impact - {displayLeaveRequest.total_classes_affected} classes affected</span>
                          ) : (
                            <span className="text-red-600">High Impact - {displayLeaveRequest.total_classes_affected} classes affected</span>
                          )}
                        </div>
                        <div className="text-xs text-blue-600">
                          Based on number of affected classes and duration
                        </div>
                      </div>
                    </div>

                    {/* Recommendations */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-blue-700">Recommendation</Label>
                      <div className="p-3 rounded-lg bg-white border border-blue-200">
                        {getSubstitutionStats().percentage === 100 ? (
                          <div className="flex items-center gap-2 text-green-700">
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm font-medium">Ready for Approval</span>
                          </div>
                        ) : getSubstitutionStats().percentage >= 80 ? (
                          <div className="flex items-center gap-2 text-yellow-700">
                            <AlertTriangle className="h-4 w-4" />
                            <span className="text-sm font-medium">Consider Approval with Conditions</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2 text-red-700">
                            <XCircle className="h-4 w-4" />
                            <span className="text-sm font-medium">Requires Complete Substitution Plan</span>
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground mt-1">
                          {getSubstitutionStats().percentage === 100
                            ? "All classes have substitute coverage. Academic continuity is ensured."
                            : getSubstitutionStats().percentage >= 80
                            ? "Most classes are covered. Consider approving with requirement to complete remaining assignments."
                            : "Significant gaps in substitute coverage. Request complete substitution plan before approval."
                          }
                        </div>
                      </div>
                    </div>

                    {/* Unassigned Classes Alert */}
                    {getSubstitutionStats().percentage < 100 && (
                      <Alert className="border-orange-200 bg-orange-50">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-orange-800">
                          <strong>{getSubstitutionStats().total - getSubstitutionStats().assigned} classes</strong> still need substitute assignments.
                          Consider requesting the faculty to complete substitution arrangements before final approval.
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          ) : (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                This leave request does not conflict with any scheduled classes.
              </AlertDescription>
            </Alert>
          )}

          {/* Rejection Form */}
          {showRejectionForm && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="text-red-600">Rejection Reason</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="rejection_reason">Please provide a reason for rejection</Label>
                  <Textarea
                    id="rejection_reason"
                    placeholder="Enter the reason for rejecting this leave request..."
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose} disabled={processing}>
              Close
            </Button>
          </div>
          
          <div className="flex gap-2">
            {showRejectionForm ? (
              <>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setShowRejectionForm(false);
                    setRejectionReason('');
                  }}
                  disabled={processing}
                >
                  Cancel
                </Button>
                <Button 
                  variant="destructive" 
                  onClick={handleReject}
                  disabled={processing || !rejectionReason.trim()}
                >
                  {processing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Rejecting...
                    </>
                  ) : (
                    'Confirm Rejection'
                  )}
                </Button>
              </>
            ) : (
              <>
                <Button 
                  variant="destructive" 
                  onClick={() => setShowRejectionForm(true)}
                  disabled={processing}
                >
                  Reject
                </Button>
                <Button 
                  onClick={handleApprove}
                  disabled={processing}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {processing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Approving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve Leave
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
