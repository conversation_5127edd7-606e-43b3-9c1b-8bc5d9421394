import { supabase } from '@/integrations/supabase/client';

export interface FacultyAssignment {
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: string;
  section: string;
  department: string;
  academic_year: string;
  time_slot?: string;
  day?: string;
  room?: string;
  period?: string;
}

export interface TimetableSlot {
  day: string;
  period: string;
  time_slot: string;
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: string;
  section: string;
  room?: string;
  is_break?: boolean;
  break_type?: string;
}

export interface FacultyAssignmentSummary {
  semesters: string[];
  sections: string[];
  subjects: { code: string; name: string; type: string }[];
  semesterSections: { semester: string; section: string }[];
  assignments: FacultyAssignment[];
  timetable: TimetableSlot[];
  weeklyTimetable: { [day: string]: { [period: string]: TimetableSlot | null } };
}

export class FacultyAssignmentService {
  /**
   * Get all current teaching assignments for a faculty member
   */
  static async getFacultyAssignments(
    facultyId: string,
    department: string
  ): Promise<FacultyAssignmentSummary> {
    try {
      console.log('🎯 Fetching faculty assignments for:', facultyId, department);

      // Get current academic year (you might want to make this dynamic)
      const currentYear = new Date().getFullYear();
      const academicYear = `${currentYear}-${currentYear + 1}`;

      const departmentVariants = [department, 'cse', 'CSE', department.toLowerCase()];
      let assignments: FacultyAssignment[] = [];
      let timetableSlots: TimetableSlot[] = [];

      // Try to get assignments from simplified_subject_faculty_mappings first
      for (const deptVariant of departmentVariants) {
        console.log(`🔍 Checking assignments for department variant: ${deptVariant}`);

        const { data: mappingData, error: mappingError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            department,
            academic_year,
            faculty_1_id,
            faculty_2_id
          `)
          .eq('department', deptVariant)
          .eq('academic_year', academicYear)
          .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`);

        if (!mappingError && mappingData && mappingData.length > 0) {
          console.log(`✅ Found ${mappingData.length} assignments in simplified mappings`);
          
          assignments = mappingData.map(mapping => ({
            subject_code: mapping.subject_code,
            subject_name: mapping.subject_name,
            subject_type: mapping.subject_type,
            semester: mapping.semester.toString(),
            section: mapping.section,
            department: mapping.department,
            academic_year: mapping.academic_year
          }));
          break;
        }
      }

      // PRIMARY APPROACH: Use timetable_slots as the main data source
      console.log('🔄 Fetching timetable data from timetable_slots for faculty_id:', facultyId);
      console.log('🔍 Query parameters:', { facultyId, department });

      // CRITICAL FIX: Query for both primary and secondary faculty assignments but with proper logic
      console.log(`🔍 DEBUGGING: Querying timetable_slots for faculty assignments with proper filtering`);
      const { data: timetableSlotsData, error: timetableSlotsError } = await supabase
        .from('timetable_slots')
        .select('*')
        .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`)
        .order('day')
        .order('time_slot');

      console.log(`🔍 DEBUGGING: Found ${timetableSlotsData?.length || 0} total faculty assignments`);

      // Filter the results to apply the same logic as the RPC function
      const filteredSlotsData = timetableSlotsData?.filter(slot => {
        // For theory subjects: only show if faculty is primary (faculty_id)
        if (slot.subject_type === 'theory' || slot.subject_type === 'elective') {
          return slot.faculty_id === facultyId;
        }
        // For lab subjects: show if faculty is either primary or secondary
        if (slot.subject_type === 'lab') {
          return slot.faculty_id === facultyId || slot.faculty2_id === facultyId;
        }
        return false;
      }) || [];

      console.log(`🔍 DEBUGGING: After filtering: ${filteredSlotsData.length} relevant assignments`);

      // Log sample data for debugging
      if (filteredSlotsData && filteredSlotsData.length > 0) {
        const academicYears = [...new Set(filteredSlotsData.map(slot => slot.academic_year))];
        console.log(`🔍 DEBUGGING: Academic years found in database:`, academicYears);
        console.log(`🔍 DEBUGGING: Expected academic year:`, academicYear);
        console.log(`🔍 DEBUGGING: Sample slots:`, filteredSlotsData.slice(0, 3).map(slot => ({
          subject_code: slot.subject_code,
          semester: slot.semester,
          section: slot.section,
          day: slot.day,
          time_slot: slot.time_slot,
          faculty_name: slot.faculty_name,
          subject_type: slot.subject_type,
          is_primary: slot.faculty_id === facultyId
        })));
      }

      console.log('📊 timetable_slots query result:', {
        error: timetableSlotsError,
        dataLength: timetableSlotsData?.length || 0,
        sampleData: timetableSlotsData?.[0]
      });

      if (timetableSlotsData && timetableSlotsData.length > 0) {
        console.log('✅ Found timetable data! Processing all slots...');
        console.log('📊 All timetable_slots data:', timetableSlotsData);

        // Process filtered timetable_slots data
        timetableSlots = filteredSlotsData.map((item, index) => {
          const period = this.extractPeriodFromTimeSlot(item.time_slot);

          const processedSlot = {
            day: item.day,
            period: period,
            time_slot: item.time_slot,
            subject_code: item.subject_code,
            subject_name: item.subject_name,
            subject_type: item.subject_type || 'theory',
            semester: item.semester?.toString() || 'N/A',
            section: item.section || 'N/A',
            room: item.room_number || '',
            is_break: false,
            break_type: undefined
          };

          // Enhanced debugging for first few slots
          if (index < 3) {
            console.log(`📊 DETAILED Processing slot ${index + 1}:`, {
              original_time_slot: item.time_slot,
              extracted_period: period,
              day: item.day,
              subject_code: item.subject_code,
              processed_slot: processedSlot
            });
          }

          return processedSlot;
        });

        // Create assignments from timetable_slots data
        const uniqueAssignments = new Map<string, FacultyAssignment>();

        filteredSlotsData.forEach(item => {
          const key = `${item.subject_code}-${item.semester}-${item.section}`;
          if (!uniqueAssignments.has(key)) {
            const period = this.extractPeriodFromTimeSlot(item.time_slot);
            uniqueAssignments.set(key, {
              subject_code: item.subject_code,
              subject_name: item.subject_name,
              subject_type: item.subject_type || 'theory',
              semester: item.semester?.toString() || 'N/A',
              section: item.section || 'N/A',
              department: item.department || department,
              academic_year: item.academic_year || academicYear,
              time_slot: item.time_slot,
              day: item.day,
              room: item.room_number,
              period: period
            });
          }
        });

        assignments = Array.from(uniqueAssignments.values());
        console.log('✅ Created', assignments.length, 'assignments from timetable_slots data');

      } else {
        console.log('❌ No data found in timetable_slots for faculty_id:', facultyId);
        console.log('🔍 This faculty may not have any timetable entries or faculty_id might be incorrect');

        // Try to find any faculty record to debug
        const { data: anyFacultyData, error: anyFacultyError } = await supabase
          .from('timetable_slots')
          .select('faculty_id, faculty_name')
          .limit(5);

        console.log('📊 Sample faculty IDs in timetable_slots:', anyFacultyData);
      }

      // If still no assignments, create empty summary
      if (assignments.length === 0) {
        console.log('⚠️ No assignments found, creating empty summary');
        return {
          semesters: [],
          sections: [],
          subjects: [],
          semesterSections: [],
          assignments: [],
          timetable: [],
          weeklyTimetable: {}
        };
      }

      // Generate weekly timetable grid
      console.log('📅 Generating weekly timetable grid from', timetableSlots.length, 'slots');
      const weeklyTimetable = this.generateWeeklyTimetableGrid(timetableSlots);
      console.log('📅 Generated weekly timetable with', Object.keys(weeklyTimetable).length, 'days');

      // Debug: Check if any actual classes were added to the grid
      let totalClassSlots = 0;
      Object.keys(weeklyTimetable).forEach(day => {
        Object.keys(weeklyTimetable[day]).forEach(period => {
          const slot = weeklyTimetable[day][period];
          if (slot && !slot.is_break) {
            totalClassSlots++;
            if (totalClassSlots <= 3) {
              console.log(`📅 Sample class slot: ${day} - Period ${period} - ${slot.subject_code}`);
            }
          }
        });
      });
      console.log(`📅 Total class slots in grid: ${totalClassSlots}`);

      // Process assignments to create summary
      const semesters = [...new Set(assignments.map(a => a.semester))].sort();
      const sections = [...new Set(assignments.map(a => a.section))].sort();
      const subjects = [...new Set(assignments.map(a => a.subject_code))]
        .map(code => {
          const assignment = assignments.find(a => a.subject_code === code)!;
          return {
            code: assignment.subject_code,
            name: assignment.subject_name,
            type: assignment.subject_type
          };
        });

      const semesterSections = [...new Set(assignments.map(a => `${a.semester}-${a.section}`))]
        .map(combo => {
          const [semester, section] = combo.split('-');
          return { semester, section };
        })
        .sort((a, b) => {
          if (a.semester !== b.semester) {
            return parseInt(a.semester) - parseInt(b.semester);
          }
          return a.section.localeCompare(b.section);
        });

      console.log('📊 Faculty assignment summary:', {
        semesters: semesters.length,
        sections: sections.length,
        subjects: subjects.length,
        semesterSections: semesterSections.length
      });

      return {
        semesters,
        sections,
        subjects,
        semesterSections,
        assignments,
        timetable: timetableSlots,
        weeklyTimetable
      };

    } catch (error) {
      console.error('❌ Error fetching faculty assignments:', error);
      return {
        semesters: [],
        sections: [],
        subjects: [],
        semesterSections: [],
        assignments: [],
        timetable: [],
        weeklyTimetable: {}
      };
    }
  }

  /**
   * Generate weekly timetable grid from timetable slots
   */
  private static generateWeeklyTimetableGrid(
    timetableSlots: TimetableSlot[]
  ): { [day: string]: { [period: string]: TimetableSlot | null } } {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const periods = ['1', '2', '3', '4', '5', '6', '7'];

    console.log('🏗️ Building timetable grid...');
    console.log('🏗️ Days:', days);
    console.log('🏗️ Periods:', periods);
    console.log('🏗️ Input slots:', timetableSlots.length);

    // Initialize empty grid
    const grid: { [day: string]: { [period: string]: TimetableSlot | null } } = {};
    days.forEach(day => {
      grid[day] = {};
      periods.forEach(period => {
        grid[day][period] = null;
      });
    });

    // Add break slots to the grid
    console.log('🍵 Adding break slots...');
    days.forEach(day => {
      // Tea break between periods 2-3
      grid[day]['tea_break'] = {
        day,
        period: 'tea_break',
        time_slot: '10:20-10:40',
        subject_code: '',
        subject_name: 'Tea Break',
        subject_type: '',
        semester: '',
        section: '',
        is_break: true,
        break_type: 'tea'
      };

      // Lunch break between periods 4-5
      grid[day]['lunch_break'] = {
        day,
        period: 'lunch_break',
        time_slot: '12:30-13:15',
        subject_code: '',
        subject_name: 'Lunch Break',
        subject_type: '',
        semester: '',
        section: '',
        is_break: true,
        break_type: 'lunch'
      };
    });
    console.log('🍵 Break slots added for all days');

    // Fill in actual timetable slots
    console.log('📅 Processing', timetableSlots.length, 'timetable slots');
    console.log('📅 Available days in grid:', Object.keys(grid));
    console.log('📅 Available periods:', periods);

    let slotsAdded = 0;
    let slotsSkipped = 0;

    timetableSlots.forEach((slot, index) => {
      console.log(`📅 Processing slot ${index + 1}:`, {
        day: slot.day,
        period: slot.period,
        time_slot: slot.time_slot,
        subject_code: slot.subject_code,
        subject_name: slot.subject_name
      });

      // Normalize day name (handle case variations)
      const normalizedDay = slot.day?.charAt(0).toUpperCase() + slot.day?.slice(1).toLowerCase();

      // Check if day exists in grid
      if (!grid[normalizedDay]) {
        console.log(`❌ Day "${slot.day}" (normalized: "${normalizedDay}") not found in grid. Available days:`, Object.keys(grid));
        slotsSkipped++;
        return;
      }

      // Check if period is valid
      if (!periods.includes(slot.period)) {
        console.log(`❌ Period "${slot.period}" not in valid periods:`, periods);
        console.log(`⚠️ Time slot was: "${slot.time_slot}"`);
        console.log(`🔍 DEBUGGING: Time slot mapping result for "${slot.time_slot}":`, this.extractPeriodFromTimeSlot(slot.time_slot));
        slotsSkipped++;
        return;
      }

      // Add slot to grid using normalized day
      grid[normalizedDay][slot.period] = slot;
      slotsAdded++;
      console.log(`✅ Added slot to grid: ${normalizedDay} - Period ${slot.period} - ${slot.subject_code}`);
    });

    console.log(`📊 Grid population summary: ${slotsAdded} slots added, ${slotsSkipped} slots skipped`);

    return grid;
  }

  /**
   * Get specific assignment details for a faculty member
   */
  static async getFacultyAssignmentDetails(
    facultyId: string,
    department: string,
    semester?: string,
    section?: string,
    subjectCode?: string
  ): Promise<FacultyAssignment[]> {
    try {
      const summary = await this.getFacultyAssignments(facultyId, department);
      let filteredAssignments = summary.assignments;

      if (semester) {
        filteredAssignments = filteredAssignments.filter(a => a.semester === semester);
      }

      if (section) {
        filteredAssignments = filteredAssignments.filter(a => a.section === section);
      }

      if (subjectCode) {
        filteredAssignments = filteredAssignments.filter(a => a.subject_code === subjectCode);
      }

      return filteredAssignments;
    } catch (error) {
      console.error('Error fetching faculty assignment details:', error);
      return [];
    }
  }

  /**
   * Check if faculty teaches a specific combination
   */
  static async facultyTeaches(
    facultyId: string,
    department: string,
    semester: string,
    section: string,
    subjectCode?: string
  ): Promise<boolean> {
    try {
      const assignments = await this.getFacultyAssignmentDetails(
        facultyId,
        department,
        semester,
        section,
        subjectCode
      );
      return assignments.length > 0;
    } catch (error) {
      console.error('Error checking faculty teaching assignment:', error);
      return false;
    }
  }

  /**
   * Extract period number from time slot
   */
  private static extractPeriodFromTimeSlot(timeSlot: string): string {
    if (!timeSlot) {
      console.log('⚠️ Empty time slot provided');
      return 'Unknown';
    }

    const timeSlotMap: { [key: string]: string } = {
      // Standard format (matching FacultyTimetableGrid)
      '08:30-09:25': '1',
      '09:25-10:20': '2',
      '10:35-11:30': '3',
      '11:30-12:25': '4',
      '13:15-14:10': '5',
      '14:10-15:05': '6',
      '15:05-16:00': '7',
      // Alternative formats without leading zeros
      '8:30-9:25': '1',
      '9:25-10:20': '2',
      '10:35-11:30': '3',
      '11:30-12:25': '4',
      '13:15-14:10': '5',
      '14:10-15:05': '6',
      '15:05-16:00': '7',
      // Alternative formats with different separators
      '08:30 - 09:25': '1',
      '09:25 - 10:20': '2',
      '10:35 - 11:30': '3',
      '11:30 - 12:25': '4',
      '13:15 - 14:10': '5',
      '14:10 - 15:05': '6',
      '15:05 - 16:00': '7',
      // Alternative formats with AM/PM
      '8:30AM-9:25AM': '1',
      '9:25AM-10:20AM': '2',
      '10:35AM-11:30AM': '3',
      '11:30AM-12:25PM': '4',
      '1:15PM-2:10PM': '5',
      '2:10PM-3:05PM': '6',
      '3:05PM-4:00PM': '7'
    };

    const period = timeSlotMap[timeSlot];
    if (!period) {
      console.log(`⚠️ Unknown time slot format: "${timeSlot}"`);
      console.log(`📋 Available time slot formats:`, Object.keys(timeSlotMap).slice(0, 10), '...');

      // Try to extract period from time slot using pattern matching
      const timePattern = /(\d{1,2}):(\d{2})/;
      const match = timeSlot.match(timePattern);
      if (match) {
        const hour = parseInt(match[1]);
        console.log(`🕐 Extracted hour: ${hour}, attempting to map to period`);

        if (hour >= 8 && hour < 9) return '1';
        if (hour >= 9 && hour < 10) return '2';
        if (hour >= 10 && hour < 11) return '3';
        if (hour >= 11 && hour < 12) return '4';
        if (hour >= 13 && hour < 14) return '5';
        if (hour >= 14 && hour < 15) return '6';
        if (hour >= 15 && hour < 16) return '7';
      }
    }

    return period || 'Unknown';
  }
}
