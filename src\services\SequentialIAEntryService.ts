import { supabase } from '@/integrations/supabase/client';

// Types for Sequential IA Entry System
export interface IAPhase {
  phase: 'IA1' | 'IA2' | 'IA3' | 'ASSIGNMENT';
  name: string;
  maxMarks: number;
  description: string;
  isActive: boolean;
  isCompleted: boolean;
  isLocked: boolean;
}

export interface SequentialIAStudent {
  id: string;
  usn: string;
  student_name: string;
  current_marks?: number;
  is_completed: boolean;
}

export interface SequentialIASession {
  subject_code: string;
  subject_name: string;
  subject_type: 'theory' | 'laboratory' | 'elective';
  semester: string;
  section: string;
  academic_year: string;
  current_phase: IAPhase;
  all_phases: IAPhase[];
  students: SequentialIAStudent[];
  completion_stats: {
    total_students: number;
    completed_students: number;
    completion_percentage: number;
  };
}

export class SequentialIAEntryService {
  /**
   * Normalize subject codes to ensure consistency with Student Progress display
   * Removes suffixes like _THEORY, _LAB, etc.
   */
  private static normalizeSubjectCode(subjectCode: string): string {
    if (!subjectCode) return subjectCode;

    // Remove common suffixes used in IA entry systems
    return subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
  }

  /**
   * Validate student ID exists in class_students table
   */
  private static async validateStudentId(studentId: string): Promise<{ isValid: boolean; student?: any; error?: string }> {
    try {
      console.log(`🔍 Validating student ID: ${studentId}`);

      // Try a more robust query approach
      const { data: students, error } = await supabase
        .from('class_students')
        .select('id, usn, student_name, semester, section, department')
        .eq('id', studentId);

      if (error) {
        console.error(`❌ Database error validating student ${studentId}:`, error);
        return {
          isValid: false,
          error: `Database error validating student ID: ${error.message}`
        };
      }

      if (!students || students.length === 0) {
        console.log(`❌ Student ID ${studentId} not found in class_students table`);
        return {
          isValid: false,
          error: `Student ID ${studentId} not found in class_students table`
        };
      }

      const student = students[0];
      console.log(`✅ Student validation passed: ${student.student_name} (${student.usn})`);

      return {
        isValid: true,
        student
      };
    } catch (error) {
      console.error(`❌ Exception validating student ${studentId}:`, error);
      return {
        isValid: false,
        error: `Error validating student ID: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate subject code exists in timetable for the given class
   */
  private static async validateSubjectCode(
    subjectCode: string,
    department: string,
    semester: string,
    section: string
  ): Promise<{ isValid: boolean; subject?: any; error?: string }> {
    try {
      const normalizedSubjectCode = this.normalizeSubjectCode(subjectCode);

      // Check if subject exists in timetable_slots
      const { data: subjects, error } = await supabase
        .from('timetable_slots')
        .select('subject_code, subject_name')
        .eq('department', department.toLowerCase())
        .eq('semester', semester)
        .eq('section', section);

      if (error) {
        return {
          isValid: false,
          error: `Error validating subject code: ${error.message}`
        };
      }

      if (!subjects || subjects.length === 0) {
        return {
          isValid: false,
          error: `No subjects found for department: ${department}, semester: ${semester}, section: ${section}`
        };
      }

      // Check if the normalized subject code exists
      const subject = subjects.find(s => s.subject_code === normalizedSubjectCode);

      if (!subject) {
        const availableSubjects = subjects.map(s => s.subject_code).join(', ');
        return {
          isValid: false,
          error: `Subject code ${normalizedSubjectCode} not found in timetable. Available subjects: ${availableSubjects}`
        };
      }

      return {
        isValid: true,
        subject
      };
    } catch (error) {
      return {
        isValid: false,
        error: `Error validating subject code: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate academic year format
   */
  private static validateAcademicYear(academicYear: string): { isValid: boolean; error?: string } {
    // Expected format: "2025-2026"
    const academicYearPattern = /^\d{4}-\d{4}$/;

    if (!academicYearPattern.test(academicYear)) {
      return {
        isValid: false,
        error: `Invalid academic year format: ${academicYear}. Expected format: YYYY-YYYY (e.g., 2025-2026)`
      };
    }

    const [startYear, endYear] = academicYear.split('-').map(Number);

    if (endYear !== startYear + 1) {
      return {
        isValid: false,
        error: `Invalid academic year: ${academicYear}. End year should be start year + 1`
      };
    }

    return { isValid: true };
  }

  /**
   * Validate semester/section combination for a student
   */
  private static async validateStudentClassMatch(
    studentId: string,
    semester: string,
    section: string
  ): Promise<{ isValid: boolean; error?: string }> {
    try {
      const { data: student, error } = await supabase
        .from('class_students')
        .select('semester, section, student_name, usn')
        .eq('id', studentId)
        .single();

      if (error || !student) {
        return {
          isValid: false,
          error: `Student not found for validation`
        };
      }

      if (student.semester !== semester || student.section !== section) {
        return {
          isValid: false,
          error: `Student ${student.student_name} (${student.usn}) belongs to semester ${student.semester}, section ${student.section}, but marks are being saved for semester ${semester}, section ${section}`
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: `Error validating student class match: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Comprehensive validation before saving marks
   */
  private static async validateMarksData(
    marksData: Array<{ student_id: string; marks: number }>,
    subjectCode: string,
    semester: string,
    section: string,
    department: string,
    academicYear: string
  ): Promise<{ isValid: boolean; errors: string[]; validatedData?: any }> {
    const errors: string[] = [];
    const validatedStudents: any[] = [];

    // Validate academic year format
    const academicYearValidation = this.validateAcademicYear(academicYear);
    if (!academicYearValidation.isValid) {
      errors.push(academicYearValidation.error!);
    }

    // Validate subject code with fallback
    const subjectValidation = await this.validateSubjectCode(subjectCode, department, semester, section);
    if (!subjectValidation.isValid) {
      // Check if this is a database connectivity issue or just missing timetable data
      if (subjectValidation.error?.includes('No subjects found')) {
        console.warn(`⚠️ Subject validation warning: ${subjectValidation.error}`);
        console.warn('⚠️ Proceeding with save despite missing timetable data...');
        // Don't add to errors, just warn - this allows saving even if timetable isn't configured
      } else {
        errors.push(subjectValidation.error!);
      }
    }

    // Validate each student with fallback approach
    let studentValidationFailed = false;

    for (const markData of marksData) {
      // Validate student ID exists
      const studentValidation = await this.validateStudentId(markData.student_id);
      if (!studentValidation.isValid) {
        // Check if this is a database connectivity issue OR student not found
        if (studentValidation.error?.includes('Database error') ||
            studentValidation.error?.includes('406') ||
            studentValidation.error?.includes('not found in class_students table')) {

          console.warn(`⚠️ Student validation skipped: ${studentValidation.error}`);
          console.warn(`⚠️ Creating placeholder for student ${markData.student_id} and proceeding...`);
          studentValidationFailed = true;

          // Create a placeholder student object for processing
          const placeholderStudent = {
            id: markData.student_id,
            usn: `Student-${markData.student_id.slice(-8)}`, // Use last 8 chars of ID as USN
            student_name: `Student ${markData.student_id.slice(-8)}`,
            semester: semester,
            section: section,
            department: department
          };

          // Validate marks range
          if (markData.marks < 0 || markData.marks > 25) {
            errors.push(`Invalid marks ${markData.marks} for student ${markData.student_id}. Marks should be between 0 and 25.`);
            continue;
          }

          validatedStudents.push({
            ...markData,
            student: placeholderStudent
          });
          continue;
        } else {
          // Only add to errors if it's a truly critical validation issue
          console.warn(`⚠️ Student validation issue (treating as warning): ${studentValidation.error}`);

          // Create placeholder and continue instead of failing
          const placeholderStudent = {
            id: markData.student_id,
            usn: `Student-${markData.student_id.slice(-8)}`,
            student_name: `Student ${markData.student_id.slice(-8)}`,
            semester: semester,
            section: section,
            department: department
          };

          // Validate marks range
          if (markData.marks < 0 || markData.marks > 25) {
            errors.push(`Invalid marks ${markData.marks} for student ${markData.student_id}. Marks should be between 0 and 25.`);
            continue;
          }

          validatedStudents.push({
            ...markData,
            student: placeholderStudent
          });
          continue;
        }
      }

      // Validate student belongs to the correct class (skip if student validation failed)
      if (!studentValidationFailed) {
        const classMatchValidation = await this.validateStudentClassMatch(
          markData.student_id,
          semester,
          section
        );
        if (!classMatchValidation.isValid) {
          errors.push(`Class mismatch: ${classMatchValidation.error}`);
          continue;
        }
      }

      // Validate marks range
      if (markData.marks < 0 || markData.marks > 25) {
        errors.push(`Invalid marks ${markData.marks} for student ${studentValidation.student?.student_name || markData.student_id}. Marks should be between 0 and 25.`);
        continue;
      }

      validatedStudents.push({
        ...markData,
        student: studentValidation.student
      });
    }

    // Add warning if student validation was skipped
    if (studentValidationFailed) {
      console.warn('⚠️ Student validation was skipped due to missing student records. Proceeding with save using placeholder data...');
    }

    // If we have validated students but some errors, treat as warnings if students were processed
    const hasValidatedStudents = validatedStudents.length > 0;
    const shouldProceed = hasValidatedStudents || errors.length === 0;

    if (!shouldProceed && errors.length > 0) {
      console.error('❌ Critical validation errors prevent saving:', errors);
    } else if (errors.length > 0 && hasValidatedStudents) {
      console.warn('⚠️ Some validation warnings, but proceeding with save:', errors);
    }

    return {
      isValid: shouldProceed, // Allow save if we have validated students, even with some errors
      errors,
      validatedData: {
        students: validatedStudents,
        normalizedSubjectCode: this.normalizeSubjectCode(subjectCode),
        subject: subjectValidation.subject
      }
    };
  }
  /**
   * Map department names for database queries
   */
  private static mapDepartmentName(userDepartment: string): string {
    const departmentMap: Record<string, string> = {
      'Computer Science and Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Electrical and Electronics Engineering': 'eee'
    };

    return departmentMap[userDepartment] || userDepartment.toLowerCase();
  }

  /**
   * Get available IA phases for a subject
   */
  static getIAPhases(subjectType: string): IAPhase[] {
    const phases: IAPhase[] = [
      {
        phase: 'IA1',
        name: 'Internal Assessment 1',
        maxMarks: 25,
        description: 'First Internal Assessment (25 marks)',
        isActive: false,
        isCompleted: false,
        isLocked: false
      },
      {
        phase: 'IA2',
        name: 'Internal Assessment 2',
        maxMarks: 25,
        description: 'Second Internal Assessment (25 marks)',
        isActive: false,
        isCompleted: false,
        isLocked: true
      },
      {
        phase: 'IA3',
        name: 'Internal Assessment 3',
        maxMarks: 25,
        description: 'Third Internal Assessment (25 marks)',
        isActive: false,
        isCompleted: false,
        isLocked: true
      },
      {
        phase: 'ASSIGNMENT',
        name: 'Assignment',
        maxMarks: 10,
        description: 'Assignment marks (10 marks)',
        isActive: false,
        isCompleted: false,
        isLocked: false
      }
    ];

    return phases;
  }

  /**
   * Get current IA phase status for a subject
   */
  static async getCurrentPhaseStatus(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<{ currentPhase: string; completedPhases: string[] }> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      // Check existing IA records to determine current phase
      const { data: existingRecords, error } = await supabase
        .from('internal_assessments')
        .select('ia1_marks, ia2_marks, ia3_marks, assignment_marks')
        .eq('faculty_id', facultyId)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('academic_year', academicYear);

      if (error) {
        console.warn('Error checking existing records:', error);
        return { currentPhase: 'IA1', completedPhases: [] };
      }

      const completedPhases: string[] = [];
      let currentPhase = 'IA1';

      if (existingRecords && existingRecords.length > 0) {
        // Check which phases have been completed
        const hasIA1 = existingRecords.some(record => record.ia1_marks !== null && record.ia1_marks !== undefined);
        const hasIA2 = existingRecords.some(record => record.ia2_marks !== null && record.ia2_marks !== undefined);
        const hasIA3 = existingRecords.some(record => record.ia3_marks !== null && record.ia3_marks !== undefined);
        const hasAssignment = existingRecords.some(record => record.assignment_marks !== null && record.assignment_marks !== undefined);

        if (hasIA1) completedPhases.push('IA1');
        if (hasIA2) completedPhases.push('IA2');
        if (hasIA3) completedPhases.push('IA3');
        if (hasAssignment) completedPhases.push('ASSIGNMENT');

        // Determine current phase based on completion
        if (!hasIA1) {
          currentPhase = 'IA1';
        } else if (!hasIA2) {
          currentPhase = 'IA2';
        } else if (!hasIA3) {
          currentPhase = 'IA3';
        } else if (!hasAssignment) {
          currentPhase = 'ASSIGNMENT';
        } else {
          currentPhase = 'COMPLETED';
        }
      }

      console.log('📊 Phase status:', { currentPhase, completedPhases });
      return { currentPhase, completedPhases };
    } catch (error) {
      console.error('Error getting phase status:', error);
      return { currentPhase: 'IA1', completedPhases: [] };
    }
  }

  /**
   * Save marks for current IA phase only with comprehensive validation
   */
  static async saveCurrentPhaseMarks(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string,
    phase: string,
    marksData: Array<{ student_id: string; marks: number }>
  ): Promise<{ success: boolean; savedCount: number; errors: string[]; warnings: string[] }> {
    try {
      console.log('💾 Starting validated save for phase:', {
        phase,
        marksCount: marksData.length,
        subjectCode,
        semester,
        section,
        academicYear
      });

      // Step 1: Comprehensive validation
      console.log('🔍 Step 1: Validating marks data...');

      // Map department name to short code for validation
      const mappedDepartment = this.mapDepartmentName(userDepartment);
      console.log('📝 Department mapping:', { userDepartment, mappedDepartment });

      const validation = await this.validateMarksData(
        marksData,
        subjectCode,
        semester,
        section,
        mappedDepartment, // Use mapped department for validation
        academicYear
      );

      if (!validation.isValid) {
        console.error('❌ Validation failed:', validation.errors);
        return {
          success: false,
          savedCount: 0,
          errors: validation.errors,
          warnings: []
        };
      }

      console.log('✅ Validation passed for all students');

      // Step 2: Prepare validated data for database
      const normalizedSubjectCode = validation.validatedData.normalizedSubjectCode;
      // mappedDepartment already defined above, reuse it

      console.log('📝 Using normalized data:', {
        originalSubjectCode: subjectCode,
        normalizedSubjectCode,
        mappedDepartment
      });

      // Prepare update data based on phase
      const updates = validation.validatedData.students.map(({ student_id, marks, student }) => {
        const baseData = {
          student_id,
          subject_code: normalizedSubjectCode, // Use normalized subject code
          faculty_id: facultyId,
          department: mappedDepartment,
          semester: String(semester).substring(0, 10),
          section: String(section).substring(0, 10),
          academic_year: String(academicYear).substring(0, 20),
          last_modified_by: facultyId,
          created_by: facultyId,
          updated_at: new Date().toISOString()
        };

        // Add the specific phase marks
        switch (phase) {
          case 'IA1':
            return { ...baseData, ia1_marks: marks };
          case 'IA2':
            return { ...baseData, ia2_marks: marks };
          case 'IA3':
            return { ...baseData, ia3_marks: marks };
          case 'ASSIGNMENT':
            return { ...baseData, assignment_marks: marks };
          default:
            throw new Error(`Invalid phase: ${phase}`);
        }
      });

      console.log('📝 Prepared update data sample:', {
        count: updates.length,
        sample: updates[0],
        studentNames: validation.validatedData.students.slice(0, 3).map(s => s.student.student_name)
      });

      // Step 3: Save to database with error handling
      console.log('💾 Step 3: Saving to database...');
      const { data, error } = await supabase
        .from('internal_assessments')
        .upsert(updates, {
          onConflict: 'student_id,subject_code,academic_year'
        })
        .select('*');

      if (error) {
        console.error('❌ Database error saving phase marks:', error);
        throw new Error(`Database save failed: ${error.message}`);
      }

      // Step 4: Verify saved data
      console.log('✅ Step 4: Verifying saved data...');
      const savedCount = data?.length || 0;
      const warnings: string[] = [];

      if (savedCount !== marksData.length) {
        warnings.push(`Expected to save ${marksData.length} records, but only ${savedCount} were saved`);
      }

      // Step 5: Real-time verification - check if data appears in Student Progress
      console.log('🔍 Step 5: Real-time verification...');
      const verificationResults = await this.verifyMarksInStudentProgress(
        validation.validatedData.students.slice(0, 2), // Verify first 2 students
        normalizedSubjectCode,
        academicYear
      );

      if (verificationResults.warnings.length > 0) {
        warnings.push(...verificationResults.warnings);
      }

      console.log('✅ Phase marks saved successfully:', {
        phase,
        savedRecords: savedCount,
        normalizedSubjectCode,
        verificationStatus: verificationResults.verified ? 'PASSED' : 'PARTIAL'
      });

      return {
        success: true,
        savedCount,
        errors: [],
        warnings
      };

    } catch (error) {
      console.error('❌ Error in saveCurrentPhaseMarks:', error);
      return {
        success: false,
        savedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
        warnings: []
      };
    }
  }

  /**
   * Verify that saved marks appear correctly in Student Progress display
   */
  private static async verifyMarksInStudentProgress(
    students: Array<{ student_id: string; student: any }>,
    subjectCode: string,
    academicYear: string
  ): Promise<{ verified: boolean; warnings: string[] }> {
    const warnings: string[] = [];
    let verifiedCount = 0;

    try {
      for (const { student_id, student } of students) {
        // Check if the saved marks can be retrieved
        const { data: savedRecord, error } = await supabase
          .from('internal_assessments')
          .select('*')
          .eq('student_id', student_id)
          .eq('subject_code', subjectCode)
          .eq('academic_year', academicYear)
          .single();

        if (error || !savedRecord) {
          warnings.push(`Verification failed for student ${student.student_name} (${student.usn}): Record not found after save`);
        } else {
          verifiedCount++;
          console.log(`✅ Verified marks for ${student.student_name}: ${JSON.stringify(savedRecord)}`);
        }
      }

      return {
        verified: verifiedCount === students.length,
        warnings
      };
    } catch (error) {
      warnings.push(`Verification error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        verified: false,
        warnings
      };
    }
  }

  /**
   * Get students with current phase marks
   */
  static async getStudentsForPhase(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string,
    phase: string,
    students: any[]
  ): Promise<SequentialIAStudent[]> {
    try {
      // Get existing IA records
      const { data: existingRecords, error } = await supabase
        .from('internal_assessments')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('academic_year', academicYear);

      if (error) {
        console.warn('Error loading existing records:', error);
      }

      // Create a map for quick lookup
      const recordsMap = new Map();
      existingRecords?.forEach(record => {
        recordsMap.set(record.student_id, record);
      });

      // Transform students with current phase marks
      const studentsWithMarks: SequentialIAStudent[] = students.map(student => {
        const record = recordsMap.get(student.id);
        let currentMarks: number | undefined;
        let isCompleted = false;

        if (record) {
          switch (phase) {
            case 'IA1':
              currentMarks = record.ia1_marks;
              break;
            case 'IA2':
              currentMarks = record.ia2_marks;
              break;
            case 'IA3':
              currentMarks = record.ia3_marks;
              break;
            case 'ASSIGNMENT':
              currentMarks = record.assignment_marks;
              break;
          }
          isCompleted = currentMarks !== null && currentMarks !== undefined;
        }

        return {
          id: student.id,
          usn: student.usn,
          student_name: student.student_name,
          current_marks: currentMarks,
          is_completed: isCompleted
        };
      });

      return studentsWithMarks;
    } catch (error) {
      console.error('Error getting students for phase:', error);
      throw error;
    }
  }
}
