import { supabase } from '@/lib/supabase';

// Types for Sequential IA System
export interface IAPhaseStatus {
  id: string;
  subject_code: string;
  semester: string;
  section: string;
  department: string;
  academic_year: string;
  faculty_id: string;
  current_phase: 'IA1' | 'IA2' | 'IA3' | 'COMPLETED';
  ia1_completed: boolean;
  ia1_completed_at?: string;
  ia1_total_students: number;
  ia1_completed_students: number;
  ia2_completed: boolean;
  ia2_completed_at?: string;
  ia2_total_students: number;
  ia2_completed_students: number;
  ia3_completed: boolean;
  ia3_completed_at?: string;
  ia3_total_students: number;
  ia3_completed_students: number;
  created_at: string;
  updated_at: string;
}

export interface IndividualIARecord {
  id: string;
  student_id: string;
  subject_code: string;
  semester: string;
  section: string;
  department: string;
  academic_year: string;
  faculty_id: string;
  ia_phase: 'IA1' | 'IA2' | 'IA3' | 'ASSIGNMENT' | 'LAB_INTERNAL';
  marks: number;
  max_marks: number;
  entered_at: string;
  updated_at: string;
}

export interface SequentialIASession {
  phase_status: IAPhaseStatus;
  students: SequentialIAStudent[];
  subject_name: string;
  subject_type: 'theory' | 'laboratory' | 'elective';
  current_phase: 'IA1' | 'IA2' | 'IA3' | 'COMPLETED';
  available_phases: string[];
  phase_summary: {
    ia1: { completed: boolean; students_completed: number; total_students: number };
    ia2: { completed: boolean; students_completed: number; total_students: number };
    ia3: { completed: boolean; students_completed: number; total_students: number };
  };
}

export interface SequentialIAStudent {
  id: string;
  usn: string;
  student_name: string;
  // Current phase marks
  current_phase_marks?: number;
  assignment_marks?: number; // Available during IA1 phase
  lab_internal_marks?: number; // Available for lab subjects
  // Historical marks (read-only)
  ia1_marks?: number;
  ia2_marks?: number;
  ia3_marks?: number;
  // Status indicators
  current_phase_completed: boolean;
  assignment_completed: boolean;
  lab_internal_completed: boolean;
}

export class SequentialIAService {
  /**
   * Map department names for database queries
   */
  private static mapDepartmentName(userDepartment: string): string {
    const departmentMap: Record<string, string> = {
      'Computer Science and Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Electrical and Electronics Engineering': 'eee'
    };
    
    return departmentMap[userDepartment] || userDepartment.toLowerCase();
  }

  /**
   * Get or create IA phase status for a subject
   */
  static async getOrCreatePhaseStatus(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string,
    totalStudents: number
  ): Promise<IAPhaseStatus> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      // Try to get existing phase status
      const { data: existingStatus, error: fetchError } = await supabase
        .from('ia_phase_status')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('academic_year', academicYear)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      if (existingStatus) {
        console.log('✅ Found existing IA phase status:', existingStatus.current_phase);
        return existingStatus;
      }

      // Create new phase status
      console.log('🆕 Creating new IA phase status for IA1');
      const { data: newStatus, error: createError } = await supabase
        .from('ia_phase_status')
        .insert({
          faculty_id: facultyId,
          subject_code: subjectCode,
          semester,
          section,
          department: mappedDepartment,
          academic_year: academicYear,
          current_phase: 'IA1',
          ia1_total_students: totalStudents,
          ia2_total_students: totalStudents,
          ia3_total_students: totalStudents
        })
        .select()
        .single();

      if (createError) throw createError;

      console.log('✅ Created new IA phase status');
      return newStatus;
    } catch (error) {
      console.error('❌ Error getting/creating phase status:', error);
      throw error;
    }
  }

  /**
   * Get individual IA records for students
   */
  static async getIndividualIARecords(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string,
    studentIds: string[]
  ): Promise<IndividualIARecord[]> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      const { data: records, error } = await supabase
        .from('individual_ia_records')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('academic_year', academicYear)
        .in('student_id', studentIds);

      if (error) throw error;

      console.log(`📊 Found ${records?.length || 0} existing IA records`);
      return records || [];
    } catch (error) {
      console.error('❌ Error fetching individual IA records:', error);
      throw error;
    }
  }

  /**
   * Get maximum marks for each IA phase
   */
  static getMaxMarks(iaPhase: string, subjectType: string): number {
    switch (iaPhase) {
      case 'IA1':
      case 'IA2':
      case 'IA3':
        return 25;
      case 'ASSIGNMENT':
        return 10;
      case 'LAB_INTERNAL':
        return subjectType === 'laboratory' ? 20 : 0;
      default:
        return 0;
    }
  }

  /**
   * Get available phases for current status
   */
  static getAvailablePhases(phaseStatus: IAPhaseStatus, subjectType: string): string[] {
    const phases = [];

    // Current active IA phase
    if (phaseStatus.current_phase !== 'COMPLETED') {
      phases.push(phaseStatus.current_phase);
    }

    // Assignment marks available during IA1 phase
    if (phaseStatus.current_phase === 'IA1') {
      phases.push('ASSIGNMENT');
    }

    // Lab internal marks for laboratory subjects
    if (subjectType === 'laboratory') {
      phases.push('LAB_INTERNAL');
    }

    return phases;
  }

  /**
   * Save marks for current IA phase
   */
  static async saveCurrentPhaseMarks(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string,
    marksData: Array<{
      student_id: string;
      ia_phase: string;
      marks: number;
    }>
  ): Promise<void> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      console.log('💾 Saving marks for current phase:', {
        subjectCode,
        semester,
        section,
        marksCount: marksData.length
      });

      // Prepare records for upsert
      const records = marksData.map(mark => ({
        student_id: mark.student_id,
        subject_code: subjectCode,
        semester,
        section,
        department: mappedDepartment,
        academic_year: academicYear,
        faculty_id: facultyId,
        ia_phase: mark.ia_phase,
        marks: mark.marks,
        max_marks: this.getMaxMarks(mark.ia_phase, 'theory') // Will be updated based on subject type
      }));

      // Upsert individual IA records
      const { error: upsertError } = await supabase
        .from('individual_ia_records')
        .upsert(records, {
          onConflict: 'student_id,subject_code,semester,section,department,academic_year,faculty_id,ia_phase'
        });

      if (upsertError) throw upsertError;

      console.log('✅ Successfully saved IA marks');
    } catch (error) {
      console.error('❌ Error saving IA marks:', error);
      throw error;
    }
  }

  /**
   * Check if current phase is complete and advance to next phase
   */
  static async checkAndAdvancePhase(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string,
    totalStudents: number
  ): Promise<IAPhaseStatus> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      // Get current phase status
      const { data: phaseStatus, error: fetchError } = await supabase
        .from('ia_phase_status')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('academic_year', academicYear)
        .single();

      if (fetchError) throw fetchError;

      const currentPhase = phaseStatus.current_phase;

      // Count completed students for current phase
      const { data: completedRecords, error: countError } = await supabase
        .from('individual_ia_records')
        .select('student_id')
        .eq('faculty_id', facultyId)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('academic_year', academicYear)
        .eq('ia_phase', currentPhase);

      if (countError) throw countError;

      const completedCount = completedRecords?.length || 0;
      const isPhaseComplete = completedCount >= totalStudents;

      console.log(`📊 Phase ${currentPhase} completion: ${completedCount}/${totalStudents} students`);

      // Determine next phase
      let nextPhase = currentPhase;
      let updateData: any = {};

      if (isPhaseComplete) {
        switch (currentPhase) {
          case 'IA1':
            nextPhase = 'IA2';
            updateData = {
              current_phase: 'IA2',
              ia1_completed: true,
              ia1_completed_at: new Date().toISOString(),
              ia1_completed_students: completedCount
            };
            break;
          case 'IA2':
            nextPhase = 'IA3';
            updateData = {
              current_phase: 'IA3',
              ia2_completed: true,
              ia2_completed_at: new Date().toISOString(),
              ia2_completed_students: completedCount
            };
            break;
          case 'IA3':
            nextPhase = 'COMPLETED';
            updateData = {
              current_phase: 'COMPLETED',
              ia3_completed: true,
              ia3_completed_at: new Date().toISOString(),
              ia3_completed_students: completedCount
            };
            break;
        }

        if (nextPhase !== currentPhase) {
          console.log(`🎯 Advancing from ${currentPhase} to ${nextPhase}`);

          const { data: updatedStatus, error: updateError } = await supabase
            .from('ia_phase_status')
            .update(updateData)
            .eq('id', phaseStatus.id)
            .select()
            .single();

          if (updateError) throw updateError;

          return updatedStatus;
        }
      } else {
        // Update completion count without advancing phase
        const phaseField = `${currentPhase.toLowerCase()}_completed_students`;
        updateData[phaseField] = completedCount;

        const { data: updatedStatus, error: updateError } = await supabase
          .from('ia_phase_status')
          .update(updateData)
          .eq('id', phaseStatus.id)
          .select()
          .single();

        if (updateError) throw updateError;

        return updatedStatus;
      }

      return phaseStatus;
    } catch (error) {
      console.error('❌ Error checking/advancing phase:', error);
      throw error;
    }
  }

  /**
   * Get sequential IA session with phase-based workflow
   */
  static async getSequentialIASession(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string,
    students: any[],
    subjectName: string,
    subjectType: 'theory' | 'laboratory' | 'elective'
  ): Promise<SequentialIASession> {
    try {
      console.log('🎯 Creating sequential IA session:', {
        subjectCode,
        semester,
        section,
        studentsCount: students.length,
        subjectType
      });

      // Get or create phase status
      const phaseStatus = await this.getOrCreatePhaseStatus(
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        academicYear,
        students.length
      );

      // Get existing IA records
      const studentIds = students.map(s => s.id);
      const iaRecords = await this.getIndividualIARecords(
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        academicYear,
        studentIds
      );

      // Create IA records map for quick lookup
      const iaRecordsMap = new Map<string, Map<string, IndividualIARecord>>();
      iaRecords.forEach(record => {
        if (!iaRecordsMap.has(record.student_id)) {
          iaRecordsMap.set(record.student_id, new Map());
        }
        iaRecordsMap.get(record.student_id)!.set(record.ia_phase, record);
      });

      // Get available phases for current status
      const availablePhases = this.getAvailablePhases(phaseStatus, subjectType);

      // Transform students with phase-based marks
      const sequentialStudents: SequentialIAStudent[] = students.map(student => {
        const studentRecords = iaRecordsMap.get(student.id) || new Map();

        return {
          id: student.id,
          usn: student.usn,
          student_name: student.student_name,
          // Current phase marks (editable)
          current_phase_marks: studentRecords.get(phaseStatus.current_phase)?.marks,
          assignment_marks: studentRecords.get('ASSIGNMENT')?.marks,
          lab_internal_marks: studentRecords.get('LAB_INTERNAL')?.marks,
          // Historical marks (read-only)
          ia1_marks: studentRecords.get('IA1')?.marks,
          ia2_marks: studentRecords.get('IA2')?.marks,
          ia3_marks: studentRecords.get('IA3')?.marks,
          // Status indicators
          current_phase_completed: studentRecords.has(phaseStatus.current_phase),
          assignment_completed: studentRecords.has('ASSIGNMENT'),
          lab_internal_completed: studentRecords.has('LAB_INTERNAL')
        };
      });

      // Create phase summary
      const phaseSummary = {
        ia1: {
          completed: phaseStatus.ia1_completed,
          students_completed: phaseStatus.ia1_completed_students,
          total_students: phaseStatus.ia1_total_students
        },
        ia2: {
          completed: phaseStatus.ia2_completed,
          students_completed: phaseStatus.ia2_completed_students,
          total_students: phaseStatus.ia2_total_students
        },
        ia3: {
          completed: phaseStatus.ia3_completed,
          students_completed: phaseStatus.ia3_completed_students,
          total_students: phaseStatus.ia3_total_students
        }
      };

      console.log('✅ Sequential IA session created:', {
        currentPhase: phaseStatus.current_phase,
        availablePhases,
        studentsCount: sequentialStudents.length
      });

      return {
        phase_status: phaseStatus,
        students: sequentialStudents,
        subject_name: subjectName,
        subject_type: subjectType,
        current_phase: phaseStatus.current_phase,
        available_phases: availablePhases,
        phase_summary: phaseSummary
      };
    } catch (error) {
      console.error('❌ Error creating sequential IA session:', error);
      throw error;
    }
  }
}
