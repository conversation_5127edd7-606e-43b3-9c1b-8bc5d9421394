
import React from 'react';
import { Faculty } from '@/stores/SubjectMappingStore';
import { FacultyAutocomplete } from '@/components/subjects/FacultyAutocomplete';

interface FacultyEditCellProps {
  faculty: Faculty | undefined;
  faculty2Id?: string;
  faculty2Name?: string;
  isLabSubject: boolean;
  facultyList: Faculty[];
  onSave: (faculty: Faculty) => void;
  onSaveFaculty2?: (faculty2Id: string) => void;
}

const FacultyEditCell: React.FC<FacultyEditCellProps> = ({
  faculty,
  faculty2Id,
  faculty2Name,
  isLabSubject,
  facultyList = [], // Default to empty array if undefined
  onSave,
  onSaveFaculty2
}) => {
  // Ensure facultyList is always an array
  const safeFacultyList = Array.isArray(facultyList) ? facultyList : [];

  const handleSelect = (faculty: Faculty) => {
    if (faculty && faculty.id) {
      onSave(faculty);
    }
  };

  const handleSelectFaculty2 = (faculty: Faculty) => {
    if (faculty && faculty.id && onSaveFaculty2) {
      onSaveFaculty2(faculty.id);
    }
  };

  // Filter out the primary faculty from secondary faculty options
  const secondaryFacultyOptions = safeFacultyList.filter(
    f => !faculty || f.id !== faculty.id
  );

  // Find the faculty2 object based on faculty2Id
  const selectedFaculty2 = faculty2Id
    ? secondaryFacultyOptions.find(f => f.id === faculty2Id) || null
    : null;

  return (
    <div className="space-y-1">
      <div className="w-full">
        <div className="font-medium text-sm mb-1">
          {isLabSubject ? "Faculty 1:" : "Faculty:"}
        </div>
        <FacultyAutocomplete
          faculties={safeFacultyList}
          selectedFaculty={faculty}
          onSelect={handleSelect}
          placeholder="Select faculty"
          className="w-full"
        />
      </div>
      {isLabSubject && (
        <div className="mt-2">
          <div className="font-medium text-sm text-gray-700 mb-1">Faculty 2:</div>
          <FacultyAutocomplete
            faculties={secondaryFacultyOptions}
            selectedFaculty={selectedFaculty2}
            onSelect={handleSelectFaculty2}
            placeholder="Select secondary faculty"
            className="w-full"
          />
        </div>
      )}
    </div>
  );
};

export default FacultyEditCell;
