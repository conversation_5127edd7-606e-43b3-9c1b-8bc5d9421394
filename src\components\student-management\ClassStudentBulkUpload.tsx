import React, { useState, useRef } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Upload, Download, FileSpreadsheet, AlertCircle, CheckCircle, X, Users } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import * as XLSX from 'xlsx';

interface ClassStudentBulkUploadProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  facultyId?: string;
}

const ClassStudentBulkUpload: React.FC<ClassStudentBulkUploadProps> = ({
  isOpen,
  onClose,
  onSuccess,
  facultyId
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedSemester, setSelectedSemester] = useState<string>('');
  const [selectedSection, setSelectedSection] = useState<string>('');
  const [selectedAcademicYear, setSelectedAcademicYear] = useState<string>('2024-2025');
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResults, setUploadResults] = useState<{
    success: any[];
    errors: { row: number; error: string }[];
  } | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const departments = [
    { value: 'CSE', label: 'Computer Science and Engineering' },
    { value: 'ECE', label: 'Electronics and Communication Engineering' },
    { value: 'EEE', label: 'Electrical and Electronics Engineering' },
    { value: 'MECH', label: 'Mechanical Engineering' },
    { value: 'CIVIL', label: 'Civil Engineering' },
    { value: 'ISE', label: 'Information Science and Engineering' }
  ];

  const semesters = ['1', '2', '3', '4', '5', '6', '7', '8'];
  const sections = ['A', 'B', 'C', 'D'];
  const academicYears = ['2024-2025', '2023-2024', '2022-2023'];

  const parseExcelFile = (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          // Find header row with USN, NAME, EMAIL, STUDENT MOBILE, FATHER MOBILE, MOTHER MOBILE pattern
          let headerRowIndex = -1;
          let usnCol = -1, nameCol = -1, emailCol = -1, studentMobileCol = -1, fatherMobileCol = -1, motherMobileCol = -1;

          for (let i = 0; i < jsonData.length; i++) {
            const row = jsonData[i] as any[];
            if (!row || row.length === 0) continue;

            for (let j = 0; j < row.length; j++) {
              const cellValue = String(row[j] || '').toLowerCase().trim();

              if (cellValue.includes('usn') || cellValue.includes('roll')) {
                usnCol = j;
              } else if (cellValue.includes('name') && !cellValue.includes('father') && !cellValue.includes('mother') && !cellValue.includes('parent')) {
                nameCol = j;
              } else if (cellValue.includes('email') && !cellValue.includes('father') && !cellValue.includes('mother') && !cellValue.includes('parent')) {
                emailCol = j;
              } else if (cellValue.includes('student') && cellValue.includes('mobile')) {
                studentMobileCol = j;
              } else if (cellValue.includes('father') && cellValue.includes('mobile')) {
                fatherMobileCol = j;
              } else if (cellValue.includes('mother') && cellValue.includes('mobile')) {
                motherMobileCol = j;
              }
            }

            if (usnCol !== -1 && nameCol !== -1) {
              headerRowIndex = i;
              break;
            }

            usnCol = -1; nameCol = -1; emailCol = -1; studentMobileCol = -1; fatherMobileCol = -1; motherMobileCol = -1;
          }

          if (headerRowIndex === -1) {
            throw new Error('Could not find required USN and NAME columns in the Excel file');
          }

          console.log(`Found header at row ${headerRowIndex + 1}, columns: USN=${usnCol}, NAME=${nameCol}, EMAIL=${emailCol}, STUDENT_MOBILE=${studentMobileCol}, FATHER_MOBILE=${fatherMobileCol}, MOTHER_MOBILE=${motherMobileCol}`);

          // Parse data rows
          const students: any[] = [];
          for (let i = headerRowIndex + 1; i < jsonData.length; i++) {
            const row = jsonData[i] as any[];
            if (!row || row.length === 0) continue;

            const usn = String(row[usnCol] || '').trim();
            const name = String(row[nameCol] || '').trim();
            const email = emailCol !== -1 ? String(row[emailCol] || '').trim() : '';
            const studentMobile = studentMobileCol !== -1 ? String(row[studentMobileCol] || '').trim() : '';
            const fatherMobile = fatherMobileCol !== -1 ? String(row[fatherMobileCol] || '').trim() : '';
            const motherMobile = motherMobileCol !== -1 ? String(row[motherMobileCol] || '').trim() : '';

            // Skip empty rows
            if (!usn && !name) continue;

            if (usn && name) {
              const studentData: any = {
                usn: usn.toUpperCase(),
                student_name: name,
                email: email || `${usn.toLowerCase()}@student.college.edu`,
                department: selectedDepartment,
                semester: selectedSemester,
                section: selectedSection,
                academic_year: selectedAcademicYear
              };

              // Only add phone numbers if they have values
              if (studentMobile) studentData.student_mobile = studentMobile;
              if (fatherMobile) studentData.father_mobile = fatherMobile;
              if (motherMobile) studentData.mother_mobile = motherMobile;

              students.push(studentData);
            }
          }

          resolve(students);
        } catch (error) {
          reject(new Error('Failed to parse Excel file: ' + (error instanceof Error ? error.message : 'Unknown error')));
        }
      };

      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsArrayBuffer(file);
    });
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;

    if (!selectedFile.name.match(/\.(xlsx|xls)$/)) {
      toast({
        title: 'Invalid file type',
        description: 'Please select an Excel file (.xlsx or .xls)',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedDepartment || !selectedSemester || !selectedSection) {
      toast({
        title: 'Missing Information',
        description: 'Please select department, semester, and section first',
        variant: 'destructive',
      });
      return;
    }

    setFile(selectedFile);

    try {
      const parsedData = await parseExcelFile(selectedFile);
      setPreviewData(parsedData.slice(0, 5));
    } catch (error) {
      toast({
        title: 'Error parsing file',
        description: error instanceof Error ? error.message : 'Failed to parse the Excel file',
        variant: 'destructive',
      });
    }
  };

  const handleUpload = async () => {
    if (!file || !selectedDepartment || !selectedSemester || !selectedSection) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      const studentsData = await parseExcelFile(file);
      setUploadProgress(25);

      const success: any[] = [];
      const errors: { row: number; error: string }[] = [];

      // First, get all existing students for this class to check for duplicates
      const { data: existingStudents, error: fetchError } = await supabase
        .from('class_students')
        .select('id, usn')
        .eq('department', selectedDepartment)
        .eq('semester', selectedSemester)
        .eq('section', selectedSection)
        .eq('academic_year', selectedAcademicYear);

      if (fetchError) {
        throw new Error(`Failed to fetch existing students: ${fetchError.message}`);
      }

      // Create a map of existing USNs to their IDs
      const existingUSNMap = new Map();
      if (existingStudents) {
        existingStudents.forEach(student => {
          existingUSNMap.set(student.usn, student.id);
        });
      }

      setUploadProgress(50);

      for (let i = 0; i < studentsData.length; i++) {
        try {
          const studentData = studentsData[i];
          const existingId = existingUSNMap.get(studentData.usn);

          let result;
          if (existingId) {
            // Update existing student
            const { data, error } = await supabase
              .from('class_students')
              .update({
                student_name: studentData.student_name,
                email: studentData.email,
                student_mobile: studentData.student_mobile,
                father_mobile: studentData.father_mobile,
                mother_mobile: studentData.mother_mobile,
                updated_at: new Date().toISOString()
              })
              .eq('id', existingId)
              .select()
              .single();

            if (error) throw error;
            result = data;
          } else {
            // Insert new student
            const { data, error } = await supabase
              .from('class_students')
              .insert(studentData)
              .select()
              .single();

            if (error) throw error;
            result = data;
          }

          success.push(result);
        } catch (error) {
          errors.push({
            row: i + 1,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      setUploadProgress(100);
      setUploadResults({ success, errors });

      if (success.length > 0) {
        const duplicateCount = studentsData.length - success.length - errors.length;
        let description = `Successfully processed ${success.length} students`;

        if (duplicateCount > 0) {
          description += ` (${duplicateCount} updated existing records)`;
        }

        if (errors.length > 0) {
          description += ` with ${errors.length} errors`;
        }

        toast({
          title: 'Upload completed',
          description,
        });
        onSuccess();
      }
    } catch (error) {
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload students',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
    }
  };

  const downloadTemplate = () => {
    const templateData = [
      {
        'USN': '1KS23CS001',
        'Student Name': 'John Doe',
        'Email': '<EMAIL>',
        'Student Mobile': '9876543210',
        'Father Mobile': '9876543211',
        'Mother Mobile': '9876543212'
      }
    ];

    const worksheet = XLSX.utils.json_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Class Students');
    XLSX.writeFile(workbook, 'class_students_template.xlsx');
  };

  const resetUpload = () => {
    setFile(null);
    setPreviewData([]);
    setUploadResults(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Upload Class Students
          </DialogTitle>
          <DialogDescription>
            Upload student class data (USN, Name, Email, Phone Numbers) to create class records.
            After this, use "Create Auth Accounts" to enable student login.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Information Alert */}
          <Alert className="border-blue-200 bg-blue-50">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Note:</strong> If a student with the same USN already exists in this class, their information will be updated with the new data from your Excel file.
            </AlertDescription>
          </Alert>

          {/* Class Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                <SelectTrigger>
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept.value} value={dept.value}>
                      {dept.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="semester">Semester</Label>
              <Select value={selectedSemester} onValueChange={setSelectedSemester}>
                <SelectTrigger>
                  <SelectValue placeholder="Select semester" />
                </SelectTrigger>
                <SelectContent>
                  {semesters.map((sem) => (
                    <SelectItem key={sem} value={sem}>
                      Semester {sem}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="section">Section</Label>
              <Select value={selectedSection} onValueChange={setSelectedSection}>
                <SelectTrigger>
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  {sections.map((sec) => (
                    <SelectItem key={sec} value={sec}>
                      Section {sec}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="academic-year">Academic Year</Label>
              <Select value={selectedAcademicYear} onValueChange={setSelectedAcademicYear}>
                <SelectTrigger>
                  <SelectValue placeholder="Select academic year" />
                </SelectTrigger>
                <SelectContent>
                  {academicYears.map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Template Download */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <FileSpreadsheet className="h-8 w-8 text-green-600" />
              <div>
                <h3 className="font-medium">Download Template</h3>
                <p className="text-sm text-muted-foreground">
                  Excel template with USN, Student Name, Email, Student Mobile, Father Mobile, Mother Mobile
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={downloadTemplate}>
              <Download className="h-4 w-4 mr-2" />
              Download Template
            </Button>
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="file-upload">Select Excel File</Label>
            <Input
              id="file-upload"
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileSelect}
              ref={fileInputRef}
              disabled={uploading || !selectedDepartment || !selectedSemester || !selectedSection}
            />
          </div>

          {/* Preview Data */}
          {previewData.length > 0 && (
            <div className="space-y-2">
              <Label>Preview (First 5 rows)</Label>
              <div className="border rounded-lg overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-muted">
                    <tr>
                      <th className="p-2 text-left">USN</th>
                      <th className="p-2 text-left">Student Name</th>
                      <th className="p-2 text-left">Email</th>
                      <th className="p-2 text-left">Student Mobile</th>
                      <th className="p-2 text-left">Father Mobile</th>
                      <th className="p-2 text-left">Mother Mobile</th>
                    </tr>
                  </thead>
                  <tbody>
                    {previewData.map((student, index) => (
                      <tr key={index} className="border-t">
                        <td className="p-2">{student.usn}</td>
                        <td className="p-2">{student.student_name}</td>
                        <td className="p-2">{student.email}</td>
                        <td className="p-2">{student.student_mobile || '-'}</td>
                        <td className="p-2">{student.father_mobile || '-'}</td>
                        <td className="p-2">{student.mother_mobile || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-2">
              <Label>Upload Progress</Label>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {/* Upload Results */}
          {uploadResults && (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Badge variant="default" className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  {uploadResults.success.length} Successful
                </Badge>
                {uploadResults.errors.length > 0 && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <X className="h-3 w-3" />
                    {uploadResults.errors.length} Errors
                  </Badge>
                )}
              </div>

              {uploadResults.errors.length > 0 && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">Upload Errors:</p>
                      {uploadResults.errors.slice(0, 5).map((error, index) => (
                        <p key={index} className="text-sm">
                          Row {error.row}: {error.error}
                        </p>
                      ))}
                      {uploadResults.errors.length > 5 && (
                        <p className="text-sm text-muted-foreground">
                          ... and {uploadResults.errors.length - 5} more errors
                        </p>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={resetUpload} disabled={uploading}>
            Reset
          </Button>
          <Button variant="outline" onClick={onClose} disabled={uploading}>
            Close
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!file || uploading || !selectedDepartment || !selectedSemester || !selectedSection}
            className="min-w-[150px]"
          >
            {uploading ? 'Uploading...' : 'Upload Class Students'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClassStudentBulkUpload;
