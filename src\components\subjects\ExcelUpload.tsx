
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Plus, X, FileSpreadsheet, FileJson, Files } from 'lucide-react';
import { UserService, Employee } from '@/services/UserService';
import {
  processFile,
  validateEmployeeData,
} from '@/utils/employeeDataProcessing';
import { getFileType } from '@/utils/fileCore';

export interface ExcelUploadProps {
  /** Called after a successful bulk import */
  onUploadComplete?: () => void;
  /** Process uploaded file data */
  onFileProcessed?: (data: Partial<Employee>[]) => Promise<{ success: Employee[]; failed: Partial<Employee>[]; } | void>;
}

const ExcelUpload: React.FC<ExcelUploadProps> = ({ onUploadComplete, onFileProcessed }) => {
  const [file, setFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [fileType, setFileType] = useState<'excel' | 'csv' | 'json' | null>(null);
  const { toast } = useToast();

  const handleDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;
    const selected = acceptedFiles[0];
    setFile(selected);
    setIsProcessing(true);

    try {
      // 1️⃣ Detect file type
      const type = getFileType(selected.name);
      if (!type) throw new Error('Only .xlsx/.xls, .csv or .json files are supported');
      setFileType(type);

      // 2️⃣ Parse & validate
      const raw = await processFile(selected);
      const valid = validateEmployeeData(raw);
      if (valid.length === 0) {
        toast({
          title: 'No valid records',
          description: 'Your file contained no valid employee entries.',
          variant: 'destructive',
        });
        return;
      }

      // Handle both callback styles for backward compatibility
      if (onFileProcessed) {
        await onFileProcessed(valid);
      } else {
        // 3️⃣ Bulk-import
        const { success, failed } = await UserService.addMultipleEmployees(valid);

        if (success.length > 0) {
          toast({
            title: 'Import Successful',
            description: `Imported ${success.length} employees.`,
          });
        }
        if (failed.length > 0) {
          toast({
            title: 'Partial Failure',
            description: `${failed.length} records failed to import.`,
            variant: 'destructive',
          });
        }
      }

      // 4️⃣ Notify parent to refresh
      onUploadComplete?.();
    } catch (err: any) {
      toast({
        title: 'Upload Error',
        description: err.message ?? 'Unknown error occurred.',
        variant: 'destructive',
      });
    } finally {
      // reset
      setIsProcessing(false);
      setFile(null);
      setFileType(null);
    }
  }, [onUploadComplete, onFileProcessed, toast]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: handleDrop,
    accept: {
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/csv': ['.csv'],
      'application/json': ['.json'],
    },
    maxFiles: 1,
    disabled: isProcessing,
  });

  const renderIcon = () => {
    switch (fileType) {
      case 'excel': return <FileSpreadsheet className="h-5 w-5 text-primary" />;
      case 'csv':   return <Files           className="h-5 w-5 text-primary" />;
      case 'json':  return <FileJson         className="h-5 w-5 text-primary" />;
      default:      return null;
    }
  };

  if (file) {
    return (
      <div className="inline-flex items-center gap-2 px-3 py-1 bg-muted rounded-full">
        {renderIcon()}
        <span className="text-sm truncate max-w-xs">{file.name}</span>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setFile(null)}
          disabled={isProcessing}
          className="h-6 w-6 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div {...getRootProps()}>
      <input {...getInputProps()} />
      <Button
        variant="outline"
        disabled={isProcessing}
        className="inline-flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Upload File
      </Button>
    </div>
  );
};

export default ExcelUpload;
