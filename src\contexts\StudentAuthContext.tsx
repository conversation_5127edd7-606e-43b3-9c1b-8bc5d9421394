import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import {
  SimpleStudentAuthService,
  StudentAuthData
} from "@/services/SimpleStudentAuthService";
import { useToast } from "@/hooks/use-toast";

export interface StudentRegistrationData {
  usn: string;
  email: string;
  password: string;
}

export interface StudentLoginCredentials {
  usn: string;
  password: string;
}

export interface StudentSession {
  student: StudentAuthData;
  token: string;
  expires_at: string;
}

interface StudentDetails {
  student_name: string;
  semester: string;
  section: string;
  department: string;
  email?: string;
}

type StudentAuthContextType = {
  student: StudentAuthData | null;
  studentDetails: StudentDetails | null;
  session: StudentSession | null;
  loading: boolean;
  isAuthenticated: boolean;
  register: (data: StudentRegistrationData) => Promise<void>;
  login: (credentials: StudentLoginCredentials) => Promise<void>;
  logout: () => void;
};

const StudentAuthContext = createContext<StudentAuthContextType | undefined>(undefined);

export function StudentAuthProvider({ children }: { children: ReactNode }) {
  const [student, setStudent] = useState<StudentAuthData | null>(null);
  const [studentDetails, setStudentDetails] = useState<StudentDetails | null>(null);
  const [session, setSession] = useState<StudentSession | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Session storage keys
  const SESSION_KEY = 'student_session';

  // Fetch student details from class_students table
  const fetchStudentDetails = async (usn: string) => {
    try {
      const details = await SimpleStudentAuthService.getStudentDetails(usn);
      setStudentDetails(details);
    } catch (error) {
      console.error('Error fetching student details:', error);
      setStudentDetails(null);
    }
  };

  useEffect(() => {
    // Check for existing session on mount
    loadSession();
  }, []);

  const loadSession = () => {
    try {
      // Check if logout was just performed
      const logoutPerformed = localStorage.getItem('logout_performed');
      if (logoutPerformed) {
        // Clear the logout flag and ensure clean state
        localStorage.removeItem('logout_performed');
        localStorage.removeItem(SESSION_KEY);
        setSession(null);
        setStudent(null);
        setStudentDetails(null);
        setLoading(false);
        return;
      }

      const savedSession = localStorage.getItem(SESSION_KEY);
      if (savedSession) {
        const parsedSession: StudentSession = JSON.parse(savedSession);

        // Simple session validation (check if not expired)
        const now = new Date();
        const expiresAt = new Date(parsedSession.expires_at);
        if (now < expiresAt) {
          setSession(parsedSession);
          setStudent(parsedSession.student);
          // Fetch student details
          fetchStudentDetails(parsedSession.student.usn);
        } else {
          // Session expired, clear it
          localStorage.removeItem(SESSION_KEY);
        }
      }
    } catch (error) {
      console.error('Error loading session:', error);
      localStorage.removeItem(SESSION_KEY);
    } finally {
      setLoading(false);
    }
  };

  const saveSession = (newSession: StudentSession) => {
    try {
      localStorage.setItem(SESSION_KEY, JSON.stringify(newSession));
      setSession(newSession);
      setStudent(newSession.student);
      // Fetch student details
      fetchStudentDetails(newSession.student.usn);
    } catch (error) {
      console.error('Error saving session:', error);
      throw new Error('Failed to save session');
    }
  };

  const clearSession = () => {
    localStorage.removeItem(SESSION_KEY);
    setSession(null);
    setStudent(null);
    setStudentDetails(null);
  };

  const register = async (data: StudentRegistrationData) => {
    try {
      setLoading(true);

      // For now, registration is disabled as accounts are created by admin
      throw new Error('Student registration is currently disabled. Please contact your administrator.');

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Registration failed';
      toast({
        title: "Registration failed",
        description: message,
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials: StudentLoginCredentials) => {
    try {
      setLoading(true);

      const authenticatedStudent = await SimpleStudentAuthService.authenticateStudent(credentials.usn, credentials.password);

      if (!authenticatedStudent) {
        throw new Error('Invalid USN or password');
      }

      // Create session
      const token = btoa(Math.random().toString(36).substring(2) + Date.now().toString(36));
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour session

      const newSession: StudentSession = {
        student: authenticatedStudent,
        token,
        expires_at: expiresAt.toISOString()
      };

      saveSession(newSession);

      toast({
        title: "Login successful",
        description: `Welcome back, ${authenticatedStudent.usn}!`,
      });

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      toast({
        title: "Login failed",
        description: message,
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    // Clear student session
    clearSession();

    // Set logout flag to prevent auto-login
    localStorage.setItem('logout_performed', 'true');

    // Clear any additional auth data
    localStorage.removeItem('auth_session');
    localStorage.removeItem('user_session');
    sessionStorage.clear();

    toast({
      title: "Logged out",
      description: "You have been logged out successfully.",
    });

    // Redirect to landing page after logout
    window.location.href = '/';
  };

  const value: StudentAuthContextType = {
    student,
    studentDetails,
    session,
    loading,
    isAuthenticated: !!student && !!session,
    register,
    login,
    logout,
  };

  return (
    <StudentAuthContext.Provider value={value}>
      {children}
    </StudentAuthContext.Provider>
  );
}

export function useStudentAuth() {
  const context = useContext(StudentAuthContext);
  if (context === undefined) {
    throw new Error('useStudentAuth must be used within a StudentAuthProvider');
  }
  return context;
}
