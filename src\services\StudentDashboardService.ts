import { supabase } from "@/integrations/supabase/client";

export interface StudentSubject {
  id: string;
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: string;
  section: string;
  department: string;
  faculty_1_id: string;
  faculty_2_id?: string;
  faculty_1_name: string;
  faculty_2_name?: string;
  hours_per_week: number;
  attendance_percentage: number;
  total_classes: number;
  attended_classes: number;
  attendance_status: 'good' | 'warning' | 'critical' | 'no-classes';
}

export interface StudentDashboardData {
  student_usn: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  subjects: StudentSubject[];
  overall_attendance: number;
}

export class StudentDashboardService {
  /**
   * Get comprehensive dashboard data for a student
   */
  static async getStudentDashboardData(usn: string): Promise<StudentDashboardData | null> {
    try {
      console.log('📊 Fetching dashboard data for student:', usn);

      // Step 1: Get student details using service function to bypass RLS
      const { data: studentData, error: studentError } = await supabase
        .rpc('get_student_dashboard_data', { student_usn: usn.toUpperCase() });

      if (studentError || !studentData || studentData.length === 0) {
        console.error('Student not found:', studentError);
        return null;
      }

      const student = studentData[0];
      console.log('✅ Student found:', student);

      // Step 2: Get subjects for the student's class
      const subjects = await this.getStudentSubjects(
        student.department,
        student.semester,
        student.section,
        student.academic_year || '2024-25'
      );

      // Step 3: Calculate attendance for each subject
      const subjectsWithAttendance = await Promise.all(
        subjects.map(async (subject) => {
          const attendance = await this.getSubjectAttendance(
            usn,
            subject.subject_code,
            student.department,
            student.semester,
            student.section
          );

          return {
            ...subject,
            ...attendance,
            attendance_status: this.getAttendanceStatus(attendance.attendance_percentage, attendance.total_classes)
          };
        })
      );

      // Step 4: Calculate overall attendance
      const totalClasses = subjectsWithAttendance.reduce((sum, subject) => sum + subject.total_classes, 0);
      const totalAttended = subjectsWithAttendance.reduce((sum, subject) => sum + subject.attended_classes, 0);
      const overall_attendance = totalClasses > 0 ? Math.round((totalAttended / totalClasses) * 100) : 0;

      return {
        student_usn: student.usn,
        student_name: student.student_name,
        department: student.department,
        semester: student.semester,
        section: student.section,
        subjects: subjectsWithAttendance,
        overall_attendance
      };

    } catch (error) {
      console.error('Error fetching student dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get subjects for a student's class using service function to bypass RLS
   */
  static async getStudentSubjects(
    department: string,
    semester: string,
    section: string,
    academicYear: string = '2024-25'
  ): Promise<StudentSubject[]> {
    try {
      console.log(`📚 Fetching subjects for ${department} ${semester} ${section}`);

      // Use service function to get subjects (bypasses RLS)
      const { data: subjects, error: subjectsError } = await supabase
        .rpc('get_student_subjects', {
          student_department: department,
          student_semester: semester,
          student_section: section,
          student_academic_year: academicYear
        });

      if (subjectsError) {
        console.error('Error fetching subjects:', subjectsError);
        throw subjectsError;
      }

      if (!subjects || subjects.length === 0) {
        console.log('No subjects found for the class');
        return [];
      }

      console.log(`✅ Found ${subjects.length} subjects:`, subjects.map(s => s.subject_code).join(', '));

      // Map the data to our interface
      return subjects.map(subject => ({
        id: subject.id,
        subject_code: subject.subject_code,
        subject_name: subject.subject_name,
        subject_type: subject.subject_type,
        semester: subject.semester,
        section: subject.section,
        department: subject.department,
        faculty_1_id: subject.faculty_1_id,
        faculty_2_id: subject.faculty_2_id,
        faculty_1_name: subject.faculty_1_name,
        faculty_2_name: subject.faculty_2_name,
        hours_per_week: subject.hours_per_week || 0,
        attendance_percentage: 0,
        total_classes: 0,
        attended_classes: 0,
        attendance_status: 'no-classes' as const
      }));

    } catch (error) {
      console.error('Error fetching student subjects:', error);
      throw error;
    }
  }

  /**
   * Get attendance data for a specific subject using service function to bypass RLS
   */
  static async getSubjectAttendance(
    studentUsn: string,
    subjectCode: string,
    department: string,
    semester: string,
    section: string
  ): Promise<{
    attendance_percentage: number;
    total_classes: number;
    attended_classes: number;
  }> {
    try {
      console.log(`📊 Calculating attendance for ${studentUsn} in ${subjectCode}`);

      // Use service function to get attendance (bypasses RLS)
      const { data: attendanceData, error: attendanceError } = await supabase
        .rpc('get_student_attendance', {
          p_student_usn: studentUsn.toUpperCase(),
          p_subject_code: subjectCode,
          p_student_department: department,
          p_student_semester: semester,
          p_student_section: section
        });

      if (attendanceError) {
        console.error('Error fetching attendance:', attendanceError);
        return {
          attendance_percentage: 0,
          total_classes: 0,
          attended_classes: 0
        };
      }

      if (!attendanceData || attendanceData.length === 0) {
        console.log('No attendance data found');
        return {
          attendance_percentage: 0,
          total_classes: 0,
          attended_classes: 0
        };
      }

      const result = attendanceData[0];
      console.log(`✅ Attendance calculated: ${result.attended_classes}/${result.total_classes} (${result.attendance_percentage}%)`);

      return {
        attendance_percentage: result.attendance_percentage,
        total_classes: Number(result.total_classes),
        attended_classes: Number(result.attended_classes)
      };

    } catch (error) {
      console.error('Error calculating subject attendance:', error);
      return {
        attendance_percentage: 0,
        total_classes: 0,
        attended_classes: 0
      };
    }
  }

  /**
   * Determine attendance status based on percentage and total classes
   */
  static getAttendanceStatus(percentage: number, totalClasses: number = 0): 'good' | 'warning' | 'critical' | 'no-classes' {
    // If no classes have been conducted yet, show neutral status
    if (totalClasses === 0) return 'no-classes';

    if (percentage >= 75) return 'good';
    if (percentage >= 65) return 'warning';
    return 'critical';
  }

  /**
   * Get attendance status color and styling information
   */
  static getAttendanceStatusDisplay(status: 'good' | 'warning' | 'critical' | 'no-classes') {
    switch (status) {
      case 'good':
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          iconName: 'CheckCircle',
          label: 'Good'
        };
      case 'warning':
        return {
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          iconName: 'AlertTriangle',
          label: 'Warning'
        };
      case 'critical':
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          iconName: 'XCircle',
          label: 'Critical'
        };
      case 'no-classes':
        return {
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          iconName: 'Clock',
          label: 'Pending'
        };
    }
  }
}
