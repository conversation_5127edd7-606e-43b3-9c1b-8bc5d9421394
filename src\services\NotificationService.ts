// NotificationService.ts
import { RescheduledSlotInfo } from "@/types/ConflictResolution";

/**
 * Service to handle notifications for conflict resolution
 */
export class NotificationService {
  /**
   * Show a persistent notification about automatic conflict resolution
   */
  static showConflictResolutionNotification(
    resolvedConflicts: number,
    rescheduledSlots?: RescheduledSlotInfo[]
  ): void {
    console.log(`🔔 NOTIFICATION: Automatically resolved ${resolvedConflicts} critical scheduling conflicts`);
    
    if (rescheduledSlots && rescheduledSlots.length > 0) {
      console.log("📋 Rescheduled slots:");
      rescheduledSlots.forEach(slot => {
        console.log(`  • ${slot.originalSlot.subject_code}: ${slot.originalSlot.day} ${slot.originalSlot.time_slot} → ${slot.newSlot.day} ${slot.newSlot.time_slot}`);
        console.log(`    Reason: ${slot.reason}`);
      });
    }

    // In a real application, this would show a toast notification or modal
    // For now, we'll use console logging with clear formatting
    console.log("=" .repeat(80));
    console.log("🎯 AUTOMATIC CONFLICT RESOLUTION COMPLETED");
    console.log(`✅ Successfully resolved ${resolvedConflicts} critical conflicts`);
    console.log("📌 Lab slots remained fixed as per timetable architecture");
    console.log("🔄 Theory slots were intelligently rescheduled to avoid conflicts");
    console.log("=" .repeat(80));
  }

  /**
   * Show warning notification for unresolved conflicts
   */
  static showUnresolvedConflictsWarning(
    unresolvedCount: number,
    warnings: string[]
  ): void {
    console.warn(`⚠️  WARNING: ${unresolvedCount} conflicts could not be automatically resolved`);
    
    if (warnings.length > 0) {
      console.warn("Details:");
      warnings.forEach(warning => {
        console.warn(`  • ${warning}`);
      });
    }

    console.warn("🔧 Manual intervention may be required for these conflicts");
  }

  /**
   * Show notification about faculty availability updates
   */
  static showFacultyAvailabilityUpdate(
    facultyName: string,
    originalSlot: { day: string; timeSlot: string },
    newSlot: { day: string; timeSlot: string }
  ): void {
    console.log(`👤 Faculty availability updated for ${facultyName}:`);
    console.log(`  📅 Freed: ${originalSlot.day} ${originalSlot.timeSlot}`);
    console.log(`  📅 Occupied: ${newSlot.day} ${newSlot.timeSlot}`);
  }
}
