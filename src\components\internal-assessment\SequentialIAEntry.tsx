import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import {
  ClipboardCheck,
  Users,
  BookOpen,
  Building2,
  RefreshCw,
  Save,
  CheckCircle,
  Clock,
  AlertCircle,
  TrendingUp,
  Award,
  Target,
  ArrowRight,
  Lock,
  Unlock
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FacultyAssignmentService } from '@/services/FacultyAssignmentService';
import { InternalAssessmentService } from '@/services/InternalAssessmentService';
import { SequentialIAEntryService, SequentialIASession, SequentialIAStudent, IAPhase } from '@/services/SequentialIAEntryService';

// Enhanced interfaces for sequential IA entry
interface FacultySubject {
  subject_code: string;
  subject_name: string;
  subject_type: 'theory' | 'laboratory' | 'elective';
  semester: string;
  section: string;
  display_name: string;
  key: string;
  batch_name?: string;
}

export default function SequentialIAEntry() {
  const { user } = useAuth();
  const { toast } = useToast();
  const {
    department,
    departmentName,
    fullName,
    loading: departmentLoading,
    error: departmentError
  } = useUserDepartment();

  // State management
  const [facultySubjects, setFacultySubjects] = useState<FacultySubject[]>([]);
  const [selectedSubjectKey, setSelectedSubjectKey] = useState<string>('');
  const [currentPhase, setCurrentPhase] = useState<string>('IA1');
  const [selectedPhaseForEdit, setSelectedPhaseForEdit] = useState<string>('');
  const [allPhases, setAllPhases] = useState<IAPhase[]>([]);
  const [students, setStudents] = useState<SequentialIAStudent[]>([]);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editMode, setEditMode] = useState(false);

  // Load faculty subjects on component mount
  useEffect(() => {
    if (user?.id && department) {
      loadFacultySubjects();
    }
  }, [user?.id, department]);

  // Load faculty teaching assignments
  const loadFacultySubjects = useCallback(async () => {
    if (!user?.id || !department) return;

    try {
      setAssignmentsLoading(true);
      console.log('🔄 Loading faculty subjects for sequential IA entry...');

      const assignments = await FacultyAssignmentService.getFacultyAssignments(user.id, department);

      console.log('📊 Faculty assignments loaded:', {
        totalAssignments: assignments.assignments.length,
        subjects: assignments.subjects.length
      });

      // Transform assignments to faculty subjects
      const transformedSubjects: FacultySubject[] = assignments.assignments.map(assignment => {
        const displayName = `${assignment.subject_code} - ${assignment.subject_name} (Sem ${assignment.semester} - Sec ${assignment.section})`;
        const key = `${assignment.subject_code}-${assignment.semester}-${assignment.section}-${assignment.subject_type}`;

        return {
          subject_code: assignment.subject_code,
          subject_name: assignment.subject_name,
          subject_type: assignment.subject_type as 'theory' | 'laboratory' | 'elective',
          semester: assignment.semester,
          section: assignment.section,
          display_name: displayName,
          key: key
        };
      });

      // Sort subjects by semester, then section, then subject code
      transformedSubjects.sort((a, b) => {
        if (a.semester !== b.semester) return parseInt(a.semester) - parseInt(b.semester);
        if (a.section !== b.section) return a.section.localeCompare(b.section);
        return a.subject_code.localeCompare(b.subject_code);
      });

      setFacultySubjects(transformedSubjects);

      console.log('✅ Faculty subjects loaded for sequential IA entry:', transformedSubjects.length);

      if (transformedSubjects.length === 0) {
        toast({
          title: 'No Subjects Found',
          description: 'No teaching assignments found for your account. Please contact administrator.',
          variant: 'default',
        });
      }
    } catch (error) {
      console.error('Error loading faculty subjects:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your teaching assignments.',
        variant: 'destructive',
      });
    } finally {
      setAssignmentsLoading(false);
    }
  }, [user?.id, department, toast]);

  // Load sequential IA session for selected subject
  const loadSequentialIASession = useCallback(async () => {
    if (!user?.id || !department || !selectedSubjectKey) {
      toast({
        title: 'Missing Information',
        description: 'Please select a subject.',
        variant: 'default',
      });
      return;
    }

    // Find the selected subject
    const selectedSubject = facultySubjects.find(s => s.key === selectedSubjectKey);
    if (!selectedSubject) {
      toast({
        title: 'Subject Not Found',
        description: 'Selected subject not found in your assignments.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);
      console.log('🔄 Loading sequential IA session for:', selectedSubject);

      // Get current academic year
      const currentYear = new Date().getFullYear();
      const academicYear = `${currentYear}-${currentYear + 1}`;

      // First, get students using existing logic
      const basicSession = await InternalAssessmentService.getStudentsForIAWithDynamicLoading(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear
      );

      if (basicSession.students.length === 0) {
        toast({
          title: 'No Students Found',
          description: `No students found for ${selectedSubject.subject_type} subject "${selectedSubject.subject_code}" (Sem ${selectedSubject.semester} - Sec ${selectedSubject.section}).`,
          variant: 'destructive',
        });
        return;
      }

      // Get current phase status
      const { currentPhase: detectedPhase, completedPhases } = await SequentialIAEntryService.getCurrentPhaseStatus(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear
      );

      // Set up phases
      const phases = SequentialIAEntryService.getIAPhases(selectedSubject.subject_type);
      const updatedPhases = phases.map(phase => ({
        ...phase,
        isCompleted: completedPhases.includes(phase.phase),
        isActive: phase.phase === detectedPhase,
        isLocked: !completedPhases.includes(phase.phase) && phase.phase !== detectedPhase
      }));

      setAllPhases(updatedPhases);
      setCurrentPhase(detectedPhase);
      setSelectedPhaseForEdit(detectedPhase); // Default to current phase
      setEditMode(false); // Reset edit mode

      // Get students with current phase marks
      const studentsWithMarks = await SequentialIAEntryService.getStudentsForPhase(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear,
        detectedPhase,
        basicSession.students
      );

      setStudents(studentsWithMarks);

      toast({
        title: 'Sequential IA Session Loaded',
        description: `Loaded ${studentsWithMarks.length} students. Current phase: ${detectedPhase}`,
      });

    } catch (error) {
      console.error('❌ Error loading sequential IA session:', error);

      let errorMessage = 'Failed to load sequential IA session.';
      if (error instanceof Error) {
        if (error.message.includes('access')) {
          errorMessage = `You do not have access to teach this subject. Please contact administrator.`;
        } else if (error.message.includes('students')) {
          errorMessage = `Unable to load student list. Please verify that students have been uploaded.`;
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: 'Failed to Load Sequential IA Session',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, department, selectedSubjectKey, facultySubjects, toast]);

  // Load students for a specific phase (for editing completed phases)
  const loadStudentsForPhase = useCallback(async (phaseToLoad: string) => {
    if (!user?.id || !department || !selectedSubjectKey) return;

    const selectedSubject = facultySubjects.find(s => s.key === selectedSubjectKey);
    if (!selectedSubject) return;

    try {
      setLoading(true);
      console.log('🔄 Loading students for phase:', phaseToLoad);

      // Get current academic year
      const currentYear = new Date().getFullYear();
      const academicYear = `${currentYear}-${currentYear + 1}`;

      // Get basic session first
      const basicSession = await InternalAssessmentService.getStudentsForIAWithDynamicLoading(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear
      );

      // Get students with marks for the selected phase
      const studentsWithMarks = await SequentialIAEntryService.getStudentsForPhase(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear,
        phaseToLoad,
        basicSession.students
      );

      setStudents(studentsWithMarks);
      setSelectedPhaseForEdit(phaseToLoad);

      toast({
        title: 'Phase Loaded for Editing',
        description: `Loaded ${studentsWithMarks.length} students for ${phaseToLoad} editing.`,
      });

    } catch (error) {
      console.error('❌ Error loading students for phase:', error);
      toast({
        title: 'Error Loading Phase',
        description: 'Failed to load students for the selected phase.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, department, selectedSubjectKey, facultySubjects, toast]);

  // Handle marks change for current/selected phase
  const handleMarksChange = (studentId: string, value: string) => {
    const numValue = value === '' ? undefined : parseFloat(value);

    // Validate marks
    if (numValue !== undefined) {
      const activePhase = editMode ? selectedPhaseForEdit : currentPhase;
      const currentPhaseData = allPhases.find(p => p.phase === activePhase);
      const maxValue = currentPhaseData?.maxMarks || 25;

      if (numValue < 0 || numValue > maxValue) {
        toast({
          title: 'Invalid Marks',
          description: `Marks must be between 0 and ${maxValue}`,
          variant: 'destructive',
        });
        return;
      }
    }

    setStudents(prevStudents =>
      prevStudents.map(student =>
        student.id === studentId
          ? {
              ...student,
              current_marks: numValue,
              is_completed: numValue !== undefined && numValue !== null
            }
          : student
      )
    );
  };

  // Save current/selected phase marks
  const handleSaveCurrentPhase = useCallback(async () => {
    const activePhase = editMode ? selectedPhaseForEdit : currentPhase;

    if (!user?.id || !department || !selectedSubjectKey || !activePhase) {
      toast({
        title: 'Missing Information',
        description: 'Unable to save marks. Please ensure all required information is available.',
        variant: 'destructive',
      });
      return;
    }

    const selectedSubject = facultySubjects.find(s => s.key === selectedSubjectKey);
    if (!selectedSubject) {
      toast({
        title: 'Subject Not Found',
        description: 'Selected subject not found.',
        variant: 'destructive',
      });
      return;
    }

    // Filter students with marks entered
    const studentsWithMarks = students.filter(s =>
      s.current_marks !== undefined && s.current_marks !== null
    );

    if (studentsWithMarks.length === 0) {
      toast({
        title: 'No Marks to Save',
        description: 'Please enter marks for at least one student.',
        variant: 'default',
      });
      return;
    }

    try {
      setSaving(true);
      console.log('💾 Saving phase marks:', {
        phase: activePhase,
        editMode: editMode,
        studentsCount: studentsWithMarks.length,
        subject: selectedSubject.subject_code
      });

      // Get current academic year
      const currentYear = new Date().getFullYear();
      const academicYear = `${currentYear}-${currentYear + 1}`;

      // Prepare marks data for saving
      const marksData = studentsWithMarks.map(student => ({
        student_id: student.id,
        marks: student.current_marks!
      }));

      // Save marks using the sequential IA service with validation
      const saveResult = await SequentialIAEntryService.saveCurrentPhaseMarks(
        user.id,
        department,
        selectedSubject.subject_code,
        selectedSubject.semester,
        selectedSubject.section,
        academicYear,
        activePhase,
        marksData
      );

      // Handle validation errors
      if (!saveResult.success) {
        // Check if errors are critical or can be bypassed
        const criticalErrors = saveResult.errors.filter(error =>
          !error.includes('No subjects found') &&
          !error.includes('Database error') &&
          !error.includes('406')
        );

        if (criticalErrors.length > 0) {
          toast({
            title: 'Validation Failed',
            description: `Critical errors: ${criticalErrors.join(', ')}`,
            variant: 'destructive',
          });
          return;
        } else {
          // Only warnings/non-critical errors, show warning but continue
          console.warn('⚠️ Non-critical validation issues:', saveResult.errors);
          toast({
            title: 'Validation Warnings',
            description: 'Some validation warnings occurred, but marks were saved. Check console for details.',
            variant: 'default',
          });
        }
      }

      // Handle warnings
      if (saveResult.warnings.length > 0) {
        console.warn('⚠️ Save warnings:', saveResult.warnings);
      }

      const actionText = editMode ? 'updated' : 'saved';
      const warningText = (saveResult.warnings.length > 0 || saveResult.errors.length > 0) ? ' (Check console for details)' : '';

      toast({
        title: `Marks ${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Successfully`,
        description: `${activePhase} marks ${actionText} for ${saveResult.savedCount} students.${warningText}`,
      });

      // Reload the session to update phase status and unlock next phase
      await loadSequentialIASession();

    } catch (error) {
      console.error('❌ Error saving current phase marks:', error);

      let errorMessage = 'Failed to save marks.';
      if (error instanceof Error) {
        if (error.message.includes('too long')) {
          errorMessage = 'One of the values is too long for the database field. Please check semester/section values.';
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: 'Error Saving Marks',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  }, [user?.id, department, selectedSubjectKey, currentPhase, selectedPhaseForEdit, editMode, students, facultySubjects, toast, loadSequentialIASession]);

  // Show loading state while fetching department info
  if (departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading department information...</span>
        </div>
      </div>
    );
  }

  // Show error if department info couldn't be loaded
  if (departmentError || !department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {departmentError || 'Unable to load your department information. Please contact administrator.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Target className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold">Sequential IA Entry</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadFacultySubjects}
            disabled={assignmentsLoading}
          >
            {assignmentsLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh Subjects
          </Button>
        </div>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <strong>Faculty:</strong> {fullName} | <strong>Department:</strong> {departmentName}
            </div>
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <BookOpen className="h-4 w-4" />
                <span>{facultySubjects.length} Subjects</span>
              </div>
              {students.length > 0 && (
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{students.length} Students</span>
                </div>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Subject Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Select Subject for Sequential IA Entry
          </CardTitle>
          <CardDescription>
            Choose a subject to begin sequential IA entry. Enter one IA at a time: IA1 → IA2 → IA3 → Assignment.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {assignmentsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 mx-auto mb-4 animate-spin text-blue-600" />
                <p className="text-muted-foreground">Loading your teaching assignments...</p>
              </div>
            </div>
          ) : facultySubjects.length === 0 ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No teaching assignments found. Please contact administrator to verify your subject assignments.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Your Teaching Subjects ({facultySubjects.length} subjects found)
                </label>
                <Select value={selectedSubjectKey} onValueChange={setSelectedSubjectKey}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a subject to begin sequential IA entry" />
                  </SelectTrigger>
                  <SelectContent>
                    {facultySubjects.map((subject) => (
                      <SelectItem key={subject.key} value={subject.key}>
                        <div className="flex items-center gap-2">
                          <Badge variant={
                            subject.subject_type === 'theory' ? 'default' :
                            subject.subject_type === 'laboratory' ? 'secondary' : 'outline'
                          }>
                            {subject.subject_type.toUpperCase()}
                          </Badge>
                          <span>{subject.display_name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedSubjectKey && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-blue-800">Selected Subject</h4>
                      <p className="text-sm text-blue-600">
                        {facultySubjects.find(s => s.key === selectedSubjectKey)?.display_name}
                      </p>
                    </div>
                    <Button
                      onClick={loadSequentialIASession}
                      disabled={loading}
                      className="min-w-[140px]"
                    >
                      {loading ? (
                        <div className="flex items-center gap-2">
                          <RefreshCw className="h-4 w-4 animate-spin" />
                          Loading...
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Target className="h-4 w-4" />
                          Load IA Session
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sequential IA Phase Management */}
      {students.length > 0 && allPhases.length > 0 && (
        <>
          {/* Phase Progress Indicator */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5" />
                    {facultySubjects.find(s => s.key === selectedSubjectKey)?.subject_name}
                    <Badge variant={
                      facultySubjects.find(s => s.key === selectedSubjectKey)?.subject_type === 'theory' ? 'default' :
                      facultySubjects.find(s => s.key === selectedSubjectKey)?.subject_type === 'laboratory' ? 'secondary' : 'outline'
                    }>
                      {facultySubjects.find(s => s.key === selectedSubjectKey)?.subject_type.toUpperCase()}
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    Sequential IA Entry • Current Phase: <strong>{currentPhase}</strong>
                  </CardDescription>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-primary">
                    {students.filter(s => s.is_completed).length}/{students.length} Completed
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {Math.round((students.filter(s => s.is_completed).length / students.length) * 100)}% Progress
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Phase Progress Steps */}
              <div className="grid grid-cols-4 gap-4 mb-6">
                {allPhases.map((phase, index) => (
                  <div key={phase.phase} className={`p-4 rounded-lg border-2 ${
                    selectedPhaseForEdit === phase.phase && editMode
                      ? 'border-purple-200 bg-purple-50'
                      : phase.isCompleted
                      ? 'border-green-200 bg-green-50'
                      : phase.isActive
                      ? 'border-primary/20 bg-primary/5'
                      : phase.isLocked
                      ? 'border-border bg-muted/50'
                      : 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      {selectedPhaseForEdit === phase.phase && editMode ? (
                        <Target className="h-5 w-5 text-secondary" />
                      ) : phase.isCompleted ? (
                        <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                      ) : phase.isActive ? (
                        <Clock className="h-5 w-5 text-primary" />
                      ) : phase.isLocked ? (
                        <Lock className="h-5 w-5 text-muted-foreground" />
                      ) : (
                        <Unlock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                      )}
                      <span className="font-medium text-sm">{phase.name}</span>
                    </div>
                    <div className="text-xs text-muted-foreground mb-2">
                      {phase.description}
                    </div>
                    <div className="flex flex-col gap-1">
                      {selectedPhaseForEdit === phase.phase && editMode && (
                        <Badge variant="outline" className="text-purple-600 border-purple-600 text-xs">
                          Editing
                        </Badge>
                      )}
                      {phase.isCompleted && !(selectedPhaseForEdit === phase.phase && editMode) && (
                        <>
                          <Badge variant="outline" className="text-green-600 border-green-600 text-xs mb-1">
                            Completed
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs h-6 px-2"
                            onClick={() => {
                              setEditMode(true);
                              loadStudentsForPhase(phase.phase);
                            }}
                          >
                            Edit
                          </Button>
                        </>
                      )}
                      {phase.isActive && !editMode && (
                        <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                          Active
                        </Badge>
                      )}
                      {phase.isLocked && (
                        <Badge variant="outline" className="text-gray-400 border-gray-400 text-xs">
                          Locked
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Current Phase Information */}
              {currentPhase !== 'COMPLETED' && (
                <Alert className={editMode ? "border-purple-200 bg-purple-50" : "border-blue-200 bg-blue-50"}>
                  <TrendingUp className={`h-4 w-4 ${editMode ? 'text-purple-600' : 'text-blue-600'}`} />
                  <AlertDescription className={editMode ? 'text-purple-800' : 'text-blue-800'}>
                    {editMode ? (
                      <>
                        <strong>Editing Mode:</strong> {allPhases.find(p => p.phase === selectedPhaseForEdit)?.name} •
                        <strong> Max Marks:</strong> {allPhases.find(p => p.phase === selectedPhaseForEdit)?.maxMarks} •
                        <strong> Progress:</strong> {students.filter(s => s.is_completed).length}/{students.length} students completed •
                        <Button
                          variant="outline"
                          size="sm"
                          className="ml-2 h-6 px-2 text-xs"
                          onClick={() => {
                            setEditMode(false);
                            loadSequentialIASession();
                          }}
                        >
                          Exit Edit Mode
                        </Button>
                      </>
                    ) : (
                      <>
                        <strong>Current Phase:</strong> {allPhases.find(p => p.phase === currentPhase)?.name} •
                        <strong> Max Marks:</strong> {allPhases.find(p => p.phase === currentPhase)?.maxMarks} •
                        <strong> Progress:</strong> {students.filter(s => s.is_completed).length}/{students.length} students completed
                      </>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              {currentPhase === 'COMPLETED' && (
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    <strong>All IA phases completed!</strong> All internal assessment marks have been successfully entered for this subject.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Current/Edit Phase Marks Entry */}
          {(currentPhase !== 'COMPLETED' || editMode) && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <ClipboardCheck className="h-5 w-5" />
                      {editMode
                        ? `Edit ${allPhases.find(p => p.phase === selectedPhaseForEdit)?.name}`
                        : `${allPhases.find(p => p.phase === currentPhase)?.name} Entry`
                      }
                    </CardTitle>
                    <CardDescription>
                      {editMode
                        ? `Edit marks for ${allPhases.find(p => p.phase === selectedPhaseForEdit)?.name} (Max: ${allPhases.find(p => p.phase === selectedPhaseForEdit)?.maxMarks} marks)`
                        : `Enter marks for ${allPhases.find(p => p.phase === currentPhase)?.name} (Max: ${allPhases.find(p => p.phase === currentPhase)?.maxMarks} marks)`
                      }
                    </CardDescription>
                  </div>
                  <Button
                    onClick={handleSaveCurrentPhase}
                    disabled={saving || students.filter(s => s.current_marks !== undefined && s.current_marks !== null).length === 0}
                    className="min-w-[120px]"
                  >
                    {saving ? (
                      <div className="flex items-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        {editMode ? 'Updating...' : 'Saving...'}
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Save className="h-4 w-4" />
                        {editMode
                          ? `Update ${selectedPhaseForEdit}`
                          : `Save ${currentPhase}`
                        }
                      </div>
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium">S.No</th>
                        <th className="text-left p-3 font-medium">USN</th>
                        <th className="text-left p-3 font-medium">Student Name</th>
                        <th className={`text-center p-3 font-medium ${editMode ? 'bg-purple-50' : 'bg-blue-50'}`}>
                          {editMode
                            ? `${allPhases.find(p => p.phase === selectedPhaseForEdit)?.name} (${allPhases.find(p => p.phase === selectedPhaseForEdit)?.maxMarks})`
                            : `${allPhases.find(p => p.phase === currentPhase)?.name} (${allPhases.find(p => p.phase === currentPhase)?.maxMarks})`
                          }
                        </th>
                        <th className="text-center p-3 font-medium">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {students.map((student, index) => (
                        <tr key={student.id} className="border-b hover:bg-gray-50">
                          <td className="p-3">{index + 1}</td>
                          <td className="p-3 font-mono">{student.usn}</td>
                          <td className="p-3">{student.student_name}</td>
                          <td className={`p-3 text-center ${editMode ? 'bg-purple-50' : 'bg-blue-50'}`}>
                            <Input
                              type="number"
                              min="0"
                              max={editMode
                                ? allPhases.find(p => p.phase === selectedPhaseForEdit)?.maxMarks || 25
                                : allPhases.find(p => p.phase === currentPhase)?.maxMarks || 25
                              }
                              step="0.5"
                              value={student.current_marks || ''}
                              onChange={(e) => handleMarksChange(student.id, e.target.value)}
                              className="w-20 text-center"
                              placeholder="0"
                            />
                          </td>
                          <td className="p-3 text-center">
                            {student.is_completed ? (
                              <Badge variant="outline" className="text-green-600 border-green-600">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Done
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-orange-600 border-orange-600">
                                <Clock className="h-3 w-3 mr-1" />
                                Pending
                              </Badge>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Entry Summary */}
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Total Students:</span>
                      <span className="ml-2">{students.length}</span>
                    </div>
                    <div>
                      <span className="font-medium">Completed:</span>
                      <span className="ml-2 text-green-600 font-semibold">
                        {students.filter(s => s.is_completed).length}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium">Pending:</span>
                      <span className="ml-2 text-orange-600 font-semibold">
                        {students.filter(s => !s.is_completed).length}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium">Progress:</span>
                      <span className="ml-2 text-blue-600 font-semibold">
                        {Math.round((students.filter(s => s.is_completed).length / students.length) * 100)}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* Phase Completion Alert */}
                {students.filter(s => s.is_completed).length === students.length && students.length > 0 && (
                  <Alert className="mt-4 border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      <strong>Phase Ready for Completion!</strong> All students have {currentPhase} marks entered.
                      Click "Save {currentPhase}" to complete this phase and unlock the next one.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}
