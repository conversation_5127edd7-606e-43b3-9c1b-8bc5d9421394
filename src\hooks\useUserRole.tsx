import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";

export const useUserRole = () => {
  const { user } = useAuth();
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUserRole() {
      if (user?.id) {
        try {
          // Fetch roles from employee_details table
          const { data, error } = await supabase
            .from('employee_details')
            .select('roles')
            .eq('id', user.id)
            .single();

          if (data && !error && data.roles) {
            setUserRoles(data.roles);
          } else {
            console.error('Error fetching user roles:', error);
            setUserRoles([]);
          }
        } catch (error) {
          console.error('Error fetching user roles:', error);
          setUserRoles([]);
        }
      } else {
        setUserRoles([]);
      }
      setLoading(false);
    }

    fetchUserRole();
  }, [user]);

  // For backward compatibility, return the first role as userRole
  const userRole = userRoles.length > 0 ? userRoles[0] : null;

  const isCollegeAdmin = userRoles.includes('college_admin');
  const isTimetableCoordinator = userRoles.includes('timetable_coordinator');
  const isFaculty = userRoles.includes('faculty');
  const isClassTeacher = userRoles.includes('class_teacher');
  const isHOD = userRoles.includes('hod');
  const isPrincipal = userRoles.includes('principal');
  const hasRole = (role: string) => userRoles.includes(role);
  const hasAnyRole = (roles: string[]) => roles.some(role => userRoles.includes(role));

  return {
    userRole, // For backward compatibility
    userRoles, // New: array of all roles
    loading,
    isCollegeAdmin,
    isTimetableCoordinator,
    isFaculty,
    isClassTeacher,
    isHOD,
    isPrincipal,
    hasRole,
    hasAnyRole,
  };
};
