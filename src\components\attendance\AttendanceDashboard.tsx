
import React from 'react';
import { format } from "date-fns";
import { Calendar as CalendarIcon, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import DataTable from "@/components/ui/DataTable";
import AttendanceCharts from './AttendanceCharts';
import { Input } from "@/components/ui/input";

interface StudentAttendanceSummary {
  name: string;
  present: number;
  absent: number;
  percentage: number;
}

interface DailyAttendanceEntry {
  date: string;
  present: number;
  absent: number;
}

interface OverallAttendanceEntry {
  name: string;
  value: number;
}

interface ChartData {
  dailyData: DailyAttendanceEntry[];
  overallData: OverallAttendanceEntry[];
}

interface AttendanceDashboardProps {
  chartData: ChartData;
  attendanceSummary: StudentAttendanceSummary[];
  filteredAttendanceSummary: StudentAttendanceSummary[];
  dashboardView: string;
  setDashboardView: (view: string) => void;
  dateRange: { from: Date; to: Date };
  setDateRange: React.Dispatch<React.SetStateAction<{ from: Date; to: Date }>>;
  attendanceFilter: {
    enabled: boolean;
    type: "greater" | "less";
    value: number;
  };
  setAttendanceFilter: React.Dispatch<React.SetStateAction<{
    enabled: boolean;
    type: "greater" | "less";
    value: number;
  }>>;
  exportAttendance: (format: 'csv' | 'excel' | 'pdf') => void;
}

const AttendanceDashboard: React.FC<AttendanceDashboardProps> = ({
  chartData,
  attendanceSummary,
  filteredAttendanceSummary,
  dashboardView,
  setDashboardView,
  dateRange,
  setDateRange,
  attendanceFilter,
  setAttendanceFilter,
  exportAttendance
}) => {
  const dashboardColumns = [
    {
      id: "name",
      header: "Student Name",
      accessorKey: "name",
    },
    {
      id: "present",
      header: "Present",
      accessorKey: "present",
      cell: (info: any) => (
        <div className="text-center font-medium text-green-600">
          {info.row.original.present || 0}
        </div>
      )
    },
    {
      id: "absent",
      header: "Absent",
      accessorKey: "absent",
      cell: (info: any) => (
        <div className="text-center font-medium text-red-600">
          {info.row.original.absent || 0}
        </div>
      )
    },
    {
      id: "percentage",
      header: "Attendance %",
      accessorKey: "percentage",
      cell: (info: any) => {
        const percentage = info.row.original.percentage || 0;
        return (
          <div className={`text-center font-medium ${
            percentage >= 75 ? 'text-green-600' : 'text-red-600'
          }`}>
            {percentage.toFixed(1)}%
          </div>
        );
      }
    },
  ];

  return (
    <div className="space-y-6">
      <div className="hidden md:grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="col-span-1 md:col-span-2">
          <Tabs 
            defaultValue="weekly" 
            value={dashboardView}
            onValueChange={setDashboardView}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
              <TabsTrigger value="semester">Semester</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
      
      <div className="block md:hidden space-y-4">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-start text-left font-normal"
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateRange.from && dateRange.to ? (
                <>
                  {format(dateRange.from, "PPP")} - {format(dateRange.to, "PPP")}
                </>
              ) : (
                <span>Select date range</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="range"
              selected={dateRange}
              onSelect={(range) => range && setDateRange(range as { from: Date; to: Date })}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>
      
      <div className="hidden md:flex items-center space-x-4">
        <div className="grid grid-cols-2 gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date-from"
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  format(dateRange.from, "PPP")
                ) : (
                  <span>From date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dateRange.from}
                onSelect={(date) => date && setDateRange(prev => ({ ...prev, from: date }))}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date-to"
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.to ? (
                  format(dateRange.to, "PPP")
                ) : (
                  <span>To date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dateRange.to}
                onSelect={(date) => date && setDateRange(prev => ({ ...prev, to: date }))}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="text-sm text-muted-foreground">Filter by:</div>
          <Select 
            value={attendanceFilter.type} 
            onValueChange={(val: "greater" | "less") => setAttendanceFilter(prev => ({...prev, type: val}))}
            disabled={!attendanceFilter.enabled}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="greater">Greater than or equal</SelectItem>
              <SelectItem value="less">Less than</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex items-center space-x-2">
            <Input 
              type="number" 
              min="0" 
              max="100"
              value={attendanceFilter.value}
              onChange={(e) => setAttendanceFilter(prev => ({...prev, value: parseInt(e.target.value)}))}
              disabled={!attendanceFilter.enabled}
              className="w-20"
            />
            <span>%</span>
          </div>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setAttendanceFilter(prev => ({...prev, enabled: !prev.enabled}))}
          >
            {attendanceFilter.enabled ? 'Disable Filter' : 'Enable Filter'}
          </Button>
        </div>
      </div>

      <AttendanceCharts chartData={chartData} />

      <div className="bg-white dark:bg-card rounded-md border shadow">
        <div className="p-4 border-b">
          <h3 className="text-lg font-medium">Student Attendance Details</h3>
          {attendanceFilter.enabled && (
            <p className="text-sm text-muted-foreground">
              Filtered by: {attendanceFilter.type === 'greater' ? '>=' : '<'} {attendanceFilter.value}%
            </p>
          )}
        </div>
        <DataTable 
          columns={dashboardColumns} 
          data={filteredAttendanceSummary}
        />
      </div>
      
      <div className="flex justify-end space-x-2">
        <Button 
          variant="outline" 
          onClick={() => exportAttendance('csv')}
          className="flex items-center"
        >
          <FileText className="h-4 w-4 mr-2" />
          Export CSV
        </Button>
        <Button 
          variant="outline" 
          onClick={() => exportAttendance('excel')}
          className="flex items-center"
        >
          <FileText className="h-4 w-4 mr-2" />
          Export Excel
        </Button>
        <Button 
          variant="outline" 
          onClick={() => exportAttendance('pdf')}
          className="flex items-center"
        >
          <FileText className="h-4 w-4 mr-2" />
          Export PDF
        </Button>
      </div>
    </div>
  );
};

export default AttendanceDashboard;
