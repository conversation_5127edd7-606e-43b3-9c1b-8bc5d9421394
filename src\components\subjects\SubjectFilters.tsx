
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent
} from "@/components/ui/card";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

type FilterOption = { id: string; name: string };

export interface SubjectFiltersProps {
  year: string;
  dept: string;
  sem: string;
  section: string;
  setYear: (value: string) => void;
  setDept: (value: string) => void;
  setSem: (value: string) => void;
  setSection: (value: string) => void;
  yearsList: string[];
  deptsList: FilterOption[];
  semsList: string[];
  sectionsList: string[];
}

const SubjectFilters: React.FC<SubjectFiltersProps> = ({
  year, dept, sem, section,
  setYear, setDept, setSem, setSection,
  yearsList, deptsList, semsList, sectionsList
}) => {
  return (
    <Card>
      <CardHeader><CardTitle>Step 1: Select Filters</CardTitle></CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[
          { label: "Academic Year", value: year, onChange: setYear, options: yearsList },
          { label: "Department", value: dept, onChange: setDept, options: deptsList.map(d => d.id), map: deptsList },
          { label: "Semester", value: sem, onChange: setSem, options: semsList },
          { label: "Section", value: section, onChange: setSection, options: sectionsList },
        ].map((f, i) => (
          <div key={i}>
            <Label>{f.label}</Label>
            <Select value={f.value} onValueChange={f.onChange}>
              <SelectTrigger><SelectValue placeholder={`Select ${f.label}`} /></SelectTrigger>
              <SelectContent>
                {f.options.map(opt => (
                  <SelectItem key={opt} value={opt}>
                    {f.map?.find((d: any) => d.id === opt)?.name || opt}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default SubjectFilters;
