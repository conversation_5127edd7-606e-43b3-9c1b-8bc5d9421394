import React, { useState, useEffect, useRef } from 'react';
import { Download, <PERSON><PERSON><PERSON>, AlertTriangle, AlertCircle, BookOpen, Trash2, Clock, Beaker, Save, Calendar, Users, Settings, Play, Eye, CheckCircle } from "lucide-react";
import { TimetableGrid } from "@/components/timetable/TimetableGrid";
import { TimetableService, TimeStructure } from "@/services/TimetableService";
import { TimetableGeneratorService } from "@/services/TimetableGeneratorService";
import { FacultyAvailabilityService } from "@/services/FacultyAvailabilityService";
import { TimetableExportService, ExportData } from "@/services/TimetableExportService";
import { useLocation, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { useSubjectMappingStore } from "@/stores/SubjectMappingStore";
import TimeStructureForm from "@/components/timetable/TimeStructureForm";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Alert,
  AlertTitle,
  AlertDescription
} from "@/components/ui/alert";
import { useFacultyLoader } from "@/components/subjects/useFacultyLoader";
import { FacultyAutocomplete } from "@/components/subjects/FacultyAutocomplete";
import { ModernLayout, ModernGrid, ModernCard, ModernButtonGroup } from "@/components/layout/ModernLayout";
import { ModernLoading } from "@/components/ui/modern-loading";



// Import AlertDialog components
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";



const Timetable = () => {
  const location = useLocation();
  const locationState = location.state as {
    academicYear?: string;
    department?: string;
    semester?: string;
    section?: string;
  } | null;

  const [activeTab, setActiveTab] = useState("timetable-grid");
  const [department, setDepartment] = useState(locationState?.department || "cse");
  const [semester, setSemester] = useState(locationState?.semester || "1");
  const [section, setSection] = useState(locationState?.section || "A");
  const [academicYear, setAcademicYear] = useState(locationState?.academicYear || "2024-2025");
  const [view, setView] = useState<"class" | "faculty">("class");
  const [selectedFaculty, setSelectedFaculty] = useState("");
  const [showTimeStructureForm, setShowTimeStructureForm] = useState(false);
  const [labSlotsGenerated, setLabSlotsGenerated] = useState(false);
  const [theorySlotsGenerated, setTheorySlotsGenerated] = useState(false);



  useEffect(() => {
    console.log("Location state:", locationState);
    if (locationState) {
      console.log("Using parameters from Subject Allotment:", {
        academicYear: locationState.academicYear,
        department: locationState.department,
        semester: locationState.semester,
        section: locationState.section
      });
    }
  }, [locationState]);

  const [isGenerating, setIsGenerating] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [isGeneratingLabSlots, setIsGeneratingLabSlots] = useState(false);
  const [isGeneratingTheorySlots, setIsGeneratingTheorySlots] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [openOptimizeDialog, setOpenOptimizeDialog] = useState(false);
  const [conflicts, setConflicts] = useState<{ description: string; severity: "high" | "medium" | "low" }[]>([]);
  const [suggestions, setSuggestions] = useState<{ description: string; type: "faculty" | "timing" }[]>([]);
  const [hasTimetable, setHasTimetable] = useState(false);
  const [subjectMappingsCount, setSubjectMappingsCount] = useState(0);
  const [labMappingsCount, setLabMappingsCount] = useState(0);
  const [theoryMappingsCount, setTheoryMappingsCount] = useState(0);
  const [savedTimeStructure, setSavedTimeStructure] = useState<TimeStructure | null>(null);
  const [validationDialogOpen, setValidationDialogOpen] = useState(false);
  const [validationMessages, setValidationMessages] = useState<string[]>([]);

  const subjectMappings = useSubjectMappingStore(state => state.mappings);
  const setStoreMappings = useSubjectMappingStore(state => state.setMappings);

  const timetableGridRef = useRef<any>(null);
  const { toast } = useToast();

  const departments = [
    { id: "cse", name: "Computer Science" },
    { id: "it", name: "Information Technology" },
    { id: "ece", name: "Electronics & Communication" },
    { id: "eee", name: "Electrical & Electronics" },
    { id: "mech", name: "Mechanical Engineering" },
  ];

  const semesters = ["1", "2", "3", "4", "5", "6", "7", "8"];
  const sections = ["A", "B", "C", "D"];
  const academicYears = ["2022-2023", "2023-2024", "2024-2025", "2025-2026"];

  // Load all faculty members
  const allFacultyMembers = useFacultyLoader();

  // Replace the existing facultyOptions with this implementation
  const facultyOptions = React.useMemo(() => {
    // Use the complete faculty list from useFacultyLoader
    if (allFacultyMembers && allFacultyMembers.length > 0) {
      return allFacultyMembers.map(faculty => ({
        id: faculty.id,
        name: faculty.name
      })).sort((a, b) => a.name.localeCompare(b.name));
    }

    // Fallback to the existing implementation if useFacultyLoader returns empty
    const map = new Map<string, string>();
    subjectMappings.forEach(mapping => {
      if (mapping.faculty?.id && mapping.faculty.name) {
        map.set(mapping.faculty.id, mapping.faculty.name);
      }
      if (mapping.faculty2Id && mapping.faculty2Name) {
        map.set(mapping.faculty2Id, mapping.faculty2Name);
      }
    });
    return Array.from(map.entries()).map(([id, name]) => ({ id, name }));
  }, [allFacultyMembers, subjectMappings]);

  useEffect(() => {
    if (view === "faculty") {
      if (facultyOptions.length > 0) {
        // Only set default faculty if none is selected or the selected one doesn't exist
        if (!selectedFaculty || !facultyOptions.find(f => f.id === selectedFaculty)) {
          const firstFacultyId = facultyOptions[0]?.id;
          console.log("Setting default faculty ID:", firstFacultyId);
          setSelectedFaculty(firstFacultyId);
        }
      } else {
        console.log("No faculty options available");
      }
    }
  }, [view, facultyOptions, selectedFaculty]);

  // Add this debugging function
  useEffect(() => {
    if (facultyOptions.length > 0) {
      console.log("FACULTY OPTIONS:", facultyOptions);
    }

    if (selectedFaculty) {
      console.log("SELECTED FACULTY:", selectedFaculty);
      console.log("SELECTED FACULTY DETAILS:", facultyOptions.find(f => f.id === selectedFaculty));
    }
  }, [facultyOptions, selectedFaculty]);

  // Add this function to handle faculty selection
  const handleFacultyChange = (value: string) => {
    setSelectedFaculty(value);

    // If we're switching to a faculty view, make sure the view is set to 'faculty'
    if (value && activeTab === "faculty-timetable") {
      setView("faculty");
    }
  };

  useEffect(() => {
    const loadTimeStructure = async () => {
      if (department && academicYear) {
        try {
          console.log("Loading time structure in Timetable component");
          const structure = await TimetableService.fetchTimeStructure({
            academicYear,
            department
          });

          setSavedTimeStructure(structure);

          if (!structure) {
            console.log("No time structure found for", academicYear, department);
            toast({
              title: "Time Structure Missing",
              description: "Please define a time structure in the Time Structure tab before generating slots.",
              variant: "destructive"
            });
          } else {
            console.log("Loaded time structure:", structure);
            toast({
              title: "Time Structure Loaded",
              description: "Time structure loaded successfully. You can now generate timetable slots."
            });
          }
        } catch (error) {
          console.error("Error loading time structure:", error);
        }
      }
    };

    loadTimeStructure();
  }, [department, academicYear]);

  useEffect(() => {
    const loadMappingsFromDatabase = async () => {
      if (!academicYear || !department || !semester || !section) return;

      try {
        console.log("Loading mappings from database for:", {
          academicYear, department, semester, section
        });

        // Try the simplified table first
        const { data: simplifiedData, error: simplifiedError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            id,
            academic_year,
            department,
            semester,
            section,
            subject_id,
            subject_code,
            subject_name,
            subject_type,
            faculty_1_id,
            faculty_2_id,
            hours_per_week,
            classroom,
            slots_per_week,
            subjects(subject_short_id)
          `)
          .eq('academic_year', academicYear)
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section)
          .not('subject_code', 'like', 'DELETED_%');

        // If simplified table query fails or returns no data, try the original table
        let mappingsData;
        let error;

        if (simplifiedError || !simplifiedData || simplifiedData.length === 0) {
          console.log("No mappings found in simplified table, trying original table");

          // Fall back to the original table
          const { data: originalData, error: originalError } = await supabase
            .from('subject_faculty_mappings')
            .select(`
              id,
              academic_year,
              department,
              semester,
              section,
              subject_id,
              subject_code,
              subject_name,
              subject_type,
              faculty_1_id,
              faculty_2_id,
              hours_per_week,
              classroom,
              slots_per_week,
              subjects(subject_short_id)
            `)
            .eq('academic_year', academicYear)
            .eq('department', department)
            .eq('semester', semester)
            .eq('section', section);

          mappingsData = originalData;
          error = originalError;
        } else {
          console.log(`Found ${simplifiedData.length} mappings in simplified table`);
          mappingsData = simplifiedData;
          error = simplifiedError;
        }

        if (error) throw error;

        const safeData = Array.isArray(mappingsData) ? mappingsData : [];
        console.log("Raw mappings data from database:", safeData);

        const labSlots: Record<string, any[]> = {};
        const labMappings = safeData.filter(m =>
          m.subject_type === 'laboratory' || m.subject_type === 'lab'
        );

        if (labMappings && labMappings.length > 0) {
          const { data: labSlotsData, error: labSlotsError } = await supabase
            .from('lab_time_slots')
            .select('*')
            .in('mapping_id', labMappings.map(m => m.id));

          if (labSlotsError) throw labSlotsError;

          if (labSlotsData && Array.isArray(labSlotsData)) {
            labSlotsData.forEach(slot => {
              if (!labSlots[slot.mapping_id]) labSlots[slot.mapping_id] = [];
              labSlots[slot.mapping_id].push({
                day: slot.day || 'Monday',
                timeOfDay: slot.time_of_day || 'Morning',
                batch: slot.batch_name || 'A',
              });
            });
          }
        }

        const facultyIds = new Set<string>();
        safeData.forEach(mapping => {
          if (mapping.faculty_1_id) facultyIds.add(mapping.faculty_1_id);
          if (mapping.faculty_2_id) facultyIds.add(mapping.faculty_2_id);
        });

        const { data: facultyData, error: facultyError } = await supabase
          .from('employee_details')
          .select('id, full_name')
          .in('id', Array.from(facultyIds));

        if (facultyError) throw facultyError;

        const facultyMap: Record<string, string> = {};
        if (facultyData && Array.isArray(facultyData)) {
          facultyData.forEach(faculty => {
            facultyMap[faculty.id] = faculty.full_name;
          });
        }

        const processedMappings = safeData.map(mapping => {
          const faculty1Details = { id: mapping.faculty_1_id, name: facultyMap[mapping.faculty_1_id] || 'Unknown' };
          const faculty2Details = mapping.faculty_2_id ? { id: mapping.faculty_2_id, name: facultyMap[mapping.faculty_2_id] || 'Unknown' } : undefined;

          const subjectType = (): "theory" | "lab" | "elective" | "laboratory" => {
            const type = mapping.subject_type?.toLowerCase();
            if (type === "theory") return "theory";
            if (type === "laboratory" || type === "lab") return "laboratory";
            if (type === "elective") return "elective";
            return "theory";
          };

          return {
            id: mapping.id,
            academicYear: mapping.academic_year,
            department: mapping.department,
            semester: mapping.semester,
            section: mapping.section,
            subject: {
              id: mapping.subject_id,
              code: mapping.subject_code,
              name: mapping.subject_name,
              type: subjectType(),
              shortId: mapping.subjects?.subject_short_id || mapping.subject_code?.substring(0, 3).toUpperCase()
            },
            subject_type: mapping.subject_type,
            faculty: faculty1Details,
            faculty2Id: mapping.faculty_2_id || undefined,
            faculty2Name: faculty2Details?.name || undefined,
            hoursPerWeek: mapping.hours_per_week || 0,
            classroom: mapping.classroom || '',
            slotsPerWeek: mapping.slots_per_week || undefined,
            labSlots: labSlots[mapping.id] || [],
          };
        });

        setStoreMappings(processedMappings);
        console.log("Updated store with mappings:", processedMappings);

        setSubjectMappingsCount(processedMappings.length);

        const labMappingsCount = processedMappings.filter(mapping =>
          mapping.subject.type === "laboratory" ||
          mapping.subject.type === "lab"
        ).length;
        setLabMappingsCount(labMappingsCount);

        const theoryMappingsCount = processedMappings.filter(mapping =>
          mapping.subject.type === "theory"
        ).length;
        setTheoryMappingsCount(theoryMappingsCount);

        console.log("Mapping counts:", {
          total: processedMappings.length,
          lab: labMappingsCount,
          theory: theoryMappingsCount
        });
      } catch (error) {
        console.error("Error loading mappings from database:", error);
        toast({
          title: "Error Loading Mappings",
          description: "Failed to load subject mappings from the database.",
          variant: "destructive"
        });
      }
    };

    loadMappingsFromDatabase();
  }, [academicYear, department, semester, section, setStoreMappings, toast]);

  useEffect(() => {
    const checkTimetable = async () => {
      if (department && semester && section && academicYear) {
        try {
          const slots = await TimetableService.fetchTimetable({
            academicYear,
            department,
            semester,
            section
          });

          setHasTimetable(slots.length > 0);

          // Check if we have lab and theory slots
          const labSlots = slots.filter(slot => slot.subject_type === 'lab');
          const theorySlots = slots.filter(slot => slot.subject_type === 'theory');

          setLabSlotsGenerated(labSlots.length > 0);
          setTheorySlotsGenerated(theorySlots.length > 0);

          console.log("Timetable slots found:", slots.length,
            `(${labSlots.length} lab slots, ${theorySlots.length} theory slots)`);
        } catch (error) {
          console.error("Error checking timetable:", error);
          setHasTimetable(false);
          setLabSlotsGenerated(false);
          setTheorySlotsGenerated(false);
        }
      }
    };

    checkTimetable();
  }, [department, semester, section, academicYear]);

  const handleTimeStructureSave = async (timeStructure: TimeStructure) => {
    setSavedTimeStructure(timeStructure);
    setActiveTab("timetable-grid");

    // Initialize faculty availability with the new time structure
    try {
      await FacultyAvailabilityService.initializeFacultyAvailability(timeStructure, academicYear);
      console.log("Faculty availability initialized successfully");
      toast({
        title: "Faculty Availability Initialized",
        description: "Faculty availability has been initialized based on the time structure.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error initializing faculty availability:", error);
      toast({
        title: "Error",
        description: "Failed to initialize faculty availability.",
        variant: "destructive"
      });
    }

    if (timetableGridRef.current) {
      timetableGridRef.current.clearAndLoadData();
    }
  };

  const handleGenerateLabSlots = async () => {
    if (!savedTimeStructure) {
      toast({
        title: "Missing Time Structure",
        description: "Please define and save time structure first in the Time Structure tab.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingLabSlots(true);

    try {
      if (timetableGridRef.current) {
        await timetableGridRef.current.fetchAndDisplayLabSlots();
        setLabSlotsGenerated(true);
        setTheorySlotsGenerated(false); // Reset theory slots status when generating new lab slots
      } else {
        toast({
          title: "Error",
          description: "Could not access the timetable grid.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error generating lab slots:", error);
      toast({
        title: "Error",
        description: "Failed to generate lab slots.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingLabSlots(false);
    }
  };

  const handleGenerateTheorySlots = async () => {
    if (!savedTimeStructure) {
      toast({
        title: "Missing Time Structure",
        description: "Please define and save time structure first in the Time Structure tab.",
        variant: "destructive"
      });
      return;
    }

    if (!labSlotsGenerated) {
      toast({
        title: "Lab Slots Required",
        description: "Please generate lab slots first before generating theory slots.",
        variant: "destructive"
      });
      return;
    }

    setIsGeneratingTheorySlots(true);

    try {
      if (timetableGridRef.current) {
        await timetableGridRef.current.fetchAndDisplayTheorySlots();
        setTheorySlotsGenerated(true);
      } else {
        toast({
          title: "Error",
          description: "Could not access the timetable grid.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error generating theory slots:", error);
      toast({
        title: "Error",
        description: "Failed to generate theory slots.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingTheorySlots(false);
    }
  };

  const handleGenerateTimetable = async () => {
    if (subjectMappingsCount === 0) {
      toast({
        title: "No Subject Mappings",
        description: "Please create subject mappings first before generating a timetable.",
        variant: "destructive"
      });
      return;
    }

    if (!savedTimeStructure) {
      toast({
        title: "Missing Time Structure",
        description: "Please define and save time structure first in the Time Structure tab.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      const timetableGenerator = new TimetableGeneratorService();

      const generatedTimetable = await timetableGenerator.generateTimetable(
        subjectMappings,
        {
          academicYear,
          department,
          semester,
          section,
          constraints: {
            noConsecutiveClasses: true,
            maxOneClassPerDay: true,
            respectLabSlots: true
          }
        }
      );

      if (generatedTimetable.length > 0) {
        if (timetableGridRef.current) {
          timetableGridRef.current.updateTimetableData(generatedTimetable);
        }

        // Uncomment this when validateTimetable is implemented
        // const validation = timetableGenerator.validateTimetable(generatedTimetable);
        // if (!validation.isValid) {
        //   setValidationMessages(validation.violations);
        //   setValidationDialogOpen(true);
        // }

        toast({
          title: "Timetable Generated",
          description: `Generated ${generatedTimetable.length} timetable slots from subject mappings.`
        });
      } else {
        toast({
          title: "Could Not Generate Timetable",
          description: "The algorithm couldn't allocate slots while respecting all constraints. Try again or reduce constraints.",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      console.error("Error generating timetable:", error);
      toast({
        title: "Error Generating Timetable",
        description: error.message || "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleOptimizeTimetable = () => {
    if (!savedTimeStructure) {
      toast({
        title: "Missing Time Structure",
        description: "Please define time structure first in the Time Structure tab before optimizing.",
        variant: "destructive"
      });
      return;
    }

    setIsOptimizing(true);

    setTimeout(() => {
      setIsOptimizing(false);

      setConflicts([
        {
          description: "Dr. Smith has overlapping classes on Monday at 10:00 AM",
          severity: "high"
        },
        {
          description: "CS104 Lab requires consecutive timeslots but is currently split",
          severity: "medium"
        },
        {
          description: "Too many theory classes scheduled back-to-back for Section A",
          severity: "low"
        }
      ]);

      setSuggestions([
        {
          description: "Replace Dr. Smith with Dr. Johnson for CS101 on Monday",
          type: "faculty"
        },
        {
          description: "Move CS104 Lab to Wednesday 2:00-4:00 PM consecutive slots",
          type: "timing"
        },
        {
          description: "Alternate theory and lab sessions for better learning outcomes",
          type: "timing"
        }
      ]);

      setOpenOptimizeDialog(true);
    }, 2000);
  };

  const applyOptimizations = () => {
    const optimizedTimetableData = [
      { id: "mon-1", day: "Monday", time_slot: "9:00-10:00", subject_code: "CS101", faculty_name: "Dr. Johnson", subject_type: "theory" },
      { id: "mon-2", day: "Monday", time_slot: "10:00-11:00", subject_code: "CS102", faculty_name: "Dr. Johnson", subject_type: "theory" },
      { id: "tue-3", day: "Tuesday", time_slot: "11:00-12:00", subject_code: "CS103", faculty_name: "Dr. Davis", subject_type: "theory" },
      { id: "wed-4", day: "Wednesday", time_slot: "14:00-15:00", subject_code: "CS104 Lab", faculty_name: "Dr. Wilson", subject_type: "lab" },
      { id: "wed-5", day: "Wednesday", time_slot: "15:00-16:00", subject_code: "CS104 Lab", faculty_name: "Dr. Wilson", subject_type: "lab" },
      { id: "thu-6", day: "Thursday", time_slot: "9:00-10:00", subject_code: "CS105", faculty_name: "Dr. Smith", subject_type: "theory" },
      { id: "fri-7", day: "Friday", time_slot: "10:00-11:00", subject_code: "CS106", faculty_name: "Dr. Smith", subject_type: "theory" },
    ];

    if (timetableGridRef.current) {
      timetableGridRef.current.updateTimetableData(optimizedTimetableData);
    }

    toast({
      title: "Optimizations Applied",
      description: "AI-suggested optimizations have been applied to the timetable."
    });

    setOpenOptimizeDialog(false);
    setConflicts([]);
    setSuggestions([]);
  };

  const handleExport = async (format: "pdf" | "excel") => {
    if (!savedTimeStructure) {
      toast({
        title: "Missing Time Structure",
        description: "Please define time structure first before exporting.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Fetch current timetable data
      const timetableData = await TimetableService.fetchTimetable({
        academicYear,
        department,
        semester,
        section
      });

      console.log('Fetched timetable data for export:', {
        count: timetableData.length,
        sampleData: timetableData.slice(0, 3),
        allTimeSlots: [...new Set(timetableData.map(slot => slot.time_slot))],
        allDays: [...new Set(timetableData.map(slot => slot.day))]
      });

      if (timetableData.length === 0) {
        toast({
          title: "No Timetable Data",
          description: "There is no timetable data to export. Please generate a timetable first.",
          variant: "destructive"
        });
        return;
      }

      // Prepare export data
      const exportData: ExportData = {
        timetableData,
        timeStructure: savedTimeStructure,
        metadata: {
          academicYear,
          department,
          semester,
          section,
          view: 'class'
        }
      };

      // Show loading toast
      toast({
        title: `Exporting as ${format.toUpperCase()}`,
        description: "Your timetable is being prepared for download..."
      });

      // Export based on format
      if (format === 'pdf') {
        await TimetableExportService.exportToPDF(exportData);
      } else {
        await TimetableExportService.exportToExcel(exportData);
      }

      // Show success toast
      toast({
        title: "Export Successful",
        description: `Timetable has been exported as ${format.toUpperCase()} and downloaded.`,
        variant: "default"
      });

    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export Failed",
        description: "Failed to export timetable. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleFacultyExport = async (format: "pdf" | "excel") => {
    if (!savedTimeStructure) {
      toast({
        title: "Missing Time Structure",
        description: "Please define time structure first before exporting.",
        variant: "destructive"
      });
      return;
    }

    if (!selectedFaculty) {
      toast({
        title: "No Faculty Selected",
        description: "Please select a faculty member to export their timetable.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Get faculty name
      const facultyName = facultyOptions.find(f => f.id === selectedFaculty)?.name || 'Unknown Faculty';

      // Fetch faculty timetable data using the RPC function (with fallback)
      let facultyTimetableData: any[] = [];

      try {
        const { data, error } = await supabase.rpc('get_faculty_timetable_by_name', {
          p_faculty_name: facultyName,
          p_academic_year: academicYear
        });

        if (error) {
          console.error("RPC error:", error);
          console.log("Falling back to direct faculty_timetables query...");

          // Fall back to direct query from faculty_timetables table
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('faculty_timetables')
            .select(`
              id,
              day,
              time_slot,
              subject_code,
              subject_short_id,
              subject_name,
              subject_type,
              semester,
              section,
              department,
              room_number
            `)
            .eq('academic_year', academicYear)
            .filter('faculty_id', 'in', `(
              select id from employee_details where full_name = '${facultyName}'
            )`)
            .order('day')
            .order('time_slot');

          if (fallbackError) {
            console.error("Fallback query error:", fallbackError);
            toast({
              title: "Export Failed",
              description: "Failed to fetch faculty timetable data.",
              variant: "destructive"
            });
            return;
          }

          facultyTimetableData = fallbackData || [];
          console.log(`Fallback query returned ${facultyTimetableData.length} slots`);

          // Log the subjects found in fallback data
          if (facultyTimetableData.length > 0) {
            const subjects = [...new Set(facultyTimetableData.map(slot => slot.subject_code))];
            console.log(`Fallback subjects found: ${subjects.join(', ')}`);
          }
        } else {
          facultyTimetableData = data || [];
          console.log(`RPC returned ${facultyTimetableData.length} slots`);

          // Log the subjects found in RPC data
          if (facultyTimetableData.length > 0) {
            const subjects = [...new Set(facultyTimetableData.map(slot => slot.subject_code))];
            console.log(`RPC subjects found: ${subjects.join(', ')}`);
          }
        }
      } catch (error) {
        console.error("Error fetching faculty timetable:", error);
        toast({
          title: "Export Failed",
          description: "Failed to fetch faculty timetable data.",
          variant: "destructive"
        });
        return;
      }

      if (!facultyTimetableData || facultyTimetableData.length === 0) {
        toast({
          title: "No Timetable Data",
          description: `No timetable data found for ${facultyName}. Please ensure the faculty has assigned slots.`,
          variant: "destructive"
        });
        return;
      }

      // Convert faculty timetable data to TimetableSlot format
      const timetableData = facultyTimetableData.map((slot: any) => ({
        id: slot.id,
        day: slot.day,
        time_slot: slot.time_slot,
        subject_code: slot.subject_code,
        subject_short_id: slot.subject_short_id || slot.subject_code, // Use actual short_id if available
        subject_name: slot.subject_name,
        subject_type: slot.subject_type,
        semester: slot.semester,
        section: slot.section,
        department: slot.department,
        room_number: slot.room_number,
        faculty_id: selectedFaculty,
        academic_year: academicYear
      }));

      // Prepare export data
      const exportData: ExportData = {
        timetableData,
        timeStructure: savedTimeStructure,
        metadata: {
          academicYear,
          department,
          facultyName,
          selectedFaculty,
          view: 'faculty'
        }
      };

      // Show loading toast
      toast({
        title: `Exporting Faculty Timetable as ${format.toUpperCase()}`,
        description: `Preparing ${facultyName}'s timetable for download...`
      });

      // Export based on format
      if (format === 'pdf') {
        await TimetableExportService.exportToPDF(exportData);
      } else {
        await TimetableExportService.exportToExcel(exportData);
      }

      // Show success toast
      toast({
        title: "Export Successful",
        description: `${facultyName}'s timetable has been exported as ${format.toUpperCase()} and downloaded.`,
        variant: "default"
      });

    } catch (error) {
      console.error("Faculty export error:", error);
      toast({
        title: "Export Failed",
        description: "Failed to export faculty timetable. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleClearTimetable = async () => {
    const confirmMessage = `Are you sure you want to clear the timetable for:
- Department: ${department.toUpperCase()}
- Semester: ${semester}
- Section: ${section}
- Academic Year: ${academicYear}

This will:
1. Delete all timetable slots from the timetable_slots table
2. Remove all entries from the class_timetables table
3. Remove all entries from the faculty_timetables table
4. Reset the timetable grid to empty state
5. Recalculate faculty availability based on remaining commitments

This action cannot be undone.`;

    if (!confirm(confirmMessage)) {
      return;
    }

    setIsClearing(true);

    try {
      if (timetableGridRef.current) {
        // Clear timetable slots and get affected faculty IDs
        const affectedFacultyIds = await TimetableService.clearTimetable({
          academicYear,
          department,
          semester,
          section
        });

        // Recalculate faculty availability based on remaining commitments
        if (savedTimeStructure && affectedFacultyIds.length > 0) {
          try {
            // Recalculate availability for affected faculty members
            await FacultyAvailabilityService.recalculateFacultyAvailabilityAfterClear(
              affectedFacultyIds,
              savedTimeStructure,
              academicYear
            );

            console.log("Faculty availability recalculated successfully");

            toast({
              title: "Faculty Availability Updated",
              description: `Recalculated availability for ${affectedFacultyIds.length} faculty members based on their remaining commitments.`,
              variant: "default"
            });
          } catch (error) {
            console.error("Error recalculating faculty availability:", error);

            // Fallback to initialize if recalculation fails
            try {
              await FacultyAvailabilityService.initializeFacultyAvailability(savedTimeStructure, academicYear);
              console.log("Faculty availability initialized as fallback");
              toast({
                title: "Faculty Availability Initialized",
                description: "Faculty availability has been initialized to default values (fallback).",
                variant: "default"
              });
            } catch (fallbackError) {
              console.error("Error in fallback initialization of faculty availability:", fallbackError);
            }
          }
        } else if (!savedTimeStructure) {
          console.error("Cannot recalculate faculty availability: No time structure available");
          toast({
            title: "Warning",
            description: "Could not recalculate faculty availability due to missing time structure.",
            variant: "warning"
          });
        } else if (affectedFacultyIds.length === 0) {
          console.log("No faculty members affected by timetable clearing");
          toast({
            title: "Information",
            description: "No faculty members were affected by this timetable clearing.",
            variant: "default"
          });
        }

        await timetableGridRef.current.clearAndLoadData();
        setHasTimetable(false);
        setLabSlotsGenerated(false);
        setTheorySlotsGenerated(false);

        toast({
          title: "Timetable Cleared",
          description: `Successfully cleared timetable for Semester ${semester}, Section ${section}`
        });
      }
    } catch (error) {
      console.error("Error clearing timetable:", error);
      toast({
        title: "Error",
        description: "Failed to clear timetable.",
        variant: "destructive"
      });
    } finally {
      setIsClearing(false);
    }
  };

  const getSeverityColor = (severity: "high" | "medium" | "low") => {
    switch (severity) {
      case "high":
        return "text-red-500 bg-red-50";
      case "medium":
        return "text-amber-500 bg-amber-50";
      case "low":
        return "text-blue-500 bg-blue-50";
    }
  };



  // Add this function to update faculty assignments
  const updateFacultyAssignments = async () => {
    try {
      await TimetableService.updateFacultyAssignments({
        academicYear,
        department,
        semester,
        section
      });

      // Reload the timetable data
      if (timetableGridRef.current) {
        timetableGridRef.current.clearAndLoadData();
      }

      toast({
        title: "Faculty Assignments Updated",
        description: "Faculty assignments have been updated successfully.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error updating faculty assignments:", error);
      toast({
        title: "Error",
        description: "Failed to update faculty assignments.",
        variant: "destructive"
      });
    }
  };

  // Call this function when loading the page
  useEffect(() => {
    if (academicYear && department && semester && section) {
      updateFacultyAssignments();
    }
  }, [academicYear, department, semester, section]);

  // Add state for save dialog
  const [isSavingTimetable, setIsSavingTimetable] = useState(false);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);

  // Add save timetable handler
  const handleSaveTimetable = async () => {
    setSaveDialogOpen(true);
  };

  // Add confirm save handler
  const handleConfirmSave = async () => {
    setIsSavingTimetable(true);
    try {
      // Fetch current timetable data
      const slots = await TimetableService.fetchTimetable({
        academicYear,
        department,
        semester,
        section
      });

      if (slots.length === 0) {
        toast({
          title: "No Timetable Data",
          description: "There is no timetable data to save.",
          variant: "destructive"
        });
        setSaveDialogOpen(false);
        return;
      }

      console.log("Saving timetable with slots:", slots.length);

      // Save to class_timetables - note the static method call
      await TimetableService.saveToClassTimetables({
        academicYear,
        department,
        semester,
        section,
        slots
      });

      // Save to faculty_timetables - note the static method call
      await TimetableService.saveToFacultyTimetables({
        academicYear,
        department,
        slots
      });

      toast({
        title: "Timetable Saved",
        description: `Saved to class and faculty timetables successfully.`
      });
    } catch (error) {
      console.error("Error saving timetable:", error);
      toast({
        title: "Error",
        description: "Failed to save timetable data.",
        variant: "destructive"
      });
    } finally {
      setIsSavingTimetable(false);
      setSaveDialogOpen(false);
    }
  };

  return (
    <ModernLayout
      title="Timetable Management"
      description="Define time structure and manage class timetables with professional scheduling tools"
      actions={
        <ModernButtonGroup>
          <Button
            variant="outline"
            onClick={() => setShowTimeStructureForm(true)}
            className="flex items-center gap-2"
          >
            <Clock className="h-4 w-4" />
            Time Structure
          </Button>
        </ModernButtonGroup>
      }
    >

            <Tabs defaultValue="timetable-grid" value={activeTab} onValueChange={setActiveTab} className="space-y-8 animate-fade-in">
              <TabsList className="grid w-full md:w-auto grid-cols-2 bg-muted/50 p-1 rounded-lg">
                <TabsTrigger
                  value="timetable-grid"
                  className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm"
                >
                  <Calendar className="w-4 h-4" />
                  Timetable Grid
                </TabsTrigger>
                <TabsTrigger
                  value="faculty-timetable"
                  className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm"
                >
                  <Users className="w-4 h-4" />
                  Faculty Timetable
                </TabsTrigger>
              </TabsList>

              {/* Faculty Timetable Tab Content */}
              <TabsContent value="faculty-timetable" className="space-y-4">
                <Card className="p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="faculty-academic-year">Academic Year</Label>
                      <Select value={academicYear} onValueChange={setAcademicYear}>
                        <SelectTrigger id="faculty-academic-year">
                          <SelectValue placeholder="Select academic year" />
                        </SelectTrigger>
                        <SelectContent>
                          {academicYears.map((year) => (
                            <SelectItem key={year} value={year}>
                              {year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="faculty-department">Department</Label>
                      <Select value={department} onValueChange={setDepartment}>
                        <SelectTrigger id="faculty-department">
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                        <SelectContent>
                          {departments.map((dept) => (
                            <SelectItem key={dept.id} value={dept.id}>
                              {dept.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="faculty-select">Faculty</Label>
                      <FacultyAutocomplete
                        faculties={facultyOptions}
                        selectedFaculty={facultyOptions.find(f => f.id === selectedFaculty) || null}
                        onSelect={(faculty) => {
                          handleFacultyChange(faculty.id);
                        }}
                        placeholder="Search and select faculty..."
                        className="w-full"
                      />
                    </div>
                  </div>
                </Card>

                {selectedFaculty && (
                  <>
                    {/* Export buttons for faculty view */}
                    <div className="flex flex-wrap gap-2 justify-end">
                      <Button
                        variant="outline"
                        onClick={() => handleFacultyExport("pdf")}
                        disabled={!selectedFaculty || !savedTimeStructure}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Export Faculty PDF
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleFacultyExport("excel")}
                        disabled={!selectedFaculty || !savedTimeStructure}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Export Faculty Excel
                      </Button>
                    </div>

                    <TimetableGrid
                      timetableGridRef={timetableGridRef}
                      department={department}
                      section=""
                      semester=""
                      academicYear={academicYear}
                      view="faculty"
                      selectedFaculty={selectedFaculty}
                      facultyOptions={facultyOptions}
                    />
                  </>
                )}
              </TabsContent>

              <TabsContent value="timetable-grid" className="space-y-6 animate-slide-up">
                <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-6 shadow-soft">
                  <div className="flex flex-wrap gap-3 justify-end">
                  <Button
                    variant="outline"
                    onClick={() => handleExport("pdf")}
                    disabled={!hasTimetable || !savedTimeStructure}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Export PDF
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleExport("excel")}
                    disabled={!hasTimetable || !savedTimeStructure}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Export Excel
                  </Button>
                  <Button
                    onClick={handleClearTimetable}
                    disabled={isClearing}
                    variant="outline"
                    className="text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className={`mr-2 h-4 w-4 ${isClearing ? "animate-spin" : ""}`} />
                    {isClearing ? "Clearing..." : "Clear Timetable"}
                  </Button>
                  <Button
                    onClick={handleGenerateLabSlots}
                    disabled={isGeneratingLabSlots || labMappingsCount === 0 || !savedTimeStructure}
                    variant="secondary"
                    className="bg-blue-100 hover:bg-blue-200 text-blue-700"
                  >
                    <Beaker className={`mr-2 h-4 w-4 ${isGeneratingLabSlots ? "animate-spin" : ""}`} />
                    {isGeneratingLabSlots ? "Generating..." : "Generate Lab Slots"}
                  </Button>
                  <Button
                    onClick={handleGenerateTheorySlots}
                    disabled={isGeneratingTheorySlots || theoryMappingsCount === 0 || !savedTimeStructure || !labSlotsGenerated}
                    variant="secondary"
                    className="bg-green-100 hover:bg-green-200 text-green-700"
                    title="Enhanced theory slot generator with conflict prevention, successive period avoidance, morning period distribution, and gap prevention"
                  >
                    <BookOpen className={`mr-2 h-4 w-4 ${isGeneratingTheorySlots ? "animate-spin" : ""}`} />
                    {isGeneratingTheorySlots ? "Generating..." : "Generate Theory Slots (Enhanced)"}
                  </Button>
                  <Button
                    onClick={handleSaveTimetable}
                    disabled={isSavingTimetable || !labSlotsGenerated || !theorySlotsGenerated}
                    variant="secondary"
                    className="bg-purple-100 hover:bg-purple-200 text-purple-700"
                  >
                    <Save className={`mr-2 h-4 w-4 ${isSavingTimetable ? "animate-spin" : ""}`} />
                    {isSavingTimetable ? "Saving..." : "Save Timetable"}
                  </Button>
                  </div>
                </div>

                <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 p-6 shadow-soft space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                    <div>
                      <Label htmlFor="view-type">View Type</Label>
                      <Select value={view} onValueChange={setView as (value: string) => void}>
                        <SelectTrigger id="view-type">
                          <SelectValue placeholder="Select view" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="class">Class View</SelectItem>
                          <SelectItem value="faculty">Faculty View</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="department">Department</Label>
                      <Select value={department} onValueChange={setDepartment}>
                        <SelectTrigger id="department">
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                        <SelectContent>
                          {departments.map((dept) => (
                            <SelectItem key={dept.id} value={dept.id}>
                              {dept.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {view === "class" && (
                      <>
                        <div>
                          <Label htmlFor="academic-year">Academic Year</Label>
                          <Select value={academicYear} onValueChange={setAcademicYear}>
                            <SelectTrigger id="academic-year">
                              <SelectValue placeholder="Select academic year" />
                            </SelectTrigger>
                            <SelectContent>
                              {academicYears.map((year) => (
                                <SelectItem key={year} value={year}>
                                  {year}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="semester">Semester</Label>
                          <Select value={semester} onValueChange={setSemester}>
                            <SelectTrigger id="semester">
                              <SelectValue placeholder="Select semester" />
                            </SelectTrigger>
                            <SelectContent>
                              {semesters.map((sem) => (
                                <SelectItem key={sem} value={sem}>
                                  Semester {sem}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="section">Section</Label>
                          <Select value={section} onValueChange={setSection}>
                            <SelectTrigger id="section">
                              <SelectValue placeholder="Select section" />
                            </SelectTrigger>
                            <SelectContent>
                              {sections.map((sec) => (
                                <SelectItem key={sec} value={sec}>
                                  Section {sec}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </>
                    )}

                    {view === "faculty" && (
                      <div>
                        <Label htmlFor="faculty">Faculty</Label>
                        <Select
                          value={selectedFaculty}
                          onValueChange={handleFacultyChange}
                        >
                          <SelectTrigger id="faculty">
                            <SelectValue placeholder="Select faculty" />
                          </SelectTrigger>
                          <SelectContent>
                            {facultyOptions.map(f => (
                              <SelectItem key={f.id} value={f.id}>
                                {f.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  {subjectMappingsCount > 0 && (
                    <div className="bg-success/10 border border-success/20 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="p-1 bg-success/20 rounded-full">
                          <CheckCircle className="w-4 h-4 text-success" />
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-success-foreground">
                            Found {subjectMappingsCount} subject mappings for the current selection.
                          </p>
                          {labMappingsCount > 0 && (
                            <p className="text-xs text-success-foreground/80">
                              Including {labMappingsCount} lab mappings ({labMappingsCount * 2} lab slots) and {theoryMappingsCount} theory mappings.
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {subjectMappingsCount === 0 && (
                    <div className="bg-warning/10 border border-warning/20 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="p-1 bg-warning/20 rounded-full">
                          <AlertCircle className="w-4 h-4 text-warning" />
                        </div>
                        <p className="text-sm font-medium text-warning-foreground">
                          No subject mappings found for this selection. Please create subject mappings first.
                        </p>
                      </div>
                    </div>
                  )}

                  {!savedTimeStructure && (
                    <div className="bg-warning/10 border border-warning/20 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="p-1 bg-warning/20 rounded-full">
                          <AlertTriangle className="w-4 h-4 text-warning" />
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-warning-foreground">
                            Time structure not defined.
                          </p>
                          <p className="text-xs text-warning-foreground/80">
                            Please define time structure in the{' '}
                            <Link to="/timetable-structure" className="font-medium underline hover:no-underline">
                              Timetable Structure
                            </Link>{' '}
                            page.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <TimetableGrid
                  timetableGridRef={timetableGridRef}
                  department={department}
                  section={section}
                  semester={semester}
                  academicYear={academicYear}
                  view={view}
                  selectedFaculty={view === "faculty" ? selectedFaculty : undefined}
                  facultyOptions={facultyOptions}
                />
              </TabsContent>
            </Tabs>

            <Dialog open={openOptimizeDialog} onOpenChange={setOpenOptimizeDialog}>
              <DialogContent className="max-w-3xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    AI Timetable Optimization
                  </DialogTitle>
                  <DialogDescription>
                    The following conflicts and optimization suggestions were found for your timetable.
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  {conflicts.length > 0 && (
                    <div>
                      <h3 className="font-medium mb-3 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                        Detected Conflicts
                      </h3>
                      <div className="space-y-2">
                        {conflicts.map((conflict, index) => (
                          <div
                            key={index}
                            className={`p-3 rounded-md flex items-start gap-2 ${getSeverityColor(conflict.severity)}`}
                          >
                            <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                            <div>
                              <p>{conflict.description}</p>
                              <p className="text-xs font-medium mt-1 capitalize">
                                {conflict.severity} Priority
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {suggestions.length > 0 && (
                    <div>
                      <h3 className="font-medium mb-3 flex items-center gap-2">
                        <Sparkles className="h-4 w-4 text-primary" />
                        Optimization Suggestionsc
                      </h3>
                      <div className="space-y-2">
                        {suggestions.map((suggestion, index) => (
                          <div
                            key={index}
                            className={`p-3 rounded-md flex items-start gap-2 ${suggestion.type === "faculty" ? "bg-blue-50" : "bg-green-50"}`}
                          >
                            <Sparkles className={`h-4 w-4 mt-0.5 flex-shrink-0 ${suggestion.type === "faculty" ? "text-blue-500" : "text-green-500"}`} />
                            <div>
                              <p>{suggestion.description}</p>
                              <p className="text-xs font-medium mt-1 capitalize">
                                {suggestion.type} Suggestion
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <DialogFooter className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" onClick={() => setOpenOptimizeDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={applyOptimizations}>
                    Apply All Optimizations
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={validationDialogOpen} onOpenChange={setValidationDialogOpen}>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-amber-500" />
                    Timetable Validation Results
                  </DialogTitle>
                  <DialogDescription>
                    The timetable has been generated, but some constraints couldn't be fully satisfied.
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4 my-2">
                  <Alert variant="warning" className="bg-amber-50">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Warning</AlertTitle>
                    <AlertDescription>
                      The timetable has been generated but has some issues that you may want to review.
                    </AlertDescription>
                  </Alert>

                  <div className="max-h-[300px] overflow-y-auto border rounded-md p-3">
                    <h4 className="font-medium mb-2">Issues Found:</h4>
                    <ul className="list-disc pl-6 space-y-1">
                      {validationMessages.map((message, index) => (
                        <li key={index} className="text-sm">{message}</li>
                      ))}
                    </ul>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setValidationDialogOpen(false)}>
                    Close
                  </Button>
                  <Button onClick={handleGenerateTimetable}>
                    Regenerate Timetable
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Add the confirmation dialog */}
            <AlertDialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Save Timetable</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will save the current timetable to both class_timetables and faculty_timetables tables.
                    Are you sure you want to continue?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleConfirmSave}>Save</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

      {/* Time Structure Form Dialog */}
      <Dialog open={showTimeStructureForm} onOpenChange={setShowTimeStructureForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Time Structure Configuration</DialogTitle>
            <DialogDescription>
              Set up working days, class durations, and break times for all timetables
            </DialogDescription>
          </DialogHeader>
          <TimeStructureForm onSave={handleTimeStructureSave} />
        </DialogContent>
      </Dialog>
    </ModernLayout>
  );
};

export default Timetable;
