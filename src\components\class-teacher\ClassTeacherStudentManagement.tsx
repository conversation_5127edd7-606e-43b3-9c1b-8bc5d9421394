import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { StudentService } from '@/services/StudentService';
import { ClassTeacherService } from '@/services/ClassTeacherService';
import { Upload, Users, BookOpen, GraduationCap, AlertCircle, CheckCircle, FileText, Eye, X, Check } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { defaultFilterOptions } from '@/components/subjects/AllotmentFilterOptions';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';


interface ClassTeacherAssignment {
  department: string;
  semester: string;
  section: string;
  academic_year: string;
}

interface StudentPreview {
  slno: string;
  usn: string;
  name: string;
  email?: string;
  student_mobile?: string;
  father_mobile?: string;
  mother_mobile?: string;
}

interface PreviewData {
  students: StudentPreview[];
  fileName: string;
  type: 'class' | 'lab' | 'elective';
  identifier?: string; // batch name for lab, subject id for elective
}

interface UploadState {
  step: 'idle' | 'file-selected' | 'preview-loaded' | 'uploading' | 'completed';
  file: File | null;
  previewData: StudentPreview[] | null;
  error: string | null;
}

// Reusable inline upload component
interface InlineUploadProps {
  title: string;
  description: string;
  uploadState: UploadState;
  onFileSelect: (file: File) => void;
  onUpload: () => void;
  onClear: () => void;
  uploading: boolean;
  uploadProgress?: number;
  acceptedFileTypes?: string;
}

const InlineUpload: React.FC<InlineUploadProps> = ({
  title,
  description,
  uploadState,
  onFileSelect,
  onUpload,
  onClear,
  uploading,
  uploadProgress,
  acceptedFileTypes = ".xlsx,.xls"
}) => {
  const getStepIcon = (step: string) => {
    switch (step) {
      case 'idle':
        return <Upload className="h-5 w-5 text-muted-foreground" />;
      case 'file-selected':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'preview-loaded':
        return <Eye className="h-5 w-5 text-green-500" />;
      case 'uploading':
        return <Upload className="h-5 w-5 text-orange-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      default:
        return <Upload className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getStepText = (step: string) => {
    switch (step) {
      case 'idle':
        return 'Select Excel file';
      case 'file-selected':
        return 'File selected, loading preview...';
      case 'preview-loaded':
        return 'Preview loaded, ready to upload';
      case 'uploading':
        return 'Uploading to database...';
      case 'completed':
        return 'Upload completed successfully';
      default:
        return 'Select Excel file';
    }
  };

  const getStepColor = (step: string) => {
    switch (step) {
      case 'idle':
        return 'text-muted-foreground';
      case 'file-selected':
        return 'text-blue-600';
      case 'preview-loaded':
        return 'text-green-600';
      case 'uploading':
        return 'text-orange-600';
      case 'completed':
        return 'text-green-700';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
        {getStepIcon(uploadState.step)}
        <div className="flex-1">
          <h4 className="font-medium">{title}</h4>
          <p className={`text-sm ${getStepColor(uploadState.step)}`}>
            {getStepText(uploadState.step)}
          </p>
        </div>
        {uploadState.step !== 'idle' && uploadState.step !== 'completed' && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClear}
            disabled={uploading}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {uploadState.step === 'idle' && (
        <div className="space-y-3">
          <Label htmlFor={`file-${title}`}>{description}</Label>
          <Input
            id={`file-${title}`}
            type="file"
            accept={acceptedFileTypes}
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                onFileSelect(file);
              }
            }}
            disabled={uploading}
          />
        </div>
      )}

      {uploadState.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{uploadState.error}</AlertDescription>
        </Alert>
      )}

      {uploadState.previewData && uploadState.step === 'preview-loaded' && (
        <div className="space-y-6">
          {/* Enhanced Preview Header */}
          <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-full">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{uploadState.file?.name}</h3>
                  <p className="text-sm text-gray-600">Excel file successfully parsed</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">{uploadState.previewData.length}</div>
                <div className="text-sm text-gray-600">Students Found</div>
              </div>
            </div>

            {/* Simple Stats */}
            <div className="text-center">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/60 rounded-lg">
                <span className="text-lg font-semibold text-blue-600">{uploadState.previewData.length}</span>
                <span className="text-sm text-gray-600">students ready to upload</span>
              </div>
            </div>
          </div>

          {/* Enhanced Student List */}
          <div className="border rounded-lg overflow-hidden shadow-sm">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 border-b">
              <div className="flex items-center justify-between">
                <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Student List Preview
                </h4>
                <Badge variant="outline" className="bg-white">
                  {uploadState.previewData.length} students
                </Badge>
              </div>
            </div>

            <div className="max-h-96 overflow-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-white shadow-sm">
                  <TableRow>
                    <TableHead className="w-16 text-center font-semibold">SLNO</TableHead>
                    <TableHead className="w-32 font-semibold">USN</TableHead>
                    <TableHead className="font-semibold">STUDENT NAME</TableHead>
                    <TableHead className="w-40 font-semibold">EMAIL</TableHead>
                    <TableHead className="w-32 font-semibold">STUDENT MOBILE</TableHead>
                    <TableHead className="w-32 font-semibold">FATHER MOBILE</TableHead>
                    <TableHead className="w-32 font-semibold">MOTHER MOBILE</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {uploadState.previewData.map((student, index) => (
                    <TableRow key={index} className="hover:bg-muted/50 transition-colors">
                      <TableCell className="text-center font-mono text-sm font-medium">
                        {student.slno}
                      </TableCell>
                      <TableCell className="font-mono text-sm font-bold text-blue-700">
                        {student.usn}
                      </TableCell>
                      <TableCell className="font-medium text-gray-900">
                        {student.name}
                      </TableCell>
                      <TableCell className="text-sm text-gray-700">
                        {student.email || '-'}
                      </TableCell>
                      <TableCell className="font-mono text-sm text-gray-700">
                        {student.student_mobile || '-'}
                      </TableCell>
                      <TableCell className="font-mono text-sm text-gray-700">
                        {student.father_mobile || '-'}
                      </TableCell>
                      <TableCell className="font-mono text-sm text-gray-700">
                        {student.mother_mobile || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Summary Footer */}
            <div className="bg-gray-50 px-4 py-3 border-t">
              <div className="text-center text-sm text-gray-600">
                <span className="font-medium">Total: {uploadState.previewData.length} students ready to upload</span>
              </div>
            </div>
          </div>

          {/* Enhanced Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={onUpload}
              disabled={uploading}
              className="flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-3 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Check className="h-5 w-5 mr-2" />
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading to Database...
                </>
              ) : (
                `Upload ${uploadState.previewData.length} Students to Database`
              )}
            </Button>
            <Button
              variant="outline"
              onClick={onClear}
              disabled={uploading}
              className="px-6 py-3 border-2 hover:bg-red-50 hover:border-red-300 hover:text-red-700 transition-all duration-200"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        </div>
      )}

      {uploadProgress !== undefined && uploadState.step === 'uploading' && (
        <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-orange-100 rounded-full">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"></div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">Uploading to Database</h4>
              <p className="text-sm text-gray-600">Saving student records to the database...</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm font-medium">
              <span className="text-gray-700">Upload Progress</span>
              <span className="text-orange-600">{uploadProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
              <div
                className="bg-gradient-to-r from-orange-500 to-yellow-500 h-3 rounded-full transition-all duration-500 shadow-sm"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-600 text-center">
              Please wait while we save your student data...
            </div>
          </div>
        </div>
      )}

      {uploadState.step === 'completed' && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-green-100 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-green-900">Upload Completed Successfully!</h4>
              <p className="text-sm text-green-700">All student records have been saved to the database.</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onClear}
              className="bg-white hover:bg-green-50 border-green-300 text-green-700 hover:text-green-800"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload Another File
            </Button>
          </div>

          <div className="bg-white/60 rounded-lg p-3 mt-3">
            <div className="flex items-center justify-center gap-6 text-sm">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">✓</div>
                <div className="text-green-700">Database Updated</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">📊</div>
                <div className="text-blue-700">Records Saved</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-600">🎉</div>
                <div className="text-purple-700">Ready for Use</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const ClassTeacherStudentManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('class-students');
  const [classTeacherAssignment, setClassTeacherAssignment] = useState<ClassTeacherAssignment | null>(null);
  const [loading, setLoading] = useState(true);
  const [labBatches, setLabBatches] = useState<string[]>([]);
  const [electiveSubjects, setElectiveSubjects] = useState<{ id: string; code: string; name: string }[]>([]);

  // New inline upload states
  const [classUploadState, setClassUploadState] = useState<UploadState>({
    step: 'idle',
    file: null,
    previewData: null,
    error: null
  });
  const [labUploadStates, setLabUploadStates] = useState<{ [batchName: string]: UploadState }>({});
  const [electiveUploadStates, setElectiveUploadStates] = useState<{ [subjectId: string]: UploadState }>({});

  // Upload progress states
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});

  // Permanent preview states
  const [uploadedClassStudents, setUploadedClassStudents] = useState<any>(null);
  const [uploadedBatchStudents, setUploadedBatchStudents] = useState<Record<string, any>>({});
  const [loadingPreviews, setLoadingPreviews] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();

  // Get current academic year
  const currentAcademicYear = defaultFilterOptions.yearsList[0] || '2024-2025';

  console.log('🔍 Student Management - Current Academic Year:', currentAcademicYear);
  console.log('🔍 Student Management - Years List:', defaultFilterOptions.yearsList);

  useEffect(() => {
    loadClassTeacherAssignment();
  }, [user?.id]);

  useEffect(() => {
    if (classTeacherAssignment) {
      loadLabBatches();
      loadElectiveSubjects();
      loadPermanentPreviews();
    }
  }, [classTeacherAssignment]);

  const loadClassTeacherAssignment = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);

      // Get class teacher assignment
      const assignment = await ClassTeacherService.getClassTeacherAssignment(user.id, currentAcademicYear);

      if (!assignment) {
        toast({
          title: 'Access Denied',
          description: 'You are not assigned as a class teacher for any class.',
          variant: 'destructive',
        });
        return;
      }

      setClassTeacherAssignment(assignment);
    } catch (error) {
      console.error('Error loading class teacher assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to load class teacher assignment.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadLabBatches = async () => {
    if (!classTeacherAssignment) return;

    try {
      console.log('🔍 Loading lab batches for assignment:', classTeacherAssignment);

      const batches = await StudentService.getLabBatches(
        classTeacherAssignment.department,
        classTeacherAssignment.semester,
        classTeacherAssignment.section
      );

      console.log('🔍 Received lab batches:', batches);
      setLabBatches(batches);
    } catch (error) {
      console.error('Error loading lab batches:', error);
      toast({
        title: 'Error',
        description: 'Failed to load lab batches.',
        variant: 'destructive',
      });
    }
  };

  const loadElectiveSubjects = async () => {
    if (!classTeacherAssignment) return;

    try {
      const electives = await StudentService.getElectiveSubjects(
        classTeacherAssignment.department,
        classTeacherAssignment.semester,
        classTeacherAssignment.section
      );
      setElectiveSubjects(electives);
    } catch (error) {
      console.error('Error loading elective subjects:', error);
      toast({
        title: 'Error',
        description: 'Failed to load elective subjects.',
        variant: 'destructive',
      });
    }
  };

  const loadPermanentPreviews = async () => {
    if (!classTeacherAssignment) return;

    try {
      setLoadingPreviews(true);

      // Load uploaded class students
      try {
        const classStudents = await StudentService.getUploadedClassStudents(
          classTeacherAssignment.department,
          classTeacherAssignment.semester,
          classTeacherAssignment.section,
          currentAcademicYear
        );
        setUploadedClassStudents(classStudents);
      } catch (error) {
        console.log('No uploaded class students found');
        setUploadedClassStudents(null);
      }

      // Load uploaded batch students for each batch
      const batchStudentsData: Record<string, any> = {};
      for (const batchName of labBatches) {
        try {
          const batchStudents = await StudentService.getLabBatchStudents(
            batchName,
            classTeacherAssignment.department,
            classTeacherAssignment.semester,
            classTeacherAssignment.section,
            currentAcademicYear
          );
          if (batchStudents.totalStudents > 0) {
            batchStudentsData[batchName] = batchStudents;
          }
        } catch (error) {
          console.log(`No uploaded students found for batch ${batchName}`);
        }
      }
      setUploadedBatchStudents(batchStudentsData);

    } catch (error) {
      console.error('Error loading permanent previews:', error);
    } finally {
      setLoadingPreviews(false);
    }
  };

  const handleDeleteClassStudents = async () => {
    if (!classTeacherAssignment) return;

    try {
      await StudentService.deleteUploadedClassStudents(
        classTeacherAssignment.department,
        classTeacherAssignment.semester,
        classTeacherAssignment.section,
        currentAcademicYear
      );

      setUploadedClassStudents(null);
      toast({
        title: 'Success',
        description: 'Class students deleted successfully.',
      });
    } catch (error) {
      console.error('Error deleting class students:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete class students.',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteBatchStudents = async (batchName: string) => {
    if (!classTeacherAssignment) return;

    try {
      await StudentService.deleteUploadedBatchStudents(
        batchName,
        classTeacherAssignment.department,
        classTeacherAssignment.semester,
        classTeacherAssignment.section,
        currentAcademicYear
      );

      setUploadedBatchStudents(prev => {
        const updated = { ...prev };
        delete updated[batchName];
        return updated;
      });

      toast({
        title: 'Success',
        description: `Batch ${batchName} students deleted successfully.`,
      });
    } catch (error) {
      console.error('Error deleting batch students:', error);
      toast({
        title: 'Error',
        description: `Failed to delete batch ${batchName} students.`,
        variant: 'destructive',
      });
    }
  };

  // New inline preview and upload handlers
  const handleFileSelect = async (file: File, type: 'class' | 'lab' | 'elective', identifier?: string) => {
    try {
      // Update state to show file selected
      const newState: UploadState = {
        step: 'file-selected',
        file,
        previewData: null,
        error: null
      };

      if (type === 'class') {
        setClassUploadState(newState);
      } else if (type === 'lab' && identifier) {
        setLabUploadStates(prev => ({ ...prev, [identifier]: newState }));
      } else if (type === 'elective' && identifier) {
        setElectiveUploadStates(prev => ({ ...prev, [identifier]: newState }));
      }

      // Automatically load preview
      await handleLoadPreview(file, type, identifier);
    } catch (error) {
      console.error('Error selecting file:', error);
    }
  };

  const handleLoadPreview = async (file: File, type: 'class' | 'lab' | 'elective', identifier?: string) => {
    try {
      const students = await StudentService.parseClassStudentExcel(file);

      const newState: UploadState = {
        step: 'preview-loaded',
        file,
        previewData: students,
        error: null
      };

      if (type === 'class') {
        setClassUploadState(newState);
      } else if (type === 'lab' && identifier) {
        setLabUploadStates(prev => ({ ...prev, [identifier]: newState }));
      } else if (type === 'elective' && identifier) {
        setElectiveUploadStates(prev => ({ ...prev, [identifier]: newState }));
      }
    } catch (error) {
      console.error('Error loading preview:', error);

      const errorState: UploadState = {
        step: 'file-selected',
        file,
        previewData: null,
        error: error instanceof Error ? error.message : 'Failed to load preview'
      };

      if (type === 'class') {
        setClassUploadState(errorState);
      } else if (type === 'lab' && identifier) {
        setLabUploadStates(prev => ({ ...prev, [identifier]: errorState }));
      } else if (type === 'elective' && identifier) {
        setElectiveUploadStates(prev => ({ ...prev, [identifier]: errorState }));
      }

      toast({
        title: 'Preview Error',
        description: error instanceof Error ? error.message : 'Failed to preview file.',
        variant: 'destructive',
      });
    }
  };

  const handleUpload = async (type: 'class' | 'lab' | 'elective', identifier?: string) => {
    if (!classTeacherAssignment) return;

    let uploadState: UploadState;
    if (type === 'class') {
      uploadState = classUploadState;
    } else if (type === 'lab' && identifier) {
      uploadState = labUploadStates[identifier];
    } else if (type === 'elective' && identifier) {
      uploadState = electiveUploadStates[identifier];
    } else {
      return;
    }

    if (!uploadState.previewData) return;

    try {
      setUploading(true);

      // Update state to uploading
      const uploadingState: UploadState = {
        ...uploadState,
        step: 'uploading'
      };

      if (type === 'class') {
        setClassUploadState(uploadingState);
      } else if (type === 'lab' && identifier) {
        setLabUploadStates(prev => ({ ...prev, [identifier]: uploadingState }));
      } else if (type === 'elective' && identifier) {
        setElectiveUploadStates(prev => ({ ...prev, [identifier]: uploadingState }));
      }

      const progressKey = identifier || type;
      setUploadProgress({ [progressKey]: 0 });

      let result;
      if (type === 'class') {
        setUploadProgress({ [progressKey]: 50 });
        result = await StudentService.uploadClassStudents(uploadState.previewData, classTeacherAssignment);
      } else if (type === 'lab' && identifier) {
        setUploadProgress({ [progressKey]: 50 });
        result = await StudentService.uploadLabBatchStudents(uploadState.previewData, identifier, classTeacherAssignment);
      } else if (type === 'elective' && identifier) {
        setUploadProgress({ [progressKey]: 50 });
        result = await StudentService.uploadElectiveStudents(uploadState.previewData, identifier, classTeacherAssignment);
      }

      setUploadProgress({ [progressKey]: 100 });

      // Update state to completed
      const completedState: UploadState = {
        step: 'completed',
        file: null,
        previewData: null,
        error: null
      };

      if (type === 'class') {
        setClassUploadState(completedState);
      } else if (type === 'lab' && identifier) {
        setLabUploadStates(prev => ({ ...prev, [identifier]: completedState }));
      } else if (type === 'elective' && identifier) {
        setElectiveUploadStates(prev => ({ ...prev, [identifier]: completedState }));
      }

      // Refresh permanent previews after successful upload
      await loadPermanentPreviews();

      toast({
        title: 'Upload Successful',
        description: `Successfully uploaded ${result?.success || uploadState.previewData.length} students.`,
      });
    } catch (error) {
      console.error('Error uploading students:', error);

      // Reset to preview-loaded state on error
      const errorState: UploadState = {
        ...uploadState,
        step: 'preview-loaded',
        error: error instanceof Error ? error.message : 'Upload failed'
      };

      if (type === 'class') {
        setClassUploadState(errorState);
      } else if (type === 'lab' && identifier) {
        setLabUploadStates(prev => ({ ...prev, [identifier]: errorState }));
      } else if (type === 'elective' && identifier) {
        setElectiveUploadStates(prev => ({ ...prev, [identifier]: errorState }));
      }

      toast({
        title: 'Upload Failed',
        description: error instanceof Error ? error.message : 'Failed to upload students.',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  };

  const handleClearUpload = (type: 'class' | 'lab' | 'elective', identifier?: string) => {
    const resetState: UploadState = {
      step: 'idle',
      file: null,
      previewData: null,
      error: null
    };

    if (type === 'class') {
      setClassUploadState(resetState);
    } else if (type === 'lab' && identifier) {
      setLabUploadStates(prev => ({ ...prev, [identifier]: resetState }));
    } else if (type === 'elective' && identifier) {
      setElectiveUploadStates(prev => ({ ...prev, [identifier]: resetState }));
    }
  };



  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading class teacher assignment...</p>
        </div>
      </div>
    );
  }

  if (!classTeacherAssignment) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          You are not assigned as a class teacher for any class in the current academic year ({currentAcademicYear}).
          Please contact the administrator to assign you as a class teacher.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Responsive Container with proper padding */}
      <div className="w-full max-w-none mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="space-y-6 sm:space-y-8">
          {/* Header Section */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="space-y-1">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Student Management</h1>
              <p className="text-sm sm:text-base text-muted-foreground">
                Manage students for {classTeacherAssignment.department} - Semester {classTeacherAssignment.semester} Section {classTeacherAssignment.section}
              </p>
            </div>
            <Badge variant="secondary" className="text-xs sm:text-sm self-start sm:self-auto">
              Class Teacher - {currentAcademicYear}
            </Badge>
          </div>

          {/* Tabs Section */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 sm:space-y-6">
            <div className="overflow-x-auto">
              <TabsList className="grid w-full grid-cols-3 min-w-fit">
                <TabsTrigger value="class-students" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4">
                  <Users className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">Class Students</span>
                  <span className="xs:hidden">Class</span>
                </TabsTrigger>
                <TabsTrigger value="lab-batches" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4">
                  <BookOpen className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">Lab Batches</span>
                  <span className="xs:hidden">Lab</span>
                </TabsTrigger>
                <TabsTrigger value="electives" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-4">
                  <GraduationCap className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">Elective Enrollment</span>
                  <span className="xs:hidden">Elective</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Class Students Tab */}
            <TabsContent value="class-students" className="space-y-4 sm:space-y-6">
              <Card className="shadow-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                    <Users className="h-4 w-4 sm:h-5 sm:w-5" />
                    Class Student Upload
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Upload the complete class student list. This will be used by faculty teaching theory subjects for attendance marking and internal assessment.
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
              <InlineUpload
                title="Class Students Upload"
                description="Upload Excel file with class students (SLNO, USN, NAME, EMAIL-ID, STUDENT MOBILE, FATHER MOBILE, MOTHER MOBILE columns)"
                uploadState={classUploadState}
                onFileSelect={(file) => handleFileSelect(file, 'class')}
                onUpload={() => handleUpload('class')}
                onClear={() => handleClearUpload('class')}
                uploading={uploading}
                uploadProgress={uploadProgress.class}
              />
            </CardContent>
          </Card>

              {/* Permanent Preview for Class Students */}
              {uploadedClassStudents && uploadedClassStudents.totalStudents > 0 && (
                <Card className="shadow-sm">
                  <CardHeader className="pb-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                        <CardTitle className="text-lg sm:text-xl">Uploaded Class Students</CardTitle>
                      </div>
                      <div className="flex items-center gap-2 flex-wrap">
                        <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs sm:text-sm">
                          {uploadedClassStudents.totalStudents} students
                        </Badge>
                        <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <X className="h-4 w-4 mr-1" />
                          Delete List
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Class Students</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete all uploaded class students? This action cannot be undone.
                            This will remove {uploadedClassStudents.totalStudents} student records.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={handleDeleteClassStudents} className="bg-red-600 hover:bg-red-700">
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
                    <CardDescription className="text-sm">
                      Permanently saved class student list for {uploadedClassStudents.classInfo.department} -
                      Semester {uploadedClassStudents.classInfo.semester} Section {uploadedClassStudents.classInfo.section}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="border rounded-lg overflow-hidden">
                      <div className="max-h-96 overflow-auto">
                        <Table>
                          <TableHeader className="sticky top-0 bg-white shadow-sm">
                            <TableRow>
                              <TableHead className="w-12 sm:w-16 text-center font-semibold text-xs sm:text-sm">SLNO</TableHead>
                              <TableHead className="w-24 sm:w-32 font-semibold text-xs sm:text-sm">USN</TableHead>
                              <TableHead className="font-semibold text-xs sm:text-sm">STUDENT NAME</TableHead>
                              <TableHead className="w-20 sm:w-32 font-semibold text-xs sm:text-sm hidden sm:table-cell">UPLOADED</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {uploadedClassStudents.students.map((student: any, index: number) => (
                              <TableRow key={student.id} className="hover:bg-muted/50 transition-colors">
                                <TableCell className="text-center font-mono text-xs sm:text-sm font-medium">
                                  {index + 1}
                                </TableCell>
                                <TableCell className="font-mono text-xs sm:text-sm font-bold text-blue-700">
                                  {student.usn}
                                </TableCell>
                                <TableCell className="font-medium text-gray-900 text-xs sm:text-sm">
                                  <div className="truncate max-w-[150px] sm:max-w-none" title={student.studentName}>
                                    {student.studentName}
                                  </div>
                                </TableCell>
                                <TableCell className="text-xs sm:text-sm text-muted-foreground hidden sm:table-cell">
                                  {student.uploadedAt ? new Date(student.uploadedAt).toLocaleDateString() : '-'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                    </Table>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

            {/* Lab Batches Tab */}
            <TabsContent value="lab-batches" className="space-y-4 sm:space-y-6">
              <Card className="shadow-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                    <BookOpen className="h-4 w-4 sm:h-5 sm:w-5" />
                    Lab Batch Student Upload
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Upload student lists for each lab batch. Lab batches are semester-section specific and shared across all lab subjects.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6 pt-0">
              {labBatches.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No lab batches found for this semester-section. Lab batches are automatically detected based on lab subject configurations.
                  </AlertDescription>
                </Alert>
              ) : (
                labBatches.map((batchName) => {
                  const batchUploadState = labUploadStates[batchName] || {
                    step: 'idle',
                    file: null,
                    previewData: null,
                    error: null
                  };

                  return (
                    <div key={batchName} className="border rounded-lg p-4 space-y-4">
                      <div className="flex items-center gap-2 mb-4">
                        <Badge variant="outline">{batchName}</Badge>
                        <span className="text-sm text-muted-foreground">
                          Upload students for {batchName}
                        </span>
                      </div>

                      <InlineUpload
                        title={`${batchName} Students`}
                        description={`Upload Excel file with ${batchName} students (SLNO, USN, NAME, EMAIL-ID, STUDENT MOBILE, FATHER MOBILE, MOTHER MOBILE columns)`}
                        uploadState={batchUploadState}
                        onFileSelect={(file) => handleFileSelect(file, 'lab', batchName)}
                        onUpload={() => handleUpload('lab', batchName)}
                        onClear={() => handleClearUpload('lab', batchName)}
                        uploading={uploading}
                        uploadProgress={uploadProgress[batchName]}
                      />
                    </div>
                  );
                })
              )}
            </CardContent>
          </Card>

          {/* Permanent Preview for Lab Batch Students */}
          {Object.keys(uploadedBatchStudents).length > 0 && (
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-green-600" />
                  <CardTitle>Uploaded Lab Batch Students</CardTitle>
                </div>
                <CardDescription>
                  Permanently saved lab batch student lists for this semester-section
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {Object.entries(uploadedBatchStudents).map(([batchName, batchData]) => (
                  <div key={batchName} className="border rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{batchName}</Badge>
                        <span className="font-medium">
                          {batchData.totalStudents} students uploaded
                        </span>
                      </div>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                            <X className="h-4 w-4 mr-1" />
                            Delete {batchName}
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete {batchName} Students</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete all uploaded students for batch {batchName}?
                              This action cannot be undone. This will remove {batchData.totalStudents} student records.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteBatchStudents(batchName)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>

                    <div className="border rounded-lg overflow-hidden">
                      <div className="max-h-64 overflow-auto">
                        <Table>
                          <TableHeader className="sticky top-0 bg-white shadow-sm">
                            <TableRow>
                              <TableHead className="w-16 text-center font-semibold">SLNO</TableHead>
                              <TableHead className="w-32 font-semibold">USN</TableHead>
                              <TableHead className="font-semibold">STUDENT NAME</TableHead>
                              <TableHead className="w-32 font-semibold">UPLOADED</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {batchData.students.map((student: any, index: number) => (
                              <TableRow key={`${batchName}-${student.usn}`} className="hover:bg-muted/50 transition-colors">
                                <TableCell className="text-center font-mono text-sm font-medium">
                                  {index + 1}
                                </TableCell>
                                <TableCell className="font-mono text-sm font-bold text-blue-700">
                                  {student.usn}
                                </TableCell>
                                <TableCell className="font-medium text-gray-900">
                                  {student.studentName}
                                </TableCell>
                                <TableCell className="text-sm text-muted-foreground">
                                  {student.uploadedAt ? new Date(student.uploadedAt).toLocaleDateString() : '-'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Electives Tab */}
        <TabsContent value="electives" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="h-5 w-5" />
                Elective Student Enrollment
              </CardTitle>
              <CardDescription>
                Upload student lists for each elective subject. Students who opted for specific elective subjects.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {electiveSubjects.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No elective subjects found for this semester-section. Elective subjects are automatically detected based on subject configurations.
                  </AlertDescription>
                </Alert>
              ) : (
                electiveSubjects.map((elective) => {
                  const electiveUploadState = electiveUploadStates[elective.id] || {
                    step: 'idle',
                    file: null,
                    previewData: null,
                    error: null
                  };

                  return (
                    <div key={elective.id} className="border rounded-lg p-4 space-y-4">
                      <div className="space-y-1 mb-4">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{elective.code}</Badge>
                          <span className="font-medium">{elective.name}</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Upload students who opted for this elective subject
                        </p>
                      </div>

                      <InlineUpload
                        title={`${elective.code} Students`}
                        description={`Upload Excel file with students for ${elective.code} (SLNO, USN, NAME columns)`}
                        uploadState={electiveUploadState}
                        onFileSelect={(file) => handleFileSelect(file, 'elective', elective.id)}
                        onUpload={() => handleUpload('elective', elective.id)}
                        onClear={() => handleClearUpload('elective', elective.id)}
                        uploading={uploading}
                        uploadProgress={uploadProgress[elective.id]}
                      />
                    </div>
                  );
                })
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

          {/* Instructions Card */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
                Excel File Format Instructions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm sm:text-base">Required Columns</h4>
                  <ul className="text-xs sm:text-sm text-muted-foreground space-y-1">
                    <li>• SLNO (Serial Number)</li>
                    <li>• USN (University Seat Number)</li>
                    <li>• NAME (Student Name)</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-sm sm:text-base">File Requirements</h4>
                  <ul className="text-xs sm:text-sm text-muted-foreground space-y-1">
                    <li>• Excel format (.xlsx or .xls)</li>
                    <li>• Headers automatically detected</li>
                    <li>• Skip empty rows</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-sm sm:text-base">Data Usage</h4>
                  <ul className="text-xs sm:text-sm text-muted-foreground space-y-1">
                    <li>• Class: Theory attendance & assessment</li>
                    <li>• Lab Batches: Lab attendance & assessment</li>
                    <li>• Electives: Elective subject enrollment</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ClassTeacherStudentManagement;
