
import React, { useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';

interface RoleSelectorProps {
  employeeId: string;
  initialSelectedRoles: string[];
  onSave: (roles: string[]) => void;
}

const RoleSelector: React.FC<RoleSelectorProps> = ({ 
  employeeId,
  initialSelectedRoles,
  onSave,
}) => {
  const [selectedRoles, setSelectedRoles] = useState<string[]>(initialSelectedRoles);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const availableRoles = [
    { id: 'principal', label: 'Principal' },
    { id: 'hod', label: 'HOD' },
    { id: 'class_teacher', label: 'Class Teacher' },
    { id: 'coordinator', label: 'Coordinator' },
    { id: 'faculty', label: 'Faculty' },
  ];

  const handleRoleToggle = (role: string) => {
    setSelectedRoles(prev => {
      if (prev.includes(role)) {
        // Don't allow removing faculty role if it's the only role
        if (role === 'faculty' && prev.length === 1) {
          toast({
            description: "At least one role is required",
            variant: "destructive"
          });
          return prev;
        }
        return prev.filter(r => r !== role);
      } else {
        return [...prev, role];
      }
    });
  };

  const handleSubmit = async () => {
    if (selectedRoles.length === 0) {
      toast({
        description: "Please select at least one role",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await onSave(selectedRoles);
      toast({
        description: "Roles updated successfully"
      });
    } catch (error) {
      toast({
        description: "Failed to update roles",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-4 space-y-4">
      <div>
        <h4 className="font-medium mb-2">Roles</h4>
        <div className="space-y-2">
          {availableRoles.map((role) => (
            <div key={role.id} className="flex items-center space-x-2">
              <Checkbox 
                id={`role-${role.id}`}
                checked={selectedRoles.includes(role.id)}
                onCheckedChange={() => handleRoleToggle(role.id)}
              />
              <label 
                htmlFor={`role-${role.id}`}
                className="text-sm cursor-pointer"
              >
                {role.label}
              </label>
            </div>
          ))}
        </div>
      </div>
      
      <div className="flex justify-end">
        <Button
          size="sm"
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Saving...' : 'Save Roles'}
        </Button>
      </div>
    </div>
  );
};

export default RoleSelector;
