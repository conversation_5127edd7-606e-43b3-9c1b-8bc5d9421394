import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar, AlertCircle } from 'lucide-react';

import { CreateFeedbackSessionRequest } from '@/types/feedback-system';

interface CreateFeedbackSessionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (request: CreateFeedbackSessionRequest) => Promise<void>;
  department: string;
}

const CreateFeedbackSessionDialog: React.FC<CreateFeedbackSessionDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  department
}) => {
  const [formData, setFormData] = useState<CreateFeedbackSessionRequest>({
    session_name: '',
    academic_year: '2024-25',
    department: department,
    semester: 'all',
    section: 'all',
    start_date: '',
    end_date: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validation
    if (!formData.session_name.trim()) {
      setError('Session name is required');
      return;
    }

    if (!formData.start_date || !formData.end_date) {
      setError('Start date and end date are required');
      return;
    }

    if (new Date(formData.start_date) >= new Date(formData.end_date)) {
      setError('End date must be after start date');
      return;
    }

    // Allow past dates for feedback collection after teaching periods
    // Remove the past date restriction to allow retrospective feedback

    try {
      setLoading(true);

      // Convert "all" values to undefined for database
      const submitData = {
        ...formData,
        semester: formData.semester === 'all' ? undefined : formData.semester,
        section: formData.section === 'all' ? undefined : formData.section
      };

      await onSubmit(submitData);

      // Reset form
      setFormData({
        session_name: '',
        academic_year: '2024-25',
        department: department,
        semester: 'all',
        section: 'all',
        start_date: '',
        end_date: ''
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create session');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateFeedbackSessionRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  // Generate session name suggestions based on start date or current date
  const generateSessionName = () => {
    // Use start date if available, otherwise use current date
    const referenceDate = formData.start_date ? new Date(formData.start_date) : new Date();
    const month = referenceDate.toLocaleDateString('en-US', { month: 'long' });
    const year = referenceDate.getFullYear();

    let suggestion = `${month} ${year} Faculty Feedback`;

    if (formData.semester !== 'all' && formData.section !== 'all') {
      suggestion = `${month} ${year} - ${formData.semester} Sem ${formData.section} Section`;
    } else if (formData.semester !== 'all') {
      suggestion = `${month} ${year} - ${formData.semester} Semester`;
    }

    setFormData(prev => ({
      ...prev,
      session_name: suggestion
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Create Feedback Session
          </DialogTitle>
          <DialogDescription>
            Create a new feedback collection session for students to evaluate faculty performance.
            You can create sessions for past months to collect retrospective feedback.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            {/* Session Name */}
            <div className="space-y-2">
              <Label htmlFor="session_name">Session Name</Label>
              <div className="flex gap-2">
                <Input
                  id="session_name"
                  value={formData.session_name}
                  onChange={(e) => handleInputChange('session_name', e.target.value)}
                  placeholder="e.g., November 2024 Faculty Feedback (Past Month)"
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateSessionName}
                >
                  Auto
                </Button>
              </div>
            </div>

            {/* Academic Year */}
            <div className="space-y-2">
              <Label htmlFor="academic_year">Academic Year</Label>
              <Select
                value={formData.academic_year}
                onValueChange={(value) => handleInputChange('academic_year', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select academic year" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2024-25">2024-25</SelectItem>
                  <SelectItem value="2025-26">2025-26</SelectItem>
                  <SelectItem value="2023-24">2023-24</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Target Class (Optional) */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="semester">Semester (Optional)</Label>
                <Select
                  value={formData.semester}
                  onValueChange={(value) => handleInputChange('semester', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All semesters" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Semesters</SelectItem>
                    <SelectItem value="1">1st Semester</SelectItem>
                    <SelectItem value="2">2nd Semester</SelectItem>
                    <SelectItem value="3">3rd Semester</SelectItem>
                    <SelectItem value="4">4th Semester</SelectItem>
                    <SelectItem value="5">5th Semester</SelectItem>
                    <SelectItem value="6">6th Semester</SelectItem>
                    <SelectItem value="7">7th Semester</SelectItem>
                    <SelectItem value="8">8th Semester</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="section">Section (Optional)</Label>
                <Select
                  value={formData.section}
                  onValueChange={(value) => handleInputChange('section', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All sections" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sections</SelectItem>
                    <SelectItem value="A">Section A</SelectItem>
                    <SelectItem value="B">Section B</SelectItem>
                    <SelectItem value="C">Section C</SelectItem>
                    <SelectItem value="D">Section D</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  id="start_date"
                  type="datetime-local"
                  value={formData.start_date}
                  onChange={(e) => handleInputChange('start_date', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Can be a past date for retrospective feedback
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  id="end_date"
                  type="datetime-local"
                  value={formData.end_date}
                  onChange={(e) => handleInputChange('end_date', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  When students can no longer submit feedback
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Session'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateFeedbackSessionDialog;
