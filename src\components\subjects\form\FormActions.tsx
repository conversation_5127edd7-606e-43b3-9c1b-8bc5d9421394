
import React from "react";
import { But<PERSON> } from "@/components/ui/button";

interface FormActionsProps {
  onCancel: () => void;
  saving: boolean;
  filtersValid: boolean;
}

export default function FormActions({ onCancel, saving, filtersValid }: FormActionsProps) {
  return (
    <div className="flex justify-end space-x-2 pt-4">
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
        disabled={saving}
      >
        Cancel
      </Button>
      <Button type="submit" disabled={saving || !filtersValid}>
        {saving ? "Saving..." : "Save Subject Mapping"}
      </Button>
    </div>
  );
}
