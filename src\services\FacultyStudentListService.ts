import { supabase } from '@/integrations/supabase/client';

export interface StudentInfo {
  id: string;
  usn: string;
  student_name: string;
  department: string;
  semester: string;
  section: string;
  batch_name?: string;
  elective_code?: string;
  source_type: 'class' | 'batch' | 'elective';
}

export interface FacultyTeachingAssignment {
  subject_code: string;
  subject_name: string;
  subject_type: string;
  semester: string;
  section: string;
  department: string;
  batch_name?: string;
  day?: string;
  time_slot?: string;
  faculty_id: string;
  faculty2_id?: string;
}

export class FacultyStudentListService {
  /**
   * Map department names for database queries
   */
  private static mapDepartmentName(userDepartment: string): string {
    const departmentMap: Record<string, string> = {
      'Computer Science and Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Electrical and Electronics Engineering': 'eee'
    };

    return departmentMap[userDepartment] || userDepartment.toLowerCase();
  }

  /**
   * Debug function to check what student data exists in the database
   */
  static async debugStudentTables(): Promise<void> {
    try {
      console.log('🔍 === DEBUGGING STUDENT TABLES ===');

      // Check class_students table
      const { data: classStudents, error: classError } = await supabase
        .from('class_students')
        .select('department, semester, section, usn, student_name')
        .limit(50);

      if (!classError && classStudents) {
        console.log('📊 class_students table sample:', classStudents.slice(0, 5));

        // Group by department, semester, section
        const groupedClass = classStudents.reduce((acc: any, student: any) => {
          const key = `${student.department} - Sem ${student.semester} Sec ${student.section}`;
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {});
        console.log('📊 class_students grouped by dept/sem/sec:', groupedClass);

        // Show total count
        console.log(`📊 Total records in class_students: ${classStudents.length}`);
      } else {
        console.log('❌ Error or no data in class_students:', classError);
      }

      // Note: students table doesn't exist in this database, skipping

      // Check batch_students table
      const { data: batchStudents, error: batchError } = await supabase
        .from('batch_students')
        .select('department, semester, section, usn, student_name, batch_name')
        .limit(50);

      if (!batchError && batchStudents) {
        console.log('📊 batch_students table sample:', batchStudents.slice(0, 5));

        // Group by department, semester, section
        const groupedBatch = batchStudents.reduce((acc: any, student: any) => {
          const key = `${student.department} - Sem ${student.semester} Sec ${student.section}`;
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {});
        console.log('📊 batch_students grouped by dept/sem/sec:', groupedBatch);

        // Show total count
        console.log(`📊 Total records in batch_students: ${batchStudents.length}`);
      } else {
        console.log('❌ Error or no data in batch_students:', batchError);
      }

      console.log('🔍 === END DEBUGGING ===');
    } catch (error) {
      console.error('❌ Error debugging student tables:', error);
    }
  }

  /**
   * Reverse map department names for student queries
   */
  private static reverseMapDepartmentName(shortDepartment: string): string {
    const reverseDepartmentMap: Record<string, string> = {
      'cse': 'Computer Science and Engineering',
      'ise': 'Information Science and Engineering',
      'ece': 'Electronics and Communication Engineering',
      'mech': 'Mechanical Engineering',
      'civil': 'Civil Engineering',
      'eee': 'Electrical and Electronics Engineering'
    };
    
    return reverseDepartmentMap[shortDepartment] || shortDepartment;
  }

  /**
   * Get all teaching assignments for a faculty member
   */
  static async getFacultyTeachingAssignments(
    facultyId: string,
    userDepartment: string
  ): Promise<FacultyTeachingAssignment[]> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      const { data: assignments, error } = await supabase
        .from('timetable_slots')
        .select(`
          subject_code,
          subject_name,
          subject_type,
          semester,
          section,
          department,
          batch_name,
          day,
          time_slot,
          faculty_id,
          faculty2_id
        `)
        .eq('faculty_id', facultyId)  // Only primary faculty assignments
        .eq('department', mappedDepartment);

      if (error) throw error;

      return assignments || [];
    } catch (error) {
      console.error('Error fetching faculty teaching assignments:', error);
      throw error;
    }
  }

  /**
   * Get students for theory subjects (complete class list)
   */
  static async getTheoryStudents(
    semester: string,
    section: string,
    userDepartment: string
  ): Promise<StudentInfo[]> {
    try {
      console.log('🔍 Loading theory students with parameters:', {
        semester,
        section,
        userDepartment
      });

      // Try multiple department name variations to ensure we find students
      const departmentVariations = [
        userDepartment, // Original department name
        this.mapDepartmentName(userDepartment), // Short code (e.g., 'cse')
        this.reverseMapDepartmentName(this.mapDepartmentName(userDepartment)) // Full name
      ];

      console.log('🔍 Trying department variations:', departmentVariations);

      let students: any[] = [];
      let successfulDepartment = '';

      // Try each department variation until we find students
      for (const deptVariation of departmentVariations) {
        console.log(`🔍 Querying class_students with department: "${deptVariation}"`);

        const { data: queryResult, error } = await supabase
          .from('class_students')
          .select('id, usn, student_name, department, semester, section')
          .eq('department', deptVariation)
          .eq('semester', semester)
          .eq('section', section)
          .order('usn');

        if (error) {
          console.error(`❌ Error querying with department "${deptVariation}":`, error);
          continue;
        }

        if (queryResult && queryResult.length > 0) {
          students = queryResult;
          successfulDepartment = deptVariation;
          console.log(`✅ Found ${students.length} students with department: "${deptVariation}"`);
          break;
        } else {
          console.log(`⚠️ No students found with department: "${deptVariation}"`);
        }
      }

      if (students.length === 0) {
        console.error('❌ No students found in class_students table for any department variation');
        console.log('🔍 Debug info:', {
          semester,
          section,
          userDepartment,
          departmentVariations,
          tableName: 'class_students'
        });

        // Additional debugging: Check what data exists in the table
        const { data: allDepartments, error: deptError } = await supabase
          .from('class_students')
          .select('department')
          .eq('semester', semester)
          .eq('section', section);

        if (!deptError && allDepartments) {
          const uniqueDepartments = [...new Set(allDepartments.map(d => d.department))];
          console.log('🔍 Available departments in class_students for this semester/section:', uniqueDepartments);
        }

        // Check what semesters and sections exist for this department
        const { data: semesterSections, error: semSecError } = await supabase
          .from('class_students')
          .select('semester, section, department')
          .in('department', departmentVariations);

        if (!semSecError && semesterSections) {
          const uniqueCombinations = [...new Set(semesterSections.map(s => `${s.department} - Sem ${s.semester} Sec ${s.section}`))];
          console.log('🔍 Available semester/section combinations in class_students:', uniqueCombinations);
        }

        // Check if there are students in batch_students table as fallback for theory subjects
        console.log('🔍 Checking batch_students table as fallback for theory subjects...');
        const { data: batchStudents, error: batchError } = await supabase
          .from('batch_students')
          .select('id, usn, student_name, department, semester, section')
          .in('department', departmentVariations)
          .eq('semester', semester)
          .eq('section', section)
          .order('usn');

        if (!batchError && batchStudents && batchStudents.length > 0) {
          console.log(`✅ Found ${batchStudents.length} students in batch_students table as fallback`);
          console.log('📋 Using batch_students as source for theory subject (class_students is empty)');
          return batchStudents.map(student => ({
            ...student,
            source_type: 'batch_fallback' as const
          }));
        } else {
          console.log('❌ No students found in batch_students table either');
        }
      }

      console.log(`📊 Final result: ${students.length} theory students loaded from department "${successfulDepartment}"`);

      return (students || []).map(student => ({
        ...student,
        source_type: 'class' as const
      }));
    } catch (error) {
      console.error('❌ Error fetching theory students:', error);
      throw error;
    }
  }

  /**
   * Get students for lab subjects based on batch and time slot
   */
  static async getLabStudents(
    semester: string,
    section: string,
    subjectCode: string,
    day: string,
    timeSlot: string,
    userDepartment: string
  ): Promise<StudentInfo[]> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);
      const studentDepartment = this.reverseMapDepartmentName(mappedDepartment);

      // First, find which batch has lab at this day/time for this subject
      const { data: labSlots, error: labError } = await supabase
        .from('timetable_slots')
        .select('batch_name')
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('subject_type', 'laboratory')
        .eq('day', day)
        .eq('time_slot', timeSlot)
        .not('batch_name', 'is', null);

      if (labError) throw labError;

      if (!labSlots || labSlots.length === 0) {
        console.log('No lab batch found for this time slot');
        return [];
      }

      const batchName = labSlots[0].batch_name;

      // Get students from the specific batch
      const { data: batchStudents, error: batchError } = await supabase
        .from('batch_students')
        .select(`
          student_id,
          batch_name,
          class_students!inner(id, usn, student_name, department, semester, section)
        `)
        .eq('batch_name', batchName)
        .eq('semester', semester)
        .eq('section', section);

      if (batchError) throw batchError;

      return (batchStudents || []).map(bs => ({
        id: bs.class_students.id,
        usn: bs.class_students.usn,
        student_name: bs.class_students.student_name,
        department: bs.class_students.department,
        semester: bs.class_students.semester,
        section: bs.class_students.section,
        batch_name: bs.batch_name,
        source_type: 'batch' as const
      }));
    } catch (error) {
      console.error('Error fetching lab students:', error);
      throw error;
    }
  }

  /**
   * Get students for elective subjects based on enrollment
   */
  static async getElectiveStudents(
    semester: string,
    section: string,
    subjectCode: string,
    userDepartment: string
  ): Promise<StudentInfo[]> {
    try {
      const studentDepartment = this.reverseMapDepartmentName(this.mapDepartmentName(userDepartment));

      // Get students enrolled in this specific elective
      const { data: electiveStudents, error: electiveError } = await supabase
        .from('elective_students')
        .select(`
          student_id,
          elective_code,
          class_students!inner(id, usn, student_name, department, semester, section)
        `)
        .eq('elective_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section);

      if (electiveError) throw electiveError;

      return (electiveStudents || []).map(es => ({
        id: es.class_students.id,
        usn: es.class_students.usn,
        student_name: es.class_students.student_name,
        department: es.class_students.department,
        semester: es.class_students.semester,
        section: es.class_students.section,
        elective_code: es.elective_code,
        source_type: 'elective' as const
      }));
    } catch (error) {
      console.error('Error fetching elective students:', error);
      throw error;
    }
  }

  /**
   * Get appropriate student list based on subject type and context
   */
  static async getStudentsForFacultySubject(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    subjectType: string,
    day?: string,
    timeSlot?: string
  ): Promise<StudentInfo[]> {
    try {
      console.log('🔍 Getting students for faculty subject:', {
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        subjectType,
        day,
        timeSlot
      });

      let students: StudentInfo[] = [];

      switch (subjectType.toLowerCase()) {
        case 'theory':
          console.log('📚 Loading theory students...');
          students = await this.getTheoryStudents(semester, section, userDepartment);
          break;

        case 'laboratory':
        case 'lab':
          console.log('🧪 Loading lab students...');
          if (!day || !timeSlot) {
            throw new Error('Day and time slot are required for lab subjects');
          }
          students = await this.getLabStudents(semester, section, subjectCode, day, timeSlot, userDepartment);
          break;

        case 'elective':
          console.log('📖 Loading elective students...');
          students = await this.getElectiveStudents(semester, section, subjectCode, userDepartment);
          break;

        default:
          console.warn(`⚠️ Unknown subject type: ${subjectType}, defaulting to theory`);
          students = await this.getTheoryStudents(semester, section, userDepartment);
          break;
      }

      console.log(`✅ Successfully loaded ${students.length} students for ${subjectType} subject`);

      if (students.length === 0) {
        console.warn('⚠️ No students found - this might indicate a data issue');
      }

      return students;
    } catch (error) {
      console.error('❌ Error getting students for faculty subject:', error);
      throw error;
    }
  }

  /**
   * Get current day's lab batch for a faculty member
   */
  static async getCurrentDayLabBatch(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    currentDay: string
  ): Promise<string | null> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      const { data: labSlots, error } = await supabase
        .from('timetable_slots')
        .select('batch_name, time_slot')
        .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('subject_type', 'laboratory')
        .eq('day', currentDay)
        .not('batch_name', 'is', null)
        .limit(1);

      if (error) throw error;

      return labSlots && labSlots.length > 0 ? labSlots[0].batch_name : null;
    } catch (error) {
      console.error('Error getting current day lab batch:', error);
      return null;
    }
  }

  /**
   * Validate if faculty teaches the given subject
   */
  static async validateFacultySubjectAccess(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    subjectType: string
  ): Promise<boolean> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      console.log('🔍 Validating faculty subject access:', {
        facultyId,
        userDepartment,
        mappedDepartment,
        subjectCode,
        semester,
        section,
        subjectType
      });

      const { data: assignments, error } = await supabase
        .from('timetable_slots')
        .select('id, faculty_id, faculty2_id, subject_code, subject_name, subject_type, department')
        .eq('faculty_id', facultyId)  // Only primary faculty assignments
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('subject_type', subjectType)
        .limit(1);

      if (error) {
        console.error('❌ Error validating faculty subject access:', error);
        throw error;
      }

      const hasAccess = assignments && assignments.length > 0;

      if (hasAccess) {
        console.log('✅ Faculty has access to subject:', assignments[0]);
      } else {
        console.log('❌ Faculty does not have access to subject');

        // Additional debugging: Check what subjects this faculty teaches
        const { data: allAssignments, error: allError } = await supabase
          .from('timetable_slots')
          .select('subject_code, subject_name, subject_type, semester, section, department')
          .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`)
          .eq('department', mappedDepartment);

        if (!allError && allAssignments) {
          console.log('🔍 All subjects taught by this faculty:', allAssignments);
        }
      }

      return hasAccess;
    } catch (error) {
      console.error('❌ Error validating faculty subject access:', error);
      return false;
    }
  }
}
