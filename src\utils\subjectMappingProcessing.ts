
import * as XLSX from 'xlsx';
import { readFileAsArrayBuffer } from './fileCore';

/**
 * Processes Excel data specifically for subject mapping format
 * Enhanced to use section information for batch naming and properly extract both batch sets
 */
export const processSubjectMappingExcel = async (file: File): Promise<{
  mappings: Record<string, any>[];
  labSlots: Record<string, any>[];
}> => {
  const data = await readFileAsArrayBuffer(file);
  const workbook = XLSX.read(data, { type: 'array' });
  const sheet = workbook.Sheets[workbook.SheetNames[0]];
  const rawData = XLSX.utils.sheet_to_json<Record<string, any>>(sheet, { defval: null });
  
  console.log("Raw Subject Mapping data:", rawData);
  
  // Try to detect section from data
  let section = "A"; // Default section
  
  // Check if section is specified in the Excel file header row or metadata
  const sectionKey = Object.keys(rawData[0] || {}).find(k => 
    k.toLowerCase().includes('section') || k.toLowerCase() === 'sec'
  );
  
  if (sectionKey && typeof rawData[0][sectionKey] === 'string') {
    // Extract just the letter part if it's like "Section A" or "A Section"
    const match = rawData[0][sectionKey].match(/[A-Z]/i);
    if (match) {
      section = match[0].toUpperCase();
    }
  }
  
  const mappings: Record<string, any>[] = [];
  const labSlots: Record<string, any>[] = [];
  
  // Process each row in the Excel file
  rawData.forEach((row, index) => {
    // Check if this row defines a section
    if (row['Section']) {
      const secValue = String(row['Section']);
      if (secValue && secValue.length > 0) {
        const match = secValue.match(/[A-Z]/i);
        if (match) {
          section = match[0].toUpperCase();
        }
      }
    }
    
    // Extract the basic mapping data
    const subjectCode = row['Subject Code'];
    const subjectName = row['Subject Name'];
    const faculty1 = row['Faculty 1'];
    const faculty2 = row['Faculty 2'] || null;
    const hoursPerWeek = row['Hours/Week'];
    const classroom = row['Class Room'];
    const slotsPerWeek = row['Slots/Week'] || 0;
    
    // Determine if it's a theory or laboratory subject
    // Logic: If faculty2 exists or slotsPerWeek > 0 or subject name contains LAB, it's a lab subject
    const isLab = Boolean(faculty2 || 
                         slotsPerWeek > 0 || 
                         (typeof subjectName === 'string' && subjectName.toUpperCase().includes('LAB')));
    
    // Create the base mapping
    const mapping = {
      subject_code: subjectCode,
      subject_name: subjectName,
      faculty1: faculty1,
      faculty2: faculty2,
      hours_per_week: hoursPerWeek,
      classroom: classroom,
      subject_type: isLab ? 'laboratory' : 'theory',
      slots_per_week: isLab ? (slotsPerWeek || 1) : undefined,
    };
    
    mappings.push(mapping);
    
    // Process lab slots if it's a lab subject
    if (isLab) {
      // IMPROVED LOGIC FOR BATCH DETECTION
      // Instead of using pattern matching with position-based inference,
      // we'll directly detect the first and second batch columns more reliably
      
      // For first batch
      let batch1Name, batch1Day, batch1Time;
      // For second batch
      let batch2Name, batch2Day, batch2Time;
      
      // First, try to find columns that explicitly contain "Batch" 
      const allKeys = Object.keys(row);
      
      // Get all batch name keys
      const batchNameKeys = allKeys.filter(key => 
        key.toLowerCase().includes('batch') && !key.toLowerCase().includes('size')
      );
      
      // Get all day keys
      const dayKeys = allKeys.filter(key => 
        key.toLowerCase().includes('day')
      );
      
      // Get all time keys
      const timeKeys = allKeys.filter(key => 
        key.toLowerCase().includes('time')
      );
      
      // If we found multiple batch columns
      if (batchNameKeys.length >= 1) {
        batch1Name = { key: batchNameKeys[0], value: row[batchNameKeys[0]] };
      }
      
      if (batchNameKeys.length >= 2) {
        batch2Name = { key: batchNameKeys[1], value: row[batchNameKeys[1]] };
      }
      
      // Same for days
      if (dayKeys.length >= 1) {
        batch1Day = { key: dayKeys[0], value: row[dayKeys[0]] };
      }
      
      if (dayKeys.length >= 2) {
        batch2Day = { key: dayKeys[1], value: row[dayKeys[1]] };
      }
      
      // And times
      if (timeKeys.length >= 1) {
        batch1Time = { key: timeKeys[0], value: row[timeKeys[0]] };
      }
      
      if (timeKeys.length >= 2) {
        batch2Time = { key: timeKeys[1], value: row[timeKeys[1]] };
      }
      
      // Fallback to older approach if we didn't find explicit columns
      if (!batch1Name || !batch1Day) {
        batch1Name = findColumnValue(row, ['Batch Name', 'Batch 1', 'Batch-1', 'Batch']);
        batch1Day = findColumnValue(row, ['Day', 'Day 1', 'Day-1']);
        batch1Time = findColumnValue(row, ['Time Slot', 'Time 1', 'Time-1', 'Time']);
      }
      
      if (!batch2Name || !batch2Day) {
        batch2Name = findColumnValue(row, ['Batch Name.1', 'Batch 2', 'Batch-2', 'Batch.1']);
        batch2Day = findColumnValue(row, ['Day.1', 'Day 2', 'Day-2', 'Day2']);
        batch2Time = findColumnValue(row, ['Time Slot.1', 'Time 2', 'Time-2', 'Time.1', 'TimeSlot.1']);
      }
      
      console.log(`Row ${index} - Lab subject: ${subjectName}, Section: ${section}`);
      console.log(`  Batch 1: ${JSON.stringify({ name: batch1Name, day: batch1Day, time: batch1Time })}`);
      console.log(`  Batch 2: ${JSON.stringify({ name: batch2Name, day: batch2Day, time: batch2Time })}`);
      
      // Generate section-specific batch names
      const defaultBatch1 = `${section}1`;
      const defaultBatch2 = `${section}2`;
      
      // Add Batch 1 if data exists
      if (batch1Day && batch1Day.value) {
        labSlots.push({
          parentRowIndex: index,
          slot_order: 1,
          batch_name: batch1Name?.value || defaultBatch1,
          day: batch1Day.value,
          time_of_day: batch1Time?.value || 'Morning',
        });
      }
      
      // Add Batch 2 if data exists - FIXED to properly use the second batch data
      if (batch2Day && batch2Day.value) {
        labSlots.push({
          parentRowIndex: index,
          slot_order: 2,
          batch_name: batch2Name?.value || defaultBatch2,
          day: batch2Day.value, 
          time_of_day: batch2Time?.value || 'Afternoon',
        });
      }
      
      // If no lab slots were found for a lab subject, add default slots
      if (labSlots.filter(slot => slot.parentRowIndex === index).length === 0) {
        console.warn(`No lab slots found for ${subjectName}, adding default slots with section ${section}`);
        labSlots.push({
          parentRowIndex: index,
          slot_order: 1,
          batch_name: defaultBatch1,
          day: 'Monday',
          time_of_day: 'Morning',
        });
        
        labSlots.push({
          parentRowIndex: index,
          slot_order: 2,
          batch_name: defaultBatch2,
          day: 'Tuesday',
          time_of_day: 'Afternoon',
        });
      }
      // If only one batch was found, add a second one with complementary schedule
      else if (labSlots.filter(slot => slot.parentRowIndex === index).length === 1) {
        console.warn(`Only one batch found for ${subjectName}, adding complementary batch for section ${section}`);
        const existingBatch = labSlots.find(slot => slot.parentRowIndex === index);
        const complementaryDay = existingBatch?.day === 'Monday' ? 'Tuesday' : 
                             existingBatch?.day === 'Tuesday' ? 'Wednesday' :
                             existingBatch?.day === 'Wednesday' ? 'Thursday' :
                             existingBatch?.day === 'Thursday' ? 'Friday' : 'Monday';
        
        const complementaryTime = existingBatch?.time_of_day === 'Morning' ? 'Afternoon' : 'Morning';
        
        // Check if existing batch is the first or second batch
        const isFirstBatch = existingBatch?.batch_name?.includes('1') || !existingBatch?.batch_name?.includes('2');
        
        labSlots.push({
          parentRowIndex: index,
          slot_order: 2,
          batch_name: isFirstBatch ? defaultBatch2 : defaultBatch1,
          day: complementaryDay,
          time_of_day: complementaryTime,
        });
      }
    }
  });
  
  console.log("Processed mappings:", mappings);
  console.log("Processed lab slots:", labSlots);
  
  return { mappings, labSlots };
};

/**
 * Find column value for a specific set of key patterns
 * Helper function for subject mapping batch data extraction
 */
function findColumnValue(
  row: Record<string, any>, 
  patterns: string[]
): { key: string, value: any } | null {
  const keys = Object.keys(row).filter(key => {
    return patterns.some(pattern => {
      // First try exact match
      if (key === pattern) return true;
      
      // Then try case-insensitive matches
      if (key.toLowerCase() === pattern.toLowerCase()) return true;
      
      // Then try contains match
      if (key.toLowerCase().includes(pattern.toLowerCase())) return true;
      
      return false;
    });
  });
  
  if (keys.length > 0) {
    return { key: keys[0], value: row[keys[0]] };
  }
  return null;
}
