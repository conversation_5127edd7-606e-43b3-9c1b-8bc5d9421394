
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Employee, UserService } from '@/services/UserService';
import { Button } from '@/components/ui/button';
import { Edit2 } from 'lucide-react';
import RoleSelector from './RoleSelector';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface RoleEditCellProps {
  employeeId: string;
  roles: string[];
  onUpdate: (updatedEmployee: Employee) => void;
}

const RoleEditCell: React.FC<RoleEditCellProps> = ({ employeeId, roles, onUpdate }) => {
  const handleSaveRoles = async (selectedRoles: string[]) => {
    try {
      const updatedEmployee = await UserService.updateEmployeeRoles(employeeId, selectedRoles);
      onUpdate(updatedEmployee);
    } catch (error) {
      console.error('Error updating roles:', error);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'principal':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 border-purple-200 dark:border-purple-800';
      case 'hod':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border-blue-200 dark:border-blue-800';
      case 'class_teacher':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800';
      case 'coordinator':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400 border-amber-200 dark:border-amber-800';
      case 'faculty':
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-1 group">
      {roles.length > 0 ? (
        <>
          {roles.map((role) => (
            <Badge 
              key={role} 
              variant="outline" 
              className={`${getRoleBadgeColor(role)} border-0`}
            >
              {role.charAt(0).toUpperCase() + role.slice(1).replace('_', ' ')}
            </Badge>
          ))}
        </>
      ) : (
        <span className="text-muted-foreground italic text-sm">No roles assigned</span>
      )}
      
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 ml-1"
          >
            <Edit2 className="h-3 w-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <RoleSelector
            employeeId={employeeId}
            initialSelectedRoles={roles}
            onSave={handleSaveRoles}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default RoleEditCell;
