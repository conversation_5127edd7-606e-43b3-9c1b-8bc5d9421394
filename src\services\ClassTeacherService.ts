import { supabase } from "@/integrations/supabase/client";

export interface ClassTeacher {
  id: string;
  faculty_id: string;
  academic_year: string;
  department: string;
  semester: string;
  section: string;
  assigned_at: string;
  assigned_by?: string;
  created_at: string;
  updated_at: string;
  faculty?: {
    id: string;
    full_name: string;
    email: string;
    department?: string;
    designation?: string;
  };
}

export interface FacultyForSection {
  id: string;
  full_name: string;
  email: string;
  department?: string;
  designation?: string;
  subjects: string[];
  isClassTeacher: boolean;
}

export class ClassTeacherService {
  /**
   * Get class teacher assignment for a faculty member
   */
  static async getClassTeacherAssignment(
    facultyId: string,
    academicYear: string
  ): Promise<{ department: string; semester: string; section: string; academic_year: string } | null> {
    try {
      console.log('🔍 Fetching class teacher assignment:', { facultyId, academicYear });

      const { data, error } = await supabase
        .from('class_teachers')
        .select('department, semester, section, academic_year')
        .eq('faculty_id', facultyId)
        .eq('academic_year', academicYear)
        .single();

      console.log('🔍 Class teacher query result:', { data, error });

      if (error) {
        if (error.code === 'PGRST116') {
          // No assignment found
          console.log('🔍 No class teacher assignment found for:', { facultyId, academicYear });
          return null;
        }
        throw error;
      }

      console.log('🔍 Found class teacher assignment:', data);
      return data;
    } catch (error) {
      console.error('Error fetching class teacher assignment:', error);
      throw error;
    }
  }

  /**
   * Normalize department values to handle different formats
   */
  private static normalizeDepartment(department: string): string[] {
    if (!department) return [];

    // Create variations of the department name to try
    const variations = [department];

    // Common department mappings
    const departmentMappings: Record<string, string[]> = {
      'cse': ['cse', 'Computer Science & Engineering', 'Computer Science and Engineering', 'CSE'],
      'ece': ['ece', 'Electronics & Communication Engineering', 'Electronics and Communication Engineering', 'ECE'],
      'mech': ['mech', 'Mechanical Engineering', 'MECH'],
      'eee': ['eee', 'Electrical & Electronics Engineering', 'Electrical and Electronics Engineering', 'EEE'],
      'civil': ['civil', 'Civil Engineering', 'CIVIL'],
      'it': ['it', 'Information Technology', 'IT'],
    };

    // If the input matches any variation, return all variations for that department
    for (const [key, values] of Object.entries(departmentMappings)) {
      if (values.some(v => v.toLowerCase() === department.toLowerCase())) {
        return values;
      }
    }

    // If no mapping found, return the original value
    return [department];
  }

  /**
   * Get faculty members teaching in a specific semester-section for the user's department
   */
  static async getFacultyForSection(
    academicYear: string,
    userDepartment: string,
    semester: string,
    section: string
  ): Promise<FacultyForSection[]> {
    try {
      console.log('ClassTeacherService.getFacultyForSection called with:', {
        academicYear,
        userDepartment,
        semester,
        section
      });

      // Validate department parameter
      if (!userDepartment) {
        throw new Error('User department is required for faculty lookup');
      }

      // Get faculty teaching in this semester-section from subject mappings
      // Only show faculty from the user's department
      console.log('🔍 Querying simplified_subject_faculty_mappings with filters:', {
        academic_year: academicYear,
        department: userDepartment,
        semester: semester,
        section: section
      });

      // CRITICAL DEBUG: Let's also check what department values exist in the table
      console.log('🔍 Checking what department values exist in simplified_subject_faculty_mappings...');
      const { data: allDepts, error: allDeptsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('department')
        .eq('academic_year', academicYear)
        .limit(10);

      console.log('📋 Available departments in table:', {
        departments: allDepts?.map(d => d.department),
        error: allDeptsError
      });

      // Get department variations to try
      const departmentVariations = this.normalizeDepartment(userDepartment);
      console.log('🔄 Trying department variations:', departmentVariations);

      let mappings = null;
      let mappingsError = null;

      // Try each department variation until we find a match
      for (const deptVariation of departmentVariations) {
        console.log(`🔍 Trying department variation: "${deptVariation}"`);

        const { data: mappingsData, error: mappingsErr } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select(`
            faculty_1_id,
            faculty_2_id,
            subject_name,
            subject_code,
            academic_year,
            department,
            semester,
            section
          `)
          .eq('academic_year', academicYear)
          .eq('department', deptVariation)
          .eq('semester', semester)
          .eq('section', section)
          .not('subject_code', 'like', 'DELETED_%');

        if (!mappingsErr && mappingsData && mappingsData.length > 0) {
          console.log(`✅ Found ${mappingsData.length} mappings with department variation: "${deptVariation}"`);
          mappings = mappingsData;
          mappingsError = mappingsErr;
          break;
        } else {
          console.log(`❌ No mappings found for department variation: "${deptVariation}"`);
        }
      }

      console.log('📊 Mappings query result:', {
        mappingsCount: mappings?.length || 0,
        mappings: mappings,
        mappingsError: mappingsError
      });

      if (mappingsError) {
        console.error('❌ Error in mappings query:', mappingsError);
        throw mappingsError;
      }

      // If no mappings found, let's check if there are any mappings at all for this department
      if (!mappings || mappings.length === 0) {
        console.log('⚠️ No mappings found for the specified filters. Checking if any mappings exist for this department...');

        const { data: allDeptMappings, error: allDeptError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select('academic_year, semester, section, subject_code')
          .eq('department', userDepartment)
          .not('subject_code', 'like', 'DELETED_%')
          .limit(10);

        console.log('📋 Sample mappings for department:', {
          department: userDepartment,
          sampleMappings: allDeptMappings,
          error: allDeptError
        });
      }

      // Get unique faculty IDs
      const facultyIds = new Set<string>();
      const facultySubjects: Record<string, string[]> = {};

      mappings?.forEach(mapping => {
        if (mapping.faculty_1_id) {
          facultyIds.add(mapping.faculty_1_id);
          if (!facultySubjects[mapping.faculty_1_id]) {
            facultySubjects[mapping.faculty_1_id] = [];
          }
          facultySubjects[mapping.faculty_1_id].push(`${mapping.subject_code} - ${mapping.subject_name}`);
        }
        if (mapping.faculty_2_id) {
          facultyIds.add(mapping.faculty_2_id);
          if (!facultySubjects[mapping.faculty_2_id]) {
            facultySubjects[mapping.faculty_2_id] = [];
          }
          facultySubjects[mapping.faculty_2_id].push(`${mapping.subject_code} - ${mapping.subject_name}`);
        }
      });

      console.log('Faculty IDs found:', Array.from(facultyIds));
      console.log('Faculty subjects mapping:', facultySubjects);

      if (facultyIds.size === 0) {
        console.log('No faculty IDs found from subject mappings, will use fallback');
        // Don't return empty array here, let the fallback logic handle it
      }

      // Get faculty details - include all faculty who teach this section regardless of their department
      let faculty = null;
      let facultyError = null;

      if (facultyIds.size > 0) {
        console.log('🔍 Querying employee_details for faculty IDs:', {
          facultyIds: Array.from(facultyIds),
          userDepartment: userDepartment
        });

        // Get faculty details without department filtering since they're already teaching this section
        const { data: facultyData, error: facultyErr } = await supabase
          .from('employee_details')
          .select('id, full_name, email, department, designation')
          .in('id', Array.from(facultyIds));

        faculty = facultyData;
        facultyError = facultyErr;

        console.log('👥 Faculty query result:', {
          facultyCount: faculty?.length || 0,
          faculty: faculty,
          facultyError: facultyError
        });

        if (facultyError) {
          console.error('❌ Error in faculty query:', facultyError);
          throw facultyError;
        }
      } else {
        console.log('🔄 No faculty IDs found from subject mappings, will use fallback');
      }

      // Get current class teacher for this section using department variations
      let classTeacher = null;
      let classTeacherError = null;

      for (const deptVariation of departmentVariations) {
        console.log(`🔍 Trying to find class teacher with department variation: "${deptVariation}"`);

        const { data: classTeacherData, error: classTeacherErr } = await supabase
          .from('class_teachers')
          .select('faculty_id')
          .eq('academic_year', academicYear)
          .eq('department', deptVariation)
          .eq('semester', semester)
          .eq('section', section)
          .single();

        if (!classTeacherErr && classTeacherData) {
          console.log(`✅ Found class teacher with department variation: "${deptVariation}"`);
          classTeacher = classTeacherData;
          classTeacherError = classTeacherErr;
          break;
        } else if (classTeacherErr?.code !== 'PGRST116') { // PGRST116 is "not found" error
          console.log(`❌ Error finding class teacher for department variation: "${deptVariation}"`, classTeacherErr);
        } else {
          console.log(`❌ No class teacher found for department variation: "${deptVariation}"`);
        }
      }

      const currentClassTeacherId = classTeacher?.faculty_id;

      // Combine data
      let result = faculty?.map(f => ({
        ...f,
        subjects: facultySubjects[f.id] || [],
        isClassTeacher: f.id === currentClassTeacherId
      })) || [];

      // If no faculty found teaching in this semester-section, provide fallback
      if (!faculty || faculty.length === 0 || result.length === 0) {
        console.log('🔄 No faculty found teaching in this semester-section. Providing fallback options...');

        // Get all faculty from the user's department as fallback
        let allDeptFaculty = null;
        let allDeptFacultyError = null;

        for (const deptVariation of departmentVariations) {
          console.log(`🔍 Trying fallback faculty with department variation: "${deptVariation}"`);

          const { data: fallbackFacultyData, error: fallbackFacultyErr } = await supabase
            .from('employee_details')
            .select('id, full_name, email, department, designation')
            .eq('department', deptVariation)
            .contains('roles', ['faculty']);

          if (!fallbackFacultyErr && fallbackFacultyData && fallbackFacultyData.length > 0) {
            console.log(`✅ Found ${fallbackFacultyData.length} fallback faculty with department variation: "${deptVariation}"`);
            allDeptFaculty = fallbackFacultyData;
            allDeptFacultyError = fallbackFacultyErr;
            break;
          } else {
            console.log(`❌ No fallback faculty found for department variation: "${deptVariation}"`);
          }
        }

        if (!allDeptFacultyError && allDeptFaculty) {
          result = allDeptFaculty.map(f => ({
            ...f,
            subjects: ['No subjects assigned for this semester-section'],
            isClassTeacher: f.id === currentClassTeacherId
          }));

          console.log('📋 Fallback faculty from department:', result);
        }
      }

      console.log('✅ Final result:', result);
      return result;

    } catch (error) {
      console.error('Error fetching faculty for section:', error);
      throw error;
    }
  }

  /**
   * Assign a faculty member as class teacher for a semester-section
   */
  static async assignClassTeacher(
    facultyId: string,
    academicYear: string,
    userDepartment: string,
    semester: string,
    section: string,
    assignedBy?: string
  ): Promise<ClassTeacher> {
    try {
      // First, remove any existing class teacher for this section
      await this.removeClassTeacher(academicYear, userDepartment, semester, section);

      // Validate that faculty exists and is eligible for assignment
      // Check if faculty teaches any subjects for this department's classes
      const departmentVariations = this.normalizeDepartment(userDepartment);
      let facultyIsEligible = false;

      // First check if faculty teaches any subjects for this department
      for (const deptVariation of departmentVariations) {
        const { data: mappingCheck, error: mappingError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select('id')
          .eq('department', deptVariation)
          .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`)
          .limit(1);

        if (!mappingError && mappingCheck && mappingCheck.length > 0) {
          facultyIsEligible = true;
          break;
        }
      }

      // If not found in mappings, check if faculty belongs to the department
      if (!facultyIsEligible) {
        for (const deptVariation of departmentVariations) {
          const { data: facultyCheck, error: facultyCheckError } = await supabase
            .from('employee_details')
            .select('id')
            .eq('id', facultyId)
            .eq('department', deptVariation)
            .single();

          if (!facultyCheckError && facultyCheck) {
            facultyIsEligible = true;
            break;
          }
        }
      }

      if (!facultyIsEligible) {
        throw new Error('Faculty member is not eligible for class teacher assignment in this department');
      }

      // Insert new class teacher assignment
      const { data, error } = await supabase
        .from('class_teachers')
        .insert({
          faculty_id: facultyId,
          academic_year: academicYear,
          department: userDepartment,
          semester: semester,
          section: section,
          assigned_by: assignedBy
        })
        .select(`
          *,
          faculty:employee_details(id, full_name, email, department, designation)
        `)
        .single();

      if (error) throw error;

      // Update faculty roles to include 'class_teacher'
      await this.updateFacultyRoles(facultyId, true);

      return data;
    } catch (error) {
      console.error('Error assigning class teacher:', error);
      throw error;
    }
  }

  /**
   * Remove class teacher assignment for a semester-section
   */
  static async removeClassTeacher(
    academicYear: string,
    userDepartment: string,
    semester: string,
    section: string
  ): Promise<void> {
    try {
      // Get current class teacher to update their roles
      const { data: currentClassTeacher } = await supabase
        .from('class_teachers')
        .select('faculty_id')
        .eq('academic_year', academicYear)
        .eq('department', userDepartment)
        .eq('semester', semester)
        .eq('section', section)
        .single();

      // Delete the assignment
      const { error } = await supabase
        .from('class_teachers')
        .delete()
        .eq('academic_year', academicYear)
        .eq('department', userDepartment)
        .eq('semester', semester)
        .eq('section', section);

      if (error) throw error;

      // Update faculty roles to remove 'class_teacher' if they're not class teacher elsewhere
      if (currentClassTeacher?.faculty_id) {
        await this.updateFacultyRoles(currentClassTeacher.faculty_id, false);
      }
    } catch (error) {
      console.error('Error removing class teacher:', error);
      throw error;
    }
  }

  /**
   * Get all class teacher assignments for a specific department
   */
  static async getAllClassTeachers(userDepartment?: string): Promise<ClassTeacher[]> {
    try {
      let query = supabase
        .from('class_teachers')
        .select(`
          *,
          faculty:employee_details(id, full_name, email, department, designation)
        `);

      // Filter by department if provided
      if (userDepartment) {
        query = query.eq('department', userDepartment);
      }

      const { data, error } = await query
        .order('academic_year', { ascending: false })
        .order('semester', { ascending: true })
        .order('section', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching all class teachers:', error);
      throw error;
    }
  }

  /**
   * Update faculty roles to include/exclude class_teacher role
   * Ensures proper role hierarchy: faculty is always the base role
   */
  private static async updateFacultyRoles(facultyId: string, isClassTeacher: boolean): Promise<void> {
    try {
      // Get current faculty data
      const { data: faculty, error: fetchError } = await supabase
        .from('employee_details')
        .select('roles')
        .eq('id', facultyId)
        .single();

      if (fetchError) throw fetchError;

      // Ensure faculty role is always present as base role
      let currentRoles = faculty?.roles || [];
      if (!currentRoles.includes('faculty')) {
        currentRoles.push('faculty');
      }

      if (isClassTeacher) {
        // Add class_teacher role if not already present
        if (!currentRoles.includes('class_teacher')) {
          currentRoles.push('class_teacher');
        }
      } else {
        // Check if faculty is still a class teacher elsewhere
        const { data: otherAssignments, error: checkError } = await supabase
          .from('class_teachers')
          .select('id')
          .eq('faculty_id', facultyId)
          .limit(1);

        if (checkError) throw checkError;

        // Remove class_teacher role only if no other assignments exist
        if (!otherAssignments || otherAssignments.length === 0) {
          currentRoles = currentRoles.filter(role => role !== 'class_teacher');
        }
      }

      // Ensure faculty role is always maintained
      if (!currentRoles.includes('faculty')) {
        currentRoles.unshift('faculty'); // Add faculty as first role
      }

      // Update faculty roles
      const { error: updateError } = await supabase
        .from('employee_details')
        .update({ roles: currentRoles })
        .eq('id', facultyId);

      if (updateError) throw updateError;

      console.log(`Updated roles for faculty ${facultyId}:`, currentRoles);
    } catch (error) {
      console.error('Error updating faculty roles:', error);
      throw error;
    }
  }

  /**
   * Get class teacher for a specific semester-section
   */
  static async getClassTeacher(
    academicYear: string,
    userDepartment: string,
    semester: string,
    section: string
  ): Promise<ClassTeacher | null> {
    try {
      const { data, error } = await supabase
        .from('class_teachers')
        .select(`
          *,
          faculty:employee_details(id, full_name, email, department, designation)
        `)
        .eq('academic_year', academicYear)
        .eq('department', userDepartment)
        .eq('semester', semester)
        .eq('section', section)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"
      return data || null;
    } catch (error) {
      console.error('Error fetching class teacher:', error);
      throw error;
    }
  }
}
