import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    hmr: {
      overlay: false
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    force: mode === 'development',
    include: [
      'react',
      'react-dom',
      '@radix-ui/react-tooltip',
      'sonner',
      '@tanstack/react-query',
      'react-router-dom',
      'next-themes'
    ]
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunk for core dependencies
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor-react';
            }
            if (id.includes('@radix-ui') || id.includes('sonner')) {
              return 'vendor-ui';
            }
            if (id.includes('@tanstack/react-query')) {
              return 'vendor-query';
            }
            if (id.includes('react-router-dom')) {
              return 'vendor-router';
            }
            if (id.includes('supabase')) {
              return 'vendor-supabase';
            }
            if (id.includes('lucide-react')) {
              return 'vendor-icons';
            }
            return 'vendor-misc';
          }

          // Page chunks for better lazy loading
          if (id.includes('/pages/')) {
            const pageName = id.split('/pages/')[1].split('.')[0];
            return `page-${pageName.toLowerCase()}`;
          }

          // Component chunks
          if (id.includes('/components/')) {
            if (id.includes('/components/ui/')) {
              return 'components-ui';
            }
            if (id.includes('/components/attendance/')) {
              return 'components-attendance';
            }
            if (id.includes('/components/quiz/')) {
              return 'components-quiz';
            }
            if (id.includes('/components/timetable/')) {
              return 'components-timetable';
            }
            return 'components-misc';
          }

          // Service chunks
          if (id.includes('/services/')) {
            return 'services';
          }
        }
      }
    },
    chunkSizeWarningLimit: 1000,
    target: 'esnext',
    minify: 'esbuild'
  }
}));
