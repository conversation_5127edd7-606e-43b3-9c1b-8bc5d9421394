import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import {
  Brain,
  Upload,
  FileText,
  Calendar,
  Users,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Eye,
  Settings,
  Zap
} from 'lucide-react';

import { QuizManagementService } from '@/services/QuizManagementService';
import {
  CourseMaterial,
  QuizTemplate,
  QuizSchedule,
  QuizAnalytics,
  FacultyQuizDashboard as DashboardData
} from '@/types/quiz-system';

import CourseMaterialUpload from './CourseMaterialUpload';
import QuizTemplateCreator from './QuizTemplateCreator';
import QuizScheduler from './QuizScheduler';
import QuizAnalyticsView from './QuizAnalyticsView';
import MaterialAnalysisView from './MaterialAnalysisView';
import FacultyDashboardSummary from './FacultyDashboardSummary';
import ContextAwareMaterialsView from './ContextAwareMaterialsView';

const FacultyQuizDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [courseMaterials, setCourseMaterials] = useState<CourseMaterial[]>([]);
  const [quizTemplates, setQuizTemplates] = useState<QuizTemplate[]>([]);
  const [quizSchedules, setQuizSchedules] = useState<QuizSchedule[]>([]);
  const [analytics, setAnalytics] = useState<QuizAnalytics[]>([]);

  const { user } = useAuth();
  const { department, departmentName, fullName } = useUserDepartment();
  const { toast } = useToast();

  useEffect(() => {
    if (user?.id) {
      loadDashboardData();
    }
  }, [user?.id]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load all quiz-related data
      const [materials, templates, schedules, analyticsData] = await Promise.all([
        QuizManagementService.getFacultyCourseMaterials(user!.id),
        QuizManagementService.getFacultyQuizTemplates(user!.id),
        QuizManagementService.getFacultyQuizSchedules(user!.id),
        QuizManagementService.getQuizAnalytics(user!.id)
      ]);

      setCourseMaterials(materials);
      setQuizTemplates(templates);
      setQuizSchedules(schedules);
      setAnalytics(analyticsData);

      // Calculate dashboard statistics
      const activeQuizzes = schedules.filter(s => s.status === 'active' || s.status === 'scheduled').length;
      const totalAttempts = analyticsData.reduce((sum, a) => sum + a.attempted_students, 0);
      const avgCompletionRate = analyticsData.length > 0 
        ? analyticsData.reduce((sum, a) => sum + a.completion_rate, 0) / analyticsData.length 
        : 0;

      const pendingReviews = templates.filter(t => t.status === 'ai_generated');

      setDashboardData({
        total_quizzes: templates.length,
        active_quizzes: activeQuizzes,
        total_attempts: totalAttempts,
        average_completion_rate: avgCompletionRate,
        recent_quizzes: schedules.slice(0, 5),
        pending_reviews: pendingReviews,
        course_materials: materials.slice(0, 5)
      });

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load quiz dashboard data.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMaterialUploaded = (material: CourseMaterial) => {
    setCourseMaterials(prev => [material, ...prev]);
    toast({
      title: 'Success',
      description: 'Course material uploaded successfully. AI processing started.',
    });
  };

  const handleQuizTemplateCreated = (template: QuizTemplate) => {
    setQuizTemplates(prev => [template, ...prev]);
    setActiveTab('templates');
    toast({
      title: 'Success',
      description: 'Quiz template created successfully.',
    });
  };

  const handleQuizScheduled = (schedule: QuizSchedule) => {
    setQuizSchedules(prev => [schedule, ...prev]);
    setActiveTab('schedules');
    toast({
      title: 'Success',
      description: 'Quiz scheduled successfully. Students will be notified.',
    });
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 pb-16">
      {/* Header */}
      <div className="space-y-0.5">
        <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
          <Brain className="h-6 w-6 text-primary" />
          AI-Powered Quiz Management
        </h2>
        <p className="text-muted-foreground">
          Create, schedule, and manage AI-generated quizzes for your students
        </p>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Brain className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Faculty:</strong> {fullName} | <strong>Department:</strong> {departmentName}
        </AlertDescription>
      </Alert>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="materials">Materials</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="schedules">Schedules</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Faculty Dashboard Summary */}
          <FacultyDashboardSummary />

          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Quizzes</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.total_quizzes || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Quiz templates created
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Quizzes</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.active_quizzes || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Currently scheduled
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Attempts</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.total_attempts || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Student quiz attempts
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {dashboardData?.average_completion_rate?.toFixed(1) || 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Average completion
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card className="hover:shadow-md transition-shadow cursor-pointer" 
                  onClick={() => setActiveTab('materials')}>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Upload className="h-5 w-5 text-blue-600" />
                  Upload Course Material
                </CardTitle>
                <CardDescription>
                  Upload PDFs, documents, and syllabus for AI processing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Material
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => setActiveTab('templates')}>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Zap className="h-5 w-5 text-green-600" />
                  Create AI Quiz
                </CardTitle>
                <CardDescription>
                  Generate quiz questions using AI from your course materials
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  <Brain className="h-4 w-4 mr-2" />
                  Create Quiz
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => setActiveTab('schedules')}>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-purple-600" />
                  Schedule Quiz
                </CardTitle>
                <CardDescription>
                  Schedule quizzes for your students with time controls
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Quiz
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent Course Materials</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {dashboardData?.course_materials.length ? (
                  dashboardData.course_materials.map((material) => (
                    <div key={material.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="font-medium">{material.file_name}</p>
                        <p className="text-sm text-muted-foreground">{material.subject_name}</p>
                      </div>
                      <Badge variant={
                        material.processing_status === 'completed' ? 'default' :
                        material.processing_status === 'processing' ? 'secondary' :
                        material.processing_status === 'failed' ? 'destructive' : 'outline'
                      }>
                        {material.processing_status}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground">No course materials uploaded yet.</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Pending Reviews</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {dashboardData?.pending_reviews.length ? (
                  dashboardData.pending_reviews.map((template) => (
                    <div key={template.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="font-medium">{template.title}</p>
                        <p className="text-sm text-muted-foreground">{template.subject_name}</p>
                      </div>
                      <Button size="sm" variant="outline" onClick={() => setActiveTab('templates')}>
                        <Eye className="h-4 w-4 mr-1" />
                        Review
                      </Button>
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground">No quizzes pending review.</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="materials">
          <div className="space-y-6">
            <CourseMaterialUpload
              onMaterialUploaded={handleMaterialUploaded}
              materials={courseMaterials}
            />
            <ContextAwareMaterialsView />
          </div>
        </TabsContent>

        <TabsContent value="analysis">
          <MaterialAnalysisView
            materials={courseMaterials}
          />
        </TabsContent>

        <TabsContent value="templates">
          <QuizTemplateCreator 
            onTemplateCreated={handleQuizTemplateCreated}
            templates={quizTemplates}
            courseMaterials={courseMaterials}
          />
        </TabsContent>

        <TabsContent value="schedules">
          <QuizScheduler 
            onQuizScheduled={handleQuizScheduled}
            schedules={quizSchedules}
            templates={quizTemplates}
          />
        </TabsContent>

        <TabsContent value="analytics">
          <QuizAnalyticsView 
            analytics={analytics}
            schedules={quizSchedules}
          />
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Quiz System Settings</CardTitle>
              <CardDescription>
                Configure AI quiz generation preferences and system settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Settings panel coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FacultyQuizDashboard;
