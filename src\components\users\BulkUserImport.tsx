
import React from 'react';
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Upload } from 'lucide-react';
import ExcelUpload from './ExcelUpload';
import { UserService, Employee } from '@/services/UserService';
import { toast } from '@/hooks/use-toast';

interface BulkUserImportProps {
  onImportComplete: () => void;
}

const BulkUserImport: React.FC<BulkUserImportProps> = ({ onImportComplete }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  const handleFileProcessed = async (data: Partial<Employee>[]) => {
    try {
      const result = await UserService.addMultipleEmployees(data);
      
      toast({
        title: `Import ${result.success.length > 0 ? 'Successful' : 'Failed'}`,
        description: `Imported ${result.success.length} employees. ${
          result.failed.length > 0 ? `${result.failed.length} records failed.` : ''
        }`,
        variant: result.success.length > 0 ? 'default' : 'destructive',
      });
      
      if (result.success.length > 0) {
        setIsOpen(false);
        onImportComplete();
      }
      
      return result;
    } catch (error: any) {
      toast({
        title: 'Import Error',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="ml-auto">
          <Upload className="h-4 w-4 mr-2" />
          Bulk Import
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Import Faculty Records</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center p-4 space-y-4">
          <p className="text-sm text-muted-foreground text-center">
            Upload an Excel file (.xlsx), CSV, or JSON with faculty information.
            <br />
            Required columns: Full Name, Email
            <br />
            Optional: Department, Designation, Phone
          </p>
          <ExcelUpload
            onFileProcessed={handleFileProcessed}
            onUploadComplete={() => {
              setIsOpen(false);
              onImportComplete();
            }}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BulkUserImport;
