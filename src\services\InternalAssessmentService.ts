import { supabase } from "@/integrations/supabase/client";
import { FacultyStudentListService } from './FacultyStudentListService';

export interface InternalAssessment {
  id: string;
  student_id: string;
  subject_code: string;
  faculty_id: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  ia1_marks?: number;
  ia1_date?: string;
  ia2_marks?: number;
  ia2_date?: string;
  ia3_marks?: number;
  ia3_date?: string;
  assignment_marks?: number;
  theory_marks?: number; // Theory marks support for subjects (changed from lab_marks)
  created_at: string;
  updated_at: string;
  created_by?: string;
  last_modified_by?: string;
  student?: {
    id: string;
    roll_number: string;
    student_name: string;
  };
}

export interface IAFormData {
  student_id: string;
  subject_code: string;
  faculty_id: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  ia1_marks?: number;
  ia1_date?: string;
  ia2_marks?: number;
  ia2_date?: string;
  ia3_marks?: number;
  ia3_date?: string;
  assignment_marks?: number;
  theory_marks?: number; // Theory marks support for subjects (changed from lab_marks)
}

export interface IAReport {
  student_id: string;
  student_name: string;
  roll_number: string;
  subject_code: string;
  ia1_marks?: number;
  ia2_marks?: number;
  ia3_marks?: number;
  assignment_marks?: number;
  theory_marks?: number; // Theory marks support for subjects (changed from lab_marks)
  total_marks: number;
  average_ia: number;
  grade?: string;
}

export interface IASession {
  subject_code: string;
  subject_name: string;
  semester: string;
  section: string;
  academic_year: string;
  students: {
    id: string;
    usn: string; // USN field for Sequential IA Entry
    roll_number: string;
    student_name: string;
    ia1_marks?: number;
    ia2_marks?: number;
    ia3_marks?: number;
    assignment_marks?: number;
    theory_marks?: number; // Theory marks support for subjects (changed from lab_marks)
    assessment_id?: string;
  }[];
}

export class InternalAssessmentService {
  /**
   * Normalize subject codes to ensure consistency with Student Progress display
   * Removes suffixes like _THEORY, _LAB, etc.
   */
  private static normalizeSubjectCode(subjectCode: string): string {
    if (!subjectCode) return subjectCode;

    // Remove common suffixes used in IA entry systems
    return subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
  }
  /**
   * Get students for IA entry with existing marks
   * @deprecated This method is deprecated. Use getStudentsForIAWithDynamicLoading instead.
   */
  static async getStudentsForIA(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<IASession> {
    try {
      // Use the dynamic loading method instead
      console.warn('⚠️ getStudentsForIA is deprecated. Using getStudentsForIAWithDynamicLoading instead.');
      return await this.getStudentsForIAWithDynamicLoading(
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        academicYear
      );
    } catch (error) {
      console.error('Error fetching students for IA:', error);
      throw error;
    }
  }

  /**
   * Get students for IA entry with enhanced faculty-subject-student mapping
   */
  static async getStudentsForIAWithDynamicLoading(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string
  ): Promise<IASession> {
    try {
      console.log('🎯 Starting enhanced faculty-subject-student mapping:', {
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        academicYear
      });

      // Step 1: Faculty-Subject Validation
      console.log('🔍 Step 1: Validating faculty authorization for subject-semester-section...');
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      const { data: facultySubjectInfo, error: facultyError } = await supabase
        .from('timetable_slots')
        .select('subject_name, subject_type, day, time_slot, batch_name')
        .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .limit(1)
        .single();

      if (facultyError || !facultySubjectInfo) {
        console.error('❌ Faculty authorization failed:', facultyError);
        throw new Error(`You are not authorized to teach ${subjectCode} for Semester ${semester} Section ${section}`);
      }

      console.log('✅ Faculty authorization confirmed:', {
        subjectName: facultySubjectInfo.subject_name,
        subjectType: facultySubjectInfo.subject_type,
        semester,
        section
      });

      const subjectType = facultySubjectInfo.subject_type;
      const subjectName = facultySubjectInfo.subject_name;

      // Step 2: Extract Semester and Section (already provided from selected subject)
      console.log('🔍 Step 2: Using extracted semester and section from selected subject:', {
        semester,
        section,
        subjectType
      });

      // Step 3: Query class_students table with proper department mapping
      console.log('🔍 Step 3: Querying class_students table...');

      // Try multiple department name variations
      const departmentVariations = [
        userDepartment, // "Computer Science and Engineering"
        this.mapDepartmentName(userDepartment), // "cse"
        mappedDepartment // Already mapped version
      ];

      console.log('🔍 Trying department variations for class_students:', departmentVariations);

      let students: any[] = [];
      let successfulDepartment = '';
      let dataSource = '';

      // Try each department variation for class_students
      for (const deptVariation of departmentVariations) {
        console.log(`🔍 Querying class_students with department: "${deptVariation}"`);

        const { data: classStudents, error: classError } = await supabase
          .from('class_students')
          .select('id, usn, student_name, department, semester, section')
          .eq('department', deptVariation)
          .eq('semester', semester)
          .eq('section', section)
          .order('usn');

        if (!classError && classStudents && classStudents.length > 0) {
          students = classStudents;
          successfulDepartment = deptVariation;
          dataSource = 'class_students';
          console.log(`✅ Found ${students.length} students in class_students with department: "${deptVariation}"`);
          break;
        } else {
          console.log(`⚠️ No students found in class_students with department: "${deptVariation}"`);
        }
      }

      // Step 4: Fallback Strategy - Use batch_students if class_students is empty
      if (students.length === 0) {
        console.log('🔍 Step 4: Fallback to batch_students table...');

        for (const deptVariation of departmentVariations) {
          console.log(`🔍 Querying batch_students with department: "${deptVariation}"`);

          const { data: batchStudents, error: batchError } = await supabase
            .from('batch_students')
            .select('id, usn, student_name, department, semester, section, batch_name')
            .eq('department', deptVariation)
            .eq('semester', semester)
            .eq('section', section)
            .order('usn');

          if (!batchError && batchStudents && batchStudents.length > 0) {
            students = batchStudents;
            successfulDepartment = deptVariation;
            dataSource = 'batch_students';
            console.log(`✅ Found ${students.length} students in batch_students with department: "${deptVariation}"`);
            break;
          } else {
            console.log(`⚠️ No students found in batch_students with department: "${deptVariation}"`);
          }
        }
      }

      // Step 5: Error Handling
      if (students.length === 0) {
        console.error('❌ No students found in any table for semester-section combination');

        // Debug: Check what data exists
        const { data: debugData } = await supabase
          .from('class_students')
          .select('department, semester, section')
          .limit(10);

        console.log('🔍 Available data in class_students (sample):', debugData);

        throw new Error(`No students found for Semester ${semester} Section ${section}. Please ensure students have been uploaded for this class.`);
      }

      console.log(`✅ Successfully loaded ${students.length} students from ${dataSource} table using department "${successfulDepartment}"`);

      console.log('👥 Students loaded:', {
        count: students.length,
        source: dataSource,
        department: successfulDepartment,
        sampleStudent: students[0]
      });

      // Step 6: Get existing IA records for these students
      console.log('🔍 Step 6: Loading existing IA records...');
      const { data: existingIA, error: iaError } = await supabase
        .from('internal_assessments')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('academic_year', academicYear);

      if (iaError) {
        console.warn('⚠️ Error loading existing IA records:', iaError);
      }

      // Create IA map for quick lookup
      const iaMap = new Map();
      existingIA?.forEach(record => {
        iaMap.set(record.student_id, record);
      });

      console.log(`📊 Found ${existingIA?.length || 0} existing IA records`);

      // Step 7: Transform students to IA session format with existing marks
      console.log('🔍 Step 7: Transforming students to IA session format...');
      const studentsWithIA = students.map(student => {
        const iaRecord = iaMap.get(student.id);
        return {
          id: student.id,
          usn: student.usn, // Preserve USN field for Sequential IA Entry
          roll_number: student.usn, // Use USN as roll number for backward compatibility
          student_name: student.student_name,
          ia1_marks: iaRecord?.ia1_marks,
          ia2_marks: iaRecord?.ia2_marks,
          ia3_marks: iaRecord?.ia3_marks,
          assignment_marks: iaRecord?.assignment_marks,
          theory_marks: iaRecord?.theory_marks || undefined, // Include theory marks for subjects
          assessment_id: iaRecord?.id
        };
      });

      console.log('✅ Enhanced faculty-subject-student mapping completed successfully');

      return {
        subject_code: subjectCode,
        subject_name: subjectName,
        semester,
        section,
        academic_year: academicYear,
        students: studentsWithIA
      };
    } catch (error) {
      console.error('Error fetching students for IA with dynamic loading:', error);
      throw error;
    }
  }

  /**
   * Map department names for database queries
   */
  private static mapDepartmentName(userDepartment: string): string {
    const departmentMap: Record<string, string> = {
      'Computer Science and Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Electrical and Electronics Engineering': 'eee'
    };

    return departmentMap[userDepartment] || userDepartment.toLowerCase();
  }

  /**
   * Save IA marks for students
   */
  static async saveIAMarks(
    iaData: IAFormData[],
    modifiedBy: string
  ): Promise<InternalAssessment[]> {
    try {
      console.log('💾 Saving IA marks to database:', {
        recordsCount: iaData.length,
        sampleRecord: iaData[0]
      });

      // CRITICAL FIX: Map department names to short codes and ensure field lengths
      // Also normalize subject codes for consistency with Student Progress display
      const records = iaData.map(data => ({
        ...data,
        subject_code: this.normalizeSubjectCode(data.subject_code), // Normalize subject codes
        department: this.mapDepartmentName(data.department), // Map to short code (e.g., 'cse')
        semester: String(data.semester).substring(0, 10), // Ensure max 10 chars
        section: String(data.section).substring(0, 10), // Ensure max 10 chars
        last_modified_by: modifiedBy,
        created_by: modifiedBy, // For new records
        updated_at: new Date().toISOString() // Ensure updated timestamp for real-time sync
      }));

      console.log('📝 Mapped records for database:', {
        recordsCount: records.length,
        sampleMappedRecord: records[0],
        fieldLengths: {
          department: records[0].department?.length,
          semester: records[0].semester?.length,
          section: records[0].section?.length,
          subject_code: records[0].subject_code?.length,
          academic_year: records[0].academic_year?.length
        }
      });

      // Use upsert to handle existing records without joining to students table
      const { data, error } = await supabase
        .from('internal_assessments')
        .upsert(records, {
          onConflict: 'student_id,subject_code,academic_year'
        })
        .select('*');

      if (error) {
        console.error('❌ Error during upsert:', error);

        // If theory_marks column doesn't exist, try without it
        if (error.message.includes('theory_marks') || error.message.includes('column')) {
          console.warn('⚠️ Column issue detected, trying without theory_marks');
          const recordsWithoutTheoryMarks = records.map(record => {
            const { theory_marks, ...recordWithoutTheoryMarks } = record;
            return recordWithoutTheoryMarks;
          });

          const { data: fallbackData, error: fallbackError } = await supabase
            .from('internal_assessments')
            .upsert(recordsWithoutTheoryMarks, {
              onConflict: 'student_id,subject_code,academic_year'
            })
            .select('*');

          if (fallbackError) {
            console.error('❌ Fallback save also failed:', fallbackError);
            throw fallbackError;
          }

          console.log('✅ Fallback save successful');
          return fallbackData || [];
        }
        throw error;
      }

      console.log('✅ IA marks saved successfully:', {
        savedRecords: data?.length || 0
      });

      // Trigger Student Progress refresh for affected students (real-time sync)
      if (data && data.length > 0) {
        console.log('🔄 Triggering Student Progress refresh for updated students...');

        // Get unique student IDs from saved records
        const updatedStudentIds = [...new Set(data.map(record => record.student_id))];

        // Log the refresh trigger (actual refresh can be implemented based on requirements)
        console.log(`📊 ${updatedStudentIds.length} students need progress refresh after IA marks update`);
        console.log(`📝 Updated subjects: ${[...new Set(data.map(record => record.subject_code))].join(', ')}`);

        // Note: Actual refresh implementation can be added here if needed
        // For example: await StudentProgressService.refreshMultipleStudents(updatedStudentIds);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error saving IA marks:', error);
      throw error;
    }
  }

  /**
   * Get IA report for a subject with score filters
   */
  static async getIAReport(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    academicYear: string,
    scoreFilter?: 'all' | 'low' | 'medium' | 'high' // <15, 15-25, >25 (out of 30 for each IA)
  ): Promise<IAReport[]> {
    try {
      console.log('📊 Generating IA report using dynamic student loading...');

      // Use the dynamic loading method to get students
      const iaSession = await this.getStudentsForIAWithDynamicLoading(
        facultyId,
        userDepartment,
        subjectCode,
        semester,
        section,
        academicYear
      );

      const students = iaSession.students;

      // Generate report for each student (IA records are already loaded in the session)
      const reports: IAReport[] = (students || []).map(student => {
        const ia1 = student.ia1_marks || 0;
        const ia2 = student.ia2_marks || 0;
        const ia3 = student.ia3_marks || 0;
        const assignment = student.assignment_marks || 0;
        const theory = student.theory_marks || 0;

        // Total marks calculation: IA1(25) + IA2(25) + IA3(25) + Assignment(10) + Theory(20 for subjects)
        const totalMarks = ia1 + ia2 + ia3 + assignment + theory;
        const averageIA = (ia1 + ia2 + ia3) / 3;

        // Calculate grade based on total marks (out of 105: 3*25 + 10 + 20 for lab subjects, or 85 for theory subjects)
        let grade = 'F';
        const maxMarks = lab > 0 ? 105 : 85; // 105 for lab subjects, 85 for theory subjects
        const percentage = (totalMarks / maxMarks) * 100;
        if (percentage >= 90) grade = 'A+';
        else if (percentage >= 80) grade = 'A';
        else if (percentage >= 70) grade = 'B+';
        else if (percentage >= 60) grade = 'B';
        else if (percentage >= 50) grade = 'C';
        else if (percentage >= 40) grade = 'D';

        return {
          student_id: student.id,
          student_name: student.student_name,
          roll_number: student.roll_number, // This comes from the IA session
          subject_code: subjectCode,
          ia1_marks: ia1,
          ia2_marks: ia2,
          ia3_marks: ia3,
          assignment_marks: assignment,
          theory_marks: theory,
          total_marks: totalMarks,
          average_ia: Math.round(averageIA * 100) / 100,
          grade
        };
      });

      // Apply score filter based on average IA marks
      if (scoreFilter && scoreFilter !== 'all') {
        return reports.filter(report => {
          switch (scoreFilter) {
            case 'low':
              return report.average_ia < 15;
            case 'medium':
              return report.average_ia >= 15 && report.average_ia <= 25;
            case 'high':
              return report.average_ia > 25;
            default:
              return true;
          }
        });
      }

      return reports;
    } catch (error) {
      console.error('Error generating IA report:', error);
      throw error;
    }
  }

  /**
   * Get consolidated IA report for class teacher (all subjects)
   */
  static async getConsolidatedIAReport(
    userDepartment: string,
    semester: string,
    section: string,
    academicYear: string,
    scoreFilter?: 'all' | 'low' | 'medium' | 'high'
  ): Promise<{ [subjectCode: string]: IAReport[] }> {
    try {
      // Get all subjects for the semester-section
      const { data: subjects, error: subjectsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('subject_code, subject_name, faculty_1_id')
        .eq('department', userDepartment)
        .eq('semester', semester)
        .eq('section', section);

      if (subjectsError) throw subjectsError;

      const consolidatedReport: { [subjectCode: string]: IAReport[] } = {};

      // Generate report for each subject
      for (const subject of subjects || []) {
        const report = await this.getIAReport(
          subject.faculty_1_id,
          userDepartment,
          subject.subject_code,
          semester,
          section,
          academicYear,
          scoreFilter
        );
        consolidatedReport[subject.subject_code] = report;
      }

      return consolidatedReport;
    } catch (error) {
      console.error('Error generating consolidated IA report:', error);
      throw error;
    }
  }

  /**
   * Check if IA entry is allowed (time-gated)
   */
  static async isIAEntryAllowed(
    iaNumber: 1 | 2 | 3,
    academicYear: string
  ): Promise<{ allowed: boolean; message?: string }> {
    try {
      // Get current date
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1; // 1-12

      // Define IA windows (can be configured)
      const iaWindows = {
        1: { start: 8, end: 9 }, // August-September
        2: { start: 11, end: 12 }, // November-December
        3: { start: 3, end: 4 } // March-April
      };

      const window = iaWindows[iaNumber];

      if (currentMonth >= window.start && currentMonth <= window.end) {
        return { allowed: true };
      }

      return {
        allowed: false,
        message: `IA${iaNumber} entry is only allowed during ${getMonthName(window.start)}-${getMonthName(window.end)}`
      };
    } catch (error) {
      console.error('Error checking IA entry permission:', error);
      return { allowed: true }; // Default to allow if check fails
    }
  }

  /**
   * Get IA statistics for dashboard
   */
  static async getIAStatistics(
    facultyId: string,
    userDepartment: string,
    academicYear: string
  ): Promise<{
    totalSubjects: number;
    totalStudents: number;
    ia1Completed: number;
    ia2Completed: number;
    ia3Completed: number;
    averageMarks: number;
  }> {
    try {
      // Get faculty's subjects
      const { data: subjects, error: subjectsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('subject_code, semester, section')
        .eq('faculty_1_id', facultyId)
        .eq('department', userDepartment);

      if (subjectsError) throw subjectsError;

      let totalStudents = 0;
      let ia1Completed = 0;
      let ia2Completed = 0;
      let ia3Completed = 0;
      let totalMarks = 0;
      let totalEntries = 0;

      for (const subject of subjects || []) {
        try {
          // Use dynamic loading to get students count for this subject
          const iaSession = await this.getStudentsForIAWithDynamicLoading(
            facultyId,
            userDepartment,
            subject.subject_code,
            subject.semester,
            subject.section,
            academicYear
          );

          const subjectStudentCount = iaSession.students?.length || 0;
          totalStudents += subjectStudentCount;

          // Count completed IAs from the session students (which already have IA records loaded)
          iaSession.students?.forEach(student => {
            if (student.ia1_marks !== null && student.ia1_marks !== undefined) {
              ia1Completed++;
              totalMarks += student.ia1_marks;
              totalEntries++;
            }
            if (student.ia2_marks !== null && student.ia2_marks !== undefined) {
              ia2Completed++;
              totalMarks += student.ia2_marks;
              totalEntries++;
            }
            if (student.ia3_marks !== null && student.ia3_marks !== undefined) {
              ia3Completed++;
              totalMarks += student.ia3_marks;
              totalEntries++;
            }
          });
        } catch (error) {
          console.warn(`Error processing subject ${subject.subject_code}:`, error);
          continue;
        }
      }

      return {
        totalSubjects: subjects?.length || 0,
        totalStudents,
        ia1Completed,
        ia2Completed,
        ia3Completed,
        averageMarks: totalEntries > 0 ? Math.round((totalMarks / totalEntries) * 100) / 100 : 0
      };
    } catch (error) {
      console.error('Error getting IA statistics:', error);
      return {
        totalSubjects: 0,
        totalStudents: 0,
        ia1Completed: 0,
        ia2Completed: 0,
        ia3Completed: 0,
        averageMarks: 0
      };
    }
  }
}

// Helper function to get month name
function getMonthName(month: number): string {
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  return months[month - 1];
}
