
import React from "react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { Faculty } from "@/stores/SubjectMappingStore";
import { FacultyAutocomplete } from "@/components/subjects/FacultyAutocomplete";
import { MappingFormData } from "@/hooks/useSubjectMappingForm";

interface FacultyFieldsProps {
  form: UseFormReturn<MappingFormData>;
  facultyList: Faculty[];
  disabled?: boolean;
}

export default function FacultyFields({ form, facultyList, disabled = false }: FacultyFieldsProps) {
  // Ensure facultyList is always an array
  const safeFacultyList = Array.isArray(facultyList) ? facultyList : [];
  
  // Get the current subject type to determine if second faculty is required
  const subjectType = form.watch("subject")?.type;
  const isLabSubject = subjectType === "lab";
  
  // Watch the primary faculty to remove it from secondary faculty options
  const primaryFaculty = form.watch("faculty");
  
  // Filter out the primary faculty from secondary faculty options
  const secondaryFacultyOptions = safeFacultyList.filter(
    f => !primaryFaculty || f.id !== primaryFaculty.id
  );

  return (
    <>
      <FormField
        control={form.control}
        name="faculty"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>{isLabSubject ? "Faculty 1 (Primary)" : "Faculty"}</FormLabel>
            <FormControl>
              <FacultyAutocomplete
                faculties={safeFacultyList}
                selectedFaculty={field.value as Faculty}
                onSelect={(faculty) => {
                  if (faculty) {
                    field.onChange(faculty);
                    
                    // If secondary faculty is same as primary, clear it
                    const faculty2Id = form.getValues("faculty2Id");
                    if (faculty2Id === faculty.id) {
                      form.setValue("faculty2Id", "");
                    }
                  }
                }}
                placeholder={isLabSubject ? "Select primary faculty" : "Select faculty"}
                disabled={disabled}
              />
            </FormControl>
            <FormDescription>
              {isLabSubject 
                ? "Select the primary faculty for this lab subject" 
                : "Search and select the faculty for this subject"}
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {isLabSubject && (
        <FormField
          control={form.control}
          name="faculty2Id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Faculty 2 (Secondary)
              </FormLabel>
              <FormControl>
                <FacultyAutocomplete
                  faculties={secondaryFacultyOptions}
                  selectedFaculty={
                    field.value 
                      ? secondaryFacultyOptions.find(f => f.id === field.value) || null 
                      : null
                  }
                  onSelect={(faculty) => {
                    field.onChange(faculty.id);
                  }}
                  placeholder="Select secondary faculty"
                  disabled={disabled}
                />
              </FormControl>
              <FormDescription>
                Select the second faculty required for this lab subject
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </>
  );
}