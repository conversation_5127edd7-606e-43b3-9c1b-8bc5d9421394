import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import {
  Users,
  RefreshCw,
  Eye,
  Beaker,
  Loader2,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { StudentService } from '@/services/StudentService';
import { ClassStudentService } from '@/services/ClassStudentService';

interface LabBatchListProps {
  facultyId: string;
  onRefresh?: () => void;
}

interface BatchSummary {
  batchName: string;
  studentCount: number;
}

interface BatchStudentData {
  batchInfo: {
    batchName: string;
    department: string;
    semester: string;
    section: string;
    academicYear: string;
  };
  students: {
    usn: string;
    studentName: string;
    uploadedBy: string | null;
    uploadedAt: string | null;
  }[];
  totalStudents: number;
}

const LabBatchList: React.FC<LabBatchListProps> = ({ facultyId, onRefresh }) => {
  const [loading, setLoading] = useState(true);
  const [batchSummaries, setBatchSummaries] = useState<BatchSummary[]>([]);
  const [expandedBatches, setExpandedBatches] = useState<Set<string>>(new Set());
  const [batchDetails, setBatchDetails] = useState<Record<string, BatchStudentData>>({});
  const [loadingDetails, setLoadingDetails] = useState<Set<string>>(new Set());
  const [assignment, setAssignment] = useState<{
    department: string;
    semester: string;
    section: string;
  } | null>(null);
  const { toast } = useToast();

  const currentAcademicYear = '2024-2025';

  // Load lab batch summaries and assignment
  const loadLabBatches = async () => {
    try {
      setLoading(true);
      
      // Get assignment first
      const assignmentData = await ClassStudentService.getClassTeacherAssignment(facultyId);
      setAssignment(assignmentData);
      
      if (!assignmentData) {
        setBatchSummaries([]);
        return;
      }

      // Get lab batch summaries
      const batches = await StudentService.getLabBatchesByClass(
        assignmentData.department,
        assignmentData.semester,
        assignmentData.section,
        currentAcademicYear
      );
      
      setBatchSummaries(batches);
    } catch (error) {
      console.error('Error loading lab batches:', error);
      toast({
        title: 'Error',
        description: 'Failed to load lab batch list. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (facultyId) {
      loadLabBatches();
    }
  }, [facultyId]);

  // Load detailed student data for a specific batch
  const loadBatchDetails = async (batchName: string) => {
    if (!assignment || batchDetails[batchName]) return;

    try {
      setLoadingDetails(prev => new Set(prev).add(batchName));
      
      const batchData = await StudentService.getLabBatchStudents(
        batchName,
        assignment.department,
        assignment.semester,
        assignment.section,
        currentAcademicYear
      );
      
      setBatchDetails(prev => ({
        ...prev,
        [batchName]: batchData
      }));
    } catch (error) {
      console.error(`Error loading details for batch ${batchName}:`, error);
      toast({
        title: 'Error',
        description: `Failed to load details for ${batchName}. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setLoadingDetails(prev => {
        const newSet = new Set(prev);
        newSet.delete(batchName);
        return newSet;
      });
    }
  };

  // Toggle batch expansion
  const toggleBatchExpansion = async (batchName: string) => {
    const newExpanded = new Set(expandedBatches);
    
    if (expandedBatches.has(batchName)) {
      newExpanded.delete(batchName);
    } else {
      newExpanded.add(batchName);
      // Load details when expanding
      await loadBatchDetails(batchName);
    }
    
    setExpandedBatches(newExpanded);
  };

  // Show loading state
  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading lab batch list...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show message if not a class teacher
  if (!assignment) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert>
            <Users className="h-4 w-4" />
            <AlertDescription>
              You are not assigned as a class teacher. Only class teachers can view lab batch assignments.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Beaker className="h-5 w-5" />
              Lab Batch Assignments
            </CardTitle>
            <CardDescription>
              {assignment.semester}{assignment.section} - {assignment.department.toUpperCase()} 
              ({batchSummaries.length} batch{batchSummaries.length !== 1 ? 'es' : ''})
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadLabBatches}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {batchSummaries.length === 0 ? (
          <div className="text-center py-8">
            <Beaker className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Lab Batches Found</h3>
            <p className="text-muted-foreground mb-4">
              Your class doesn't have any lab batch assignments yet. Upload lab batch data to get started.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {batchSummaries.map((batch) => (
              <div key={batch.batchName} className="border rounded-lg overflow-hidden">
                {/* Batch Header */}
                <div 
                  className="flex items-center justify-between p-4 bg-muted/30 cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => toggleBatchExpansion(batch.batchName)}
                >
                  <div className="flex items-center gap-3">
                    {expandedBatches.has(batch.batchName) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                    <Badge variant="outline" className="font-medium">
                      {batch.batchName}
                    </Badge>
                    <span className="font-medium text-sm">
                      {batch.studentCount} student{batch.studentCount !== 1 ? 's' : ''}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {loadingDetails.has(batch.batchName) && (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    )}
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>

                {/* Batch Details */}
                {expandedBatches.has(batch.batchName) && (
                  <div className="border-t">
                    {loadingDetails.has(batch.batchName) ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="text-center">
                          <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                          <p className="text-sm text-muted-foreground">Loading batch details...</p>
                        </div>
                      </div>
                    ) : batchDetails[batch.batchName] ? (
                      <div className="max-h-64 overflow-auto">
                        <Table>
                          <TableHeader className="sticky top-0 bg-white shadow-sm">
                            <TableRow>
                              <TableHead className="w-16 text-center font-semibold">SLNO</TableHead>
                              <TableHead className="w-32 font-semibold">USN</TableHead>
                              <TableHead className="font-semibold">STUDENT NAME</TableHead>
                              <TableHead className="w-32 font-semibold">UPLOADED</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {batchDetails[batch.batchName].students.map((student, index) => (
                              <TableRow key={`${batch.batchName}-${student.usn}`} className="hover:bg-muted/50 transition-colors">
                                <TableCell className="text-center font-mono text-sm font-medium">
                                  {index + 1}
                                </TableCell>
                                <TableCell className="font-mono text-sm font-bold text-blue-700">
                                  {student.usn}
                                </TableCell>
                                <TableCell className="font-medium text-gray-900 text-sm">
                                  <div className="truncate max-w-[200px]" title={student.studentName}>
                                    {student.studentName}
                                  </div>
                                </TableCell>
                                <TableCell className="text-sm text-muted-foreground">
                                  {student.uploadedAt ? new Date(student.uploadedAt).toLocaleDateString() : 'N/A'}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center py-8">
                        <p className="text-sm text-muted-foreground">Failed to load batch details</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LabBatchList;
