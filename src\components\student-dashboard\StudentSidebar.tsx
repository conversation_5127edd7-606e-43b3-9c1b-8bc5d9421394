import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Home,
  BookOpen,
  Clock,
  FileText,
  Calendar,
  Settings,
  Bell,
  TrendingUp,
  Users,
  GraduationCap,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  disabled?: boolean;
}

interface StudentSidebarProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
  overallAttendance?: number;
  totalSubjects?: number;
}

const StudentSidebar: React.FC<StudentSidebarProps> = ({ 
  activeTab, 
  onTabChange, 
  className,
  overallAttendance,
  totalSubjects
}) => {
  const sidebarItems: SidebarItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
    },

    {
      id: 'attendance',
      label: 'Attendance Details',
      icon: Clock,
      badge: overallAttendance ? `${overallAttendance}%` : undefined,
      disabled: true,
    },
    {
      id: 'feedback',
      label: 'Faculty Feedback',
      icon: MessageSquare,
    },
    {
      id: 'timetable',
      label: 'Timetable',
      icon: Calendar,
      disabled: true,
    },
    {
      id: 'assignments',
      label: 'Assignments',
      icon: FileText,
      disabled: true,
    },
    {
      id: 'grades',
      label: 'Grades & Results',
      icon: TrendingUp,
      disabled: true,
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      disabled: true,
    },
    {
      id: 'profile',
      label: 'Profile Settings',
      icon: Settings,
      disabled: true,
    },
  ];

  const getAttendanceStatusColor = (percentage?: number) => {
    if (!percentage) return 'bg-gray-100 text-gray-600';
    if (percentage >= 75) return 'bg-green-100 text-green-700';
    if (percentage >= 65) return 'bg-yellow-100 text-yellow-700';
    return 'bg-red-100 text-red-700';
  };

  return (
    <div className={cn("w-64 bg-white/95 backdrop-blur-sm border-r border-gray-200/50 h-full", className)}>
      {/* Modern Sidebar Header */}
      <div className="p-6 border-b border-gray-200/50">
        <div className="flex items-center gap-3">
          <div className="p-2.5 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-sm">
            <GraduationCap className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="font-bold text-gray-900">Student Portal</h2>
            <p className="text-xs text-gray-500 font-medium">Academic Dashboard</p>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      {(overallAttendance !== undefined || totalSubjects !== undefined) && (
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">
            Quick Overview
          </h3>
          <div className="space-y-2">
            {totalSubjects !== undefined && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Subjects</span>
                <Badge variant="outline" className="text-xs">
                  {totalSubjects}
                </Badge>
              </div>
            )}
            {overallAttendance !== undefined && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Overall Attendance</span>
                <Badge 
                  variant="outline" 
                  className={cn("text-xs", getAttendanceStatusColor(overallAttendance))}
                >
                  {overallAttendance}%
                </Badge>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modern Navigation Items */}
      <nav className="p-4">
        <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-4">
          Navigation
        </h3>
        <div className="space-y-2">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;

            return (
              <Button
                key={item.id}
                variant="ghost"
                className={cn(
                  "w-full justify-start h-11 px-4 rounded-xl transition-all duration-200",
                  isActive && "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md hover:from-blue-600 hover:to-blue-700",
                  !isActive && "text-gray-700 hover:bg-gray-100 hover:text-gray-900",
                  item.disabled && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => !item.disabled && onTabChange(item.id)}
                disabled={item.disabled}
              >
                <Icon className={cn(
                  "h-5 w-5 mr-3",
                  isActive ? "text-white" : "text-gray-500"
                )} />
                <span className="flex-1 text-left font-medium">{item.label}</span>
                {item.badge && (
                  <Badge
                    variant="outline"
                    className={cn(
                      "text-xs ml-2 font-medium",
                      isActive ? "bg-white/20 text-white border-white/30" : "bg-gray-100 text-gray-600 border-gray-200",
                      item.id === 'attendance' && !isActive && getAttendanceStatusColor(overallAttendance)
                    )}
                  >
                    {item.badge}
                  </Badge>
                )}
                {item.disabled && (
                  <Badge variant="outline" className="text-xs ml-2 text-gray-400 border-gray-300 bg-gray-50">
                    Soon
                  </Badge>
                )}
              </Button>
            );
          })}
        </div>
      </nav>

      {/* Modern Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-4">
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 text-center border border-gray-200">
          <p className="text-sm font-semibold text-gray-700">
            Academic Year 2024-25
          </p>
          <p className="text-xs text-gray-500 mt-1">
            🎓 Excellence in Education
          </p>
        </div>
      </div>
    </div>
  );
};

export default StudentSidebar;
