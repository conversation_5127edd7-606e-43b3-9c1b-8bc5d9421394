
import React, { useState, useEffect } from 'react';
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";
import SubjectService from '@/services/SubjectService';

interface AttendanceFiltersProps {
  onDateChange: (date: Date | undefined) => void;
  onSubjectChange: (subjectId: string) => void;
}

const AttendanceFilters: React.FC<AttendanceFiltersProps> = ({ onDateChange, onSubjectChange }) => {
  const [date, setDate] = useState<Date>();
  const [selectedSubject, setSelectedSubject] = useState("");
  const [subjects, setSubjects] = useState<{ id: string; name: string; code: string; }[]>([]);

  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const fetchedSubjects = await SubjectService.fetchAll();
        setSubjects(fetchedSubjects.map(subject => ({
          id: subject.id,
          name: subject.subject_name,
          code: subject.subject_code
        })));
      } catch (error) {
        console.error("Error fetching subjects:", error);
      }
    };

    fetchSubjects();
  }, []);

  useEffect(() => {
    onDateChange(date);
  }, [date, onDateChange]);

  useEffect(() => {
    onSubjectChange(selectedSubject);
  }, [selectedSubject, onSubjectChange]);

  return (
    <div className="grid grid-cols-6 gap-4">
      <div className="col-span-3 md:col-span-1">
        <Label htmlFor="date">Select Date</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-full justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "PPP") : <span>Pick a date</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={date}
              onSelect={setDate}
              disabled={(date) =>
                date > new Date() || date < new Date("1900-01-01")
              }
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="col-span-3">
        <Label htmlFor="subject">Select Subject</Label>
        <Select value={selectedSubject} onValueChange={setSelectedSubject}>
          <SelectTrigger>
            <SelectValue placeholder="Select Subject" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Assigned Subjects</SelectLabel>
              {subjects.map((subject) => (
                <SelectItem key={subject.id} value={subject.id}>
                  {subject.code} - {subject.name}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default AttendanceFilters;
