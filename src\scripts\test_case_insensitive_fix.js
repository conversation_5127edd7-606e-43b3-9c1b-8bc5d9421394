/**
 * Test script to verify the case-insensitive department matching fix
 * This should resolve the BCS401 "Unknown Subject" issue
 */

import { StudentProgressService } from '../services/StudentProgressService.js';

// Test configuration
const TEST_CONFIG = {
  studentUsn: '1KS23CS001', // Student with BCS401 showing as "Unknown Subject"
  academicYear: '2024-25'
};

async function testCaseInsensitiveFix() {
  console.log('🧪 Testing Case-Insensitive Department Matching Fix\n');
  console.log('Test Configuration:', TEST_CONFIG);

  try {
    console.log('\n🔄 Loading Student Progress Card...');
    const progressData = await StudentProgressService.getStudentProgressCard(
      TEST_CONFIG.studentUsn,
      TEST_CONFIG.academicYear
    );

    if (!progressData) {
      console.log('❌ No progress data returned');
      return;
    }

    console.log('\n📊 Student Progress Results:');
    console.log(`Student: ${progressData.basic_info.student_name}`);
    console.log(`Department: ${progressData.basic_info.department}`);
    console.log(`Semester: ${progressData.basic_info.semester}`);
    console.log(`Section: ${progressData.basic_info.section}`);

    console.log('\n📚 IA Marks Results:');
    console.log(`Total subjects with IA marks: ${progressData.ia_marks.length}`);

    // Check specifically for BCS401
    const bcs401Record = progressData.ia_marks.find(record => record.subject_code === 'BCS401');
    
    if (bcs401Record) {
      console.log('\n✅ BCS401 Found:');
      console.log(`  Subject Code: ${bcs401Record.subject_code}`);
      console.log(`  Subject Name: "${bcs401Record.subject_name}"`);
      console.log(`  IA1: ${bcs401Record.ia1_marks || 'N/A'}`);
      console.log(`  IA2: ${bcs401Record.ia2_marks || 'N/A'}`);
      console.log(`  IA3: ${bcs401Record.ia3_marks || 'N/A'}`);
      console.log(`  Assignment: ${bcs401Record.assignment_marks || 'N/A'}`);
      
      if (bcs401Record.subject_name === 'Unknown Subject') {
        console.log('❌ BCS401 still showing as "Unknown Subject" - fix did not work');
      } else if (bcs401Record.subject_name === 'ANALYSIS & DESIGN OF ALGORITHMS') {
        console.log('✅ BCS401 now showing correct subject name - fix worked!');
      } else {
        console.log(`⚠️ BCS401 showing unexpected name: "${bcs401Record.subject_name}"`);
      }
    } else {
      console.log('❌ BCS401 not found in IA marks');
    }

    console.log('\n📋 All IA Subjects:');
    progressData.ia_marks.forEach(record => {
      const status = record.subject_name === 'Unknown Subject' ? '❌' : '✅';
      console.log(`  ${status} ${record.subject_code}: "${record.subject_name}"`);
    });

    console.log('\n📊 Attendance Results:');
    console.log(`Total subjects with attendance: ${progressData.attendance.length}`);
    
    // Check if BCS401 has attendance data with proper name
    const bcs401Attendance = progressData.attendance.find(record => record.subject_code === 'BCS401');
    if (bcs401Attendance) {
      console.log(`✅ BCS401 Attendance: "${bcs401Attendance.subject_name}" - ${bcs401Attendance.percentage}%`);
    } else {
      console.log('❌ BCS401 not found in attendance data');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testCaseInsensitiveFix();
