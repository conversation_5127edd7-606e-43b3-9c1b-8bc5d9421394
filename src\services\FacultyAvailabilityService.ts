// FacultyAvailabilityService.ts
import { supabase } from "@/integrations/supabase/client";
import { TimeStructure } from "./TimetableService";
import { TimeSlotValidationService } from "./TimeSlotValidationService";
import { v4 as uuidv4 } from 'uuid';

/**
 * Centralized Faculty Availability Recalculator
 * This service ensures that faculty availability is always accurate across all timetable operations
 */
export class FacultyAvailabilityRecalculator {
  /**
   * Recalculate faculty availability for specific faculty members based on ALL their current timetable commitments
   * This is the main method that should be called after any timetable operation
   */
  static async recalculateForFaculty(
    facultyIds: string[],
    timeStructure: TimeStructure,
    academicYear: string
  ): Promise<void> {
    try {
      console.log(`🔄 Recalculating availability for ${facultyIds.length} faculty members`);

      if (!facultyIds.length) {
        console.log("No faculty IDs provided for recalculation");
        return;
      }

      // Get standard time slots from time structure
      const standardTimeSlots = TimeSlotValidationService.generateTimeSlots(timeStructure);

      for (const facultyId of facultyIds) {
        await this.recalculateSingleFaculty(facultyId, timeStructure, academicYear, standardTimeSlots);
      }

      console.log(`✅ Successfully recalculated availability for ${facultyIds.length} faculty members`);
    } catch (error) {
      console.error("❌ Error in faculty availability recalculation:", error);
      throw error;
    }
  }

  /**
   * Recalculate availability for a single faculty member
   */
  private static async recalculateSingleFaculty(
    facultyId: string,
    timeStructure: TimeStructure,
    academicYear: string,
    standardTimeSlots: string[]
  ): Promise<void> {
    try {
      // Initialize availability with all slots available
      const vacant_by_day: Record<string, number> = {};
      const vacant_count_by_day: Record<string, string[]> = {};

      // Set default availability for all working days
      timeStructure.working_days.forEach(day => {
        vacant_by_day[day] = 7;
        vacant_count_by_day[day] = [...standardTimeSlots];
      });

      // Get ALL timetable commitments for this faculty across all semester-sections
      const { data: allCommitments, error: commitmentsError } = await supabase
        .from('timetable_slots')
        .select('day, time_slot, subject_type, semester, section')
        .eq('academic_year', academicYear)
        .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`);

      if (commitmentsError) {
        console.error(`Error fetching commitments for faculty ${facultyId}:`, commitmentsError);
        return;
      }

      console.log(`Faculty ${facultyId} has ${allCommitments?.length || 0} total commitments`);

      // Process each commitment and remove occupied time slots
      if (allCommitments && allCommitments.length > 0) {
        for (const commitment of allCommitments) {
          const day = commitment.day;
          const timeSlot = commitment.time_slot;

          if (!vacant_count_by_day[day]) {
            console.warn(`Day ${day} not found in working days for faculty ${facultyId}`);
            continue;
          }

          // Convert time slot to individual periods that need to be removed
          const occupiedPeriods = this.getOccupiedPeriods(timeSlot, standardTimeSlots);

          // Remove occupied periods from available slots
          vacant_count_by_day[day] = vacant_count_by_day[day].filter(
            slot => !occupiedPeriods.includes(slot)
          );

          console.log(`Faculty ${facultyId} - ${day}: Removed ${occupiedPeriods.join(', ')} for ${commitment.subject_type}`);
        }

        // Update vacant_by_day counts
        timeStructure.working_days.forEach(day => {
          vacant_by_day[day] = vacant_count_by_day[day].length;
        });
      }

      // Update the database
      const { error: updateError } = await supabase
        .from('employee_details')
        .update({
          vacant_by_day,
          vacant_count_by_day
        })
        .eq('id', facultyId);

      if (updateError) {
        console.error(`Error updating availability for faculty ${facultyId}:`, updateError);
        throw updateError;
      }

      // Log final availability
      timeStructure.working_days.forEach(day => {
        console.log(`Faculty ${facultyId} - ${day}: ${vacant_by_day[day]} slots available`);
      });

    } catch (error) {
      console.error(`Error recalculating availability for faculty ${facultyId}:`, error);
      throw error;
    }
  }

  /**
   * Convert a time slot (e.g., "08:30-11:30") to individual period slots that it occupies
   */
  private static getOccupiedPeriods(timeSlot: string, standardTimeSlots: string[]): string[] {
    const [startTime, endTime] = timeSlot.split('-');
    const occupiedPeriods: string[] = [];

    for (const slot of standardTimeSlots) {
      const [slotStart, slotEnd] = slot.split('-');

      // Check if this standard slot overlaps with the given time slot
      if (this.timeOverlaps(startTime, endTime, slotStart, slotEnd)) {
        occupiedPeriods.push(slot);
      }
    }

    return occupiedPeriods;
  }

  /**
   * Check if two time ranges overlap
   */
  private static timeOverlaps(start1: string, end1: string, start2: string, end2: string): boolean {
    const parseTime = (time: string) => {
      const [hours, minutes] = time.split(':').map(Number);
      return hours * 60 + minutes;
    };

    const start1Min = parseTime(start1);
    const end1Min = parseTime(end1);
    const start2Min = parseTime(start2);
    const end2Min = parseTime(end2);

    return start1Min < end2Min && end1Min > start2Min;
  }

  /**
   * Recalculate availability for all faculty members affected by a timetable operation
   * This should be called after any bulk operation like clearing or generating timetables
   */
  static async recalculateForAllAffectedFaculty(
    academicYear: string,
    department: string,
    timeStructure: TimeStructure
  ): Promise<void> {
    try {
      console.log(`🔄 Recalculating availability for all faculty in ${department} for ${academicYear}`);

      // Get all faculty members in the department
      const { data: facultyMembers, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name')
        .eq('department', department)
        .contains('roles', ['faculty']);

      if (facultyError || !facultyMembers) {
        console.error("Error fetching faculty members:", facultyError);
        return;
      }

      const facultyIds = facultyMembers.map(f => f.id);
      await this.recalculateForFaculty(facultyIds, timeStructure, academicYear);

      console.log(`✅ Recalculated availability for all ${facultyIds.length} faculty members in ${department}`);
    } catch (error) {
      console.error("❌ Error recalculating availability for all faculty:", error);
      throw error;
    }
  }
}

/**
 * Service to manage faculty availability for timetable scheduling
 */
export class FacultyAvailabilityService {
  /**
   * Save the current faculty availability state before making changes
   * This creates a history record that can be restored later
   */
  static async saveAvailabilityState(
    facultyIds: string[],
    academicYear: string,
    department: string,
    semester: string,
    section: string
  ): Promise<void> {
    try {
      console.log(`Saving availability state for ${facultyIds.length} faculty members`);

      // Process each faculty member
      for (const facultyId of facultyIds) {
        // Get current faculty availability
        const { data: faculty, error: facultyError } = await supabase
          .from('employee_details')
          .select('vacant_by_day, vacant_count_by_day')
          .eq('id', facultyId)
          .single();

        if (facultyError) {
          console.error(`Error fetching faculty ${facultyId}:`, facultyError);
          continue;
        }

        if (!faculty) {
          console.error(`Faculty ${facultyId} not found`);
          continue;
        }

        // Save to history table
        const { error: insertError } = await supabase
          .from('faculty_availability_history')
          .insert({
            id: uuidv4(),
            faculty_id: facultyId,
            academic_year: academicYear,
            department,
            semester,
            section,
            vacant_by_day: faculty.vacant_by_day,
            vacant_count_by_day: faculty.vacant_count_by_day,
            created_at: new Date().toISOString()
          });

        if (insertError) {
          console.error(`Error saving availability history for faculty ${facultyId}:`, insertError);
        } else {
          console.log(`Saved availability history for faculty ${facultyId}`);
        }
      }
    } catch (error) {
      console.error("Error saving faculty availability history:", error);
      throw error;
    }
  }

  /**
   * Restore faculty availability to the most recent saved state
   */
  static async restoreAvailabilityState(
    academicYear: string,
    department: string,
    semester: string,
    section: string
  ): Promise<void> {
    try {
      console.log(`Restoring availability state for ${academicYear}, ${department}, ${semester}, ${section}`);

      // Get all faculty members with saved history
      const { data: historyRecords, error: historyError } = await supabase
        .from('faculty_availability_history')
        .select('faculty_id, vacant_by_day, vacant_count_by_day, created_at')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .order('created_at', { ascending: false });

      if (historyError) {
        console.error("Error fetching availability history:", historyError);
        throw historyError;
      }

      if (!historyRecords || historyRecords.length === 0) {
        console.log("No availability history found to restore");
        return;
      }

      // Group by faculty_id and get the most recent record for each
      const facultyHistoryMap = new Map();
      historyRecords.forEach(record => {
        if (!facultyHistoryMap.has(record.faculty_id)) {
          facultyHistoryMap.set(record.faculty_id, record);
        }
      });

      console.log(`Found history records for ${facultyHistoryMap.size} faculty members`);

      // Restore each faculty member's availability
      for (const [facultyId, record] of facultyHistoryMap.entries()) {
        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day: record.vacant_by_day,
            vacant_count_by_day: record.vacant_count_by_day
          })
          .eq('id', facultyId);

        if (updateError) {
          console.error(`Error restoring availability for faculty ${facultyId}:`, updateError);
        } else {
          console.log(`Restored availability for faculty ${facultyId}`);
        }
      }

      // Clean up history records after successful restoration
      const { error: deleteError } = await supabase
        .from('faculty_availability_history')
        .delete()
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section);

      if (deleteError) {
        console.error("Error cleaning up availability history:", deleteError);
      } else {
        console.log("Cleaned up availability history records");
      }
    } catch (error) {
      console.error("Error restoring faculty availability:", error);
      throw error;
    }
  }
  /**
   * Reset availability for a specific faculty member
   * This can be used to manually fix availability issues
   */
  static async resetFacultyAvailability(facultyId: string, timeStructure: TimeStructure): Promise<void> {
    try {
      console.log(`Resetting availability for faculty ${facultyId}`);

      // Define the standard time slots for all days
      const standardTimeSlots = [
        "08:30-09:25",
        "09:25-10:20",
        "10:35-11:30",
        "11:30-12:25",
        "13:15-14:10",
        "14:10-15:05",
        "15:05-16:00"
      ];

      // Create vacant_by_day and vacant_count_by_day objects with correct default values
      const vacant_by_day: Record<string, number> = {};
      const vacant_count_by_day: Record<string, string[]> = {};

      // Initialize for each working day with the standard 7 slots
      timeStructure.working_days.forEach(day => {
        // Count of available slots per day - always 7
        vacant_by_day[day] = 7;

        // List of available time slots per day - use the standard time slots
        vacant_count_by_day[day] = [...standardTimeSlots];
      });

      // Save the reset availability
      const { error: updateError } = await supabase
        .from('employee_details')
        .update({
          vacant_by_day,
          vacant_count_by_day
        })
        .eq('id', facultyId);

      if (updateError) {
        console.error(`Error resetting faculty ${facultyId} availability:`, updateError);
        throw updateError;
      }

      console.log(`Successfully reset availability for faculty ${facultyId}`);
    } catch (error) {
      console.error("Error resetting faculty availability:", error);
      throw error;
    }
  }
  /**
   * Initialize faculty availability for all faculty members
   * This should be called when setting up the system or resetting availability
   */
  static async initializeFacultyAvailability(timeStructure: TimeStructure, academicYear?: string): Promise<void> {
    try {
      // Get all faculty members
      const { data: facultyMembers, error: facultyError } = await supabase
        .from('employee_details')
        .select('id')
        .filter('roles', 'cs', '{faculty}');

      if (facultyError) {
        console.error("Error fetching faculty members:", facultyError);
        throw facultyError;
      }

      if (!facultyMembers || facultyMembers.length === 0) {
        console.log("No faculty members found");
        return;
      }

      console.log(`Initializing availability for ${facultyMembers.length} faculty members`);

      // Define the standard time slots for all days
      const standardTimeSlots = [
        "08:30-09:25",
        "09:25-10:20",
        "10:35-11:30",
        "11:30-12:25",
        "13:15-14:10",
        "14:10-15:05",
        "15:05-16:00"
      ];

      // Create vacant_by_day and vacant_count_by_day objects with correct default values
      const vacant_by_day: Record<string, number> = {};
      const vacant_count_by_day: Record<string, string[]> = {};

      // Initialize for each working day with the standard 7 slots
      timeStructure.working_days.forEach(day => {
        // Count of available slots per day - always 7
        vacant_by_day[day] = 7;

        // List of available time slots per day - use the standard time slots
        vacant_count_by_day[day] = [...standardTimeSlots];
      });

      // Get existing timetable slots if academicYear is provided
      let existingSlots: any[] = [];
      if (academicYear) {
        const { data: slots, error: slotsError } = await supabase
          .from('timetable_slots')
          .select('*')
          .eq('academic_year', academicYear);

        if (!slotsError && slots) {
          existingSlots = slots;
          console.log(`Found ${existingSlots.length} existing timetable slots`);
        }
      }

      // Update each faculty member
      for (const faculty of facultyMembers) {
        // Create a copy of the initial availability for this faculty
        const facultyVacantByDay = { ...vacant_by_day };
        const facultyVacantCountByDay = JSON.parse(JSON.stringify(vacant_count_by_day));

        // If we have existing slots, adjust the availability
        if (existingSlots.length > 0) {
          // Filter slots for this faculty
          const facultySlots = existingSlots.filter(
            slot => slot.faculty_id === faculty.id || slot.faculty2_id === faculty.id
          );

          console.log(`Faculty ${faculty.id} has ${facultySlots.length} existing slots`);

          // Process each slot to update availability
          for (const slot of facultySlots) {
            const day = slot.day;
            const timeSlot = slot.time_slot;

            if (day && timeSlot && facultyVacantCountByDay[day]) {
              // Determine if this is a morning or afternoon slot
              const isMorningSlot = this.isMorningTimeSlot(timeSlot, timeStructure);

              // Get the individual periods this slot occupies
              const occupiedPeriods = this.getLabOccupiedPeriods(timeSlot, isMorningSlot, timeStructure);

              // Update vacant_count_by_day by removing the occupied periods
              facultyVacantCountByDay[day] = facultyVacantCountByDay[day].filter(
                (slot: string) => !occupiedPeriods.includes(slot)
              );

              // Update vacant_by_day to match the actual count
              facultyVacantByDay[day] = facultyVacantCountByDay[day].length;
            }
          }
        }

        // Save the updated availability for this faculty
        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day: facultyVacantByDay,
            vacant_count_by_day: facultyVacantCountByDay
          })
          .eq('id', faculty.id);

        if (updateError) {
          console.error(`Error updating faculty ${faculty.id}:`, updateError);
        } else {
          console.log(`Initialized availability for faculty ${faculty.id}`);
        }
      }

      console.log("Faculty availability initialized successfully");
    } catch (error) {
      console.error("Error initializing faculty availability:", error);
      throw error;
    }
  }

  /**
   * Update faculty availability when a lab slot is assigned
   * @param facultyIds Array of faculty IDs (primary and secondary)
   * @param day Day of the week
   * @param timeSlot Time slot in format "HH:MM-HH:MM"
   * @param timeStructure Time structure for the department
   * @param academicYear Academic year
   * @param department Department
   * @param semester Semester
   * @param section Section
   * @param saveState Whether to save the current state before updating
   */
  static async updateFacultyAvailability(
    facultyIds: string[],
    day: string,
    timeSlot: string,
    timeStructure: TimeStructure,
    academicYear?: string,
    department?: string,
    semester?: string,
    section?: string,
    saveState: boolean = false
  ): Promise<void> {
    try {
      // Skip if no faculty IDs provided
      if (!facultyIds.length) {
        console.log("No faculty IDs provided for availability update");
        return;
      }

      console.log(`Updating availability for ${facultyIds.length} faculty members on ${day} at ${timeSlot}`);

      // Save current state if requested and all required parameters are provided
      if (saveState && academicYear && department && semester && section) {
        try {
          await this.saveAvailabilityState(facultyIds, academicYear, department, semester, section);
        } catch (error) {
          console.error("Error saving availability state:", error);
          // Continue with the update even if saving state fails
        }
      }

      // Determine if this is a morning or afternoon lab
      const isMorningLab = this.isMorningTimeSlot(timeSlot, timeStructure);

      // Get the individual periods this lab occupies
      const occupiedPeriods = this.getLabOccupiedPeriods(timeSlot, isMorningLab, timeStructure);

      console.log(`Lab occupies ${occupiedPeriods.length} periods:`, occupiedPeriods);

      // Update each faculty member
      for (const facultyId of facultyIds) {
        // Get current faculty availability
        const { data: faculty, error: facultyError } = await supabase
          .from('employee_details')
          .select('vacant_by_day, vacant_count_by_day')
          .eq('id', facultyId)
          .single();

        if (facultyError) {
          console.error(`Error fetching faculty ${facultyId}:`, facultyError);
          continue;
        }

        if (!faculty) {
          console.error(`Faculty ${facultyId} not found`);
          continue;
        }

        // Define the standard time slots
        const standardTimeSlots = [
          "08:30-09:25",
          "09:25-10:20",
          "10:35-11:30",
          "11:30-12:25",
          "13:15-14:10",
          "14:10-15:05",
          "15:05-16:00"
        ];

        // Initialize if not exists
        const vacant_by_day = faculty.vacant_by_day ? { ...faculty.vacant_by_day } : {};
        const vacant_count_by_day = faculty.vacant_count_by_day ? { ...faculty.vacant_count_by_day } : {};

        // Ensure the day exists in both objects with proper initialization
        if (!vacant_by_day[day]) vacant_by_day[day] = 7; // Default to 7 slots
        if (!vacant_count_by_day[day] || !Array.isArray(vacant_count_by_day[day])) {
          vacant_count_by_day[day] = [...standardTimeSlots];
        }

        // Update vacant_count_by_day by removing the occupied periods
        // First, ensure we're working with an array
        let daySlots = Array.isArray(vacant_count_by_day[day]) ?
          [...vacant_count_by_day[day]] : [...standardTimeSlots];

        // Filter out the occupied periods
        daySlots = daySlots.filter(slot => !occupiedPeriods.includes(slot));

        // Update the array of available slots
        vacant_count_by_day[day] = daySlots;

        // Update vacant_by_day to match the actual count of available slots
        vacant_by_day[day] = daySlots.length;

        console.log(`Faculty ${facultyId} on ${day}: ${vacant_by_day[day]} slots available`);
        console.log(`Available slots: ${JSON.stringify(vacant_count_by_day[day])}`);

        // Save the updated availability
        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day,
            vacant_count_by_day
          })
          .eq('id', facultyId);

        if (updateError) {
          console.error(`Error updating faculty ${facultyId} availability:`, updateError);
        } else {
          console.log(`Updated availability for faculty ${facultyId} on ${day}: ${vacant_by_day[day]} slots available`);
        }
      }
    } catch (error) {
      console.error("Error updating faculty availability:", error);
      throw error;
    }
  }

  /**
   * Generate all possible time slots based on time structure
   * Note: This method is currently unused but kept for future reference
   */
  private static _generateAllTimeSlots(timeStructure: TimeStructure): string[] {
    const slots: string[] = [];

    // Morning periods (first half)
    let currentTime = timeStructure.first_half_start_time;
    for (let i = 0; i < timeStructure.periods_in_first_half; i++) {
      const startTime = currentTime;
      const endTime = this.addMinutesToTime(startTime, timeStructure.theory_class_duration);

      // Skip tea break if needed
      if (startTime === timeStructure.tea_break_start_time) {
        currentTime = timeStructure.tea_break_end_time;
        continue;
      }

      slots.push(`${startTime}-${endTime}`);
      currentTime = endTime;
    }

    // Afternoon periods (second half)
    currentTime = timeStructure.second_half_start_time;
    for (let i = 0; i < timeStructure.periods_in_second_half; i++) {
      const startTime = currentTime;
      const endTime = this.addMinutesToTime(startTime, timeStructure.theory_class_duration);

      slots.push(`${startTime}-${endTime}`);
      currentTime = endTime;
    }

    return slots;
  }

  /**
   * Check if a time slot is in the morning
   */
  private static isMorningTimeSlot(timeSlot: string, timeStructure: TimeStructure): boolean {
    const startTime = timeSlot.split('-')[0];
    return this.timeIsBefore(startTime, timeStructure.lunch_break_start_time);
  }

  /**
   * Get individual periods occupied by a lab session
   * This method returns the time slots that are occupied by a lab session
   * For morning labs, it returns the first 3 periods (not 4)
   * For afternoon labs, it returns the last 3 periods
   */
  private static getLabOccupiedPeriods(timeSlot: string, isMorning: boolean, timeStructure?: TimeStructure): string[] {
    // First, try to parse as a flexible time range (e.g., "08:30-10:20")
    const flexibleMatch = timeSlot.match(/^([01][0-9]|2[0-3]):([0-5][0-9])-([01][0-9]|2[0-3]):([0-5][0-9])$/);

    if (flexibleMatch) {
      // This is a flexible time range - extract the individual periods
      const [, startHour, startMin, endHour, endMin] = flexibleMatch;
      const startTime = `${startHour}:${startMin}`;
      const endTime = `${endHour}:${endMin}`;

      // Use TimeSlotValidationService to get the time slots
      return TimeSlotValidationService.getTimeSlotStringsForRange(startTime, endTime, timeStructure);
    }

    // Fallback to predefined slot handling
    const timeSlotInfo = TimeSlotValidationService.parseTimeSlotInfo(timeSlot, timeStructure);
    if (timeSlotInfo) {
      return timeSlotInfo.timeSlots;
    }

    // Final fallback to legacy logic
    const standardTimeSlots = [
      "08:30-09:25",
      "09:25-10:20",
      "10:35-11:30",
      "11:30-12:25",
      "13:15-14:10",
      "14:10-15:05",
      "15:05-16:00"
    ];

    if (isMorning) {
      return standardTimeSlots.slice(0, 3);
    } else {
      return standardTimeSlots.slice(4);
    }
  }

  /**
   * Get individual periods occupied by a lab session based on time of day string and semester configuration
   * This method returns the time slots that are occupied by a lab session
   * Duration and periods depend on semester lab configuration
   */
  private static async getLabOccupiedPeriodsFromTimeOfDay(
    timeOfDay: string,
    timeStructure?: TimeStructure,
    academicYear?: string,
    department?: string,
    semester?: string
  ): Promise<string[]> {
    // Define the standard time slots
    const standardTimeSlots = [
      "08:30-09:25",
      "09:25-10:20",
      "10:35-11:30",
      "11:30-12:25",
      "13:15-14:10",
      "14:10-15:05",
      "15:05-16:00"
    ];

    // Try to get semester-specific configuration if available
    if (academicYear && department && semester) {
      try {
        const { SemesterLabConfigurationService } = await import('@/services/SemesterLabConfigurationService');
        const config = await SemesterLabConfigurationService.getLabConfiguration(academicYear, department, semester);

        if (config) {
          const availableSlots = SemesterLabConfigurationService.getAvailableTimeSlots(config);
          const matchingSlot = availableSlots.find(slot => slot.name === timeOfDay);

          if (matchingSlot) {
            const duration = matchingSlot.duration;

            // Map time slot to periods based on configuration
            if (matchingSlot.id.includes('morning')) {
              return standardTimeSlots.slice(0, duration); // Periods 1-2 or 1-3
            } else if (matchingSlot.id.includes('mid')) {
              return standardTimeSlots.slice(2, 2 + duration); // Periods 3-4
            } else if (matchingSlot.id.includes('early_afternoon')) {
              return standardTimeSlots.slice(4, 4 + duration); // Periods 5-6
            } else if (matchingSlot.id.includes('afternoon')) {
              if (duration === 2) {
                return standardTimeSlots.slice(4, 6); // Periods 5-6 for 2-hour
              } else {
                return standardTimeSlots.slice(4); // Periods 5-7 for 3-hour
              }
            }
          }
        }
      } catch (error) {
        console.warn('Could not load semester lab configuration, falling back to defaults:', error);
      }
    }

    // Fallback to standard mapping for backward compatibility
    if (timeOfDay.toLowerCase() === "morning") {
      // Return the first 3 periods (morning slots)
      return standardTimeSlots.slice(0, 3);
    }
    // For "Afternoon" - include all 3 afternoon periods
    else {
      // Return the last 3 periods (afternoon slots)
      return standardTimeSlots.slice(4);
    }
  }

  /**
   * Helper method to add minutes to a time string
   */
  private static addMinutesToTime(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, mins, 0, 0);
    date.setMinutes(date.getMinutes() + minutes);
    return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  }

  /**
   * Helper method to check if one time is before another
   */
  private static timeIsBefore(time1: string, time2: string): boolean {
    const [hours1, minutes1] = time1.split(':').map(Number);
    const [hours2, minutes2] = time2.split(':').map(Number);

    if (hours1 < hours2) return true;
    if (hours1 > hours2) return false;
    return minutes1 < minutes2;
  }



  /**
   * Debug faculty availability for a specific faculty member
   * This can be used to check the current state of faculty availability
   */
  static async debugFacultyAvailability(facultyId: string): Promise<void> {
    try {
      console.log(`Debugging availability for faculty ${facultyId}`);

      // Get current faculty availability
      const { data: faculty, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, vacant_by_day, vacant_count_by_day')
        .eq('id', facultyId)
        .single();

      if (facultyError) {
        console.error(`Error fetching faculty ${facultyId}:`, facultyError);
        return;
      }

      if (!faculty) {
        console.error(`Faculty ${facultyId} not found`);
        return;
      }

      console.log(`Faculty: ${faculty.full_name} (${faculty.id})`);
      console.log(`vacant_by_day:`, faculty.vacant_by_day);

      // Log each day's available slots
      if (faculty.vacant_count_by_day) {
        console.log(`vacant_count_by_day:`);
        Object.entries(faculty.vacant_count_by_day).forEach(([day, slots]) => {
          if (Array.isArray(slots)) {
            console.log(`  ${day}: ${slots.length} slots`);
            console.log(`    ${slots.join(', ')}`);
          } else {
            console.log(`  ${day}: Invalid slots format`);
          }
        });
      } else {
        console.log(`vacant_count_by_day: null or undefined`);
      }
    } catch (error) {
      console.error("Error debugging faculty availability:", error);
    }
  }

  /**
   * Reset all faculty availability to the correct default values
   * This should be used when clearing the timetable
   */
  static async resetAllFacultyAvailability(timeStructure: TimeStructure): Promise<void> {
    try {
      // Get all faculty members
      const { data: facultyMembers, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name')
        .filter('roles', 'cs', '{faculty}');

      if (facultyError) {
        console.error("Error fetching faculty members:", facultyError);
        throw facultyError;
      }

      if (!facultyMembers || facultyMembers.length === 0) {
        console.log("No faculty members found");
        return;
      }

      console.log(`Resetting availability for ${facultyMembers.length} faculty members`);

      // Define the standard time slots for all days
      const standardTimeSlots = [
        "08:30-09:25",
        "09:25-10:20",
        "10:35-11:30",
        "11:30-12:25",
        "13:15-14:10",
        "14:10-15:05",
        "15:05-16:00"
      ];

      // Create vacant_by_day and vacant_count_by_day objects with correct default values
      const vacant_by_day: Record<string, number> = {};
      const vacant_count_by_day: Record<string, string[]> = {};

      // Initialize for each working day with the standard 7 slots
      timeStructure.working_days.forEach(day => {
        // Count of available slots per day - always 7
        vacant_by_day[day] = 7;

        // List of available time slots per day - use the standard time slots
        vacant_count_by_day[day] = [...standardTimeSlots];
      });

      // Update each faculty member
      for (const faculty of facultyMembers) {
        console.log(`Resetting availability for faculty ${faculty.full_name} (${faculty.id})`);

        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day,
            vacant_count_by_day
          })
          .eq('id', faculty.id);

        if (updateError) {
          console.error(`Error resetting faculty ${faculty.id} availability:`, updateError);
        } else {
          console.log(`Successfully reset availability for faculty ${faculty.full_name} (${faculty.id})`);
        }
      }

      console.log("All faculty availability reset successfully");
    } catch (error) {
      console.error("Error resetting all faculty availability:", error);
      throw error;
    }
  }

  /**
   * Update faculty availability when lab time slots are added
   * This function is called when lab time slots are saved through the "Save Mapping Details" button
   * @param facultyId Primary faculty ID
   * @param faculty2Id Secondary faculty ID (optional)
   * @param day Day of the week
   * @param timeOfDay Time of day ("Morning" or "Afternoon")
   */
  static async updateFacultyAvailabilityFromLabSlots(
    facultyId: string,
    faculty2Id: string | null,
    day: string,
    timeOfDay: string
  ): Promise<void> {
    try {
      console.log(`Updating faculty availability for lab slot on ${day} at ${timeOfDay}`);

      // Define the standard time slots for all days
      const standardTimeSlots = [
        "08:30-09:25",
        "09:25-10:20",
        "10:35-11:30",
        "11:30-12:25",
        "13:15-14:10",
        "14:10-15:05",
        "15:05-16:00"
      ];

      // Parse the time slot information to get the actual occupied periods
      const timeSlotInfo = TimeSlotValidationService.parseTimeSlotInfo(timeOfDay);
      let occupiedPeriods: string[] = [];

      if (timeSlotInfo) {
        occupiedPeriods = timeSlotInfo.timeSlots;
        console.log(`Using ${timeSlotInfo.isFlexibleFormat ? 'flexible' : 'predefined'} format for ${timeOfDay}`);
        console.log(`Duration: ${timeSlotInfo.duration} periods`);
      } else {
        // Fallback to legacy logic
        if (timeOfDay.toLowerCase() === "morning") {
          occupiedPeriods = standardTimeSlots.slice(0, 3);
        } else {
          occupiedPeriods = standardTimeSlots.slice(4);
        }
        console.log(`Using fallback logic for ${timeOfDay}`);
      }

      console.log(`Lab occupies ${occupiedPeriods.length} periods:`, occupiedPeriods);

      // Create an array of faculty IDs to update (primary and secondary if provided)
      const facultyIds: string[] = [facultyId];
      if (faculty2Id) {
        facultyIds.push(faculty2Id);
      }

      // Update each faculty member's availability
      for (const id of facultyIds) {
        // Get current faculty availability
        const { data: faculty, error: facultyError } = await supabase
          .from('employee_details')
          .select('vacant_by_day, vacant_count_by_day, full_name')
          .eq('id', id)
          .single();

        if (facultyError) {
          console.error(`Error fetching faculty ${id}:`, facultyError);
          continue;
        }

        if (!faculty) {
          console.error(`Faculty ${id} not found`);
          continue;
        }

        console.log(`Updating availability for faculty ${faculty.full_name} (${id})`);

        // Initialize if not exists
        const vacant_by_day = faculty.vacant_by_day ? { ...faculty.vacant_by_day } : {};
        const vacant_count_by_day = faculty.vacant_count_by_day ? { ...faculty.vacant_count_by_day } : {};

        // Ensure the day exists in both objects with proper initialization
        if (!vacant_by_day[day]) vacant_by_day[day] = 7; // Default to 7 slots
        if (!vacant_count_by_day[day] || !Array.isArray(vacant_count_by_day[day])) {
          vacant_count_by_day[day] = [...standardTimeSlots];
        }

        // Update vacant_count_by_day by removing the occupied periods
        // First, ensure we're working with an array
        let daySlots = Array.isArray(vacant_count_by_day[day]) ?
          [...vacant_count_by_day[day]] : [...standardTimeSlots];

        // Filter out the occupied periods
        daySlots = daySlots.filter(slot => !occupiedPeriods.includes(slot));

        // Update the array of available slots
        vacant_count_by_day[day] = daySlots;

        // Update vacant_by_day to match the actual count of available slots
        vacant_by_day[day] = daySlots.length;

        console.log(`Faculty ${id} on ${day}: ${vacant_by_day[day]} slots available`);
        console.log(`Available slots: ${JSON.stringify(vacant_count_by_day[day])}`);

        // Save the updated availability
        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day,
            vacant_count_by_day
          })
          .eq('id', id);

        if (updateError) {
          console.error(`Error updating faculty ${id} availability:`, updateError);
        } else {
          console.log(`Updated availability for faculty ${id} on ${day}: ${vacant_by_day[day]} slots available`);
        }
      }
    } catch (error) {
      console.error("Error updating faculty availability from lab slots:", error);
      throw error;
    }
  }

  /**
   * Restore faculty availability when lab time slots are deleted
   * This function is called when a lab mapping is deleted
   * @param facultyId Primary faculty ID
   * @param faculty2Id Secondary faculty ID (optional)
   * @param day Day of the week
   * @param timeOfDay Time of day ("Morning" or "Afternoon")
   */
  static async restoreFacultyAvailabilityFromLabSlots(
    facultyId: string,
    faculty2Id: string | null,
    day: string,
    timeOfDay: string
  ): Promise<void> {
    try {
      console.log(`Restoring faculty availability for lab slot on ${day} at ${timeOfDay}`);

      // Define the standard time slots for all days
      const standardTimeSlots = [
        "08:30-09:25",
        "09:25-10:20",
        "10:35-11:30",
        "11:30-12:25",
        "13:15-14:10",
        "14:10-15:05",
        "15:05-16:00"
      ];

      // Parse the time slot information to get the actual periods to restore
      const timeSlotInfo = TimeSlotValidationService.parseTimeSlotInfo(timeOfDay);
      let periodsToRestore: string[] = [];

      if (timeSlotInfo) {
        periodsToRestore = timeSlotInfo.timeSlots;
        console.log(`Restoring ${timeSlotInfo.isFlexibleFormat ? 'flexible' : 'predefined'} format for ${timeOfDay}`);
        console.log(`Duration: ${timeSlotInfo.duration} periods`);
      } else {
        // Fallback to legacy logic
        if (timeOfDay.toLowerCase() === "morning") {
          periodsToRestore = standardTimeSlots.slice(0, 3);
        } else {
          periodsToRestore = standardTimeSlots.slice(4);
        }
        console.log(`Using fallback logic for restoring ${timeOfDay}`);
      }

      console.log(`Restoring ${periodsToRestore.length} periods:`, periodsToRestore);

      // Create an array of faculty IDs to update (primary and secondary if provided)
      const facultyIds: string[] = [facultyId];
      if (faculty2Id) {
        facultyIds.push(faculty2Id);
      }

      // Update each faculty member's availability
      for (const id of facultyIds) {
        // Get current faculty availability
        const { data: faculty, error: facultyError } = await supabase
          .from('employee_details')
          .select('vacant_by_day, vacant_count_by_day, full_name')
          .eq('id', id)
          .single();

        if (facultyError) {
          console.error(`Error fetching faculty ${id}:`, facultyError);
          continue;
        }

        if (!faculty) {
          console.error(`Faculty ${id} not found`);
          continue;
        }

        console.log(`Restoring availability for faculty ${faculty.full_name} (${id})`);

        // Initialize if not exists
        const vacant_by_day = faculty.vacant_by_day ? { ...faculty.vacant_by_day } : {};
        const vacant_count_by_day = faculty.vacant_count_by_day ? { ...faculty.vacant_count_by_day } : {};

        // Ensure the day exists in both objects with proper initialization
        if (!vacant_by_day[day]) vacant_by_day[day] = 7; // Default to 7 slots
        if (!vacant_count_by_day[day] || !Array.isArray(vacant_count_by_day[day])) {
          vacant_count_by_day[day] = [...standardTimeSlots];
        }

        // Update vacant_count_by_day by adding back the periods to restore
        // First, ensure we're working with an array
        let daySlots = Array.isArray(vacant_count_by_day[day]) ?
          [...vacant_count_by_day[day]] : [...standardTimeSlots];

        // Add back the periods to restore, but only if they're not already there
        for (const period of periodsToRestore) {
          if (!daySlots.includes(period)) {
            daySlots.push(period);
          }
        }

        // Sort the slots to maintain the correct order
        daySlots.sort((a, b) => {
          const aIndex = standardTimeSlots.indexOf(a);
          const bIndex = standardTimeSlots.indexOf(b);
          return aIndex - bIndex;
        });

        // Update the array of available slots
        vacant_count_by_day[day] = daySlots;

        // Update vacant_by_day to match the actual count of available slots
        vacant_by_day[day] = daySlots.length;

        console.log(`Faculty ${id} on ${day}: ${vacant_by_day[day]} slots available after restoration`);
        console.log(`Available slots after restoration: ${JSON.stringify(vacant_count_by_day[day])}`);

        // Save the updated availability
        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day,
            vacant_count_by_day
          })
          .eq('id', id);

        if (updateError) {
          console.error(`Error restoring faculty ${id} availability:`, updateError);
        } else {
          console.log(`Restored availability for faculty ${id} on ${day}: ${vacant_by_day[day]} slots available`);
        }
      }
    } catch (error) {
      console.error("Error restoring faculty availability from lab slots:", error);
      throw error;
    }
  }

  /**
   * Reset faculty availability to the default values with Wednesday having 4 slots
   * This is specifically for the admin reset functionality
   */
  static async resetFacultyAvailabilityToDefaults(): Promise<void> {
    try {
      // Get all faculty members
      const { data: facultyMembers, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name')
        .filter('roles', 'cs', '{faculty}');

      if (facultyError) {
        console.error("Error fetching faculty members:", facultyError);
        throw facultyError;
      }

      if (!facultyMembers || facultyMembers.length === 0) {
        console.log("No faculty members found");
        return;
      }

      console.log(`Resetting availability for ${facultyMembers.length} faculty members to default values`);

      // Define the standard time slots for all days
      const standardTimeSlots = [
        "08:30-09:25",
        "09:25-10:20",
        "10:35-11:30",
        "11:30-12:25",
        "13:15-14:10",
        "14:10-15:05",
        "15:05-16:00"
      ];

      // Create vacant_by_day with correct default values
      const vacant_by_day = {
        "Monday": 7,
        "Tuesday": 7,
        "Wednesday": 7,
        "Thursday": 7,
        "Friday": 7
      };

      // Create vacant_count_by_day with correct default values
      const vacant_count_by_day = {
        "Monday": [...standardTimeSlots],
        "Tuesday": [...standardTimeSlots],
        "Wednesday": [...standardTimeSlots],
        "Thursday": [...standardTimeSlots],
        "Friday": [...standardTimeSlots]
      };

      // Update each faculty member
      for (const faculty of facultyMembers) {
        console.log(`Resetting availability for faculty ${faculty.full_name} (${faculty.id})`);

        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day,
            vacant_count_by_day
          })
          .eq('id', faculty.id);

        if (updateError) {
          console.error(`Error resetting faculty ${faculty.id} availability:`, updateError);
        } else {
          console.log(`Successfully reset availability for faculty ${faculty.full_name} (${faculty.id})`);
        }
      }

      console.log("All faculty availability reset successfully to default values");
      return;
    } catch (error) {
      console.error("Error resetting faculty availability to defaults:", error);
      throw error;
    }
  }

  /**
   * Reset faculty availability based on lab assignments
   * This function recalculates faculty availability based on their current lab assignments
   * It's used by the "Reset Lab Slots" button in the admin interface
   */
  static async resetFacultyAvailabilityFromLabs(): Promise<void> {
    try {
      // Get all faculty members
      const { data: facultyMembers, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name')
        .filter('roles', 'cs', '{faculty}');

      if (facultyError) {
        console.error("Error fetching faculty members:", facultyError);
        throw facultyError;
      }

      if (!facultyMembers || facultyMembers.length === 0) {
        console.log("No faculty members found");
        return;
      }

      console.log(`Recalculating lab-based availability for ${facultyMembers.length} faculty members`);

      // First, reset all faculty to default availability
      // Define the standard time slots for all days
      const standardTimeSlots = [
        "08:30-09:25",
        "09:25-10:20",
        "10:35-11:30",
        "11:30-12:25",
        "13:15-14:10",
        "14:10-15:05",
        "15:05-16:00"
      ];

      // Create vacant_by_day with correct default values
      const vacant_by_day = {
        "Monday": 7,
        "Tuesday": 7,
        "Wednesday": 7,
        "Thursday": 7,
        "Friday": 7
      };

      // Create vacant_count_by_day with correct default values
      const vacant_count_by_day = {
        "Monday": [...standardTimeSlots],
        "Tuesday": [...standardTimeSlots],
        "Wednesday": [...standardTimeSlots],
        "Thursday": [...standardTimeSlots],
        "Friday": [...standardTimeSlots]
      };

      // Reset all faculty members to default availability
      for (const faculty of facultyMembers) {
        console.log(`Resetting availability for faculty ${faculty.full_name} (${faculty.id})`);

        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day,
            vacant_count_by_day
          })
          .eq('id', faculty.id);

        if (updateError) {
          console.error(`Error resetting faculty ${faculty.id} availability:`, updateError);
        }
      }

      // Now, get all lab mappings and update faculty availability
      const { data: labMappings, error: labMappingsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('id, faculty_1_id, faculty_2_id, subject_code, subject_name')
        .in('subject_type', ['laboratory', 'lab'])
        .not('subject_code', 'like', 'DELETED_%');

      if (labMappingsError) {
        console.error("Error fetching lab mappings:", labMappingsError);
        throw labMappingsError;
      }

      console.log(`Found ${labMappings?.length || 0} lab mappings`);

      if (!labMappings || labMappings.length === 0) {
        console.log("No lab mappings found, faculty availability reset to defaults");
        return;
      }

      // Get all lab time slots
      const { data: labTimeSlots, error: labTimeSlotsError } = await supabase
        .from('lab_time_slots')
        .select('*')
        .in('mapping_id', labMappings.map(m => m.id));

      if (labTimeSlotsError) {
        console.error("Error fetching lab time slots:", labTimeSlotsError);
        throw labTimeSlotsError;
      }

      console.log(`Found ${labTimeSlots?.length || 0} lab time slots`);

      if (!labTimeSlots || labTimeSlots.length === 0) {
        console.log("No lab time slots found, faculty availability reset to defaults");
        return;
      }

      // Create a mapping from mapping_id to faculty IDs
      const mappingToFacultyMap = new Map();
      labMappings.forEach(mapping => {
        mappingToFacultyMap.set(mapping.id, {
          faculty1Id: mapping.faculty_1_id,
          faculty2Id: mapping.faculty_2_id,
          subjectCode: mapping.subject_code,
          subjectName: mapping.subject_name
        });
      });

      // Process each lab time slot to update faculty availability
      for (const slot of labTimeSlots) {
        const facultyInfo = mappingToFacultyMap.get(slot.mapping_id);
        if (!facultyInfo) {
          console.log(`No faculty mapping found for slot ${slot.id}`);
          continue;
        }

        console.log(`Processing lab slot for ${facultyInfo.subjectCode} on ${slot.day} at ${slot.time_of_day}`);

        // Update faculty1 availability
        if (facultyInfo.faculty1Id) {
          await this.updateFacultyAvailabilityFromLabSlots(
            facultyInfo.faculty1Id,
            facultyInfo.faculty2Id,
            slot.day,
            slot.time_of_day
          );
        }
      }

      console.log("Faculty availability recalculated successfully based on lab assignments");
      return;
    } catch (error) {
      console.error("Error recalculating faculty availability from labs:", error);
      throw error;
    }
  }

  /**
   * Recalculate faculty availability after clearing a specific timetable
   * This ensures faculty availability accurately reflects their remaining commitments
   * across all active timetables, including lab assignments in other semester-sections
   */
  static async recalculateFacultyAvailabilityAfterClear(
    affectedFacultyIds: string[],
    timeStructure: TimeStructure,
    academicYear: string
  ): Promise<void> {
    try {
      if (!affectedFacultyIds.length) {
        console.log("No faculty members affected by timetable clearing");
        return;
      }

      console.log(`Recalculating availability for ${affectedFacultyIds.length} affected faculty members`);

      // Define the standard time slots for all days
      const standardTimeSlots = [
        "08:30-09:25",
        "09:25-10:20",
        "10:35-11:30",
        "11:30-12:25",
        "13:15-14:10",
        "14:10-15:05",
        "15:05-16:00"
      ];

      // Process each affected faculty member
      for (const facultyId of affectedFacultyIds) {
        console.log(`Recalculating availability for faculty ${facultyId}`);

        // Create default availability objects with all slots available
        const vacant_by_day: Record<string, number> = {};
        const vacant_count_by_day: Record<string, string[]> = {};

        // Initialize for each working day with the standard 7 slots
        timeStructure.working_days.forEach(day => {
          vacant_by_day[day] = 7;
          vacant_count_by_day[day] = [...standardTimeSlots];
        });

        // STEP 1: Check for lab assignments in lab_time_slots table across all semester-sections
        // First, get all subject mappings where this faculty is assigned (either as faculty1 or faculty2)
        const { data: facultyMappings, error: mappingsError } = await supabase
          .from('simplified_subject_faculty_mappings')
          .select('id, subject_code, subject_name, subject_type')
          .eq('academic_year', academicYear)
          .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`)
          .in('subject_type', ['laboratory', 'lab'])
          .not('subject_code', 'like', 'DELETED_%');

        if (mappingsError) {
          console.error(`Error fetching lab mappings for faculty ${facultyId}:`, mappingsError);

          // Try the original table as fallback
          const { data: originalMappings, error: originalError } = await supabase
            .from('subject_faculty_mappings')
            .select('id, subject_code, subject_name, subject_type')
            .eq('academic_year', academicYear)
            .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`)
            .in('subject_type', ['laboratory', 'lab']);

          if (originalError) {
            console.error(`Error fetching lab mappings from original table for faculty ${facultyId}:`, originalError);
            continue;
          }

          console.log(`Found ${originalMappings?.length || 0} lab mappings in original table for faculty ${facultyId}`);

          // If we found mappings in the original table, use those
          if (originalMappings && originalMappings.length > 0) {
            // Get lab time slots for these mappings
            const { data: labTimeSlots, error: labSlotsError } = await supabase
              .from('lab_time_slots')
              .select('*')
              .in('mapping_id', originalMappings.map(m => m.id));

            if (labSlotsError) {
              console.error(`Error fetching lab time slots for faculty ${facultyId}:`, labSlotsError);
            } else {
              console.log(`Found ${labTimeSlots?.length || 0} lab time slots for faculty ${facultyId}`);

              // Process each lab time slot to update availability
              if (labTimeSlots && labTimeSlots.length > 0) {
                for (const slot of labTimeSlots) {
                  const day = slot.day;
                  const timeOfDay = slot.time_of_day;

                  if (day && timeOfDay && vacant_count_by_day[day]) {
                    // Get the occupied periods based on time of day
                    const occupiedPeriods = this.getLabOccupiedPeriodsFromTimeOfDay(timeOfDay, timeStructure);

                    // Update vacant_count_by_day by removing the occupied periods
                    vacant_count_by_day[day] = vacant_count_by_day[day].filter(
                      ts => !occupiedPeriods.includes(ts)
                    );

                    // Update vacant_by_day to match the actual count
                    vacant_by_day[day] = vacant_count_by_day[day].length;

                    console.log(`Faculty ${facultyId} on ${day}: Removed ${occupiedPeriods.length} periods for lab at ${timeOfDay} - ${vacant_by_day[day]} slots remaining`);
                  }
                }
              }
            }
          }
        } else {
          console.log(`Found ${facultyMappings?.length || 0} lab mappings in simplified table for faculty ${facultyId}`);

          // If we found mappings in the simplified table, use those
          if (facultyMappings && facultyMappings.length > 0) {
            // Get lab time slots for these mappings
            const { data: labTimeSlots, error: labSlotsError } = await supabase
              .from('lab_time_slots')
              .select('*')
              .in('mapping_id', facultyMappings.map(m => m.id));

            if (labSlotsError) {
              console.error(`Error fetching lab time slots for faculty ${facultyId}:`, labSlotsError);
            } else {
              console.log(`Found ${labTimeSlots?.length || 0} lab time slots for faculty ${facultyId}`);

              // Process each lab time slot to update availability
              if (labTimeSlots && labTimeSlots.length > 0) {
                for (const slot of labTimeSlots) {
                  const day = slot.day;
                  const timeOfDay = slot.time_of_day;

                  if (day && timeOfDay && vacant_count_by_day[day]) {
                    // Get the occupied periods based on time of day
                    const occupiedPeriods = this.getLabOccupiedPeriodsFromTimeOfDay(timeOfDay, timeStructure);

                    // Update vacant_count_by_day by removing the occupied periods
                    vacant_count_by_day[day] = vacant_count_by_day[day].filter(
                      ts => !occupiedPeriods.includes(ts)
                    );

                    // Update vacant_by_day to match the actual count
                    vacant_by_day[day] = vacant_count_by_day[day].length;

                    console.log(`Faculty ${facultyId} on ${day}: Removed ${occupiedPeriods.length} periods for lab at ${timeOfDay} - ${vacant_by_day[day]} slots remaining`);
                  }
                }
              }
            }
          }
        }

        // STEP 2: Check for remaining timetable slots across all semester-sections
        const { data: facultySlots, error: slotsError } = await supabase
          .from('timetable_slots')
          .select('*')
          .eq('academic_year', academicYear)
          .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`);

        if (slotsError) {
          console.error(`Error fetching timetable slots for faculty ${facultyId}:`, slotsError);
        } else {
          console.log(`Found ${facultySlots?.length || 0} timetable slots for faculty ${facultyId}`);

          // Process each slot to update availability
          if (facultySlots && facultySlots.length > 0) {
            for (const slot of facultySlots) {
              const day = slot.day;
              const timeSlot = slot.time_slot;

              if (day && timeSlot && vacant_count_by_day[day]) {
                // Determine if this is a morning or afternoon slot
                const isMorningSlot = this.isMorningTimeSlot(timeSlot, timeStructure);

                // Get the individual periods this slot occupies
                let occupiedPeriods: string[] = [];

                if (slot.subject_type === 'lab') {
                  // For lab slots, get all periods they occupy
                  occupiedPeriods = this.getLabOccupiedPeriods(timeSlot, isMorningSlot, timeStructure);
                } else {
                  // For theory slots, just the single period
                  occupiedPeriods = [timeSlot];
                }

                // Update vacant_count_by_day by removing the occupied periods
                vacant_count_by_day[day] = vacant_count_by_day[day].filter(
                  ts => !occupiedPeriods.includes(ts)
                );

                // Update vacant_by_day to match the actual count
                vacant_by_day[day] = vacant_count_by_day[day].length;

                console.log(`Faculty ${facultyId} on ${day}: Removed ${occupiedPeriods.join(', ')} - ${vacant_by_day[day]} slots remaining`);
              }
            }
          }
        }

        // Update the faculty's availability in the database
        const { error: updateError } = await supabase
          .from('employee_details')
          .update({
            vacant_by_day,
            vacant_count_by_day
          })
          .eq('id', facultyId);

        if (updateError) {
          console.error(`Error updating availability for faculty ${facultyId}:`, updateError);
        } else {
          console.log(`Successfully recalculated availability for faculty ${facultyId}`);
        }
      }

      console.log("Faculty availability recalculation completed successfully");
    } catch (error) {
      console.error("Error recalculating faculty availability:", error);
      throw error;
    }
  }
}
