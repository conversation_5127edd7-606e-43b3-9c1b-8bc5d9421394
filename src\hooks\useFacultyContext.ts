/**
 * Faculty Context Hook
 * 
 * Custom hook for managing faculty teaching context and auto-population
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  FacultyContextService, 
  FacultyTeachingContext, 
  SubjectClassOption 
} from '@/services/FacultyContextService';

export interface UseFacultyContextReturn {
  // Context data
  context: FacultyTeachingContext | null;
  subjectOptions: SubjectClassOption[];
  
  // Loading states
  loading: boolean;
  optionsLoading: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  refreshContext: () => Promise<void>;
  getAutoFillData: (assignmentId: string) => Promise<any>;
  validateAccess: (assignmentId: string) => Promise<boolean>;
  
  // Dashboard stats
  dashboardStats: {
    total_subjects: number;
    total_classes: number;
    total_students: number;
    total_quizzes: number;
    active_quizzes: number;
    total_materials: number;
    processed_materials: number;
  } | null;
}

export const useFacultyContext = (): UseFacultyContextReturn => {
  const { user } = useAuth();
  const [context, setContext] = useState<FacultyTeachingContext | null>(null);
  const [subjectOptions, setSubjectOptions] = useState<SubjectClassOption[]>([]);
  const [dashboardStats, setDashboardStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [optionsLoading, setOptionsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load faculty context
  const loadContext = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      // Load teaching context
      const teachingContext = await FacultyContextService.getFacultyTeachingContext(user.id);
      setContext(teachingContext);

      // Load subject options
      setOptionsLoading(true);
      const options = await FacultyContextService.getFacultySubjectOptions(user.id);
      setSubjectOptions(options);

      // Load dashboard stats
      const stats = await FacultyContextService.getFacultyDashboardStats(user.id);
      setDashboardStats(stats);

    } catch (err) {
      console.error('Failed to load faculty context:', err);
      setError(err instanceof Error ? err.message : 'Failed to load faculty context');
    } finally {
      setLoading(false);
      setOptionsLoading(false);
    }
  }, [user?.id]);

  // Load context on mount and user change
  useEffect(() => {
    loadContext();
  }, [loadContext]);

  // Refresh context
  const refreshContext = useCallback(async () => {
    await loadContext();
  }, [loadContext]);

  // Get auto-fill data for a specific assignment
  const getAutoFillData = useCallback(async (assignmentId: string) => {
    try {
      return await FacultyContextService.getSubjectAutoFillData(assignmentId);
    } catch (err) {
      console.error('Failed to get auto-fill data:', err);
      throw err;
    }
  }, []);

  // Validate faculty access to assignment
  const validateAccess = useCallback(async (assignmentId: string) => {
    if (!user?.id) return false;
    
    try {
      return await FacultyContextService.validateFacultySubjectAccess(user.id, assignmentId);
    } catch (err) {
      console.error('Failed to validate access:', err);
      return false;
    }
  }, [user?.id]);

  return {
    context,
    subjectOptions,
    loading,
    optionsLoading,
    error,
    refreshContext,
    getAutoFillData,
    validateAccess,
    dashboardStats
  };
};

// Hook for getting materials grouped by subject
export const useFacultyMaterialsBySubject = () => {
  const { user } = useAuth();
  const [materialsBySubject, setMaterialsBySubject] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadMaterialsBySubject = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);
      
      const materials = await FacultyContextService.getFacultyMaterialsBySubject(user.id);
      setMaterialsBySubject(materials);
      
    } catch (err) {
      console.error('Failed to load materials by subject:', err);
      setError(err instanceof Error ? err.message : 'Failed to load materials');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    loadMaterialsBySubject();
  }, [loadMaterialsBySubject]);

  return {
    materialsBySubject,
    loading,
    error,
    refresh: loadMaterialsBySubject
  };
};

// Hook for subject-specific context
export const useSubjectContext = (assignmentId: string | null) => {
  const [subjectData, setSubjectData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!assignmentId) {
      setSubjectData(null);
      return;
    }

    const loadSubjectData = async () => {
      try {
        setLoading(true);
        const data = await FacultyContextService.getSubjectAutoFillData(assignmentId);
        setSubjectData(data);
      } catch (err) {
        console.error('Failed to load subject data:', err);
        setSubjectData(null);
      } finally {
        setLoading(false);
      }
    };

    loadSubjectData();
  }, [assignmentId]);

  return {
    subjectData,
    loading
  };
};
