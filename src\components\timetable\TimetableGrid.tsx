import React, { useEffect, useState, useImperativeHandle } from 'react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TimetableService, TimetableSlot, TimeStructure } from '@/services/TimetableService';
import { TimeSlotValidationService } from '@/services/TimeSlotValidationService';
import { TimetableCard } from './TimetableCard';
import { TimetableCellContent } from './TimetableCellContent';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from '@/components/ui/use-toast';
import { TheorySlotGenerator } from '@/services/TheorySlotGenerator';
import { FacultyAvailabilityService, FacultyAvailabilityRecalculator } from '@/services/FacultyAvailabilityService';
import { ConflictResolutionService } from '@/services/ConflictResolutionService';
import { LabDurationUtils } from '@/services/LabDurationUtils';
import { v4 as uuidv4 } from 'uuid';
import { LabGenerationConfigPopup } from './LabGenerationConfigPopup';
import { ElectiveConfigurationModal, ElectiveConfiguration } from './ElectiveConfigurationModal';
import { ElectivePlacementService } from '@/services/ElectivePlacementService';

import { SemesterConfigurationService, LabGenerationConfig, TheoryGenerationConfig } from '@/services/SemesterConfigurationService';

interface TimetableGridProps {
  timetableGridRef: React.RefObject<any>;
  department: string;
  semester: string;
  section: string;
  academicYear: string;
  view: 'class' | 'faculty';
  selectedFaculty?: string;
  facultyOptions?: Array<{ id: string, name: string }>;
}

export const TimetableGrid: React.FC<TimetableGridProps> = ({
  timetableGridRef,
  department,
  semester,
  section,
  academicYear,
  view,
  selectedFaculty,
  facultyOptions = [] // Default to empty array
}) => {
  const [days, setDays] = useState<string[]>([]);
  const [timeSlots, setTimeSlots] = useState<any[]>([]);
  const [timetableData, setTimetableData] = useState<TimetableSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [timeStructure, setTimeStructure] = useState<TimeStructure | null>(null);
  const [processedTimetable, setProcessedTimetable] = useState<Record<string, Record<string, any>>>({});
  const { toast } = useToast();

  // Configuration popup states
  const [showLabConfigPopup, setShowLabConfigPopup] = useState(false);
  const [showElectiveConfigPopup, setShowElectiveConfigPopup] = useState(false);

  // Loading states
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStatus, setGenerationStatus] = useState('');

  // Skill lab filtering state
  const [showOnlySkillLabs, setShowOnlySkillLabs] = useState(false);

  // Reset skill lab only mode
  const resetSkillLabMode = () => {
    if (showOnlySkillLabs) {
      setShowOnlySkillLabs(false);
      loadTimetableData();
    }
  };

  // Add a debugging function to check secondary faculty information
  const checkSecondaryFacultyInfo = () => {
    console.log("CHECKING SECONDARY FACULTY INFO:");

    // Count slots with secondary faculty
    const slotsWithSecondaryFaculty = timetableData.filter(slot => slot.faculty2_id).length;
    console.log(`Total slots with secondary faculty: ${slotsWithSecondaryFaculty}`);

    // Log some sample slots with secondary faculty
    const samplesWithSecondaryFaculty = timetableData.filter(slot => slot.faculty2_id).slice(0, 3);
    console.log("Sample slots with secondary faculty:", samplesWithSecondaryFaculty);

    if (view === 'faculty' && selectedFaculty) {
      // Check if the selected faculty is a secondary faculty in any slots
      const slotsWhereSecondary = timetableData.filter(slot => slot.faculty2_id === selectedFaculty);
      console.log(`Selected faculty is secondary in ${slotsWhereSecondary.length} slots`);

      // Log these slots
      if (slotsWhereSecondary.length > 0) {
        console.log("Slots where selected faculty is secondary:", slotsWhereSecondary);
      }
    }
  };

  // Call this after timetable data is loaded
  useEffect(() => {
    if (timetableData.length > 0) {
      checkSecondaryFacultyInfo();
    }
  }, [timetableData, selectedFaculty, view]);

  useEffect(() => {
    if (view === 'faculty' && selectedFaculty) {
      console.log("Faculty view active with selected faculty:", selectedFaculty);
      console.log("Timetable data:", timetableData);

      // Check if any slots match the selected faculty
      const matchingSlots = timetableData.filter(slot =>
        slot.faculty_id === selectedFaculty ||
        slot.faculty_1_id === selectedFaculty ||
        slot.faculty2_id === selectedFaculty
      );

      console.log("Matching slots for faculty:", matchingSlots.length);
    }
  }, [view, selectedFaculty, timetableData]);

  useEffect(() => {
    if (view === 'faculty' && selectedFaculty) {
      console.log("FACULTY VIEW DEBUG:");
      console.log("Selected faculty ID:", selectedFaculty);

      // Log all faculty IDs in the timetable data
      const facultyIds = new Set();
      timetableData.forEach(slot => {
        if (slot.faculty_id) facultyIds.add(slot.faculty_id);
        if (slot.faculty2_id) facultyIds.add(slot.faculty2_id);
      });
      console.log("All faculty IDs in timetable:", Array.from(facultyIds));

      // Check each slot
      timetableData.forEach(slot => {
        if (slot.faculty_id === selectedFaculty || slot.faculty2_id === selectedFaculty) {
          console.log("MATCH FOUND:", slot.subject_code, slot.day, slot.time_slot);
        }
      });
    }
  }, [view, selectedFaculty, timetableData]);

  // Add a function to get a consistent color for each lab subject
  const getLabBackgroundColor = (subjectId: string | undefined, subjectCode: string | undefined): string => {
    // Create a hash from the subject ID or code to get a consistent color
    const hash = subjectId || subjectCode || '';
    const hashCode = hash.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    // List of light pastel colors suitable for backgrounds
    const colors = [
      'bg-blue-200',
      'bg-green-200',
      'bg-purple-200',
      'bg-pink-200',
      'bg-yellow-200',
      'bg-indigo-200',
      'bg-red-200',
      'bg-orange-200',
      'bg-teal-200',
      'bg-cyan-200',
    ];

    // Use the hash to select a color
    const colorIndex = Math.abs(hashCode) % colors.length;
    return colors[colorIndex];
  };

  // Add a function to get a matching border color for each lab
  const getLabBorderColor = (subjectId: string | undefined, subjectCode: string | undefined): string => {
    // Create a hash from the subject ID or code to get a consistent color
    const hash = subjectId || subjectCode || '';
    const hashCode = hash.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    // List of border colors that match the backgrounds
    const colors = [
      'border-blue-500',
      'border-green-500',
      'border-purple-500',
      'border-pink-500',
      'border-yellow-500',
      'border-indigo-500',
      'border-red-500',
      'border-orange-500',
      'border-teal-500',
      'border-cyan-500',
    ];

    // Use the hash to select a color
    const colorIndex = Math.abs(hashCode) % colors.length;
    return colors[colorIndex];
  };

  // Add a function to get a matching text color for each lab
  const getLabTextColor = (subjectId: string | undefined, subjectCode: string | undefined): string => {
    // Create a hash from the subject ID or code to get a consistent color
    const hash = subjectId || subjectCode || '';
    const hashCode = hash.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    // List of theme-aware text colors
    const colors = [
      'text-primary',
      'text-accent',
      'text-secondary',
      'text-blue-600 dark:text-blue-400',
      'text-green-600 dark:text-green-400',
      'text-purple-600 dark:text-purple-400',
      'text-pink-600 dark:text-pink-400',
      'text-yellow-600 dark:text-yellow-400',
      'text-indigo-600 dark:text-indigo-400',
      'text-red-600 dark:text-red-400',
    ];

    // Use the hash to select a color
    const colorIndex = Math.abs(hashCode) % colors.length;
    return colors[colorIndex];
  };

  // Helper function to check if a lab spans across a break
  const checkIfLabSpansBreak = (day: string, breakSlot: any): boolean => {
    // Find a lab that starts before this break
    const labStartSlot = timeSlots.find(ts =>
      ts.isPeriod &&
      processedTimetable[day]?.[ts.time]?.isLab &&
      processedTimetable[day]?.[ts.time]?.isLabStart
    );

    if (labStartSlot && processedTimetable[day]?.[labStartSlot.time]) {
      const labContent = processedTimetable[day][labStartSlot.time];
      const labEndPeriod = labStartSlot.periodNumber + (labContent.colSpan || 3) - 1;
      const currentPeriod = breakSlot.periodNumber || 0;

      return labStartSlot.periodNumber < currentPeriod && currentPeriod < labEndPeriod;
    }

    return false;
  };

  // Configuration handlers
  const handleLabConfigConfirm = async (config: LabGenerationConfig) => {
    try {
      await generateLabSlotsWithConfig(config);
    } finally {
      setShowLabConfigPopup(false);
    }
  };

  const handleElectiveConfigConfirm = async (config: ElectiveConfiguration) => {
    try {
      await generateTheorySlotsWithElectiveConfig(config);
    } finally {
      setShowElectiveConfigPopup(false);
    }
  };

  // Expose methods to parent using the ref
  useImperativeHandle(timetableGridRef, () => ({
    updateTimetableData: (data: TimetableSlot[]) => {
      console.log("Updating timetable data with:", data.length, "slots");
      setTimetableData(data);
    },
    clearAndLoadData: async () => {
      await loadTimetableData();
    },
    fetchAndDisplayLabSlots: async () => {
      // Show configuration popup instead of directly generating
      setShowLabConfigPopup(true);
    },
    fetchAndDisplayTheorySlots: async () => {
      // Open elective configuration modal for enhanced theory generation
      setShowElectiveConfigPopup(true);
    }
  }));

  // Helper function to generate time slots based on time structure
  const generateTimeSlots = (structure: TimeStructure) => {
    const slots: any[] = [];
    let periodCounter = 1;

    // Helper function to add minutes to time
    const addMinutesToTime = (time: string, minutes: number): string => {
      const [hours, mins] = time.split(':').map(Number);
      let totalMinutes = hours * 60 + mins + minutes;
      const newHours = Math.floor(totalMinutes / 60);
      const newMinutes = totalMinutes % 60;
      return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
    };

    // Generate first half periods (before tea break)
    let currentTime = structure.first_half_start_time;

    // Period 1
    let endTime = addMinutesToTime(currentTime, structure.theory_class_duration);
    slots.push({
      id: `period-1`,
      label: `1`,
      time: `${currentTime}-${endTime}`,
      displayTime: `${formatTimeToAmPm(currentTime)}-${formatTimeToAmPm(endTime)}`,
      isPeriod: true,
      isBreak: false,
      periodNumber: 1
    });
    currentTime = endTime;

    // Period 2
    endTime = addMinutesToTime(currentTime, structure.theory_class_duration);
    slots.push({
      id: `period-2`,
      label: `2`,
      time: `${currentTime}-${endTime}`,
      displayTime: `${formatTimeToAmPm(currentTime)}-${formatTimeToAmPm(endTime)}`,
      isPeriod: true,
      isBreak: false,
      periodNumber: 2
    });

    // Tea Break (fixed position after period 2)
    slots.push({
      id: `tea-break`,
      label: "TEA BREAK",
      time: `${structure.tea_break_start_time}-${structure.tea_break_end_time}`,
      displayTime: `${formatTimeToAmPm(structure.tea_break_start_time)}-${formatTimeToAmPm(structure.tea_break_end_time)}`,
      isPeriod: false,
      isBreak: true,
      breakType: "tea",
      periodNumber: 2.5
    });

    // Continue with remaining first half periods (after tea break)
    currentTime = structure.tea_break_end_time;

    // Period 3
    endTime = addMinutesToTime(currentTime, structure.theory_class_duration);
    slots.push({
      id: `period-3`,
      label: `3`,
      time: `${currentTime}-${endTime}`,
      displayTime: `${formatTimeToAmPm(currentTime)}-${formatTimeToAmPm(endTime)}`,
      isPeriod: true,
      isBreak: false,
      periodNumber: 3
    });
    currentTime = endTime;

    // Period 4
    endTime = addMinutesToTime(currentTime, structure.theory_class_duration);
    slots.push({
      id: `period-4`,
      label: `4`,
      time: `${currentTime}-${endTime}`,
      displayTime: `${formatTimeToAmPm(currentTime)}-${formatTimeToAmPm(endTime)}`,
      isPeriod: true,
      isBreak: false,
      periodNumber: 4
    });

    // Lunch Break (fixed position after period 4)
    slots.push({
      id: "lunch-break",
      label: "LUNCH BREAK",
      time: `${structure.lunch_break_start_time}-${structure.lunch_break_end_time}`,
      displayTime: `${formatTimeToAmPm(structure.lunch_break_start_time)}-${formatTimeToAmPm(structure.lunch_break_end_time)}`,
      isPeriod: false,
      isBreak: true,
      breakType: "lunch",
      periodNumber: 4.5
    });

    // Generate second half periods (after lunch break)
    currentTime = structure.second_half_start_time;
    periodCounter = 5; // Start from period 5 after lunch

    for (let i = 0; i < structure.periods_in_second_half; i++) {
      endTime = addMinutesToTime(currentTime, structure.theory_class_duration);
      slots.push({
        id: `period-${periodCounter}`,
        label: `${periodCounter}`,
        time: `${currentTime}-${endTime}`,
        displayTime: `${formatTimeToAmPm(currentTime)}-${formatTimeToAmPm(endTime)}`,
        isPeriod: true,
        isBreak: false,
        periodNumber: periodCounter
      });
      currentTime = endTime;
      periodCounter++;
    }

    return slots;
  };

  // Helper function to convert time to minutes for sorting
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // Helper functions

  const formatTimeToAmPm = (time: string): string => {
    const [hours, minutes] = time.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${String(minutes).padStart(2, '0')} ${period}`;
  };

  // Find time slot for a given time of day using semester configuration
  const findTimeSlotForLabSession = async (
    timeOfDay: string,
    timeStructure: TimeStructure | null,
    academicYear?: string,
    department?: string,
    semester?: string
  ): Promise<string> => {
    if (!timeStructure) return '';

    // Try to get semester-specific configuration if available
    if (academicYear && department && semester) {
      try {
        const { SemesterLabConfigurationService } = await import('@/services/SemesterLabConfigurationService');
        const config = await SemesterLabConfigurationService.getLabConfiguration(academicYear, department, semester);

        if (config) {
          const timeSlotMapping = SemesterLabConfigurationService.getTimeSlotMapping(timeOfDay, config);
          if (timeSlotMapping) {
            return timeSlotMapping;
          }
        }
      } catch (error) {
        console.warn('Could not load semester lab configuration, falling back to defaults:', error);
      }
    }

    // Fallback to standard time slots for backward compatibility
    if (timeOfDay.toLowerCase() === 'morning') {
      return `${timeStructure.first_half_start_time}-${timeStructure.lunch_break_start_time}`;
    } else {
      return `${timeStructure.second_half_start_time}-${timeStructure.second_half_end_time}`;
    }
  };

  // Generate lab slots with configuration
  const generateLabSlotsWithConfig = async (config: LabGenerationConfig) => {
    if (department && semester && section && academicYear) {
      setIsLoading(true);
      resetSkillLabMode(); // Reset skill lab only mode when generating new labs
      try {
        if (!timeStructure) {
          toast({
            title: "Missing Time Structure",
            description: "Time structure not defined. Please define in the Time Structure tab.",
            variant: "destructive"
          });
          setIsLoading(false);
          return;
        }

        // STEP 1: Always generate regular lab slots first
        console.log('🔧 STEP 1: Generating regular lab sessions...');
        await fetchLabSlots();

        // STEP 2: Then add skill lab slots if configured (after regular labs are in place)
        if (config.hasSkillLab) {
          console.log('🔧 STEP 2: Adding skill lab sessions to existing lab schedule...');
          await generateSkillLabSlots(config);
        }

        // STEP 3: Generate tutorial slots if configured (after labs are in place)
        if (config.hasTutorialHours) {
          console.log('🔧 STEP 3: Adding tutorial hours to existing schedule...');
          await generateTutorialSlots({
            hasTutorialHours: config.hasTutorialHours,
            tutorialPeriodsPerWeek: config.tutorialPeriodsPerWeek
          });
        }

        setGenerationStatus('Completing generation...');
        setGenerationProgress(100);

        // Small delay to show completion
        await new Promise(resolve => setTimeout(resolve, 500));

        let description = "Generated all regular lab sessions successfully.";
        if (config.hasSkillLab && config.hasTutorialHours) {
          description = `Generated all regular lab sessions, ${config.skillLabPeriodsPerWeek} skill lab session(s) using ${config.skillLabPlacement.replace('_', ' ')} placement, and ${config.tutorialPeriodsPerWeek} tutorial hour(s).`;
        } else if (config.hasSkillLab) {
          description = `Generated all regular lab sessions and ${config.skillLabPeriodsPerWeek} skill lab session(s) using ${config.skillLabPlacement.replace('_', ' ')} placement.`;
        } else if (config.hasTutorialHours) {
          description = `Generated all regular lab sessions and ${config.tutorialPeriodsPerWeek} tutorial hour(s).`;
        }

        toast({
          title: "Lab Slots Generated Successfully",
          description: description,
          variant: "default"
        });
      } catch (error) {
        console.error('Error generating lab slots:', error);
        toast({
          title: "Generation Failed",
          description: "Failed to generate lab slots. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
        setGenerationProgress(0);
        setGenerationStatus('');
      }
    }
  };

  // Create or find skill lab mapping
  const createSkillLabMapping = async (): Promise<string> => {
    try {
      setGenerationStatus('Checking existing skill lab mapping...');
      setGenerationProgress(10);

      // Check if skill lab mapping already exists - try with better error handling
      let existingMapping = null;
      let checkError = null;

      try {
        const result = await supabase
          .from('simplified_subject_faculty_mappings')
          .select('id')
          .eq('academic_year', academicYear)
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section)
          .eq('subject_code', 'SKILL LAB')
          .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no record exists

        existingMapping = result.data;
        checkError = result.error;
      } catch (error) {
        console.log('Error checking existing mapping (will create new):', error);
        checkError = error;
      }

      if (existingMapping && !checkError) {
        setGenerationProgress(20);
        return existingMapping.id;
      }

      // Use the unassigned faculty ID for skill labs
      const UNASSIGNED_FACULTY_ID = '00000000-0000-0000-0000-000000000000';
      const SKILL_LAB_SUBJECT_ID = '11111111-1111-1111-1111-111111111111';

      setGenerationStatus('Creating skill lab mapping...');
      setGenerationProgress(30);

      // Create new skill lab mapping
      const { data: newMapping, error: insertError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .insert({
          academic_year: academicYear,
          department: department,
          semester: semester,
          section: section,
          subject_id: SKILL_LAB_SUBJECT_ID, // Use the predefined skill lab subject ID
          subject_code: 'SKILL LAB',
          subject_name: 'Skill Lab',
          subject_type: 'skill_lab',
          faculty_1_id: UNASSIGNED_FACULTY_ID, // Use unassigned faculty instead of null
          faculty_2_id: null, // This can be null
          hours_per_week: 3,
          classroom: 'Lab',
          slots_per_week: 1
        })
        .select('id')
        .single();

      if (insertError) throw insertError;
      return newMapping.id;
    } catch (error) {
      console.error('Error creating skill lab mapping:', error);
      throw error;
    }
  };

  // Generate skill lab slots based on configuration
  const generateSkillLabSlots = async (config: LabGenerationConfig) => {
    if (!timeStructure) return;

    try {
      setGenerationStatus('Clearing existing skill lab slots...');
      setGenerationProgress(5);

      // Remove ONLY existing skill lab slots, preserve all regular lab sessions
      console.log('🧹 Removing only existing skill lab slots (preserving regular labs)...');
      const { error: deleteError } = await supabase
        .from('timetable_slots')
        .delete()
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .or('subject_code.eq.SKILL LAB,subject_name.eq.Skill Development Lab,subject_name.ilike.%skill%lab%');

      if (deleteError) {
        console.error('❌ Error deleting existing skill lab slots:', deleteError);
        // Continue anyway - don't fail the generation
      } else {
        console.log('✅ Cleared ONLY skill lab slots (regular labs preserved)');
      }

      setGenerationStatus('Planning skill lab placement...');
      setGenerationProgress(15);

      // Get existing lab days to determine placement
      const existingLabDays = await getExistingLabDays();

      // Get skill lab placement days with proper conflict checking
      const allWorkingDays = timeStructure.working_days;
      console.log('All working days:', allWorkingDays);
      console.log('Days with existing labs:', existingLabDays);

      let skillLabDays: string[] = [];

      if (config.skillLabPlacement === 'vacant_days') {
        // STRICT vacant days only - days with NO lab sessions at all
        const vacantDays = allWorkingDays.filter(day => !existingLabDays.includes(day));
        console.log('🎯 STRICT vacant days analysis:');
        console.log(`   Working days: [${allWorkingDays.join(', ')}]`);
        console.log(`   Days with labs: [${existingLabDays.join(', ')}]`);
        console.log(`   Completely vacant days: [${vacantDays.join(', ')}]`);

        if (vacantDays.length === 0) {
          const errorMsg = `❌ VACANT DAYS ONLY: No completely vacant days available for skill lab placement. All working days [${allWorkingDays.join(', ')}] already have lab sessions [${existingLabDays.join(', ')}].`;
          console.error(errorMsg);
          toast({
            title: "Skill Lab Placement Failed - No Vacant Days",
            description: "All working days already have lab sessions. Please choose 'Least labs day' or 'Specific day' placement instead.",
            variant: "destructive"
          });
          throw new Error(errorMsg);
        }

        if (vacantDays.length < config.skillLabPeriodsPerWeek) {
          const errorMsg = `❌ VACANT DAYS ONLY: Not enough completely vacant days available. Need ${config.skillLabPeriodsPerWeek} days, but only ${vacantDays.length} vacant days found: [${vacantDays.join(', ')}]`;
          console.error(errorMsg);
          toast({
            title: "Skill Lab Placement Failed - Insufficient Vacant Days",
            description: `Need ${config.skillLabPeriodsPerWeek} vacant days, but only ${vacantDays.length} available: ${vacantDays.join(', ')}. Please reduce skill lab sessions or choose different placement.`,
            variant: "destructive"
          });
          throw new Error(errorMsg);
        }

        skillLabDays = vacantDays.slice(0, config.skillLabPeriodsPerWeek);
        console.log('✅ Selected STRICT vacant days for skill labs:', skillLabDays);
      } else if (config.skillLabPlacement === 'least_labs_day') {
        // LEAST LABS DAY: Find the day with the minimum number of existing lab sessions
        // AND ensure no afternoon conflicts (periods 5-7: 13:00-15:30)
        console.log('🎯 LEAST LABS DAY analysis with afternoon conflict detection:');

        // Count lab sessions per day and check for afternoon conflicts
        const labCountByDay: Record<string, number> = {};
        const afternoonConflictsByDay: Record<string, boolean> = {};
        allWorkingDays.forEach(day => {
          labCountByDay[day] = 0;
          afternoonConflictsByDay[day] = false;
        });

        // Get detailed lab count from database with time slot information
        const { data: allLabSessions, error: labError } = await supabase
          .from('timetable_slots')
          .select('day, subject_code, subject_name, time_slot')
          .eq('academic_year', academicYear)
          .eq('department', department)
          .eq('semester', semester)
          .eq('section', section)
          .neq('subject_code', 'SKILL LAB')
          .in('subject_type', ['lab', 'laboratory']);

        if (!labError && allLabSessions) {
          allLabSessions.forEach(session => {
            if (labCountByDay[session.day] !== undefined) {
              labCountByDay[session.day]++;

              // Check if this lab conflicts with afternoon periods (13:00-15:30)
              // This includes any lab that overlaps with periods 5-7
              if (session.time_slot) {
                const [startTime] = session.time_slot.split('-');
                const startHour = parseInt(startTime.split(':')[0]);
                const startMinute = parseInt(startTime.split(':')[1]);
                const startTimeInMinutes = startHour * 60 + startMinute;

                // Afternoon periods start at 13:00 (780 minutes) and end at 15:30 (930 minutes)
                const afternoonStart = 13 * 60; // 13:00 = 780 minutes
                const afternoonEnd = 15 * 60 + 30; // 15:30 = 930 minutes

                // Check if lab starts during or before afternoon and could overlap
                if (startTimeInMinutes <= afternoonEnd) {
                  // Parse end time to check for overlap
                  const [, endTime] = session.time_slot.split('-');
                  const endHour = parseInt(endTime.split(':')[0]);
                  const endMinute = parseInt(endTime.split(':')[1]);
                  const endTimeInMinutes = endHour * 60 + endMinute;

                  // If lab ends after afternoon starts, there's a conflict
                  if (endTimeInMinutes > afternoonStart) {
                    afternoonConflictsByDay[session.day] = true;
                    console.log(`   Afternoon conflict detected on ${session.day}: ${session.subject_code} (${session.time_slot})`);
                  }
                }
              }
            }
          });
        }

        console.log('📊 Lab count by day:', labCountByDay);
        console.log('🚫 Afternoon conflicts by day:', afternoonConflictsByDay);

        // Filter days that have no afternoon conflicts
        const daysWithoutAfternoonConflicts = allWorkingDays.filter(day => !afternoonConflictsByDay[day]);
        console.log(`   Days without afternoon conflicts: [${daysWithoutAfternoonConflicts.join(', ')}]`);

        if (daysWithoutAfternoonConflicts.length === 0) {
          throw new Error('No days available for skill lab placement - all days have afternoon lab conflicts');
        }

        // Among days without afternoon conflicts, find those with minimum lab count
        const labCountsForValidDays = daysWithoutAfternoonConflicts.map(day => labCountByDay[day]);
        const minLabCount = Math.min(...labCountsForValidDays);
        const daysWithMinLabsAndNoConflicts = daysWithoutAfternoonConflicts.filter(day => labCountByDay[day] === minLabCount);

        console.log(`   Minimum lab count among valid days: ${minLabCount}`);
        console.log(`   Days with minimum labs AND no afternoon conflicts: [${daysWithMinLabsAndNoConflicts.join(', ')}]`);

        skillLabDays = daysWithMinLabsAndNoConflicts.slice(0, config.skillLabPeriodsPerWeek);
        console.log('✅ Selected least labs days for skill labs (with conflict avoidance):', skillLabDays);

      } else if (config.skillLabPlacement === 'specific_day') {
        // SPECIFIC DAY: Use the user-specified day
        if (config.skillLabPreferredDay) {
          skillLabDays = [config.skillLabPreferredDay];
          console.log('✅ Selected specific day for skill labs:', skillLabDays);
        } else {
          throw new Error('Specific day placement selected but no preferred day specified');
        }
      } else {
        // Fallback to original logic
        skillLabDays = SemesterConfigurationService.getSkillLabPlacementDays(
          config,
          existingLabDays
        );
      }

      // Create skill lab slots for afternoon periods (13:15-16:00)
      const skillLabSlots: any[] = [];

      for (let i = 0; i < config.skillLabPeriodsPerWeek; i++) {
        const day = skillLabDays[i % skillLabDays.length];

        // Create a valid subject_id by finding or creating a skill lab mapping
        const skillLabSubjectId = await createSkillLabMapping();
        const UNASSIGNED_FACULTY_ID = '00000000-0000-0000-0000-000000000000';

        skillLabSlots.push({
          academic_year: academicYear,
          department: department,
          semester: semester,
          section: section,
          day: day,
          time_slot: '13:15-16:00', // Afternoon periods 5-7
          subject_id: skillLabSubjectId,
          subject_code: 'SKILL LAB', // This creates the PURPLE skill lab sessions
          subject_name: 'Skill Lab', // NOT "Skill Development Lab" - that's the old system
          subject_type: 'skill_lab', // Specific type for skill labs
          faculty_id: UNASSIGNED_FACULTY_ID, // Use unassigned faculty ID
          faculty_name: 'Unassigned',
          room_number: 'Lab',
          subject_short_id: 'SKILL',
          batch_name: null,
          is_lab_start: true,
          is_processed: false,
          col_span: 3, // Spans 3 periods (5, 6, 7)
          faculty2_id: null,
          faculty2_name: null,
          is_hidden: false,
          is_full_session_lab: true,
          period_index: 5 // Starting from period 5
        });
      }

      // Insert skill lab slots into database
      if (skillLabSlots.length > 0) {
        setGenerationStatus('Inserting skill lab slots...');
        setGenerationProgress(60);
        console.log('Inserting skill lab slots:', skillLabSlots);

        for (let i = 0; i < skillLabSlots.length; i++) {
          const slot = skillLabSlots[i];
          try {
            setGenerationStatus(`Inserting skill lab slot ${i + 1} of ${skillLabSlots.length}...`);
            setGenerationProgress(60 + (i / skillLabSlots.length) * 30);

            const { error } = await supabase
              .from('timetable_slots')
              .insert(slot);

            if (error) {
              console.error('Error inserting skill lab slot:', error);
              throw error;
            }
          } catch (slotError) {
            console.error('Failed to insert skill lab slot:', slotError);
            throw slotError;
          }
        }

        setGenerationProgress(90);
        console.log(`Generated ${skillLabSlots.length} skill lab slots`);
      }

      // Refresh the timetable data to show both regular labs AND skill labs together
      setGenerationStatus('Refreshing timetable...');
      setGenerationProgress(95);
      setShowOnlySkillLabs(false); // Show ALL sessions (regular labs + skill labs)
      await loadTimetableData();

      console.log('✅ Skill lab generation completed - timetable now shows both regular labs and skill labs');

    } catch (error) {
      console.error('Error generating skill lab slots:', error);
      throw error;
    }
  };

  // Get existing lab days for skill lab placement logic with detailed analysis
  const getExistingLabDays = async (): Promise<string[]> => {
    try {
      console.log('🔍 Analyzing existing lab sessions for skill lab placement...');

      // Get all lab sessions (excluding skill labs) for this class
      const { data, error } = await supabase
        .from('timetable_slots')
        .select('day, subject_code, subject_type, time_slot, subject_name')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .neq('subject_code', 'SKILL LAB'); // Exclude existing skill labs

      if (error) throw error;

      // Log all existing sessions for debugging
      console.log('📊 All existing timetable sessions:', data?.length || 0);

      // Filter for lab sessions - check multiple criteria
      const labSessions = data?.filter(slot =>
        slot.subject_type === 'lab' ||
        slot.subject_type === 'laboratory' ||
        slot.subject_code?.toLowerCase().includes('lab') ||
        slot.subject_code?.includes('LAB')
      ) || [];

      console.log('🧪 Filtered lab sessions:', labSessions.length);

      // Group lab sessions by day for detailed analysis
      const labsByDay: Record<string, any[]> = {};
      labSessions.forEach(session => {
        if (!labsByDay[session.day]) {
          labsByDay[session.day] = [];
        }
        labsByDay[session.day].push(session);
      });

      // Log detailed analysis
      console.log('📅 Lab sessions by day:');
      Object.entries(labsByDay).forEach(([day, sessions]) => {
        console.log(`  ${day}: ${sessions.length} lab(s) - ${sessions.map(s => s.subject_code).join(', ')}`);
      });

      // Get unique days that have lab sessions
      const labDays = [...new Set(labSessions.map(slot => slot.day))];
      console.log('🎯 Days with existing lab sessions:', labDays);

      return labDays;
    } catch (error) {
      console.error('❌ Error fetching existing lab days:', error);
      return [];
    }
  };

  // Fetch lab slots from the subject_faculty_mappings and lab_time_slots tables
  const fetchLabSlots = async () => {
    if (department && semester && section && academicYear) {
      setIsLoading(true);
      try {
        if (!timeStructure) {
          toast({
            title: "Missing Time Structure",
            description: "Time structure not defined. Please define in the Time Structure tab.",
            variant: "destructive"
          });
          setIsLoading(false);
          return;
        }

        // Get lab mappings and slots
        console.log("Fetching lab slot mappings for:", { academicYear, department, semester, section });
        const { mappings, labSlots, facultyMap, subjectMap } = await TimetableService.fetchLabSlotMappings({
          academicYear,
          department,
          semester,
          section
        });

        console.log("Found lab mappings:", mappings.length, mappings.map(m => `${m.subject_code} - ${m.subject_name}`));
        console.log("Found lab slots:", labSlots.length, labSlots);

        if (mappings.length === 0) {
          toast({
            title: "No Lab Mappings",
            description: "No laboratory subject mappings found for the selected filters.",
            variant: "destructive"
          });
          setIsLoading(false);
          return;
        }

        // If we have mappings but no lab slots, create default ones
        if (labSlots.length === 0 || labSlots.length < mappings.length * 2) {
          console.log("Not enough lab slots found. Creating default slots.");
          await TimetableService.createDefaultLabSlots({
            academicYear,
            department,
            semester,
            section
          });

          // Fetch the lab slots again after creating defaults
          const result = await TimetableService.fetchLabSlotMappings({
            academicYear,
            department,
            semester,
            section
          });

          // Update our local variables
          Object.assign({ mappings, labSlots, facultyMap, subjectMap }, result);

          console.log("After creating defaults - Lab slots:", labSlots.length);
        }


        const timeSlotMap: Record<string, Array<TimetableSlot>> = {};

        // Clear existing lab slots before adding new ones and get affected faculty IDs
        const affectedFacultyIds = await TimetableService.clearTimetable({
          academicYear,
          department,
          semester,
          section,
          subject_type: 'lab'
        });

        // Recalculate faculty availability for affected faculty members after clearing
        if (affectedFacultyIds.length > 0 && timeStructure) {
          try {
            await FacultyAvailabilityRecalculator.recalculateForFaculty(
              affectedFacultyIds,
              timeStructure,
              academicYear
            );
            console.log(`✅ Recalculated availability for ${affectedFacultyIds.length} faculty members after clearing lab slots`);
          } catch (error) {
            console.error("❌ Error recalculating faculty availability after clearing lab slots:", error);
          }
        }

        // Ensure we're creating slots for all lab mappings
        for (const mapping of mappings) {
          const mappingLabSlots = labSlots.filter(ls => ls.mapping_id === mapping.id);
          console.log(`Processing ${mappingLabSlots.length} slots for mapping ${mapping.subject_code}`);

          // If no slots defined for this mapping, create default ones on the fly
          if (mappingLabSlots.length === 0) {
            console.warn(`No lab slots defined for ${mapping.subject_code}, skipping`);
            continue;
          }

          for (const labSlot of mappingLabSlots) {
            // Get subject short ID
            const shortId = subjectMap[mapping.subject_id] || mapping.subject_code?.substring(0, 3).toUpperCase();

            // Parse time slot information using TimeSlotValidationService
            const timeSlotInfo = TimeSlotValidationService.parseTimeSlotInfo(labSlot.time_of_day, timeStructure);
            let timeSlot = '';
            let actualColspan = 3; // Default fallback

            if (timeSlotInfo) {
              // Use the parsed time range for flexible format or predefined mapping
              timeSlot = timeSlotInfo.isFlexibleFormat
                ? `${timeSlotInfo.startTime}-${timeSlotInfo.endTime}`
                : await findTimeSlotForLabSession(labSlot.time_of_day, timeStructure, academicYear, department, semester);
              actualColspan = timeSlotInfo.duration;

              console.log(`Lab ${mapping.subject_code}: ${timeSlotInfo.isFlexibleFormat ? 'Flexible' : 'Predefined'} format`);
              console.log(`  Time: ${timeSlotInfo.startTime}-${timeSlotInfo.endTime}`);
              console.log(`  Duration: ${actualColspan} periods`);
              console.log(`  Periods: ${timeSlotInfo.periods.join(', ')}`);
            } else {
              // Fallback to legacy method
              timeSlot = await findTimeSlotForLabSession(
                labSlot.time_of_day,
                timeStructure,
                academicYear,
                department,
                semester
              );
              console.log(`Using legacy time slot calculation for ${labSlot.time_of_day}: ${timeSlot}`);
            }

            if (!timeSlot) {
              console.warn(`Could not find time slot for ${labSlot.time_of_day} session`);
              continue;
            }

            // Create a unique key for this day and time slot
            const slotKey = `${labSlot.day}-${timeSlot}`;

            // Initialize array for this slot if it doesn't exist
            if (!timeSlotMap[slotKey]) {
              timeSlotMap[slotKey] = [];
            }

            const uiId = `lab-${crypto.randomUUID()}`;

            const slot: TimetableSlot = {
              id: uiId, // Temporary UI ID
              academic_year: academicYear,
              department: department,
              semester: semester,
              section: section,
              day: labSlot.day,
              time_slot: timeSlot,
              subject_id: mapping.subject_id, // This will be replaced with mapping.id in saveTimetableSlot
              subject_code: mapping.subject_code,
              subject_name: mapping.subject_name,
              subject_type: 'lab',
              faculty_id: mapping.faculty_1_id,
              faculty_name: facultyMap[mapping.faculty_1_id] || 'Unknown',
              faculty2_id: mapping.faculty_2_id,
              faculty2_name: mapping.faculty_2_id ? facultyMap[mapping.faculty_2_id] || '' : '',
              room_number: mapping.classroom,
              batch_name: labSlot.batch_name || 'Batch A',
              subject_short_id: shortId,
              is_lab_start: true,
              col_span: actualColspan, // Use calculated colspan based on actual duration
              is_processed: false,
              is_hidden: false,
              is_full_session_lab: true
            };

            console.log(`Creating slot with subject_id: ${mapping.subject_id} for ${labSlot.batch_name || 'Batch A'} from mapping:`, mapping);

            timeSlotMap[slotKey].push(slot);
          }
        }

        // Log summary with more details
        console.log(`Generated ${Object.values(timeSlotMap).flat().length} lab slots from ${mappings.length} mappings:`,
          Object.values(timeSlotMap).flat().map(s => `${s.subject_code} - ${s.day} - ${s.time_slot} - ${s.batch_name}`));

        // Save all slots to the database, check for conflicts, and update faculty availability
        for (const slot of Object.values(timeSlotMap).flat()) {
          // First, check if this lab slot conflicts with any existing theory slots
          if (timeStructure) {
            try {
              console.log(`Checking for conflicts before saving lab slot: ${slot.subject_code} on ${slot.day} at ${slot.time_slot}`);

              // Check for conflicts with theory slots
              const conflictResult = await ConflictResolutionService.checkAndResolveLabTheoryConflicts(
                slot,
                timeStructure
              );

              if (conflictResult.resolvedConflicts > 0) {
                toast({
                  title: "Conflicts Resolved",
                  description: `Automatically rescheduled ${conflictResult.resolvedConflicts} theory slots to accommodate lab slot.`,
                  variant: "default"
                });
              }

              if (conflictResult.unresolvedConflicts.length > 0) {
                // There are unresolved conflicts that need manual intervention
                toast({
                  title: "Conflicts Detected",
                  description: `${conflictResult.unresolvedConflicts.length} theory slots could not be automatically rescheduled. Manual resolution required.`,
                  variant: "warning"
                });

                // Log the details of unresolved conflicts
                console.warn("Unresolved conflicts:", conflictResult.unresolvedConflicts);

                // Display more detailed information about each conflict
                conflictResult.unresolvedConflicts.forEach(conflict => {
                  toast({
                    title: `Conflict: ${conflict.theorySlot.subject_code}`,
                    description: `${conflict.reason} - Faculty: ${conflict.theorySlot.faculty_name}`,
                    variant: "destructive"
                  });
                });
              }
            } catch (error) {
              console.error("Error checking for conflicts:", error);
            }
          }

          // Save the lab slot to the database
          const savedSlot = await TimetableService.saveTimetableSlot(slot);

          // Update faculty availability for both primary and secondary faculty
          const facultyIds = [];
          if (savedSlot.faculty_id) facultyIds.push(savedSlot.faculty_id);
          if (savedSlot.faculty2_id) facultyIds.push(savedSlot.faculty2_id);

          if (facultyIds.length > 0 && timeStructure) {
            try {
              await FacultyAvailabilityRecalculator.recalculateForFaculty(
                facultyIds,
                timeStructure,
                academicYear
              );
              console.log(`✅ Updated availability for faculty members: ${facultyIds.join(', ')}`);
            } catch (error) {
              console.error("❌ Error updating faculty availability:", error);
            }
          }
        }

        // Reload all timetable data
        await loadTimetableData();

        // Skill labs are now generated in the main generateSkillLabSlots function
        // No need for additional logic here

        toast({
          title: "Lab Slots Generated",
          description: `Generated and saved ${Object.values(timeSlotMap).flat().length} lab slots.`
        });
      } catch (error) {
        console.error("Error fetching lab slots:", error);
        toast({
          title: "Error",
          description: "Failed to fetch and process lab slots.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Enhanced theory generation with elective configuration
  const generateTheorySlotsWithElectiveConfig = async (config: ElectiveConfiguration) => {
    if (!(department && semester && section && academicYear)) return;
    setIsLoading(true);

    try {
      console.log('🎯 Starting enhanced theory generation with elective configuration:', config);

      // Phase 1: Handle Electives if present
      if (config.hasElectives) {
        console.log('📚 Processing electives...');

        // Get elective subjects
        const electives = await ElectivePlacementService.getElectiveSubjects({
          academicYear,
          department,
          semester,
          section,
          groupingType: config.groupingType,
          synchronizationMode: config.synchronizationMode
        });

        console.log(`Found ${electives.length} elective subjects:`, electives);

        if (electives.length > 0) {
          // Find optimal time slots for electives
          const electiveSlots = await ElectivePlacementService.findSynchronizedElectiveSlots(
            electives,
            {
              academicYear,
              department,
              semester,
              section,
              groupingType: config.groupingType,
              synchronizationMode: config.synchronizationMode
            },
            config.groupingType
          );

          console.log('🎯 Selected elective time slots:', electiveSlots);

          // Create elective timetable slots
          await ElectivePlacementService.createElectiveSlots(
            electives,
            electiveSlots,
            {
              academicYear,
              department,
              semester,
              section,
              groupingType: config.groupingType,
              synchronizationMode: config.synchronizationMode
            },
            config.groupingType
          );

          // Update faculty availability after elective placement
          await ElectivePlacementService.updateFacultyAvailability(electives, electiveSlots);

          console.log('✅ Electives placed successfully');
        }
      }

      // Phase 2: Generate remaining theory slots
      console.log('📖 Generating remaining theory slots...');
      await generateStandardTheorySlots();

      console.log('🎉 Enhanced theory generation completed successfully');

      toast({
        title: "Theory Slots Generated",
        description: `Successfully generated theory slots ${config.hasElectives ? 'with elective configuration' : ''}`,
      });

    } catch (error) {
      console.error('❌ Error in enhanced theory generation:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate theory slots",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      await loadTimetableData();
    }
  };

  // Standard theory slot generation (for non-elective subjects)
  const generateStandardTheorySlots = async () => {

  try {
    if (!timeStructure) {
      toast({
        title: "Missing Time Structure",
        description: "Time structure not defined. Please define in the Time Structure tab.",
        variant: "destructive"
      });
      setIsLoading(false);
      return;
    }

    // Generate theory slots (tutorials should already be placed during lab generation)
    await fetchAndDisplayTheorySlots();

    toast({
      title: "Theory Slots Generated",
      description: "Theory slots generated successfully.",
      variant: "default"
    });
  } catch (error) {
    console.error('Error generating theory slots:', error);
    toast({
      title: "Generation Failed",
      description: "Failed to generate theory slots. Please try again.",
      variant: "destructive"
    });
  } finally {
    setIsLoading(false);
  }
};

// Create or find tutorial mapping
const createTutorialMapping = async (): Promise<string> => {
  try {
    // Check if tutorial mapping already exists
    const { data: existingMapping, error: checkError } = await supabase
      .from('simplified_subject_faculty_mappings')
      .select('id')
      .eq('academic_year', academicYear)
      .eq('department', department)
      .eq('semester', semester)
      .eq('section', section)
      .eq('subject_code', 'TUTORIAL')
      .single();

    if (existingMapping && !checkError) {
      return existingMapping.id;
    }

    // Create new tutorial mapping
    const { data: newMapping, error: insertError } = await supabase
      .from('simplified_subject_faculty_mappings')
      .insert({
        academic_year: academicYear,
        department: department,
        semester: semester,
        section: section,
        subject_id: '22222222-2222-2222-2222-222222222222', // Use the predefined tutorial subject ID
        subject_code: 'TUTORIAL',
        subject_name: 'Tutorial',
        subject_type: 'tutorial',
        faculty_1_id: '00000000-0000-0000-0000-000000000000', // Unknown Faculty ID
        faculty_2_id: null,
        hours_per_week: 1,
        classroom: 'Classroom',
        slots_per_week: 1
      })
      .select('id')
      .single();

    if (insertError) throw insertError;
    return newMapping.id;
  } catch (error) {
    console.error('Error creating tutorial mapping:', error);
    throw error;
  }
};

// Generate tutorial slots based on configuration
const generateTutorialSlots = async (config: { hasTutorialHours: boolean; tutorialPeriodsPerWeek: number }) => {
  if (!timeStructure) return;

  try {
    console.log('🎯 Starting intelligent tutorial slot generation...');

    // Clear existing tutorial slots for this class
    const { error: deleteError } = await supabase
      .from('timetable_slots')
      .delete()
      .eq('academic_year', academicYear)
      .eq('department', department)
      .eq('semester', semester)
      .eq('section', section)
      .eq('subject_type', 'tutorial');

    if (deleteError) {
      console.error('Error clearing existing tutorial slots:', deleteError);
      throw deleteError;
    }

    // Get intelligent tutorial placement days that avoid skill lab conflicts
    const tutorialDays = await SemesterConfigurationService.getTutorialPlacementDays(
      config.tutorialPeriodsPerWeek,
      academicYear,
      department,
      semester,
      section
    );

    console.log(`📅 Selected tutorial days: ${tutorialDays.join(', ')}`);

    if (tutorialDays.length === 0) {
      console.warn('⚠️ No available days found for tutorial placement');
      return;
    }

    // Create tutorial slots for Period 7 (15:05-16:00)
    const tutorialSlots: any[] = [];

    for (let i = 0; i < Math.min(config.tutorialPeriodsPerWeek, tutorialDays.length); i++) {
      const day = tutorialDays[i];

      // Create a valid subject_id by finding or creating a tutorial mapping
      const tutorialSubjectId = await createTutorialMapping();

      // Get Period 7 time slot from timeSlots array
      let period7TimeSlot = '15:05-16:00'; // Default fallback

      // Find Period 7 in the timeSlots array
      const period7Slot = timeSlots.find(slot =>
        slot.isPeriod && (slot.periodNumber === 7 || slot.label === '7')
      );

      if (period7Slot) {
        period7TimeSlot = period7Slot.time;
        console.log(`📅 Using Period 7 time slot from timeSlots: ${period7TimeSlot}`);
      } else {
        console.log(`⚠️ Period 7 not found in timeSlots, using default: ${period7TimeSlot}`);
        console.log('Available time slots:', timeSlots.filter(slot => slot.isPeriod).map(slot => ({
          label: slot.label,
          time: slot.time,
          periodNumber: slot.periodNumber
        })));
      }

      tutorialSlots.push({
        academic_year: academicYear,
        department: department,
        semester: semester,
        section: section,
        day: day,
        time_slot: period7TimeSlot,
        subject_id: tutorialSubjectId,
        subject_code: 'TUTORIAL',
        subject_name: 'Tutorial',
        subject_type: 'tutorial',
        faculty_id: '00000000-0000-0000-0000-000000000000', // Unknown Faculty ID
        faculty_name: 'Unknown Faculty',
        room_number: 'Classroom',
        subject_short_id: 'TUT',
        batch_name: null,
        is_lab_start: false,
        is_processed: false,
        col_span: 1, // Single period
        faculty2_id: null,
        faculty2_name: null,
        is_hidden: false,
        is_full_session_lab: false,
        period_index: 7 // Period 7
      });

      console.log(`✅ Created tutorial slot for ${day} at ${period7TimeSlot}`);
    }

    // Insert tutorial slots into database
    if (tutorialSlots.length > 0) {
      console.log('📝 Inserting tutorial slots into database:', tutorialSlots);

      const { data, error } = await supabase
        .from('timetable_slots')
        .insert(tutorialSlots)
        .select();

      if (error) {
        console.error('❌ Error inserting tutorial slots:', error);
        throw error;
      }

      console.log(`🎉 Generated ${tutorialSlots.length} tutorial slots successfully`);
      console.log('✅ Inserted tutorial slots data:', data);

      // Reload timetable data to show the new tutorial slots
      await loadTimetableData();
    } else {
      console.warn('⚠️ No tutorial slots to insert');
    }
  } catch (error) {
    console.error('Error generating tutorial slots:', error);
    throw error;
  }
};



// inside TimetableGrid.tsx …

const fetchAndDisplayTheorySlots = async () => {
  if (!(department && semester && section && academicYear)) return;
  setIsLoading(true);

  try {
    // First, clear any existing theory slots and get affected faculty IDs
    const affectedFacultyIds = await TimetableService.clearTimetable({
      academicYear,
      department,
      semester,
      section,
      subject_type: 'theory'
    });

    // Recalculate faculty availability for affected faculty members after clearing
    if (affectedFacultyIds.length > 0 && timeStructure) {
      try {
        await FacultyAvailabilityRecalculator.recalculateForFaculty(
          affectedFacultyIds,
          timeStructure,
          academicYear
        );
        console.log(`✅ Recalculated availability for ${affectedFacultyIds.length} faculty members after clearing theory slots`);
      } catch (error) {
        console.error("❌ Error recalculating faculty availability after clearing theory slots:", error);
      }
    }

    // 1) grab existing lab slots so we don't stomp on them
    const { data: existingLabSlots } = await supabase
      .from("timetable_slots")
      .select("*")
      .eq("academic_year", academicYear)
      .eq("department", department)
      .eq("semester", semester)
      .eq("section", section)
      .eq("subject_type", "lab");

    // 2) Fetch theory subject mappings with subject short IDs using raw SQL
    console.log("Fetching theory subject mappings...");
    const { data: theoryMappingsData, error: mappingsError } = await supabase
      .from('simplified_subject_faculty_mappings')
      .select(`
        id,
        subject_code,
        subject_name,
        faculty_1_id,
        hours_per_week,
        subject_id
      `)
      .eq('academic_year', academicYear)
      .eq('department', department)
      .eq('semester', semester)
      .eq('section', section)
      .eq('subject_type', 'theory')
      .not('subject_code', 'like', 'DELETED_%');

    if (mappingsError) {
      console.error('Error fetching theory mappings:', mappingsError);
      throw mappingsError;
    }

    if (!theoryMappingsData || theoryMappingsData.length === 0) {
      toast({
        title: "No Theory Subjects Found",
        description: "No theory subjects found for this class. Please add theory subjects first.",
        variant: "destructive"
      });
      setIsLoading(false);
      return;
    }

    // 2.5) Fetch subject short IDs for the mappings
    const subjectIds = [...new Set(theoryMappingsData.map(m => m.subject_id))];
    const { data: subjectsData, error: subjectsError } = await supabase
      .from('subjects')
      .select('id, subject_short_id')
      .in('id', subjectIds);

    if (subjectsError) {
      console.error('Error fetching subjects data:', subjectsError);
      throw subjectsError;
    }

    // Create a map of subject_id to subject_short_id
    const subjectShortIdMap = new Map();
    subjectsData?.forEach(subject => {
      subjectShortIdMap.set(subject.id, subject.subject_short_id);
    });

    // 3) Fetch faculty names
    const facultyIds = [...new Set(theoryMappingsData.map(m => m.faculty_1_id))];
    const { data: facultyData, error: facultyError } = await supabase
      .from('employee_details')
      .select('id, full_name')
      .in('id', facultyIds);

    if (facultyError) {
      console.error('Error fetching faculty data:', facultyError);
      throw facultyError;
    }

    const facultyMap = new Map();
    facultyData?.forEach(faculty => {
      facultyMap.set(faculty.id, faculty.full_name);
    });

    // 4) Transform mappings to the format expected by TheorySlotGenerator
    const theoryMappings = theoryMappingsData.map(mapping => ({
      id: mapping.id,
      subject_code: mapping.subject_code,
      subject_name: mapping.subject_name,
      subject_short_id: subjectShortIdMap.get(mapping.subject_id),
      faculty_id: mapping.faculty_1_id,
      faculty_name: facultyMap.get(mapping.faculty_1_id) || 'Unknown Faculty',
      weekly_hours: mapping.hours_per_week || 0
    }));



    // 4) Generate theory slots with comprehensive conflict resolution

    const theoryGen = new TheorySlotGenerator();
    const newTheorySlots = await theoryGen.generateTheorySlots(
      theoryMappings,
      { academicYear, department, semester, section }
    );

    console.log(`Generated ${newTheorySlots.length} theory slots with intelligent conflict resolution`);

    // Get detailed allocation results for debugging
    const allocationResults = theoryGen.getAllocationResults();
    console.log('📊 Allocation Results:', allocationResults);

    // Check if any slots were generated
    if (newTheorySlots.length === 0) {
      toast({
        title: "No Theory Slots Generated",
        description: "No vacant slots available for theory allocation. Try clearing some slots first.",
        variant: "destructive"
      });
      setIsLoading(false);
      return;
    }

    // Show detailed results in toast if there are issues
    if (allocationResults.unallocatedSubjects.length > 0 || allocationResults.partiallyAllocatedSubjects.length > 0) {
      const unallocatedCount = allocationResults.unallocatedSubjects.length;
      const partialCount = allocationResults.partiallyAllocatedSubjects.length;

      let description = "";
      if (unallocatedCount > 0) {
        description += `${unallocatedCount} subjects completely failed to allocate. `;
      }
      if (partialCount > 0) {
        description += `${partialCount} subjects partially allocated. `;
      }
      description += "Check browser console for detailed analysis.";

      toast({
        title: "Theory Slot Generation Issues",
        description,
        variant: "destructive"
      });
    }

    // 3) Check if any slots couldn't be allocated due to faculty clashes
    const unallocatedSubjects = theoryGen.getUnallocatedSubjects();

    // 4) Handle any unallocated subjects by finding free slots
    if (unallocatedSubjects && unallocatedSubjects.length > 0) {
      console.log("Attempting to place unallocated subjects:", unallocatedSubjects);

      // Get all subject-faculty mappings for unallocated subjects
      const { data: unallocatedMappings } = await supabase
        .from("subject_faculty_mappings")
        .select("*")
        .eq("academic_year", academicYear)
        .eq("department", department)
        .eq("semester", semester)
        .eq("section", section)
        .in("subject_code", unallocatedSubjects);

      if (unallocatedMappings && unallocatedMappings.length > 0) {
        // Get all existing timetable slots to check for free slots
        const { data: allExistingSlots } = await supabase
          .from("timetable_slots")
          .select("*")
          .eq("academic_year", academicYear);

        // Get time structure to know all possible slots
        const timeStructure = await TimetableService.fetchTimeStructure({
          academicYear,
          department,
        });

        if (timeStructure) {
          const days = timeStructure.working_days;
          // Generate time slots from the time structure
          const allTimeSlots = timeSlots.filter(ts => ts.isPeriod).map(ts => ts.time);
          // Or use a different property that contains the time slots
          // const allTimeSlots = generateTimeSlots(timeStructure);
          // ...

          // For each unallocated mapping, find free slots
          for (const mapping of unallocatedMappings) {
            const facultyId = mapping.faculty_id;

            // Get all slots where this faculty is already assigned
            const facultySlots = allExistingSlots?.filter(
              slot => slot.faculty_id === facultyId
            ) || [];

            // Create a map of occupied slots for this faculty
            const occupiedSlots: Record<string, Set<string>> = {};
            days.forEach(day => {
              occupiedSlots[day] = new Set();
            });

            // Mark all slots where faculty is already assigned
            facultySlots.forEach(slot => {
              if (occupiedSlots[slot.day]) {
                occupiedSlots[slot.day].add(slot.time_slot);

                // For lab slots that span multiple periods
                if (slot.subject_type === 'lab' && slot.col_span && slot.col_span > 1) {
                  // Find all periods this lab occupies
                  const startIndex = allTimeSlots.indexOf(slot.time_slot);
                  if (startIndex >= 0) {
                    for (let i = 1; i < slot.col_span; i++) {
                      if (startIndex + i < allTimeSlots.length) {
                        occupiedSlots[slot.day].add(allTimeSlots[startIndex + i]);
                      }
                    }
                  }
                }
              }
            });

            // Find free slots for this faculty
            const freeSlots: {day: string, timeSlot: string}[] = [];
            days.forEach(day => {
              allTimeSlots.forEach(timeSlot => {
                if (!occupiedSlots[day].has(timeSlot)) {
                  freeSlots.push({day, timeSlot});
                }
              });
            });

            console.log(`Found ${freeSlots.length} free slots for faculty ${mapping.faculty_name}`);

            // Calculate how many more slots we need for this subject
            const existingSlotCount = newTheorySlots.filter(
              slot => slot.subject_code === mapping.subject_code
            ).length;

            const neededSlots = mapping.weekly_hours - existingSlotCount;

            if (neededSlots > 0 && freeSlots.length > 0) {
              console.log(`Need to allocate ${neededSlots} more slots for ${mapping.subject_code}`);

              // Randomly select slots from free slots
              const selectedSlots = freeSlots
                .sort(() => 0.5 - Math.random())
                .slice(0, neededSlots);

              // Create new slots for these free slots
              for (const slot of selectedSlots) {
                const newSlot = {
                  id: uuidv4(),
                  academic_year: academicYear,
                  department,
                  semester,
                  section,
                  day: slot.day,
                  time_slot: slot.timeSlot,
                  subject_id: mapping.subject_id,
                  subject_code: mapping.subject_code,
                  subject_name: mapping.subject_name,
                  subject_short_id: mapping.subject_short_id,
                  subject_type: "theory" as const,
                  faculty_id: mapping.faculty_id,
                  faculty_name: mapping.faculty_name,
                  is_manual_allocation: true // Mark as manually allocated
                };

                // Insert into database
                const { error } = await supabase
                  .from("timetable_slots")
                  .insert(newSlot);

                if (error) {
                  console.error(`Error inserting fallback slot for ${mapping.subject_code}:`, error);
                } else {
                  console.log(`Successfully placed ${mapping.subject_code} on ${slot.day} at ${slot.timeSlot}`);
                  newTheorySlots.push(newSlot);
                }
              }
            }
          }
        }
      }
    }

    // Show warning if we still have unallocated subjects after our attempts
    const stillUnallocated = theoryGen.getUnallocatedSubjects();
    if (stillUnallocated.length > 0) {
      toast({
        title: "Faculty Clash Warning",
        description: `Some theory subjects couldn't be fully allocated due to faculty clashes: ${stillUnallocated.join(', ')}`,
        variant: "warning"
      });
    }

    console.log("Inserted theory slots:", newTheorySlots);

    // 5) Recalculate faculty availability for all affected faculty after theory generation
    if (timeStructure) {
      try {
        await FacultyAvailabilityRecalculator.recalculateForAllAffectedFaculty(
          academicYear,
          department,
          timeStructure
        );
        console.log(`✅ Recalculated availability for all faculty after theory generation`);
      } catch (error) {
        console.error("❌ Error recalculating faculty availability after theory generation:", error);
      }
    }

    // 6) reload full grid
    await loadTimetableData();
    toast({
      title: "Theory Slots Generated",
      description: `Added ${newTheorySlots.length} theory slots.`,
    });
  } catch (error) {
    console.error(error);

    // Check for specific error types
    if (error && typeof error === 'object' && 'code' in error) {
      const pgError = error as { code: string, message: string, details?: string };

      // Handle duplicate key constraint violation
      if (pgError.code === '23505' && pgError.details?.includes('uq_timetable_per_subject')) {
        toast({
          title: "Duplicate Slot Error",
          description: "Some theory slots already exist. Try clearing existing slots first or check for duplicate subject mappings.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Database Error",
          description: `Failed to generate theory slots: ${pgError.message}`,
          variant: "destructive",
        });
      }
    } else if (error instanceof Error && error.message.includes('critical faculty conflicts')) {
      // Handle faculty lab conflicts
      toast({
        title: "Faculty Scheduling Conflict",
        description: "Cannot generate theory slots because some faculty members are already scheduled for lab sessions in other classes during the same periods. Please check the console for details.",
        variant: "destructive",
      });

      // Show a more detailed error message
      console.error("FACULTY SCHEDULING CONFLICT DETAILS:");
      console.error("Some faculty members are scheduled to teach theory classes during periods where they are already assigned to lab sessions in other sections.");
      console.error("This is a critical conflict that must be resolved before proceeding.");
      console.error("Please check the lab schedules for these faculty members and ensure they are not double-booked.");
    } else {
      toast({
        title: "Error",
        description: "Failed to generate theory slots. See console for details.",
        variant: "destructive",
      });
    }
  } finally {
    setIsLoading(false);
  }
};


  // Load time structure and generate time slots
  useEffect(() => {
    const loadTimeStructure = async () => {
      setIsLoading(true);
      try {
        const structure = await TimetableService.fetchTimeStructure({
          academicYear,
          department
        });

        if (structure) {
          setTimeStructure(structure);
          // Set working days from the time structure
          setDays(structure.working_days);

          // Generate time slots based on the structure
          const slots = generateTimeSlots(structure);
          setTimeSlots(slots);
        } else {
          console.log("No time structure found for", academicYear, department);
          setTimeStructure(null);
          setDays([]);
          setTimeSlots([]);
        }
      } catch (error) {
        console.error("Error loading time structure:", error);
        setTimeStructure(null);
        setDays([]);
        setTimeSlots([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadTimeStructure();
  }, [department, academicYear]);

  // Load timetable data with faculty availability validation
  const loadTimetableData = async () => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('timetable_slots')
        .select('*')
        .eq('academic_year', academicYear)
        .eq('department', department);

      // For class view, filter by semester and section
      if (view === 'class') {
        query = query
          .eq('semester', semester)
          .eq('section', section);
      }
      // For faculty view, show ALL commitments across ALL semester-sections
      else if (view === 'faculty' && selectedFaculty) {
        // Comprehensive faculty view: ignore UI filters and show all their teaching commitments
        query = query.or(`faculty_id.eq.${selectedFaculty},faculty2_id.eq.${selectedFaculty}`);
        console.log(`🔍 Loading comprehensive faculty view for faculty ID: ${selectedFaculty}`);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      console.log("Raw timetable data loaded:", data?.length || 0, "slots");

      // Debug tutorial slots specifically
      const tutorialSlots = data?.filter(slot => slot.subject_code === 'TUTORIAL' || slot.subject_type === 'tutorial') || [];
      if (tutorialSlots.length > 0) {
        console.log(`🎯 Found ${tutorialSlots.length} tutorial slots in loaded data:`, tutorialSlots);
      } else {
        console.log('❌ No tutorial slots found in loaded data');
      }

      // 🔥 FACULTY AVAILABILITY VALIDATION
      // Filter out slots where faculty are not available
      let validatedData = data || [];
      if (data && data.length > 0) {
        console.log("🔍 Validating faculty availability for", data.length, "slots");

        // Get all unique faculty IDs from the slots
        const facultyIds = new Set<string>();
        data.forEach(slot => {
          if (slot.faculty_id) facultyIds.add(slot.faculty_id);
          if (slot.faculty2_id) facultyIds.add(slot.faculty2_id);
        });

        if (facultyIds.size > 0) {
          // Fetch faculty availability data
          const { data: facultyData, error: facultyError } = await supabase
            .from('employee_details')
            .select('id, full_name, vacant_by_day, vacant_count_by_day')
            .in('id', Array.from(facultyIds));

          if (facultyError) {
            console.error("Error fetching faculty availability:", facultyError);
            // Continue without validation if faculty data fetch fails
          } else if (facultyData) {
            // Create faculty availability map
            const facultyAvailabilityMap = new Map();
            facultyData.forEach(faculty => {
              facultyAvailabilityMap.set(faculty.id, {
                name: faculty.full_name,
                vacantByDay: faculty.vacant_by_day || {},
                vacantCountByDay: faculty.vacant_count_by_day || {}
              });
            });

            // Helper function to get time slot index
            const getTimeSlotIndex = (timeSlot: string): number => {
              // Map time slots to indices (1-7 for periods 1-7)
              const timeSlotMap: Record<string, number> = {
                '08:30-09:20': 1,
                '09:20-10:10': 2,
                '10:35-11:25': 3,
                '11:25-12:15': 4,
                '13:15-14:05': 5,
                '14:05-14:55': 6,
                '14:55-15:45': 7
              };
              return timeSlotMap[timeSlot] || -1;
            };

            // Validate each slot against faculty availability
            validatedData = data.filter(slot => {
              const faculty1 = facultyAvailabilityMap.get(slot.faculty_id);
              const faculty2 = slot.faculty2_id ? facultyAvailabilityMap.get(slot.faculty2_id) : null;

              // Check if primary faculty is available
              if (faculty1) {
                const dayAvailability = faculty1.vacantByDay[slot.day] || [];
                const timeSlotIndex = getTimeSlotIndex(slot.time_slot);

                if (timeSlotIndex !== -1 && !dayAvailability.includes(timeSlotIndex)) {
                  console.log(`❌ Removing slot: ${slot.subject_code} on ${slot.day} at ${slot.time_slot} - Faculty ${faculty1.name} not available`);
                  return false;
                }
              }

              // Check if secondary faculty is available (if exists)
              if (faculty2) {
                const dayAvailability = faculty2.vacantByDay[slot.day] || [];
                const timeSlotIndex = getTimeSlotIndex(slot.time_slot);

                if (timeSlotIndex !== -1 && !dayAvailability.includes(timeSlotIndex)) {
                  console.log(`❌ Removing slot: ${slot.subject_code} on ${slot.day} at ${slot.time_slot} - Faculty ${faculty2.name} not available`);
                  return false;
                }
              }

              return true;
            });

            console.log(`✅ Faculty availability validation complete: ${validatedData.length}/${data.length} slots are valid`);
          }
        }
      }

      // Always include skill labs in class view
      if (view === 'class') {
        // Check if we have any skill labs
        const hasSkillLabs = validatedData?.some(slot => slot.subject_code === "SKILL LAB");

        if (!hasSkillLabs) {
          // If no skill labs found, check if there are any in the database
          const { data: skillLabData, error: skillLabError } = await supabase
            .from('timetable_slots')
            .select('*')
            .eq('academic_year', academicYear)
            .eq('department', department)
            .eq('semester', semester)
            .eq('section', section)
            .eq('subject_code', "SKILL LAB");

          if (!skillLabError && skillLabData && skillLabData.length > 0) {
            // Add skill labs to the validated data
            validatedData.push(...skillLabData);
            console.log(`Added ${skillLabData.length} skill lab slots to timetable data`);
          }
        }
      }

      // Debug log for skill labs
      const skillLabs = validatedData?.filter(slot => slot.subject_code === "SKILL LAB") || [];
      if (skillLabs.length > 0) {
        console.log(`Found ${skillLabs.length} skill labs in timetable data:`,
          skillLabs.map(slot => ({
            day: slot.day,
            time: slot.time_slot,
            faculty: slot.faculty_name
          }))
        );
      } else {
        console.log("No skill labs found in timetable data");
      }

      console.log(`Loaded ${validatedData?.length || 0} timetable slots`);

      // Filter data based on skill lab only mode
      let filteredData = validatedData || [];
      if (showOnlySkillLabs) {
        filteredData = filteredData.filter(slot => slot.subject_code === 'SKILL LAB');
        console.log(`Filtered to ${filteredData.length} skill lab slots only`);
      }

      setTimetableData(filteredData);
    } catch (error) {
      console.error("Error loading timetable data:", error);
      toast({
        title: "Error",
        description: "Failed to load timetable data.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadTimetableData();
  }, [department, semester, section, academicYear, view, selectedFaculty]);

  // Trigger automatic conflict resolution for existing data
  const resolveExistingConflicts = async () => {
    if (!department || !semester || !section || !academicYear) return;

    try {
      console.log("🔄 Running automatic conflict resolution for existing data...");

      const conflictParams = {
        academicYear,
        department,
        semester,
        section,
        includeAllSemesters: false
      };

      const resolutionResult = await ConflictResolutionService.resolveAllConflictsIntelligently(conflictParams);

      if (resolutionResult.resolvedConflicts > 0) {
        toast({
          title: "Existing Conflicts Resolved",
          description: `Automatically resolved ${resolutionResult.resolvedConflicts} existing conflicts in the timetable.`,
          variant: "default"
        });

        // Reload data to show the resolved conflicts
        await loadTimetableData();
      } else {
        toast({
          title: "No Conflicts Found",
          description: "No conflicts were detected in the current timetable.",
          variant: "default"
        });
      }
    } catch (error) {
      console.error("❌ Error resolving existing conflicts:", error);
      toast({
        title: "Error",
        description: "Failed to resolve existing conflicts.",
        variant: "destructive"
      });
    }
  };

  // Auto-resolve conflicts when timetable loads (for existing data)
  useEffect(() => {
    // For class view, require all parameters
    if (view === 'class' && timetableData.length > 0 && department && semester && section && academicYear) {
      console.log("🔄 Triggering automatic conflict resolution for class view...");
      const timer = setTimeout(() => {
        resolveExistingConflicts();
      }, 1000);

      return () => clearTimeout(timer);
    }

    // For faculty view, trigger comprehensive recalculation for the selected faculty
    if (view === 'faculty' && selectedFaculty && timetableData.length > 0 && academicYear && timeStructure) {
      console.log(`🔄 Triggering faculty availability recalculation for ${selectedFaculty}...`);
      const timer = setTimeout(async () => {
        try {
          await FacultyAvailabilityRecalculator.recalculateForFaculty(
            [selectedFaculty],
            timeStructure,
            academicYear
          );
          console.log(`✅ Recalculated availability for faculty ${selectedFaculty} in faculty view`);

          // Also trigger conflict resolution for all semester-sections this faculty is involved in
          const uniqueSemesterSections = new Set();
          timetableData.forEach(slot => {
            if (slot.faculty_id === selectedFaculty || slot.faculty2_id === selectedFaculty) {
              uniqueSemesterSections.add(`${slot.semester}-${slot.section}`);
            }
          });

          console.log(`🔄 Running conflict resolution for ${uniqueSemesterSections.size} semester-sections...`);

          for (const semesterSection of uniqueSemesterSections) {
            const [sem, sec] = (semesterSection as string).split('-');
            if (department && sem && sec) {
              try {
                const conflictParams = {
                  academicYear,
                  department,
                  semester: sem,
                  section: sec,
                  includeAllSemesters: false
                };

                const resolutionResult = await ConflictResolutionService.resolveAllConflictsIntelligently(conflictParams);

                if (resolutionResult.resolvedConflicts > 0) {
                  console.log(`✅ Resolved ${resolutionResult.resolvedConflicts} conflicts for ${sem}${sec}`);
                }
              } catch (error) {
                console.error(`❌ Error resolving conflicts for ${sem}${sec}:`, error);
              }
            }
          }

          // Reload timetable data to show updated results
          await loadTimetableData();

        } catch (error) {
          console.error("❌ Error in faculty view automatic recalculation:", error);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [timetableData.length, department, semester, section, academicYear, view, selectedFaculty, timeStructure]);

  // Process timetable data for display
  useEffect(() => {
    if (!timetableData.length || !timeStructure || !timeSlots.length || !days.length) {
      setProcessedTimetable({});
      return;
    }

    // Initialize the structure with arrays for each cell
    const processed: Record<string, Record<string, any[]>> = {};
    days.forEach(day => {
      processed[day] = {};
      timeSlots.forEach(slot => {
        processed[day][slot.time] = [];
      });
    });

    // Filter data if in faculty view
    let dataToProcess = timetableData;
    if (view === 'faculty' && selectedFaculty) {
      // Get the faculty name for logging
      const facultyName = facultyOptions?.find(f => f.id === selectedFaculty)?.name || selectedFaculty;
      console.log(`Filtering timetable for faculty: ${facultyName} (ID: ${selectedFaculty})`);

      // Count primary and secondary roles
      const primaryRoles = timetableData.filter(slot => slot.faculty_id === selectedFaculty);
      const secondaryRoles = timetableData.filter(slot => slot.faculty2_id === selectedFaculty);

      console.log(`Faculty ${facultyName} has ${primaryRoles.length} primary roles and ${secondaryRoles.length} secondary roles`);

      // Filter slots where the faculty is either primary or secondary (for labs only)
      dataToProcess = timetableData.filter(slot => {
        const isPrimaryFaculty = slot.faculty_id === selectedFaculty;
        const isSecondaryFaculty = slot.faculty2_id === selectedFaculty;
        const isLabSubject = slot.subject_type === 'lab' || slot.subject_type === 'laboratory';
        const isSkillLab = slot.subject_code === "SKILL LAB";

        // Always include skill labs in class view, but not in faculty view
        if (isSkillLab) {
          if (view === 'class') {
            return true;
          }
          return false;
        }

        // Handle tutorial slots - include in both views but check faculty assignment for faculty view
        const isTutorial = slot.subject_code === 'TUTORIAL' || slot.subject_type === 'tutorial';
        if (isTutorial) {
          if (view === 'class') {
            return true;
          }
          if (view === 'faculty' && selectedFaculty) {
            const isAssignedToFaculty = slot.faculty_id === selectedFaculty || slot.faculty2_id === selectedFaculty;
            return isAssignedToFaculty;
          }
        }

        if (isPrimaryFaculty) {
          return true;
        } else if (isSecondaryFaculty && isLabSubject) {
          return true;
        } else {
          return false;
        }
      });
    }

    // Process the filtered data
    dataToProcess.forEach(slot => {
      // Debug log for skill labs
      if (slot.subject_code === "SKILL LAB") {
        console.log(`Processing SKILL LAB for day ${slot.day} at ${slot.time_slot}`);
      }

      if (!processed[slot.day]) return;

      // For lab slots that span multiple periods (including skill labs)
      if (slot.subject_type === 'lab' || slot.subject_type === 'laboratory' || slot.subject_type === 'skill_lab' || slot.subject_code === "SKILL LAB") {
        // Check if this is a continued lab slot from the database
        if (slot.is_processed === true && slot.is_lab_start === false && slot.col_span === 1) {
          // This is a continued lab slot from the database
          console.log(`🔄 Processing continued lab slot from database: ${slot.subject_code} on ${slot.day} at ${slot.time_slot}`);

          // Find the matching time slot
          for (const timeSlot of timeSlots) {
            if (!timeSlot.isPeriod) continue;

            if (timeSlot.time === slot.time_slot) {
              processed[slot.day][timeSlot.time].push({
                ...slot,
                isLab: true,
                isLabStart: false,
                isContinuedLab: true,
                is_processed: true,
                isHidden: false,
                col_span: 1,
                showContinuedBadge: true
              });
              break;
            }
          }
        } else {
          // Special handling for skill labs
          if (slot.subject_code === "SKILL LAB") {
            console.log(`Processing SKILL LAB in day ${slot.day} at time ${slot.time_slot}`);
          }

          // Use LabDurationUtils to get comprehensive lab duration information
          const labDurationInfo = LabDurationUtils.getLabDurationInfo(
            slot.time_slot,
            slot.col_span,
            timeSlots
          );

          // Find the matching time slot index using the utility
          const matchingIndex = LabDurationUtils.findMatchingTimeSlotIndex(slot.time_slot, timeSlots);

          if (matchingIndex >= 0) {
            const i = matchingIndex;
            const timeSlot = timeSlots[i];

            // Special handling for skill labs
            if (slot.subject_code === "SKILL LAB") {
              console.log(`Found matching time slot for SKILL LAB: ${timeSlot.time}`);
            }

            const totalPeriods = labDurationInfo.totalPeriods;

            // Debug the specific "PW I - PROJECT PHASE I" lab during processing
            if (slot.subject_code?.includes("PW I") || slot.subject_name?.includes("PROJECT PHASE I")) {
              console.log(`🔍 PROCESSING PW I LAB:`);
              console.log(`  Subject: ${slot.subject_code} - ${slot.subject_name}`);
              console.log(`  time_slot: ${slot.time_slot}`);
              console.log(`  col_span from DB: ${slot.col_span}`);
              console.log(`  totalPeriods calculated: ${totalPeriods}`);
            }

            // Use the lab duration info from the utility
            const labStartPeriod = labDurationInfo.startPeriod;
            const finalSpansTeaBreak = labDurationInfo.spansTeaBreak;

            // Special handling for skill labs
            if (slot.subject_code === "SKILL LAB") {
              console.log(`Adding SKILL LAB to processed timetable for day ${slot.day} at time ${timeSlot.time}`);
            }

            // Debug spans tea break logic
            if (finalSpansTeaBreak) {
              console.log(`🔥 Lab ${slot.subject_code} on ${slot.day} at ${timeSlot.time} SPANS TEA BREAK:`, {
                labStartIndex: i,
                labStartPeriod,
                totalPeriods,
                labEndIndex: i + totalPeriods - 1,
                finalSpansTeaBreak
              });
            }

            // Additional debug for 3-hour morning labs
            if (labDurationInfo.is3HourMorningLab) {
              console.log(`🌅 3-hour morning lab detected: ${slot.subject_code} on ${slot.day}`, {
                totalPeriods,
                labStartPeriod,
                finalSpansTeaBreak,
                labStartIndex: i
              });
            }

            // Build the lab-meta object using the utility info
            const is3HourMorningLab = labDurationInfo.is3HourMorningLab;
            const visualColSpan = labDurationInfo.visualColSpan;

            console.log(`🔍 DEBUG DATA PROCESSING: ${slot.subject_code}`, {
              'totalPeriods': totalPeriods,
              'visualColSpan': visualColSpan,
              'is3HourMorningLab': is3HourMorningLab,
              'time_slot': slot.time_slot
            });

            const labSlotWithMeta = {
              ...slot,
              isLab: true,
              isLabStart: true,
              colSpan: visualColSpan, // Visual span for rendering
              col_span: totalPeriods, // Keep original span for logic
              isProcessed: false,
              spansTeaBreak: finalSpansTeaBreak,
              teaBreakIndex: finalSpansTeaBreak ? (timeSlots.findIndex(ts => ts.breakType === 'tea') - i) : null
            };

            // Add the lab start
            processed[slot.day][timeSlot.time].push(labSlotWithMeta);

            // For 3-hour morning labs, manually create period 3 continuation card
            if (is3HourMorningLab) {
              // Find period 3 time slot (10:35-11:30)
              const period3TimeSlot = timeSlots.find(ts => ts.periodNumber === 3);
              if (period3TimeSlot) {
                console.log(`🔄 Creating period 3 card for 3-hour morning lab ${slot.subject_code} on ${slot.day} at ${period3TimeSlot.time}`);
                processed[slot.day][period3TimeSlot.time].push({
                  ...slot,
                  isPartOfLab: true,
                  isHidden: false, // Show as separate card in period 3
                  col_span: totalPeriods // Keep original span for identification
                });
              }

              // Mark period 2 as hidden (covered by main lab card)
              const period2TimeSlot = timeSlots.find(ts => ts.periodNumber === 2);
              if (period2TimeSlot) {
                processed[slot.day][period2TimeSlot.time].push({
                  ...slot,
                  isPartOfLab: true,
                  isHidden: true
                });
              }
            } else {
              // For other labs, mark subsequent periods as hidden
              for (let offset = 1; offset < totalPeriods; offset++) {
                const coveredSlotIndex = i + offset;
                const covered = timeSlots[coveredSlotIndex]?.time;
                if (covered) {
                  processed[slot.day][covered].push({
                    ...slot,
                    isPartOfLab: true,
                    isHidden: true
                  });
                }
              }
            }
          }
        }
      } else {
        // For theory and tutorial slots
        const theoryTimeSlot = slot.time_slot;
        const isTutorial = slot.subject_code === 'TUTORIAL' || slot.subject_type === 'tutorial';

        // Debug tutorial processing
        if (isTutorial) {
          console.log(`🎯 Processing tutorial slot: ${slot.subject_code} on ${slot.day} at ${theoryTimeSlot}`);
        }

        // Find the matching time slot
        for (const timeSlot of timeSlots) {
          if (!timeSlot.isPeriod) continue;

          if (timeSlot.time === theoryTimeSlot) {
            if (isTutorial) {
              console.log(`✅ Found matching time slot for tutorial: ${timeSlot.time}`);
            }

            processed[slot.day][timeSlot.time].push({
              ...slot,
              isLab: false,
              isLabStart: false,
              colSpan: 1,
              col_span: 1, // Also set snake_case for consistency
              isProcessed: false
            });

            if (isTutorial) {
              console.log(`✅ Added tutorial to processed timetable for ${slot.day} at ${timeSlot.time}`);
            }
            break;
          }
        }
      }
    });

    // Debug: Log all labs that span tea break
    console.log("🔍 FINAL PROCESSED TIMETABLE DEBUG:");
    Object.keys(processed).forEach(day => {
      Object.keys(processed[day]).forEach(timeSlot => {
        const contents = processed[day][timeSlot];
        if (contents.length > 0) {
          contents.forEach(content => {
            if (content.isLabStart && content.spansTeaBreak) {
              console.log(`✅ Lab ${content.subject_code} on ${day} at ${timeSlot} SPANS TEA BREAK`);
            }
          });
        }
      });
    });

    setProcessedTimetable(processed);
  }, [timetableData, timeStructure, timeSlots, days, view, selectedFaculty]);

  useEffect(() => {
    if (view === 'faculty' && selectedFaculty) {
      // When switching to faculty view, we need to reload the timetable
      // but filter it for the selected faculty
      loadTimetableData();
    }
  }, [view, selectedFaculty]);

  // Handle drag over for drop target
  const handleDragOver = (e: React.DragEvent<HTMLTableCellElement>) => {
    e.preventDefault();
    e.currentTarget.classList.add('bg-blue-100');
  };

  // Handle drag leave for drop target
  const handleDragLeave = (e: React.DragEvent<HTMLTableCellElement>) => {
    e.currentTarget.classList.remove('bg-blue-100');
  };

  // Handle drop for drop target
  const handleDrop = async (e: React.DragEvent<HTMLTableCellElement>, day: string, timeSlot: string) => {
    e.preventDefault();
    e.currentTarget.classList.remove('bg-blue-100');

    try {
      const slotData = JSON.parse(e.dataTransfer.getData('text/plain')) as TimetableSlot;
      console.log("Dropped slot data:", slotData);

      // Create updated slot with new day and time_slot
      const updatedSlot: TimetableSlot = {
        ...slotData,
        day,
        time_slot: timeSlot
      };

      // Check for clashes when in faculty view
      if (view === 'faculty' && selectedFaculty) {
        // Check if this would create a clash in the target class
        const targetClassSlots = await checkForClassClash(updatedSlot);

        if (targetClassSlots.length > 0) {
          // There's a clash in the target class
          toast({
            title: "Slot Clash Detected",
            description: `Cannot move this slot as it would create a clash with ${targetClassSlots[0].subject_code} in the target class.`,
            variant: "destructive"
          });
          return; // Prevent the drop
        }
      }

      // If we get here, no clash was detected or we're not in faculty view
      const result = await TimetableService.saveTimetableSlot(updatedSlot);

      if (result) {
        // Recalculate faculty availability for affected faculty
        if (timeStructure && academicYear) {
          const facultyIds = [];
          if (updatedSlot.faculty_id) facultyIds.push(updatedSlot.faculty_id);
          if (updatedSlot.faculty2_id) facultyIds.push(updatedSlot.faculty2_id);

          if (facultyIds.length > 0) {
            await FacultyAvailabilityRecalculator.recalculateForFaculty(
              facultyIds,
              timeStructure,
              academicYear
            );
            console.log(`✅ Recalculated availability after drag & drop for faculty: ${facultyIds.join(', ')}`);
          }
        }

        // Automatic conflict resolution after drag & drop
        if (department && semester && section && academicYear) {
          try {
            console.log("🔄 Running automatic conflict resolution after drag & drop...");
            const conflictParams = {
              academicYear,
              department,
              semester,
              section,
              includeAllSemesters: false
            };

            const resolutionResult = await ConflictResolutionService.resolveAllConflictsIntelligently(conflictParams);

            if (resolutionResult.resolvedConflicts > 0) {
              toast({
                title: "Conflicts Auto-Resolved",
                description: `Automatically resolved ${resolutionResult.resolvedConflicts} conflicts after moving ${slotData.subject_code}.`,
                variant: "default"
              });
            }
          } catch (error) {
            console.error("❌ Error in automatic conflict resolution:", error);
          }
        }

        // Update the UI
        setTimetableData(prev =>
          prev.map(slot => slot.id === slotData.id ? updatedSlot : slot)
        );

        toast({
          title: "Slot Updated",
          description: `${slotData.subject_code} moved to ${day} at ${timeSlot}`,
        });
      }
    } catch (error) {
      console.error("Error handling drop:", error);
      toast({
        title: "Error",
        description: "Failed to update timetable slot",
        variant: "destructive",
      });
    }
  };

  // Function to check for clashes in the target class
  const checkForClassClash = async (updatedSlot: TimetableSlot): Promise<TimetableSlot[]> => {
    try {
      // Fetch all slots for the target class (semester/section)
      const { data: classSlots, error } = await supabase
        .from('timetable_slots')
        .select('*')
        .eq('academic_year', updatedSlot.academic_year)
        .eq('department', updatedSlot.department)
        .eq('semester', updatedSlot.semester)
        .eq('section', updatedSlot.section)
        .eq('day', updatedSlot.day);

      if (error) {
        console.error("Error checking for class clash:", error);
        return [];
      }

      // Filter out the slot being moved (if it's already in this class)
      let potentialClashes = classSlots.filter(slot => slot.id !== updatedSlot.id);

      // Check for direct time slot matches
      const directClashes = potentialClashes.filter(slot =>
        slot.time_slot === updatedSlot.time_slot
      );

      if (directClashes.length > 0) {
        console.log(`Found direct clash with ${directClashes[0].subject_code}`);
        return directClashes;
      }

      // Check for lab slots that might span multiple periods
      const labSlots = potentialClashes.filter(slot =>
        slot.subject_type === 'lab' &&
        slot.is_lab_start &&
        slot.col_span &&
        slot.col_span > 1
      );

      // For each lab slot, check if it overlaps with the target time slot
      for (const labSlot of labSlots) {
        // Get all time slots
        const allTimeSlots = timeSlots
          .filter(ts => ts.isPeriod)
          .map(ts => ts.time);

        // Find the index of the lab start time
        const labStartIndex = allTimeSlots.indexOf(labSlot.time_slot);

        if (labStartIndex >= 0) {
          // Calculate the lab end index based on col_span
          const labEndIndex = labStartIndex + (labSlot.col_span || 1) - 1;

          // Find the index of the target time slot
          const targetIndex = allTimeSlots.indexOf(updatedSlot.time_slot);

          // Check if the target time slot falls within the lab's span
          if (targetIndex >= labStartIndex && targetIndex <= labEndIndex) {
            console.log(`Found lab clash with ${labSlot.subject_code} (spans periods ${labStartIndex+1}-${labEndIndex+1})`);
            return [labSlot];
          }
        }
      }

      console.log(`No clashes found in target class`);
      return [];
    } catch (err) {
      console.error("Exception checking for class clash:", err);
      return [];
    }
  };

  // Handle slot updates
  const handleSlotUpdate = async (updatedSlot: TimetableSlot) => {
    try {
      // Clean up any UI-specific properties before saving
      const slotToSave = { ...updatedSlot };
      delete (slotToSave as any).isLab;
      delete (slotToSave as any).colSpan;

      await TimetableService.saveTimetableSlot(slotToSave);

      // Recalculate faculty availability for affected faculty
      if (timeStructure && academicYear) {
        const facultyIds = [];
        if (slotToSave.faculty_id) facultyIds.push(slotToSave.faculty_id);
        if (slotToSave.faculty2_id) facultyIds.push(slotToSave.faculty2_id);

        if (facultyIds.length > 0) {
          await FacultyAvailabilityRecalculator.recalculateForFaculty(
            facultyIds,
            timeStructure,
            academicYear
          );
          console.log(`✅ Recalculated availability after slot update for faculty: ${facultyIds.join(', ')}`);
        }
      }

      // Automatic conflict resolution after manual slot update
      if (department && semester && section && academicYear) {
        try {
          console.log("🔄 Running automatic conflict resolution after slot update...");
          const conflictParams = {
            academicYear,
            department,
            semester,
            section,
            includeAllSemesters: false
          };

          const resolutionResult = await ConflictResolutionService.resolveAllConflictsIntelligently(conflictParams);

          if (resolutionResult.resolvedConflicts > 0) {
            toast({
              title: "Conflicts Auto-Resolved",
              description: `Automatically resolved ${resolutionResult.resolvedConflicts} conflicts after your update.`,
              variant: "default"
            });
          }
        } catch (error) {
          console.error("❌ Error in automatic conflict resolution:", error);
        }
      }

      toast({
        title: "Slot Updated",
        description: "The timetable slot has been updated successfully."
      });
      loadTimetableData();
    } catch (error) {
      console.error("Error updating slot:", error);
      toast({
        title: "Error",
        description: "Failed to update timetable slot.",
        variant: "destructive"
      });
    }
  };

  // Handle slot deletion
  const handleSlotDelete = async (slotId: string) => {
    try {
      // Get the slot data before deletion to identify affected faculty
      const slotToDelete = timetableData.find(slot => slot.id === slotId);

      await TimetableService.deleteTimetableSlot(slotId);

      // Recalculate faculty availability for affected faculty
      if (slotToDelete && timeStructure && academicYear) {
        const facultyIds = [];
        if (slotToDelete.faculty_id) facultyIds.push(slotToDelete.faculty_id);
        if (slotToDelete.faculty2_id) facultyIds.push(slotToDelete.faculty2_id);

        if (facultyIds.length > 0) {
          await FacultyAvailabilityRecalculator.recalculateForFaculty(
            facultyIds,
            timeStructure,
            academicYear
          );
          console.log(`✅ Recalculated availability after slot deletion for faculty: ${facultyIds.join(', ')}`);
        }
      }

      toast({
        title: "Slot Deleted",
        description: "The timetable slot has been deleted successfully."
      });
      loadTimetableData();
    } catch (error) {
      console.error("Error deleting slot:", error);
      toast({
        title: "Error",
        description: "Failed to delete timetable slot.",
        variant: "destructive"
      });
    }
  };

  // Render cell with the appropriate content
  const renderCell = (day: string, timeSlot: string, index: number) => {
    const cellContents = processedTimetable[day]?.[timeSlot] || [];



    // 1) Handle hidden parts of spanning labs
    const hiddenLabParts = cellContents.filter(c => c.isHidden && c.isPartOfLab);
    if (hiddenLabParts.length > 0) {
      // For 3-hour morning labs in period 2, show a visual continuation instead of hiding
      const hiddenLab = hiddenLabParts[0];
      const currentPeriod = timeSlots.find(ts => ts.time === timeSlot)?.periodNumber;

      // Check if this is period 2 of a 3-hour morning lab
      const labDurationInfo = LabDurationUtils.getLabDurationInfo(
        hiddenLab.time_slot,
        hiddenLab.col_span,
        timeSlots
      );

      if (labDurationInfo.is3HourMorningLab && currentPeriod === 2) {

        return (
          <TableCell
            key={`${day}-${timeSlot}-${index}-continuation`}
            className="p-0 border-2 border-emerald-300 bg-gradient-to-br from-emerald-50 to-green-50 opacity-75"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={e => handleDrop(e, day, timeSlot)}
            data-day={day}
            data-slot={timeSlot}
          >
            <div className="p-2 text-center">
              <div className="text-xs text-emerald-600 font-medium">
                {hiddenLab.subject_short_id || hiddenLab.subject_code}
              </div>
              <div className="text-xs text-emerald-500 mt-1">
                (Continued)
              </div>
            </div>
          </TableCell>
        );
      }

      // For other hidden lab parts, return null (skip rendering)
      return null;
    }

    // 2) REMOVED: Do NOT skip break headers - breaks are handled in the main table rendering logic
    // Break cells maintain their fixed positions and are rendered separately



    // 4) Labs starting in this slot: merge across span with proper colSpan
    const labStarts = cellContents.filter(c => (c.isLabStart || c.subject_code === "SKILL LAB") && !c.is_processed);
    if (labStarts.length > 0) {
      // Get lab duration information using our utility
      const labData = labStarts[0];

      // CRITICAL FIX: Use the processed colSpan (visual span) if available, otherwise calculate it
      const effectiveColSpan = labData.colSpan || labData.col_span;

      const labDurationInfo = LabDurationUtils.getLabDurationInfo(
        labData.time_slot,
        effectiveColSpan,
        timeSlots
      );

      // Use the visual colspan from our utility
      const actualColspan = labDurationInfo.visualColSpan;

      // CRITICAL FIX: For 3-hour morning labs, use colSpan = 2 to span periods 1-2 only
      // This will span exactly 2 periods (1 and 2), then tea break will be in its fixed position
      const cellColSpan = labDurationInfo.is3HourMorningLab ? 2 : actualColspan;

      return (
        <TableCell
          key={`${day}-${timeSlot}-${index}`}
          className="p-0 border-2 border-gray-300 bg-gradient-to-br from-emerald-50 to-green-50 hover:from-emerald-100 hover:to-green-100 transition-all duration-200"
          colSpan={cellColSpan}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={e => handleDrop(e, day, timeSlot)}
          data-day={day}
          data-slot={timeSlot}
        >
          <div className="space-y-1">
            {labStarts.map((c, i) => {
              const isSkillLab = c.subject_code === "SKILL LAB";

              return (
                <TimetableCellContent
                  key={`lab-${c.id}-${i}`}
                  slot={{
                    ...c,
                    customTextColor: isSkillLab ? 'text-purple-800' : 'text-emerald-800'
                  }}
                  isLab
                  onUpdate={handleSlotUpdate}
                  onDelete={handleSlotDelete}
                  view={view}
                  selectedFaculty={selectedFaculty}
                  className={isSkillLab ? 'text-purple-800' : 'text-emerald-800'}
                />
              );
            })}
          </div>
        </TableCell>
      );
    }

    // 4.5) Check for period 3 of 3-hour morning labs (after tea break)
    const period3LabContinuation = cellContents.filter(c => {
      if (!c.isPartOfLab || c.isHidden) return false;

      // CRITICAL FIX: Use the original col_span (not the visual colSpan) for logic detection
      const originalColSpan = c.col_span; // This should be 3 for 3-hour labs

      // Use our utility to check if this is a 3-hour morning lab continuation
      const labDurationInfo = LabDurationUtils.getLabDurationInfo(
        c.time_slot,
        originalColSpan,
        timeSlots
      );

      const currentPeriod = timeSlots.find(ts => ts.time === timeSlot)?.periodNumber;

      // This should be a 3-hour morning lab and we're in period 3
      return labDurationInfo.is3HourMorningLab && currentPeriod === 3;
    });

    if (period3LabContinuation.length > 0) {


      return (
        <TableCell
          key={`${day}-${timeSlot}-${index}`}
          className="p-0 border-2 border-gray-300 bg-gradient-to-br from-orange-50 to-amber-50 hover:from-orange-100 hover:to-amber-100 transition-all duration-200"
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={e => handleDrop(e, day, timeSlot)}
          data-day={day}
          data-slot={timeSlot}
        >
          <div className="space-y-1">
            {period3LabContinuation.map((labData, i) => (
              <TimetableCellContent
                key={`continued-lab-${labData.id}-${i}`}
                slot={{
                  ...labData,
                  customTextColor: 'text-orange-800',
                  isContinuedLab: true,
                  showContinuedBadge: true,
                  excludeFaculty: true
                }}
                isLab
                onUpdate={handleSlotUpdate}
                onDelete={handleSlotDelete}
                view={view}
                selectedFaculty={selectedFaculty}
                className="text-orange-800"
              />
            ))}
          </div>
        </TableCell>
      );
    }

    // 5) Theory, Tutorial, and Elective slots
    if (cellContents.length > 0) {
      const c = cellContents[0];
      const isTutorial = c.subject_code === 'TUTORIAL' || c.subject_type === 'tutorial';
      const isElective = c.subject_type === 'elective';

      // Filter only elective slots for parallel detection
      const electiveSlots = cellContents.filter(slot => slot.subject_type === 'elective');
      const hasParallelElectives = electiveSlots.length > 1;



      // Different styling for different slot types
      let cellClassName = "";
      if (isTutorial) {
        cellClassName = "p-2 border-2 border-gray-300 bg-gradient-to-br from-orange-50 to-amber-50 hover:from-orange-100 hover:to-amber-100 transition-all duration-200";
      } else if (isElective) {
        cellClassName = "p-2 border-2 border-gray-300 bg-gradient-to-br from-purple-50 to-indigo-50 hover:from-purple-100 hover:to-indigo-100 transition-all duration-200";
      } else {
        cellClassName = "p-2 border-2 border-gray-300 bg-gradient-to-br from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 transition-all duration-200";
      }

      return (
        <TableCell
          key={`${day}-${timeSlot}-${index}`}
          className={cellClassName}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={e => handleDrop(e, day, timeSlot)}
          data-day={day}
          data-slot={timeSlot}
        >
          <div className="h-full space-y-1">
            {hasParallelElectives && (
              // Render parallel electives (same group - mutually exclusive)
              <div className="space-y-1">
                {electiveSlots.map((elective, idx) => (
                  <TimetableCellContent
                    key={`elective-${idx}`}
                    slot={{
                      ...elective,
                      customTextColor: 'text-purple-800',
                      displayText: `${elective.subject_short_id || elective.subject_code}`
                    }}
                    isLab={false}
                    onUpdate={handleSlotUpdate}
                    onDelete={handleSlotDelete}
                    view={view}
                    selectedFaculty={selectedFaculty}
                    className="text-purple-800"
                  />
                ))}
              </div>
            )}

            {/* Render non-elective slots */}
            {cellContents.filter(slot => slot.subject_type !== 'elective').map((slot, idx) => {
              const isTutorialSlot = slot.subject_code === 'TUTORIAL' || slot.subject_type === 'tutorial';
              return (
                <TimetableCellContent
                  key={`non-elective-${idx}`}
                  slot={{
                    ...slot,
                    customTextColor: isTutorialSlot ? 'text-orange-800' : 'text-blue-800'
                  }}
                  isLab={false}
                  onUpdate={handleSlotUpdate}
                  onDelete={handleSlotDelete}
                  view={view}
                  selectedFaculty={selectedFaculty}
                  className={isTutorialSlot ? 'text-orange-800' : 'text-blue-800'}
                />
              );
            })}

            {/* Render single elective if no parallel electives */}
            {!hasParallelElectives && electiveSlots.length === 1 && (
              <TimetableCellContent
                slot={{
                  ...electiveSlots[0],
                  customTextColor: 'text-purple-800',
                  displayText: electiveSlots[0].subject_short_id || electiveSlots[0].subject_code
                }}
                isLab={false}
                onUpdate={handleSlotUpdate}
                onDelete={handleSlotDelete}
                view={view}
                selectedFaculty={selectedFaculty}
                className="text-purple-800"
              />
            )}
          </div>
        </TableCell>
      );
    }

    // 6) Empty
    return (
      <TableCell
        key={`${day}-${timeSlot}-${index}`}
        className="p-2 border-2 min-w-[100px] h-16 bg-card hover:bg-muted/50 transition-all duration-200 relative group"
        style={{ borderColor: 'hsl(var(--border))' }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={e => handleDrop(e, day, timeSlot)}
        data-day={day}
        data-slot={timeSlot}
      >
        {/* Subtle drop zone indicator */}
        <div className="absolute inset-0 border-2 border-dashed border-transparent group-hover:border-primary/30 rounded-md transition-all duration-200" />
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-30 transition-opacity duration-200">
          <div className="text-xs text-muted-foreground font-medium">Drop here</div>
        </div>
      </TableCell>
    );
  };






  return (
    <div className="timetable-container overflow-auto relative">
      {/* Faculty View Comprehensive Information Panel */}
      {view === 'faculty' && selectedFaculty && timetableData.length > 0 && (
        <div className="mb-4 bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full"></div>
              <h3 className="text-lg font-semibold text-blue-900">
                Comprehensive Faculty Schedule
              </h3>
            </div>
            <div className="text-sm text-blue-700 font-medium">
              {facultyOptions.find(f => f.id === selectedFaculty)?.name || 'Selected Faculty'}
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
            <div className="bg-card p-3 rounded-lg border border-primary/20">
              <div className="text-2xl font-bold text-primary">{timetableData.length}</div>
              <div className="text-sm text-primary/80">Total Slots</div>
            </div>
            <div className="bg-card p-3 rounded-lg border border-accent/20">
              <div className="text-2xl font-bold text-accent">
                {[...new Set(timetableData.map(slot => `${slot.semester}${slot.section}`))].length}
              </div>
              <div className="text-sm text-accent/80">Classes Teaching</div>
            </div>
            <div className="bg-card p-3 rounded-lg border border-secondary/20">
              <div className="text-2xl font-bold text-secondary">
                {timetableData.filter(slot => slot.subject_type === 'theory').length}
              </div>
              <div className="text-sm text-secondary/80">Theory Slots</div>
            </div>
            <div className="bg-card p-3 rounded-lg border border-green-500/20">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {timetableData.filter(slot => slot.subject_type === 'lab').length}
              </div>
              <div className="text-sm text-green-600/80 dark:text-green-400/80">Lab Slots</div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <div className="text-sm text-blue-700">
              <span className="font-medium">Classes:</span> {[...new Set(timetableData.map(slot => `${slot.semester}${slot.section}`))].join(', ')}
            </div>
            <div className="text-sm text-blue-700">
              <span className="font-medium">Primary Role:</span> {timetableData.filter(slot => slot.faculty_id === selectedFaculty).length} slots
            </div>
            <div className="text-sm text-blue-700">
              <span className="font-medium">Secondary Role:</span> {timetableData.filter(slot => slot.faculty2_id === selectedFaculty).length} slots
            </div>
          </div>
        </div>
      )}

      {/* Skill Lab Filter Toggle */}
      {timetableData.some(slot => slot.subject_code === 'SKILL LAB') && (
        <div className="mb-4 flex items-center justify-between bg-purple-50 p-3 rounded-lg border border-purple-200">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-purple-600 rounded"></div>
            <span className="text-sm font-medium text-purple-800">Skill Lab View</span>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-purple-700">
              {showOnlySkillLabs ? 'Showing only skill labs' : 'Showing all sessions'}
            </span>
            <button
              onClick={() => {
                setShowOnlySkillLabs(!showOnlySkillLabs);
                // Reload data with new filter
                loadTimetableData();
              }}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                showOnlySkillLabs
                  ? 'bg-secondary text-secondary-foreground hover:bg-secondary/90'
                  : 'bg-card text-secondary border border-secondary/30 hover:bg-secondary/5'
              }`}
            >
              {showOnlySkillLabs ? 'Show All Sessions' : 'Show Only Skill Labs'}
            </button>
          </div>
        </div>
      )}
      <style dangerouslySetInnerHTML={{
        __html: `
        .writing-vertical {
          min-height: 120px;
          width: 40px;
        }
        .rotate-270 {
          writing-mode: vertical-rl;
          transform: rotate(180deg);
          text-orientation: mixed;
        }
        .break-cell {
          text-align: center;
          font-weight: 600;
        }
        .lab-span-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 20px;
          z-index: 10;
        }

        /* Enhanced responsive styles */
        @media (max-width: 1024px) {
          .timetable-container {
            overflow-x: auto;
            overflow-y: visible;
          }

          .timetable-container table {
            min-width: 800px;
          }
        }

        @media (max-width: 768px) {
          .timetable-container table {
            min-width: 700px;
          }

          .writing-vertical {
            min-height: 100px;
            width: 35px;
          }
        }

        @media (max-width: 640px) {
          .timetable-container table {
            min-width: 600px;
          }

          .writing-vertical {
            min-height: 80px;
            width: 30px;
          }
        }
      `}} />
      <div className="modern-timetable-container rounded-xl border-2 border-border/50 bg-card/95 backdrop-blur-sm shadow-strong overflow-hidden">
        <Table className="border-separate border-spacing-0">
          <TableCaption className="bg-muted/30 p-4 text-sm font-medium text-foreground border-b border-border/50">
            {timeStructure ? (
              view === 'faculty' ? (
                (() => {
                  const facultyName = facultyOptions.find(f => f.id === selectedFaculty)?.name || 'Selected Faculty';
                  const totalSlots = timetableData.length;
                  const uniqueSemesters = [...new Set(timetableData.map(slot => `${slot.semester}${slot.section}`))];
                  const theorySlots = timetableData.filter(slot => slot.subject_type === 'theory').length;
                  const labSlots = timetableData.filter(slot => slot.subject_type === 'lab').length;
                  const primaryRoles = timetableData.filter(slot => slot.faculty_id === selectedFaculty).length;
                  const secondaryRoles = timetableData.filter(slot => slot.faculty2_id === selectedFaculty).length;

                  return (
                    <div className="space-y-2">
                      <div className="text-lg font-semibold text-primary">
                        Faculty Schedule: {facultyName} ({academicYear})
                      </div>
                      <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                          {totalSlots} Total Slots
                        </span>
                        <span className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-success rounded-full"></div>
                          {uniqueSemesters.length} Classes: {uniqueSemesters.join(', ')}
                        </span>
                        <span className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-info rounded-full"></div>
                          Theory: {theorySlots}, Lab: {labSlots}
                        </span>
                        <span className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-warning rounded-full"></div>
                          Primary: {primaryRoles}, Secondary: {secondaryRoles}
                        </span>
                      </div>
                    </div>
                  );
                })()
              ) : (
                <div className="space-y-2">
                  <div className="text-lg font-semibold text-primary">
                    Class Timetable: {department.toUpperCase()} - Semester {semester}, Section {section}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Academic Year: {academicYear}
                  </div>
                </div>
              )
            ) : (
              <div className="text-center py-4">
                <div className="text-warning font-medium">No time structure defined</div>
                <div className="text-sm text-muted-foreground">Please define in the Time Structure tab</div>
              </div>
            )}
          </TableCaption>
        <TableHeader>
          <TableRow className="border-b-2 border-border">
            <TableHead className="min-w-[100px] bg-gradient-to-r from-primary to-primary/90 text-primary-foreground text-center font-bold text-sm border-r-2 border-border sticky left-0 z-20">
              <div className="py-3 px-2">
                PERIOD
              </div>
            </TableHead>
            {timeSlots.map((slot, index) => (
              <TableHead
                key={`period-${index}`}
                className={`text-center font-bold text-sm border-r-2 border-border min-w-[120px] ${slot.isBreak
                  ? slot.breakType === 'tea'
                    ? 'bg-gradient-to-r from-info/20 to-info/10 text-info border-r-4 border-info/30'
                    : 'bg-gradient-to-r from-warning/20 to-warning/10 text-warning border-r-4 border-warning/30'
                  : 'bg-gradient-to-r from-primary to-primary/90 text-primary-foreground'
                  }`}
              >
                <div className="py-3 px-2">
                  {slot.label}
                </div>
              </TableHead>
            ))}
          </TableRow>
          <TableRow className="border-b-2 border-border bg-muted/50">
            <TableHead className="min-w-[100px] bg-gradient-to-r from-muted to-muted/80 text-center font-semibold text-foreground/80 text-xs border-r-2 border-border sticky left-0 z-20">
              <div className="py-2">
                <div className="text-xs font-bold">TIME</div>
                <div className="text-xs">DAY</div>
              </div>
            </TableHead>
            {timeSlots.map((slot, index) => (
              <TableHead
                key={`time-${index}`}
                className={`text-center whitespace-nowrap font-semibold text-xs border-r-2 border-border ${slot.isBreak
                  ? slot.breakType === 'tea'
                    ? 'bg-gradient-to-r from-info/10 to-info/5 text-info/80 border-r-4 border-info/20'
                    : 'bg-gradient-to-r from-warning/10 to-warning/5 text-warning/80 border-r-4 border-warning/20'
                  : 'bg-gradient-to-r from-muted to-muted/80 text-foreground/70'
                  }`}
              >
                <div className="py-2 px-1">
                  <div className="text-xs font-medium">{slot.displayTime}</div>
                </div>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {days.map((day, dayIndex) => {
            return (
              <TableRow key={day} className="relative hover:bg-muted/30 transition-all duration-300 border-b border-border/50 group">
                <TableCell className="font-bold bg-gradient-to-r from-muted/80 to-muted/60 text-center text-foreground border-r-2 border-border sticky left-0 z-10 group-hover:bg-muted">
                  <div className="py-4 px-3">
                    <div className="text-sm font-bold tracking-wide">
                      {day.substring(0, 3).toUpperCase()}
                    </div>
                  </div>
                </TableCell>

                {/* Render cells with proper spanning logic */}
                {(() => {
                  const cells: React.ReactNode[] = [];
                  const skippedIndices = new Set<number>(); // Track indices to skip due to spanning

                  timeSlots.forEach((slot, idx) => {
                    // Skip if this cell is covered by a previous spanning lab
                    if (skippedIndices.has(idx)) {
                      return;
                    }

                    // For break cells, ALWAYS render them in their fixed positions
                    if (slot.isBreak) {
                      // CRITICAL: Break cells must NEVER be skipped due to spanning labs
                      // They maintain fixed positions in the grid structure

                      if (dayIndex === 0) { // Only for the first day
                        cells.push(
                          <TableCell
                            key={`${day}-${slot.id}-${idx}`}
                            rowSpan={days.length}
                            className={`${slot.breakType === 'tea' ? 'tea-break-column' : 'break-column'
                              } border-2 ${slot.breakType === 'tea' ? 'border-info/30 bg-gradient-to-b from-info/10 to-info/5' : 'border-warning/30 bg-gradient-to-b from-warning/10 to-warning/5'} p-0 text-center shadow-inner relative overflow-hidden`}
                          >
                            <div className="flex justify-center items-center h-full w-full relative">
                              {/* Background pattern */}
                              <div className={`absolute inset-0 opacity-5 ${slot.breakType === 'tea' ? 'bg-info' : 'bg-warning'}`}>
                                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-current to-transparent"></div>
                              </div>

                              <div className="writing-vertical flex justify-center items-center relative z-10">
                                <span className={`rotate-270 text-xs font-bold tracking-wider ${
                                  slot.breakType === 'tea' ? 'text-info' : 'text-warning'
                                }`}>
                                  {slot.breakType === 'tea' ? 'TEA BREAK' : 'LUNCH BREAK'}
                                </span>
                              </div>

                              {/* Modern decorative elements */}
                              <div className={`absolute top-3 left-1/2 transform -translate-x-1/2 w-2 h-2 rounded-full ${
                                slot.breakType === 'tea' ? 'bg-info/60' : 'bg-warning/60'
                              } animate-pulse`} />
                              <div className={`absolute bottom-3 left-1/2 transform -translate-x-1/2 w-2 h-2 rounded-full ${
                                slot.breakType === 'tea' ? 'bg-info/60' : 'bg-warning/60'
                              } animate-pulse`} style={{ animationDelay: '0.5s' }} />

                              {/* Side accent lines */}
                              <div className={`absolute left-1 top-4 bottom-4 w-0.5 ${
                                slot.breakType === 'tea' ? 'bg-info/30' : 'bg-warning/30'
                              } rounded-full`} />
                              <div className={`absolute right-1 top-4 bottom-4 w-0.5 ${
                                slot.breakType === 'tea' ? 'bg-info/30' : 'bg-warning/30'
                              } rounded-full`} />
                            </div>
                          </TableCell>
                        );
                      }
                      // Skip for other days since the first day's cell spans all rows
                      return;
                    }

                    // Check if this slot has a spanning lab and calculate skipped cells
                    const cellContents = processedTimetable[day]?.[slot.time] || [];
                    const labStarts = cellContents.filter(c => c.isLabStart || c.subject_code === "SKILL LAB");

                    if (labStarts.length > 0) {
                      const labData = labStarts[0];

                      // CRITICAL FIX: Use the processed colSpan (visual span) if available, otherwise calculate it
                      const effectiveColSpan = labData.colSpan || labData.col_span;

                      // Use our LabDurationUtils to get the correct spanning information
                      const labDurationInfo = LabDurationUtils.getLabDurationInfo(
                        labData.time_slot,
                        effectiveColSpan,
                        timeSlots
                      );

                      const actualColspan = labDurationInfo.visualColSpan;

                      console.log(`Lab ${labData.subject_code}: totalPeriods=${labDurationInfo.totalPeriods}, visualColspan=${actualColspan}, is3HourMorningLab=${labDurationInfo.is3HourMorningLab}`);

                      // CRITICAL FIX: For 3-hour morning labs, skip only 1 period (period 2)
                      // For other labs, mark the next (actualColspan-1) period slots as skipped
                      const periodsToSkip = labDurationInfo.is3HourMorningLab ? 1 : (actualColspan - 1);

                      // CRITICAL: NEVER skip break slots - they must always be rendered in their fixed positions
                      let periodsSkipped = 0;
                      for (let i = 1; i < timeSlots.length && periodsSkipped < periodsToSkip; i++) {
                        const skipIndex = idx + i;
                        if (skipIndex < timeSlots.length) {
                          const skipSlot = timeSlots[skipIndex];

                          // Only skip period slots, never break slots
                          if (skipSlot.isPeriod) {
                            skippedIndices.add(skipIndex);
                            periodsSkipped++;
                            console.log(`Skipping period index ${skipIndex} (${skipSlot.time}) due to spanning lab (${periodsSkipped}/${periodsToSkip})`);
                          } else {
                            console.log(`NOT skipping break slot at index ${skipIndex} (${skipSlot.breakType} break) - breaks maintain fixed positions`);
                          }
                        }
                      }

                      if (labDurationInfo.is3HourMorningLab) {
                        console.log(`🌅 3-hour morning lab ${labData.subject_code}: skipped ${periodsSkipped} period(s) to span periods 1-2 only`);
                      }
                    }

                    // Render the cell
                    const cell = renderCell(day, slot.time, idx);
                    if (cell) {
                      cells.push(cell);
                    }
                  });

                  return cells;
                })()}

                {/* REMOVED: Lab span overlays are no longer needed since 3-hour morning labs
                    are properly split with continuation cards instead of spanning across breaks */}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>

      {/* Modern Loading Overlay */}
      {(isLoading || generationProgress > 0) && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-xl">
          <div className="bg-card/95 backdrop-blur-sm p-8 rounded-xl shadow-strong border border-border/50 max-w-md w-full mx-4 animate-scale-in">
            <div className="flex items-center space-x-4 mb-6">
              <div className="relative">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary/30 border-t-primary"></div>
                <div className="absolute inset-0 rounded-full bg-primary/10 animate-pulse"></div>
              </div>
              <div>
                <h3 className="text-xl font-bold text-foreground">Generating Timetable</h3>
                <p className="text-sm text-muted-foreground">Please wait while we process your request</p>
              </div>
            </div>

            {generationStatus && (
              <div className="mb-4 p-3 bg-muted/50 rounded-lg border border-border/30">
                <p className="text-sm text-foreground font-medium">{generationStatus}</p>
              </div>
            )}

            <div className="space-y-3">
              <div className="w-full bg-muted/50 rounded-full h-3 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-primary to-primary/80 h-3 rounded-full transition-all duration-500 ease-out relative overflow-hidden"
                  style={{ width: `${Math.max(generationProgress, isLoading ? 10 : 0)}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                </div>
              </div>

              <div className="flex justify-between items-center text-sm">
                <span className="text-muted-foreground font-medium">Progress</span>
                <span className="text-foreground font-bold">
                  {Math.round(Math.max(generationProgress, isLoading ? 10 : 0))}%
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Configuration Popups */}
      <LabGenerationConfigPopup
        isOpen={showLabConfigPopup}
        onClose={() => setShowLabConfigPopup(false)}
        onConfirm={handleLabConfigConfirm}
        academicYear={academicYear}
        department={department}
        semester={semester}
        section={section}
      />

      <ElectiveConfigurationModal
        isOpen={showElectiveConfigPopup}
        onClose={() => setShowElectiveConfigPopup(false)}
        onConfirm={handleElectiveConfigConfirm}
        academicYear={academicYear}
        department={department}
        semester={semester}
        section={section}
      />

      </div>
    </div>
  );
};
