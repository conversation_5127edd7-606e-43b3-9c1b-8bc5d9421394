
import { supabase } from "@/integrations/supabase/client";
import * as XLSX from 'xlsx';

export interface User {
  id: string;
  email?: string;
  username?: string;
  full_name?: string;
  role?: string;
  department?: string;
  designation?: string;
  phone?: string;
  status?: string;
}

export interface Employee {
  id: string;
  full_name: string;
  email: string;
  department?: string;
  designation?: string;
  phone?: string;
  status?: string;
  roles?: string[]; // Added roles as array to support multiple roles
  created_at?: string;
  updated_at?: string;
}

export class UserService {
  static readonly ALLOWED_ROLES = ['college_admin', 'timetable_coordinator', 'faculty', 'principal', 'hod', 'class_teacher', 'coordinator'];

  static async fetchUsers(): Promise<User[]> {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*');

    if (error) throw error;

    return profiles || [];
  }

  static async fetchEmployees(): Promise<Employee[]> {
    const { data: employees, error } = await supabase
      .from('employee_details')
      .select('*')
      .order('full_name', { ascending: true });

    if (error) throw error;
    return employees || [];
  }

  static async addUser(user: Partial<User>): Promise<User> {
    // First, create the auth user via Supabase Auth API
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: user.email!,
      password: 'temporary-password', // We should implement password reset functionality
      email_confirm: true,
      user_metadata: {
        full_name: user.full_name,
      }
    });

    if (authError) throw authError;

    // Then update the profile with additional fields
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .update({
        full_name: user.full_name,
        username: user.email, // Ensure username is set to email for consistency
        department: user.department,
        designation: user.designation,
        phone: user.phone,
        role: user.role || 'timetable_coordinator', // Default role
      })
      .eq('id', authUser.user.id)
      .select();

    if (profileError) throw profileError;

    return profile[0];
  }

  static async addEmployee(employee: Partial<Employee>): Promise<Employee> {
    // Set default role to 'faculty' if not provided
    const roles = employee.roles || ['faculty'];

    const { data, error } = await supabase
      .from('employee_details')
      .insert({
        full_name: employee.full_name,
        email: employee.email,
        department: employee.department,
        designation: employee.designation,
        phone: employee.phone,
        roles: roles,
      })
      .select();

    if (error) throw error;
    return data[0];
  }

  static async addMultipleEmployees(employees: Partial<Employee>[]): Promise<{ success: Employee[]; failed: Partial<Employee>[] }> {
    const successfulEntries: Employee[] = [];
    const failedEntries: Partial<Employee>[] = [];

    for (const employee of employees) {
      try {
        if (!employee.full_name || !employee.email) {
          console.log('Skipping employee with missing name or email:', employee);
          failedEntries.push(employee);
          continue;
        }

        const addedEmployee = await this.addEmployee(employee);
        successfulEntries.push(addedEmployee);
      } catch (error) {
        console.error(`Error adding employee ${employee.full_name}:`, error);
        failedEntries.push(employee);
      }
    }

    return { success: successfulEntries, failed: failedEntries };
  }

  static async updateUser(user: Partial<User>): Promise<User> {
    const { data, error } = await supabase
      .from('profiles')
      .update({
        full_name: user.full_name,
        department: user.department,
        designation: user.designation,
        phone: user.phone,
        username: user.username,
        role: user.role,
      })
      .eq('id', user.id!)
      .select();

    if (error) throw error;
    return data[0];
  }

  static async updateEmployee(employee: Partial<Employee>): Promise<Employee> {
    const { data, error } = await supabase
      .from('employee_details')
      .update({
        full_name: employee.full_name,
        email: employee.email,
        department: employee.department,
        designation: employee.designation,
        phone: employee.phone,
        roles: employee.roles || ['faculty'],
      })
      .eq('id', employee.id!)
      .select();

    if (error) throw error;
    return data[0];
  }

  static async deleteUser(userId: string): Promise<void> {
    // Delete the auth user (cascade will delete profile)
    const { error } = await supabase.auth.admin.deleteUser(userId);
    if (error) throw error;
  }

  static async deleteEmployee(employeeId: string): Promise<void> {
    const { error } = await supabase
      .from('employee_details')
      .delete()
      .eq('id', employeeId);

    if (error) throw error;
  }

  static async parseExcelFile(file: File): Promise<Partial<Employee>[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = e.target?.result;
          const workbook = XLSX.read(data, { type: 'binary' });
          const sheet = workbook.Sheets[workbook.SheetNames[0]];
          const rows = XLSX.utils.sheet_to_json<any>(sheet, { defval: null });

          console.log("Raw Excel data:", rows);

          // Map Excel columns to our Employee interface
          const employees = rows.map(row => {
            // Check for different possible column names for full name
            const fullName = row['Full Name'] || row['full name'] || row['FullName'] || row['fullname'] || row['Name'] || row['name'] || '';

            return {
              full_name: fullName.trim(),
              designation: row['Designation'] || row['designation'] || '',
              department: row['Department'] || row['department'] || '',
              email: row['Email Id'] || row['EmailId'] || row['Email'] || row['email'] || '',
              phone: row['Phone'] || row['phone'] || row['Phone Number'] || row['PhoneNumber'] || '',
              roles: ['faculty'], // Default role
            };
          });

          // Filter out entries with missing name or email
          const validEmployees = employees.filter(emp => emp.full_name && emp.email);
          if (validEmployees.length === 0) {
            console.warn("No valid employees found in Excel file");
          } else if (validEmployees.length < employees.length) {
            console.warn(`Filtered out ${employees.length - validEmployees.length} entries with missing name or email`);
          }

          resolve(validEmployees);
        } catch (error) {
          console.error("Error parsing Excel file:", error);
          reject(error);
        }
      };

      reader.onerror = (error) => {
        reject(error);
      };

      reader.readAsBinaryString(file);
    });
  }

  static async updateEmployeeRoles(employeeId: string, roles: string[]): Promise<Employee> {
    // Validate roles against allowed roles
    const validRoles = roles.filter(role => this.ALLOWED_ROLES.includes(role));

    const { data, error } = await supabase
      .from('employee_details')
      .update({ roles: validRoles })
      .eq('id', employeeId)
      .select();

    if (error) throw error;
    return data[0];
  }
}
