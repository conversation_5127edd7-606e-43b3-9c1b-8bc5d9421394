import { supabase } from "@/integrations/supabase/client";
import { AffectedClass, SubstituteFaculty, ClassImpactAnalysis } from './LeaveManagementService';
import { FacultyQualificationService } from './FacultyQualificationService';

export class ClassSubstitutionService {
  /**
   * Analyze class impact for given leave dates and faculty
   */
  static async analyzeClassImpact(
    facultyId: string,
    leaveDates: string[],
    academicYear: string = '2024-25'
  ): Promise<ClassImpactAnalysis> {
    try {
      console.log('🔍 Analyzing class impact for faculty:', facultyId, 'dates:', leaveDates);

      const affectedClasses: AffectedClass[] = [];
      const departmentsAffected = new Set<string>();
      const semestersAffected = new Set<string>();
      let theoryCount = 0, labCount = 0, tutorialCount = 0;

      // Convert dates to day names for timetable lookup
      for (const dateStr of leaveDates) {
        const date = new Date(dateStr + 'T00:00:00'); // Ensure proper date parsing
        const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });

        console.log(`📅 Processing date: ${dateStr} (${dayName})`);

        // First, let's debug what's in the timetable_slots table for this faculty
        console.log(`🔍 DEBUG: Searching for faculty_id: ${facultyId} in timetable_slots`);

        // Query all timetable slots for this faculty to see what data exists
        const { data: allSlots, error: debugError } = await supabase
          .from('timetable_slots')
          .select('id, day, time_slot, subject_code, faculty_id, academic_year')
          .eq('faculty_id', facultyId)
          .limit(10);

        if (debugError) {
          console.error('Debug query error:', debugError);
        } else {
          console.log(`🔍 DEBUG: Found ${allSlots?.length || 0} total slots for faculty:`, allSlots);
          // Log the specific day we're looking for
          const fridaySlots = allSlots?.filter(slot => slot.day === dayName);
          console.log(`🔍 DEBUG: Slots for ${dayName}:`, fridaySlots);
        }

        // Query timetable_slots for this faculty and day - Remove academic_year filter for now
        console.log(`🔍 DEBUG: Querying for faculty_id: ${facultyId}, day: ${dayName}`);
        let { data: timetableSlots, error } = await supabase
          .from('timetable_slots')
          .select(`
            id,
            day,
            time_slot,
            subject_code,
            subject_name,
            subject_type,
            semester,
            section,
            department,
            room_number,
            batch_name,
            academic_year
          `)
          .eq('faculty_id', facultyId)
          .eq('day', dayName)
          .order('time_slot');

        if (error) {
          console.error('Error fetching timetable slots:', error);
          continue;
        }

        console.log(`📋 Query result for ${dayName}: Found ${timetableSlots?.length || 0} classes`);
        console.log(`📋 Raw query result:`, timetableSlots);

        // If no classes found, let's debug further
        if (!timetableSlots || timetableSlots.length === 0) {
          console.log(`🔄 No classes found for '${dayName}', debugging further...`);

          // Check if it's an academic year issue
          const { data: anyDaySlots, error: anyDayError } = await supabase
            .from('timetable_slots')
            .select('academic_year, day')
            .eq('faculty_id', facultyId)
            .eq('day', dayName);

          console.log(`🔍 DEBUG: All ${dayName} slots regardless of academic year:`, anyDaySlots);

          if (anyDaySlots && anyDaySlots.length > 0) {
            // Found slots but with different academic year, let's use them
            console.log(`✅ Found slots with different academic year, fetching full data...`);
            const { data: fullSlots, error: fullError } = await supabase
              .from('timetable_slots')
              .select(`
                id, day, time_slot, subject_code, subject_name, subject_type,
                semester, section, department, room_number, batch_name, academic_year
              `)
              .eq('faculty_id', facultyId)
              .eq('day', dayName)
              .order('time_slot');

            if (!fullError && fullSlots) {
              console.log(`✅ Using slots from any academic year: ${fullSlots.length} classes`);
              // Ensure timetableSlots is an array before assigning
              timetableSlots = fullSlots || [];
            }
          }
        }

        if (timetableSlots && timetableSlots.length > 0) {
          console.log(`📋 Found ${timetableSlots.length} classes for ${dayName}:`, timetableSlots);

          for (const slot of timetableSlots) {
            // Extract period number from time_slot (e.g., "Period 1" -> 1)
            const periodMatch = slot.time_slot.match(/Period (\d+)/);
            const periodNumber = periodMatch ? parseInt(periodMatch[1]) : 0;

            // Log the original slot data to debug
            console.log('📊 Original timetable slot data:', {
              id: slot.id,
              subject_code: slot.subject_code,
              subject_name: slot.subject_name,
              subject_type: slot.subject_type,
              semester: slot.semester,
              section: slot.section,
              day: dayName,
              time_slot: slot.time_slot
            });

            const affectedClass: AffectedClass = {
              id: `${slot.id}-${dateStr}`,
              day: dayName,
              time_slot: slot.time_slot || 'Time TBD',
              period_number: periodNumber,
              // Preserve original subject codes - don't replace with placeholders yet
              subject_code: slot.subject_code || '',
              subject_name: slot.subject_name || '',
              subject_type: slot.subject_type || 'theory',
              semester: slot.semester || '',
              section: slot.section || '',
              room_number: slot.room_number || undefined,
              batch_name: slot.batch_name || undefined,
              substitution_status: 'pending'
            };

            affectedClasses.push(affectedClass);
            departmentsAffected.add(slot.department);
            semestersAffected.add(slot.semester);

            // Count by type
            switch (slot.subject_type?.toLowerCase()) {
              case 'theory':
                theoryCount++;
                break;
              case 'lab':
                labCount++;
                break;
              case 'tutorial':
                tutorialCount++;
                break;
            }
          }
        }
      }

      const impactAnalysis: ClassImpactAnalysis = {
        leave_dates: leaveDates,
        total_classes_affected: affectedClasses.length,
        affected_classes: affectedClasses,
        impact_summary: {
          theory_classes: theoryCount,
          lab_classes: labCount,
          tutorial_classes: tutorialCount,
          departments_affected: Array.from(departmentsAffected),
          semesters_affected: Array.from(semestersAffected)
        }
      };

      console.log('📊 Class impact analysis completed:', impactAnalysis.impact_summary);
      return impactAnalysis;

    } catch (error) {
      console.error('Error analyzing class impact:', error);
      throw error;
    }
  }

  /**
   * Get available substitute faculty for a specific time slot and date - ENHANCED FOR SMART FILTERING
   */
  static async getAvailableSubstitutes(
    department: string,
    day: string,
    timeSlot: string,
    date: string,
    academicYear: string = '2024-25',
    affectedClass?: AffectedClass,
    showAllFaculties: boolean = false
  ): Promise<SubstituteFaculty[]> {
    try {
      console.log('🔍 Finding substitutes for:', {
        department,
        day,
        timeSlot,
        date,
        showAllFaculties,
        affectedClass: affectedClass ? {
          semester: affectedClass.semester,
          section: affectedClass.section,
          subject_type: affectedClass.subject_type
        } : 'NOT PROVIDED'
      });

      // ENHANCED: Detect if this is a lab class requiring special handling
      const isLabClass = this.isLabClass(affectedClass);
      console.log(`🧪 Lab class detection: ${isLabClass ? 'YES' : 'NO'}`, affectedClass?.subject_type);

      // SMART FILTERING: If not showing all faculties and we have class details, get qualified faculties first
      if (!showAllFaculties && affectedClass) {
        console.log('🎯 SMART FILTERING: Finding qualified faculties for class:', {
          semester: affectedClass.semester,
          section: affectedClass.section,
          subject_type: affectedClass.subject_type,
          subject_code: affectedClass.subject_code,
          department: department
        });

        const qualifiedFaculties = await FacultyQualificationService.getQualifiedFaculties(
          affectedClass,
          department,
          academicYear
        );

        if (qualifiedFaculties.length > 0) {
          console.log(`✅ Found ${qualifiedFaculties.length} qualified faculties:`,
            qualifiedFaculties.map(f => ({ name: f.full_name, reason: f.qualification_reason }))
          );

          const qualifiedSubstitutes = await FacultyQualificationService.convertToSubstituteFaculties(
            qualifiedFaculties,
            day,
            timeSlot,
            date,
            academicYear
          );

          // Sort qualified substitutes by availability and qualification
          qualifiedSubstitutes.sort((a, b) => {
            // First priority: Availability status
            const statusOrder = { 'available': 0, 'occupied': 1, 'on_leave': 2 };
            const aStatusOrder = statusOrder[a.availability_status];
            const bStatusOrder = statusOrder[b.availability_status];

            if (aStatusOrder !== bStatusOrder) return aStatusOrder - bStatusOrder;

            // Second priority: Alphabetical by name
            return a.full_name.localeCompare(b.full_name);
          });

          console.log(`🎯 SMART FILTERING RESULT: Returning ${qualifiedSubstitutes.length} qualified faculties with availability:`,
            qualifiedSubstitutes.map(s => ({ name: s.full_name, status: s.availability_status, details: s.conflict_details }))
          );
          return qualifiedSubstitutes;
        } else {
          console.log('⚠️ No qualified faculties found, falling back to all faculties');
        }
      } else {
        console.log('🔄 SMART FILTERING DISABLED:', {
          reason: showAllFaculties ? 'Show All Faculties is enabled' : 'No affected class provided',
          showAllFaculties: showAllFaculties,
          hasAffectedClass: !!affectedClass
        });
      }

      // FALLBACK OR SHOW ALL: Run queries in parallel for all faculties (original logic)
      console.log('📋 PROCEEDING TO FALLBACK/SHOW ALL LOGIC');
      const queryPromises = [
        // Get all faculty from the same department with their availability data
        supabase
          .from('employee_details')
          .select('id, full_name, department, designation, roles, vacant_count_by_day')
          .eq('department', department)
          .contains('roles', ['faculty']),

        // Get timetable conflicts for this specific day/time slot
        supabase
          .from('timetable_slots')
          .select('faculty_id, faculty2_id, subject_code, subject_name, semester, section, room_number, subject_type, batch_name')
          .eq('day', day)
          .eq('time_slot', timeSlot)
          .eq('academic_year', academicYear),

        // Get all approved leave requests for this date
        supabase
          .from('leave_requests')
          .select('faculty_id')
          .eq('status', 'approved')
          .lte('start_date', date)
          .gte('end_date', date)
      ];

      // ENHANCED: For lab classes, add query to get the secondary faculty (broader approach)
      if (isLabClass && affectedClass) {
        console.log(`🔍 LAB QUERY PARAMS:`, {
          subject_code: affectedClass.subject_code,
          semester: affectedClass.semester,
          section: affectedClass.section,
          day: day,
          time_slot: timeSlot,
          academic_year: academicYear
        });

        // Try multiple queries with decreasing specificity
        queryPromises.push(
          // Query 1: Try exact match first
          supabase
            .from('timetable_slots')
            .select('faculty_id, faculty2_id, subject_code, subject_name, batch_name')
            .eq('subject_code', affectedClass.subject_code)
            .eq('semester', affectedClass.semester)
            .eq('section', affectedClass.section)
            .eq('academic_year', academicYear)
            .not('faculty2_id', 'is', null)
            .limit(5) // Get multiple results to find the right one
        );
      }

      const results = await Promise.all(queryPromises);
      const [facultyResult, timetableResult, leaveResult, labSpecificResult] = results;

      if (facultyResult.error) throw facultyResult.error;
      if (timetableResult.error) console.warn('Timetable query error:', timetableResult.error);
      if (leaveResult.error) console.warn('Leave query error:', leaveResult.error);

      const facultyList = facultyResult.data || [];
      const timetableConflicts = timetableResult.data || [];
      const facultyOnLeave = new Set((leaveResult.data || []).map(leave => leave.faculty_id));

      // ENHANCED: Extract lab-specific secondary faculty information
      let labSecondaryFacultyId: string | null = null;
      let labSecondaryFacultyInfo: any = null;

      if (isLabClass && labSpecificResult) {
        console.log(`🔍 LAB QUERY DEBUG:`, {
          hasError: !!labSpecificResult.error,
          error: labSpecificResult.error,
          hasData: !!labSpecificResult.data,
          dataType: Array.isArray(labSpecificResult.data) ? 'array' : typeof labSpecificResult.data,
          dataLength: Array.isArray(labSpecificResult.data) ? labSpecificResult.data.length : 'N/A',
          rawData: labSpecificResult.data
        });

        if (!labSpecificResult.error && labSpecificResult.data) {
          // Handle array result (we're getting multiple results now)
          const labDataArray = Array.isArray(labSpecificResult.data) ? labSpecificResult.data : [labSpecificResult.data];
          console.log(`🔍 LAB QUERY RESULTS: Found ${labDataArray.length} lab slots for ${affectedClass.subject_code}`);

          // Look for any lab slot with secondary faculty
          for (const labData of labDataArray) {
            if (labData && labData.faculty2_id) {
              labSecondaryFacultyId = labData.faculty2_id;
              console.log(`🧪 LAB SECONDARY FACULTY FOUND: ${labSecondaryFacultyId}`);
              console.log(`🧪 LAB DATA:`, labData);

              // Get faculty details from the main faculty list
              const facultyDetails = facultyList.find(f => f.id === labSecondaryFacultyId);
              if (facultyDetails) {
                labSecondaryFacultyInfo = {
                  id: facultyDetails.id,
                  full_name: facultyDetails.full_name,
                  designation: facultyDetails.designation,
                  department: facultyDetails.department
                };
                console.log(`🧪 LAB SECONDARY INFO:`, labSecondaryFacultyInfo);
                break; // Found secondary faculty, stop looking
              }
            }
          }

          if (!labSecondaryFacultyId) {
            console.log(`❌ NO SECONDARY FACULTY FOUND in ${labDataArray.length} lab slots`);
          }
        } else {
          console.log(`❌ LAB QUERY FAILED:`, labSpecificResult.error);
        }
      } else {
        console.log(`🔍 LAB QUERY SKIPPED: isLabClass=${isLabClass}, hasResult=${!!labSpecificResult}`);
      }

      // FALLBACK: If lab-specific query didn't work, try to find secondary faculty from general timetable conflicts
      if (isLabClass && !labSecondaryFacultyId && affectedClass) {
        console.log(`🔍 FALLBACK: Looking for secondary faculty in general timetable conflicts...`);
        console.log(`🔍 Available conflicts:`, timetableConflicts.map(c => ({
          subject_code: c.subject_code,
          faculty_id: c.faculty_id,
          faculty2_id: c.faculty2_id,
          semester: c.semester,
          section: c.section
        })));

        const labConflict = timetableConflicts.find(conflict =>
          conflict.subject_code === affectedClass.subject_code &&
          conflict.faculty2_id // Has a secondary faculty
        );

        if (labConflict && labConflict.faculty2_id) {
          labSecondaryFacultyId = labConflict.faculty2_id;
          console.log(`🧪 FALLBACK SUCCESS: Found secondary faculty ${labSecondaryFacultyId} from general conflicts`);

          // Try to get faculty details from the faculty list
          const facultyDetails = facultyList.find(f => f.id === labSecondaryFacultyId);
          if (facultyDetails) {
            labSecondaryFacultyInfo = {
              id: facultyDetails.id,
              full_name: facultyDetails.full_name,
              designation: facultyDetails.designation,
              department: facultyDetails.department
            };
            console.log(`🧪 FALLBACK: Got faculty details from faculty list`, labSecondaryFacultyInfo);
          }
        } else {
          console.log(`❌ FALLBACK FAILED: No secondary faculty found in general conflicts`);
          console.log(`🔍 Looking for subject code: ${affectedClass.subject_code}`);
          console.log(`🔍 Available subject codes:`, [...new Set(timetableConflicts.map(c => c.subject_code))]);

          // FINAL FALLBACK: Try the broadest query - any lab with this subject code
          console.log(`🔍 FINAL FALLBACK: Trying very broad query for subject ${affectedClass.subject_code}...`);
          try {
            const broadQuery = await supabase
              .from('timetable_slots')
              .select('faculty_id, faculty2_id, subject_code, subject_name, semester, section')
              .eq('subject_code', affectedClass.subject_code)
              .not('faculty2_id', 'is', null)
              .limit(10); // Get multiple results

            console.log(`🔍 BROAD QUERY RESULTS:`, broadQuery.data);

            if (broadQuery.data && broadQuery.data.length > 0) {
              // Try to find one that matches semester and section first
              let bestMatch = broadQuery.data.find(d =>
                d.semester === affectedClass.semester && d.section === affectedClass.section
              );

              // If no exact match, take the first one
              if (!bestMatch) {
                bestMatch = broadQuery.data[0];
                console.log(`🔍 NO EXACT MATCH: Using first available secondary faculty`);
              }

              labSecondaryFacultyId = bestMatch.faculty2_id;
              console.log(`🧪 FINAL FALLBACK SUCCESS: Found secondary faculty ${labSecondaryFacultyId} via broad query`);

              // Get faculty details from the main faculty list
              const facultyDetails = facultyList.find(f => f.id === labSecondaryFacultyId);
              if (facultyDetails) {
                labSecondaryFacultyInfo = {
                  id: facultyDetails.id,
                  full_name: facultyDetails.full_name,
                  designation: facultyDetails.designation,
                  department: facultyDetails.department
                };
                console.log(`🧪 FINAL FALLBACK: Got faculty details`, labSecondaryFacultyInfo);
              }
            } else {
              console.log(`❌ FINAL FALLBACK FAILED: No secondary faculty found for subject ${affectedClass.subject_code}`);
            }
          } catch (error) {
            console.log(`❌ FINAL FALLBACK ERROR:`, error);
          }
        }
      }

      console.log(`👥 Found ${facultyList.length} faculty, ${timetableConflicts.length} conflicts, ${facultyOnLeave.size} on leave`);

      // Create conflict lookup for faster checking
      const conflictMap = new Map();
      timetableConflicts.forEach(conflict => {
        if (conflict.faculty_id) conflictMap.set(conflict.faculty_id, conflict);
        if (conflict.faculty2_id) conflictMap.set(conflict.faculty2_id, conflict);
      });

      const substitutes: SubstituteFaculty[] = [];

      // ENHANCED: Process all faculty with lab role detection and prioritization
      for (const faculty of facultyList) {
        let availabilityStatus: 'available' | 'occupied' | 'on_leave' = 'occupied';
        let conflictDetails = '';

        // ENHANCED: Determine lab role and priority for this faculty
        const isSecondaryFaculty = isLabClass && faculty.id === labSecondaryFacultyId;
        const labRole = isSecondaryFaculty ? 'secondary' : 'none';
        const priorityLevel = isSecondaryFaculty ? 'high' : 'medium';
        const roleDescription = isSecondaryFaculty ? 'Current Lab Assistant - Already Assigned' : undefined;

        // Step 1: Check if faculty is on approved leave (using pre-fetched data)
        if (facultyOnLeave.has(faculty.id)) {
          availabilityStatus = 'on_leave';
          conflictDetails = 'Faculty is on approved leave for this date';
        } else {
          // Step 2: Check faculty's configured availability schedule
          // FIXED: Handle different data types and null values safely
          let facultyAvailableSlots = [];

          if (faculty.vacant_count_by_day && typeof faculty.vacant_count_by_day === 'object') {
            const daySlots = faculty.vacant_count_by_day[day];
            if (Array.isArray(daySlots)) {
              facultyAvailableSlots = daySlots;
            } else if (typeof daySlots === 'string') {
              // Handle case where it might be a string
              facultyAvailableSlots = [daySlots];
            }
          }

          // DEBUG: Log availability data for first few faculty
          if (substitutes.length < 3) {
            console.log(`🔍 DEBUG ${faculty.full_name}:`);
            console.log(`   vacant_count_by_day:`, faculty.vacant_count_by_day);
            console.log(`   Available slots for ${day}:`, facultyAvailableSlots);
            console.log(`   Looking for time slot: "${timeSlot}"`);
            console.log(`   Slots include target:`, facultyAvailableSlots.map(slot => `"${slot}"`));
          }

          // ENHANCED: Handle multi-period lab slots (e.g., "10:35-12:25" spans two 1-hour periods)
          const isScheduledAvailable = this.checkTimeSlotAvailability(facultyAvailableSlots, timeSlot);

          if (!isScheduledAvailable) {
            availabilityStatus = 'occupied';
            conflictDetails = `Not scheduled to be available during ${timeSlot} on ${day}`;
          } else {
            // Step 3: Check for timetable conflicts (using pre-fetched data)
            const conflict = conflictMap.get(faculty.id);

            if (conflict) {
              availabilityStatus = 'occupied';
              const facultyRole = conflict.faculty2_id === faculty.id ? 'Assistant Faculty' : 'Primary Faculty';
              conflictDetails = `${facultyRole} - Teaching ${conflict.subject_code} to ${conflict.semester}-${conflict.section} in ${conflict.room_number || 'N/A'}`;
            } else {
              // Faculty is both scheduled to be available AND has no class conflicts
              availabilityStatus = 'available';
              conflictDetails = '';
            }
          }
        }

        substitutes.push({
          id: faculty.id,
          full_name: faculty.full_name,
          department: faculty.department || department,
          designation: faculty.designation || 'Faculty',
          availability_status: availabilityStatus,
          conflict_details: conflictDetails,
          // ENHANCED: Lab-specific fields
          is_secondary_faculty: isSecondaryFaculty,
          lab_role: labRole,
          priority_level: priorityLevel,
          role_description: roleDescription
        });
      }

      // ENHANCED: For lab classes, show ONLY the specific secondary faculty assigned to this lab
      let finalSubstitutes = substitutes;

      if (isLabClass && labSecondaryFacultyId && labSecondaryFacultyInfo) {
        // Create the specific secondary faculty substitute entry
        const specificSecondaryFaculty = {
          id: labSecondaryFacultyId,
          full_name: labSecondaryFacultyInfo.full_name || 'Lab Faculty',
          department: labSecondaryFacultyInfo.department || department,
          designation: labSecondaryFacultyInfo.designation || 'Faculty',
          availability_status: 'available' as const, // Assume available for now
          conflict_details: '',
          is_secondary_faculty: true,
          lab_role: 'secondary' as const,
          priority_level: 'high' as const,
          role_description: 'Assigned Faculty for this Lab Session'
        };

        finalSubstitutes = [specificSecondaryFaculty];
        console.log(`🧪 LAB SPECIFIC: Showing ONLY the assigned lab faculty - ${specificSecondaryFaculty.full_name}`);
      } else if (isLabClass && labSecondaryFacultyId) {
        // Fallback: Find secondary faculty in the general faculty list
        const secondaryFaculty = substitutes.find(s => s.is_secondary_faculty);

        if (secondaryFaculty) {
          finalSubstitutes = [secondaryFaculty];
          console.log(`🧪 LAB FALLBACK: Showing secondary faculty from general list - ${secondaryFaculty.full_name}`);
        } else {
          console.log(`❌ LAB ERROR: Secondary faculty ${labSecondaryFacultyId} not found in department faculty list`);
          finalSubstitutes = [];
        }
      } else if (isLabClass && !labSecondaryFacultyId) {
        console.log(`❌ LAB ERROR: No secondary faculty found for this lab class`);
        console.log(`🚫 LAB POLICY: For lab classes, only the assigned lab assistant should be shown`);

        // For lab classes, if we can't find the specific secondary faculty, show empty list
        // This forces the user to contact admin to fix the lab assignment data
        finalSubstitutes = [];
        console.log(`🚫 LAB STRICT: Showing 0 faculty - lab assistant must be properly assigned in database`);
      } else {
        // For theory classes or labs without secondary faculty, show all with standard sorting
        finalSubstitutes.sort((a, b) => {
          // First priority: Availability status
          const statusOrder = { 'available': 0, 'occupied': 1, 'on_leave': 2 };
          const aStatusOrder = statusOrder[a.availability_status];
          const bStatusOrder = statusOrder[b.availability_status];

          if (aStatusOrder !== bStatusOrder) return aStatusOrder - bStatusOrder;

          // Second priority: Lab role (secondary faculty first for lab classes)
          if (isLabClass) {
            const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
            const aPriorityOrder = priorityOrder[a.priority_level || 'medium'];
            const bPriorityOrder = priorityOrder[b.priority_level || 'medium'];

            if (aPriorityOrder !== bPriorityOrder) return aPriorityOrder - bPriorityOrder;
          }

          // Final priority: Alphabetical by name
          return a.full_name.localeCompare(b.full_name);
        });
      }

      // OPTIMIZED: Calculate counts for the final filtered list
      let availableCount = 0, occupiedCount = 0, onLeaveCount = 0;
      finalSubstitutes.forEach(s => {
        if (s.availability_status === 'available') availableCount++;
        else if (s.availability_status === 'occupied') occupiedCount++;
        else onLeaveCount++;
      });

      const resultType = isLabClass && labSecondaryFacultyId && labSecondaryFacultyInfo ? 'FOCUSED LAB (SPECIFIC ASSISTANT)' :
                        isLabClass && labSecondaryFacultyId ? 'FOCUSED LAB (ASSISTANT FROM FALLBACK)' :
                        isLabClass && !labSecondaryFacultyId ? 'LAB (MANUAL SELECTION)' :
                        showAllFaculties ? 'SHOW ALL FACULTIES' : 'FALLBACK TO ALL FACULTIES';
      console.log(`📋 ${resultType} RESULT: ${finalSubstitutes.length} faculty shown - ${availableCount} available, ${occupiedCount} occupied, ${onLeaveCount} on leave`);

      if (isLabClass) {
        console.log(`🧪 LAB DEBUG SUMMARY:`);
        console.log(`   - Lab class detected: YES`);
        console.log(`   - Secondary faculty ID: ${labSecondaryFacultyId || 'NOT FOUND'}`);
        console.log(`   - Secondary faculty info: ${labSecondaryFacultyInfo ? 'FOUND' : 'NOT FOUND'}`);
        console.log(`   - Total faculty in department: ${substitutes.length}`);
        console.log(`   - Faculty shown to user: ${finalSubstitutes.length}`);

        if (labSecondaryFacultyInfo) {
          console.log(`   - Specific lab faculty: ${labSecondaryFacultyInfo.full_name}`);
        } else if (finalSubstitutes.length > 0) {
          console.log(`   - Fallback: Showing available faculty for manual selection`);
        } else {
          console.log(`   - Issue: No faculty available or found`);
        }
      }

      return finalSubstitutes;

    } catch (error) {
      console.error('Error getting available substitutes:', error);
      throw error;
    }
  }

  /**
   * Check if a faculty is on leave for a specific date
   */
  static async checkFacultyLeaveStatus(
    facultyId: string,
    date: string
  ): Promise<boolean> {
    try {
      const { data: leaveRequests, error } = await supabase
        .from('leave_requests')
        .select('start_date, end_date')
        .eq('faculty_id', facultyId)
        .eq('status', 'approved')
        .lte('start_date', date)
        .gte('end_date', date);

      if (error) throw error;

      return (leaveRequests && leaveRequests.length > 0);
    } catch (error) {
      console.error('Error checking faculty leave status:', error);
      return false;
    }
  }

  /**
   * Save substitution assignments
   */
  static async saveSubstitutionAssignments(
    leaveRequestId: string,
    substitutions: {
      class_id: string;
      substitute_faculty_id: string;
      notes?: string;
    }[]
  ): Promise<void> {
    try {
      console.log('💾 Saving substitution assignments for leave request:', leaveRequestId);

      // First, get the current leave request to access existing affected_classes
      const { data: currentRequest, error: fetchError } = await supabase
        .from('leave_requests')
        .select('affected_classes')
        .eq('id', leaveRequestId)
        .single();

      if (fetchError) throw fetchError;

      let updatedAffectedClasses = currentRequest?.affected_classes || [];

      // Get substitute faculty details for each assignment
      const substituteIds = substitutions.map(sub => sub.substitute_faculty_id);
      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details')
        .select('id, full_name, department, designation')
        .in('id', substituteIds);

      if (facultyError) {
        console.error('Error fetching substitute faculty data:', facultyError);
      }

      // Create a map for quick lookup
      const facultyMap = new Map();
      if (facultyData) {
        facultyData.forEach(faculty => {
          facultyMap.set(faculty.id, faculty);
        });
      }

      // Merge substitution assignments with existing affected classes
      if (Array.isArray(updatedAffectedClasses)) {
        updatedAffectedClasses = updatedAffectedClasses.map(affectedClass => {
          const substitution = substitutions.find(sub => sub.class_id === affectedClass.id);
          if (substitution) {
            const substituteFaculty = facultyMap.get(substitution.substitute_faculty_id);
            return {
              ...affectedClass,
              substitute_faculty_id: substitution.substitute_faculty_id,
              substitute_faculty_name: substituteFaculty?.full_name || 'Unknown Faculty',
              substitution_notes: substitution.notes || '',
              substitution_status: 'assigned'
            };
          }
          return affectedClass;
        });
      }

      // Update the leave request with merged data
      const { error } = await supabase
        .from('leave_requests')
        .update({
          affected_classes: updatedAffectedClasses,
          substitution_status: 'assigned',
          updated_at: new Date().toISOString()
        })
        .eq('id', leaveRequestId);

      if (error) throw error;

      console.log('✅ Substitution assignments saved and merged successfully');
    } catch (error) {
      console.error('Error saving substitution assignments:', error);
      throw error;
    }
  }

  /**
   * ENHANCED: Check if faculty is available for a time slot (handles multi-period slots)
   */
  private static checkTimeSlotAvailability(facultyAvailableSlots: string[], requestedTimeSlot: string): boolean {
    // First, try exact match
    if (facultyAvailableSlots.includes(requestedTimeSlot)) {
      return true;
    }

    // For multi-period slots (e.g., "10:35-12:25"), check if all constituent periods are available
    const [startTime, endTime] = requestedTimeSlot.split('-');

    // Common lab slot patterns
    const multiPeriodPatterns = {
      '10:35-12:25': ['10:35-11:30', '11:30-12:25'],
      '08:30-10:20': ['08:30-09:25', '09:25-10:20'],
      '13:15-15:05': ['13:15-14:10', '14:10-15:05'],
      '14:10-16:00': ['14:10-15:05', '15:05-16:00'],
      '15:05-16:55': ['15:05-16:00', '16:00-16:55']
    };

    // Check if this is a known multi-period pattern
    const constituentSlots = multiPeriodPatterns[requestedTimeSlot];
    if (constituentSlots) {
      const allSlotsAvailable = constituentSlots.every(slot => facultyAvailableSlots.includes(slot));
      console.log(`   🔍 Multi-period check for ${requestedTimeSlot}: ${constituentSlots.join(' + ')} = ${allSlotsAvailable ? 'AVAILABLE' : 'NOT AVAILABLE'}`);
      return allSlotsAvailable;
    }

    // Fallback: exact match only
    return false;
  }

  /**
   * ENHANCED: Detect if the affected class is a lab class
   */
  private static isLabClass(affectedClass?: AffectedClass): boolean {
    if (!affectedClass) return false;

    // Check subject_type field
    if (affectedClass.subject_type === 'lab' ||
        affectedClass.subject_type === 'laboratory') {
      return true;
    }

    // Check subject_code patterns (e.g., "CS301L", "BCS601L")
    if (affectedClass.subject_code &&
        affectedClass.subject_code.toUpperCase().endsWith('L')) {
      return true;
    }

    // Check subject_name for lab indicators
    if (affectedClass.subject_name &&
        affectedClass.subject_name.toUpperCase().includes('LAB')) {
      return true;
    }

    // Check if batch_name exists (labs typically have batches)
    if (affectedClass.batch_name &&
        affectedClass.batch_name.trim() !== '' &&
        affectedClass.batch_name !== 'null') {
      return true;
    }

    return false;
  }
}
