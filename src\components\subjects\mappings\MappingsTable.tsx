
import React from "react";
import { MappingType } from "@/stores/SubjectMappingStore";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import EmptyMappingsCard from "./EmptyMappingsCard";
import DeleteMappingDialog from "./DeleteMappingDialog";
import FacultyEditCell from "./FacultyEditCell";
import { useFacultyLoader } from "../useFacultyLoader";
import { useMappingActions } from "./useMappingActions";

interface MappingsTableProps {
  mappings: MappingType[];
  onMappingDeleted: () => void;
  onDelete?: (id: string) => Promise<void>; // Optional direct delete handler
}

const MappingsTable: React.FC<MappingsTableProps> = ({
  mappings = [], // Default to empty array if undefined
  onMappingDeleted,
  onDelete,
}) => {
  // Load faculty list for autocomplete - ensure it's always an array
  const facultyList = useFacultyLoader() || [];

  // Use our custom hook for mapping actions
  const { handleUpdateFaculty, handleUpdateFaculty2, handleDelete } = useMappingActions({
    onMappingChanged: onMappingDeleted
  });

  // Safely check if mappings is undefined or empty
  if (!mappings || mappings.length === 0) {
    return <EmptyMappingsCard />;
  }

  return (
    <div className="rounded-md border overflow-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Code</TableHead>
            <TableHead>Short ID</TableHead>
            <TableHead>Subject</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Faculty</TableHead>
            <TableHead>Hours</TableHead>
            <TableHead>Room</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {mappings.map((mapping) => (
            <TableRow key={mapping.id}>
              <TableCell className="font-mono">{mapping.subject?.code || 'N/A'}</TableCell>
              <TableCell className="font-mono font-medium">
                {mapping.subject?.shortId || 'N/A'}
              </TableCell>
              <TableCell>{mapping.subject?.name || 'Unknown Subject'}</TableCell>
              <TableCell className="capitalize">
                {mapping.subject?.type || 'unknown'}
              </TableCell>
              <TableCell>
                <FacultyEditCell
                  faculty={mapping.faculty}
                  faculty2Id={mapping.faculty2Id}
                  faculty2Name={mapping.faculty2Name}
                  isLabSubject={mapping.subject?.type === 'lab'}
                  facultyList={facultyList}
                  onSave={(faculty) => handleUpdateFaculty(mapping.id, faculty)}
                  onSaveFaculty2={(faculty2Id) => handleUpdateFaculty2(mapping.id, faculty2Id)}
                />
              </TableCell>
              <TableCell>{mapping.hoursPerWeek || 0}</TableCell>
              <TableCell>{mapping.classroom || 'N/A'}</TableCell>
              <TableCell>
                <DeleteMappingDialog
                  onDelete={async () => {
                    // Use the direct delete handler if provided, otherwise use the one from useMappingActions
                    if (onDelete) {
                      await onDelete(mapping.id);
                    } else {
                      await handleDelete(mapping.id);
                    }
                  }}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

export default MappingsTable;
