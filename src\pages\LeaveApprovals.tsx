import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useUserRole } from '@/hooks/useUserRole';
import { 
  CheckCircle, 
  XCircle, 
  Calendar, 
  User, 
  Building2,
  Clock,
  FileText,
  Filter,
  Search,
  AlertCircle,
  Eye
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  LeaveManagementService,
  LeaveRequest,
  LeavePolicy
} from '@/services/LeaveManagementService';
import LeaveApprovalHistory from '@/components/leave-management/LeaveApprovalHistory';
import EnhancedLeaveApprovalDialog from '@/components/leave-management/EnhancedLeaveApprovalDialog';

export default function LeaveApprovals() {
  const { user } = useAuth();
  const { toast } = useToast();
  const { department, departmentName, loading: departmentLoading } = useUserDepartment();
  const { isHOD, loading: roleLoading } = useUserRole();

  // State management
  const [pendingRequests, setPendingRequests] = useState<LeaveRequest[]>([]);
  const [leavePolicies, setLeavePolicies] = useState<LeavePolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showEnhancedDialog, setShowEnhancedDialog] = useState(false);
  const [processing, setProcessing] = useState(false);

  // Filter state
  const [filters, setFilters] = useState({
    leave_type: 'all',
    start_date: '',
    end_date: '',
    faculty_name: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  // Load data
  useEffect(() => {
    if (user?.id && department && isHOD) {
      loadPendingRequests();
      loadLeavePolicies();
    }
  }, [user?.id, department, isHOD]);

  const loadPendingRequests = async () => {
    if (!user?.id || !department) return;

    try {
      setLoading(true);
      const filterParams = {
        leave_type: filters.leave_type === 'all' ? undefined : filters.leave_type,
        start_date: filters.start_date || undefined,
        end_date: filters.end_date || undefined,
        faculty_name: filters.faculty_name || undefined
      };
      const requests = await LeaveManagementService.getPendingRequestsForHOD(
        user.id,
        department,
        filterParams
      );
      setPendingRequests(requests);
    } catch (error) {
      console.error('Error loading pending requests:', error);
      toast({
        title: 'Error',
        description: 'Failed to load pending leave requests.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadLeavePolicies = async () => {
    try {
      const policies = await LeaveManagementService.getLeavePolicies();
      setLeavePolicies(policies);
    } catch (error) {
      console.error('Error loading leave policies:', error);
    }
  };

  const handleApprove = async () => {
    if (!selectedRequest || !user?.id || !department) return;

    try {
      setProcessing(true);
      await LeaveManagementService.approveLeaveRequest(
        selectedRequest.id,
        user.id,
        'hod',
        department
      );

      toast({
        title: 'Leave Approved',
        description: `Leave request for ${selectedRequest.employee_details?.full_name} has been approved.`,
      });

      setShowApproveDialog(false);
      setShowEnhancedDialog(false);
      setSelectedRequest(null);
      await loadPendingRequests();
    } catch (error) {
      console.error('Error approving leave:', error);
      toast({
        title: 'Approval Failed',
        description: error instanceof Error ? error.message : 'Failed to approve leave request.',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async (reason: string) => {
    if (!selectedRequest || !user?.id || !department || !reason.trim()) return;

    try {
      setProcessing(true);
      await LeaveManagementService.rejectLeaveRequest(
        selectedRequest.id,
        user.id,
        'hod',
        reason.trim(),
        department
      );

      toast({
        title: 'Leave Rejected',
        description: `Leave request for ${selectedRequest.employee_details?.full_name} has been rejected.`,
      });

      setShowRejectDialog(false);
      setShowEnhancedDialog(false);
      setSelectedRequest(null);
      setRejectionReason('');
      await loadPendingRequests();
    } catch (error) {
      console.error('Error rejecting leave:', error);
      toast({
        title: 'Rejection Failed',
        description: error instanceof Error ? error.message : 'Failed to reject leave request.',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleLegacyReject = async () => {
    if (!selectedRequest || !user?.id || !department || !rejectionReason.trim()) return;

    try {
      setProcessing(true);
      await LeaveManagementService.rejectLeaveRequest(
        selectedRequest.id,
        user.id,
        'hod',
        rejectionReason.trim(),
        department
      );

      toast({
        title: 'Leave Rejected',
        description: `Leave request for ${selectedRequest.faculty?.full_name} has been rejected.`,
      });

      setShowRejectDialog(false);
      setSelectedRequest(null);
      setRejectionReason('');
      await loadPendingRequests();
    } catch (error) {
      console.error('Error rejecting leave:', error);
      toast({
        title: 'Rejection Failed',
        description: error instanceof Error ? error.message : 'Failed to reject leave request.',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  const applyFilters = async () => {
    await loadPendingRequests();
  };

  const clearFilters = () => {
    setFilters({
      leave_type: 'all',
      start_date: '',
      end_date: '',
      faculty_name: ''
    });
  };

  const getLeaveTypeName = (leaveType: string): string => {
    const policy = leavePolicies.find(p => p.leave_type === leaveType);
    return policy?.leave_name || leaveType.replace('_', ' ').toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Show loading while checking roles and department
  if (roleLoading || departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  // Check if user has HOD role
  if (!isHOD) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Access denied. This page is only accessible to users with HOD role.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Check if department is available
  if (!department) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Department information not found. Please contact administrator to set your department.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold">Leave Approvals</h1>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </Button>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <strong>Department:</strong> {departmentName} | <strong>Role:</strong> HOD
            </div>
            <div className="text-sm">
              Showing pending leave requests from faculty in your department
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filter Leave Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label>Leave Type</Label>
                <Select value={filters.leave_type} onValueChange={(value) => setFilters(prev => ({ ...prev, leave_type: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    {leavePolicies.map((policy) => (
                      <SelectItem key={policy.leave_type} value={policy.leave_type}>
                        {policy.leave_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Start Date From</Label>
                <Input
                  type="date"
                  value={filters.start_date}
                  onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label>End Date To</Label>
                <Input
                  type="date"
                  value={filters.end_date}
                  onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Faculty Name</Label>
                <Input
                  placeholder="Search by name..."
                  value={filters.faculty_name}
                  onChange={(e) => setFilters(prev => ({ ...prev, faculty_name: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button onClick={applyFilters} size="sm">
                <Search className="h-4 w-4 mr-2" />
                Apply Filters
              </Button>
              <Button variant="outline" onClick={clearFilters} size="sm">
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pending Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Pending Leave Requests ({pendingRequests.length})
          </CardTitle>
          <CardDescription>
            Review and approve/reject leave requests from faculty in your department
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              <span className="ml-2">Loading requests...</span>
            </div>
          ) : pendingRequests.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No pending leave requests found</p>
              <p className="text-sm">All requests from your department have been processed</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <div key={request.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">{request.faculty?.full_name}</span>
                        <Badge variant="outline">{request.faculty?.designation}</Badge>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>📧 {request.faculty?.email}</span>
                        <span>🏢 {request.faculty?.department}</span>
                      </div>
                    </div>
                    
                    <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                      Pending Approval
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Leave Type:</span>
                      <p>{getLeaveTypeName(request.leave_type)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Duration:</span>
                      <p>{formatDate(request.start_date)} - {formatDate(request.end_date)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Total Days:</span>
                      <p>{request.total_days} days</p>
                    </div>
                    <div>
                      <span className="font-medium">Applied Date:</span>
                      <p>{formatDate(request.applied_date)}</p>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium text-sm">Reason:</span>
                    <p className="text-sm text-muted-foreground mt-1">{request.reason}</p>
                  </div>

                  <div className="flex gap-2 pt-2">
                    {/* Enhanced Review Button */}
                    <Button
                      size="sm"
                      onClick={() => {
                        setSelectedRequest(request);
                        setShowEnhancedDialog(true);
                      }}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Review & Approve
                    </Button>

                    {/* Legacy Quick Actions */}
                    <Dialog open={showApproveDialog && selectedRequest?.id === request.id} onOpenChange={setShowApproveDialog}>
                      <DialogTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedRequest(request)}
                          className="text-green-600 border-green-600 hover:bg-green-50"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Quick Approve
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Quick Approve Leave Request</DialogTitle>
                          <DialogDescription>
                            Are you sure you want to approve this leave request for {selectedRequest?.faculty?.full_name}?
                          </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setShowApproveDialog(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleApprove} disabled={processing}>
                            {processing ? 'Approving...' : 'Approve'}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>

                    <Dialog open={showRejectDialog && selectedRequest?.id === request.id} onOpenChange={setShowRejectDialog}>
                      <DialogTrigger asChild>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => setSelectedRequest(request)}
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Reject
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Reject Leave Request</DialogTitle>
                          <DialogDescription>
                            Please provide a reason for rejecting this leave request for {selectedRequest?.faculty?.full_name}.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="rejection-reason">Rejection Reason *</Label>
                            <Textarea
                              id="rejection-reason"
                              placeholder="Please provide a detailed reason for rejection..."
                              value={rejectionReason}
                              onChange={(e) => setRejectionReason(e.target.value)}
                              rows={4}
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => {
                            setShowRejectDialog(false);
                            setRejectionReason('');
                          }}>
                            Cancel
                          </Button>
                          <Button
                            variant="destructive"
                            onClick={handleLegacyReject}
                            disabled={processing || !rejectionReason.trim()}
                          >
                            {processing ? 'Rejecting...' : 'Reject'}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Leave Approval History */}
      <LeaveApprovalHistory className="mt-6" />

      {/* Enhanced Leave Approval Dialog */}
      <EnhancedLeaveApprovalDialog
        isOpen={showEnhancedDialog}
        onClose={() => {
          setShowEnhancedDialog(false);
          setSelectedRequest(null);
        }}
        leaveRequest={selectedRequest}
        onApprove={handleApprove}
        onReject={handleReject}
        processing={processing}
      />
    </div>
  );
}
