// SemesterLabConfigurationService.ts
import { supabase } from "@/integrations/supabase/client";
import { Tables, TablesInsert, TablesUpdate } from "@/integrations/supabase/types";

export type SemesterLabConfiguration = Tables<"semester_lab_configurations">;
export type SemesterLabConfigurationInsert = TablesInsert<"semester_lab_configurations">;
export type SemesterLabConfigurationUpdate = TablesUpdate<"semester_lab_configurations">;

export interface LabTimeSlotDefinition {
  id: string;
  name: string;
  timeRange: string;
  periods: string;
  duration: 2 | 3;
  enabled: boolean;
}

export interface LabDurationConfig {
  duration: 2 | 3;
  type: 'two_hour' | 'three_hour';
  availableTimeSlots: LabTimeSlotDefinition[];
}

export class SemesterLabConfigurationService {

  /**
   * Get lab configuration for a specific semester
   */
  static async getLabConfiguration(
    academicYear: string,
    department: string,
    semester: string
  ): Promise<SemesterLabConfiguration | null> {
    try {
      const { data, error } = await supabase
        .from('semester_lab_configurations')
        .select('*')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No configuration found, return default
          return this.getDefaultConfiguration(academicYear, department, semester);
        }
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching lab configuration:', error);
      return this.getDefaultConfiguration(academicYear, department, semester);
    }
  }

  /**
   * Get default lab configuration for backward compatibility
   * This provides a flexible default that supports both 2-hour and 3-hour labs
   */
  static getDefaultConfiguration(
    academicYear: string,
    department: string,
    semester: string
  ): SemesterLabConfiguration {
    return {
      id: `default-${academicYear}-${department}-${semester}`,
      academic_year: academicYear,
      department: department,
      semester: semester,
      default_lab_duration: 3, // Default to 3-hour but can be changed
      lab_duration_type: 'three_hour',
      enable_morning_session: true,
      enable_mid_session: true, // Enable all sessions by default for flexibility
      enable_afternoon_session: true,
      enable_early_afternoon_session: true,
      skill_lab_required: true,
      skill_lab_placement_preference: 'vacant_day',
      skill_lab_preferred_day: null,
      skill_lab_duration: 3,
      max_labs_per_day: 2, // Allow more flexibility
      allow_consecutive_labs: true,
      prefer_lab_distribution: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      created_by: null,
      last_modified_by: null
    };
  }

  /**
   * Save or update lab configuration
   */
  static async saveLabConfiguration(
    config: SemesterLabConfigurationInsert
  ): Promise<SemesterLabConfiguration> {
    try {
      const { data, error } = await supabase
        .from('semester_lab_configurations')
        .upsert(config, {
          onConflict: 'academic_year,department,semester'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error saving lab configuration:', error);
      throw error;
    }
  }

  /**
   * Get available time slots based on lab duration configuration
   * Now supports flexible duration selection - shows all possible options
   */
  static getAvailableTimeSlots(config: SemesterLabConfiguration): LabTimeSlotDefinition[] {
    const timeSlots: LabTimeSlotDefinition[] = [];

    // Check if this is a default configuration (flexible mode)
    const isDefaultConfig = config.id?.startsWith('default-');

    if (isDefaultConfig) {
      // For default configurations, show all possible time slots for maximum flexibility
      // Users can choose any duration they want

      // 2-hour options
      timeSlots.push({
        id: 'morning_2h',
        name: 'Morning Session',
        timeRange: '08:30-10:20',
        periods: 'Periods 1-2 (2 hours)',
        duration: 2,
        enabled: true
      });

      timeSlots.push({
        id: 'mid_2h',
        name: 'Mid Session',
        timeRange: '10:35-12:25',
        periods: 'Periods 3-4 (2 hours)',
        duration: 2,
        enabled: true
      });

      timeSlots.push({
        id: 'early_afternoon_2h',
        name: 'Early Afternoon Session',
        timeRange: '13:15-15:05',
        periods: 'Periods 5-6 (2 hours)',
        duration: 2,
        enabled: true
      });

      // 3-hour options
      timeSlots.push({
        id: 'morning_3h',
        name: 'Morning Session',
        timeRange: '08:30-11:30',
        periods: 'Periods 1-3 (3 hours)',
        duration: 3,
        enabled: true
      });

      timeSlots.push({
        id: 'afternoon_3h',
        name: 'Afternoon Session',
        timeRange: '13:15-16:00',
        periods: 'Periods 5-7 (3 hours)',
        duration: 3,
        enabled: true
      });

    } else {
      // For specific configurations, use the configured duration and enabled sessions

      // Morning Session
      if (config.enable_morning_session) {
        if (config.default_lab_duration === 2) {
          timeSlots.push({
            id: 'morning_2h',
            name: 'Morning Session',
            timeRange: '08:30-10:20',
            periods: 'Periods 1-2',
            duration: 2,
            enabled: true
          });
        } else {
          timeSlots.push({
            id: 'morning_3h',
            name: 'Morning Session',
            timeRange: '08:30-11:30',
            periods: 'Periods 1-3',
            duration: 3,
            enabled: true
          });
        }
      }

      // Mid Session (only for 2-hour labs)
      if (config.enable_mid_session && config.default_lab_duration === 2) {
        timeSlots.push({
          id: 'mid_2h',
          name: 'Mid Session',
          timeRange: '10:35-12:25',
          periods: 'Periods 3-4',
          duration: 2,
          enabled: true
        });
      }

      // Early Afternoon Session (only for 2-hour labs)
      if (config.enable_early_afternoon_session && config.default_lab_duration === 2) {
        timeSlots.push({
          id: 'early_afternoon_2h',
          name: 'Early Afternoon Session',
          timeRange: '13:15-15:05',
          periods: 'Periods 5-6',
          duration: 2,
          enabled: true
        });
      }

      // Afternoon Session
      if (config.enable_afternoon_session) {
        if (config.default_lab_duration === 2) {
          // For 2-hour labs, afternoon session is same as early afternoon
          if (!config.enable_early_afternoon_session) {
            timeSlots.push({
              id: 'afternoon_2h',
              name: 'Afternoon Session',
              timeRange: '13:15-15:05',
              periods: 'Periods 5-6',
              duration: 2,
              enabled: true
            });
          }
        } else {
          timeSlots.push({
            id: 'afternoon_3h',
            name: 'Afternoon Session',
            timeRange: '13:15-16:00',
            periods: 'Periods 5-7',
            duration: 3,
            enabled: true
          });
        }
      }
    }

    return timeSlots;
  }

  /**
   * Get lab duration configuration for a semester
   */
  static async getLabDurationConfig(
    academicYear: string,
    department: string,
    semester: string
  ): Promise<LabDurationConfig> {
    const config = await this.getLabConfiguration(academicYear, department, semester);

    return {
      duration: config.default_lab_duration as 2 | 3,
      type: config.lab_duration_type as 'two_hour' | 'three_hour',
      availableTimeSlots: this.getAvailableTimeSlots(config)
    };
  }

  /**
   * Check if a specific time slot is enabled for a semester
   */
  static async isTimeSlotEnabled(
    academicYear: string,
    department: string,
    semester: string,
    timeSlotId: string
  ): Promise<boolean> {
    const config = await this.getLabConfiguration(academicYear, department, semester);
    const availableSlots = this.getAvailableTimeSlots(config);

    return availableSlots.some(slot => slot.id === timeSlotId && slot.enabled);
  }

  /**
   * Get all configurations for a department and academic year
   */
  static async getAllConfigurations(
    academicYear: string,
    department: string
  ): Promise<SemesterLabConfiguration[]> {
    try {
      const { data, error } = await supabase
        .from('semester_lab_configurations')
        .select('*')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .order('semester');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching all configurations:', error);
      return [];
    }
  }

  /**
   * Delete a lab configuration
   */
  static async deleteLabConfiguration(
    academicYear: string,
    department: string,
    semester: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('semester_lab_configurations')
        .delete()
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting lab configuration:', error);
      throw error;
    }
  }

  /**
   * Calculate column span based on lab duration
   */
  static getColumnSpan(config: SemesterLabConfiguration): number {
    return config.default_lab_duration;
  }

  /**
   * Get time slot mapping for lab generation
   */
  static getTimeSlotMapping(timeOfDay: string, config: SemesterLabConfiguration): string | null {
    const availableSlots = this.getAvailableTimeSlots(config);

    // Map time of day strings to time slot IDs
    const mapping: Record<string, string[]> = {
      'Morning': ['morning_2h', 'morning_3h'],
      'Mid': ['mid_2h'],
      'Early Afternoon': ['early_afternoon_2h'],
      'Afternoon': ['afternoon_2h', 'afternoon_3h']
    };

    const possibleSlots = mapping[timeOfDay] || [];
    const enabledSlot = availableSlots.find(slot =>
      possibleSlots.includes(slot.id) && slot.enabled
    );

    return enabledSlot ? enabledSlot.timeRange : null;
  }
}
