// SlotSwappingService.ts - <PERSON>les intelligent slot swapping for conflict resolution

import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from 'uuid';
import {
  ConflictInfo,
  ConflictSlot,
  SwapProposal,
  SwapCandidate,
  AlternativeSlot,
  SlotSwapValidation,
  FacultyAvailabilityUpdate,
  ConflictDetectionParams
} from "@/types/ConflictResolution";
import { FacultyAvailabilityAnalyzer } from "./FacultyAvailabilityAnalyzer";
import { FacultyAvailabilityRecalculator } from "./FacultyAvailabilityService";
import { TimeStructure } from "./TimetableService";

export class SlotSwappingService {
  /**
   * Find the best swap candidates for resolving a conflict
   */
  static async findSwapCandidates(
    conflict: ConflictInfo,
    params: ConflictDetectionParams
  ): Promise<SwapCandidate[]> {
    const candidates: SwapCandidate[] = [];

    // Get all faculty members who could potentially swap
    const highAvailabilityFaculty = await FacultyAvailabilityAnalyzer.findHighAvailabilityFaculty(
      [conflict.facultyId],
      params,
      50 // Minimum flexibility score
    );

    for (const facultyProfile of highAvailabilityFaculty) {
      // Find current slots for this faculty that could be swapped
      const { data: facultySlots, error } = await supabase
        .from("timetable_slots")
        .select("*")
        .eq("academic_year", params.academicYear)
        .eq("department", params.department)
        .eq("faculty_id", facultyProfile.facultyId)
        .eq("subject_type", "theory"); // Focus on theory slots for swapping

      if (error || !facultySlots) continue;

      for (const slot of facultySlots) {
        // Check if this faculty could take the conflicted slot
        const isAvailableForConflictSlot = await FacultyAvailabilityAnalyzer.isFacultyAvailable(
          facultyProfile.facultyId,
          conflict.day,
          conflict.timeSlot,
          params
        );

        if (isAvailableForConflictSlot) {
          // Find alternative slots for the current faculty's slot
          const alternativeSlots = await this.findAlternativeSlotsForSwap(
            slot,
            conflict.facultyId,
            params
          );

          if (alternativeSlots.length > 0) {
            const candidate: SwapCandidate = {
              facultyId: facultyProfile.facultyId,
              facultyName: facultyProfile.facultyName,
              currentSlot: this.convertToConflictSlot(slot),
              availabilityScore: facultyProfile.flexibilityScore,
              swapFeasibility: this.calculateSwapFeasibility(
                slot,
                conflict,
                alternativeSlots,
                facultyProfile
              ),
              alternativeSlots
            };

            candidates.push(candidate);
          }
        }
      }
    }

    // Sort by feasibility score (higher is better)
    return candidates.sort((a, b) => b.swapFeasibility - a.swapFeasibility);
  }

  /**
   * Create a swap proposal for resolving conflicts
   */
  static async createSwapProposal(
    conflict: ConflictInfo,
    swapCandidate: SwapCandidate,
    params: ConflictDetectionParams
  ): Promise<SwapProposal> {
    const proposalId = uuidv4();

    // Find the best alternative slot for the original faculty
    const bestAlternative = swapCandidate.alternativeSlots
      .sort((a, b) => b.score - a.score)[0];

    const proposal: SwapProposal = {
      id: proposalId,
      type: 'direct_swap',
      primaryFaculty: {
        facultyId: conflict.facultyId,
        facultyName: conflict.facultyName,
        fromSlot: conflict.affectedSlots[0], // The conflicted slot
        toSlot: bestAlternative
      },
      secondaryFaculty: {
        facultyId: swapCandidate.facultyId,
        facultyName: swapCandidate.facultyName,
        fromSlot: swapCandidate.currentSlot,
        toSlot: {
          day: conflict.day,
          timeSlot: conflict.timeSlot,
          score: 100, // High score since this resolves the conflict
          reason: "Takes over conflicted slot",
          conflicts: []
        }
      },
      cascadingSwaps: [],
      totalScore: this.calculateProposalScore(swapCandidate, bestAlternative),
      estimatedSuccess: this.estimateSwapSuccess(swapCandidate, bestAlternative),
      potentialConflicts: []
    };

    // Check for potential cascading conflicts
    const cascadingConflicts = await this.checkCascadingConflicts(proposal, params);
    proposal.potentialConflicts = cascadingConflicts;

    return proposal;
  }

  /**
   * Validate a swap proposal before execution
   */
  static async validateSwapProposal(
    proposal: SwapProposal,
    params: ConflictDetectionParams
  ): Promise<SlotSwapValidation> {
    const validation: SlotSwapValidation = {
      isValid: true,
      conflicts: [],
      warnings: [],
      facultyAvailabilityImpact: [],
      cascadingEffects: []
    };

    // Check if both faculty members are still available for their new slots
    const primaryAvailable = await FacultyAvailabilityAnalyzer.isFacultyAvailable(
      proposal.primaryFaculty.facultyId,
      proposal.primaryFaculty.toSlot.day,
      proposal.primaryFaculty.toSlot.timeSlot,
      params
    );

    const secondaryAvailable = await FacultyAvailabilityAnalyzer.isFacultyAvailable(
      proposal.secondaryFaculty!.facultyId,
      proposal.secondaryFaculty!.toSlot.day,
      proposal.secondaryFaculty!.toSlot.timeSlot,
      params
    );

    if (!primaryAvailable) {
      validation.isValid = false;
      validation.conflicts.push({
        id: uuidv4(),
        type: 'faculty_double_booking',
        severity: 'critical',
        description: `Primary faculty ${proposal.primaryFaculty.facultyName} is not available for the proposed slot`,
        affectedSlots: [],
        facultyId: proposal.primaryFaculty.facultyId,
        facultyName: proposal.primaryFaculty.facultyName,
        day: proposal.primaryFaculty.toSlot.day,
        timeSlot: proposal.primaryFaculty.toSlot.timeSlot,
        academicYear: params.academicYear,
        department: params.department
      });
    }

    if (!secondaryAvailable) {
      validation.isValid = false;
      validation.conflicts.push({
        id: uuidv4(),
        type: 'faculty_double_booking',
        severity: 'critical',
        description: `Secondary faculty ${proposal.secondaryFaculty!.facultyName} is not available for the proposed slot`,
        affectedSlots: [],
        facultyId: proposal.secondaryFaculty!.facultyId,
        facultyName: proposal.secondaryFaculty!.facultyName,
        day: proposal.secondaryFaculty!.toSlot.day,
        timeSlot: proposal.secondaryFaculty!.toSlot.timeSlot,
        academicYear: params.academicYear,
        department: params.department
      });
    }

    // Check for consecutive theory slot violations
    await this.checkConsecutiveTheoryViolations(proposal, validation, params);

    return validation;
  }

  /**
   * Execute a validated swap proposal
   */
  static async executeSwapProposal(
    proposal: SwapProposal,
    params: ConflictDetectionParams
  ): Promise<{ success: boolean; error?: string; facultyUpdates: FacultyAvailabilityUpdate[] }> {
    const facultyUpdates: FacultyAvailabilityUpdate[] = [];

    try {
      // Start a transaction-like operation
      const updates = [];

      // CRITICAL FIX: Validate primary faculty's target slot is free before swapping
      const { data: primaryExistingSlots } = await supabase
        .from("timetable_slots")
        .select("id")
        .eq("faculty_id", proposal.primaryFaculty.facultyId)
        .eq("day", proposal.primaryFaculty.toSlot.day)
        .eq("time_slot", proposal.primaryFaculty.toSlot.timeSlot)
        .eq("academic_year", params.academicYear);

      if (primaryExistingSlots && primaryExistingSlots.length > 0) {
        throw new Error(`Target slot ${proposal.primaryFaculty.toSlot.day} ${proposal.primaryFaculty.toSlot.timeSlot} is already occupied for faculty ${proposal.primaryFaculty.facultyId}`);
      }

      // Update primary faculty's slot
      updates.push(
        supabase
          .from("timetable_slots")
          .update({
            day: proposal.primaryFaculty.toSlot.day,
            time_slot: proposal.primaryFaculty.toSlot.timeSlot
          })
          .eq("id", proposal.primaryFaculty.fromSlot.slotId)
      );

      // Update secondary faculty's slot - CRITICAL FIX: Move to new time, don't change faculty
      if (proposal.secondaryFaculty) {
        // Validate that the target slot is actually free before swapping
        const { data: existingSlots } = await supabase
          .from("timetable_slots")
          .select("id")
          .eq("faculty_id", proposal.secondaryFaculty.facultyId)
          .eq("day", proposal.secondaryFaculty.toSlot.day)
          .eq("time_slot", proposal.secondaryFaculty.toSlot.timeSlot)
          .eq("academic_year", params.academicYear);

        if (existingSlots && existingSlots.length > 0) {
          throw new Error(`Target slot ${proposal.secondaryFaculty.toSlot.day} ${proposal.secondaryFaculty.toSlot.timeSlot} is already occupied for faculty ${proposal.secondaryFaculty.facultyId}`);
        }

        updates.push(
          supabase
            .from("timetable_slots")
            .update({
              day: proposal.secondaryFaculty.toSlot.day,
              time_slot: proposal.secondaryFaculty.toSlot.timeSlot
            })
            .eq("id", proposal.secondaryFaculty.fromSlot.slotId)
        );
      }

      // Execute all updates
      const results = await Promise.all(updates);

      // Check for errors
      for (const result of results) {
        if (result.error) {
          throw new Error(`Database update failed: ${result.error.message}`);
        }
      }

      // Update faculty availability
      const primaryUpdate = await this.updateFacultyAvailabilityAfterSwap(
        proposal.primaryFaculty.facultyId,
        proposal.primaryFaculty.fromSlot,
        proposal.primaryFaculty.toSlot,
        params
      );
      facultyUpdates.push(primaryUpdate);

      if (proposal.secondaryFaculty) {
        const secondaryUpdate = await this.updateFacultyAvailabilityAfterSwap(
          proposal.secondaryFaculty.facultyId,
          proposal.secondaryFaculty.fromSlot,
          proposal.secondaryFaculty.toSlot,
          params
        );
        facultyUpdates.push(secondaryUpdate);
      }

      return { success: true, facultyUpdates };
    } catch (error) {
      console.error("Error executing swap proposal:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        facultyUpdates: []
      };
    }
  }

  /**
   * Find alternative slots for a faculty member's current slot during a swap
   */
  private static async findAlternativeSlotsForSwap(
    currentSlot: any,
    targetFacultyId: string,
    params: ConflictDetectionParams
  ): Promise<AlternativeSlot[]> {
    const alternatives: AlternativeSlot[] = [];

    // Get target faculty's availability
    const targetProfile = await FacultyAvailabilityAnalyzer.analyzeFacultyAvailability(
      [targetFacultyId],
      params
    );

    if (targetProfile.length === 0) return alternatives;

    const profile = targetProfile[0];

    // Check each day for available slots
    for (const [day, availableSlots] of Object.entries(profile.availableSlotsByDay)) {
      for (const timeSlot of availableSlots) {
        // Skip if it's the same as current slot
        if (day === currentSlot.day && timeSlot === currentSlot.time_slot) continue;

        const alternative: AlternativeSlot = {
          day,
          timeSlot,
          score: this.calculateAlternativeSlotScore(day, timeSlot, currentSlot, profile),
          reason: `Available slot on ${day} at ${timeSlot}`,
          conflicts: []
        };

        alternatives.push(alternative);
      }
    }

    return alternatives.sort((a, b) => b.score - a.score);
  }

  // Helper methods
  private static convertToConflictSlot(slot: any): ConflictSlot {
    return {
      slotId: slot.id,
      semester: slot.semester,
      section: slot.section,
      subjectCode: slot.subject_code,
      subjectName: slot.subject_name,
      subjectType: slot.subject_type,
      day: slot.day,
      timeSlot: slot.time_slot,
      facultyId: slot.faculty_id,
      facultyName: slot.faculty_name,
      faculty2Id: slot.faculty2_id,
      faculty2Name: slot.faculty2_name
    };
  }

  private static calculateSwapFeasibility(
    slot: any,
    conflict: ConflictInfo,
    alternatives: AlternativeSlot[],
    facultyProfile: any
  ): number {
    let score = 0;

    // Base score from faculty availability
    score += facultyProfile.flexibilityScore * 0.3;

    // Bonus for having good alternatives
    score += alternatives.length * 10;

    // Bonus for best alternative quality
    if (alternatives.length > 0) {
      score += alternatives[0].score * 0.5;
    }

    return score;
  }

  private static calculateProposalScore(candidate: SwapCandidate, alternative: AlternativeSlot): number {
    return candidate.swapFeasibility + alternative.score;
  }

  private static estimateSwapSuccess(candidate: SwapCandidate, alternative: AlternativeSlot): number {
    // Return percentage (0-100)
    const baseSuccess = Math.min(90, candidate.availabilityScore * 0.5);
    const alternativeBonus = Math.min(10, alternative.score * 0.1);
    return Math.min(100, baseSuccess + alternativeBonus);
  }

  private static async checkCascadingConflicts(
    proposal: SwapProposal,
    params: ConflictDetectionParams
  ): Promise<ConflictInfo[]> {
    // Implementation for checking cascading conflicts
    // This would check if the proposed swap creates new conflicts
    return [];
  }

  private static async checkConsecutiveTheoryViolations(
    proposal: SwapProposal,
    validation: SlotSwapValidation,
    params: ConflictDetectionParams
  ): Promise<void> {
    // Implementation for checking consecutive theory slot violations
    // This would ensure the swap doesn't create consecutive theory slots
  }

  private static calculateAlternativeSlotScore(
    day: string,
    timeSlot: string,
    currentSlot: any,
    profile: any
  ): number {
    let score = 50; // Base score

    // Prefer same day to minimize disruption
    if (day === currentSlot.day) {
      score += 20;
    }

    // Prefer days with higher availability
    const dayAvailability = profile.availableSlotsByDay[day]?.length || 0;
    score += dayAvailability * 5;

    // Prefer less loaded days
    const dayWorkload = profile.workloadDistribution[day] || 0;
    score -= dayWorkload * 3;

    return score;
  }

  private static async updateFacultyAvailabilityAfterSwap(
    facultyId: string,
    fromSlot: any,
    toSlot: any,
    params: ConflictDetectionParams
  ): Promise<FacultyAvailabilityUpdate> {
    try {
      // Get current availability before recalculation
      const { data: faculty } = await supabase
        .from("employee_details")
        .select("vacant_by_day, vacant_count_by_day, full_name")
        .eq("id", facultyId)
        .single();

      const previousAvailability = {
        vacant_by_day: { ...(faculty?.vacant_by_day || {}) },
        vacant_count_by_day: JSON.parse(JSON.stringify(faculty?.vacant_count_by_day || {}))
      };

      // Get time structure for comprehensive recalculation
      const { data: timeStructureData } = await supabase
        .from("time_structure")
        .select("*")
        .eq("department", params.department)
        .single();

      if (!timeStructureData) {
        throw new Error(`Time structure not found for department: ${params.department}`);
      }

      const timeStructure: TimeStructure = timeStructureData;

      // Use our comprehensive recalculator
      await FacultyAvailabilityRecalculator.recalculateForFaculty(
        [facultyId],
        timeStructure,
        params.academicYear
      );

      console.log(`✅ Recalculated availability after slot swap for faculty ${facultyId}`);

      // Get updated availability
      const { data: updatedFaculty } = await supabase
        .from("employee_details")
        .select("vacant_by_day, vacant_count_by_day")
        .eq("id", facultyId)
        .single();

      const newAvailability = {
        vacant_by_day: { ...(updatedFaculty?.vacant_by_day || {}) },
        vacant_count_by_day: JSON.parse(JSON.stringify(updatedFaculty?.vacant_count_by_day || {}))
      };

      return {
        facultyId,
        facultyName: faculty?.full_name || "",
        previousAvailability,
        newAvailability,
        changedDays: [fromSlot.day, toSlot.day]
      };
    } catch (error) {
      console.error(`❌ Error updating faculty availability after swap for ${facultyId}:`, error);
      throw error;
    }
  }
}
