
import React from "react";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";

interface UploadedFileDisplayProps {
  file: File;
  onRemove: () => void;
  uploadComplete: boolean;
  isUploading: boolean;
}

export default function UploadedFileDisplay({ 
  file, 
  onRemove, 
  uploadComplete, 
  isUploading 
}: UploadedFileDisplayProps) {
  return (
    <div className="flex items-center space-x-4 p-4 border rounded-md">
      <div className="p-2 bg-primary/10 rounded">
        <FileText className="h-6 w-6 text-primary" />
      </div>
      <div className="flex-1">
        <p className="text-sm font-medium truncate">{file.name}</p>
        <p className="text-xs text-muted-foreground">
          {(file.size / 1024).toFixed(2)} KB
        </p>
      </div>
      {!uploadComplete && !isUploading && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onRemove}
        >
          Remove
        </Button>
      )}
    </div>
  );
}
