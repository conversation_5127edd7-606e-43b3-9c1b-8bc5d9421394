
import React from "react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from "react-hook-form";
import { SubjectType } from "@/components/subjects/useSubjectLoader";
import { MappingFormData } from "@/hooks/useSubjectMappingForm";

interface SubjectSelectorProps {
  form: UseFormReturn<MappingFormData>;
  subjects: SubjectType[];
  disabled?: boolean;
}

export default function SubjectSelector({ form, subjects, disabled = false }: SubjectSelectorProps) {
  // Convert subjects to options format
  const subjectOptions = subjects.map((s) => ({
    value: s.id,
    label: `${s.code} - ${s.name}`,
    type: s.type,
  }));

  return (
    <FormField
      control={form.control}
      name="subject"
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>Subject</FormLabel>
          <Select
            value={field.value?.id}
            onValueChange={(value) => {
              const selectedSubject = subjects?.find((s) => s.id === value);
              if (selectedSubject) {
                field.onChange({
                  id: selectedSubject.id,
                  code: selectedSubject.code,
                  name: selectedSubject.name,
                  type: selectedSubject.type,
                });
              }
            }}
            disabled={disabled}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select a subject" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {subjectOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormDescription>
            Select the subject for this mapping
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
