
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { processSubjectMappingExcel } from "@/utils/subjectMappingProcessing";
import { MappingService } from "@/services/mapping";
import { FileUploader } from "./FileUploader";
import { UploadResults } from "./UploadResults";
import { UploadGuidelines } from "./UploadGuidelines";

// Define LabSlotRow interface to match expected type in MappingService
interface LabSlotRow {
  parentRowIndex: number;
  slot_order: number;
  batch_name: string;
  day: string;
  time_of_day: string;
}

const SubjectAllotmentUpload = () => {
  const [file, setFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [skippedEntries, setSkippedEntries] = useState<string[]>([]);
  const [processedData, setProcessedData] = useState<{
    mappings: number;
    labSlots: number;
    labSubjects: number;
    batchesPerLab: number;
  }>({ mappings: 0, labSlots: 0, labSubjects: 0, batchesPerLab: 0 });
  
  const { toast } = useToast();
  
  const handleUpload = async () => {
    if (!file) return;
    
    setUploading(true);
    setUploadProgress(10);
    
    try {
      // Process the Excel file
      const result = await processSubjectMappingExcel(file);
      setUploadProgress(40);
      
      // Calculate lab subjects count and avg batches per lab
      const labSubjects = result.mappings.filter(m => m.subject_type === 'laboratory').length;
      const batchesPerLab = labSubjects > 0 ? (result.labSlots.length / labSubjects).toFixed(1) : "0";
      
      setProcessedData({
        mappings: result.mappings.length,
        labSlots: result.labSlots.length,
        labSubjects: labSubjects,
        batchesPerLab: parseFloat(batchesPerLab)
      });
      
      // Set academic year, department, etc.
      const academicYear = "2023-2024";
      const department = "CSE";
      const semester = "5";
      const section = "A";
      
      // Transform the mappings to match the MappingRow interface
      const enrichedMappings = result.mappings.map(mapping => ({
        academic_year: academicYear,
        department: department,
        semester: semester,
        section: section,
        subject_code: mapping.subject_code,
        subject_name: mapping.subject_name,
        subject_type: mapping.subject_type as "theory" | "laboratory" | "elective",
        faculty1: mapping.faculty1,
        faculty2: mapping.faculty2,
        hours_per_week: Number(mapping.hours_per_week) || 0,
        classroom: mapping.classroom || "Not specified",
        slots_per_week: mapping.subject_type === "laboratory" ? (Number(mapping.slots_per_week) || 1) : undefined
      }));
      
      setUploadProgress(60);
      
      // Pass the section information to ensure correct batch naming
      const typedLabSlots: LabSlotRow[] = result.labSlots.map(slot => ({
        parentRowIndex: slot.parentRowIndex,
        slot_order: slot.slot_order,
        batch_name: slot.batch_name, // This should now correctly maintain section-specific batch names
        day: slot.day,
        time_of_day: slot.time_of_day
      }));
      
      console.log("Uploading lab slots with batch names:", typedLabSlots.map(s => s.batch_name));
      
      // Upload to database through MappingService
      const importResult = await MappingService.importExcel(enrichedMappings, typedLabSlots);
      
      setUploadProgress(100);
      setUploadComplete(true);
      
      // Check for skipped entries
      if (importResult && importResult.skipped > 0) {
        setSkippedEntries(importResult.reasons || []);
        toast({ 
          title: "Upload partially successful", 
          description: `Imported ${importResult.imported} mappings. ${importResult.skipped} entries were skipped.`
        });
      } else {
        toast({ 
          title: "Upload successful", 
          description: `Imported ${result.mappings.length} subject mappings with ${result.labSlots.length} lab slots.`
        });
      }
      
    } catch (error: any) {
      console.error("Upload error:", error);
      toast({
        title: "Upload failed",
        description: error.message || "An error occurred during upload",
        variant: "destructive"
      });
      setUploadProgress(0);
    } finally {
      setUploading(false);
    }
  };
  
  const handleUploadAgain = () => {
    setFile(null);
    setUploadProgress(0);
    setUploadComplete(false);
    setSkippedEntries([]);
    setProcessedData({ mappings: 0, labSlots: 0, labSubjects: 0, batchesPerLab: 0 });
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">Upload Subject Allotment</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <FileUploader 
            file={file}
            setFile={setFile}
            uploadProgress={uploadProgress}
            uploading={uploading}
            uploadComplete={uploadComplete}
            onUploadClick={handleUpload}
          />
          
          <UploadResults
            uploadComplete={uploadComplete}
            processedData={processedData}
            skippedEntries={skippedEntries}
            onUploadAgain={handleUploadAgain}
          />
          
          <UploadGuidelines />
        </div>
      </CardContent>
    </Card>
  );
};

export default SubjectAllotmentUpload;
