import { lazy } from 'react';

// Lazy load heavy components for better performance
export const LazyTimetableGeneration = lazy(() => import('../timetable/TimetableGeneration'));
export const LazyManualAttendance = lazy(() => import('../attendance/ManualAttendance'));
export const LazyAttendanceSheet = lazy(() => import('../attendance/AttendanceSheet'));
export const LazyFacultyQuizDashboard = lazy(() => import('../quiz/FacultyQuizDashboard'));
export const LazyStudentQuizInterface = lazy(() => import('../quiz/StudentQuizInterface'));
export const LazyAnalyticsDashboard = lazy(() => import('../analytics/AnalyticsDashboard'));
export const LazyReportsGeneration = lazy(() => import('../reports/ReportsGeneration'));
export const LazyStudentManagement = lazy(() => import('../student-management/StudentManagement'));
export const LazyUserManagement = lazy(() => import('../users/UserManagement'));
export const LazySubjectManagement = lazy(() => import('../subjects/SubjectManagement'));
export const LazyLeaveManagement = lazy(() => import('../leave/LeaveManagement'));
export const LazyFeedbackManagement = lazy(() => import('../feedback/FeedbackManagement'));

// Export all lazy components for easy importing
export {
  LazyTimetableGeneration,
  LazyManualAttendance,
  LazyAttendanceSheet,
  LazyFacultyQuizDashboard,
  LazyStudentQuizInterface,
  LazyAnalyticsDashboard,
  LazyReportsGeneration,
  LazyStudentManagement,
  LazyUserManagement,
  LazySubjectManagement,
  LazyLeaveManagement,
  LazyFeedbackManagement
};
