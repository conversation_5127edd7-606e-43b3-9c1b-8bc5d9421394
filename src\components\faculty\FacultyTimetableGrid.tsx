import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, MapPin, Users, BookOpen } from 'lucide-react';
import { type TimetableSlot } from '@/services/FacultyAssignmentService';
import { getDisplaySubjectCode } from '@/utils/subjectUtils';

interface FacultyTimetableGridProps {
  weeklyTimetable: { [day: string]: { [period: string]: TimetableSlot | null } };
  loading?: boolean;
}

const FacultyTimetableGrid: React.FC<FacultyTimetableGridProps> = ({
  weeklyTimetable,
  loading = false
}) => {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const periods = ['1', '2', 'tea_break', '3', '4', 'lunch_break', '5', '6', '7'];

  // Debug: Log what data the component is receiving
  React.useEffect(() => {
    console.log('🎨 FacultyTimetableGrid received data:', {
      loading,
      weeklyTimetableKeys: Object.keys(weeklyTimetable),
      totalDays: Object.keys(weeklyTimetable).length,
      sampleDay: weeklyTimetable['Monday'] ? Object.keys(weeklyTimetable['Monday']).length : 0
    });

    // Check for any actual class data
    let hasClassData = false;
    Object.keys(weeklyTimetable).forEach(day => {
      Object.keys(weeklyTimetable[day] || {}).forEach(period => {
        const slot = weeklyTimetable[day][period];
        if (slot && !slot.is_break && slot.subject_code) {
          hasClassData = true;
          console.log(`🎨 Found class data: ${day} - Period ${period} - ${slot.subject_code}`);
        }
      });
    });

    console.log('🎨 Has class data:', hasClassData);
  }, [weeklyTimetable, loading]);

  const periodTimes: { [key: string]: string } = {
    '1': '08:30-09:25',
    '2': '09:25-10:20',
    'tea_break': '10:20-10:40',
    '3': '10:40-11:35',
    '4': '11:35-12:30',
    'lunch_break': '12:30-13:15',
    '5': '13:15-14:10',
    '6': '14:10-15:05',
    '7': '15:05-16:00'
  };

  const getSlotContent = (slot: TimetableSlot | null) => {
    if (!slot) {
      return (
        <div className="h-full flex items-center justify-center text-muted-foreground text-sm bg-gray-50 rounded border-2 border-dashed border-gray-200">
          <span className="text-gray-400">Free</span>
        </div>
      );
    }

    if (slot.is_break) {
      return (
        <div className={`h-full flex items-center justify-center ${
          slot.break_type === 'tea' ? 'bg-orange-100 text-orange-800' : 'bg-blue-100 text-blue-800'
        } rounded`}>
          <div className="text-center">
            <Clock className="h-4 w-4 mx-auto mb-1" />
            <span className="text-xs font-medium">{slot.subject_name}</span>
          </div>
        </div>
      );
    }

    return (
      <div className="h-full p-2 bg-gradient-to-br from-green-50 to-blue-50 border-2 border-green-300 rounded shadow-sm hover:shadow-md transition-shadow">
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <Badge variant="default" className="text-xs bg-green-600 hover:bg-green-700">
              {getDisplaySubjectCode(slot.subject_code, slot.subject_short_id)}
            </Badge>
            <Badge variant="outline" className="text-xs border-blue-300 text-blue-700">
              {slot.subject_type.toUpperCase()}
            </Badge>
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Users className="h-3 w-3" />
              <span>Sem {slot.semester} - Sec {slot.section}</span>
            </div>

            {slot.room && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span>{slot.room}</span>
              </div>
            )}

            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>{slot.time_slot}</span>
            </div>
          </div>

          <div className="text-xs font-medium text-blue-800 truncate" title={slot.subject_name}>
            {slot.subject_name}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            My Weekly Timetable
          </CardTitle>
          <CardDescription>
            Loading your teaching schedule...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading timetable...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpen className="h-5 w-5" />
          My Weekly Timetable
        </CardTitle>
        <CardDescription>
          Your complete teaching schedule for the week
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <div className="min-w-[800px]">
            {/* Header Row */}
            <div className="grid grid-cols-8 gap-1 mb-2">
              <div className="p-2 bg-muted rounded font-medium text-center text-sm">
                Time / Day
              </div>
              {days.map(day => (
                <div key={day} className="p-2 bg-muted rounded font-medium text-center text-sm">
                  {day}
                </div>
              ))}
            </div>

            {/* Timetable Grid */}
            <div className="space-y-1">
              {periods.map(period => (
                <div key={period} className="grid grid-cols-8 gap-1">
                  {/* Period Header */}
                  <div className={`p-2 rounded text-center text-sm font-medium ${
                    period === 'tea_break' || period === 'lunch_break'
                      ? 'bg-orange-100 text-orange-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    <div>
                      {period === 'tea_break' ? 'Tea' :
                       period === 'lunch_break' ? 'Lunch' :
                       `Period ${period}`}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {periodTimes[period]}
                    </div>
                  </div>

                  {/* Day Slots */}
                  {days.map(day => (
                    <div key={`${day}-${period}`} className="min-h-[80px]">
                      {getSlotContent(weeklyTimetable[day]?.[period] || null)}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-3">Legend</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded"></div>
              <span>Class Period</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-100 rounded"></div>
              <span>Tea Break</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-100 rounded"></div>
              <span>Lunch Break</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-100 rounded"></div>
              <span>Free Period</span>
            </div>
          </div>
        </div>

        {/* Summary */}
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium mb-2 text-blue-800">Weekly Summary</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-blue-600 font-medium">
                {Object.values(weeklyTimetable).reduce((total, day) =>
                  total + Object.values(day).filter(slot => slot && !slot.is_break).length, 0
                )}
              </span>
              <span className="text-blue-700 ml-1">Total Classes</span>
            </div>
            <div>
              <span className="text-blue-600 font-medium">
                {new Set(Object.values(weeklyTimetable).flatMap(day =>
                  Object.values(day).filter(slot => slot && !slot.is_break).map(slot => slot!.subject_code)
                )).size}
              </span>
              <span className="text-blue-700 ml-1">Subjects</span>
            </div>
            <div>
              <span className="text-blue-600 font-medium">
                {Object.values(weeklyTimetable).reduce((total, day) =>
                  total + Object.values(day).filter(slot => slot && !slot.is_break && slot.subject_type === 'theory').length, 0
                )}
              </span>
              <span className="text-blue-700 ml-1">Theory</span>
            </div>
            <div>
              <span className="text-blue-600 font-medium">
                {Object.values(weeklyTimetable).reduce((total, day) =>
                  total + Object.values(day).filter(slot => slot && !slot.is_break && slot.subject_type === 'lab').length, 0
                )}
              </span>
              <span className="text-blue-700 ml-1">Lab</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default FacultyTimetableGrid;
