/**
 * Debug script to investigate why BCS402 (Microcontrollers) IA marks are not showing up
 * in the Student Progress Card
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://milmyotuougemocvieof.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1MjE5NzQsImV4cCI6MjA1MDA5Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test configuration
const TEST_CONFIG = {
  studentUsn: '1KS23CS001',
  subjectCode: 'BCS402',
  academicYear: '2024-25'
};

async function debugBCS402IAMarks() {
  console.log('🔍 Debugging BCS402 (Microcontrollers) IA Marks Issue\n');
  console.log('Test Configuration:', TEST_CONFIG);

  try {
    // Step 1: Get student details
    console.log('\n📋 Step 1: Getting Student Details');
    const { data: studentData, error: studentError } = await supabase
      .from('class_students')
      .select('*')
      .eq('usn', TEST_CONFIG.studentUsn.toUpperCase())
      .single();

    if (studentError || !studentData) {
      console.log('❌ Student not found:', studentError?.message);
      return;
    }

    console.log('✅ Student found:', {
      id: studentData.id,
      name: studentData.student_name,
      department: studentData.department,
      semester: studentData.semester,
      section: studentData.section,
      academic_year: studentData.academic_year
    });

    // Step 2: Check for BCS402 IA records
    console.log('\n📋 Step 2: Checking BCS402 IA Records');
    const { data: iaRecords, error: iaError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', studentData.id)
      .eq('subject_code', 'BCS402');

    if (iaError) {
      console.log('❌ Error fetching IA records:', iaError.message);
    } else {
      console.log(`✅ Found ${iaRecords?.length || 0} BCS402 IA records`);
      
      if (iaRecords && iaRecords.length > 0) {
        iaRecords.forEach((record, index) => {
          console.log(`\n📝 IA Record ${index + 1}:`);
          console.log(`  - Subject Code: ${record.subject_code}`);
          console.log(`  - Department: ${record.department}`);
          console.log(`  - Semester: ${record.semester}`);
          console.log(`  - Section: ${record.section}`);
          console.log(`  - Academic Year: ${record.academic_year}`);
          console.log(`  - IA1 Marks: ${record.ia1_marks}`);
          console.log(`  - IA2 Marks: ${record.ia2_marks}`);
          console.log(`  - IA3 Marks: ${record.ia3_marks}`);
          console.log(`  - Assignment Marks: ${record.assignment_marks}`);
          console.log(`  - Created At: ${record.created_at}`);
          console.log(`  - Updated At: ${record.updated_at}`);
        });
      } else {
        console.log('❌ No BCS402 IA records found for this student');
      }
    }

    // Step 3: Check for any BCS402 variations
    console.log('\n📋 Step 3: Checking BCS402 Variations');
    const variations = ['BCS402', 'BCS402_THEORY', 'BCS402_LAB', 'bcs402', 'Bcs402'];
    
    for (const variation of variations) {
      const { data: varData, error: varError } = await supabase
        .from('internal_assessments')
        .select('subject_code, department, semester, section, academic_year, ia1_marks, ia2_marks, ia3_marks')
        .eq('student_id', studentData.id)
        .eq('subject_code', variation);

      if (!varError && varData && varData.length > 0) {
        console.log(`✅ Found records for variation "${variation}":`, varData.length);
        varData.forEach(record => {
          console.log(`  - ${record.subject_code}: ${record.department}-${record.semester}-${record.section} (${record.academic_year})`);
        });
      }
    }

    // Step 4: Check academic year mismatches
    console.log('\n📋 Step 4: Checking Academic Year Issues');
    const { data: allIARecords, error: allIAError } = await supabase
      .from('internal_assessments')
      .select('subject_code, academic_year, department, semester, section')
      .eq('student_id', studentData.id);

    if (!allIAError && allIARecords) {
      console.log(`📊 All IA records for student (${allIARecords.length} total):`);
      
      const academicYears = [...new Set(allIARecords.map(r => r.academic_year))];
      console.log(`📅 Academic years in IA records: ${academicYears.join(', ')}`);
      console.log(`📅 Student's academic year: ${studentData.academic_year}`);
      console.log(`📅 Test config academic year: ${TEST_CONFIG.academicYear}`);
      
      // Check if there are BCS402 records with different academic years
      const bcs402Records = allIARecords.filter(r => r.subject_code === 'BCS402');
      if (bcs402Records.length > 0) {
        console.log(`\n🔍 BCS402 records found with different academic years:`);
        bcs402Records.forEach(record => {
          console.log(`  - ${record.academic_year}: ${record.department}-${record.semester}-${record.section}`);
        });
      }
      
      allIARecords.forEach(record => {
        console.log(`  ${record.subject_code}: ${record.academic_year} (${record.department}-${record.semester}-${record.section})`);
      });
    }

    // Step 5: Check department case sensitivity issues
    console.log('\n📋 Step 5: Checking Department Case Sensitivity');
    const { data: deptRecords, error: deptError } = await supabase
      .from('internal_assessments')
      .select('subject_code, department, semester, section, academic_year')
      .eq('student_id', studentData.id)
      .eq('subject_code', 'BCS402');

    if (!deptError && deptRecords) {
      console.log(`🔍 BCS402 records by department format:`);
      deptRecords.forEach(record => {
        const matches = record.department === studentData.department.toLowerCase() &&
                       record.semester === studentData.semester &&
                       record.section === studentData.section;
        console.log(`  - Dept: "${record.department}" vs Student: "${studentData.department.toLowerCase()}" ${matches ? '✅' : '❌'}`);
      });
    }

    // Step 6: Test the service query logic
    console.log('\n📋 Step 6: Testing Service Query Logic');
    
    // Simulate the exact query used by StudentProgressService
    const department = studentData.department.toLowerCase(); // Service maps to lowercase
    const semester = studentData.semester;
    const section = studentData.section;
    const academicYear = TEST_CONFIG.academicYear;

    console.log(`🔍 Service query parameters:`);
    console.log(`  - Department: "${department}"`);
    console.log(`  - Semester: "${semester}"`);
    console.log(`  - Section: "${section}"`);
    console.log(`  - Academic Year: "${academicYear}"`);

    const { data: serviceQuery, error: serviceError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', studentData.id)
      .eq('department', department)
      .eq('semester', semester)
      .eq('academic_year', academicYear);

    if (!serviceError && serviceQuery) {
      console.log(`✅ Service query returned ${serviceQuery.length} records`);
      const bcs402ServiceRecord = serviceQuery.find(r => r.subject_code === 'BCS402');
      if (bcs402ServiceRecord) {
        console.log(`✅ BCS402 found in service query results`);
      } else {
        console.log(`❌ BCS402 NOT found in service query results`);
        console.log(`📋 Available subjects in service query:`, serviceQuery.map(r => r.subject_code));
      }
    } else {
      console.log(`❌ Service query failed:`, serviceError?.message);
    }

  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

// Run the debug script
debugBCS402IAMarks();
