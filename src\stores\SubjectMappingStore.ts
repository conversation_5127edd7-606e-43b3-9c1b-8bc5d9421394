
import { create } from 'zustand';

export interface MappingType {
  id: string;
  academicYear: string;
  department: string;
  semester: string;
  section: string;
  subject: {
    id: string;
    code: string;
    name: string;
    type: "theory" | "lab" | "elective" | "laboratory";
    shortId?: string;
  };
  // Add subject_type for compatibility with existing code
  subject_type?: string;
  faculty: {
    id: string;
    name: string;
  };
  faculty2Id?: string;  // Added to store the secondary faculty ID
  faculty2Name?: string;
  hoursPerWeek: number;
  classroom: string;
  slotsPerWeek?: number;
  labSlots?: Array<{
    day: string;
    timeOfDay: string;
    batch: string;
  }>;
}

// Update Faculty interface to allow optional properties for form state handling
export interface Faculty {
  id: string;
  name: string;
}

// Create a store for subject mappings
interface SubjectMappingState {
  mappings: MappingType[];
  setMappings: (mappings: MappingType[]) => void;
  addMapping: (mapping: MappingType) => void;
  updateMapping: (id: string, updates: Partial<MappingType>) => void;
  deleteMapping: (id: string) => void;
  getLabMappings: () => MappingType[];
  getTheoryMappings: () => MappingType[];
  getLaboratoryMappings: () => MappingType[];
}

export const useSubjectMappingStore = create<SubjectMappingState>((set, get) => ({
  mappings: [],
  setMappings: (mappings) => set({ mappings }),
  addMapping: (mapping) => set((state) => ({ 
    mappings: [...state.mappings, mapping] 
  })),
  updateMapping: (id, updates) => set((state) => ({
    mappings: state.mappings.map((mapping) => 
      mapping.id === id ? { ...mapping, ...updates } : mapping
    )
  })),
  deleteMapping: (id) => set((state) => ({
    mappings: state.mappings.filter((mapping) => mapping.id !== id)
  })),
  getLabMappings: () => {
    return get().mappings.filter(mapping => 
      mapping.subject.type === 'laboratory' || 
      mapping.subject.type === 'lab' || 
      mapping.subject_type === 'laboratory' || 
      mapping.subject_type === 'lab'
    );
  },
  getLaboratoryMappings: () => {
    return get().mappings.filter(mapping => 
      mapping.subject.type === 'laboratory' || 
      mapping.subject_type === 'laboratory'
    );
  },
  getTheoryMappings: () => {
    return get().mappings.filter(mapping => 
      mapping.subject.type === 'theory' || 
      mapping.subject_type === 'theory'
    );
  }
}));
