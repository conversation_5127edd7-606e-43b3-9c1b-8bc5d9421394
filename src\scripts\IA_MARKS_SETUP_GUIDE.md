# IA Marks Setup Guide

## Quick Setup for Student Progress Testing

To see IA marks in the Student Progress card, you need to add data to the `internal_assessments` table.

### Method 1: Direct SQL Insert (Recommended)

Copy and paste this SQL into your Supabase SQL editor:

```sql
INSERT INTO internal_assessments (
    student_id,
    subject_code,
    department,
    semester,
    section,
    academic_year,
    ia1_marks,
    ia2_marks,
    ia3_marks,
    assignment_marks,
    lab_marks
) VALUES 
-- BCS401 - ANALYSIS & DESIGN OF ALGORITHMS
('4b4b0737-262f-4bc5-bc11-a17b29b4ee54', 'BCS401', 'cse', '4', 'A', '2024-25', 22, 20, 24, 8, NULL),
-- BCS402 - MICROCONTROLLERS  
('4b4b0737-262f-4bc5-bc11-a17b29b4ee54', 'BCS402', 'cse', '4', 'A', '2024-25', 18, 21, 19, 7, NULL),
-- BCS403 - <PERSON><PERSON><PERSON><PERSON><PERSON> MANAGEMENT SYSTEMS
('4b4b0737-262f-4bc5-bc11-a17b29b4ee54', 'BCS403', 'cse', '4', 'A', '2024-25', 25, 23, 22, 9, NULL),
-- BCSL404 - ANALYSIS & DESIGN OF ALGORITHMS LAB
('4b4b0737-262f-4bc5-bc11-a17b29b4ee54', 'BCSL404', 'cse', '4', 'A', '2024-25', 20, 22, 21, 8, 18),
-- BCS405A - DISCRETE MATHEMATICAL STRUCTURES
('4b4b0737-262f-4bc5-bc11-a17b29b4ee54', 'BCS405A', 'cse', '4', 'A', '2024-25', 16, 19, 17, 6, NULL),
-- BBOK407 - BIOLOGY FOR ENGINEERS
('4b4b0737-262f-4bc5-bc11-a17b29b4ee54', 'BBOK407', 'cse', '4', 'A', '2024-25', 21, 20, 23, 9, NULL),
-- BCS456C - UI/UX
('4b4b0737-262f-4bc5-bc11-a17b29b4ee54', 'BCS456C', 'cse', '4', 'A', '2024-25', 19, 18, 20, 7, NULL),
-- BUHK408 - UNIVERSAL HUMAN VALUES COURSE
('4b4b0737-262f-4bc5-bc11-a17b29b4ee54', 'BUHK408', 'cse', '4', 'A', '2024-25', 24, 25, 23, 10, NULL);
```

### Method 2: Using Supabase Dashboard

1. Go to your Supabase dashboard
2. Navigate to Table Editor → `internal_assessments`
3. Click "Insert" → "Insert row"
4. Fill in the data manually for each subject

### Required Fields

| Field | Value | Description |
|-------|-------|-------------|
| `student_id` | `4b4b0737-262f-4bc5-bc11-a17b29b4ee54` | Student UUID from class_students |
| `subject_code` | `BCS401`, `BCS402`, etc. | Subject code (must match timetable) |
| `department` | `cse` | Department (lowercase) |
| `semester` | `4` | Semester number |
| `section` | `A` | Section letter |
| `academic_year` | `2024-25` | Academic year |
| `ia1_marks` | `0-25` | IA1 marks out of 25 |
| `ia2_marks` | `0-25` | IA2 marks out of 25 |
| `ia3_marks` | `0-25` | IA3 marks out of 25 |
| `assignment_marks` | `0-10` | Assignment marks out of 10 |
| `lab_marks` | `0-20` or `NULL` | Lab marks (only for lab subjects) |

### Expected Results

After adding the data, the Student Progress card will show:

- **IA1 Column**: Individual IA1 marks (e.g., "22/25")
- **IA2 Column**: Individual IA2 marks (e.g., "20/25") 
- **IA3 Column**: Individual IA3 marks (e.g., "24/25")
- **Assignment Column**: Assignment marks (e.g., "8/10")
- **Lab Column**: Lab marks for lab subjects (e.g., "18/20")
- **Total IA Column**: Sum of all marks
- **Grade Column**: Calculated grade based on performance

### Sample Expected Output

| Subject | IA1 | IA2 | IA3 | Assignment | Lab | Total | Grade |
|---------|-----|-----|-----|------------|-----|-------|-------|
| BCS401  | 22/25 | 20/25 | 24/25 | 8/10 | - | 74 | A |
| BCS403  | 25/25 | 23/25 | 22/25 | 9/10 | - | 79 | A |
| BCSL404 | 20/25 | 22/25 | 21/25 | 8/10 | 18/20 | 89 | A+ |

### Verification Query

To verify the data was inserted correctly:

```sql
SELECT 
    subject_code,
    ia1_marks,
    ia2_marks, 
    ia3_marks,
    assignment_marks,
    lab_marks,
    (COALESCE(ia1_marks, 0) + COALESCE(ia2_marks, 0) + COALESCE(ia3_marks, 0) + COALESCE(assignment_marks, 0) + COALESCE(lab_marks, 0)) as total_marks
FROM internal_assessments 
WHERE student_id = '4b4b0737-262f-4bc5-bc11-a17b29b4ee54'
ORDER BY subject_code;
```

### Troubleshooting

If IA marks still don't show:

1. **Check student_id**: Ensure it matches the UUID from class_students table
2. **Check department**: Must be lowercase 'cse', not 'CSE'
3. **Check academic_year**: Must match '2024-25' format
4. **Refresh browser**: Clear cache and refresh the Student Progress card

### Next Steps

1. Run the SQL insert statement above
2. Refresh the Student Progress card
3. Verify that IA marks now appear in all columns
4. Check that grades are calculated correctly
