
import React, { useEffect } from "react";
import { <PERSON>er, <PERSON>er<PERSON>ontent, <PERSON>er<PERSON>eader, DrawerTitle } from "@/components/ui/drawer";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { useIsMobile } from "@/hooks/use-mobile";
import { SubjectType } from "./useSubjectLoader";
import { Faculty } from "@/stores/SubjectMappingStore";
import { useSubjectMappingForm } from "@/hooks/useSubjectMappingForm";

// Import form components
import SubjectSelector from "./form/SubjectSelector";
import FacultyFields from "./form/FacultyFields";
import BasicInfoFields from "./form/BasicInfoFields";
import LabInfoFields from "./form/LabInfoFields";
import FormActions from "./form/FormActions";

interface Props {
  filtersValid: boolean;
  refetchMappings: () => Promise<void>;
  year: string;
  dept: string;
  sem: string;
  section: string;
  closeDrawer: () => void;
  open: boolean;
  subjects: SubjectType[];
  facultyList: Faculty[];
  onMappingSaved: () => void;
}

export default function ManualEntryForm({
  filtersValid,
  year,
  dept,
  sem,
  section,
  open,
  closeDrawer,
  subjects = [],
  facultyList = [],
  refetchMappings,
}: Props) {
  const isMobile = useIsMobile();
  const {
    form,
    saving,
    isLabSubject,
    hoursPerSlot,
    onSubmit
  } = useSubjectMappingForm({
    filtersValid,
    year,
    dept,
    sem,
    section,
    closeDrawer,
    refetchMappings,
    facultyList
  });

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const FormContent = (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 overflow-y-auto">
          <div className="space-y-4">
            <SubjectSelector form={form} subjects={subjects} disabled={saving} />
            <FacultyFields form={form} facultyList={facultyList} disabled={saving} />
            <BasicInfoFields form={form} disabled={saving} />
            <LabInfoFields
              form={form}
              hoursPerSlot={hoursPerSlot}
              disabled={saving}
              isLabSubject={isLabSubject}
              academicYear={year}
              department={dept}
              semester={sem}
            />
          </div>

          <FormActions
            onCancel={closeDrawer}
            saving={saving}
            filtersValid={filtersValid}
          />
        </form>
      </Form>
    </div>
  );

  if (isMobile) {
    return (
      <Drawer open={open} onOpenChange={(open) => !open && closeDrawer()}>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Add Subject Mapping</DrawerTitle>
          </DrawerHeader>
          <div className="px-4 pb-4">{FormContent}</div>
        </DrawerContent>
      </Drawer>
    );
  } else {
    return (
      <Dialog open={open} onOpenChange={(open) => !open && closeDrawer()}>
        <DialogContent className="max-h-[90vh] overflow-y-auto max-w-xl">
          <DialogHeader>
            <DialogTitle>Add Subject Mapping</DialogTitle>
          </DialogHeader>
          {FormContent}
        </DialogContent>
      </Dialog>
    );
  }
}
