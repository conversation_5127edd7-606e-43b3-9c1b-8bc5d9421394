import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import {
  Users,
  Trash2,
  RefreshCw,
  AlertTriangle,
  Phone,
  Mail,
  CheckSquare,
  Square,
  Loader2,
  Edit,
  UserX
} from 'lucide-react';
import { ClassStudentService, ClassStudent } from '@/services/ClassStudentService';
import EditStudentDialog from './EditStudentDialog';

interface ClassStudentListProps {
  facultyId: string;
  onRefresh?: () => void;
}

const ClassStudentList: React.FC<ClassStudentListProps> = ({ facultyId, onRefresh }) => {
  const [students, setStudents] = useState<ClassStudent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(new Set());
  const [deleting, setDeleting] = useState(false);
  const [editingStudent, setEditingStudent] = useState<ClassStudent | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [assignment, setAssignment] = useState<{
    department: string;
    semester: string;
    section: string;
  } | null>(null);
  const { toast } = useToast();

  // Load students and assignment
  const loadStudents = async () => {
    try {
      setLoading(true);
      
      // Get assignment first
      const assignmentData = await ClassStudentService.getClassTeacherAssignment(facultyId);
      setAssignment(assignmentData);
      
      if (!assignmentData) {
        setStudents([]);
        return;
      }

      // Get students
      const studentsData = await ClassStudentService.getStudentsForClassTeacher(facultyId);
      setStudents(studentsData);
      setSelectedStudents(new Set()); // Clear selection
    } catch (error) {
      console.error('Error loading students:', error);
      toast({
        title: 'Error',
        description: 'Failed to load student list. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (facultyId) {
      loadStudents();
    }
  }, [facultyId]);

  // Handle individual student selection
  const handleStudentSelect = (studentId: string, checked: boolean) => {
    const newSelection = new Set(selectedStudents);
    if (checked) {
      newSelection.add(studentId);
    } else {
      newSelection.delete(studentId);
    }
    setSelectedStudents(newSelection);
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedStudents(new Set(students.map(s => s.id)));
    } else {
      setSelectedStudents(new Set());
    }
  };

  // Handle edit student
  const handleEditStudent = (student: ClassStudent) => {
    setEditingStudent(student);
    setShowEditDialog(true);
  };

  // Handle edit success
  const handleEditSuccess = (updatedStudent: ClassStudent) => {
    setStudents(prev => prev.map(s => s.id === updatedStudent.id ? updatedStudent : s));
    setShowEditDialog(false);
    setEditingStudent(null);
    onRefresh?.();
  };

  // Handle delete individual student
  const handleDeleteStudent = async (student: ClassStudent) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete ${student.student_name} (${student.usn})? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      setDeleting(true);
      const result = await ClassStudentService.deleteStudent(student.id, facultyId);

      if (result.success) {
        toast({
          title: 'Success',
          description: `Successfully deleted ${student.student_name}.`,
        });
        // Remove student from local state
        setStudents(prev => prev.filter(s => s.id !== student.id));
        onRefresh?.();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to delete student.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting student:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete student. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setDeleting(false);
    }
  };

  // Delete selected students
  const handleDeleteSelected = async () => {
    if (selectedStudents.size === 0) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedStudents.size} selected student${selectedStudents.size > 1 ? 's' : ''}? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      setDeleting(true);
      const result = await ClassStudentService.deleteStudents(
        Array.from(selectedStudents),
        facultyId
      );

      if (result.errors.length > 0) {
        toast({
          title: 'Partial Success',
          description: `Deleted ${result.success} students. ${result.errors.length} errors occurred.`,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Success',
          description: `Successfully deleted ${result.success} student${result.success > 1 ? 's' : ''}.`,
        });
      }

      // Reload the list
      await loadStudents();
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting students:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete students. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setDeleting(false);
    }
  };

  // Delete all students
  const handleDeleteAll = async () => {
    if (students.length === 0) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete ALL ${students.length} students from your class? This action cannot be undone.`
    );

    if (!confirmed) return;

    // Double confirmation for delete all
    const doubleConfirmed = window.confirm(
      'This will permanently remove all student records from your class. Are you absolutely sure?'
    );

    if (!doubleConfirmed) return;

    try {
      setDeleting(true);
      const result = await ClassStudentService.deleteAllStudents(facultyId);

      if (result.errors.length > 0) {
        toast({
          title: 'Error',
          description: result.errors[0],
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Success',
          description: `Successfully deleted all ${result.success} students from your class.`,
        });
      }

      // Reload the list
      await loadStudents();
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting all students:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete all students. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setDeleting(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading student list...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show message if not a class teacher
  if (!assignment) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert>
            <Users className="h-4 w-4" />
            <AlertDescription>
              You are not assigned as a class teacher. Only class teachers can view and manage student lists.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const allSelected = students.length > 0 && selectedStudents.size === students.length;
  const someSelected = selectedStudents.size > 0 && selectedStudents.size < students.length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Class Student List
            </CardTitle>
            <CardDescription>
              {assignment.semester}{assignment.section} - {assignment.department.toUpperCase()} 
              ({students.length} student{students.length !== 1 ? 's' : ''})
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadStudents}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {selectedStudents.size > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDeleteSelected}
                disabled={deleting}
              >
                {deleting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4 mr-2" />
                )}
                Delete Selected ({selectedStudents.size})
              </Button>
            )}
            {students.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDeleteAll}
                disabled={deleting}
              >
                {deleting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <AlertTriangle className="h-4 w-4 mr-2" />
                )}
                Delete All
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {students.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Students Found</h3>
            <p className="text-muted-foreground mb-4">
              Your class doesn't have any students yet. Upload student data to get started.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Bulk Actions */}
            <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={allSelected}
                  ref={(el) => {
                    if (el) el.indeterminate = someSelected;
                  }}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm font-medium">
                  {allSelected ? 'Deselect All' : someSelected ? 'Select All' : 'Select All'}
                </span>
              </div>
              {selectedStudents.size > 0 && (
                <Badge variant="secondary">
                  {selectedStudents.size} of {students.length} selected
                </Badge>
              )}
            </div>

            {/* Students Table */}
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <span className="sr-only">Select</span>
                    </TableHead>
                    <TableHead>USN</TableHead>
                    <TableHead>Student Name</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Added</TableHead>
                    <TableHead className="w-32 text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {students.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedStudents.has(student.id)}
                          onCheckedChange={(checked) => 
                            handleStudentSelect(student.id, checked as boolean)
                          }
                        />
                      </TableCell>
                      <TableCell className="font-mono font-medium">
                        {student.usn}
                      </TableCell>
                      <TableCell className="font-medium">
                        {student.student_name}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {student.student_mobile && (
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3" />
                              <span>S: {student.student_mobile}</span>
                            </div>
                          )}
                          {student.father_mobile && (
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3" />
                              <span>F: {student.father_mobile}</span>
                            </div>
                          )}
                          {student.mother_mobile && (
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3" />
                              <span>M: {student.mother_mobile}</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {student.email && (
                          <div className="flex items-center gap-1 text-sm">
                            <Mail className="h-3 w-3" />
                            <span className="truncate max-w-[200px]">{student.email}</span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {new Date(student.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditStudent(student)}
                            disabled={deleting}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteStudent(student)}
                            disabled={deleting}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <UserX className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>

      {/* Edit Student Dialog */}
      <EditStudentDialog
        isOpen={showEditDialog}
        onClose={() => {
          setShowEditDialog(false);
          setEditingStudent(null);
        }}
        onSuccess={handleEditSuccess}
        student={editingStudent}
        facultyId={facultyId}
      />
    </Card>
  );
};

export default ClassStudentList;
