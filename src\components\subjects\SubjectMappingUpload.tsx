
// src/components/subjects/SubjectMappingUpload.tsx
import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import * as XLSX from "xlsx";
import { readFileAsArrayBuffer } from "@/utils/fileProcessing";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { MappingService, MappingRow, LabSlotRow } from "@/services/MappingService";

interface Props {
  onComplete: (firstRow: MappingRow) => void;
}

export default function SubjectMappingUpload({ onComplete }: Props) {
  const { toast } = useToast();
  const [busy, setBusy] = useState(false);
  const [progress, setProgress] = useState(0);

  const onDrop = useCallback(async (files: File[]) => {
    if (!files.length) return;
    setBusy(true);
    toast({ title: "Reading file…", variant: "default" });

    try {
      // 1) load buffer
      const buf = await readFileAsArrayBuffer(files[0]);

      // 2) parse workbook
      const wb = XLSX.read(buf, { type: "array" });
      const sheet = wb.Sheets[wb.SheetNames[0]];
      const raw: Record<string, any>[] = XLSX.utils.sheet_to_json(sheet, { defval: null });

      // 3) build MappingRow[] + LabSlotRow[]
      const rows: MappingRow[] = [];
      const slots: LabSlotRow[] = [];

      raw.forEach((r, idx) => {
        const mr: MappingRow = {
          academic_year:  r["Academic Year"],
          department:     r["Department"],
          semester:       r["Semester"],
          section:        r["Section"],
          subject_code:   r["Subject Code"],
          subject_name:   r["Subject Name"],
          subject_type:   (r["Subject Type"] as string).toLowerCase() as any,
          faculty1:       r["Faculty 1"],
          faculty2:       r["Faculty 2"] || undefined,
          hours_per_week: Number(r["Hours/Week"]),
          classroom:      r["Class Room"],
          slots_per_week: Number(r["Slots/Week"]) || undefined,
        };
        rows.push(mr);

        if (mr.subject_type === "laboratory" && mr.slots_per_week) {
          for (let slot = 1; slot <= mr.slots_per_week; slot++) {
            slots.push({
              parentRowIndex: idx,
              slot_order:     slot,
              batch_name:     r[`Batch Name ${slot}`],
              day:            r[`Day ${slot}`],
              time_of_day:    r[`Time of Day ${slot}`] as "Morning" | "Afternoon",
            });
          }
        }
      });

      console.log("📥 Importing", rows.length, "rows +", slots.length, "slots");
      await MappingService.importExcel(rows, slots);
      toast({ title: "Upload successful!", description: "Data imported successfully", variant: "default" });

      // 4) hand first row back so parent sets its filters & reloads
      onComplete(rows[0]);
    } catch (e: any) {
      console.error(e);
      toast({ title: "Upload failed", description: e.message, variant: "destructive" });
    } finally {
      setBusy(false);
      setProgress(0);
    }
  }, [toast, onComplete]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop, accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls', '.csv']
    }, 
    disabled: busy
  });

  return (
    <Card>
      <CardHeader><CardTitle>Upload Excel</CardTitle></CardHeader>
      <CardContent>
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded p-6 text-center ${
            isDragActive ? "bg-muted" : "cursor-pointer"
          }`}
        >
          <input {...getInputProps()} />
          {isDragActive
            ? "Drop file here…"
            : "Drag & drop Excel/CSV here, or click to browse"}
        </div>
        {busy && <Progress className="mt-4" value={progress} />}
      </CardContent>
    </Card>
  );
}
