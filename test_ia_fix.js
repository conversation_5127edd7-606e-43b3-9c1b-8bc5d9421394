import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://milmyotuougemocvieof.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NjI0NzQsImV4cCI6MjA1MDUzODQ3NH0.Ej5Ej6bJYJJhUvSJKJhUvSJKJhUvSJKJhUvSJKJhUvS';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testIAMarksFetch() {
  console.log('🧪 Testing IA marks fetch after fix...\n');

  try {
    // Test with a known student USN
    const testUSN = '1KS23CS001';
    console.log(`📋 Testing with student USN: ${testUSN}`);

    // Step 1: Get student info
    const { data: studentData, error: studentError } = await supabase
      .from('class_students')
      .select('id, usn, student_name, department, semester, section, academic_year')
      .eq('usn', testUSN)
      .single();

    if (studentError || !studentData) {
      console.log('❌ Student not found:', studentError?.message);
      return;
    }

    console.log('✅ Student found:', {
      id: studentData.id,
      name: studentData.student_name,
      department: studentData.department,
      semester: studentData.semester,
      section: studentData.section,
      academic_year: studentData.academic_year
    });

    // Step 2: Check IA marks with the fixed query
    console.log('\n🔍 Checking IA marks with fixed query...');
    
    // Try multiple academic year variations as the service does
    const academicYearVariations = [
      studentData.academic_year, // e.g., "2024-2025"
      "2025-2026", // Current year where IA data might be stored
      "2024-25",
      "2025-26"
    ];

    let iaData = null;
    let foundWithYear = null;

    for (const yearVariant of academicYearVariations) {
      console.log(`  Trying academic year: ${yearVariant}`);
      
      const { data: tempData, error: tempError } = await supabase
        .from('internal_assessments')
        .select('*')
        .eq('student_id', studentData.id)
        .eq('department', studentData.department.toLowerCase())
        .eq('semester', studentData.semester)
        .eq('academic_year', yearVariant);

      if (!tempError && tempData && tempData.length > 0) {
        iaData = tempData;
        foundWithYear = yearVariant;
        console.log(`  ✅ Found ${tempData.length} IA records with year: ${yearVariant}`);
        break;
      } else {
        console.log(`  📝 No records found with year: ${yearVariant}`);
      }
    }

    if (!iaData || iaData.length === 0) {
      console.log('\n❌ No IA records found with any year variation');
      
      // Try broader search
      console.log('🔍 Trying broader search (student_id only)...');
      const { data: broadData, error: broadError } = await supabase
        .from('internal_assessments')
        .select('*')
        .eq('student_id', studentData.id);

      if (!broadError && broadData && broadData.length > 0) {
        console.log(`✅ Found ${broadData.length} IA records with broader search:`);
        broadData.forEach(record => {
          console.log(`  - ${record.subject_code}: dept=${record.department}, sem=${record.semester}, year=${record.academic_year}`);
          console.log(`    IA1=${record.ia1_marks}, IA2=${record.ia2_marks}, IA3=${record.ia3_marks}, Assignment=${record.assignment_marks}, Theory=${record.theory_marks}`);
        });
      } else {
        console.log('❌ No IA records found even with broader search');
      }
      return;
    }

    // Step 3: Display the IA marks data
    console.log(`\n✅ IA Marks found (using academic year: ${foundWithYear}):`);
    console.log('=' .repeat(80));
    
    iaData.forEach(record => {
      console.log(`Subject: ${record.subject_code}`);
      console.log(`  IA1: ${record.ia1_marks || 'N/A'}/25`);
      console.log(`  IA2: ${record.ia2_marks || 'N/A'}/25`);
      console.log(`  IA3: ${record.ia3_marks || 'N/A'}/25`);
      console.log(`  Assignment: ${record.assignment_marks || 'N/A'}/10`);
      console.log(`  Theory: ${record.theory_marks || 'N/A'}/20`);
      console.log(`  Updated: ${record.updated_at}`);
      console.log('');
    });

    // Step 4: Test the data transformation
    console.log('🔄 Testing data transformation...');
    const transformedData = iaData.map(record => {
      const ia1 = record.ia1_marks !== null && record.ia1_marks !== undefined ? Number(record.ia1_marks) : 0;
      const ia2 = record.ia2_marks !== null && record.ia2_marks !== undefined ? Number(record.ia2_marks) : 0;
      const ia3 = record.ia3_marks !== null && record.ia3_marks !== undefined ? Number(record.ia3_marks) : 0;
      const assignment = record.assignment_marks !== null && record.assignment_marks !== undefined ? Number(record.assignment_marks) : 0;
      const theory = record.theory_marks !== null && record.theory_marks !== undefined ? Number(record.theory_marks) : 0;

      const totalMarks = ia1 + ia2 + ia3 + assignment + theory;
      const iaCount = [record.ia1_marks, record.ia2_marks, record.ia3_marks].filter(mark => mark !== null && mark !== undefined).length;
      const averageMarks = iaCount > 0 ? (ia1 + ia2 + ia3) / iaCount : 0;

      return {
        subject_code: record.subject_code,
        ia1_marks: record.ia1_marks !== null && record.ia1_marks !== undefined ? Number(record.ia1_marks) : undefined,
        ia2_marks: record.ia2_marks !== null && record.ia2_marks !== undefined ? Number(record.ia2_marks) : undefined,
        ia3_marks: record.ia3_marks !== null && record.ia3_marks !== undefined ? Number(record.ia3_marks) : undefined,
        assignment_marks: record.assignment_marks !== null && record.assignment_marks !== undefined ? Number(record.assignment_marks) : undefined,
        theory_marks: record.theory_marks !== null && record.theory_marks !== undefined ? Number(record.theory_marks) : undefined,
        total_marks: totalMarks,
        average_marks: Math.round(averageMarks * 100) / 100
      };
    });

    console.log('✅ Transformed data:');
    transformedData.forEach(data => {
      console.log(`  ${data.subject_code}: Total=${data.total_marks}, Average=${data.average_marks}`);
    });

    console.log('\n🎉 Test completed successfully! IA marks should now display correctly in Student Proctoring.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testIAMarksFetch();
