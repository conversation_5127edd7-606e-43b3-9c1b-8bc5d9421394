
import { But<PERSON> } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface DataTablePaginationProps {
  currentPage: number;
  setCurrentPage: (page: number) => void;
  pageSize: number;
  setPageSize: (size: number) => void;
  totalItems: number;
  pageCount: number;
}

export function DataTablePagination({
  currentPage,
  setCurrentPage,
  pageSize,
  setPageSize,
  totalItems,
  pageCount,
}: DataTablePaginationProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex-1 text-sm text-muted-foreground">
        Showing {Math.min(totalItems, 1 + (currentPage - 1) * pageSize)}-
        {Math.min(currentPage * pageSize, totalItems)} of {totalItems}
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Rows per page</p>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => {
              setPageSize(Number(value));
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {[5, 10, 20, 30, 40, 50].map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => {
              // Fix: Calculate the new page number directly instead of using a function
              const newPage = Math.max(currentPage - 1, 1);
              setCurrentPage(newPage);
            }}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to previous page</span>
            <span>←</span>
          </Button>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {currentPage} of {pageCount}
          </div>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => {
              // Fix: Calculate the new page number directly instead of using a function
              const newPage = Math.min(currentPage + 1, pageCount);
              setCurrentPage(newPage);
            }}
            disabled={currentPage === pageCount}
          >
            <span className="sr-only">Go to next page</span>
            <span>→</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
