import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON>ton } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, Clock } from "lucide-react";
import {
  SemesterConfigurationService,
  TheoryGenerationConfig,
  SemesterConfiguration
} from '@/services/SemesterConfigurationService';

interface TheoryGenerationConfigPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (config: TheoryGenerationConfig) => void;
  academicYear: string;
  department: string;
  semester: string;
  section: string;
}

export const TheoryGenerationConfigPopup: React.FC<TheoryGenerationConfigPopupProps> = ({
  isOpen,
  onClose,
  onConfirm,
  academicYear,
  department,
  semester,
  section
}) => {
  const [hasTutorialHours, setHasTutorialHours] = useState<string>('no');
  const [tutorialPeriodsPerWeek, setTutorialPeriodsPerWeek] = useState<number>(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [existingConfig, setExistingConfig] = useState<SemesterConfiguration | null>(null);

  // Load existing configuration when popup opens
  useEffect(() => {
    if (isOpen) {
      loadExistingConfiguration();
    }
  }, [isOpen, academicYear, department, semester, section]);

  const loadExistingConfiguration = async () => {
    try {
      const config = await SemesterConfigurationService.getConfiguration(
        academicYear,
        department,
        semester,
        section
      );

      setExistingConfig(config);

      if (config) {
        setHasTutorialHours(config.has_tutorial_hours ? 'yes' : 'no');
        setTutorialPeriodsPerWeek(config.tutorial_periods_per_week || 1);
      } else {
        // Reset to defaults
        setHasTutorialHours('no');
        setTutorialPeriodsPerWeek(1);
      }
    } catch (error) {
      console.error('Error loading configuration:', error);
    }
  };

  const handleConfirm = async () => {
    setError('');
    setIsLoading(true);

    try {
      const config: TheoryGenerationConfig = {
        hasTutorialHours: hasTutorialHours === 'yes',
        tutorialPeriodsPerWeek: hasTutorialHours === 'yes' ? tutorialPeriodsPerWeek : 0
      };

      // Validate configuration
      const validation = SemesterConfigurationService.validateTheoryConfig(config);
      if (!validation.isValid) {
        setError(validation.message || 'Invalid configuration');
        setIsLoading(false);
        return;
      }

      // Save configuration to database (merge with existing lab config)
      const semesterConfig: SemesterConfiguration = {
        academic_year: academicYear,
        department: department,
        semester: semester,
        section: section,
        has_skill_lab: existingConfig?.has_skill_lab || false,
        skill_lab_periods_per_week: existingConfig?.skill_lab_periods_per_week || 0,
        skill_lab_placement_preference: existingConfig?.skill_lab_placement_preference || 'vacant_days',
        skill_lab_preferred_day: existingConfig?.skill_lab_preferred_day,
        has_tutorial_hours: config.hasTutorialHours,
        tutorial_periods_per_week: config.tutorialPeriodsPerWeek
      };

      await SemesterConfigurationService.saveConfiguration(semesterConfig);

      // Call the confirm callback
      onConfirm(config);
      onClose();
    } catch (error) {
      console.error('Error saving configuration:', error);
      setError('Failed to save configuration. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setError('');
    onClose();
  };

  const getTutorialDaysPreview = (): string[] => {
    if (hasTutorialHours === 'no') return [];
    return SemesterConfigurationService.getSimpleTutorialPlacementDays(tutorialPeriodsPerWeek);
  };

  const tutorialDaysPreview = getTutorialDaysPreview();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Tutorial Configuration for CSE Semester {semester}, Section {section}
          </DialogTitle>
          <DialogDescription>
            Configure tutorial hours for this class before generating theory slots.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Tutorial Hours Question */}
          <div className="space-y-3">
            <Label className="text-base font-medium">
              Does this class have tutorial hours?
            </Label>
            <RadioGroup
              value={hasTutorialHours}
              onValueChange={setHasTutorialHours}
              className="flex space-x-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="tutorial-yes" />
                <Label htmlFor="tutorial-yes" className="cursor-pointer">Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="tutorial-no" />
                <Label htmlFor="tutorial-no" className="cursor-pointer">No</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Tutorial Configuration (shown only if Yes) */}
          {hasTutorialHours === 'yes' && (
            <div className="space-y-4 p-4 bg-green-50 rounded-lg border">
              <h4 className="font-medium text-green-900">Tutorial Configuration</h4>

              {/* Periods per week */}
              <div className="space-y-2">
                <Label htmlFor="tutorial-periods-per-week">How many tutorial periods per week?</Label>
                <Select
                  value={tutorialPeriodsPerWeek.toString()}
                  onValueChange={(value) => setTutorialPeriodsPerWeek(parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select tutorial periods per week" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 period</SelectItem>
                    <SelectItem value="2">2 periods</SelectItem>
                    <SelectItem value="3">3 periods</SelectItem>
                    <SelectItem value="4">4 periods</SelectItem>
                    <SelectItem value="5">5 periods</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Tutorial Days Preview */}
              {tutorialDaysPreview.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-green-800">
                    Tutorial slots will be placed on:
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {tutorialDaysPreview.map((day, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-green-200 text-green-800 rounded-md text-sm"
                      >
                        {day}
                      </span>
                    ))}
                  </div>
                  <p className="text-xs text-green-700">
                    All tutorial slots will be in Period 7 (15:05-16:00)
                  </p>
                </div>
              )}

              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  Tutorial slots will be automatically placed in Period 7 (15:05-16:00, last period)
                  on days without skill lab conflicts and will be assigned to "Unknown Faculty".
                </AlertDescription>
              </Alert>

              {/* Validation warning */}
              {tutorialPeriodsPerWeek > 5 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Tutorial periods cannot exceed 5 (maximum weekdays available).
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Information about Period 7 usage */}
          {hasTutorialHours === 'no' && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Without tutorial hours, Period 7 (15:05-16:00) will be available for theory slots if needed.
              </AlertDescription>
            </Alert>
          )}

          {/* Error message */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Continue with Theory Generation
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
