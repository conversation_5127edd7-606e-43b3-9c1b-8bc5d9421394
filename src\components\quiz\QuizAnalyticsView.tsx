import React from 'react';
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BarChart3, Users, TrendingUp, Clock, Award, Target } from 'lucide-react';
import { QuizAnalytics, QuizSchedule } from '@/types/quiz-system';

interface QuizAnalyticsViewProps {
  analytics: QuizAnalytics[];
  schedules: QuizSchedule[];
}

const QuizAnalyticsView: React.FC<QuizAnalyticsViewProps> = ({
  analytics,
  schedules
}) => {
  const totalStudents = analytics.reduce((sum, a) => sum + a.total_students, 0);
  const totalAttempts = analytics.reduce((sum, a) => sum + a.attempted_students, 0);
  const avgScore = analytics.length > 0 
    ? analytics.reduce((sum, a) => sum + a.average_score, 0) / analytics.length 
    : 0;
  const avgCompletion = analytics.length > 0 
    ? analytics.reduce((sum, a) => sum + a.completion_rate, 0) / analytics.length 
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold">Quiz Analytics</h3>
        <p className="text-sm text-muted-foreground">
          Track student performance and quiz effectiveness
        </p>
      </div>

      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStudents}</div>
            <p className="text-xs text-muted-foreground">
              Across all quizzes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Attempts</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAttempts}</div>
            <p className="text-xs text-muted-foreground">
              Quiz submissions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Score</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgScore.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Across all quizzes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgCompletion.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Average completion
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quiz Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Quiz Performance</CardTitle>
          <CardDescription>
            Detailed analytics for each quiz
          </CardDescription>
        </CardHeader>
        <CardContent>
          {analytics.length > 0 ? (
            <div className="space-y-4">
              {analytics.map((quiz, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium">{quiz.quiz_title}</h4>
                    <Badge variant="outline">
                      {quiz.completion_rate.toFixed(1)}% completion
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Students</p>
                      <p className="font-medium">{quiz.attempted_students}/{quiz.total_students}</p>
                    </div>
                    
                    <div>
                      <p className="text-muted-foreground">Average Score</p>
                      <p className="font-medium">{quiz.average_score.toFixed(1)}%</p>
                    </div>
                    
                    <div>
                      <p className="text-muted-foreground">Highest Score</p>
                      <p className="font-medium">{quiz.highest_score.toFixed(1)}%</p>
                    </div>
                    
                    <div>
                      <p className="text-muted-foreground">Lowest Score</p>
                      <p className="font-medium">{quiz.lowest_score.toFixed(1)}%</p>
                    </div>
                    
                    <div>
                      <p className="text-muted-foreground">Completion</p>
                      <p className="font-medium">{quiz.completion_rate.toFixed(1)}%</p>
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mt-3">
                    <div className="flex justify-between text-xs text-muted-foreground mb-1">
                      <span>Completion Progress</span>
                      <span>{quiz.attempted_students}/{quiz.total_students}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${quiz.completion_rate}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-muted-foreground">No quiz analytics available yet.</p>
              <p className="text-sm text-muted-foreground">
                Analytics will appear here once students start taking quizzes.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Quiz Activity</CardTitle>
          <CardDescription>
            Latest quiz schedules and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {schedules.length > 0 ? (
            <div className="space-y-3">
              {schedules.slice(0, 5).map((schedule) => (
                <div key={schedule.id} className="flex items-center justify-between p-3 border rounded">
                  <div>
                    <p className="font-medium">{schedule.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {schedule.target_department} - Semester {schedule.target_semester}
                      {schedule.target_section && ` Section ${schedule.target_section}`}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(schedule.start_time).toLocaleDateString()}
                    </p>
                  </div>
                  <Badge variant={
                    schedule.status === 'active' ? 'default' :
                    schedule.status === 'scheduled' ? 'secondary' :
                    schedule.status === 'completed' ? 'outline' : 'destructive'
                  }>
                    {schedule.status}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-muted-foreground">No recent quiz activity.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default QuizAnalyticsView;
