
import * as XLSX from 'xlsx';
import { Employee } from '@/services/UserService';
import { readFileAsArrayBuffer, readFileAsText, getFileType } from './fileCore';

/**
 * Helper function to find keys in an object that might match a pattern
 */
export const findMatchingKeys = (obj: Record<string, any>, patterns: string[]): string[] => {
  return Object.keys(obj).filter(key => {
    return patterns.some(pattern => {
      // First try exact match
      if (key === pattern) return true;
      
      // Then try case-insensitive matches
      if (key.toLowerCase() === pattern.toLowerCase()) return true;
      
      // Then try contains match
      if (key.toLowerCase().includes(pattern.toLowerCase())) return true;
      
      return false;
    });
  });
};

/**
 * Finds a value in a row based on multiple possible column names
 */
export const findValue = (row: Record<string, any>, possibleKeys: string[]): string => {
  for (const key of possibleKeys) {
    if (row[key] !== undefined && row[key] !== null) {
      return String(row[key]);
    }
  }
  return '';
};

/**
 * Maps raw data to employee structure with flexible field mapping
 */
export const mapDataToEmployees = (rawData: Record<string, any>[]): Partial<Employee>[] => {
  return rawData.map(row => {
    // Try to match column names with various possible formats
    const fullName = findValue(row, [
      'full_name', 'fullname', 'Full Name', 'FullName', 'name', 'Name', 'employee_name'
    ]);
    
    const email = findValue(row, [
      'email', 'Email', 'EmailId', 'Email Id', 'email_id', 'mail'
    ]);
    
    const department = findValue(row, [
      'department', 'Department', 'dept', 'Dept', 'department_name'
    ]);
    
    const designation = findValue(row, [
      'designation', 'Designation', 'position', 'Position', 'role', 'Role', 'job_title'
    ]);
    
    const phone = findValue(row, [
      'phone', 'Phone', 'mobile', 'Mobile', 'phone_number', 'Phone Number', 'contact'
    ]);
    
    return {
      full_name: fullName,
      email: email,
      department: department,
      designation: designation,
      phone: phone,
      roles: ['faculty'], // Default role
    };
  });
};

/**
 * Find column values for a specific set of key patterns
 * This helps identify batch data from the Excel sheet by examining different column name formats
 */
export const findColumnValues = (
  row: Record<string, any>, 
  patterns: string[]
): { key: string, value: any } | null => {
  const keys = findMatchingKeys(row, patterns);
  if (keys.length > 0) {
    return { key: keys[0], value: row[keys[0]] };
  }
  return null;
};

/**
 * Process Excel file and convert to employee data
 */
export const processExcelFile = async (file: File): Promise<Partial<Employee>[]> => {
  const data = await readFileAsArrayBuffer(file);
  const workbook = XLSX.read(data, { type: 'array' });
  const sheet = workbook.Sheets[workbook.SheetNames[0]];
  const rawData = XLSX.utils.sheet_to_json<Record<string, any>>(sheet, { defval: null });
  
  console.log("Raw Excel data:", rawData);
  
  // Map Excel columns to our Employee interface with flexible column name matching
  return mapDataToEmployees(rawData);
};

/**
 * Process JSON file and convert to employee data
 */
export const processJsonFile = async (file: File): Promise<Partial<Employee>[]> => {
  const text = await readFileAsText(file);
  const jsonData = JSON.parse(text);
  
  if (Array.isArray(jsonData)) {
    return mapDataToEmployees(jsonData);
  }
  return [];
};

/**
 * Process CSV file and convert to employee data
 */
export const processCsvFile = async (file: File): Promise<Partial<Employee>[]> => {
  const text = await readFileAsText(file);
  const workbook = XLSX.read(text, { type: 'string' });
  const sheet = workbook.Sheets[workbook.SheetNames[0]];
  const rawData = XLSX.utils.sheet_to_json<Record<string, any>>(sheet, { defval: null });
  
  console.log("Raw CSV data:", rawData);
  
  return mapDataToEmployees(rawData);
};

/**
 * Process file based on file type for employee data
 */
export const processFile = async (file: File): Promise<Partial<Employee>[]> => {
  const fileType = getFileType(file.name);
  
  if (fileType === 'excel') {
    return processExcelFile(file);
  } else if (fileType === 'json') {
    return processJsonFile(file);
  } else if (fileType === 'csv') {
    return processCsvFile(file);
  }
  
  throw new Error('Unsupported file type');
};

/**
 * Validates employee data and filters out invalid entries
 */
export const validateEmployeeData = (data: Partial<Employee>[]): Partial<Employee>[] => {
  // Filter out entries with missing required fields
  return data.filter(item => item.full_name && item.email);
};
