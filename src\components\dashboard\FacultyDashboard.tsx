import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Calendar,
  ClipboardCheck,
  Users,
  BookOpen,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Building2,
  GraduationCap,
  UserCheck
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import DashboardCard from './DashboardCard';
import StudentProctoring from '../proctoring/StudentProctoring';

interface FacultyStats {
  totalSubjects: number;
  totalStudents: number;
  attendanceMarked: number;
  iaCompleted: number;
  classesScheduled: number;
  upcomingClasses: number;
}

const FacultyDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState<FacultyStats>({
    totalSubjects: 0,
    totalStudents: 0,
    attendanceMarked: 0,
    iaCompleted: 0,
    classesScheduled: 0,
    upcomingClasses: 0
  });
  const [loading, setLoading] = useState(true);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const { user } = useAuth();
  const { department, departmentName, fullName } = useUserDepartment();
  const navigate = useNavigate();

  // Check if user has class teacher role
  const isClassTeacher = userRoles.includes('class_teacher');

  useEffect(() => {
    if (user?.id && department) {
      fetchUserRoles();
      fetchFacultyStats();
    }
  }, [user?.id, department]);

  const fetchUserRoles = async () => {
    if (!user?.id) return;

    try {
      const { data, error } = await supabase
        .from('employee_details')
        .select('roles')
        .eq('id', user.id)
        .single();

      if (data?.roles) {
        setUserRoles(data.roles);
      }
    } catch (error) {
      console.error('Error fetching user roles:', error);
    }
  };

  const fetchFacultyStats = async () => {
    if (!user?.id || !department) return;

    try {
      setLoading(true);

      // Get subjects taught by this faculty
      const { data: subjects, error: subjectsError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('subject_code, semester, section')
        .eq('faculty_1_id', user.id)
        .eq('department', department);

      if (subjectsError) throw subjectsError;

      const totalSubjects = subjects?.length || 0;

      // Calculate total students across all subjects
      let totalStudents = 0;
      if (subjects && subjects.length > 0) {
        for (const subject of subjects) {
          const { data: students } = await supabase
            .from('students')
            .select('id')
            .eq('department', department)
            .eq('semester', subject.semester)
            .eq('section', subject.section)
            .eq('is_active', true);

          totalStudents += students?.length || 0;
        }
      }

      // Get attendance records count
      const { data: attendanceRecords } = await supabase
        .from('attendance')
        .select('id')
        .eq('faculty_id', user.id);

      // Get IA records count
      const { data: iaRecords } = await supabase
        .from('internal_assessments')
        .select('id')
        .eq('faculty_id', user.id);

      // Get today's classes from timetable
      const today = new Date();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const todayName = dayNames[today.getDay()];

      const { data: todayClasses } = await supabase
        .from('timetable_slots')
        .select('id')
        .eq('faculty_id', user.id)
        .eq('day', todayName);

      setStats({
        totalSubjects,
        totalStudents,
        attendanceMarked: attendanceRecords?.length || 0,
        iaCompleted: iaRecords?.length || 0,
        classesScheduled: todayClasses?.length || 0,
        upcomingClasses: todayClasses?.length || 0
      });

    } catch (error) {
      console.error('Error fetching faculty stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: "Mark Attendance",
      description: "Mark attendance for today's classes",
      icon: Calendar,
      action: () => navigate('/attendance'),
      color: "text-blue-600 bg-blue-50"
    },
    {
      title: "Enter IA Marks",
      description: "Enter Internal Assessment marks",
      icon: ClipboardCheck,
      action: () => navigate('/internal-assessment'),
      color: "text-green-600 bg-green-50"
    }
  ];

  // Add class teacher specific actions
  if (isClassTeacher) {
    quickActions.push(
      {
        title: "Manage Students",
        description: "Manage student information",
        icon: Users,
        action: () => navigate('/students'),
        color: "text-purple-600 bg-purple-50"
      },
      {
        title: "Academic Reports",
        description: "Generate academic reports",
        icon: TrendingUp,
        action: () => navigate('/academic-reports'),
        color: "text-orange-600 bg-orange-50"
      }
    );
  }

  return (
    <div className="space-y-6 p-6 pb-16">
      <div className="space-y-0.5">
        <h2 className="text-2xl font-bold tracking-tight">Faculty Dashboard</h2>
        <p className="text-muted-foreground">
          Welcome back, {fullName}
        </p>
      </div>

      {/* Department Context */}
      <Alert className="border-blue-200 bg-blue-50">
        <Building2 className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Department:</strong> {departmentName}
          {isClassTeacher && <span className="ml-4"><strong>Role:</strong> Faculty & Class Teacher</span>}
        </AlertDescription>
      </Alert>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="schedule">Today's Schedule</TabsTrigger>
          <TabsTrigger value="quick-actions">Quick Actions</TabsTrigger>
          <TabsTrigger value="student-proctoring">Student Proctoring</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Metrics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <DashboardCard
              title="Subjects Teaching"
              value={loading ? "..." : stats.totalSubjects}
              icon={BookOpen}
              description="Active subject assignments"
              colorClass="text-primary bg-primary/10"
            />
            <DashboardCard
              title="Total Students"
              value={loading ? "..." : stats.totalStudents}
              icon={GraduationCap}
              description="Students across all subjects"
              colorClass="text-accent bg-accent/10"
            />
            <DashboardCard
              title="Attendance Marked"
              value={loading ? "..." : stats.attendanceMarked}
              icon={CheckCircle}
              description="Total attendance records"
              colorClass="text-secondary bg-secondary/10"
            />
            <DashboardCard
              title="IA Records"
              value={loading ? "..." : stats.iaCompleted}
              icon={ClipboardCheck}
              description="Internal assessment entries"
              colorClass="text-orange-600 bg-orange-50"
            />
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <div>
                    <p className="text-sm font-medium">Attendance marked for CS601</p>
                    <p className="text-xs text-gray-600">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <ClipboardCheck className="w-4 h-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium">IA marks updated for CS602</p>
                    <p className="text-xs text-gray-600">1 day ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Today's Classes
              </CardTitle>
              <CardDescription>
                Your scheduled classes for today
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Schedule Integration</h3>
                <p className="text-gray-600 mb-4">
                  Your daily schedule will be displayed here based on the timetable system.
                </p>
                <Button onClick={() => navigate('/attendance')}>
                  Mark Attendance
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quick-actions" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {quickActions.map((action, index) => (
              <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow" onClick={action.action}>
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className={`p-3 rounded-full ${action.color}`}>
                      <action.icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-medium">{action.title}</h3>
                      <p className="text-sm text-muted-foreground">{action.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="student-proctoring" className="space-y-4">
          <StudentProctoring />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FacultyDashboard;
