import React, { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, Clock } from "lucide-react";
import {
  SemesterConfigurationService,
  LabGenerationConfig,
  SemesterConfiguration
} from '@/services/SemesterConfigurationService';

interface LabGenerationConfigPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (config: LabGenerationConfig) => void;
  academicYear: string;
  department: string;
  semester: string;
  section: string;
}

export const LabGenerationConfigPopup: React.FC<LabGenerationConfigPopupProps> = ({
  isOpen,
  onClose,
  onConfirm,
  academicYear,
  department,
  semester,
  section
}) => {
  const [hasSkillLab, setHasSkillLab] = useState<string>('no');
  const [skillLabPeriodsPerWeek, setSkillLabPeriodsPerWeek] = useState<number>(1);
  const [skillLabPlacement, setSkillLabPlacement] = useState<'vacant_days' | 'least_labs_day' | 'specific_day'>('vacant_days');
  const [skillLabPreferredDay, setSkillLabPreferredDay] = useState<string>('');
  const [hasTutorialHours, setHasTutorialHours] = useState<string>('no');
  const [tutorialPeriodsPerWeek, setTutorialPeriodsPerWeek] = useState<number>(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Load existing configuration when popup opens
  useEffect(() => {
    if (isOpen) {
      loadExistingConfiguration();
    }
  }, [isOpen, academicYear, department, semester, section]);

  const loadExistingConfiguration = async () => {
    try {
      const config = await SemesterConfigurationService.getConfiguration(
        academicYear,
        department,
        semester,
        section
      );

      if (config) {
        setHasSkillLab(config.has_skill_lab ? 'yes' : 'no');
        setSkillLabPeriodsPerWeek(config.skill_lab_periods_per_week || 1);
        setSkillLabPlacement(config.skill_lab_placement_preference || 'vacant_days');
        setSkillLabPreferredDay(config.skill_lab_preferred_day || '');
        setHasTutorialHours(config.has_tutorial_hours ? 'yes' : 'no');
        setTutorialPeriodsPerWeek(config.tutorial_periods_per_week || 1);
      } else {
        // Reset to defaults
        setHasSkillLab('no');
        setSkillLabPeriodsPerWeek(1);
        setSkillLabPlacement('vacant_days');
        setSkillLabPreferredDay('');
        setHasTutorialHours('no');
        setTutorialPeriodsPerWeek(1);
      }
    } catch (error) {
      console.error('Error loading configuration:', error);
    }
  };

  const handleConfirm = async () => {
    setError('');
    setIsLoading(true);

    try {
      const config: LabGenerationConfig = {
        hasSkillLab: hasSkillLab === 'yes',
        skillLabPeriodsPerWeek: hasSkillLab === 'yes' ? skillLabPeriodsPerWeek : 0,
        skillLabPlacement: skillLabPlacement,
        skillLabPreferredDay: skillLabPlacement === 'specific_day' ? skillLabPreferredDay : undefined,
        hasTutorialHours: hasTutorialHours === 'yes',
        tutorialPeriodsPerWeek: hasTutorialHours === 'yes' ? tutorialPeriodsPerWeek : 0
      };

      // Validate configuration
      const validation = SemesterConfigurationService.validateLabConfig(config);
      if (!validation.isValid) {
        setError(validation.message || 'Invalid configuration');
        setIsLoading(false);
        return;
      }

      // Save configuration to database
      const semesterConfig: SemesterConfiguration = {
        academic_year: academicYear,
        department: department,
        semester: semester,
        section: section,
        has_skill_lab: config.hasSkillLab,
        skill_lab_periods_per_week: config.skillLabPeriodsPerWeek,
        skill_lab_placement_preference: config.skillLabPlacement,
        skill_lab_preferred_day: config.skillLabPreferredDay,
        has_tutorial_hours: config.hasTutorialHours,
        tutorial_periods_per_week: config.tutorialPeriodsPerWeek
      };

      await SemesterConfigurationService.saveConfiguration(semesterConfig);

      // Call the confirm callback
      onConfirm(config);
      onClose();
    } catch (error) {
      console.error('Error saving configuration:', error);
      setError('Failed to save configuration. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setError('');
    onClose();
  };

  const weekDays = [
    { value: 'Monday', label: 'Monday' },
    { value: 'Tuesday', label: 'Tuesday' },
    { value: 'Wednesday', label: 'Wednesday' },
    { value: 'Thursday', label: 'Thursday' },
    { value: 'Friday', label: 'Friday' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[480px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg font-semibold text-gray-900">
            Lab & Tutorial Configuration
          </DialogTitle>
          <DialogDescription className="text-sm text-gray-600">
            Configure settings for Semester {semester}, Section {section}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Skill Lab Section */}
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center justify-between mb-3">
              <Label className="text-sm font-medium text-blue-900">Skill Lab Sessions</Label>
              <RadioGroup
                value={hasSkillLab}
                onValueChange={setHasSkillLab}
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="skill-lab-yes" />
                  <Label htmlFor="skill-lab-yes" className="text-sm cursor-pointer">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="skill-lab-no" />
                  <Label htmlFor="skill-lab-no" className="text-sm cursor-pointer">No</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Skill Lab Configuration (shown only if Yes) */}
            {hasSkillLab === 'yes' && (
              <div className="space-y-3 mt-3 pt-3 border-t border-blue-300">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label className="text-xs text-blue-700 mb-1 block">Periods per week</Label>
                    <Select
                      value={skillLabPeriodsPerWeek.toString()}
                      onValueChange={(value) => setSkillLabPeriodsPerWeek(parseInt(value))}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 period</SelectItem>
                        <SelectItem value="2">2 periods</SelectItem>
                        <SelectItem value="3">3 periods</SelectItem>
                        <SelectItem value="4">4 periods</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-xs text-blue-700 mb-1 block">Placement strategy</Label>
                    <Select
                      value={skillLabPlacement}
                      onValueChange={(value: 'vacant_days' | 'least_labs_day' | 'specific_day') => setSkillLabPlacement(value)}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="vacant_days">Vacant days</SelectItem>
                        <SelectItem value="least_labs_day">Least labs day</SelectItem>
                        <SelectItem value="specific_day">Specific day</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Specific day selector (shown only if specific day is selected) */}
                {skillLabPlacement === 'specific_day' && (
                  <div>
                    <Label className="text-xs text-blue-700 mb-1 block">Preferred day</Label>
                    <Select
                      value={skillLabPreferredDay}
                      onValueChange={setSkillLabPreferredDay}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent>
                        {weekDays.map((day) => (
                          <SelectItem key={day.value} value={day.value}>
                            {day.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded">
                  Placed in afternoon periods (13:15-16:00) • No faculty assignment
                </div>
              </div>
            )}
          </div>

          {/* Tutorial Section */}
          <div className="bg-green-50 rounded-lg p-4 border border-green-200">
            <div className="flex items-center justify-between mb-3">
              <Label className="text-sm font-medium text-green-900">Tutorial Hours</Label>
              <RadioGroup
                value={hasTutorialHours}
                onValueChange={setHasTutorialHours}
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="tutorial-yes" />
                  <Label htmlFor="tutorial-yes" className="text-sm cursor-pointer">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="tutorial-no" />
                  <Label htmlFor="tutorial-no" className="text-sm cursor-pointer">No</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Tutorial Configuration (shown only if Yes) */}
            {hasTutorialHours === 'yes' && (
              <div className="space-y-3 mt-3 pt-3 border-t border-green-300">
                <div>
                  <Label className="text-xs text-green-700 mb-1 block">Periods per week</Label>
                  <Select
                    value={tutorialPeriodsPerWeek.toString()}
                    onValueChange={(value) => setTutorialPeriodsPerWeek(parseInt(value))}
                  >
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 period</SelectItem>
                      <SelectItem value="2">2 periods</SelectItem>
                      <SelectItem value="3">3 periods</SelectItem>
                      <SelectItem value="4">4 periods</SelectItem>
                      <SelectItem value="5">5 periods</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-xs text-green-600 bg-green-100 p-2 rounded">
                  Placed in Period 7 (last period) • Assigned to "Unknown Faculty"
                </div>

                {/* Validation warning */}
                {tutorialPeriodsPerWeek > 5 && (
                  <div className="text-xs text-red-600 bg-red-100 p-2 rounded border border-red-200">
                    ⚠️ Cannot exceed 5 periods (maximum weekdays available)
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Error message */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded border border-red-200">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{error}</span>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="pt-4 border-t">
          <div className="flex space-x-3 w-full">
            <Button variant="outline" onClick={handleCancel} disabled={isLoading} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleConfirm} disabled={isLoading} className="flex-1">
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Generate Lab Slots
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
