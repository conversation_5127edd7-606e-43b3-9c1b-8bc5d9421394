import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { 
  CalendarDays, 
  FileText, 
  AlertCircle, 
  CheckCircle, 
  Upload,
  Calculator,
  Clock
} from 'lucide-react';
import { 
  LeaveManagementService, 
  LeavePolicy, 
  LeaveBalance, 
  LeaveFormData 
} from '@/services/LeaveManagementService';

interface LeaveRequestFormProps {
  onSubmitSuccess?: () => void;
}

export default function LeaveRequestForm({ onSubmitSuccess }: LeaveRequestFormProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const { department, departmentName } = useUserDepartment();

  // Form state
  const [formData, setFormData] = useState<LeaveFormData>({
    leave_type: '',
    start_date: '',
    end_date: '',
    reason: '',
    supporting_documents: []
  });

  // Component state
  const [leavePolicies, setLeavePolicies] = useState<LeavePolicy[]>([]);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [calculatedDays, setCalculatedDays] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string>('');

  // Load initial data
  useEffect(() => {
    if (user?.id && department) {
      loadInitialData();
    }
  }, [user?.id, department]);

  // Calculate days when dates change
  useEffect(() => {
    if (formData.start_date && formData.end_date) {
      const days = calculateLeaveDays(formData.start_date, formData.end_date);
      setCalculatedDays(days);
      validateLeaveBalance(formData.leave_type, days);
    } else {
      setCalculatedDays(0);
      setValidationError('');
    }
  }, [formData.start_date, formData.end_date, formData.leave_type, leaveBalances]);

  const loadInitialData = async () => {
    if (!user?.id || !department) return;

    try {
      setLoading(true);
      
      // Initialize leave balances for the faculty member
      await LeaveManagementService.initializeLeaveBalances(user.id, department);
      
      // Load leave policies and balances
      const [policies, balances] = await Promise.all([
        LeaveManagementService.getLeavePolicies(),
        LeaveManagementService.getLeaveBalances(user.id)
      ]);

      setLeavePolicies(policies);
      setLeaveBalances(balances);
    } catch (error) {
      console.error('Error loading initial data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load leave information. Please refresh the page.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateLeaveDays = (startDate: string, endDate: string): number => {
    if (!startDate || !endDate) return 0;
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (end < start) return 0;
    
    let totalDays = 0;
    const currentDate = new Date(start);
    
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      // Count only weekdays (Monday = 1, Friday = 5)
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        totalDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return totalDays;
  };

  const validateLeaveBalance = (leaveType: string, requestedDays: number) => {
    if (!leaveType || requestedDays === 0) {
      setValidationError('');
      return;
    }

    const balance = leaveBalances.find(b => b.leave_type === leaveType);
    if (!balance) {
      setValidationError('Leave balance information not found.');
      return;
    }

    if (balance.remaining_days < requestedDays) {
      setValidationError(
        `Insufficient leave balance. Available: ${balance.remaining_days} days, Requested: ${requestedDays} days`
      );
    } else {
      setValidationError('');
    }
  };

  const handleInputChange = (field: keyof LeaveFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.id || !department) {
      toast({
        title: 'Error',
        description: 'User information not available. Please refresh the page.',
        variant: 'destructive',
      });
      return;
    }

    if (validationError) {
      toast({
        title: 'Validation Error',
        description: validationError,
        variant: 'destructive',
      });
      return;
    }

    if (calculatedDays === 0) {
      toast({
        title: 'Invalid Dates',
        description: 'Please select valid start and end dates.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSubmitting(true);
      
      await LeaveManagementService.submitLeaveRequest(user.id, department, formData);
      
      toast({
        title: 'Leave Request Submitted',
        description: `Your leave request for ${calculatedDays} days has been submitted successfully.`,
      });

      // Reset form
      setFormData({
        leave_type: '',
        start_date: '',
        end_date: '',
        reason: '',
        supporting_documents: []
      });
      setCalculatedDays(0);
      
      // Reload balances
      const updatedBalances = await LeaveManagementService.getLeaveBalances(user.id);
      setLeaveBalances(updatedBalances);
      
      // Call success callback
      onSubmitSuccess?.();
      
    } catch (error) {
      console.error('Error submitting leave request:', error);
      toast({
        title: 'Submission Failed',
        description: error instanceof Error ? error.message : 'Failed to submit leave request.',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const getSelectedLeaveBalance = () => {
    if (!formData.leave_type) return null;
    return leaveBalances.find(b => b.leave_type === formData.leave_type);
  };

  const getSelectedPolicy = () => {
    if (!formData.leave_type) return null;
    return leavePolicies.find(p => p.leave_type === formData.leave_type);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            <span className="ml-2">Loading leave information...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Submit Leave Request
        </CardTitle>
        <CardDescription>
          Fill out the form below to submit a new leave request. Your request will be sent for approval.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Leave Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="leave_type">Leave Type *</Label>
            <Select value={formData.leave_type} onValueChange={(value) => handleInputChange('leave_type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select leave type" />
              </SelectTrigger>
              <SelectContent>
                {leavePolicies.map((policy) => {
                  const balance = leaveBalances.find(b => b.leave_type === policy.leave_type);
                  return (
                    <SelectItem key={policy.leave_type} value={policy.leave_type}>
                      <div className="flex items-center justify-between w-full">
                        <span>{policy.leave_name}</span>
                        {balance && (
                          <span className="text-sm text-muted-foreground ml-2">
                            ({balance.remaining_days}/{balance.total_allocated} available)
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            
            {/* Leave Balance Display */}
            {getSelectedLeaveBalance() && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Available Balance:</span>
                  <span className="text-blue-600 font-semibold">
                    {getSelectedLeaveBalance()!.remaining_days} days
                  </span>
                  <span className="text-muted-foreground">
                    (Used: {getSelectedLeaveBalance()!.used_days}/{getSelectedLeaveBalance()!.total_allocated})
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Date Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date">Start Date *</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
                min={new Date().toISOString().split('T')[0]}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end_date">End Date *</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
                min={formData.start_date || new Date().toISOString().split('T')[0]}
                required
              />
            </div>
          </div>

          {/* Calculated Days Display */}
          {calculatedDays > 0 && (
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 text-sm">
                <Calculator className="h-4 w-4 text-green-600" />
                <span className="font-medium">Total Leave Days:</span>
                <span className="text-green-600 font-semibold">{calculatedDays} days</span>
                <span className="text-muted-foreground">(excluding weekends)</span>
              </div>
            </div>
          )}

          {/* Validation Error */}
          {validationError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}

          {/* Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason">Reason for Leave *</Label>
            <Textarea
              id="reason"
              placeholder="Please provide a detailed reason for your leave request..."
              value={formData.reason}
              onChange={(e) => handleInputChange('reason', e.target.value)}
              rows={4}
              required
            />
          </div>

          {/* Policy Information */}
          {getSelectedPolicy() && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Leave Policy Information:</h4>
              <p className="text-sm text-muted-foreground">{getSelectedPolicy()!.description}</p>
              {getSelectedPolicy()!.max_consecutive_days && (
                <p className="text-sm text-muted-foreground mt-1">
                  Maximum consecutive days: {getSelectedPolicy()!.max_consecutive_days}
                </p>
              )}
              {getSelectedPolicy()!.requires_documents && (
                <p className="text-sm text-orange-600 mt-1">
                  ⚠️ Supporting documents may be required for this leave type.
                </p>
              )}
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={submitting || !!validationError || calculatedDays === 0}
              className="min-w-[150px]"
            >
              {submitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                  Submitting...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Submit Request
                </div>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
