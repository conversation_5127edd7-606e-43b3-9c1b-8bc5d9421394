/**
 * Faculty Context Service
 * 
 * This service handles automatic detection of faculty teaching assignments
 * and provides context-aware data for the AI Quiz Management system.
 */

import { supabase } from '@/integrations/supabase/client';

export interface FacultySubjectAssignment {
  id: string;
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
  faculty_role: 'faculty_1' | 'faculty_2'; // Primary or secondary faculty
  total_students?: number;
}

export interface FacultyTeachingContext {
  faculty_id: string;
  faculty_name: string;
  department: string;
  total_subjects: number;
  total_classes: number;
  assignments: FacultySubjectAssignment[];
  subjects_summary: {
    [subject_code: string]: {
      subject_name: string;
      classes: Array<{
        department: string;
        semester: string;
        section: string;
        academic_year: string;
        student_count?: number;
      }>;
    };
  };
}

export interface SubjectClassOption {
  value: string; // Unique identifier for the assignment
  label: string; // Display text
  subject_code: string;
  subject_name: string;
  department: string;
  semester: string;
  section: string;
  academic_year: string;
}

export class FacultyContextService {

  /**
   * Get complete teaching context for a faculty member
   */
  static async getFacultyTeachingContext(facultyId: string): Promise<FacultyTeachingContext> {
    try {
      console.log('Getting faculty context for:', facultyId);

      // Get faculty basic information
      const { data: facultyData, error: facultyError } = await supabase
        .from('employee_details')
        .select('full_name, department')
        .eq('id', facultyId)
        .single();

      if (facultyError) {
        console.error('Faculty data error:', facultyError);
        throw facultyError;
      }

      console.log('Faculty data:', facultyData);

      // Get all subject assignments for this faculty
      const assignments = await this.getFacultySubjectAssignments(facultyId);
      console.log('Raw assignments:', assignments);

      // Group assignments by subject
      const subjectsSummary = this.groupAssignmentsBySubject(assignments);
      console.log('Subjects summary:', subjectsSummary);

      // Get student counts for each class
      const assignmentsWithCounts = await this.addStudentCounts(assignments);

      console.log('Final context:', {
        total_subjects: Object.keys(subjectsSummary).length,
        total_classes: assignments.length,
        assignments_count: assignments.length
      });

      return {
        faculty_id: facultyId,
        faculty_name: facultyData.full_name,
        department: facultyData.department,
        total_subjects: Object.keys(subjectsSummary).length,
        total_classes: assignments.length,
        assignments: assignmentsWithCounts,
        subjects_summary: subjectsSummary
      };

    } catch (error) {
      console.error('Failed to get faculty teaching context:', error);
      throw new Error(`Failed to load faculty context: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all subject assignments for a faculty member
   */
  static async getFacultySubjectAssignments(facultyId: string): Promise<FacultySubjectAssignment[]> {
    try {
      // Try simplified table first, fallback to original table
      let data, error;

      // First try the simplified table
      const { data: simplifiedData, error: simplifiedError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          id,
          subject_code,
          subject_name,
          department,
          semester,
          section,
          academic_year,
          faculty_1_id,
          faculty_2_id
        `)
        .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`)
        .order('subject_code', { ascending: true });

      if (!simplifiedError && simplifiedData && simplifiedData.length > 0) {
        data = simplifiedData;
        error = null;
        console.log('Using simplified table, found:', simplifiedData.length, 'assignments');
      } else {
        // Fallback to original table
        console.log('Simplified table not available, using subject_faculty_mappings');
        const { data: originalData, error: originalError } = await supabase
          .from('subject_faculty_mappings')
          .select(`
            id,
            subject_code,
            subject_name,
            department,
            semester,
            section,
            academic_year,
            faculty_1_id,
            faculty_2_id
          `)
          .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`)
          .order('subject_code', { ascending: true });

        data = originalData;
        error = originalError;
        console.log('Using original table, found:', originalData?.length || 0, 'assignments');
      }

      if (error) {
        console.error('Query error:', error);
        throw error;
      }

      console.log('Final query data:', data);

      const mappedAssignments = (data || []).map(assignment => ({
        id: assignment.id,
        subject_code: assignment.subject_code,
        subject_name: assignment.subject_name,
        department: assignment.department,
        semester: assignment.semester,
        section: assignment.section,
        academic_year: assignment.academic_year,
        faculty_role: assignment.faculty_1_id === facultyId ? 'faculty_1' : 'faculty_2'
      }));

      // Remove duplicates based on unique combination of subject_code, department, semester, section, academic_year
      const uniqueAssignments = mappedAssignments.filter((assignment, index, self) => {
        const uniqueKey = `${assignment.subject_code}-${assignment.department}-${assignment.semester}-${assignment.section}-${assignment.academic_year}`;
        return index === self.findIndex(a =>
          `${a.subject_code}-${a.department}-${a.semester}-${a.section}-${a.academic_year}` === uniqueKey
        );
      });

      console.log('After deduplication:', uniqueAssignments.length, 'unique assignments');

      return uniqueAssignments;

    } catch (error) {
      console.error('Failed to get faculty subject assignments:', error);
      throw error;
    }
  }

  /**
   * Get subject-class options for dropdowns
   */
  static async getFacultySubjectOptions(facultyId: string): Promise<SubjectClassOption[]> {
    const assignments = await this.getFacultySubjectAssignments(facultyId);
    
    return assignments.map(assignment => ({
      value: assignment.id,
      label: `${assignment.subject_code} - ${assignment.subject_name} (${assignment.department} Sem-${assignment.semester} Sec-${assignment.section})`,
      subject_code: assignment.subject_code,
      subject_name: assignment.subject_name,
      department: assignment.department,
      semester: assignment.semester,
      section: assignment.section,
      academic_year: assignment.academic_year
    }));
  }

  /**
   * Get auto-fill data for a specific subject assignment
   */
  static async getSubjectAutoFillData(assignmentId: string): Promise<Omit<SubjectClassOption, 'value' | 'label'> | null> {
    try {
      // Try simplified table first, fallback to original table
      let data, error;

      const { data: simplifiedData, error: simplifiedError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select(`
          subject_code,
          subject_name,
          department,
          semester,
          section,
          academic_year
        `)
        .eq('id', assignmentId)
        .single();

      if (!simplifiedError && simplifiedData) {
        data = simplifiedData;
        error = null;
      } else {
        // Fallback to original table
        const { data: originalData, error: originalError } = await supabase
          .from('subject_faculty_mappings')
          .select(`
            subject_code,
            subject_name,
            department,
            semester,
            section,
            academic_year
          `)
          .eq('id', assignmentId)
          .single();

        data = originalData;
        error = originalError;
      }

      if (error) throw error;

      return data;

    } catch (error) {
      console.error('Failed to get subject auto-fill data:', error);
      return null;
    }
  }

  /**
   * Group assignments by subject for summary display
   */
  private static groupAssignmentsBySubject(assignments: FacultySubjectAssignment[]) {
    return assignments.reduce((acc, assignment) => {
      const subjectCode = assignment.subject_code;
      
      if (!acc[subjectCode]) {
        acc[subjectCode] = {
          subject_name: assignment.subject_name,
          classes: []
        };
      }

      acc[subjectCode].classes.push({
        department: assignment.department,
        semester: assignment.semester,
        section: assignment.section,
        academic_year: assignment.academic_year
      });

      return acc;
    }, {} as FacultyTeachingContext['subjects_summary']);
  }

  /**
   * Add student counts to assignments
   */
  private static async addStudentCounts(assignments: FacultySubjectAssignment[]): Promise<FacultySubjectAssignment[]> {
    try {
      const assignmentsWithCounts = await Promise.all(
        assignments.map(async (assignment) => {
          const { count, error } = await supabase
            .from('class_students')
            .select('*', { count: 'exact', head: true })
            .eq('department', assignment.department)
            .eq('semester', assignment.semester)
            .eq('section', assignment.section)
            .eq('academic_year', assignment.academic_year);

          if (error) {
            console.warn(`Failed to get student count for ${assignment.subject_code}:`, error);
          }

          return {
            ...assignment,
            total_students: count || 0
          };
        })
      );

      return assignmentsWithCounts;

    } catch (error) {
      console.error('Failed to add student counts:', error);
      return assignments;
    }
  }

  /**
   * Get faculty's course materials grouped by subject
   */
  static async getFacultyMaterialsBySubject(facultyId: string) {
    try {
      const { data: materials, error } = await supabase
        .from('course_materials')
        .select('*')
        .eq('faculty_id', facultyId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Group materials by subject-class combination
      const groupedMaterials = (materials || []).reduce((acc, material) => {
        const key = `${material.subject_code}-${material.department}-${material.semester}-${material.section}`;
        
        if (!acc[key]) {
          acc[key] = {
            subject_code: material.subject_code,
            subject_name: material.subject_name,
            department: material.department,
            semester: material.semester,
            section: material.section,
            academic_year: material.academic_year,
            materials: []
          };
        }

        acc[key].materials.push(material);
        return acc;
      }, {} as Record<string, any>);

      return Object.values(groupedMaterials);

    } catch (error) {
      console.error('Failed to get faculty materials by subject:', error);
      throw error;
    }
  }

  /**
   * Validate if faculty has access to a specific subject assignment
   */
  static async validateFacultySubjectAccess(facultyId: string, assignmentId: string): Promise<boolean> {
    try {
      // Try simplified table first, fallback to original table
      let data, error;

      const { data: simplifiedData, error: simplifiedError } = await supabase
        .from('simplified_subject_faculty_mappings')
        .select('id')
        .eq('id', assignmentId)
        .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`)
        .single();

      if (!simplifiedError && simplifiedData) {
        data = simplifiedData;
        error = null;
      } else {
        // Fallback to original table
        const { data: originalData, error: originalError } = await supabase
          .from('subject_faculty_mappings')
          .select('id')
          .eq('id', assignmentId)
          .or(`faculty_1_id.eq.${facultyId},faculty_2_id.eq.${facultyId}`)
          .single();

        data = originalData;
        error = originalError;
      }

      return !error && !!data;

    } catch (error) {
      console.error('Failed to validate faculty subject access:', error);
      return false;
    }
  }

  /**
   * Get faculty dashboard summary statistics
   */
  static async getFacultyDashboardStats(facultyId: string) {
    try {
      const context = await this.getFacultyTeachingContext(facultyId);
      
      // Get quiz statistics
      const { data: quizTemplates, error: quizError } = await supabase
        .from('quiz_templates')
        .select('id, status')
        .eq('faculty_id', facultyId);

      if (quizError) throw quizError;

      // Get material statistics
      const { data: materials, error: materialError } = await supabase
        .from('course_materials')
        .select('id, processing_status')
        .eq('faculty_id', facultyId);

      if (materialError) throw materialError;

      const totalStudents = context.assignments.reduce((sum, assignment) => 
        sum + (assignment.total_students || 0), 0
      );

      return {
        total_subjects: context.total_subjects,
        total_classes: context.total_classes,
        total_students: totalStudents,
        total_quizzes: quizTemplates?.length || 0,
        active_quizzes: quizTemplates?.filter(q => q.status === 'published').length || 0,
        total_materials: materials?.length || 0,
        processed_materials: materials?.filter(m => m.processing_status === 'completed').length || 0
      };

    } catch (error) {
      console.error('Failed to get faculty dashboard stats:', error);
      throw error;
    }
  }
}
