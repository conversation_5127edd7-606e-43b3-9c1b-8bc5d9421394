
import React from "react";
import { AlertCircle } from "lucide-react";

export default function UploadGuidelines() {
  return (
    <div className="text-xs space-y-2 text-muted-foreground mt-4">
      <p className="flex items-center">
        <AlertCircle className="h-3 w-3 mr-1" />
        The Excel file should match the format shown in the guide
      </p>
      <p className="flex items-center">
        <AlertCircle className="h-3 w-3 mr-1" />
        Maximum file size: 5MB
      </p>
      <p className="flex items-center">
        <AlertCircle className="h-3 w-3 mr-1" />
        Faculty names must match those in the system exactly
      </p>
      <p className="flex items-center">
        <AlertCircle className="h-3 w-3 mr-1" />
        For lab subjects, include both Batch 1 and Batch 2 information in separate columns
      </p>
    </div>
  );
}
