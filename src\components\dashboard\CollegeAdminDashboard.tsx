import { useState, useEffect } from "react";
import { Users, UserCheck, UserX, Shield, Activity, TrendingUp } from "lucide-react";
import DashboardCard from "@/components/dashboard/DashboardCard";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { ModernLayout, ModernGrid, ModernCard } from "@/components/layout/ModernLayout";
import { ModernLoading } from "@/components/ui/modern-loading";

interface UserStats {
  totalUsers: number;
  activeUsers: number;
  collegeAdmins: number;
  timetableCoordinators: number;
}

const CollegeAdminDashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [userStats, setUserStats] = useState<UserStats>({
    totalUsers: 0,
    activeUsers: 0,
    collegeAdmins: 0,
    timetableCoordinators: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserStats();
  }, []);

  const fetchUserStats = async () => {
    try {
      setLoading(true);
      
      // Fetch all users
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('role, created_at');

      if (error) {
        console.error('Error fetching user stats:', error);
        return;
      }

      const stats = {
        totalUsers: profiles?.length || 0,
        activeUsers: profiles?.length || 0, // Assuming all users are active for now
        collegeAdmins: profiles?.filter(p => p.role === 'college_admin').length || 0,
        timetableCoordinators: profiles?.filter(p => p.role === 'timetable_coordinator').length || 0,
      };

      setUserStats(stats);
    } catch (error) {
      console.error('Error fetching user stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <ModernLayout>
        <ModernLoading
          size="lg"
          text="Loading dashboard data..."
          fullScreen={false}
        />
      </ModernLayout>
    );
  }

  return (
    <ModernLayout
      title="College Admin Dashboard"
      description="Manage users and monitor system access"
    >
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:w-auto lg:grid-cols-2">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Recent Activity
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <ModernGrid cols={4} gap="lg">
            <DashboardCard
              title="Total Users"
              value={userStats.totalUsers}
              icon={Users}
              description="All registered users"
              colorClass="text-primary bg-primary/10"
              trend={{ value: 12, isPositive: true }}
              interactive
            />
            <DashboardCard
              title="Active Users"
              value={userStats.activeUsers}
              icon={UserCheck}
              description="Currently active users"
              colorClass="text-success bg-success/10"
              trend={{ value: 8, isPositive: true }}
              interactive
            />
            <DashboardCard
              title="College Admins"
              value={userStats.collegeAdmins}
              icon={Shield}
              description="Administrative users"
              colorClass="text-info bg-info/10"
              interactive
            />
            <DashboardCard
              title="Timetable Coordinators"
              value={userStats.timetableCoordinators}
              icon={UserX}
              description="Timetable management users"
              colorClass="text-warning bg-warning/10"
              interactive
            />
          </ModernGrid>

          <ModernGrid cols={2} gap="lg">
            <ModernCard
              title="User Management"
              description="Manage user accounts and permissions"
              elevated
            >
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Users className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">User Registration</p>
                      <p className="text-sm text-muted-foreground">Manage user accounts</p>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-primary">Active</div>
                </div>
                <div className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-success/10 flex items-center justify-center">
                      <Shield className="h-4 w-4 text-success" />
                    </div>
                    <div>
                      <p className="font-medium">Role Assignment</p>
                      <p className="text-sm text-muted-foreground">Assign and modify roles</p>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-success">Active</div>
                </div>
                <div className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-info/10 flex items-center justify-center">
                      <UserCheck className="h-4 w-4 text-info" />
                    </div>
                    <div>
                      <p className="font-medium">Access Control</p>
                      <p className="text-sm text-muted-foreground">Monitor system access</p>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-info">Active</div>
                </div>
              </div>
            </ModernCard>

            <ModernCard
              title="System Status"
              description="Monitor system health and performance"
              elevated
            >
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">User Management System</span>
                    <span className="text-sm font-medium text-success">100%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div className="bg-success h-full w-full transition-all duration-500"></div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Authentication Service</span>
                    <span className="text-sm font-medium text-success">100%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div className="bg-success h-full w-full transition-all duration-500"></div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Role-Based Access</span>
                    <span className="text-sm font-medium text-success">100%</span>
                  </div>
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div className="bg-success h-full w-full transition-all duration-500"></div>
                  </div>
                </div>
              </div>
            </ModernCard>
          </ModernGrid>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <ModernCard
            title="Recent User Activity"
            description="Latest system activities and user actions"
            elevated
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">New User Registration</p>
                    <p className="text-sm text-muted-foreground">System Administrator</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-sm text-muted-foreground">2 hours ago</span>
                  <div className="w-2 h-2 bg-primary rounded-full ml-auto mt-1"></div>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-success/10 flex items-center justify-center">
                    <Shield className="h-5 w-5 text-success" />
                  </div>
                  <div>
                    <p className="font-medium">Role Updated</p>
                    <p className="text-sm text-muted-foreground">User role changed to Timetable Coordinator</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-sm text-muted-foreground">5 hours ago</span>
                  <div className="w-2 h-2 bg-success rounded-full ml-auto mt-1"></div>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-info/10 flex items-center justify-center">
                    <UserCheck className="h-5 w-5 text-info" />
                  </div>
                  <div>
                    <p className="font-medium">User Access Granted</p>
                    <p className="text-sm text-muted-foreground">New user granted system access</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="text-sm text-muted-foreground">1 day ago</span>
                  <div className="w-2 h-2 bg-info rounded-full ml-auto mt-1"></div>
                </div>
              </div>
            </div>
          </ModernCard>
        </TabsContent>
      </Tabs>
    </ModernLayout>
  );
};

export default CollegeAdminDashboard;
