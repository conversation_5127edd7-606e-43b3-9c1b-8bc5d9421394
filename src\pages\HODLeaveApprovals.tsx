import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useUserRole } from '@/hooks/useUserRole';
import { 
  CheckCircle, 
  XCircle, 
  Calendar, 
  User, 
  Building2,
  Clock,
  FileText,
  Filter,
  Search,
  AlertCircle,
  Crown,
  Users
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  LeaveManagementService,
  LeaveRequest,
  LeavePolicy
} from '@/services/LeaveManagementService';
import LeaveApprovalHistory from '@/components/leave-management/LeaveApprovalHistory';

export default function HODLeaveApprovals() {
  const { user } = useAuth();
  const { toast } = useToast();
  const { department, departmentName, loading: departmentLoading } = useUserDepartment();
  const { isPrincipal, loading: roleLoading } = useUserRole();

  // State management
  const [pendingHODRequests, setPendingHODRequests] = useState<LeaveRequest[]>([]);
  const [leavePolicies, setLeavePolicies] = useState<LeavePolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [processing, setProcessing] = useState(false);

  // Filter state
  const [filters, setFilters] = useState({
    leave_type: 'all',
    start_date: '',
    end_date: '',
    department: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);

  // Department list for filtering
  const [departments] = useState([
    'Computer Science and Engineering',
    'Information Science and Engineering',
    'Electronics and Communication Engineering',
    'Mechanical Engineering',
    'Civil Engineering',
    'Electrical and Electronics Engineering'
  ]);

  // Load data
  useEffect(() => {
    if (user?.id && isPrincipal) {
      loadPendingHODRequests();
      loadLeavePolicies();
    }
  }, [user?.id, isPrincipal]);

  const loadPendingHODRequests = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const filterParams = {
        leave_type: filters.leave_type === 'all' ? undefined : filters.leave_type,
        start_date: filters.start_date || undefined,
        end_date: filters.end_date || undefined,
        department: filters.department === 'all' ? undefined : filters.department
      };
      const requests = await LeaveManagementService.getPendingHODRequestsForPrincipal(filterParams);
      setPendingHODRequests(requests);
    } catch (error) {
      console.error('Error loading pending HOD requests:', error);
      toast({
        title: 'Error',
        description: 'Failed to load pending HOD leave requests.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadLeavePolicies = async () => {
    try {
      const policies = await LeaveManagementService.getLeavePolicies();
      setLeavePolicies(policies);
    } catch (error) {
      console.error('Error loading leave policies:', error);
    }
  };

  const handleApprove = async () => {
    if (!selectedRequest || !user?.id || !department) return;

    try {
      setProcessing(true);
      await LeaveManagementService.approveLeaveRequest(
        selectedRequest.id,
        user.id,
        'principal',
        department
      );

      toast({
        title: 'HOD Leave Approved',
        description: `Leave request for HOD ${selectedRequest.faculty?.full_name} has been approved.`,
      });

      setShowApproveDialog(false);
      setSelectedRequest(null);
      await loadPendingHODRequests();
    } catch (error) {
      console.error('Error approving HOD leave:', error);
      toast({
        title: 'Approval Failed',
        description: error instanceof Error ? error.message : 'Failed to approve HOD leave request.',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!selectedRequest || !user?.id || !department || !rejectionReason.trim()) return;

    try {
      setProcessing(true);
      await LeaveManagementService.rejectLeaveRequest(
        selectedRequest.id,
        user.id,
        'principal',
        rejectionReason.trim(),
        department
      );

      toast({
        title: 'HOD Leave Rejected',
        description: `Leave request for HOD ${selectedRequest.faculty?.full_name} has been rejected.`,
      });

      setShowRejectDialog(false);
      setSelectedRequest(null);
      setRejectionReason('');
      await loadPendingHODRequests();
    } catch (error) {
      console.error('Error rejecting HOD leave:', error);
      toast({
        title: 'Rejection Failed',
        description: error instanceof Error ? error.message : 'Failed to reject HOD leave request.',
        variant: 'destructive',
      });
    } finally {
      setProcessing(false);
    }
  };

  const applyFilters = async () => {
    await loadPendingHODRequests();
  };

  const clearFilters = () => {
    setFilters({
      leave_type: 'all',
      start_date: '',
      end_date: '',
      department: 'all'
    });
  };

  const getLeaveTypeName = (leaveType: string): string => {
    const policy = leavePolicies.find(p => p.leave_type === leaveType);
    return policy?.leave_name || leaveType.replace('_', ' ').toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDepartmentDisplayName = (dept: string) => {
    const deptMap: Record<string, string> = {
      'cse': 'Computer Science and Engineering',
      'ise': 'Information Science and Engineering',
      'ece': 'Electronics and Communication Engineering',
      'mech': 'Mechanical Engineering',
      'civil': 'Civil Engineering',
      'eee': 'Electrical and Electronics Engineering'
    };
    return deptMap[dept] || dept;
  };

  // Show loading while checking roles
  if (roleLoading || departmentLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  // Check if user has Principal role
  if (!isPrincipal) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Access denied. This page is only accessible to users with Principal role.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Crown className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold">HOD Leave Approvals</h1>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </Button>
      </div>

      {/* Principal Context */}
      <Alert className="border-purple-200 bg-purple-50">
        <Crown className="h-4 w-4 text-purple-600" />
        <AlertDescription className="text-purple-800">
          <div className="flex items-center justify-between">
            <div>
              <strong>Role:</strong> Principal | <strong>Authority:</strong> HOD Leave Approvals
            </div>
            <div className="text-sm">
              Showing pending leave requests from HODs across all departments
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filter HOD Leave Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label>Leave Type</Label>
                <Select value={filters.leave_type} onValueChange={(value) => setFilters(prev => ({ ...prev, leave_type: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    {leavePolicies.map((policy) => (
                      <SelectItem key={policy.leave_type} value={policy.leave_type}>
                        {policy.leave_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Department</Label>
                <Select value={filters.department} onValueChange={(value) => setFilters(prev => ({ ...prev, department: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="All departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Start Date From</Label>
                <Input
                  type="date"
                  value={filters.start_date}
                  onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label>End Date To</Label>
                <Input
                  type="date"
                  value={filters.end_date}
                  onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button onClick={applyFilters} size="sm">
                <Search className="h-4 w-4 mr-2" />
                Apply Filters
              </Button>
              <Button variant="outline" onClick={clearFilters} size="sm">
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pending HOD Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Pending HOD Leave Requests ({pendingHODRequests.length})
          </CardTitle>
          <CardDescription>
            Review and approve/reject leave requests from HODs across all departments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              <span className="ml-2">Loading HOD requests...</span>
            </div>
          ) : pendingHODRequests.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Crown className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No pending HOD leave requests found</p>
              <p className="text-sm">All HOD requests have been processed</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingHODRequests.map((request) => (
                <div key={request.id} className="border rounded-lg p-4 space-y-3 bg-gradient-to-r from-purple-50 to-blue-50">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Crown className="h-4 w-4 text-purple-600" />
                        <span className="font-medium">{request.faculty?.full_name}</span>
                        <Badge className="bg-purple-100 text-purple-800 border-purple-200">HOD</Badge>
                        <Badge variant="outline">{request.faculty?.designation}</Badge>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>📧 {request.faculty?.email}</span>
                        <span>🏢 {getDepartmentDisplayName(request.department)}</span>
                      </div>
                    </div>
                    
                    <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                      Pending Principal Approval
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Leave Type:</span>
                      <p>{getLeaveTypeName(request.leave_type)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Duration:</span>
                      <p>{formatDate(request.start_date)} - {formatDate(request.end_date)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Total Days:</span>
                      <p>{request.total_days} days</p>
                    </div>
                    <div>
                      <span className="font-medium">Applied Date:</span>
                      <p>{formatDate(request.applied_date)}</p>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium text-sm">Reason:</span>
                    <p className="text-sm text-muted-foreground mt-1">{request.reason}</p>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Dialog open={showApproveDialog && selectedRequest?.id === request.id} onOpenChange={setShowApproveDialog}>
                      <DialogTrigger asChild>
                        <Button 
                          size="sm" 
                          onClick={() => setSelectedRequest(request)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Approve HOD Leave Request</DialogTitle>
                          <DialogDescription>
                            Are you sure you want to approve this leave request for HOD {selectedRequest?.faculty?.full_name} from {getDepartmentDisplayName(selectedRequest?.department || '')}?
                          </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setShowApproveDialog(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleApprove} disabled={processing}>
                            {processing ? 'Approving...' : 'Approve'}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>

                    <Dialog open={showRejectDialog && selectedRequest?.id === request.id} onOpenChange={setShowRejectDialog}>
                      <DialogTrigger asChild>
                        <Button 
                          variant="destructive" 
                          size="sm"
                          onClick={() => setSelectedRequest(request)}
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Reject
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Reject HOD Leave Request</DialogTitle>
                          <DialogDescription>
                            Please provide a reason for rejecting this leave request for HOD {selectedRequest?.faculty?.full_name}.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="rejection-reason">Rejection Reason *</Label>
                            <Textarea
                              id="rejection-reason"
                              placeholder="Please provide a detailed reason for rejection..."
                              value={rejectionReason}
                              onChange={(e) => setRejectionReason(e.target.value)}
                              rows={4}
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => {
                            setShowRejectDialog(false);
                            setRejectionReason('');
                          }}>
                            Cancel
                          </Button>
                          <Button 
                            variant="destructive" 
                            onClick={handleReject} 
                            disabled={processing || !rejectionReason.trim()}
                          >
                            {processing ? 'Rejecting...' : 'Reject'}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Leave Approval History */}
      <LeaveApprovalHistory className="mt-6" />
    </div>
  );
}
