import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useAuth } from '@/contexts/AuthContext';
import {
  Users,
  BookOpen,
  CheckCircle,
  Clock,
  AlertCircle,
  TrendingUp,
  User,
  GraduationCap
} from 'lucide-react';

import { ClassTeacherFeedbackTracking, SubjectSubmissionSummary, StudentSubmissionStatus } from '@/types/feedback-system';
import { FeedbackService } from '@/services/FeedbackService';
import { ClassTeacherService } from '@/services/ClassTeacherService';

const ClassTeacherFeedbackTrackingDashboard: React.FC = () => {
  const [trackingData, setTrackingData] = useState<ClassTeacherFeedbackTracking | null>(null);
  const [loading, setLoading] = useState(true);
  const [classAssignment, setClassAssignment] = useState<{
    department: string;
    semester: string;
    section: string;
    academic_year: string;
  } | null>(null);
  const [isClassTeacher, setIsClassTeacher] = useState(false);

  const { user } = useAuth();
  const { department, departmentName } = useUserDepartment();
  const { toast } = useToast();

  useEffect(() => {
    if (user?.id && department) {
      loadClassTeacherAssignment();
    }
  }, [user?.id, department]);

  const loadClassTeacherAssignment = async () => {
    try {
      setLoading(true);

      // First, check if the current user is a class teacher
      const assignment = await ClassTeacherService.getClassTeacherAssignment(
        user!.id,
        '2024-2025'
      );

      if (!assignment) {
        setIsClassTeacher(false);
        setClassAssignment(null);
        setTrackingData(null);
        setLoading(false);
        return;
      }

      setIsClassTeacher(true);
      setClassAssignment(assignment);

      // Load tracking data for the specific class
      await loadTrackingData(assignment);
    } catch (error) {
      console.error('Error loading class teacher assignment:', error);
      toast({
        title: "Error",
        description: "Failed to load class teacher assignment",
        variant: "destructive",
      });
      setLoading(false);
    }
  };

  const loadTrackingData = async (assignment: {
    department: string;
    semester: string;
    section: string;
    academic_year: string;
  }) => {
    try {
      console.log('🔍 Loading feedback tracking for class:', assignment);

      // Get comprehensive tracking data for this specific class using the faculty-specific method
      const trackingData = await FeedbackService.getClassTeacherFeedbackTrackingByFaculty(
        user!.id,
        assignment.academic_year
      );

      setTrackingData(trackingData);
    } catch (error) {
      console.error('Error loading tracking data:', error);
      toast({
        title: "Error",
        description: "Failed to load feedback tracking data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getCompletionBadge = (percentage: number) => {
    if (percentage >= 80) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (percentage >= 60) return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge className="bg-red-100 text-red-800">Needs Attention</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="loading-spinner h-8 w-8 mx-auto"></div>
          <p className="text-muted-foreground">Loading feedback tracking...</p>
        </div>
      </div>
    );
  }

  // Show message if user is not a class teacher
  if (!isClassTeacher || !classAssignment) {
    return (
      <div className="container-modern py-responsive space-y-6">
        <div className="page-header">
          <h1 className="page-title">Feedback Submission Tracking</h1>
          <p className="text-muted-foreground">
            Monitor student feedback submission progress for your assigned class
          </p>
        </div>

        <Card>
          <CardContent className="p-8 text-center">
            <GraduationCap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Class Teacher Access Required</h3>
            <p className="text-muted-foreground mb-4">
              You need to be assigned as a class teacher to access feedback submission tracking.
            </p>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Contact your HOD to get assigned as a class teacher for a specific semester and section.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!trackingData) {
    return (
      <div className="container-modern py-responsive space-y-6">
        <div className="page-header">
          <h1 className="page-title">Feedback Submission Tracking</h1>
          <p className="text-muted-foreground">
            Monitor student feedback submission progress for {classAssignment.semester} Semester, Section {classAssignment.section}
          </p>
        </div>

        <Card>
          <CardContent className="p-8 text-center">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Active Feedback Sessions</h3>
            <p className="text-muted-foreground mb-4">
              There are currently no active feedback sessions for your assigned class ({classAssignment.semester} Semester, Section {classAssignment.section}).
            </p>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Contact your HOD to initiate feedback collection sessions for your class.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container-modern py-responsive space-y-8">
      {/* Header */}
      <div className="page-header">
        <h1 className="page-title">Feedback Submission Tracking</h1>
        <p className="text-muted-foreground">
          Monitor student feedback submission progress for your assigned class: {classAssignment.semester} Semester, Section {classAssignment.section}
        </p>
        <div className="flex items-center gap-2 mt-2">
          <Badge variant="outline" className="text-xs">
            <GraduationCap className="h-3 w-3 mr-1" />
            Class Teacher View
          </Badge>
          <Badge variant="secondary" className="text-xs">
            {trackingData.session_name}
          </Badge>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Students</p>
                <p className="text-2xl font-bold">{trackingData.total_students}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Subjects</p>
                <p className="text-2xl font-bold">{trackingData.total_subjects}</p>
              </div>
              <BookOpen className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg. Completion</p>
                <p className="text-2xl font-bold">
                  {trackingData.submission_summary.length > 0
                    ? Math.round(trackingData.submission_summary.reduce((acc, s) => acc + s.completion_percentage, 0) / trackingData.submission_summary.length)
                    : 0}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Session</p>
                <p className="text-lg font-bold">{trackingData.session_name}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Subject-wise Submission Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Subject-wise Submission Status
          </CardTitle>
          <CardDescription>
            Feedback submission progress for each subject
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {trackingData.submission_summary.map((subject) => (
              <div key={`${subject.faculty_id}-${subject.subject_code}`} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between mb-3">
                  <div className="space-y-1">
                    <h4 className="font-medium">{subject.faculty_name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {subject.subject_name} ({subject.subject_code})
                    </p>
                  </div>
                  {getCompletionBadge(subject.completion_percentage)}
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Submission Progress</span>
                    <span className={getCompletionColor(subject.completion_percentage)}>
                      {subject.submitted_count} of {subject.total_students} students
                    </span>
                  </div>
                  <Progress value={subject.completion_percentage} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>{subject.completion_percentage.toFixed(1)}% Complete</span>
                    <span>{subject.pending_count} Pending</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Student-wise Submission Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Student Submission Status
          </CardTitle>
          <CardDescription>
            Individual student feedback completion status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {trackingData.student_submission_status.map((student) => (
              <div key={student.student_usn} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-1">
                  <h4 className="font-medium">{student.student_name}</h4>
                  <p className="text-sm text-muted-foreground">USN: {student.student_usn}</p>
                  {student.pending_subjects.length > 0 && (
                    <p className="text-xs text-red-600">
                      Pending: {student.pending_subjects.join(', ')}
                    </p>
                  )}
                </div>
                <div className="text-right space-y-1">
                  <div className="flex items-center gap-2">
                    <span className={`text-sm font-medium ${getCompletionColor(student.completion_percentage)}`}>
                      {student.completion_percentage.toFixed(0)}%
                    </span>
                    {student.completion_percentage === 100 ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <Clock className="h-4 w-4 text-orange-600" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {student.completed_subjects} of {student.total_subjects}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClassTeacherFeedbackTrackingDashboard;
