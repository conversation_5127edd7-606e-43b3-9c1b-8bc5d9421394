import React, { useState, useEffect } from 'react';
import {
  User,
  Mail,
  Calendar,
  BookOpen,
  GraduationCap,
  Bell,
  Settings,
  LogOut,
  Home,
  FileText,
  Clock,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Menu,
  X
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LogoutButton } from '@/components/ui/logout-button';
import { useStudentAuth } from '@/contexts/StudentAuthContext';
import { StudentDashboardService, StudentSubject } from '@/services/StudentDashboardService';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import StudentSidebar from '@/components/student-dashboard/StudentSidebar';
import StudentSubjectCard from '@/components/student-dashboard/StudentSubjectCard';
import StudentFeedbackDashboard from '@/components/feedback/StudentFeedbackDashboard';

const StudentDashboard: React.FC = () => {
  const { student, studentDetails } = useStudentAuth();
  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState('dashboard');
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  if (!student) {
    return null; // This should not happen due to route protection
  }

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!student?.usn) return;

      try {
        setLoading(true);
        const data = await StudentDashboardService.getStudentDashboardData(student.usn);
        setDashboardData(data);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast({
          title: "Error loading dashboard",
          description: "Failed to load your academic data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [student?.usn, toast]);

  // Helper function to get ordinal suffix for semester
  const getOrdinalSuffix = (num: string) => {
    const number = parseInt(num);
    if (number >= 11 && number <= 13) return 'th';
    switch (number % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  };

  // Generate welcome message
  const getWelcomeMessage = () => {
    if (dashboardData) {
      const semesterWithSuffix = `${dashboardData.semester}${getOrdinalSuffix(dashboardData.semester)}`;
      return `Welcome back, ${dashboardData.student_name} - ${semesterWithSuffix} Semester, Section ${dashboardData.section}`;
    }
    if (studentDetails) {
      const semesterWithSuffix = `${studentDetails.semester}${getOrdinalSuffix(studentDetails.semester)}`;
      return `Welcome back, ${studentDetails.student_name} - ${semesterWithSuffix} Semester, Section ${studentDetails.section}`;
    }
    return `Welcome back, ${student.usn}!`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex">
      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 lg:static lg:inset-0",
        "transform transition-transform duration-300 ease-in-out lg:transform-none",
        sidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
      )}>
        <StudentSidebar
          activeTab={activeTab}
          onTabChange={setActiveTab}
          overallAttendance={dashboardData?.overall_attendance}
          totalSubjects={dashboardData?.subjects?.length}
          className="h-full"
        />
      </div>

      {/* Main Content Container */}
      <div className="flex-1 flex flex-col min-h-screen overflow-hidden">
        {/* Modern Header */}
        <header className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200/50 sticky top-0 z-30">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className="lg:hidden mr-3 hover:bg-gray-100"
                  onClick={() => setSidebarOpen(true)}
                >
                  <Menu className="h-5 w-5" />
                </Button>
                <div className="flex items-center">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg mr-3">
                    <GraduationCap className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-xl font-bold text-gray-900">Student Portal</h1>
                    <p className="text-xs text-gray-500 hidden sm:block">Academic Dashboard</p>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3 bg-gray-50 rounded-lg px-3 py-2">
                  <div className="p-1.5 bg-blue-100 rounded-full">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="hidden sm:block">
                    <p className="text-sm font-medium text-gray-900">{student.usn}</p>
                    <p className="text-xs text-gray-500">Student</p>
                  </div>
                  {!student.is_verified && (
                    <Badge variant="outline" className="text-orange-600 border-orange-200 text-xs">
                      Unverified
                    </Badge>
                  )}
                </div>

                <LogoutButton
                  variant="outline"
                  size="sm"
                  className="hover:bg-red-50 hover:border-red-200 hover:text-red-600"
                />
              </div>
            </div>
          </div>
        </header>

        {/* Main Content - Scrollable */}
        <main className="flex-1 overflow-y-auto">
          <div className="px-4 sm:px-6 lg:px-8 py-8 pb-20">
            {loading ? (
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <div className="relative">
                    <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 mx-auto mb-6"></div>
                    <div className="absolute inset-0 animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent mx-auto"></div>
                  </div>
                  <p className="text-gray-600 font-medium">Loading your dashboard...</p>
                  <p className="text-gray-400 text-sm mt-1">Please wait while we fetch your academic data</p>
                </div>
              </div>
            ) : (
              <div className="max-w-7xl mx-auto space-y-8">
                {/* Modern Welcome Section */}
                <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 rounded-2xl p-8 text-white">
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="relative z-10">
                    <h2 className="text-3xl font-bold mb-3">
                      {getWelcomeMessage()}
                    </h2>
                    <p className="text-blue-100 text-lg mb-6">
                      Track your academic progress and manage your student journey
                    </p>
                    {(dashboardData || studentDetails) && (
                      <div className="flex flex-wrap gap-3">
                        <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30 px-3 py-1">
                          📚 {(dashboardData?.department || studentDetails?.department)?.toUpperCase()}
                        </Badge>
                        <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30 px-3 py-1">
                          🎓 Semester {dashboardData?.semester || studentDetails?.semester}
                        </Badge>
                        <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30 px-3 py-1">
                          👥 Section {dashboardData?.section || studentDetails?.section}
                        </Badge>
                        {dashboardData?.overall_attendance !== undefined && (
                          <Badge className={cn(
                            "px-3 py-1 font-semibold",
                            dashboardData.overall_attendance >= 75 ? "bg-green-500/90 text-white border-green-400" :
                            dashboardData.overall_attendance >= 65 ? "bg-yellow-500/90 text-white border-yellow-400" :
                            "bg-red-500/90 text-white border-red-400"
                          )}>
                            📊 {dashboardData.overall_attendance}% Attendance
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                  {/* Decorative elements */}
                  <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full"></div>
                  <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full"></div>
                </div>

                {/* Email Verification Alert */}
                {!student.is_verified && (
                  <div className="bg-gradient-to-r from-orange-50 to-amber-50 border border-orange-200 rounded-xl p-6 mb-8">
                    <div className="flex items-start">
                      <div className="p-2 bg-orange-100 rounded-lg mr-4">
                        <Bell className="h-5 w-5 text-orange-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-orange-800 mb-2">
                          Email Verification Required
                        </h3>
                        <p className="text-orange-700 mb-3">
                          Please verify your email address <span className="font-medium">({student.email})</span> to access all features and receive important notifications.
                        </p>
                        <Button size="sm" className="bg-orange-600 hover:bg-orange-700 text-white">
                          Resend Verification Email
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Dashboard Content */}
                {activeTab === 'dashboard' && (
                  <div className="space-y-8">
                    {/* Modern Overview Stats */}
                    {dashboardData && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                          <div className="flex items-center justify-between mb-4">
                            <div className="p-3 bg-blue-100 rounded-lg">
                              <BookOpen className="h-6 w-6 text-blue-600" />
                            </div>
                            <Badge variant="outline" className="text-blue-600 border-blue-200">
                              Active
                            </Badge>
                          </div>
                          <div className="text-3xl font-bold text-gray-900 mb-2">
                            {dashboardData.subjects?.length || 0}
                          </div>
                          <p className="text-gray-600 font-medium">Total Subjects</p>
                          <p className="text-sm text-gray-500 mt-1">Enrolled this semester</p>
                        </div>

                        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                          <div className="flex items-center justify-between mb-4">
                            <div className={cn(
                              "p-3 rounded-lg",
                              dashboardData.overall_attendance >= 75 ? "bg-green-100" :
                              dashboardData.overall_attendance >= 65 ? "bg-yellow-100" : "bg-red-100"
                            )}>
                              <TrendingUp className={cn(
                                "h-6 w-6",
                                dashboardData.overall_attendance >= 75 ? "text-green-600" :
                                dashboardData.overall_attendance >= 65 ? "text-yellow-600" : "text-red-600"
                              )} />
                            </div>
                            <Badge className={cn(
                              dashboardData.overall_attendance >= 75 ? "bg-green-100 text-green-700 border-green-200" :
                              dashboardData.overall_attendance >= 65 ? "bg-yellow-100 text-yellow-700 border-yellow-200" :
                              "bg-red-100 text-red-700 border-red-200"
                            )}>
                              {dashboardData.overall_attendance >= 75 ? "Good" :
                               dashboardData.overall_attendance >= 65 ? "Warning" : "Critical"}
                            </Badge>
                          </div>
                          <div className={cn(
                            "text-3xl font-bold mb-2",
                            dashboardData.overall_attendance >= 75 ? "text-green-600" :
                            dashboardData.overall_attendance >= 65 ? "text-yellow-600" : "text-red-600"
                          )}>
                            {dashboardData.overall_attendance}%
                          </div>
                          <p className="text-gray-600 font-medium">Overall Attendance</p>
                          <p className="text-sm text-gray-500 mt-1">Across all subjects</p>
                        </div>

                        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                          <div className="flex items-center justify-between mb-4">
                            <div className="p-3 bg-purple-100 rounded-lg">
                              <Clock className="h-6 w-6 text-purple-600" />
                            </div>
                            <Badge variant="outline" className="text-purple-600 border-purple-200">
                              Total
                            </Badge>
                          </div>
                          <div className="text-3xl font-bold text-gray-900 mb-2">
                            {dashboardData.subjects?.reduce((sum, subject) => sum + subject.attended_classes, 0) || 0}
                          </div>
                          <p className="text-gray-600 font-medium">Classes Attended</p>
                          <p className="text-sm text-gray-500 mt-1">
                            Out of {dashboardData.subjects?.reduce((sum, subject) => sum + subject.total_classes, 0) || 0} total classes
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Modern Subjects Section */}
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">My Subjects</h3>
                          <p className="text-gray-600 mt-1">Track your academic progress across all enrolled subjects</p>
                        </div>
                        {dashboardData?.subjects && dashboardData.subjects.length > 0 && (
                          <div className="flex items-center gap-3">
                            <Badge className="bg-blue-100 text-blue-700 border-blue-200 px-4 py-2 text-sm font-medium">
                              📚 {dashboardData.subjects.length} Subjects Enrolled
                            </Badge>
                          </div>
                        )}
                      </div>

                      {dashboardData?.subjects && dashboardData.subjects.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                          {dashboardData.subjects.map((subject, index) => (
                            <div
                              key={subject.id}
                              className="animate-in slide-in-from-bottom-4 fade-in"
                              style={{ animationDelay: `${index * 100}ms` }}
                            >
                              <StudentSubjectCard
                                subject={subject}
                              />
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="bg-white rounded-xl border border-gray-200 p-12 text-center">
                          <div className="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                            <BookOpen className="h-10 w-10 text-gray-400" />
                          </div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-3">No Subjects Found</h3>
                          <p className="text-gray-600 mb-6 max-w-md mx-auto">
                            No subjects are currently assigned to your class. Please contact your academic coordinator for assistance.
                          </p>
                          <Button variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100">
                            Contact Support
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Feedback Content */}
                {activeTab === 'feedback' && dashboardData && (
                  <div className="space-y-6">
                    <StudentFeedbackDashboard
                      studentUsn={dashboardData.student_usn}
                      studentName={dashboardData.student_name}
                      department={dashboardData.department}
                      semester={dashboardData.semester}
                      section={dashboardData.section}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default StudentDashboard;
