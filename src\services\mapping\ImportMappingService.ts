
// src/services/mapping/ImportMappingService.ts
import { supabase } from "@/integrations/supabase/client";
import { MappingRow, LabSlotRow, ImportResult } from "./types";
import { normalizeName } from "./utils";
import { FacultyAvailabilityService } from "@/services/FacultyAvailabilityService";

export class ImportMappingService {
  static async importExcel(
    rows: MappingRow[],
    slots: LabSlotRow[]
  ): Promise<ImportResult> {
    console.log("Importing rows:", rows);
    console.log("Importing slots:", slots);

    // 1) fetch faculty details to match names
    const { data: facs } = await supabase
      .from("employee_details")
      .select("id, full_name");

    const facMap = Object.fromEntries(
      (facs || []).map((f: any) => [
        normalizeName(f.full_name),
        f.id
      ])
    );

    // 2) fetch subject data
    const { data: subjects } = await supabase
      .from("subjects")
      .select("id, subject_code, subject_name, subject_type");

    // Build lookup maps for faster matching
    const subjectByCode: Record<string, any> = {};
    const subjectByNameAndCode: Record<string, any> = {};

    (subjects || []).forEach((s) => {
      subjectByCode[s.subject_code] = s;
      subjectByNameAndCode[`${s.subject_code}|${s.subject_name.toLowerCase()}`] = s;
    });

    // 3) prepare the mappings data
    const validMappings = [];
    const invalidMappings = [];

    for (const r of rows) {
      // Try to find faculty IDs
      const faculty1Id = facMap[normalizeName(r.faculty1)];
      const faculty2Id = r.faculty2 ? facMap[normalizeName(r.faculty2)] : null;

      if (!faculty1Id) {
        console.warn(`Faculty "${r.faculty1}" not found in database`);
        invalidMappings.push({...r, reason: `Faculty "${r.faculty1}" not found in database`});
        continue; // Skip this mapping
      }

      // Try to find subject by code and name first, then just by code
      const lookupKey = `${r.subject_code}|${r.subject_name.toLowerCase()}`;
      const subject = subjectByNameAndCode[lookupKey] || subjectByCode[r.subject_code];

      validMappings.push({
        academic_year:   r.academic_year,
        department:      r.department,
        semester:        r.semester,
        section:         r.section,
        subject_id:      subject?.id || null,
        subject_code:    r.subject_code,
        subject_name:    r.subject_name,
        subject_type:    r.subject_type,
        faculty_1_id:    faculty1Id,
        faculty_2_id:    faculty2Id,
        hours_per_week:  Number(r.hours_per_week) || 0,
        classroom:       r.classroom || "Not specified",
        slots_per_week:  r.subject_type === "laboratory" ? (Number(r.slots_per_week) || 1) : null,
      });
    }

    if (validMappings.length === 0) {
      throw new Error("No valid mappings found. Please check that faculty names match those in the system.");
    }

    // 4) Insert the valid mappings
    const { data: inserted, error: mappingError } = await supabase
      .from("subject_faculty_mappings")
      .insert(validMappings)
      .select("id, subject_code, subject_name");

    if (mappingError) {
      console.error("Error inserting mappings:", mappingError);
      throw new Error(`Failed to insert mappings: ${mappingError.message}`);
    }

    if (!inserted || inserted.length === 0) {
      throw new Error("No mappings were inserted");
    }

    // 5) Create a lookup from parentRowIndex to the inserted mapping id
    const mappingIdLookup: Record<number, string> = {};

    // Match inserted rows with original data based on subject_code and subject_name
    rows.forEach((row, index) => {
      const match = inserted.find(ins =>
        ins.subject_code === row.subject_code &&
        ins.subject_name === row.subject_name
      );

      if (match) {
        mappingIdLookup[index] = match.id;
      }
    });

    // 6) If we have lab slots, insert them
    if (slots.length > 0) {
      const slotPayload = slots
        .filter(slot => mappingIdLookup[slot.parentRowIndex])
        .map(slot => ({
          mapping_id: mappingIdLookup[slot.parentRowIndex],
          slot_order: slot.slot_order,
          batch_name: slot.batch_name,
          day: slot.day,
          time_of_day: slot.time_of_day || "Morning",
        }));

      if (slotPayload.length > 0) {
        const { error: slotError } = await supabase
          .from("lab_time_slots")
          .insert(slotPayload);

        if (slotError) {
          console.error("Error inserting lab slots:", slotError);
          throw new Error(`Failed to insert lab slots: ${slotError.message}`);
        }

        // Update faculty availability for each lab slot
        console.log("Updating faculty availability for imported lab slots");

        // Create a mapping of mapping_id to faculty IDs
        const mappingToFacultyMap: Record<string, { faculty1Id: string, faculty2Id: string | null }> = {};

        // Get faculty information for each mapping
        for (const row of rows) {
          const index = rows.indexOf(row);
          const mappingId = mappingIdLookup[index];

          if (mappingId) {
            const faculty1Id = facMap[normalizeName(row.faculty1)];
            const faculty2Id = row.faculty2 ? facMap[normalizeName(row.faculty2)] : null;

            if (faculty1Id) {
              mappingToFacultyMap[mappingId] = {
                faculty1Id,
                faculty2Id
              };
            }
          }
        }

        // Update faculty availability for each lab slot
        for (const slot of slotPayload) {
          try {
            const facultyInfo = mappingToFacultyMap[slot.mapping_id];

            if (facultyInfo) {
              await FacultyAvailabilityService.updateFacultyAvailabilityFromLabSlots(
                facultyInfo.faculty1Id,
                facultyInfo.faculty2Id,
                slot.day,
                slot.time_of_day
              );
              console.log(`Updated faculty availability for lab slot on ${slot.day} at ${slot.time_of_day}`);
            }
          } catch (availabilityError) {
            console.error("Error updating faculty availability:", availabilityError);
            // Continue with the next slot even if this one fails
          }
        }
      }
    }

    if (invalidMappings.length > 0) {
      console.warn(`Skipped ${invalidMappings.length} invalid mappings:`, invalidMappings);
    }

    console.log(`Successfully imported ${inserted.length} mappings and ${slots.length} lab slots`);

    // Return information about skipped entries
    const result: ImportResult = {
      imported: inserted.length,
      skipped: invalidMappings.length,
    };

    if (invalidMappings.length > 0) {
      result.reasons = invalidMappings.map(m => `${m.subject_name}: ${m.reason}`);
    }

    return result;
  }
}
