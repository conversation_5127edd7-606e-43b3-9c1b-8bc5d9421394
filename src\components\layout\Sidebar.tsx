

import { Link, useLocation } from "react-router-dom";
import { Calendar, FileText, Home, Users, Clock, Settings, UserCheck, BarChart3, GraduationCap, Grid3X3, TrendingUp, Target, CheckCircle, Crown, Brain, MessageSquare, UserCog } from "lucide-react";
import { cn } from "@/lib/utils";
import IconWrapper from "@/components/icons/IconWrapper";
import { Button } from "@/components/ui/button";
import { useUserRole } from "@/hooks/useUserRole";

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const Sidebar = ({ isOpen, setIsOpen }: SidebarProps) => {
  const location = useLocation();
  const { isCollegeAdmin, isTimetableCoordinator, isFaculty, isClassTeacher, isHOD, isPrincipal } = useUserRole();

  // Define navigation items based on role
  const getNavigationItems = () => {
    const baseNavigation = [
      {
        name: "Dashboard",
        href: "/dashboard",
        icon: Home,
        group: "Main",
      },
    ];

    if (isCollegeAdmin) {
      // College Admin: Dashboard, Users, and Student Management
      return [
        ...baseNavigation,
        {
          name: "Users",
          href: "/users",
          icon: Users,
          group: "Administration",
        },
        {
          name: "Student Management",
          href: "/students",
          icon: GraduationCap,
          group: "Administration",
        },
      ];
    }

    if (isTimetableCoordinator) {
      // Timetable Coordinator: Access to timetable features + student management
      const coordinatorNav = [
        ...baseNavigation,
        {
          name: "Subject Allotment",
          href: "/subjects",
          icon: FileText,
          group: "Timetable Management",
        },
        {
          name: "Timetable Generator",
          href: "/timetable",
          icon: Calendar,
          group: "Timetable Management",
        },
        {
          name: "Timetable Structure",
          href: "/timetable-structure",
          icon: Clock,
          group: "Timetable Management",
        },
        {
          name: "Student Management",
          href: "/students",
          icon: GraduationCap,
          group: "Administration",
        },
        {
          name: "Assign Class Teacher",
          href: "/class-teacher",
          icon: Users,
          group: "Administration",
        },
        {
          name: "Reset Faculty",
          href: "/faculty-availability-reset",
          icon: Settings,
          group: "System",
        },
      ];

      // Add class teacher specific features if user is also a class teacher
      if (isClassTeacher) {
        coordinatorNav.splice(-1, 0, {
          name: "Academic Reports",
          href: "/academic-reports",
          icon: BarChart3,
          group: "Class Teacher",
        });
      }

      return coordinatorNav;
    }

    // Faculty role: Access to attendance and IA entry
    if (isFaculty) {
      const facultyNav = [
        {
          name: "Analytics Dashboard",
          href: "/faculty-analytics",
          icon: TrendingUp,
          group: "Faculty"
        },
        {
          name: "Mark Attendance",
          href: "/attendance",
          icon: UserCheck,
          group: "Faculty"
        },
        {
          name: "Internal Assessment",
          href: "/sequential-ia-entry",
          icon: Target,
          group: "Faculty"
        },
        {
          name: "Attendance Sheet",
          href: "/faculty-attendance-sheet",
          icon: Grid3X3,
          group: "Faculty"
        },
        {
          name: "Faculty Reports",
          href: "/faculty-reports",
          icon: FileText,
          group: "Faculty"
        },
        {
          name: "Leave Request",
          href: "/leave-management",
          icon: Calendar,
          group: "Faculty"
        },
        {
          name: "AI Quiz Management",
          href: "/ai-quiz-management",
          icon: Brain,
          group: "Faculty"
        },
        {
          name: "Student Proctoring",
          href: "/student-proctoring",
          icon: UserCog,
          group: "Faculty"
        },
      ];

      // Add HOD specific features if user has HOD role
      if (isHOD) {
        facultyNav.push(
          {
            name: "Leave Approvals",
            href: "/leave-approvals",
            icon: CheckCircle,
            group: "HOD"
          },
          {
            name: "Feedback Management",
            href: "/feedback-management",
            icon: MessageSquare,
            group: "HOD"
          }
        );
      }

      // Add Principal specific features if user has Principal role
      if (isPrincipal) {
        facultyNav.push({
          name: "HOD Leave Approvals",
          href: "/hod-leave-approvals",
          icon: Crown,
          group: "Principal"
        });
      }

      // Add class teacher specific features if user is also a class teacher
      if (isClassTeacher) {
        facultyNav.push(
          {
            name: "Student Management",
            href: "/class-teacher-students",
            icon: GraduationCap,
            group: "Class Teacher"
          },
          {
            name: "Academic Reports",
            href: "/academic-reports",
            icon: BarChart3,
            group: "Class Teacher"
          },
          {
            name: "Feedback Tracking",
            href: "/feedback-tracking",
            icon: MessageSquare,
            group: "Class Teacher"
          }
        );
      }

      return facultyNav;
    }

    // Default: Only dashboard for unknown roles
    return baseNavigation;
  };

  const navigation = getNavigationItems();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // Group navigation items by their group property
  const groupedNavigation = navigation.reduce((groups: { [key: string]: any[] }, item: any) => {
    const group = item.group || 'Main';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(item);
    return groups;
  }, {});

  return (
    <div
      className={cn(
        "fixed inset-y-0 left-0 z-20 flex w-64 flex-col bg-sidebar/95 backdrop-blur supports-[backdrop-filter]:bg-sidebar/90 transition-all duration-300 ease-in-out border-r border-sidebar-border shadow-strong",
        isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
      )}
    >
      <div className="flex h-16 items-center border-b border-sidebar-border px-6 bg-sidebar/50">
        <Link to="/" className="flex items-center gap-2 group transition-all duration-200 hover:scale-105">
          <IconWrapper icon={Calendar} className="h-6 w-6 text-sidebar-primary group-hover:text-primary transition-colors duration-200" />
          <span className="text-xl font-bold bg-gradient-to-r from-sidebar-primary to-accent bg-clip-text text-transparent">EduFlow</span>
        </Link>
      </div>
      <nav className="flex-1 overflow-auto py-6 px-4 space-y-4">
        {Object.entries(groupedNavigation).map(([groupName, items]) => (
          <div key={groupName} className="space-y-2">
            {/* Group Header */}
            {groupName !== 'Main' && (
              <div className="px-3 py-2">
                <h3 className="text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider">
                  {groupName}
                </h3>
              </div>
            )}

            {/* Group Items */}
            <div className="space-y-1">
              {items.map((item) => (
                <Link key={item.name} to={item.href} className="block">
                  <Button
                    variant={isActive(item.href) ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start text-left h-11 rounded-lg transition-all duration-200 hover:scale-[1.02] hover:shadow-sm",
                      isActive(item.href)
                        ? "font-medium bg-accent/80 text-accent-foreground shadow-sm"
                        : "hover:bg-accent/50 text-sidebar-foreground/80 hover:text-sidebar-foreground"
                    )}
                  >
                    <IconWrapper
                      icon={item.icon}
                      className={cn(
                        "mr-3 h-4 w-4 transition-colors duration-200",
                        isActive(item.href) ? "text-accent-foreground" : "text-sidebar-foreground/60"
                      )}
                    />
                    <span className="text-sm">{item.name}</span>
                  </Button>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </nav>
      <div className="border-t border-sidebar-border p-4 bg-sidebar/30">
        <div className="flex flex-col space-y-1 p-3 rounded-lg bg-sidebar/50">
          <span className="text-sm font-medium text-sidebar-foreground">EduFlow v1.0</span>
          <span className="text-xs text-sidebar-foreground/70">Academic ERP System</span>
          <div className="mt-2 h-1 w-full bg-sidebar-border rounded-full overflow-hidden">
            <div className="h-full w-3/4 bg-gradient-to-r from-primary to-accent rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
