// TimetableUpdateService.ts
import { supabase } from "@/integrations/supabase/client";
import { TimetableSlot } from "./TimetableService";
import { RescheduledSlotInfo } from "@/types/ConflictResolution";

/**
 * Service to handle database updates for timetable modifications
 */
export class TimetableUpdateService {
  /**
   * Update timetable slots in the database after conflict resolution
   */
  static async updateRescheduledSlots(
    rescheduledSlots: RescheduledSlotInfo[]
  ): Promise<{
    success: boolean;
    updatedSlots: number;
    errors: string[];
  }> {
    console.log(`🔄 Updating ${rescheduledSlots.length} rescheduled slots in database...`);
    
    const errors: string[] = [];
    let updatedSlots = 0;

    for (const rescheduled of rescheduledSlots) {
      try {
        // Update the slot in timetable_slots table
        const { error: updateError } = await supabase
          .from('timetable_slots')
          .update({
            day: rescheduled.newSlot.day,
            time_slot: rescheduled.newSlot.time_slot,
            updated_at: new Date().toISOString()
          })
          .eq('id', rescheduled.originalSlot.id);

        if (updateError) {
          console.error(`Error updating slot ${rescheduled.originalSlot.subject_code}:`, updateError);
          errors.push(`Failed to update ${rescheduled.originalSlot.subject_code}: ${updateError.message}`);
        } else {
          updatedSlots++;
          console.log(`✅ Updated ${rescheduled.originalSlot.subject_code} in database`);
        }

        // Also update faculty_timetables table if it exists
        try {
          const { error: facultyUpdateError } = await supabase
            .from('faculty_timetables')
            .update({
              day: rescheduled.newSlot.day,
              time_slot: rescheduled.newSlot.time_slot,
              updated_at: new Date().toISOString()
            })
            .eq('subject_code', rescheduled.originalSlot.subject_code)
            .eq('faculty_id', rescheduled.facultyId)
            .eq('academic_year', rescheduled.originalSlot.academic_year)
            .eq('semester', rescheduled.originalSlot.semester)
            .eq('section', rescheduled.originalSlot.section);

          if (facultyUpdateError) {
            console.warn(`Warning: Could not update faculty_timetables for ${rescheduled.originalSlot.subject_code}:`, facultyUpdateError);
          }
        } catch (facultyError) {
          console.warn(`Faculty timetables table may not exist or be accessible:`, facultyError);
        }

        // Also update class_timetables table if it exists
        try {
          const { error: classUpdateError } = await supabase
            .from('class_timetables')
            .update({
              day: rescheduled.newSlot.day,
              time_slot: rescheduled.newSlot.time_slot,
              updated_at: new Date().toISOString()
            })
            .eq('subject_code', rescheduled.originalSlot.subject_code)
            .eq('academic_year', rescheduled.originalSlot.academic_year)
            .eq('semester', rescheduled.originalSlot.semester)
            .eq('section', rescheduled.originalSlot.section);

          if (classUpdateError) {
            console.warn(`Warning: Could not update class_timetables for ${rescheduled.originalSlot.subject_code}:`, classUpdateError);
          }
        } catch (classError) {
          console.warn(`Class timetables table may not exist or be accessible:`, classError);
        }

      } catch (error) {
        console.error(`Exception updating slot ${rescheduled.originalSlot.subject_code}:`, error);
        errors.push(`Exception updating ${rescheduled.originalSlot.subject_code}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    const success = updatedSlots > 0 && errors.length === 0;
    console.log(`📊 Database update completed: ${updatedSlots}/${rescheduledSlots.length} slots updated successfully`);

    if (errors.length > 0) {
      console.error(`❌ ${errors.length} errors occurred during database updates`);
      errors.forEach(error => console.error(`  • ${error}`));
    }

    return {
      success,
      updatedSlots,
      errors
    };
  }

  /**
   * Validate that the rescheduled slots don't create new conflicts
   */
  static async validateRescheduledSlots(
    rescheduledSlots: RescheduledSlotInfo[]
  ): Promise<{
    isValid: boolean;
    conflicts: string[];
  }> {
    console.log(`🔍 Validating ${rescheduledSlots.length} rescheduled slots for conflicts...`);
    
    const conflicts: string[] = [];

    for (const rescheduled of rescheduledSlots) {
      try {
        // Check for faculty conflicts at the new time slot
        const { data: facultyConflicts, error: facultyError } = await supabase
          .from('timetable_slots')
          .select('subject_code, semester, section')
          .eq('faculty_id', rescheduled.facultyId)
          .eq('day', rescheduled.newSlot.day)
          .eq('time_slot', rescheduled.newSlot.time_slot)
          .eq('academic_year', rescheduled.originalSlot.academic_year)
          .neq('id', rescheduled.originalSlot.id);

        if (facultyError) {
          console.error(`Error checking faculty conflicts for ${rescheduled.originalSlot.subject_code}:`, facultyError);
          conflicts.push(`Could not validate faculty conflicts for ${rescheduled.originalSlot.subject_code}`);
          continue;
        }

        if (facultyConflicts && facultyConflicts.length > 0) {
          facultyConflicts.forEach(conflict => {
            conflicts.push(`Faculty conflict: ${rescheduled.originalSlot.subject_code} conflicts with ${conflict.subject_code} in ${conflict.semester}-${conflict.section} at ${rescheduled.newSlot.day} ${rescheduled.newSlot.time_slot}`);
          });
        }

        // Check for class conflicts at the new time slot
        const { data: classConflicts, error: classError } = await supabase
          .from('timetable_slots')
          .select('subject_code, faculty_name')
          .eq('semester', rescheduled.originalSlot.semester)
          .eq('section', rescheduled.originalSlot.section)
          .eq('day', rescheduled.newSlot.day)
          .eq('time_slot', rescheduled.newSlot.time_slot)
          .eq('academic_year', rescheduled.originalSlot.academic_year)
          .neq('id', rescheduled.originalSlot.id);

        if (classError) {
          console.error(`Error checking class conflicts for ${rescheduled.originalSlot.subject_code}:`, classError);
          conflicts.push(`Could not validate class conflicts for ${rescheduled.originalSlot.subject_code}`);
          continue;
        }

        if (classConflicts && classConflicts.length > 0) {
          classConflicts.forEach(conflict => {
            conflicts.push(`Class conflict: ${rescheduled.originalSlot.subject_code} conflicts with ${conflict.subject_code} (${conflict.faculty_name}) at ${rescheduled.newSlot.day} ${rescheduled.newSlot.time_slot}`);
          });
        }

      } catch (error) {
        console.error(`Exception validating ${rescheduled.originalSlot.subject_code}:`, error);
        conflicts.push(`Exception validating ${rescheduled.originalSlot.subject_code}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    const isValid = conflicts.length === 0;
    
    if (isValid) {
      console.log(`✅ All rescheduled slots validated successfully - no conflicts detected`);
    } else {
      console.error(`❌ Validation failed: ${conflicts.length} conflicts detected`);
      conflicts.forEach(conflict => console.error(`  • ${conflict}`));
    }

    return {
      isValid,
      conflicts
    };
  }
}
