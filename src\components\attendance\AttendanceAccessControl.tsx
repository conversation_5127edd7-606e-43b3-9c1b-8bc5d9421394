import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Shield, 
  ShieldCheck, 
  ShieldX, 
  UserCheck, 
  Calendar,
  Clock,
  AlertTriangle,
  Info
} from 'lucide-react';
import { AttendanceAccessResult } from '@/services/AttendanceAccessControlService';

interface AttendanceAccessControlProps {
  accessResult: AttendanceAccessResult;
  classInfo: {
    subject_code: string;
    subject_name?: string;
    semester: string;
    section: string;
    time_slot?: string;
  };
  date: string;
  onRetry?: () => void;
}

export const AttendanceAccessControl: React.FC<AttendanceAccessControlProps> = ({
  accessResult,
  classInfo,
  date,
  onRetry
}) => {
  if (accessResult.canAccess && accessResult.reason === 'normal_access') {
    // Normal access - no need to show anything
    return null;
  }

  if (accessResult.canAccess && accessResult.reason === 'substitute_access') {
    // Substitute faculty has access - show informational message
    return (
      <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-900/30">
        <UserCheck className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800 dark:text-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <strong>Substitute Assignment:</strong> You are authorized to mark attendance for this class as a substitute faculty member.
            </div>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              <Shield className="h-3 w-3 mr-1" />
              Substitute Access
            </Badge>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (!accessResult.canAccess && accessResult.reason === 'faculty_on_leave') {
    // Faculty is on approved leave - show leave message
    return (
      <Alert className="border-orange-200 bg-orange-50 dark:bg-orange-900/30">
        <ShieldX className="h-4 w-4 text-orange-600" />
        <AlertDescription className="text-orange-800 dark:text-orange-200">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <strong>Attendance Access Restricted</strong>
              </div>
              <Badge variant="destructive" className="bg-orange-100 text-orange-800 border-orange-300">
                <Calendar className="h-3 w-3 mr-1" />
                On Leave
              </Badge>
            </div>

            <div className="bg-orange-100 dark:bg-orange-800/50 p-3 rounded-md space-y-2">
              <p className="font-medium">You are currently on approved leave for this date.</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span><strong>Date:</strong> {new Date(date).toLocaleDateString()}</span>
                </div>

                {classInfo.time_slot && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span><strong>Time:</strong> {classInfo.time_slot}</span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  <span><strong>Class:</strong> {classInfo.subject_code} (Sem {classInfo.semester} Sec {classInfo.section})</span>
                </div>
              </div>

              <div className="mt-3 p-2 bg-orange-200 dark:bg-orange-700/50 rounded text-sm">
                <AlertTriangle className="h-4 w-4 inline mr-2" />
                <strong>Note:</strong> Attendance marking is disabled while you are on approved leave.
                If a substitute has been assigned, they will handle attendance for your classes.
              </div>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (!accessResult.canAccess && accessResult.reason === 'substitute_assigned') {
    // Original faculty is blocked - show blocking message
    const blockingInfo = accessResult.blockingInfo!;

    return (
      <Alert className="border-red-200 bg-red-50 dark:bg-red-900/30">
        <ShieldX className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800 dark:text-red-200">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <strong>Attendance Access Restricted</strong>
              </div>
              <Badge variant="destructive">
                <ShieldX className="h-3 w-3 mr-1" />
                Access Blocked
              </Badge>
            </div>

            <div className="bg-red-100 dark:bg-red-800/50 p-3 rounded-md space-y-2">
              <p className="font-medium">This class is currently being handled by a substitute faculty:</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <UserCheck className="h-4 w-4" />
                  <span><strong>Substitute Faculty:</strong> {blockingInfo.substitute_faculty_name}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span><strong>Date:</strong> {new Date(date).toLocaleDateString()}</span>
                </div>

                {classInfo.time_slot && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span><strong>Time:</strong> {classInfo.time_slot}</span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  <span><strong>Class:</strong> {classInfo.subject_code} (Sem {classInfo.semester} Sec {classInfo.section})</span>
                </div>
              </div>

              <div className="mt-3 p-2 bg-red-200 dark:bg-red-700/50 rounded text-sm">
                <AlertTriangle className="h-4 w-4 inline mr-2" />
                <strong>Note:</strong> Attendance for this class should be marked by the substitute faculty member.
                If you need to mark attendance, please contact the substitute faculty or your department HOD.
              </div>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (accessResult.reason === 'error_fallback') {
    // Error occurred but access is allowed as fallback
    return (
      <Alert className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/30">
        <AlertTriangle className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-800 dark:text-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <strong>Access Control Warning:</strong> Unable to verify substitute assignments. 
              Proceeding with normal attendance marking.
            </div>
            <div className="flex items-center gap-2">
              {onRetry && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={onRetry}
                  className="text-yellow-800 border-yellow-300"
                >
                  Retry Check
                </Button>
              )}
              <Badge variant="outline" className="border-yellow-300 text-yellow-800">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Fallback Access
              </Badge>
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  // Default case - should not happen
  return (
    <Alert className="border-gray-200 bg-gray-50">
      <Shield className="h-4 w-4 text-gray-600" />
      <AlertDescription className="text-gray-800">
        <div className="flex items-center justify-between">
          <div>
            <strong>Access Status:</strong> {accessResult.canAccess ? 'Allowed' : 'Restricted'} 
            {accessResult.reason && ` (${accessResult.reason})`}
          </div>
          <Badge variant={accessResult.canAccess ? "default" : "destructive"}>
            {accessResult.canAccess ? (
              <ShieldCheck className="h-3 w-3 mr-1" />
            ) : (
              <ShieldX className="h-3 w-3 mr-1" />
            )}
            {accessResult.canAccess ? 'Access Granted' : 'Access Denied'}
          </Badge>
        </div>
      </AlertDescription>
    </Alert>
  );
};
