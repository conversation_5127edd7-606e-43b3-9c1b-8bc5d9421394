
import { useCallback } from "react";
import { useToast } from "@/hooks/use-toast";

interface UseRefreshMappingsProps {
  loadMappings: () => Promise<void>;
  filtersValid: boolean;
  year: string;
  dept: string;
  sem: string;
  section: string;
}

export const useRefreshMappings = ({
  loadMappings,
  filtersValid,
  year,
  dept,
  sem,
  section
}: UseRefreshMappingsProps) => {
  const { toast } = useToast();
  
  // Load mappings callback (memoized to prevent unnecessary rerenders)
  const refreshMappings = useCallback(async () => {
    if (filtersValid) {
      console.log("Refreshing mappings for", year, dept, sem, section);
      try {
        await loadMappings();
      } catch (error) {
        console.error("Error refreshing mappings:", error);
        toast({
          title: "Error refreshing mappings",
          description: "Failed to load subject-faculty mappings",
          variant: "destructive"
        });
      }
    }
  }, [loadMappings, filtersValid, year, dept, sem, section, toast]);

  return refreshMappings;
};
