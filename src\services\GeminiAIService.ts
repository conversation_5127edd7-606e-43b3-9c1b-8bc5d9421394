/**
 * Google Gemini AI Service
 * Alternative to OpenAI for quiz generation
 */

import { 
  AIQuestionGenerationRequest, 
  AIQuestionGenerationResponse, 
  AIGeneratedQuestion,
  DifficultyLevel 
} from '@/types/quiz-system';

const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

// Try multiple Gemini model endpoints
const GEMINI_MODELS = [
  'gemini-1.5-flash-latest',
  'gemini-1.5-flash',
  'gemini-pro',
  'gemini-1.0-pro'
];

const getGeminiURL = (model: string) =>
  `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;

export class GeminiAIService {
  
  /**
   * Generate quiz questions using Google Gemini
   */
  static async generateQuestions(request: AIQuestionGenerationRequest): Promise<AIQuestionGenerationResponse> {
    const startTime = Date.now();
    
    try {
      if (!GEMINI_API_KEY) {
        throw new Error('Gemini API key not configured');
      }

      console.log('🚀 Generating questions with Google Gemini...');
      console.log('🔑 API Key status:', {
        hasKey: !!GEMINI_API_KEY,
        keyLength: GEMINI_API_KEY?.length || 0,
        keyPrefix: GEMINI_API_KEY?.substring(0, 10) + '...'
      });
      console.log('📊 Request details:', {
        subject: request.subject_name,
        difficulty: request.difficulty_level,
        questionCount: request.question_count,
        questionTypes: request.question_types,
        textLength: request.extracted_text?.length || 0
      });

      const prompt = this.buildPrompt(request);

      // Try multiple models until one works
      let lastError: Error | null = null;

      for (const model of GEMINI_MODELS) {
        try {
          console.log(`Trying Gemini model: ${model}`);

          const response = await fetch(`${getGeminiURL(model)}?key=${GEMINI_API_KEY}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contents: [{
                parts: [{
                  text: prompt
                }]
              }],
              generationConfig: {
                temperature: 0.7,
                maxOutputTokens: 4000,
              }
            })
          });

          if (response.ok) {
            console.log(`✅ Success with model: ${model}`);
            const data = await response.json();
            console.log('📥 Gemini response data:', JSON.stringify(data, null, 2));

            const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

            if (!generatedText) {
              console.error('❌ No content generated by Gemini. Response structure:', data);
              throw new Error('No content generated by Gemini');
            }

            console.log('📝 Generated text:', generatedText);

            // Parse JSON from the generated text
            const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
              console.error('❌ No valid JSON found in response:', generatedText);
              throw new Error('No valid JSON found in Gemini response');
            }

            console.log('🔍 Extracted JSON:', jsonMatch[0]);
            const aiResponse = JSON.parse(jsonMatch[0]);
            const processingTime = Date.now() - startTime;

            console.log('✅ Parsed AI response successfully:', {
              questionCount: aiResponse.questions?.length,
              confidence: aiResponse.confidence_score
            });

            return {
              questions: this.validateAndFormatQuestions(aiResponse.questions),
              processing_time_ms: processingTime,
              confidence_score: aiResponse.confidence_score || 0.85,
              source_analysis: {
                key_topics: aiResponse.key_topics || [],
                content_complexity: aiResponse.content_complexity || request.difficulty_level,
                suggested_question_distribution: aiResponse.question_distribution || {}
              }
            };
          } else {
            const errorText = await response.text();
            console.error(`❌ Model ${model} failed:`, {
              status: response.status,
              statusText: response.statusText,
              error: errorText
            });
            lastError = new Error(`Gemini API error: ${response.status} - ${errorText}`);
          }
        } catch (error) {
          console.warn(`❌ Model ${model} failed:`, error);
          lastError = error as Error;
        }
      }

      // If all models failed, throw the last error
      throw lastError || new Error('All Gemini models failed');

    } catch (error) {
      console.error('Gemini question generation failed:', error);
      throw error;
    }
  }

  /**
   * Analyze content complexity using Gemini
   */
  static async analyzeContentComplexity(text: string, subjectName: string): Promise<{
    complexity: DifficultyLevel;
    keyTopics: string[];
    suggestedDistribution: Record<string, number>;
  }> {
    try {
      if (!GEMINI_API_KEY) {
        throw new Error('Gemini API key not configured');
      }

      const prompt = `
Analyze the following educational content for the subject "${subjectName}" and provide a JSON response:

CONTENT:
${text.substring(0, 2000)}...

Return only a JSON object with this exact structure:
{
  "complexity": "easy|medium|hard",
  "keyTopics": ["topic1", "topic2", "topic3"],
  "suggestedDistribution": {
    "multiple_choice": 15,
    "true_false": 3,
    "short_answer": 2,
    "essay": 0
  }
}

Base the complexity on vocabulary level, concept depth, and technical requirements.
`;

      // Try multiple models until one works
      let lastError: Error | null = null;

      for (const model of GEMINI_MODELS) {
        try {
          console.log(`Trying Gemini model for analysis: ${model}`);

          const response = await fetch(`${getGeminiURL(model)}?key=${GEMINI_API_KEY}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contents: [{
                parts: [{
                  text: prompt
                }]
              }],
              generationConfig: {
                temperature: 0.3,
                maxOutputTokens: 1000,
              }
            })
          });

          if (response.ok) {
            console.log(`✅ Analysis success with model: ${model}`);
            const data = await response.json();
            const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

            const jsonMatch = generatedText?.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
              throw new Error('No valid JSON in Gemini analysis response');
            }

            const analysis = JSON.parse(jsonMatch[0]);

            return {
              complexity: analysis.complexity || 'medium',
              keyTopics: analysis.keyTopics || [],
              suggestedDistribution: analysis.suggestedDistribution || {
                multiple_choice: 15,
                true_false: 3,
                short_answer: 2,
                essay: 0
              }
            };
          } else {
            const errorText = await response.text();
            console.warn(`❌ Analysis model ${model} failed: ${response.status} - ${errorText}`);
            lastError = new Error(`Gemini analysis failed: ${response.status} - ${errorText}`);
          }
        } catch (error) {
          console.warn(`❌ Analysis model ${model} failed:`, error);
          lastError = error as Error;
        }
      }

      // If all models failed, throw the last error
      throw lastError || new Error('All Gemini analysis models failed');

    } catch (error) {
      console.error('Gemini content analysis failed:', error);
      throw error;
    }
  }

  /**
   * Build prompt for Gemini
   */
  private static buildPrompt(request: AIQuestionGenerationRequest): string {
    return `
Generate ${request.question_count} high-quality quiz questions from the following course content for the subject "${request.subject_name}".

CONTENT TO ANALYZE:
${request.extracted_text}

REQUIREMENTS:
- Difficulty Level: ${request.difficulty_level}
- Question Types: ${request.question_types.join(', ')}
- Subject: ${request.subject_name}

Return ONLY a JSON object with this exact structure:
{
  "questions": [
    {
      "question_text": "Clear question text",
      "question_type": "multiple_choice|true_false|short_answer|essay",
      "options": {"A": "Option 1", "B": "Option 2", "C": "Option 3", "D": "Option 4"},
      "correct_answer": "A|B|C|D|true|false|answer text",
      "difficulty_level": "easy|medium|hard",
      "topic_tags": ["topic1", "topic2"],
      "ai_confidence_score": 0.95,
      "source_material_reference": "Brief reference"
    }
  ],
  "confidence_score": 0.90,
  "key_topics": ["topic1", "topic2"],
  "content_complexity": "medium",
  "question_distribution": {
    "multiple_choice": 15,
    "true_false": 3,
    "short_answer": 2
  }
}

Generate exactly ${request.question_count} questions. Ensure all questions are directly related to the provided content.
`;
  }

  /**
   * Validate and format questions (same as OpenAI service)
   */
  private static validateAndFormatQuestions(questions: any[]): AIGeneratedQuestion[] {
    return questions.map((q, index) => {
      if (!q.question_text || !q.question_type || !q.correct_answer) {
        throw new Error(`Invalid question at index ${index}: missing required fields`);
      }

      if (!['multiple_choice', 'true_false', 'short_answer', 'essay'].includes(q.question_type)) {
        throw new Error(`Invalid question type at index ${index}: ${q.question_type}`);
      }

      if (q.question_type === 'multiple_choice' && (!q.options || Object.keys(q.options).length < 2)) {
        throw new Error(`Multiple choice question at index ${index} missing valid options`);
      }

      return {
        question_text: q.question_text.trim(),
        question_type: q.question_type,
        options: q.question_type === 'multiple_choice' ? q.options : undefined,
        correct_answer: q.correct_answer,
        difficulty_level: q.difficulty_level || 'medium',
        topic_tags: Array.isArray(q.topic_tags) ? q.topic_tags : [],
        ai_confidence_score: Math.min(Math.max(q.ai_confidence_score || 0.8, 0), 1),
        source_material_reference: q.source_material_reference || ''
      };
    });
  }
}
