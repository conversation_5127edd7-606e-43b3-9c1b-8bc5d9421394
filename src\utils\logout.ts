/**
 * Standardized logout utility for the EduFlow application
 * Provides consistent logout behavior across all components and pages
 * Always redirects to the landing page (/) after successful logout
 */

import { supabase } from "@/integrations/supabase/client";

/**
 * Performs a complete logout for any type of user (faculty, admin, or student)
 * Clears all authentication data and redirects to landing page
 * OPTIMIZED: Clears local data immediately and makes Supabase signOut non-blocking
 */
export const performLogout = async (): Promise<void> => {
  try {
    // COMPREHENSIVE LOGOUT: Clear ALL possible authentication data
    localStorage.removeItem('student_session');
    localStorage.removeItem('auth_session');
    localStorage.removeItem('user_session');
    localStorage.removeItem('supabase.auth.token');
    localStorage.removeItem('sb-milmyotuougemocvieof-auth-token');
    localStorage.removeItem('sb-xjltzryjhdcvzbglcqvo-auth-token');

    // Clear all session storage
    sessionStorage.clear();

    // Clear any Supabase auth cookies
    document.cookie.split(";").forEach((c) => {
      const cookieName = c.replace(/^ +/, "").replace(/=.*/, "");
      if (cookieName.includes('supabase') || cookieName.includes('auth') || cookieName.includes('sb-')) {
        document.cookie = cookieName + "=;expires=" + new Date().toUTCString() + ";path=/";
        document.cookie = cookieName + "=;expires=" + new Date().toUTCString() + ";path=/;domain=" + window.location.hostname;
      }
    });

    // Set logout flag to prevent auto-login
    localStorage.setItem('logout_performed', 'true');

    // PERFORMANCE FIX: Start Supabase signOut in background (non-blocking)
    supabase.auth.signOut().catch((error) => {
      console.error('Background Supabase signOut error:', error);
    });

    // PERFORMANCE FIX: Immediate redirect for better user experience
    window.location.href = '/';
  } catch (error) {
    console.error('Logout error:', error);

    // Even if logout fails, redirect to landing page for security
    window.location.href = '/';
  }
};

/**
 * Quick logout function that can be called from anywhere
 * Includes error handling and always redirects to landing page
 * OPTIMIZED: Non-blocking for immediate logout experience
 */
export const quickLogout = (): void => {
  performLogout().catch((error) => {
    console.error('Quick logout failed:', error);
    // Force redirect even if logout fails
    window.location.href = '/';
  });
};

/**
 * PERFORMANCE OPTIMIZED: Instant logout function for immediate response
 * Clears all data immediately and redirects without any async operations
 * Use this when you need the fastest possible logout experience
 */
export const instantLogout = (): void => {
  // COMPREHENSIVE INSTANT LOGOUT: Clear ALL possible authentication data
  localStorage.removeItem('student_session');
  localStorage.removeItem('auth_session');
  localStorage.removeItem('user_session');
  localStorage.removeItem('supabase.auth.token');
  localStorage.removeItem('sb-milmyotuougemocvieof-auth-token');
  localStorage.removeItem('sb-xjltzryjhdcvzbglcqvo-auth-token');
  sessionStorage.clear();

  // Clear any Supabase auth cookies
  document.cookie.split(";").forEach((c) => {
    const cookieName = c.replace(/^ +/, "").replace(/=.*/, "");
    if (cookieName.includes('supabase') || cookieName.includes('auth') || cookieName.includes('sb-')) {
      document.cookie = cookieName + "=;expires=" + new Date().toUTCString() + ";path=/";
      document.cookie = cookieName + "=;expires=" + new Date().toUTCString() + ";path=/;domain=" + window.location.hostname;
    }
  });

  // Set logout flag to prevent auto-login
  localStorage.setItem('logout_performed', 'true');

  // Start Supabase signOut in background (fire and forget)
  supabase.auth.signOut().catch((error) => {
    console.error('Background Supabase signOut error:', error);
  });

  // Immediate redirect - no waiting
  window.location.href = '/';
};

/**
 * Logout with confirmation dialog
 * Shows a confirmation before logging out
 */
export const logoutWithConfirmation = (): void => {
  const confirmed = window.confirm('Are you sure you want to logout?');
  if (confirmed) {
    quickLogout();
  }
};

/**
 * Emergency logout function for security purposes
 * Immediately clears all data and redirects without waiting for API calls
 */
export const emergencyLogout = (): void => {
  // Clear all storage immediately
  localStorage.clear();
  sessionStorage.clear();
  
  // Clear cookies if any
  document.cookie.split(";").forEach((c) => {
    document.cookie = c
      .replace(/^ +/, "")
      .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
  });
  
  // Immediate redirect
  window.location.href = '/';
};
