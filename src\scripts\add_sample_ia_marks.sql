-- Sample IA Marks Data for Student Progress Testing
-- This script adds sample IA marks for student 1KS23CS001

-- First, let's get the student ID
-- Student USN: 1KS23CS001
-- Student ID: 4b4b0737-262f-4bc5-bc11-a17b29b4ee54 (from your logs)

-- Insert sample IA marks for different subjects
INSERT INTO internal_assessments (
    student_id,
    subject_code,
    department,
    semester,
    section,
    academic_year,
    ia1_marks,
    ia2_marks,
    ia3_marks,
    assignment_marks,
    lab_marks,
    created_at,
    updated_at
) VALUES 
-- BCS401 - ANALYSIS & DESIGN OF ALGORITHMS
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BCS401',
    'cse',
    '4',
    'A',
    '2024-25',
    22,  -- IA1: 22/25
    20,  -- IA2: 20/25
    24,  -- IA3: 24/25
    8,   -- Assignment: 8/10
    NULL, -- No lab marks for theory subject
    NOW(),
    NOW()
),
-- BCS402 - MICROCONTROLLERS
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BCS402',
    'cse',
    '4',
    'A',
    '2024-25',
    18,  -- IA1: 18/25
    21,  -- IA2: 21/25
    19,  -- IA3: 19/25
    7,   -- Assignment: 7/10
    NULL,
    NOW(),
    NOW()
),
-- BCS403 - DATABASE MANAGEMENT SYSTEMS
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BCS403',
    'cse',
    '4',
    'A',
    '2024-25',
    25,  -- IA1: 25/25 (Full marks)
    23,  -- IA2: 23/25
    22,  -- IA3: 22/25
    9,   -- Assignment: 9/10
    NULL,
    NOW(),
    NOW()
),
-- BCSL404 - ANALYSIS & DESIGN OF ALGORITHMS LAB
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BCSL404',
    'cse',
    '4',
    'A',
    '2024-25',
    20,  -- IA1: 20/25
    22,  -- IA2: 22/25
    21,  -- IA3: 21/25
    8,   -- Assignment: 8/10
    18,  -- Lab marks: 18/20
    NOW(),
    NOW()
),
-- BCS405A - DISCRETE MATHEMATICAL STRUCTURES
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BCS405A',
    'cse',
    '4',
    'A',
    '2024-25',
    16,  -- IA1: 16/25
    19,  -- IA2: 19/25
    17,  -- IA3: 17/25
    6,   -- Assignment: 6/10
    NULL,
    NOW(),
    NOW()
),
-- BBOK407 - BIOLOGY FOR ENGINEERS
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BBOK407',
    'cse',
    '4',
    'A',
    '2024-25',
    21,  -- IA1: 21/25
    20,  -- IA2: 20/25
    23,  -- IA3: 23/25
    9,   -- Assignment: 9/10
    NULL,
    NOW(),
    NOW()
),
-- BCS456C - UI/UX
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BCS456C',
    'cse',
    '4',
    'A',
    '2024-25',
    19,  -- IA1: 19/25
    18,  -- IA2: 18/25
    20,  -- IA3: 20/25
    7,   -- Assignment: 7/10
    NULL,
    NOW(),
    NOW()
),
-- BUHK408 - UNIVERSAL HUMAN VALUES COURSE
(
    '4b4b0737-262f-4bc5-bc11-a17b29b4ee54',
    'BUHK408',
    'cse',
    '4',
    'A',
    '2024-25',
    24,  -- IA1: 24/25
    25,  -- IA2: 25/25 (Full marks)
    23,  -- IA3: 23/25
    10,  -- Assignment: 10/10 (Full marks)
    NULL,
    NOW(),
    NOW()
);

-- Verify the data was inserted
SELECT 
    subject_code,
    ia1_marks,
    ia2_marks,
    ia3_marks,
    assignment_marks,
    lab_marks,
    (COALESCE(ia1_marks, 0) + COALESCE(ia2_marks, 0) + COALESCE(ia3_marks, 0) + COALESCE(assignment_marks, 0) + COALESCE(lab_marks, 0)) as total_marks
FROM internal_assessments 
WHERE student_id = '4b4b0737-262f-4bc5-bc11-a17b29b4ee54'
ORDER BY subject_code;
