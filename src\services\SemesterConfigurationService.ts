import { supabase } from "@/integrations/supabase/client";

export interface SemesterConfiguration {
  id?: string;
  academic_year: string;
  department: string;
  semester: string;
  section: string;
  has_skill_lab: boolean;
  skill_lab_periods_per_week: number;
  skill_lab_placement_preference: 'vacant_days' | 'least_labs_day' | 'specific_day';
  skill_lab_preferred_day?: string;
  has_tutorial_hours: boolean;
  tutorial_periods_per_week: number;
  created_at?: string;
  updated_at?: string;
}

export interface LabGenerationConfig {
  hasSkillLab: boolean;
  skillLabPeriodsPerWeek: number;
  skillLabPlacement: 'vacant_days' | 'least_labs_day' | 'specific_day';
  skillLabPreferredDay?: string;
  hasTutorialHours: boolean;
  tutorialPeriodsPerWeek: number;
}

export interface TheoryGenerationConfig {
  hasTutorialHours: boolean;
  tutorialPeriodsPerWeek: number;
}

export class SemesterConfigurationService {
  /**
   * Get existing configuration for a semester-section
   */
  static async getConfiguration(
    academicYear: string,
    department: string,
    semester: string,
    section: string
  ): Promise<SemesterConfiguration | null> {
    try {
      const { data, error } = await supabase
        .from('semester_configurations')
        .select('*')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching semester configuration:', error);
      return null;
    }
  }

  /**
   * Save or update semester configuration
   */
  static async saveConfiguration(config: SemesterConfiguration): Promise<SemesterConfiguration> {
    try {
      const { data, error } = await supabase
        .from('semester_configurations')
        .upsert({
          academic_year: config.academic_year,
          department: config.department,
          semester: config.semester,
          section: config.section,
          has_skill_lab: config.has_skill_lab,
          skill_lab_periods_per_week: config.skill_lab_periods_per_week,
          skill_lab_placement_preference: config.skill_lab_placement_preference,
          skill_lab_preferred_day: config.skill_lab_preferred_day,
          has_tutorial_hours: config.has_tutorial_hours,
          tutorial_periods_per_week: config.tutorial_periods_per_week,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'academic_year,department,semester,section'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error saving semester configuration:', error);
      throw error;
    }
  }

  /**
   * Validate lab generation configuration
   */
  static validateLabConfig(config: LabGenerationConfig): { isValid: boolean; message?: string } {
    if (config.hasSkillLab) {
      if (config.skillLabPeriodsPerWeek < 1 || config.skillLabPeriodsPerWeek > 4) {
        return {
          isValid: false,
          message: 'Skill lab periods per week must be between 1 and 4'
        };
      }

      if (config.skillLabPlacement === 'specific_day' && !config.skillLabPreferredDay) {
        return {
          isValid: false,
          message: 'Please select a preferred day for skill lab placement'
        };
      }
    }

    if (config.hasTutorialHours) {
      if (config.tutorialPeriodsPerWeek < 1 || config.tutorialPeriodsPerWeek > 5) {
        return {
          isValid: false,
          message: 'Tutorial periods per week must be between 1 and 5 (max weekdays)'
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Validate theory generation configuration
   */
  static validateTheoryConfig(config: TheoryGenerationConfig): { isValid: boolean; message?: string } {
    if (config.hasTutorialHours) {
      if (config.tutorialPeriodsPerWeek < 1 || config.tutorialPeriodsPerWeek > 5) {
        return {
          isValid: false,
          message: 'Tutorial periods per week must be between 1 and 5 (max weekdays)'
        };
      }
    }

    return { isValid: true };
  }

  /**
   * Get default configuration for a semester
   */
  static getDefaultConfiguration(
    academicYear: string,
    department: string,
    semester: string,
    section: string
  ): SemesterConfiguration {
    return {
      academic_year: academicYear,
      department: department,
      semester: semester,
      section: section,
      has_skill_lab: false,
      skill_lab_periods_per_week: 0,
      skill_lab_placement_preference: 'vacant_days',
      has_tutorial_hours: false,
      tutorial_periods_per_week: 0
    };
  }

  /**
   * Get skill lab placement days based on configuration
   */
  static getSkillLabPlacementDays(
    config: LabGenerationConfig,
    existingLabDays: string[] = []
  ): string[] {
    const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const placementDays: string[] = [];

    switch (config.skillLabPlacement) {
      case 'vacant_days':
        // Find days without existing labs
        const vacantDays = allDays.filter(day => !existingLabDays.includes(day));
        placementDays.push(...vacantDays.slice(0, config.skillLabPeriodsPerWeek));
        break;

      case 'least_labs_day':
        // Find days with least number of labs (simplified - just avoid days with labs)
        const leastLabDays = allDays.filter(day => !existingLabDays.includes(day));
        if (leastLabDays.length >= config.skillLabPeriodsPerWeek) {
          placementDays.push(...leastLabDays.slice(0, config.skillLabPeriodsPerWeek));
        } else {
          // If not enough vacant days, use all vacant days and some with labs
          placementDays.push(...leastLabDays);
          const remainingNeeded = config.skillLabPeriodsPerWeek - leastLabDays.length;
          const daysWithLabs = allDays.filter(day => existingLabDays.includes(day));
          placementDays.push(...daysWithLabs.slice(0, remainingNeeded));
        }
        break;

      case 'specific_day':
        if (config.skillLabPreferredDay) {
          for (let i = 0; i < config.skillLabPeriodsPerWeek; i++) {
            placementDays.push(config.skillLabPreferredDay);
          }
        }
        break;
    }

    return placementDays;
  }

  /**
   * Get tutorial placement days with intelligent conflict avoidance
   */
  static async getTutorialPlacementDays(
    tutorialPeriodsPerWeek: number,
    academicYear: string,
    department: string,
    semester: string,
    section: string
  ): Promise<string[]> {
    const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const availableDays: string[] = [];

    // Import supabase here to avoid circular dependencies
    const { supabase } = await import("@/integrations/supabase/client");

    // Check each day for Period 7 availability (no skill labs in periods 5-7)
    for (const day of allDays) {
      // Check if any skill labs are scheduled during periods 5-7 on this day
      const { data: skillLabSlots, error } = await supabase
        .from('timetable_slots')
        .select('time_slot, subject_type')
        .eq('academic_year', academicYear)
        .eq('department', department)
        .eq('semester', semester)
        .eq('section', section)
        .eq('day', day)
        .or('subject_type.eq.skill_lab,subject_code.eq.SKILL LAB');

      if (error) {
        console.error('Error checking skill lab conflicts for tutorial placement:', error);
        continue;
      }

      // Check if any skill lab conflicts with Period 7 (15:05-16:00)
      const hasAfternoonSkillLab = skillLabSlots?.some(slot => {
        // Skill labs typically occupy periods 5-7 (13:15-16:00)
        // If there's any skill lab on this day, Period 7 is likely occupied
        return slot.time_slot && (
          slot.time_slot.includes('13:15') ||
          slot.time_slot.includes('13:30') ||
          slot.time_slot.includes('14:') ||
          slot.time_slot.includes('15:') ||
          slot.time_slot.includes('16:')
        );
      });

      if (!hasAfternoonSkillLab) {
        availableDays.push(day);
        console.log(`✅ ${day} is available for tutorial placement (no skill lab conflicts)`);
      } else {
        console.log(`❌ ${day} has skill lab conflicts, skipping for tutorial placement`);
      }
    }

    // Return the requested number of available days
    const selectedDays = availableDays.slice(0, tutorialPeriodsPerWeek);

    if (selectedDays.length < tutorialPeriodsPerWeek) {
      console.warn(`⚠️ Only ${selectedDays.length} days available for ${tutorialPeriodsPerWeek} tutorial periods`);
    }

    return selectedDays;
  }

  /**
   * Simple tutorial placement days (fallback for non-async contexts)
   */
  static getSimpleTutorialPlacementDays(tutorialPeriodsPerWeek: number): string[] {
    const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    return allDays.slice(0, tutorialPeriodsPerWeek);
  }
}
