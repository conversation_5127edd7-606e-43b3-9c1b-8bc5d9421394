import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  CheckCircle, 
  XCircle, 
  Calendar, 
  User, 
  Filter,
  Search,
  Clock,
  FileText,
  Download,
  Eye
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { LeaveManagementService, LeaveRequest } from '@/services/LeaveManagementService';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface LeaveApprovalHistoryProps {
  className?: string;
}

export default function LeaveApprovalHistory({ className }: LeaveApprovalHistoryProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const { department } = useUserDepartment();
  
  const [approvalHistory, setApprovalHistory] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    faculty_name: '',
    leave_type: 'all',
    status: 'all',
    start_date: '',
    end_date: ''
  });

  // Load approval history
  const loadApprovalHistory = async () => {
    if (!user?.id || !department) return;

    try {
      setLoading(true);

      // Use the new service method to get approval history
      const historyData = await LeaveManagementService.getApprovalHistoryForHOD(
        user.id,
        department,
        {
          faculty_name: filters.faculty_name || undefined,
          leave_type: filters.leave_type === 'all' ? undefined : filters.leave_type,
          status: filters.status === 'all' ? undefined : filters.status,
          start_date: filters.start_date || undefined,
          end_date: filters.end_date || undefined
        }
      );

      setApprovalHistory(historyData);
    } catch (error) {
      console.error('Error loading approval history:', error);
      toast({
        title: "Error",
        description: "Failed to load approval history",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadApprovalHistory();
  }, [user?.id, department, filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      faculty_name: '',
      leave_type: 'all',
      status: 'all',
      start_date: '',
      end_date: ''
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            {status}
          </Badge>
        );
    }
  };

  const getLeaveTypeBadge = (leaveType: string) => {
    const colors = {
      casual_leave: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800',
      sick_leave: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
      earned_leave: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
      emergency_leave: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800'
    };

    return (
      <Badge variant="outline" className={colors[leaveType as keyof typeof colors] || 'bg-muted text-muted-foreground border-border'}>
        {leaveType.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Leave Approval History
        </CardTitle>
        <CardDescription>
          Track all leave requests you have approved or rejected
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="space-y-2">
            <Label htmlFor="faculty-search">Faculty Name</Label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="faculty-search"
                placeholder="Search faculty..."
                value={filters.faculty_name}
                onChange={(e) => handleFilterChange('faculty_name', e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Leave Type</Label>
            <Select value={filters.leave_type} onValueChange={(value) => handleFilterChange('leave_type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                <SelectItem value="casual_leave">Casual Leave</SelectItem>
                <SelectItem value="sick_leave">Sick Leave</SelectItem>
                <SelectItem value="earned_leave">Earned Leave</SelectItem>
                <SelectItem value="emergency_leave">Emergency Leave</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Status</Label>
            <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="start-date">From Date</Label>
            <Input
              id="start-date"
              type="date"
              value={filters.start_date}
              onChange={(e) => handleFilterChange('start_date', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="end-date">To Date</Label>
            <Input
              id="end-date"
              type="date"
              value={filters.end_date}
              onChange={(e) => handleFilterChange('end_date', e.target.value)}
            />
          </div>
        </div>

        <div className="flex justify-between items-center">
          <Button variant="outline" onClick={clearFilters}>
            <Filter className="h-4 w-4 mr-2" />
            Clear Filters
          </Button>
          <div className="text-sm text-muted-foreground">
            {approvalHistory.length} record{approvalHistory.length !== 1 ? 's' : ''} found
          </div>
        </div>

        {/* History Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Faculty</TableHead>
                <TableHead>Leave Type</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Days</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Decision Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-2 text-muted-foreground">Loading approval history...</p>
                  </TableCell>
                </TableRow>
              ) : approvalHistory.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground">No approval history found</p>
                    <p className="text-sm text-muted-foreground">Approved or rejected requests will appear here</p>
                  </TableCell>
                </TableRow>
              ) : (
                approvalHistory.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">
                          {request.employee_details?.full_name || 'Unknown Faculty'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getLeaveTypeBadge(request.leave_type)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{new Date(request.start_date).toLocaleDateString()}</div>
                        <div className="text-muted-foreground">to {new Date(request.end_date).toLocaleDateString()}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {request.total_days} day{request.total_days !== 1 ? 's' : ''}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(request.status)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        {request.approved_date ? new Date(request.approved_date).toLocaleDateString() : '-'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
