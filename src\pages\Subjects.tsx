
import { useState, useEffect } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import SubjectAllotment from "@/components/subjects/SubjectAllotment";
import SubjectEntryForm from "@/components/subjects/SubjectEntryForm";
import { Toaster } from "@/components/ui/toaster";

const Subjects = () => {
  const [activeTab, setActiveTab] = useState(() => {
    // Try to retrieve the previously selected tab from localStorage
    const savedTab = localStorage.getItem("subjects-active-tab");
    return savedTab || "entry";
  });

  // Save active tab to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("subjects-active-tab", activeTab);
  }, [activeTab]);

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Subject Management</h1>
        <p className="text-muted-foreground">Manage subjects and faculty allocations for departments</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="entry">Subject Entry</TabsTrigger>
          <TabsTrigger value="allotment">Subject Allotment</TabsTrigger>
        </TabsList>
        <TabsContent value="entry">
          <SubjectEntryForm />
        </TabsContent>
        <TabsContent value="allotment">
          <SubjectAllotment />
        </TabsContent>
      </Tabs>

      <Toaster />
    </div>
  );
};

export default Subjects;
