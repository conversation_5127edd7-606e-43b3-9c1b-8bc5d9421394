
import React from "react";
import { useNavigate } from "react-router-dom";
import { Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { MappingType } from "@/stores/SubjectMappingStore";

interface PublishButtonProps {
  mappings: MappingType[];
  filters: {
    year: string;
    dept: string;
    sem: string;
    section: string;
  };
}

const PublishButton: React.FC<PublishButtonProps> = ({ mappings, filters }) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { year, dept, sem, section } = filters;

  const handlePublishToTimetable = () => {
    if (!mappings || mappings.length === 0) {
      toast({
        title: "Cannot publish",
        description: "No mappings available to publish to timetable",
        variant: "destructive"
      });
      return;
    }

    // Navigate to timetable page
    toast({
      title: "Successfully published",
      description: `${mappings.length} subject mappings published to timetable`,
    });
    
    navigate("/timetable", {
      state: {
        academicYear: year,
        department: dept,
        semester: sem,
        section: section
      }
    });
  };

  return (
    <Button 
      onClick={handlePublishToTimetable} 
      className="md:w-auto"
      disabled={!mappings || mappings.length === 0}
    >
      <Send className="mr-2" size={18} /> Publish to Timetable
    </Button>
  );
};

export default PublishButton;
