import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDepartment } from '@/hooks/useUserDepartment';
import { useToast } from '@/hooks/use-toast';
import {
  Calendar,
  Clock,
  BookOpen,
  Users,
  MapPin,
  AlertTriangle,
  CheckCircle,
  Loader2,
  <PERSON>r<PERSON><PERSON><PERSON>
} from 'lucide-react';
import {
  LeaveManagementService,
  LeavePolicy,
  LeaveBalance,
  LeaveFormData,
  AffectedClass,
  ClassImpactAnalysis
} from '@/services/LeaveManagementService';
import { ClassSubstitutionService, SubstituteFaculty } from '@/services/ClassSubstitutionService';

interface EnhancedLeaveRequestFormProps {
  onSubmitSuccess?: () => void;
}

export default function EnhancedLeaveRequestForm({ onSubmitSuccess }: EnhancedLeaveRequestFormProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const { department, departmentName } = useUserDepartment();

  // Form state
  const [formData, setFormData] = useState<LeaveFormData>({
    leave_type: '',
    start_date: '',
    end_date: '',
    reason: '',
    supporting_documents: []
  });

  // Enhanced state for class impact
  const [classImpact, setClassImpact] = useState<ClassImpactAnalysis | null>(null);
  const [substitutionAssignments, setSubstitutionAssignments] = useState<{
    [classId: string]: {
      substitute_faculty_id: string;
      notes: string;
    }
  }>({});
  const [availableSubstitutes, setAvailableSubstitutes] = useState<{
    [classId: string]: SubstituteFaculty[]
  }>({});
  const [showAllFaculties, setShowAllFaculties] = useState<{
    [classId: string]: boolean
  }>({});

  // Loading states
  const [analyzingImpact, setAnalyzingImpact] = useState(false);
  const [loadingSubstitutes, setLoadingSubstitutes] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Leave policies and balances
  const [leavePolicies, setLeavePolicies] = useState<LeavePolicy[]>([]);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [calculatedDays, setCalculatedDays] = useState(0);

  // Load initial data
  useEffect(() => {
    if (user?.id) {
      loadInitialData();
    }
  }, [user?.id]);

  const loadInitialData = async () => {
    try {
      const [policies, balances] = await Promise.all([
        LeaveManagementService.getLeavePolicies(),
        LeaveManagementService.getLeaveBalances(user.id)
      ]);
      setLeavePolicies(policies);
      setLeaveBalances(balances);
    } catch (error) {
      console.error('Error loading initial data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load leave policies and balances.',
        variant: 'destructive',
      });
    }
  };

  // Calculate days when dates change
  useEffect(() => {
    if (formData.start_date && formData.end_date) {
      const days = LeaveManagementService.calculateLeaveDays(formData.start_date, formData.end_date);
      setCalculatedDays(days);

      // Analyze class impact when dates are set
      analyzeClassImpact();
    } else {
      setCalculatedDays(0);
      setClassImpact(null);
    }
  }, [formData.start_date, formData.end_date]);

  const analyzeClassImpact = async () => {
    if (!user?.id || !formData.start_date || !formData.end_date) return;

    try {
      setAnalyzingImpact(true);
      const leaveDates = generateDateRange(formData.start_date, formData.end_date);
      const impact = await ClassSubstitutionService.analyzeClassImpact(user.id, leaveDates);
      setClassImpact(impact);

      // Reset substitution assignments when impact changes
      setSubstitutionAssignments({});
      setAvailableSubstitutes({});
      setShowAllFaculties({});
    } catch (error) {
      console.error('Error analyzing class impact:', error);
      toast({
        title: 'Analysis Error',
        description: 'Failed to analyze class impact. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setAnalyzingImpact(false);
    }
  };

  const generateDateRange = (startDate: string, endDate: string): string[] => {
    const dates: string[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      dates.push(date.toISOString().split('T')[0]);
    }

    return dates;
  };

  const loadSubstitutesForClass = async (affectedClass: AffectedClass, forceShowAll: boolean = false) => {
    try {
      setLoadingSubstitutes(affectedClass.id);

      // Use the leave date for availability checking
      const leaveDate = affectedClass.date || formData.start_date;
      if (!leaveDate) {
        toast({
          title: 'Error',
          description: 'Leave date is required to check faculty availability.',
          variant: 'destructive',
        });
        return;
      }

      // Determine whether to show all faculties
      // CRITICAL FIX: Use forceShowAll parameter if provided (for immediate toggle response)
      const shouldShowAll = forceShowAll !== undefined ? forceShowAll : (showAllFaculties[affectedClass.id] || false);

      console.log('🔍 LOAD SUBSTITUTES DEBUG:', {
        classId: affectedClass.id,
        forceShowAll,
        showAllFacultiesState: showAllFaculties[affectedClass.id],
        finalShouldShowAll: shouldShowAll,
        department: department,
        departmentName: departmentName,
        affectedClass: {
          semester: affectedClass.semester,
          section: affectedClass.section,
          subject_type: affectedClass.subject_type
        }
      });

      const substitutes = await ClassSubstitutionService.getAvailableSubstitutes(
        department, // Use the short code (e.g., 'cse') not the full name
        affectedClass.day,
        affectedClass.time_slot,
        leaveDate,
        '2024-2025', // ✅ FIXED: Use correct academic year format
        affectedClass, // ENHANCED: Pass affected class for smart filtering
        shouldShowAll  // ENHANCED: Pass show all faculties flag
      );

      setAvailableSubstitutes(prev => ({
        ...prev,
        [affectedClass.id]: substitutes
      }));
    } catch (error) {
      console.error('Error loading substitutes:', error);
      toast({
        title: 'Error',
        description: 'Failed to load available substitutes.',
        variant: 'destructive',
      });
    } finally {
      setLoadingSubstitutes(null);
    }
  };

  const handleShowAllFacultiesToggle = (classId: string, checked: boolean) => {
    console.log('🔄 TOGGLE DEBUG:', {
      classId,
      checked,
      previousState: showAllFaculties[classId],
      newState: checked
    });

    setShowAllFaculties(prev => ({
      ...prev,
      [classId]: checked
    }));

    // Reload substitutes with the new setting
    const affectedClass = classImpact?.affected_classes.find(cls => cls.id === classId);
    if (affectedClass) {
      console.log('🔄 Reloading substitutes with new setting:', {
        classId,
        showAllFaculties: checked,
        affectedClass: {
          semester: affectedClass.semester,
          section: affectedClass.section,
          subject_type: affectedClass.subject_type
        }
      });
      loadSubstitutesForClass(affectedClass, checked);
    }
  };

  const handleSubstituteSelection = (classId: string, substituteId: string) => {
    setSubstitutionAssignments(prev => ({
      ...prev,
      [classId]: {
        substitute_faculty_id: substituteId,
        notes: prev[classId]?.notes || ''
      }
    }));
  };

  const handleSubstituteNotes = (classId: string, notes: string) => {
    setSubstitutionAssignments(prev => ({
      ...prev,
      [classId]: {
        substitute_faculty_id: prev[classId]?.substitute_faculty_id || '',
        notes
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id || !department) {
      toast({
        title: 'Error',
        description: 'User information not available.',
        variant: 'destructive',
      });
      return;
    }

    // Validate substitution assignments for affected classes
    if (classImpact && classImpact.affected_classes.length > 0) {
      const unassignedClasses = classImpact.affected_classes.filter(
        cls => !substitutionAssignments[cls.id]?.substitute_faculty_id
      );

      if (unassignedClasses.length > 0) {
        toast({
          title: 'Incomplete Substitutions',
          description: `Please assign substitute faculty for all ${unassignedClasses.length} affected classes.`,
          variant: 'destructive',
        });
        return;
      }
    }

    try {
      setSubmitting(true);

      // Prepare substitution assignments
      const substitutions = Object.entries(substitutionAssignments).map(([classId, assignment]) => ({
        class_id: classId,
        substitute_faculty_id: assignment.substitute_faculty_id,
        notes: assignment.notes
      }));

      const enhancedFormData: LeaveFormData = {
        ...formData,
        affected_classes: classImpact?.affected_classes || [],
        substitution_assignments: substitutions
      };

      await LeaveManagementService.submitEnhancedLeaveRequest(user.id, department, enhancedFormData);

      toast({
        title: 'Leave Request Submitted',
        description: `Your leave request for ${calculatedDays} days with ${classImpact?.total_classes_affected || 0} class substitutions has been submitted successfully.`,
      });

      // Reset form
      setFormData({
        leave_type: '',
        start_date: '',
        end_date: '',
        reason: '',
        supporting_documents: []
      });
      setClassImpact(null);
      setSubstitutionAssignments({});
      setAvailableSubstitutes({});
      setShowAllFaculties({});
      setCalculatedDays(0);

      // Reload balances
      const updatedBalances = await LeaveManagementService.getLeaveBalances(user.id);
      setLeaveBalances(updatedBalances);

      // Call success callback
      onSubmitSuccess?.();

    } catch (error) {
      console.error('Error submitting leave request:', error);
      toast({
        title: 'Submission Failed',
        description: error instanceof Error ? error.message : 'Failed to submit leave request.',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const selectedPolicy = leavePolicies.find(p => p.leave_type === formData.leave_type);
  const selectedBalance = leaveBalances.find(b => b.leave_type === formData.leave_type);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Enhanced Leave Application
          </CardTitle>
          <CardDescription>
            Apply for leave with automatic class impact analysis and substitute faculty assignment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Leave Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="leave_type">Leave Type</Label>
                <Select
                  value={formData.leave_type}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, leave_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select leave type" />
                  </SelectTrigger>
                  <SelectContent>
                    {leavePolicies.map((policy) => (
                      <SelectItem key={policy.id} value={policy.leave_type}>
                        {policy.leave_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedBalance && (
                <div className="space-y-2">
                  <Label>Available Balance</Label>
                  <div className="p-3 bg-muted rounded-md">
                    <div className="text-sm text-muted-foreground">
                      {selectedBalance.remaining_days} of {selectedBalance.total_allocated} days remaining
                    </div>
                    <div className="w-full bg-background rounded-full h-2 mt-1">
                      <div
                        className="bg-primary h-2 rounded-full"
                        style={{
                          width: `${(selectedBalance.remaining_days / selectedBalance.total_allocated) * 100}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                  required
                />
              </div>
            </div>

            {calculatedDays > 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Total leave days: <strong>{calculatedDays} days</strong>
                  {selectedBalance && calculatedDays > selectedBalance.remaining_days && (
                    <span className="text-destructive ml-2">
                      (Exceeds available balance by {calculatedDays - selectedBalance.remaining_days} days)
                    </span>
                  )}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="reason">Reason for Leave</Label>
              <Textarea
                id="reason"
                placeholder="Please provide a detailed reason for your leave request..."
                value={formData.reason}
                onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                required
                rows={3}
              />
            </div>

            {/* Class Impact Analysis Section */}
            {analyzingImpact && (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Analyzing class impact...</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {classImpact && classImpact.total_classes_affected > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-orange-500" />
                    Class Impact Analysis
                  </CardTitle>
                  <CardDescription>
                    Your leave will affect {classImpact.total_classes_affected} classes. Please assign substitute faculty for each class.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Impact Summary */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{classImpact.impact_summary.theory_classes}</div>
                      <div className="text-sm text-muted-foreground">Theory Classes</div>
                    </div>
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{classImpact.impact_summary.lab_classes}</div>
                      <div className="text-sm text-muted-foreground">Lab Classes</div>
                    </div>
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{classImpact.impact_summary.tutorial_classes}</div>
                      <div className="text-sm text-muted-foreground">Tutorial Classes</div>
                    </div>
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">{classImpact.impact_summary.semesters_affected.length}</div>
                      <div className="text-sm text-muted-foreground">Semesters</div>
                    </div>
                  </div>

                  <Separator />

                  {/* Affected Classes List */}
                  <div className="space-y-4">
                    <h4 className="font-semibold">Affected Classes & Substitute Assignment</h4>
                    {classImpact.affected_classes.map((affectedClass) => (
                      <Card key={affectedClass.id} className="border-l-4 border-l-orange-500">
                        <CardContent className="pt-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Class Details */}
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <BookOpen className="h-4 w-4" />
                                <span className="font-medium">{affectedClass.subject_code} - {affectedClass.subject_name}</span>
                                <Badge variant="outline">{affectedClass.subject_type}</Badge>
                              </div>
                              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <div className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {affectedClass.day} - {affectedClass.time_slot}
                                </div>
                                <div className="flex items-center gap-1">
                                  <Users className="h-3 w-3" />
                                  {affectedClass.semester}-{affectedClass.section}
                                </div>
                                {affectedClass.room_number && (
                                  <div className="flex items-center gap-1">
                                    <MapPin className="h-3 w-3" />
                                    {affectedClass.room_number}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Substitute Assignment */}
                            <div className="space-y-2">
                              <Label>
                                {affectedClass.subject_type === 'lab' ||
                                 affectedClass.subject_type === 'laboratory' ||
                                 affectedClass.subject_code?.toUpperCase().endsWith('L') ||
                                 affectedClass.subject_name?.toUpperCase().includes('LAB')
                                  ? "Lab Faculty Assignment"
                                  : "Assign Substitute Faculty"}
                              </Label>

                              {/* Smart Filtering Checkbox */}
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id={`show-all-${affectedClass.id}`}
                                  checked={showAllFaculties[affectedClass.id] || false}
                                  onCheckedChange={(checked) =>
                                    handleShowAllFacultiesToggle(affectedClass.id, checked as boolean)
                                  }
                                />
                                <Label
                                  htmlFor={`show-all-${affectedClass.id}`}
                                  className="text-sm font-normal cursor-pointer"
                                >
                                  Show All Faculties
                                </Label>
                              </div>

                              {/* Smart Filtering Info */}
                              {!showAllFaculties[affectedClass.id] && (
                                <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                                  🎯 Smart Filtering: Showing faculties qualified for {affectedClass.semester}-{affectedClass.section} {affectedClass.subject_type} classes
                                </div>
                              )}

                              {(affectedClass.subject_type === 'lab' ||
                                affectedClass.subject_type === 'laboratory' ||
                                affectedClass.subject_code?.toUpperCase().endsWith('L') ||
                                affectedClass.subject_name?.toUpperCase().includes('LAB')) && (
                                <div className="text-sm text-green-600 bg-green-50 p-2 rounded">
                                  🧪 Lab Class: The system will show the specific faculty assigned to this lab session
                                </div>
                              )}
                              <div className="flex gap-2">
                                <Select
                                  value={substitutionAssignments[affectedClass.id]?.substitute_faculty_id || ''}
                                  onValueChange={(value) => handleSubstituteSelection(affectedClass.id, value)}
                                  onOpenChange={(open) => {
                                    if (open && !availableSubstitutes[affectedClass.id]) {
                                      loadSubstitutesForClass(affectedClass);
                                    }
                                  }}
                                >
                                  <SelectTrigger className="flex-1">
                                    <SelectValue placeholder={
                                      affectedClass.subject_type === 'lab' ||
                                      affectedClass.subject_type === 'laboratory' ||
                                      affectedClass.subject_code?.toUpperCase().endsWith('L') ||
                                      affectedClass.subject_name?.toUpperCase().includes('LAB')
                                        ? "Select lab faculty for this session"
                                        : "Select substitute faculty"
                                    } />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {loadingSubstitutes === affectedClass.id ? (
                                      <SelectItem value="loading" disabled>
                                        <div className="flex items-center gap-2">
                                          <Loader2 className="h-3 w-3 animate-spin" />
                                          Loading...
                                        </div>
                                      </SelectItem>
                                    ) : availableSubstitutes[affectedClass.id]?.length === 0 &&
                                        (affectedClass.subject_type === 'lab' ||
                                         affectedClass.subject_type === 'laboratory' ||
                                         affectedClass.subject_code?.toUpperCase().endsWith('L') ||
                                         affectedClass.subject_name?.toUpperCase().includes('LAB')) ? (
                                      <SelectItem value="no-lab-faculty" disabled>
                                        <div className="text-red-600">
                                          🚫 No lab faculty assigned to this session
                                        </div>
                                      </SelectItem>
                                    ) : (
                                      availableSubstitutes[affectedClass.id]?.map((substitute) => (
                                        <SelectItem
                                          key={substitute.id}
                                          value={substitute.id}
                                          disabled={substitute.availability_status !== 'available'}
                                        >
                                          <div className="flex items-center justify-between w-full">
                                            <div className="flex items-center gap-2">
                                              <span>{substitute.full_name}</span>
                                              {/* ENHANCED: Lab role badge */}
                                              {substitute.is_secondary_faculty && (
                                                <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                                                  Lab Faculty
                                                </Badge>
                                              )}
                                              {/* ENHANCED: Show qualification badge for smart filtering */}
                                              {!showAllFaculties[affectedClass.id] && substitute.conflict_details?.includes('Qualified:') && (
                                                <Badge variant="outline" className="text-purple-600 border-purple-600 text-xs">
                                                  Qualified
                                                </Badge>
                                              )}
                                            </div>
                                            <div className="flex items-center gap-1 ml-2">
                                              {substitute.availability_status === 'available' ? (
                                                <Badge variant="outline" className="text-green-600 border-green-600">
                                                  Available
                                                </Badge>
                                              ) : substitute.availability_status === 'occupied' ? (
                                                <Badge variant="outline" className="text-red-600 border-red-600">
                                                  Occupied
                                                </Badge>
                                              ) : (
                                                <Badge variant="outline" className="text-orange-600 border-orange-600">
                                                  On Leave
                                                </Badge>
                                              )}
                                            </div>
                                          </div>
                                          {/* ENHANCED: Show role description for lab assistants */}
                                          {substitute.role_description && (
                                            <div className="text-xs text-blue-600 mt-1">
                                              {substitute.role_description}
                                            </div>
                                          )}
                                          {substitute.conflict_details && (
                                            <div className="text-xs text-muted-foreground mt-1">
                                              {substitute.conflict_details}
                                            </div>
                                          )}
                                        </SelectItem>
                                      ))
                                    )}
                                  </SelectContent>
                                </Select>

                                {/* Show message when no lab assistant is found */}
                                {availableSubstitutes[affectedClass.id]?.length === 0 &&
                                 (affectedClass.subject_type === 'lab' ||
                                  affectedClass.subject_type === 'laboratory' ||
                                  affectedClass.subject_code?.toUpperCase().endsWith('L') ||
                                  affectedClass.subject_name?.toUpperCase().includes('LAB')) && (
                                  <div className="text-sm text-red-600 bg-red-50 p-2 rounded border border-red-200">
                                    <div className="font-medium">⚠️ Lab Faculty Not Found</div>
                                    <div className="text-xs mt-1">
                                      This lab session doesn't have an assigned faculty in the system.
                                      Please contact the timetable administrator to assign faculty to this lab session.
                                    </div>
                                  </div>
                                )}
                              </div>

                              {substitutionAssignments[affectedClass.id]?.substitute_faculty_id && (
                                <div className="space-y-2">
                                  <Label>Notes for Substitute (Optional)</Label>
                                  <Textarea
                                    placeholder="Any special instructions for the substitute faculty..."
                                    value={substitutionAssignments[affectedClass.id]?.notes || ''}
                                    onChange={(e) => handleSubstituteNotes(affectedClass.id, e.target.value)}
                                    rows={2}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {classImpact && classImpact.total_classes_affected === 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Great! Your leave dates don't conflict with any scheduled classes.
                </AlertDescription>
              </Alert>
            )}

            {/* Submit Button */}
            <div className="flex justify-end space-x-2">
              <Button
                type="submit"
                disabled={submitting || analyzingImpact || (selectedBalance && calculatedDays > selectedBalance.remaining_days)}
                className="min-w-[120px]"
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Leave Request'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
