import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  User,
  Calendar,
  TrendingUp,
  BookOpen,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Award,
  Clock,
  Mail,
  Phone,
  GraduationCap,
  Building2,
  Loader2,
  X
} from 'lucide-react';
import { StudentProgressService, StudentProgressCard as ProgressCardData, SubjectAttendance, IAMarks } from '@/services/StudentProgressService';
import ProctoringRemarksSection from './ProctoringRemarksSection';

interface StudentProgressCardProps {
  studentUsn: string;
  onClose: () => void;
}

interface CombinedSubjectData {
  subject_code: string;
  subject_name?: string;
  // Attendance data
  attendance_percentage: number;
  total_classes: number;
  attended_classes: number;
  // IA marks data
  ia1_marks?: number;
  ia2_marks?: number;
  ia3_marks?: number;
  assignment_marks?: number;
  theory_marks?: number; // Changed from lab_marks to match database schema
  total_marks?: number;
  average_marks?: number;
  grade?: string;
}

const StudentProgressCard: React.FC<StudentProgressCardProps> = ({ studentUsn, onClose }) => {
  const [progressData, setProgressData] = useState<ProgressCardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper function to apply enhanced subject name mapping for lab batches
  const getEnhancedSubjectName = (subjectCode: string, originalSubjectName?: string): string => {
    // Lab subject mappings (same as in StudentProgressService)
    const LAB_SUBJECT_MAPPINGS: Record<string, string> = {
      'BCS403': 'DBMS Lab',    // DBMS Lab
      'BCS402': 'MC Lab',      // MC Lab
      'BCS401': 'ADA Lab',     // ADA Lab
      'BCS404': 'CN Lab',      // CN Lab
      'BCS405': 'Software Engineering Lab',  // SE Lab
      'BCSL403': 'DBMS Lab',   // DBMS Lab (alternative code)
      'BCSL402': 'MC Lab',     // MC Lab (alternative code)
      'BCSL401': 'ADA Lab',    // ADA Lab (alternative code)
      'BCSL404': 'CN Lab',     // CN Lab (alternative code)
      'BCSL405': 'Software Engineering Lab',  // SE Lab (alternative code)
    };

    // Detect batch suffix pattern
    const batchPattern = /^(.+)_([A-Z]\d+)$/;
    const match = subjectCode.match(batchPattern);
    const isLabBatch = !!match;

    // For lab batches (with batch suffixes like _A1, _B2), always use lab mappings
    if (isLabBatch) {
      const [, baseCode, batchSuffix] = match;
      const labName = LAB_SUBJECT_MAPPINGS[baseCode];
      if (labName) {
        return `${labName} (${batchSuffix})`;
      }
    }

    // For subjects with explicit _LAB suffix, use lab mappings
    if (subjectCode.includes('_LAB')) {
      const normalizedCode = subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
      const labName = LAB_SUBJECT_MAPPINGS[normalizedCode];
      if (labName) {
        return labName;
      }
    }

    // For BCSL prefixed subjects (clear lab indicators), use lab mappings only if unknown
    if (subjectCode.startsWith('BCSL') && (!originalSubjectName || originalSubjectName === 'Unknown Subject')) {
      const labName = LAB_SUBJECT_MAPPINGS[subjectCode];
      if (labName) {
        return labName;
      }
    }

    return originalSubjectName || 'Unknown Subject';
  };

  // Helper function to combine attendance and IA data with fallback for all subjects
  const combineSubjectData = (attendance: SubjectAttendance[], iaMarks: IAMarks[]): CombinedSubjectData[] => {
    const subjectMap = new Map<string, CombinedSubjectData>();

    // Add attendance data
    attendance.forEach(att => {
      const enhancedSubjectName = getEnhancedSubjectName(att.subject_code, att.subject_name);
      subjectMap.set(att.subject_code, {
        subject_code: att.subject_code,
        subject_name: enhancedSubjectName,
        attendance_percentage: att.percentage,
        total_classes: att.total_classes,
        attended_classes: att.attended_classes,
      });
    });

    // Add IA marks data
    iaMarks.forEach(ia => {
      const existing = subjectMap.get(ia.subject_code);
      const enhancedSubjectName = getEnhancedSubjectName(ia.subject_code, ia.subject_name);

      if (existing) {
        // Update existing entry, prefer enhanced name
        subjectMap.set(ia.subject_code, {
          ...existing,
          subject_name: enhancedSubjectName,
          ia1_marks: ia.ia1_marks,
          ia2_marks: ia.ia2_marks,
          ia3_marks: ia.ia3_marks,
          assignment_marks: ia.assignment_marks,
          theory_marks: ia.theory_marks,
          total_marks: ia.total_marks,
          average_marks: ia.average_marks,
          grade: ia.grade,
        });
      } else {
        // Create new entry for subjects with only IA data
        subjectMap.set(ia.subject_code, {
          subject_code: ia.subject_code,
          subject_name: enhancedSubjectName,
          attendance_percentage: 0,
          total_classes: 0,
          attended_classes: 0,
          ia1_marks: ia.ia1_marks,
          ia2_marks: ia.ia2_marks,
          ia3_marks: ia.ia3_marks,
          assignment_marks: ia.assignment_marks,
          theory_marks: ia.theory_marks,
          total_marks: ia.total_marks,
          average_marks: ia.average_marks,
          grade: ia.grade,
        });
      }
    });

    // Note: If no data found, the service should fetch actual subjects from timetable
    // This fallback is handled in the StudentProgressService

    return Array.from(subjectMap.values()).sort((a, b) => a.subject_code.localeCompare(b.subject_code));
  };

  useEffect(() => {
    loadProgressData();
  }, [studentUsn]);

  const loadProgressData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Loading progress data for student:', studentUsn);
      const data = await StudentProgressService.getStudentProgressCard(studentUsn);
      console.log('✅ Progress data loaded successfully:', data);
      setProgressData(data);
    } catch (error) {
      console.error('❌ Error loading progress data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load student progress data';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getAttendanceStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getIAStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-50';
      case 'good': return 'text-blue-600 bg-blue-50';
      case 'average': return 'text-yellow-600 bg-yellow-50';
      case 'poor': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading student progress...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !progressData) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Error</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { basic_info, attendance, ia_marks, overall_performance } = progressData;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between border-b">
          <div>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Student Progress Card
            </CardTitle>
            <CardDescription>
              Comprehensive academic progress for {basic_info.student_name}
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <CardContent className="p-6 space-y-6">
            {/* Student Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <GraduationCap className="w-5 h-5" />
                    Student Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">{basic_info.student_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{basic_info.usn}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Building2 className="w-4 h-4 text-gray-500" />
                    <span>{basic_info.department}</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    Semester {basic_info.semester} • Section {basic_info.section}
                  </div>
                  {basic_info.email && (
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="w-4 h-4 text-gray-500" />
                      <span>{basic_info.email}</span>
                    </div>
                  )}
                  {basic_info.student_mobile && (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span>{basic_info.student_mobile}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Overall Performance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Attendance Status</span>
                    <Badge className={getAttendanceStatusColor(overall_performance.attendance_status)}>
                      {overall_performance.attendance_status.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">IA Performance</span>
                    <Badge className={getIAStatusColor(overall_performance.ia_status)}>
                      {overall_performance.ia_status.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Overall Grade</span>
                    <Badge variant="outline">
                      {overall_performance.overall_grade}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Overall Attendance Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Overall Attendance Summary
                </CardTitle>
                <CardDescription>
                  Period: {attendance.date_range.from} to {attendance.date_range.to}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 text-center mb-4">
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {attendance.attended_classes}
                    </div>
                    <div className="text-sm text-gray-600">Classes Attended</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      {attendance.total_classes}
                    </div>
                    <div className="text-sm text-gray-600">Total Classes</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600">
                      {attendance.overall_percentage.toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-600">Overall Percentage</div>
                  </div>
                </div>
                <Progress value={attendance.overall_percentage} className="w-full" />
              </CardContent>
            </Card>

            {/* Comprehensive Subjects Table */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  Subject-wise Progress
                </CardTitle>
                <CardDescription>
                  Complete overview of attendance and internal assessment marks for all subjects
                </CardDescription>
              </CardHeader>
              <CardContent>
                {(() => {
                  const combinedData = combineSubjectData(attendance.subject_wise_attendance, ia_marks);

                  if (combinedData.length === 0) {
                    return (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          <div className="space-y-2">
                            <p><strong>No subject data found for this student.</strong></p>
                            <p>This could be because:</p>
                            <ul className="ml-4 list-disc space-y-1">
                              <li>The student's timetable has not been set up yet</li>
                              <li>No subjects are assigned to this class (Dept: {progressData?.basic_info?.department}, Sem: {progressData?.basic_info?.semester}, Sec: {progressData?.basic_info?.section})</li>
                              <li>No attendance has been marked for any subjects</li>
                              <li>No IA marks have been entered for any subjects</li>
                            </ul>
                            <p className="text-sm text-gray-600 mt-2">
                              Please contact the administrator to ensure the timetable is properly configured for this class.
                            </p>
                          </div>
                        </AlertDescription>
                      </Alert>
                    );
                  }

                  return (
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border border-gray-200 text-sm">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-200 p-3 text-left font-medium">Subject Code</th>
                            <th className="border border-gray-200 p-3 text-left font-medium">Subject Name</th>
                            <th className="border border-gray-200 p-3 text-center font-medium">Attendance</th>
                            <th className="border border-gray-200 p-3 text-center font-medium">Classes</th>
                            <th className="border border-gray-200 p-3 text-center font-medium">IA1</th>
                            <th className="border border-gray-200 p-3 text-center font-medium">IA2</th>
                            <th className="border border-gray-200 p-3 text-center font-medium">IA3</th>
                            <th className="border border-gray-200 p-3 text-center font-medium">Assignment</th>
                            <th className="border border-gray-200 p-3 text-center font-medium">Theory</th>
                          </tr>
                        </thead>
                        <tbody>
                          {combinedData.map((subject, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="border border-gray-200 p-3 font-medium">
                                {subject.subject_code}
                              </td>
                              <td className="border border-gray-200 p-3">
                                {subject.subject_name || '-'}
                              </td>
                              <td className="border border-gray-200 p-3 text-center">
                                <div className="flex flex-col items-center gap-1">
                                  <Badge
                                    variant={subject.total_classes > 0 && subject.attendance_percentage >= 75 ? 'default' : 'destructive'}
                                    className="text-xs"
                                  >
                                    {subject.total_classes > 0 ? `${subject.attendance_percentage.toFixed(1)}%` : '-'}
                                  </Badge>
                                  {subject.total_classes > 0 && (
                                    <Progress
                                      value={subject.attendance_percentage}
                                      className="w-16 h-1"
                                    />
                                  )}
                                </div>
                              </td>
                              <td className="border border-gray-200 p-3 text-center text-xs">
                                {subject.total_classes > 0
                                  ? `${subject.attended_classes}/${subject.total_classes}`
                                  : '-'
                                }
                              </td>
                              <td className="border border-gray-200 p-3 text-center">
                                {subject.ia1_marks !== undefined ? `${subject.ia1_marks}/25` : '-'}
                              </td>
                              <td className="border border-gray-200 p-3 text-center">
                                {subject.ia2_marks !== undefined ? `${subject.ia2_marks}/25` : '-'}
                              </td>
                              <td className="border border-gray-200 p-3 text-center">
                                {subject.ia3_marks !== undefined ? `${subject.ia3_marks}/25` : '-'}
                              </td>
                              <td className="border border-gray-200 p-3 text-center">
                                {subject.assignment_marks !== undefined ? `${subject.assignment_marks}/10` : '-'}
                              </td>
                              <td className="border border-gray-200 p-3 text-center">
                                {subject.theory_marks !== undefined ? `${subject.theory_marks}/20` : '-'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  );
                })()}
              </CardContent>
            </Card>

            {/* Proctoring Remarks Section */}
            <ProctoringRemarksSection
              studentId={basic_info.id}
              studentUsn={basic_info.usn}
              studentName={basic_info.student_name}
              academicYear={basic_info.academic_year}
            />
          </CardContent>
        </div>
      </Card>
    </div>
  );
};

export default StudentProgressCard;
