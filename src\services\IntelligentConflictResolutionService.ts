// IntelligentConflictResolutionService.ts - Main service for intelligent conflict resolution

import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from 'uuid';
import {
  ConflictInfo,
  ConflictSlot,
  ConflictResolutionResult,
  ConflictResolutionConfig,
  ConflictDetectionParams,
  SwapProposal,
  FacultyAvailabilityUpdate
} from "@/types/ConflictResolution";
import { FacultyAvailabilityAnalyzer } from "./FacultyAvailabilityAnalyzer";
import { SlotSwappingService } from "./SlotSwappingService";

export class IntelligentConflictResolutionService {
  private static readonly DEFAULT_CONFIG: ConflictResolutionConfig = {
    maxCascadingDepth: 3,
    maxSwapAttempts: 10,
    prioritizeHighAvailabilityFaculty: true,
    allowConsecutiveTheorySlots: false,
    preserveLabSlotPriority: true,
    enableCrossValidation: true,
    timeoutMs: 30000 // 30 seconds
  };

  /**
   * Main entry point for intelligent conflict resolution
   */
  static async resolveConflicts(
    params: ConflictDetectionParams,
    config: Partial<ConflictResolutionConfig> = {}
  ): Promise<ConflictResolutionResult> {
    const startTime = Date.now();
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    
    console.log("Starting intelligent conflict resolution with params:", params);

    const result: ConflictResolutionResult = {
      success: false,
      resolvedConflicts: [],
      unresolvedConflicts: [],
      appliedSwaps: [],
      facultyAvailabilityUpdates: [],
      warnings: [],
      executionTime: 0
    };

    try {
      // Step 1: Detect all conflicts
      console.log("Step 1: Detecting conflicts...");
      const conflicts = await this.detectAllConflicts(params);
      console.log(`Found ${conflicts.length} conflicts`);

      if (conflicts.length === 0) {
        result.success = true;
        result.executionTime = Date.now() - startTime;
        return result;
      }

      // Step 2: Prioritize conflicts by severity
      const prioritizedConflicts = this.prioritizeConflicts(conflicts);

      // Step 3: Attempt to resolve each conflict
      console.log("Step 3: Attempting to resolve conflicts...");
      for (const conflict of prioritizedConflicts) {
        // Check timeout
        if (Date.now() - startTime > finalConfig.timeoutMs) {
          result.warnings.push("Conflict resolution timed out");
          break;
        }

        console.log(`Resolving conflict: ${conflict.description}`);
        const resolutionAttempt = await this.resolveIndividualConflict(
          conflict,
          params,
          finalConfig
        );

        if (resolutionAttempt.success) {
          result.resolvedConflicts.push(conflict);
          result.appliedSwaps.push(...resolutionAttempt.appliedSwaps);
          result.facultyAvailabilityUpdates.push(...resolutionAttempt.facultyUpdates);
          console.log(`Successfully resolved conflict: ${conflict.id}`);
        } else {
          result.unresolvedConflicts.push(conflict);
          if (resolutionAttempt.error) {
            result.warnings.push(`Failed to resolve conflict ${conflict.id}: ${resolutionAttempt.error}`);
          }
          console.log(`Failed to resolve conflict: ${conflict.id}`);
        }
      }

      // Step 4: Validate final state
      console.log("Step 4: Validating final state...");
      const remainingConflicts = await this.detectAllConflicts(params);
      result.unresolvedConflicts = remainingConflicts;

      result.success = result.resolvedConflicts.length > 0 && remainingConflicts.length === 0;
      result.executionTime = Date.now() - startTime;

      console.log(`Conflict resolution completed in ${result.executionTime}ms`);
      console.log(`Resolved: ${result.resolvedConflicts.length}, Unresolved: ${result.unresolvedConflicts.length}`);

      return result;
    } catch (error) {
      console.error("Error in intelligent conflict resolution:", error);
      result.warnings.push(`Critical error: ${error instanceof Error ? error.message : "Unknown error"}`);
      result.executionTime = Date.now() - startTime;
      return result;
    }
  }

  /**
   * Detect all types of conflicts in the timetable
   */
  static async detectAllConflicts(params: ConflictDetectionParams): Promise<ConflictInfo[]> {
    const conflicts: ConflictInfo[] = [];

    // Get all timetable slots for analysis
    let query = supabase
      .from("timetable_slots")
      .select("*")
      .eq("academic_year", params.academicYear)
      .eq("department", params.department);

    if (params.semester && params.section) {
      query = query.eq("semester", params.semester).eq("section", params.section);
    }

    if (params.facultyId) {
      query = query.or(`faculty_id.eq.${params.facultyId},faculty2_id.eq.${params.facultyId}`);
    }

    const { data: slots, error } = await query;

    if (error || !slots) {
      console.error("Error fetching timetable slots:", error);
      return conflicts;
    }

    // Detect faculty double-booking conflicts
    conflicts.push(...await this.detectFacultyDoubleBooking(slots, params));

    // Detect consecutive theory slot violations
    conflicts.push(...await this.detectConsecutiveTheoryViolations(slots, params));

    // Detect lab-theory overlap conflicts
    conflicts.push(...await this.detectLabTheoryOverlaps(slots, params));

    // Detect faculty overload conflicts
    conflicts.push(...await this.detectFacultyOverload(slots, params));

    return conflicts;
  }

  /**
   * Detect faculty double-booking conflicts (same faculty in multiple places at same time)
   */
  private static async detectFacultyDoubleBooking(
    slots: any[],
    params: ConflictDetectionParams
  ): Promise<ConflictInfo[]> {
    const conflicts: ConflictInfo[] = [];
    const facultySlotMap = new Map<string, Map<string, any[]>>();

    // Group slots by faculty and time
    for (const slot of slots) {
      const facultyIds = [slot.faculty_id];
      if (slot.faculty2_id) facultyIds.push(slot.faculty2_id);

      for (const facultyId of facultyIds) {
        const timeKey = `${slot.day}-${slot.time_slot}`;
        
        if (!facultySlotMap.has(facultyId)) {
          facultySlotMap.set(facultyId, new Map());
        }
        
        const facultyMap = facultySlotMap.get(facultyId)!;
        if (!facultyMap.has(timeKey)) {
          facultyMap.set(timeKey, []);
        }
        
        facultyMap.get(timeKey)!.push(slot);
      }
    }

    // Check for conflicts
    for (const [facultyId, timeSlotMap] of facultySlotMap) {
      for (const [timeKey, timeSlots] of timeSlotMap) {
        if (timeSlots.length > 1) {
          // Faculty double-booking detected
          const [day, timeSlot] = timeKey.split('-');
          
          const conflict: ConflictInfo = {
            id: uuidv4(),
            type: 'faculty_double_booking',
            severity: 'critical',
            description: `Faculty ${timeSlots[0].faculty_name} has ${timeSlots.length} overlapping classes on ${day} at ${timeSlot}`,
            affectedSlots: timeSlots.map(slot => this.convertToConflictSlot(slot)),
            facultyId,
            facultyName: timeSlots[0].faculty_name,
            day,
            timeSlot,
            academicYear: params.academicYear,
            department: params.department
          };

          conflicts.push(conflict);
        }
      }
    }

    return conflicts;
  }

  /**
   * Detect consecutive theory slot violations
   */
  private static async detectConsecutiveTheoryViolations(
    slots: any[],
    params: ConflictDetectionParams
  ): Promise<ConflictInfo[]> {
    const conflicts: ConflictInfo[] = [];
    
    // Standard time slots in order
    const timeSlots = [
      '08:30-09:25', '09:25-10:20', '10:35-11:30', '11:30-12:25',
      '13:15-14:10', '14:10-15:05', '15:05-16:00'
    ];

    // Group theory slots by faculty and day
    const facultyDaySlots = new Map<string, Map<string, any[]>>();
    
    for (const slot of slots.filter(s => s.subject_type === 'theory')) {
      const facultyIds = [slot.faculty_id];
      if (slot.faculty2_id) facultyIds.push(slot.faculty2_id);

      for (const facultyId of facultyIds) {
        if (!facultyDaySlots.has(facultyId)) {
          facultyDaySlots.set(facultyId, new Map());
        }
        
        const facultyMap = facultyDaySlots.get(facultyId)!;
        if (!facultyMap.has(slot.day)) {
          facultyMap.set(slot.day, []);
        }
        
        facultyMap.get(slot.day)!.push(slot);
      }
    }

    // Check for consecutive slots
    for (const [facultyId, daySlotMap] of facultyDaySlots) {
      for (const [day, daySlots] of daySlotMap) {
        // Sort slots by time
        const sortedSlots = daySlots.sort((a, b) => 
          timeSlots.indexOf(a.time_slot) - timeSlots.indexOf(b.time_slot)
        );

        // Check for consecutive slots
        for (let i = 0; i < sortedSlots.length - 1; i++) {
          const currentIndex = timeSlots.indexOf(sortedSlots[i].time_slot);
          const nextIndex = timeSlots.indexOf(sortedSlots[i + 1].time_slot);

          if (nextIndex === currentIndex + 1) {
            // Consecutive theory slots found
            const conflict: ConflictInfo = {
              id: uuidv4(),
              type: 'consecutive_theory',
              severity: 'medium',
              description: `Faculty ${sortedSlots[i].faculty_name} has consecutive theory classes on ${day}`,
              affectedSlots: [
                this.convertToConflictSlot(sortedSlots[i]),
                this.convertToConflictSlot(sortedSlots[i + 1])
              ],
              facultyId,
              facultyName: sortedSlots[i].faculty_name,
              day,
              timeSlot: sortedSlots[i].time_slot,
              academicYear: params.academicYear,
              department: params.department
            };

            conflicts.push(conflict);
          }
        }
      }
    }

    return conflicts;
  }

  /**
   * Resolve an individual conflict using intelligent swapping
   */
  private static async resolveIndividualConflict(
    conflict: ConflictInfo,
    params: ConflictDetectionParams,
    config: ConflictResolutionConfig
  ): Promise<{
    success: boolean;
    appliedSwaps: SwapProposal[];
    facultyUpdates: FacultyAvailabilityUpdate[];
    error?: string;
  }> {
    const result = {
      success: false,
      appliedSwaps: [] as SwapProposal[],
      facultyUpdates: [] as FacultyAvailabilityUpdate[],
      error: undefined as string | undefined
    };

    try {
      // Find swap candidates
      const swapCandidates = await SlotSwappingService.findSwapCandidates(conflict, params);

      if (swapCandidates.length === 0) {
        result.error = "No suitable swap candidates found";
        return result;
      }

      // Try each candidate until one works
      for (let i = 0; i < Math.min(swapCandidates.length, config.maxSwapAttempts); i++) {
        const candidate = swapCandidates[i];
        
        // Create swap proposal
        const proposal = await SlotSwappingService.createSwapProposal(conflict, candidate, params);
        
        // Validate proposal
        const validation = await SlotSwappingService.validateSwapProposal(proposal, params);
        
        if (!validation.isValid) {
          console.log(`Swap proposal ${i + 1} invalid:`, validation.conflicts);
          continue;
        }

        // Execute the swap
        const execution = await SlotSwappingService.executeSwapProposal(proposal, params);
        
        if (execution.success) {
          result.success = true;
          result.appliedSwaps.push(proposal);
          result.facultyUpdates.push(...execution.facultyUpdates);
          break;
        } else {
          console.log(`Swap execution ${i + 1} failed:`, execution.error);
        }
      }

      if (!result.success) {
        result.error = "All swap attempts failed";
      }

      return result;
    } catch (error) {
      result.error = error instanceof Error ? error.message : "Unknown error";
      return result;
    }
  }

  // Helper methods
  private static prioritizeConflicts(conflicts: ConflictInfo[]): ConflictInfo[] {
    const severityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    return conflicts.sort((a, b) => severityOrder[a.severity] - severityOrder[b.severity]);
  }

  private static convertToConflictSlot(slot: any): ConflictSlot {
    return {
      slotId: slot.id,
      semester: slot.semester,
      section: slot.section,
      subjectCode: slot.subject_code,
      subjectName: slot.subject_name,
      subjectType: slot.subject_type,
      day: slot.day,
      timeSlot: slot.time_slot,
      facultyId: slot.faculty_id,
      facultyName: slot.faculty_name,
      faculty2Id: slot.faculty2_id,
      faculty2Name: slot.faculty2_name
    };
  }

  private static async detectLabTheoryOverlaps(slots: any[], params: ConflictDetectionParams): Promise<ConflictInfo[]> {
    // Implementation for detecting lab-theory overlaps
    return [];
  }

  private static async detectFacultyOverload(slots: any[], params: ConflictDetectionParams): Promise<ConflictInfo[]> {
    // Implementation for detecting faculty overload
    return [];
  }
}
