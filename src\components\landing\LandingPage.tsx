import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Calendar,
  Users,
  GraduationCap,
  BookOpen,
  Clock,
  Shield,
  ArrowRight,
  UserCheck,
  School,
  Sparkles,
  Zap,
  Globe,
  BarChart3,
  Settings
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { FloatingGraduationCaps } from '@/components/landing/FloatingGraduationCap';

const LandingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 dark:from-background dark:via-background dark:to-muted/10">
      {/* Animated Background Pattern */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-secondary/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Floating Graduation Caps */}
      <FloatingGraduationCaps />

      {/* Header */}
      <header className="relative bg-background/80 dark:bg-background/80 backdrop-blur-md border-b border-border/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="relative">
                <Calendar className="h-8 w-8 text-primary mr-3" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full animate-ping"></div>
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                EduFlow
              </h1>
            </div>

            <div className="flex items-center space-x-3">
              <ThemeToggle />
              <Link to="/auth">
                <Button variant="outline" className="border-border/50 hover:bg-accent/50 transition-all duration-200">
                  <UserCheck className="h-4 w-4 mr-2" />
                  Faculty Login
                </Button>
              </Link>
              <Link to="/student-login">
                <Button className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 shadow-lg hover:shadow-xl transition-all duration-200">
                  <GraduationCap className="h-4 w-4 mr-2" />
                  Student Login
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-24 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="max-w-7xl mx-auto text-center relative z-10">
          {/* Hero Icon with Animation */}
          <div className="flex justify-center mb-12">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-xl animate-pulse"></div>
              <div className="relative p-6 bg-gradient-to-br from-background to-muted/50 backdrop-blur-sm rounded-full border border-border/50 shadow-2xl">
                <School className="h-20 w-20 text-primary" />
                <div className="absolute -top-2 -right-2">
                  <Sparkles className="h-6 w-6 text-accent animate-bounce" />
                </div>
              </div>
            </div>
          </div>

          {/* Hero Title with Gradient */}
          <div className="mb-8 space-y-4">
            <h1 className="text-5xl md:text-7xl font-bold leading-tight">
              <span className="bg-gradient-to-r from-foreground via-primary to-accent bg-clip-text text-transparent">
                Comprehensive
              </span>
              <br />
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                College ERP
              </span>
            </h1>
            <div className="flex justify-center">
              <div className="h-1 w-24 bg-gradient-to-r from-primary to-accent rounded-full"></div>
            </div>
          </div>

          {/* Hero Description */}
          <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed">
            Transform your educational institution with EduFlow - a comprehensive ERP solution for modern colleges.
            Manage timetables, attendance, assessments, faculty, students, and administrative processes seamlessly.
          </p>

          {/* CTA Buttons with Enhanced Styling */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link to="/student-registration" className="group">
              <Button size="lg" className="bg-gradient-to-r from-accent to-accent/90 hover:from-accent/90 hover:to-accent/80 text-lg px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <GraduationCap className="h-6 w-6 mr-3 group-hover:animate-bounce" />
                Student Registration
                <ArrowRight className="h-6 w-6 ml-3 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>

            <Link to="/auth" className="group">
              <Button size="lg" variant="outline" className="border-2 border-primary/30 hover:border-primary/50 bg-background/50 backdrop-blur-sm text-lg px-10 py-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <Users className="h-6 w-6 mr-3 group-hover:animate-pulse" />
                Faculty Portal
                <ArrowRight className="h-6 w-6 ml-3 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
          </div>

          {/* Floating Elements */}
          <div className="absolute top-20 left-10 opacity-20">
            <Clock className="h-8 w-8 text-primary animate-spin" style={{ animationDuration: '8s' }} />
          </div>
          <div className="absolute top-32 right-16 opacity-20">
            <BookOpen className="h-6 w-6 text-accent animate-bounce" style={{ animationDelay: '1s' }} />
          </div>
          <div className="absolute bottom-20 left-20 opacity-20">
            <Users className="h-7 w-7 text-secondary animate-pulse" style={{ animationDelay: '2s' }} />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-background to-muted/20">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center justify-center p-2 bg-primary/10 rounded-full mb-6">
              <Zap className="h-6 w-6 text-primary" />
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                Complete ERP
              </span>
              <br />
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Ecosystem
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              EduFlow provides integrated portals for faculty, students, and administrators with comprehensive role-based access control
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Faculty Features */}
            <Card className="group relative overflow-hidden border-border/50 bg-card/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-primary/10 transition-all duration-500 transform hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="relative">
                <div className="flex items-center justify-between mb-2">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <ArrowRight className="h-5 w-5 text-primary" />
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                  Faculty Portal
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Complete ERP tools for academic staff
                </CardDescription>
              </CardHeader>
              <CardContent className="relative">
                <ul className="space-y-3">
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <Clock className="h-4 w-4 mr-3 text-primary" />
                    Intelligent Timetable Management
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <BookOpen className="h-4 w-4 mr-3 text-primary" />
                    Comprehensive Subject Control
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <UserCheck className="h-4 w-4 mr-3 text-primary" />
                    Advanced Attendance System
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <Shield className="h-4 w-4 mr-3 text-primary" />
                    Leave Management Portal
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Student Features */}
            <Card className="group relative overflow-hidden border-border/50 bg-card/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-accent/10 transition-all duration-500 transform hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="relative">
                <div className="flex items-center justify-between mb-2">
                  <div className="p-2 bg-accent/10 rounded-lg">
                    <GraduationCap className="h-6 w-6 text-accent" />
                  </div>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <ArrowRight className="h-5 w-5 text-accent" />
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground group-hover:text-accent transition-colors">
                  Student Portal
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Comprehensive student management platform
                </CardDescription>
              </CardHeader>
              <CardContent className="relative">
                <ul className="space-y-3">
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <Calendar className="h-4 w-4 mr-3 text-accent" />
                    Personal Timetable Dashboard
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <Clock className="h-4 w-4 mr-3 text-accent" />
                    Attendance & Performance Tracking
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <BookOpen className="h-4 w-4 mr-3 text-accent" />
                    Internal Assessment Portal
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <Shield className="h-4 w-4 mr-3 text-accent" />
                    Secure Student Access
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* System Features */}
            <Card className="group relative overflow-hidden border-border/50 bg-card/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-secondary/10 transition-all duration-500 transform hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-secondary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardHeader className="relative">
                <div className="flex items-center justify-between mb-2">
                  <div className="p-2 bg-secondary/10 rounded-lg">
                    <Settings className="h-6 w-6 text-secondary" />
                  </div>
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <ArrowRight className="h-5 w-5 text-secondary" />
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground group-hover:text-secondary transition-colors">
                  ERP Features
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  Enterprise-grade management system
                </CardDescription>
              </CardHeader>
              <CardContent className="relative">
                <ul className="space-y-3">
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <Shield className="h-4 w-4 mr-3 text-secondary" />
                    Enterprise Security & Privacy
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <Users className="h-4 w-4 mr-3 text-secondary" />
                    Multi-Department Management
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <Globe className="h-4 w-4 mr-3 text-secondary" />
                    Cloud-based Infrastructure
                  </li>
                  <li className="flex items-center text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    <BarChart3 className="h-4 w-4 mr-3 text-secondary" />
                    Comprehensive Analytics Suite
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-primary/5 via-background to-accent/5">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="max-w-6xl mx-auto text-center relative z-10">
          {/* Section Header */}
          <div className="mb-16">
            <div className="inline-flex items-center justify-center p-2 bg-primary/10 rounded-full mb-6">
              <Sparkles className="h-6 w-6 text-primary animate-pulse" />
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
                Get Started Today
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Choose your portal to access the comprehensive EduFlow ERP system
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Student CTA Card */}
            <Card className="group relative overflow-hidden p-8 border-border/50 bg-card/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-accent/20 transition-all duration-500 transform hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-accent/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative text-center">
                <div className="relative mb-6">
                  <div className="absolute inset-0 bg-accent/20 rounded-full blur-xl animate-pulse"></div>
                  <div className="relative p-4 bg-gradient-to-br from-accent/10 to-accent/5 rounded-full border border-accent/20">
                    <GraduationCap className="h-16 w-16 text-accent mx-auto" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-3 group-hover:text-accent transition-colors">
                  Students
                </h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Register or login to access your personalized student portal with comprehensive academic management tools
                </p>
                <div className="space-y-3">
                  <Link to="/student-registration" className="block">
                    <Button className="w-full bg-gradient-to-r from-accent to-accent/90 hover:from-accent/90 hover:to-accent/80 shadow-lg hover:shadow-xl transition-all duration-300 py-3">
                      <UserCheck className="h-5 w-5 mr-2" />
                      Create Account
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </Button>
                  </Link>
                  <Link to="/student-login" className="block">
                    <Button variant="outline" className="w-full border-accent/30 hover:border-accent/50 hover:bg-accent/5 transition-all duration-300 py-3">
                      Sign In
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>

            {/* Faculty CTA Card */}
            <Card className="group relative overflow-hidden p-8 border-border/50 bg-card/50 backdrop-blur-sm hover:shadow-2xl hover:shadow-primary/20 transition-all duration-500 transform hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative text-center">
                <div className="relative mb-6">
                  <div className="absolute inset-0 bg-primary/20 rounded-full blur-xl animate-pulse"></div>
                  <div className="relative p-4 bg-gradient-to-br from-primary/10 to-primary/5 rounded-full border border-primary/20">
                    <Users className="h-16 w-16 text-primary mx-auto" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors">
                  Faculty & Staff
                </h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Access the comprehensive faculty portal for advanced ERP management and administrative tools
                </p>
                <div className="space-y-3">
                  <Link to="/auth" className="block">
                    <Button className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 shadow-lg hover:shadow-xl transition-all duration-300 py-3">
                      <Shield className="h-5 w-5 mr-2" />
                      Faculty Login
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gradient-to-r from-muted/50 to-muted/30 border-t border-border/50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Logo and Brand */}
            <div className="flex items-center justify-center mb-6">
              <div className="relative">
                <Calendar className="h-8 w-8 mr-3 text-primary" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full animate-ping"></div>
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
                EduFlow
              </span>
            </div>

            {/* Description */}
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              Empowering educational institutions with comprehensive ERP solutions,
              intelligent management systems, and seamless academic administration.
            </p>

            {/* Features Icons */}
            <div className="flex justify-center items-center space-x-8 mb-8 opacity-60">
              <div className="flex flex-col items-center">
                <Clock className="h-6 w-6 text-primary mb-2" />
                <span className="text-xs text-muted-foreground">Smart Scheduling</span>
              </div>
              <div className="flex flex-col items-center">
                <Shield className="h-6 w-6 text-accent mb-2" />
                <span className="text-xs text-muted-foreground">Enterprise Security</span>
              </div>
              <div className="flex flex-col items-center">
                <Users className="h-6 w-6 text-secondary mb-2" />
                <span className="text-xs text-muted-foreground">ERP Management</span>
              </div>
              <div className="flex flex-col items-center">
                <BarChart3 className="h-6 w-6 text-primary mb-2" />
                <span className="text-xs text-muted-foreground">Advanced Analytics</span>
              </div>
            </div>

            {/* Copyright */}
            <div className="pt-6 border-t border-border/30">
              <p className="text-muted-foreground text-sm">
                &copy; 2024 EduFlow. All rights reserved. Built with modern technology for educational excellence.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
