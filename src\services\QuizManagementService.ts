/**
 * Quiz Management Service
 * 
 * This service handles all quiz-related operations including:
 * - Course material management
 * - Quiz template creation and management
 * - Question management and AI integration
 * - Quiz scheduling and student access
 * - Analytics and reporting
 */

import { supabase } from '@/integrations/supabase/client';
import { AIQuizGenerationService } from './AIQuizGenerationService';
import { GeminiAIService } from './GeminiAIService';
import {
  CourseMaterial,
  CreateCourseMaterialRequest,
  QuizTemplate,
  CreateQuizTemplateRequest,
  QuizQuestion,
  QuizSchedule,
  CreateQuizScheduleRequest,
  StudentQuizAttempt,
  QuizAnalytics,
  AIQuestionGenerationRequest,
  ProcessingStatus,
  QuizStatus
} from '@/types/quiz-system';

export class QuizManagementService {

  // ==================== COURSE MATERIALS ====================

  /**
   * Upload and process course material
   */
  static async uploadCourseMaterial(
    facultyId: string,
    request: CreateCourseMaterialRequest
  ): Promise<CourseMaterial> {
    try {
      // 1. Upload file to Supabase Storage
      const fileExt = request.file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${facultyId}/${fileName}`; // Remove 'course-materials/' prefix

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('course-materials')
        .upload(filePath, request.file);

      if (uploadError) throw uploadError;

      // 2. Create database record
      const materialData = {
        faculty_id: facultyId,
        subject_code: request.subject_code,
        subject_name: request.subject_name,
        department: request.department,
        semester: request.semester,
        section: request.section,
        academic_year: request.academic_year,
        file_name: request.file.name,
        file_path: uploadData.path,
        file_type: fileExt?.toLowerCase() as any,
        file_size_bytes: request.file.size,
        processing_status: 'pending' as ProcessingStatus
      };

      const { data: material, error: dbError } = await supabase
        .from('course_materials')
        .insert(materialData)
        .select()
        .single();

      if (dbError) throw dbError;

      // 3. Start AI processing in background
      this.processCourseMaterialAsync(material.id);

      return material;

    } catch (error) {
      console.error('Failed to upload course material:', error);
      throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process course material with AI (async)
   */
  private static async processCourseMaterialAsync(materialId: string): Promise<void> {
    try {
      // Update status to processing
      await supabase
        .from('course_materials')
        .update({ processing_status: 'processing' })
        .eq('id', materialId);

      // Get material details
      const { data: material, error } = await supabase
        .from('course_materials')
        .select('*')
        .eq('id', materialId)
        .single();

      if (error || !material) throw new Error('Material not found');

      // Download file from storage
      const { data: fileData, error: downloadError } = await supabase.storage
        .from('course-materials')
        .download(material.file_path);

      if (downloadError) throw downloadError;

      // Convert to File object for processing
      const file = new File([fileData], material.file_name, { type: fileData.type });

      // Extract text using AI service
      const extractedText = await AIQuizGenerationService.extractTextFromFile(file);

      // Analyze content complexity using smart AI service selection
      const analysis = await this.analyzeContentWithBestAvailableAI(
        extractedText,
        material.subject_name
      );

      // Update material with processed data
      await supabase
        .from('course_materials')
        .update({
          processing_status: 'completed',
          extracted_text: extractedText,
          ai_analysis: {
            complexity: analysis.complexity,
            key_topics: analysis.keyTopics,
            suggested_distribution: analysis.suggestedDistribution,
            processed_at: new Date().toISOString()
          },
          processed_date: new Date().toISOString()
        })
        .eq('id', materialId);

    } catch (error) {
      console.error('AI processing failed:', error);
      
      // Update status to failed
      await supabase
        .from('course_materials')
        .update({
          processing_status: 'failed',
          processing_error: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('id', materialId);
    }
  }

  /**
   * Get course materials for faculty
   */
  static async getFacultyCourseMaterials(facultyId: string): Promise<CourseMaterial[]> {
    const { data, error } = await supabase
      .from('course_materials')
      .select('*')
      .eq('faculty_id', facultyId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  // ==================== QUIZ TEMPLATES ====================

  /**
   * Create a new quiz template
   */
  static async createQuizTemplate(
    facultyId: string,
    request: CreateQuizTemplateRequest
  ): Promise<QuizTemplate> {
    try {
      const templateData = {
        faculty_id: facultyId,
        ...request,
        status: 'draft' as QuizStatus
      };

      const { data: template, error } = await supabase
        .from('quiz_templates')
        .insert(templateData)
        .select()
        .single();

      if (error) throw error;
      return template;

    } catch (error) {
      console.error('Failed to create quiz template:', error);
      throw new Error(`Creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate AI questions for a quiz template
   */
  static async generateAIQuestions(
    templateId: string,
    questionCount: number = 20
  ): Promise<QuizQuestion[]> {
    try {
      // Get template and associated materials
      const { data: template, error: templateError } = await supabase
        .from('quiz_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (templateError || !template) throw new Error('Template not found');

      // Get course materials
      const { data: materials, error: materialsError } = await supabase
        .from('course_materials')
        .select('*')
        .in('id', template.source_material_ids)
        .eq('processing_status', 'completed');

      if (materialsError) throw materialsError;

      if (!materials || materials.length === 0) {
        throw new Error('No processed course materials found');
      }

      // Combine extracted text from all materials
      const combinedText = materials
        .map(m => m.extracted_text)
        .filter(Boolean)
        .join('\n\n');

      if (!combinedText) {
        throw new Error('No text content available for question generation');
      }

      // Generate questions using AI
      const aiRequest: AIQuestionGenerationRequest = {
        extracted_text: combinedText,
        subject_name: template.subject_name,
        difficulty_level: template.difficulty_level,
        question_types: template.question_types,
        question_count: questionCount
      };

      const aiResponse = await this.generateQuestionsWithBestAvailableAI(aiRequest);

      // Save generated questions to database
      const questionsToInsert = aiResponse.questions.map((q, index) => ({
        quiz_template_id: templateId,
        question_text: q.question_text,
        question_type: q.question_type,
        options: q.options,
        correct_answer: q.correct_answer,
        marks: 1,
        difficulty_level: q.difficulty_level,
        topic_tags: q.topic_tags,
        is_ai_generated: true,
        ai_confidence_score: q.ai_confidence_score,
        source_material_reference: q.source_material_reference,
        question_order: index + 1,
        review_status: 'pending' as const
      }));

      const { data: questions, error: questionsError } = await supabase
        .from('quiz_questions')
        .insert(questionsToInsert)
        .select();

      if (questionsError) throw questionsError;

      // Update template status
      await supabase
        .from('quiz_templates')
        .update({ 
          status: 'ai_generated',
          is_ai_generated: true 
        })
        .eq('id', templateId);

      return questions || [];

    } catch (error) {
      console.error('AI question generation failed:', error);
      throw new Error(`Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get quiz templates for faculty
   */
  static async getFacultyQuizTemplates(facultyId: string): Promise<QuizTemplate[]> {
    const { data, error } = await supabase
      .from('quiz_templates')
      .select('*')
      .eq('faculty_id', facultyId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  /**
   * Get questions for a quiz template
   */
  static async getQuizQuestions(templateId: string): Promise<QuizQuestion[]> {
    const { data, error } = await supabase
      .from('quiz_questions')
      .select('*')
      .eq('quiz_template_id', templateId)
      .order('question_order');

    if (error) throw error;
    return data || [];
  }

  // ==================== QUIZ SCHEDULING ====================

  /**
   * Schedule a quiz for students
   */
  static async scheduleQuiz(
    facultyId: string,
    request: CreateQuizScheduleRequest
  ): Promise<QuizSchedule> {
    try {
      const scheduleData = {
        faculty_id: facultyId,
        ...request,
        status: 'scheduled' as const
      };

      const { data: schedule, error } = await supabase
        .from('quiz_schedules')
        .insert(scheduleData)
        .select()
        .single();

      if (error) throw error;

      // TODO: Send notifications to students
      // this.sendQuizNotifications(schedule.id);

      return schedule;

    } catch (error) {
      console.error('Failed to schedule quiz:', error);
      throw new Error(`Scheduling failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get scheduled quizzes for faculty
   */
  static async getFacultyQuizSchedules(facultyId: string): Promise<QuizSchedule[]> {
    const { data, error } = await supabase
      .from('quiz_schedules')
      .select('*')
      .eq('faculty_id', facultyId)
      .order('start_time', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  // ==================== ANALYTICS ====================

  /**
   * Get quiz analytics for faculty
   */
  static async getQuizAnalytics(
    facultyId: string,
    quizScheduleId?: string
  ): Promise<QuizAnalytics[]> {
    try {
      const { data, error } = await supabase.rpc('get_quiz_analytics', {
        faculty_uuid: facultyId,
        quiz_schedule_id_param: quizScheduleId || null
      });

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Failed to get quiz analytics:', error);
      throw new Error(`Analytics failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get student attempts for a quiz
   */
  static async getQuizAttempts(quizScheduleId: string): Promise<StudentQuizAttempt[]> {
    const { data, error } = await supabase
      .from('student_quiz_attempts')
      .select('*')
      .eq('quiz_schedule_id', quizScheduleId)
      .order('submission_time', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  // ==================== SMART AI SERVICE SELECTION ====================

  /**
   * Analyze content complexity using the best available AI service
   * Priority: Gemini -> OpenAI -> Fallback
   */
  private static async analyzeContentWithBestAvailableAI(
    text: string,
    subjectName: string
  ): Promise<{
    complexity: any;
    keyTopics: string[];
    suggestedDistribution: Record<string, number>;
  }> {
    const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
    const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;

    console.log('🤖 Smart AI Service Selection for Content Analysis');
    console.log('Gemini API Key:', GEMINI_API_KEY ? '✅ Available' : '❌ Not configured');
    console.log('OpenAI API Key:', OPENAI_API_KEY ? '✅ Available' : '❌ Not configured');

    // Try Gemini first
    if (GEMINI_API_KEY) {
      try {
        console.log('🚀 Trying Gemini AI...');
        const result = await GeminiAIService.analyzeContentComplexity(text, subjectName);
        console.log('✅ Gemini AI analysis successful!');
        return result;
      } catch (error) {
        console.warn('⚠️ Gemini AI failed, trying OpenAI...', error);
      }
    }

    // Try OpenAI as fallback
    if (OPENAI_API_KEY) {
      try {
        console.log('🚀 Trying OpenAI...');
        const result = await AIQuizGenerationService.analyzeContentComplexity(text, subjectName);
        console.log('✅ OpenAI analysis successful!');
        return result;
      } catch (error) {
        console.warn('⚠️ OpenAI failed, using fallback...', error);
      }
    }

    // Use fallback mode
    console.log('🔄 Using fallback content analysis...');
    return await AIQuizGenerationService.analyzeContentComplexity(text, subjectName);
  }

  /**
   * Generate questions using the best available AI service
   * Priority: Gemini -> OpenAI -> Fallback
   */
  private static async generateQuestionsWithBestAvailableAI(
    request: AIQuestionGenerationRequest
  ): Promise<any> {
    const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
    const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;

    console.log('🤖 Smart AI Service Selection for Question Generation');
    console.log('Gemini API Key:', GEMINI_API_KEY ? '✅ Available' : '❌ Not configured');
    console.log('OpenAI API Key:', OPENAI_API_KEY ? '✅ Available' : '❌ Not configured');

    // Try Gemini first
    if (GEMINI_API_KEY) {
      try {
        console.log('🚀 Trying Gemini AI for question generation...');
        const result = await GeminiAIService.generateQuestions(request);
        console.log('✅ Gemini AI question generation successful!');
        return result;
      } catch (error) {
        console.warn('⚠️ Gemini AI failed, trying OpenAI...', error);
      }
    }

    // Try OpenAI as fallback
    if (OPENAI_API_KEY) {
      try {
        console.log('🚀 Trying OpenAI for question generation...');
        const result = await AIQuizGenerationService.generateQuestions(request);
        console.log('✅ OpenAI question generation successful!');
        return result;
      } catch (error) {
        console.warn('⚠️ OpenAI failed, using fallback...', error);
      }
    }

    // Use fallback mode
    console.log('🔄 Using fallback question generation...');
    return await AIQuizGenerationService.generateQuestions(request);
  }
}
