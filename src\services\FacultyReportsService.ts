import { supabase } from '@/integrations/supabase/client';

export interface AttendanceRecord {
  id: string;
  student_id: string;
  usn: string;
  student_name: string;
  subject_code: string;
  faculty_id: string;
  attendance_date: string;
  status: 'present' | 'absent';
  semester: string;
  section: string;
  department: string;
  time_slot?: string;
  marked_by: string;
  marked_at: string;
}

export interface StudentAttendanceSummary {
  student_id: string;
  usn: string;
  student_name: string;
  total_classes: number;
  classes_attended: number;
  attendance_percentage: number;
  status: 'good' | 'warning' | 'critical'; // Based on percentage thresholds
}

export interface SubjectAttendanceReport {
  subject_code: string;
  subject_name: string;
  semester: string;
  section: string;
  department: string;
  total_classes_conducted: number;
  total_students: number;
  average_attendance_percentage: number;
  students: StudentAttendanceSummary[];
}

export interface ReportFilters {
  subject_code?: string;
  date_from?: string;
  date_to?: string;
  show_below_percentage?: number; // Show only students with attendance below this percentage
  show_above_percentage?: number; // Show only students with attendance above or equal to this percentage
  // Additional fields for comprehensive reports
  semester?: string;
  section?: string;
  subjectCode?: string;
}

export class FacultyReportsService {
  /**
   * Map department names between employee_details and timetable_slots
   */
  private static mapDepartmentName(userDepartment: string): string {
    const departmentMap: Record<string, string> = {
      'Computer Science and Engineering': 'cse',
      'Information Science and Engineering': 'ise',
      'Electronics and Communication Engineering': 'ece',
      'Mechanical Engineering': 'mech',
      'Civil Engineering': 'civil',
      'Electrical and Electronics Engineering': 'eee'
    };

    return departmentMap[userDepartment] || userDepartment.toLowerCase();
  }

  /**
   * Reverse map department names from timetable_slots back to full names
   */
  private static reverseMapDepartmentName(shortDepartment: string): string {
    const reverseDepartmentMap: Record<string, string> = {
      'cse': 'Computer Science and Engineering',
      'ise': 'Information Science and Engineering',
      'ece': 'Electronics and Communication Engineering',
      'mech': 'Mechanical Engineering',
      'civil': 'Civil Engineering',
      'eee': 'Electrical and Electronics Engineering'
    };

    return reverseDepartmentMap[shortDepartment] || shortDepartment;
  }

  /**
   * Get subjects taught by a faculty member with theory/lab disambiguation
   * ENHANCED: Shows only theory subjects and lab subjects where faculty is PRIMARY faculty
   */
  static async getFacultySubjects(
    facultyId: string,
    userDepartment: string
  ): Promise<{
    subject_code: string;
    subject_name: string;
    semester: string;
    section: string;
    department: string;
    subject_type: string;
    batch_name?: string;
  }[]> {
    try {
      const mappedDepartment = this.mapDepartmentName(userDepartment);

      console.log(`📊 FACULTY REPORTS: Fetching subjects for faculty ${facultyId} in department ${mappedDepartment}`);

      // ENHANCED: Only fetch subjects where faculty is PRIMARY faculty
      // This excludes lab classes where they are secondary faculty
      const { data: subjects, error } = await supabase
        .from('timetable_slots')
        .select(`
          subject_code,
          subject_name,
          semester,
          section,
          department,
          subject_type,
          batch_name,
          faculty_id,
          faculty2_id
        `)
        .eq('faculty_id', facultyId)  // CRITICAL: Only primary faculty assignments
        .eq('department', mappedDepartment);

      if (error) throw error;

      console.log(`📊 FACULTY REPORTS: Found ${subjects?.length || 0} primary faculty assignments`);

      // Log the filtering logic for transparency
      const theoryCount = subjects?.filter(s => s.subject_type === 'theory' || s.subject_type === 'elective').length || 0;
      const labCount = subjects?.filter(s => s.subject_type === 'lab' || s.subject_type === 'laboratory').length || 0;

      console.log(`📊 FACULTY REPORTS: ${theoryCount} theory subjects, ${labCount} lab subjects (primary faculty only)`);

      // Remove duplicates based on subject_code, semester, section, subject_type, and batch_name
      const uniqueSubjects = subjects?.reduce((acc, current) => {
        const key = `${current.subject_code}-${current.semester}-${current.section}-${current.subject_type}-${current.batch_name || 'no-batch'}`;
        if (!acc.find(item =>
          `${item.subject_code}-${item.semester}-${item.section}-${item.subject_type}-${item.batch_name || 'no-batch'}` === key
        )) {
          // Only include the essential fields for the reports
          acc.push({
            subject_code: current.subject_code,
            subject_name: current.subject_name,
            semester: current.semester,
            section: current.section,
            department: current.department,
            subject_type: current.subject_type,
            batch_name: current.batch_name
          });
        }
        return acc;
      }, [] as {
        subject_code: string;
        subject_name: string;
        semester: string;
        section: string;
        department: string;
        subject_type: string;
        batch_name?: string;
      }[]) || [];

      console.log(`📊 FACULTY REPORTS: Returning ${uniqueSubjects.length} unique subjects after deduplication`);

      return uniqueSubjects;
    } catch (error) {
      console.error('Error fetching faculty subjects:', error);
      throw error;
    }
  }

  /**
   * UPDATED: Get attendance records from usn_attendance table with theory/lab separation
   */
  static async getSubjectAttendanceRecords(
    facultyId: string,
    subjectCode: string,
    semester: string,
    section: string,
    subjectType: string,
    userDepartment: string,
    batchName?: string,
    filters: ReportFilters = {}
  ): Promise<AttendanceRecord[]> {
    try {
      console.log('📊 UPDATED: Getting attendance records from usn_attendance table...');

      // CRITICAL FIX: Use the provided subjectType parameter directly, not subject code analysis
      const actualSubjectType = (subjectType === 'laboratory' || subjectType === 'lab') ? 'lab' : 'theory';

      // CRITICAL FIX: Include batch name for lab subjects to get batch-specific data
      let subjectIdentifier;
      if (actualSubjectType === 'lab') {
        subjectIdentifier = batchName ? `${subjectCode}_LAB_${batchName}` : `${subjectCode}_LAB`;
      } else {
        subjectIdentifier = `${subjectCode}_THEORY`;
      }

      console.log('🔍 Subject identification:', {
        subjectCode,
        subjectType,
        actualSubjectType,
        subjectIdentifier
      });

      // Handle department mapping inconsistencies
      const departmentVariants = [
        userDepartment,
        this.mapDepartmentName(userDepartment),
        'cse',
        'CSE',
        userDepartment.toLowerCase()
      ];

      let attendanceRecords: any[] = [];
      let allRegularRecords: any[] = [];
      let allSubstituteRecords: any[] = [];

      // CRITICAL FIX: Try different department variants and collect from ALL variants
      for (const deptVariant of departmentVariants) {
        console.log(`🔍 Trying department variant: "${deptVariant}"`);

        // Query 1: Regular attendance records where faculty is primary instructor
        let regularQuery = supabase
          .from('usn_attendance')
          .select('*')
          .eq('subject_identifier', subjectIdentifier)
          .eq('faculty_id', facultyId)
          .eq('semester', semester)
          .eq('section', section)
          .eq('department', deptVariant);

        // Apply date filters to regular query
        if (filters.date_from) {
          regularQuery = regularQuery.gte('attendance_date', filters.date_from);
        }
        if (filters.date_to) {
          regularQuery = regularQuery.lte('attendance_date', filters.date_to);
        }

        const { data: regularRecords, error: regularError } = await regularQuery.order('attendance_date', { ascending: false });

        console.log(`📊 REGULAR QUERY RESULT for ${deptVariant}:`, {
          records_found: regularRecords?.length || 0,
          error: regularError,
          sample_records: regularRecords?.slice(0, 3).map(r => ({
            attendance_date: r.attendance_date,
            time_slot: r.time_slot,
            subject_code: r.subject_code,
            subject_identifier: r.subject_identifier
          }))
        });

        // Collect regular records from this department variant
        if (!regularError && regularRecords && regularRecords.length > 0) {
          console.log(`✅ Found ${regularRecords.length} regular attendance records with department: "${deptVariant}"`);
          allRegularRecords.push(...regularRecords);
        }

        // Query 2: Substitute attendance records where this faculty was acting as substitute
        let substituteRecords: any[] = [];

        try {
          console.log(`🔍 Checking for substitute assignments for faculty ${facultyId} in reports`);

          // Get substitute assignments from leave_requests where this faculty is the substitute
          const { data: leaveRequests, error: leaveError } = await supabase
            .from('leave_requests')
            .select(`
              id,
              faculty_id,
              start_date,
              end_date,
              affected_classes,
              status
            `)
            .eq('status', 'approved');

          if (!leaveError && leaveRequests) {
            for (const leaveRequest of leaveRequests) {
              if (leaveRequest.affected_classes && Array.isArray(leaveRequest.affected_classes)) {
                for (const affectedClass of leaveRequest.affected_classes) {
                  // Check if this faculty is assigned as substitute for this class
                  if (affectedClass.substitute_faculty_id === facultyId) {
                    // Query attendance records for this substitute assignment
                    let substituteQuery = supabase
                      .from('usn_attendance')
                      .select('*')
                      .eq('faculty_id', facultyId)
                      .eq('semester', semester)
                      .eq('section', section)
                      .eq('department', deptVariant)
                      .gte('attendance_date', leaveRequest.start_date)
                      .lte('attendance_date', leaveRequest.end_date)
                      .order('attendance_date', { ascending: false });

                    // Apply additional date filters if specified
                    if (filters.date_from && filters.date_from > leaveRequest.start_date) {
                      substituteQuery = substituteQuery.gte('attendance_date', filters.date_from);
                    }
                    if (filters.date_to && filters.date_to < leaveRequest.end_date) {
                      substituteQuery = substituteQuery.lte('attendance_date', filters.date_to);
                    }

                    const { data: subRecords, error: subError } = await substituteQuery;

                    if (!subError && subRecords && subRecords.length > 0) {
                      console.log(`✅ Found ${subRecords.length} substitute attendance records for reports`);

                      // Mark these records as substitute assignments
                      const markedSubRecords = subRecords.map(record => ({
                        ...record,
                        is_substitute_assignment: true,
                        original_faculty_id: leaveRequest.faculty_id,
                        original_subject_code: affectedClass.subject_code,
                        leave_request_id: leaveRequest.id,
                        substitution_notes: affectedClass.substitution_notes || `Substitute for ${affectedClass.subject_code}`
                      }));

                      allSubstituteRecords.push(...markedSubRecords);
                    }
                  }
                }
              }
            }
          }
        } catch (substituteError) {
          console.warn('⚠️ Error checking substitute assignments in reports:', substituteError);
        }
      }

      // CRITICAL FIX: Combine ALL records from ALL department variants
      attendanceRecords = [
        ...allRegularRecords,
        ...allSubstituteRecords
      ];

      // Remove duplicates based on unique combination of date, time_slot, and student_usn
      const uniqueRecords = attendanceRecords.filter((record, index, self) => {
        const recordKey = `${record.attendance_date}-${record.time_slot}-${record.student_usn}`;
        return index === self.findIndex(r => `${r.attendance_date}-${r.time_slot}-${r.student_usn}` === recordKey);
      });

      attendanceRecords = uniqueRecords;

      console.log(`✅ FINAL RESULT: Found total ${attendanceRecords.length} unique attendance records (${allRegularRecords.length} regular + ${allSubstituteRecords.length} substitute) across all department variants`);
      console.log(`📊 UNIQUE CLASS SESSIONS:`, [...new Set(attendanceRecords.map(r => `${r.attendance_date} ${r.time_slot}`))]);

      if (attendanceRecords.length === 0) {
        console.log(`❌ No attendance records found for any department variant`);
        return [];
      }

      // Transform records to match the expected interface
      return attendanceRecords.map(record => ({
        id: record.id,
        student_id: record.student_usn, // Use USN as student_id for compatibility
        usn: record.student_usn,
        student_name: record.student_name,
        subject_code: record.subject_code,
        faculty_id: record.faculty_id,
        attendance_date: record.attendance_date,
        status: record.status,
        semester: record.semester,
        section: record.section,
        department: record.department,
        time_slot: record.time_slot,
        marked_by: record.marked_by,
        marked_at: record.marked_at
      }));
    } catch (error) {
      console.error('Error fetching attendance records:', error);
      throw error;
    }
  }

  /**
   * Calculate attendance summary for students in a subject
   */
  static calculateAttendanceSummary(
    attendanceRecords: AttendanceRecord[],
    allStudents: { id: string; usn: string; student_name: string }[]
  ): StudentAttendanceSummary[] {
    console.log('📊 calculateAttendanceSummary called with:');
    console.log('📊 Attendance records count:', attendanceRecords.length);
    console.log('📊 All students count:', allStudents.length);
    console.log('📊 Sample attendance record:', attendanceRecords[0]);
    console.log('📊 Sample student:', allStudents[0]);

    // If no attendance records, return all students with 0 attendance
    if (attendanceRecords.length === 0) {
      console.log('📊 No attendance records found, returning students with 0 attendance');
      return allStudents.map(student => ({
        student_id: student.id,
        usn: student.usn,
        student_name: student.student_name,
        total_classes: 0,
        classes_attended: 0,
        attendance_percentage: 0,
        status: 'critical' as const
      }));
    }

    // Group attendance records by student USN (since usn_attendance table uses USN as identifier)
    const studentAttendanceMap = new Map<string, AttendanceRecord[]>();

    attendanceRecords.forEach(record => {
      // Use USN for mapping since attendance records from usn_attendance table use USN
      const studentKey = record.usn || record.student_id; // Fallback to student_id if usn not available
      if (!studentAttendanceMap.has(studentKey)) {
        studentAttendanceMap.set(studentKey, []);
      }
      studentAttendanceMap.get(studentKey)!.push(record);
    });

    console.log('📊 Attendance records grouped by USN:', studentAttendanceMap.size);

    // Get unique class sessions when classes were conducted
    // CRITICAL FIX: Count unique date-time_slot combinations to properly handle multiple classes per day
    const uniqueClassSessions = [...new Set(attendanceRecords.map(record => `${record.attendance_date}-${record.time_slot || 'unknown'}`))];
    const totalClassesConducted = uniqueClassSessions.length;

    // Also log unique dates for comparison
    const uniqueDates = [...new Set(attendanceRecords.map(record => record.attendance_date))];
    console.log(`📊 Attendance Summary: ${uniqueDates.length} unique dates vs ${totalClassesConducted} unique class sessions`);

    return allStudents.map(student => {
      // Match by USN instead of database ID
      const studentRecords = studentAttendanceMap.get(student.usn) || [];
      console.log(`📊 Student ${student.usn} (${student.student_name}): ${studentRecords.length} attendance records`);
      const classesAttended = studentRecords.filter(record => record.status === 'present').length;

      // CORRECT LOGIC: Total classes for each student is the number of attendance records they have
      // This represents the number of classes they were marked for (present or absent)
      const totalClasses = studentRecords.length;
      const attendancePercentage = totalClasses > 0 ? (classesAttended / totalClasses) * 100 : 0;

      let status: 'good' | 'warning' | 'critical' = 'good';
      if (attendancePercentage < 50) {
        status = 'critical';
      } else if (attendancePercentage < 75) {
        status = 'warning';
      }

      return {
        student_id: student.id,
        usn: student.usn,
        student_name: student.student_name,
        total_classes: totalClasses,
        classes_attended: classesAttended,
        attendance_percentage: Math.round(attendancePercentage * 100) / 100,
        status
      };
    });
  }

  /**
   * Generate comprehensive subject attendance report with theory/lab disambiguation
   */
  static async generateSubjectReport(
    facultyId: string,
    userDepartment: string,
    subjectCode: string,
    semester: string,
    section: string,
    subjectType: string,
    batchName?: string,
    filters: ReportFilters = {}
  ): Promise<SubjectAttendanceReport> {
    try {
      // Get subject details
      const mappedDepartment = this.mapDepartmentName(userDepartment);
      let subjectQuery = supabase
        .from('timetable_slots')
        .select('subject_name')
        .or(`faculty_id.eq.${facultyId},faculty2_id.eq.${facultyId}`)
        .eq('subject_code', subjectCode)
        .eq('semester', semester)
        .eq('section', section)
        .eq('department', mappedDepartment)
        .eq('subject_type', subjectType);

      // Add batch filter for lab subjects
      if (batchName) {
        subjectQuery = subjectQuery.eq('batch_name', batchName);
      } else if (subjectType === 'theory') {
        subjectQuery = subjectQuery.is('batch_name', null);
      }

      const { data: subjectInfo, error: subjectError } = await subjectQuery.limit(1).single();
      if (subjectError) throw subjectError;

      // Get students for this class (for labs, get batch-specific students)
      const studentDepartment = this.reverseMapDepartmentName(mappedDepartment);
      let studentsData;

      console.log('📊 Fetching students for:', { subjectType, batchName, semester, section, studentDepartment });

      if (subjectType === 'laboratory' && batchName) {
        // For lab subjects, get students from the specific batch
        console.log('📊 Fetching lab batch students...');
        const { data: batchStudents, error: batchError } = await supabase
          .from('batch_students')
          .select(`
            student_id,
            class_students!inner(id, usn, student_name)
          `)
          .eq('batch_name', batchName)
          .eq('semester', semester)
          .eq('section', section);

        if (batchError) {
          console.error('📊 Error fetching batch students:', batchError);
          throw batchError;
        }
        studentsData = batchStudents?.map(bs => bs.class_students) || [];
        console.log('📊 Lab batch students found:', studentsData.length);
      } else {
        // For theory subjects, get all class students
        console.log('📊 Fetching class students...');

        // Try multiple department variants to handle department name mismatches
        const departmentVariants = [
          studentDepartment, // Original department name
          this.mapDepartmentName(studentDepartment), // Mapped version
          'cse', // Common abbreviation
          'Computer Science and Engineering' // Full name
        ];

        let classStudents = null;
        let studentsError = null;

        for (const deptVariant of departmentVariants) {
          console.log(`📊 Trying department variant: "${deptVariant}"`);
          const { data, error } = await supabase
            .from('class_students')
            .select('id, usn, student_name')
            .eq('department', deptVariant)
            .eq('semester', semester)
            .eq('section', section)
            .order('usn');

          if (!error && data && data.length > 0) {
            classStudents = data;
            console.log(`✅ Found ${data.length} students with department: "${deptVariant}"`);
            break;
          } else if (error) {
            console.log(`❌ Error with department "${deptVariant}":`, error.message);
            studentsError = error;
          } else {
            console.log(`📊 No students found with department: "${deptVariant}"`);
          }
        }

        studentsData = classStudents || [];
        console.log('📊 Final class students found:', studentsData.length);
      }

      // Get attendance records
      const attendanceRecords = await this.getSubjectAttendanceRecords(
        facultyId,
        subjectCode,
        semester,
        section,
        subjectType,
        userDepartment,
        batchName,
        filters
      );

      // If no students found in class_students table but we have attendance records,
      // extract student information from attendance records
      if ((!studentsData || studentsData.length === 0) && attendanceRecords.length > 0) {
        console.log('📊 No students in class_students table, extracting from attendance records...');

        // Get unique students from attendance records
        const uniqueStudents = new Map<string, { id: string; usn: string; student_name: string }>();

        attendanceRecords.forEach(record => {
          if (!uniqueStudents.has(record.usn)) {
            uniqueStudents.set(record.usn, {
              id: record.usn, // Use USN as ID for consistency
              usn: record.usn,
              student_name: record.student_name
            });
          }
        });

        studentsData = Array.from(uniqueStudents.values()).sort((a, b) => a.usn.localeCompare(b.usn));
        console.log('📊 Students extracted from attendance records:', studentsData.length);
        console.log('📊 Sample extracted student:', studentsData[0]);
      }

      // Calculate attendance summary for all students
      const studentSummaries = this.calculateAttendanceSummary(attendanceRecords, studentsData || []);
      console.log('📊 Student summaries calculated:', studentSummaries.length);
      console.log('📊 Sample student summary:', studentSummaries[0]);

      // Apply percentage filtering if specified
      // Default behavior: show ALL students
      let filteredStudents = studentSummaries;

      // Apply "below percentage" filter
      if (filters.show_below_percentage !== undefined) {
        filteredStudents = filteredStudents.filter(
          student => student.attendance_percentage < filters.show_below_percentage!
        );
      }

      // Apply "above percentage" filter
      if (filters.show_above_percentage !== undefined) {
        filteredStudents = filteredStudents.filter(
          student => student.attendance_percentage >= filters.show_above_percentage!
        );
      }

      // Calculate overall statistics
      // CRITICAL FIX: Count unique date-time_slot combinations to properly handle multiple classes per day
      const uniqueClassSessions = [...new Set(attendanceRecords.map(record => `${record.attendance_date}-${record.time_slot || 'unknown'}`))];
      const totalClassesConducted = uniqueClassSessions.length;

      // Also log unique dates for comparison
      const uniqueDates = [...new Set(attendanceRecords.map(record => record.attendance_date))];
      console.log(`📊 Subject ${subjectCode}: ${uniqueDates.length} unique dates vs ${totalClassesConducted} unique class sessions`);
      const averageAttendance = studentSummaries.length > 0
        ? studentSummaries.reduce((sum, student) => sum + student.attendance_percentage, 0) / studentSummaries.length
        : 0;

      return {
        subject_code: subjectCode,
        subject_name: subjectInfo?.subject_name || subjectCode,
        semester,
        section,
        department: mappedDepartment,
        total_classes_conducted: totalClassesConducted,
        total_students: studentsData?.length || 0,
        average_attendance_percentage: Math.round(averageAttendance * 100) / 100,
        students: filteredStudents
      };
    } catch (error) {
      console.error('Error generating subject report:', error);
      throw error;
    }
  }

  /**
   * UPDATED: Get detailed attendance history for a specific student from usn_attendance table
   */
  static async getStudentAttendanceHistory(
    facultyId: string,
    studentUsn: string, // Changed to use USN instead of student_id
    subjectCode: string,
    semester: string,
    section: string,
    subjectType: string, // ADDED: Need subject type for proper identifier creation
    batchName?: string, // ADDED: Need batch name for lab subjects
    filters: ReportFilters = {}
  ): Promise<AttendanceRecord[]> {
    try {
      console.log('📊 UPDATED: Getting student attendance history from usn_attendance table...');

      // CRITICAL FIX: Use the provided subjectType parameter directly, not subject code analysis
      const actualSubjectType = (subjectType === 'laboratory' || subjectType === 'lab') ? 'lab' : 'theory';

      // CRITICAL FIX: Include batch name for lab subjects to get batch-specific data
      let subjectIdentifier;
      if (actualSubjectType === 'lab') {
        subjectIdentifier = batchName ? `${subjectCode}_LAB_${batchName}` : `${subjectCode}_LAB`;
      } else {
        subjectIdentifier = `${subjectCode}_THEORY`;
      }

      let query = supabase
        .from('usn_attendance')
        .select('*')
        .eq('faculty_id', facultyId)
        .eq('student_usn', studentUsn)
        .eq('subject_identifier', subjectIdentifier)
        .eq('semester', semester)
        .eq('section', section)
        .order('attendance_date', { ascending: false });

      // Apply date filters
      if (filters.date_from) {
        query = query.gte('attendance_date', filters.date_from);
      }
      if (filters.date_to) {
        query = query.lte('attendance_date', filters.date_to);
      }

      const { data: attendanceRecords, error } = await query;
      if (error) throw error;

      // Transform records to match the expected interface
      return (attendanceRecords || []).map(record => ({
        id: record.id,
        student_id: record.student_usn, // Use USN as student_id for compatibility
        usn: record.student_usn,
        student_name: record.student_name,
        subject_code: record.subject_code,
        faculty_id: record.faculty_id,
        attendance_date: record.attendance_date,
        status: record.status,
        semester: record.semester,
        section: record.section,
        department: record.department,
        time_slot: record.time_slot,
        marked_by: record.marked_by,
        marked_at: record.marked_at
      }));
    } catch (error) {
      console.error('Error fetching student attendance history:', error);
      throw error;
    }
  }
}
