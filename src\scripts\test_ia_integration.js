/**
 * Integration Test Script for IA Marks Entry and Student Progress Display
 * This script tests the complete workflow from faculty IA entry to student progress display
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://milmyotuougemocvieof.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pbG15b3R1b3VnZW1vY3ZpZW9mIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ1MjE5NzQsImV4cCI6MjA1MDA5Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test data
const TEST_STUDENT_USN = '1KS23CS001';
const TEST_STUDENT_ID = '4b4b0737-262f-4bc5-bc11-a17b29b4ee54';
const TEST_SUBJECT_CODE = 'BCS401';
const TEST_ACADEMIC_YEAR = '2024-25';

async function testIAIntegration() {
  console.log('🧪 Starting IA Integration Test...');
  console.log('=' .repeat(60));

  try {
    // Test 1: Verify subject code normalization
    console.log('\n📝 Test 1: Subject Code Normalization');
    await testSubjectCodeNormalization();

    // Test 2: Test IA marks entry
    console.log('\n💾 Test 2: IA Marks Entry');
    await testIAMarksEntry();

    // Test 3: Test Student Progress display
    console.log('\n📊 Test 3: Student Progress Display');
    await testStudentProgressDisplay();

    // Test 4: Test real-time synchronization
    console.log('\n🔄 Test 4: Real-time Synchronization');
    await testRealTimeSync();

    // Test 5: Test data consistency
    console.log('\n✅ Test 5: Data Consistency Validation');
    await testDataConsistency();

    console.log('\n🎉 All integration tests completed successfully!');

  } catch (error) {
    console.error('❌ Integration test failed:', error);
  }
}

async function testSubjectCodeNormalization() {
  console.log('Testing subject code variations...');

  const testCases = [
    { input: 'BCS401_THEORY', expected: 'BCS401' },
    { input: 'BCS401_LAB', expected: 'BCS401' },
    { input: 'BCS401', expected: 'BCS401' },
    { input: 'BCSL404_PRACTICAL', expected: 'BCSL404' }
  ];

  testCases.forEach(testCase => {
    const normalized = normalizeSubjectCode(testCase.input);
    const result = normalized === testCase.expected ? '✅' : '❌';
    console.log(`  ${result} ${testCase.input} -> ${normalized} (expected: ${testCase.expected})`);
  });
}

function normalizeSubjectCode(subjectCode) {
  if (!subjectCode) return subjectCode;
  return subjectCode.replace(/_THEORY|_LAB|_PRACTICAL|_TUTORIAL/g, '');
}

async function testIAMarksEntry() {
  console.log('Testing IA marks entry...');

  // Sample IA marks data
  const iaMarksData = {
    student_id: TEST_STUDENT_ID,
    subject_code: TEST_SUBJECT_CODE,
    department: 'cse',
    semester: '4',
    section: 'A',
    academic_year: TEST_ACADEMIC_YEAR,
    ia1_marks: 22,
    ia2_marks: 20,
    ia3_marks: 24,
    assignment_marks: 8,
    lab_marks: null,
    faculty_id: '7965c1a9-fee4-4ea1-843b-462b77c83222',
    updated_at: new Date().toISOString()
  };

  try {
    // Insert/update IA marks
    const { data, error } = await supabase
      .from('internal_assessments')
      .upsert([iaMarksData], {
        onConflict: 'student_id,subject_code,academic_year'
      })
      .select();

    if (error) {
      console.log('❌ IA marks entry failed:', error.message);
      return;
    }

    console.log('✅ IA marks saved successfully');
    console.log(`📝 Saved record: ${data[0].subject_code} - Total: ${(data[0].ia1_marks || 0) + (data[0].ia2_marks || 0) + (data[0].ia3_marks || 0) + (data[0].assignment_marks || 0)}`);

  } catch (error) {
    console.log('❌ IA marks entry error:', error.message);
  }
}

async function testStudentProgressDisplay() {
  console.log('Testing Student Progress display...');

  try {
    // Fetch student progress data
    const { data: studentData, error: studentError } = await supabase
      .from('class_students')
      .select('id')
      .eq('usn', TEST_STUDENT_USN)
      .single();

    if (studentError) {
      console.log('❌ Student not found:', studentError.message);
      return;
    }

    // Fetch IA marks for the student
    const { data: iaData, error: iaError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', studentData.id)
      .eq('academic_year', TEST_ACADEMIC_YEAR);

    if (iaError) {
      console.log('❌ Error fetching IA marks:', iaError.message);
      return;
    }

    console.log('✅ Student Progress data fetched successfully');
    console.log(`📊 Found ${iaData.length} IA records for student ${TEST_STUDENT_USN}`);

    if (iaData.length > 0) {
      iaData.forEach(record => {
        const total = (record.ia1_marks || 0) + (record.ia2_marks || 0) + (record.ia3_marks || 0) + (record.assignment_marks || 0);
        console.log(`  📝 ${record.subject_code}: IA1=${record.ia1_marks}, IA2=${record.ia2_marks}, IA3=${record.ia3_marks}, Assignment=${record.assignment_marks}, Total=${total}`);
      });
    }

  } catch (error) {
    console.log('❌ Student Progress display error:', error.message);
  }
}

async function testRealTimeSync() {
  console.log('Testing real-time synchronization...');

  try {
    // Update IA marks
    const updatedMarks = {
      student_id: TEST_STUDENT_ID,
      subject_code: TEST_SUBJECT_CODE,
      academic_year: TEST_ACADEMIC_YEAR,
      ia1_marks: 25, // Updated value
      ia2_marks: 23, // Updated value
      ia3_marks: 24,
      assignment_marks: 9, // Updated value
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('internal_assessments')
      .upsert([updatedMarks], {
        onConflict: 'student_id,subject_code,academic_year'
      })
      .select();

    if (error) {
      console.log('❌ Real-time sync test failed:', error.message);
      return;
    }

    console.log('✅ IA marks updated successfully');

    // Verify the update is reflected
    const { data: verifyData, error: verifyError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', TEST_STUDENT_ID)
      .eq('subject_code', TEST_SUBJECT_CODE)
      .eq('academic_year', TEST_ACADEMIC_YEAR)
      .single();

    if (!verifyError && verifyData) {
      const newTotal = (verifyData.ia1_marks || 0) + (verifyData.ia2_marks || 0) + (verifyData.ia3_marks || 0) + (verifyData.assignment_marks || 0);
      console.log(`📊 Updated marks verified: Total = ${newTotal}`);
      console.log(`🕒 Last updated: ${verifyData.updated_at}`);
    }

  } catch (error) {
    console.log('❌ Real-time sync error:', error.message);
  }
}

async function testDataConsistency() {
  console.log('Testing data consistency between entry and display systems...');

  try {
    // Check if subject codes are consistent
    const { data: iaRecords, error } = await supabase
      .from('internal_assessments')
      .select('subject_code, student_id')
      .eq('student_id', TEST_STUDENT_ID);

    if (error) {
      console.log('❌ Data consistency check failed:', error.message);
      return;
    }

    console.log('✅ Data consistency check completed');
    console.log(`📊 Found ${iaRecords.length} IA records for consistency check`);

    // Check for subject code variations
    const subjectCodes = iaRecords.map(record => record.subject_code);
    const uniqueSubjects = [...new Set(subjectCodes)];

    console.log(`📝 Subject codes found: ${uniqueSubjects.join(', ')}`);

    // Check for any subject codes with suffixes
    const hasVariations = subjectCodes.some(code => /_THEORY|_LAB|_PRACTICAL/.test(code));
    if (hasVariations) {
      console.log('⚠️ Found subject codes with suffixes - normalization needed');
    } else {
      console.log('✅ All subject codes are normalized');
    }

  } catch (error) {
    console.log('❌ Data consistency error:', error.message);
  }
}

/**
 * Test the new IA integration fixes
 */
async function testIAIntegrationFixes() {
  console.log('\n🔧 Testing IA Integration Fixes');
  console.log('=' .repeat(60));

  try {
    // Test 1: Check both IA tables
    console.log('\n📋 Test 1: Checking Both IA Tables');

    // Get student ID
    const { data: studentData, error: studentError } = await supabase
      .from('class_students')
      .select('id, usn, student_name, department, semester, section, academic_year')
      .eq('usn', TEST_STUDENT_USN.toUpperCase())
      .single();

    if (studentError || !studentData) {
      console.log('❌ Student not found:', studentError?.message);
      return;
    }

    console.log('✅ Student found:', {
      name: studentData.student_name,
      department: studentData.department,
      semester: studentData.semester,
      section: studentData.section,
      academic_year: studentData.academic_year
    });

    // Check internal_assessments table
    const { data: standardIA, error: standardError } = await supabase
      .from('internal_assessments')
      .select('*')
      .eq('student_id', studentData.id);

    console.log(`📊 internal_assessments table: ${standardIA?.length || 0} records`);
    if (standardIA && standardIA.length > 0) {
      standardIA.forEach(record => {
        console.log(`  - ${record.subject_code}: IA1=${record.ia1_marks}, IA2=${record.ia2_marks}, IA3=${record.ia3_marks} (${record.academic_year})`);
      });
    }

    // Check individual_ia_records table
    const { data: individualIA, error: individualError } = await supabase
      .from('individual_ia_records')
      .select('*')
      .eq('student_id', studentData.id);

    console.log(`📊 individual_ia_records table: ${individualIA?.length || 0} records`);
    if (individualIA && individualIA.length > 0) {
      // Group by subject
      const subjectGroups = {};
      individualIA.forEach(record => {
        if (!subjectGroups[record.subject_code]) {
          subjectGroups[record.subject_code] = {};
        }
        subjectGroups[record.subject_code][record.ia_phase] = record.marks;
      });

      Object.keys(subjectGroups).forEach(subjectCode => {
        const phases = subjectGroups[subjectCode];
        console.log(`  - ${subjectCode}: IA1=${phases.IA1 || 'N/A'}, IA2=${phases.IA2 || 'N/A'}, IA3=${phases.IA3 || 'N/A'}, Assignment=${phases.ASSIGNMENT || 'N/A'}`);
      });
    }

    // Test 2: Check academic year variations
    console.log('\n📅 Test 2: Academic Year Variations');
    const currentYear = new Date().getFullYear();
    const academicYearVariations = [
      '2024-25',
      '2024-2025',
      `${currentYear}-${currentYear + 1}`,
      `${currentYear-1}-${currentYear}`
    ];

    for (const yearVariant of academicYearVariations) {
      const { data: yearData, error: yearError } = await supabase
        .from('internal_assessments')
        .select('subject_code, academic_year')
        .eq('student_id', studentData.id)
        .eq('academic_year', yearVariant);

      if (!yearError && yearData && yearData.length > 0) {
        console.log(`✅ Found ${yearData.length} records with academic year: "${yearVariant}"`);
        console.log(`   Subjects: ${yearData.map(r => r.subject_code).join(', ')}`);
      } else {
        console.log(`❌ No records found with academic year: "${yearVariant}"`);
      }
    }

    // Test 3: Check department case sensitivity
    console.log('\n🏢 Test 3: Department Case Sensitivity');
    const departmentVariations = [
      studentData.department,
      studentData.department.toLowerCase(),
      studentData.department.toUpperCase()
    ];

    for (const deptVariant of departmentVariations) {
      const { data: deptData, error: deptError } = await supabase
        .from('internal_assessments')
        .select('subject_code, department')
        .eq('student_id', studentData.id)
        .eq('department', deptVariant);

      if (!deptError && deptData && deptData.length > 0) {
        console.log(`✅ Found ${deptData.length} records with department: "${deptVariant}"`);
      } else {
        console.log(`❌ No records found with department: "${deptVariant}"`);
      }
    }

    // Test 4: Check timetable subject names
    console.log('\n📚 Test 4: Timetable Subject Names');
    const { data: timetableData, error: timetableError } = await supabase
      .from('timetable_slots')
      .select('subject_code, subject_name')
      .eq('department', studentData.department.toLowerCase())
      .eq('semester', studentData.semester)
      .eq('section', studentData.section);

    if (!timetableError && timetableData) {
      console.log(`✅ Found ${timetableData.length} timetable entries`);

      // Check for critical subjects
      const criticalSubjects = ['BCS401', 'BCS402', 'BCS403'];
      criticalSubjects.forEach(subjectCode => {
        const timetableEntry = timetableData.find(t => t.subject_code === subjectCode);
        if (timetableEntry) {
          console.log(`✅ ${subjectCode}: "${timetableEntry.subject_name}"`);
        } else {
          console.log(`❌ ${subjectCode}: Not found in timetable`);
        }
      });
    } else {
      console.log('❌ Error fetching timetable data:', timetableError?.message);
    }

  } catch (error) {
    console.log('❌ Integration fixes test error:', error.message);
  }
}

// Run both tests
async function runAllTests() {
  await testIAIntegration();
  await testIAIntegrationFixes();
}

runAllTests();
